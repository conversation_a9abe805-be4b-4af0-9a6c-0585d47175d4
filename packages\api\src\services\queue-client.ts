import { logger } from "@repo/logs";

// 定义任务数据接口
export interface VideoExportJobData {
	// 任务ID
	taskId: string;
	// 日记ID
	diaryId: string;
	// 用户ID
	userId: string;
	// 导出选项
	options: {
		// 视频分辨率: "720p", "1080p" 等
		resolution: string;
		// 视频帧率
		fps: number;
		// 每个点停留的秒数
		pointDuration: number;
	};
}

// 创建Redis连接
// MVP阶段：注释掉Redis连接逻辑
function createRedisConnection() {
	logger.info("MVP阶段：跳过Redis连接创建");
	return null;

	/* MVP阶段注释掉的Redis连接逻辑
	// 优先使用Upstash Redis URL
	if (process.env.UPSTASH_REDIS_URL) {
		return {
			url: process.env.UPSTASH_REDIS_URL,
			// Upstash连接可能需要TLS
			tls: {
				rejectUnauthorized: false,
			},
		};
	}

	// 备用：使用传统连接方式
	return {
		host: process.env.REDIS_HOST || "localhost",
		port: Number(process.env.REDIS_PORT || 6379),
		password: process.env.REDIS_PASSWORD,
	};
	*/
}

// 创建队列客户端
// MVP阶段：注释掉队列创建逻辑
const videoExportQueue = null;
/* MVP阶段注释掉的队列创建逻辑
const videoExportQueue = new Queue<VideoExportJobData>("video-export-queue", {
	connection: createRedisConnection(),
});
*/

// 向队列添加任务
export async function queueVideoExportTask(
	jobData: VideoExportJobData,
): Promise<string> {
	// MVP阶段：模拟队列操作
	logger.info(`MVP阶段：模拟视频导出任务添加到队列: ${jobData.taskId}`, {
		taskId: jobData.taskId,
		diaryId: jobData.diaryId,
	});

	// 返回模拟的job ID
	return `mock-job-${jobData.taskId}`;

	/* MVP阶段注释掉的队列操作逻辑
	try {
		// 添加到队列
		const job = await videoExportQueue.add("export-video", jobData, {
			removeOnComplete: false,
			removeOnFail: false,
		});

		logger.info(`已将视频导出任务添加到队列: ${jobData.taskId}`, {
			taskId: jobData.taskId,
			diaryId: jobData.diaryId,
		});

		return job.id as string;
	} catch (error) {
		logger.error(
			`队列任务创建失败: ${error instanceof Error ? error.message : String(error)}`,
			{
				taskId: jobData.taskId,
			},
		);
		throw error;
	}
	*/
}

// 应用关闭时关闭连接
export async function closeQueueConnection(): Promise<void> {
	// MVP阶段：跳过队列连接关闭
	logger.info("MVP阶段：跳过队列连接关闭");

	/* MVP阶段注释掉的队列关闭逻辑
	await videoExportQueue.close();
	*/
}
