<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f1f5f9;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="mountainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#94a3b8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#64748b;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="100" height="100" fill="url(#bgGradient)"/>
  
  <!-- Mountains in background -->
  <path d="M0,70 Q25,50 40,55 Q60,45 80,50 Q90,55 100,50 L100,100 L0,100 Z" fill="url(#mountainGradient)" opacity="0.6"/>
  
  <!-- Sun/Moon circle -->
  <circle cx="75" cy="35" r="12" fill="#fbbf24" opacity="0.8"/>
  
  <!-- Simple tree -->
  <rect x="25" y="60" width="3" height="20" fill="#84cc16" opacity="0.7"/>
  <circle cx="26.5" cy="58" r="4" fill="#22c55e" opacity="0.7"/>
  
  <!-- Cloud -->
  <ellipse cx="35" cy="25" rx="8" ry="4" fill="#ffffff" opacity="0.8"/>
  <ellipse cx="40" cy="23" rx="6" ry="3" fill="#ffffff" opacity="0.8"/>
  
  <!-- Ground line -->
  <line x1="0" y1="80" x2="100" y2="80" stroke="#cbd5e1" stroke-width="1" opacity="0.5"/>
  
  <!-- Subtle camera icon in corner -->
  <g transform="translate(75,75) scale(0.4)" opacity="0.3">
    <rect x="0" y="8" width="20" height="12" rx="2" fill="#64748b"/>
    <circle cx="10" cy="14" r="4" fill="#334155"/>
    <rect x="2" y="6" width="4" height="2" rx="1" fill="#94a3b8"/>
  </g>
</svg> 