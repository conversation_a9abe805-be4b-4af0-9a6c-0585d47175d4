---
type: "manual"
---

You are an expert in TypeScript, Node.js, Next.js App Router, React, Shadcn UI, Radix UI and Tailwind.

Key Principles
- Write concise, technical TypeScript code with accurate examples.
- Use functional and declarative programming patterns; avoid classes.
- Prefer iteration and modularization over code duplication.
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).
- Structure files: exported component, subcomponents, helpers, static content, types.

Project Structure
- The main application is in the `packages/app` directory which is a Next.js App Router. Put all frontend-only code in this directory.
- Put all backend logic into one of the `packages` directories:
  - `packages/ai` contains all AI-related code
  - `packages/api` contains all API routes
  - `packages/auth` contains the config for better-auth and helper functions
  - `packages/database` contains the database schema and auto-generated types
  - `packages/i18n` contains translations and internationalization helper functions
  - `packages/logs` contains the logging config and helper functions
  - `packages/mail` contains providers for sending mails and email templates
  - `packages/payments` contains code for payment providers and payment processing
  - `packages/storage` contains providers for storing files and images
  - `packages/utils` contains utility functions

Naming Conventions
- Use lowercase with dashes for directories (e.g., components/auth-wizard).
- <PERSON>avor named exports for components.

TypeScript Usage
- Use TypeScript for all code; prefer interfaces over types.
- Avoid enums; use maps instead.
- Use functional components with TypeScript interfaces.

Syntax and Formatting
- Use the "function" keyword for pure functions.
- Avoid unnecessary curly braces in conditionals; use concise syntax for simple statements.
- Use declarative JSX.

UI and Styling
- Use Shadcn UI, Radix, and Tailwind for components and styling.
- Implement responsive design with Tailwind CSS; use a mobile-first approach.
- Use the `cn` function for class name concatenation.

Performance Optimization
- Minimize 'use client', 'useEffect', and 'setState'; favor React Server Components (RSC).
- Wrap client components in Suspense with fallback.
- Use dynamic loading for non-critical components.
- Optimize images: use WebP format, include size data, implement lazy loading.

Key Conventions
- Use 'nuqs' for URL search parameter state management.
- Optimize Web Vitals (LCP, CLS, FID).
- Limit 'use client':
- Favor server components and Next.js SSR.
- Use only for Web API access in small components.
- Avoid for data fetching or state management.

Follow Next.js docs for Data Fetching, Rendering, and Routing.
Follow the documentation at supastarter.dev/docs/nextjs for supastarter specifc patterns.

Database Operation
- migrate changes, execute the command like: `pnpm --filter database migrate --name add_status_field_for_logical_delete`
- push changes to the database: `pnpm --filter database push`
- generate prisma client: `pnpm --filter database generate`