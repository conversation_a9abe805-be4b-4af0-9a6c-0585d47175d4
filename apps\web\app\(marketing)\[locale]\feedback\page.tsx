"use client";

import { FeatureVotingBoard } from "@repo/utils/lib/feature-voting";
import { Alert, AlertDescription } from "@ui/components/alert";
import { But<PERSON> } from "@ui/components/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { <PERSON>ertCircle, Loader2, RefreshCw } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface Product {
	id: string;
	name: string;
	description?: string;
}

export default function FeedbackPage() {
	const [products, setProducts] = useState<Product[]>([]);
	const [selectedProduct, setSelectedProduct] = useState<string>("");
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	// 加载产品列表
	const loadProducts = async () => {
		setIsLoading(true);
		setError(null);

		try {
			const response = await fetch("/api/products");
			if (response.ok) {
				const data = await response.json();
				if (data.success && data.data) {
					const productList = Array.isArray(data.data)
						? data.data
						: [];
					setProducts(productList);

					// 自动选择第一个产品
					if (productList.length > 0 && !selectedProduct) {
						setSelectedProduct(productList[0].id);
					}
				} else {
					setProducts([]);
				}
			} else {
				throw new Error("加载产品列表失败");
			}
		} catch (err: any) {
			console.error("加载产品失败:", err);
			setError(err?.message || "未知错误");
			toast.error("加载产品失败，请稍后重试");
		} finally {
			setIsLoading(false);
		}
	};

	// 初次加载
	useEffect(() => {
		loadProducts();
	}, []);

	if (isLoading) {
		return (
			<div className="container mx-auto py-16 px-4">
				<div className="flex items-center justify-center h-64">
					<Loader2 className="size-8 animate-spin" />
					<span className="ml-2">加载中...</span>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="container mx-auto py-16 px-4">
				<Alert>
					<AlertCircle className="size-4" />
					<AlertDescription>{error}</AlertDescription>
				</Alert>
				<div className="mt-4">
					<Button onClick={loadProducts}>
						<RefreshCw className="size-4 mr-2" />
						重试
					</Button>
				</div>
			</div>
		);
	}

	if (products.length === 0) {
		return (
			<div className="container mx-auto py-16 px-4">
				<Card>
					<CardHeader>
						<CardTitle>暂无可用产品</CardTitle>
					</CardHeader>
					<CardContent>
						<p className="text-muted-foreground">
							当前没有可用的产品，请联系管理员。
						</p>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="min-h-screen bg-gradient-to-b from-background to-muted/20">
			{/* 页面头部 */}
			<div className="bg-background border-b">
				<div className="container mx-auto py-16 px-4">
					<div className="text-center max-w-3xl mx-auto">
						<h1 className="text-4xl font-bold text-foreground mb-4">
							产品反馈与建议
						</h1>
						<p className="text-lg text-muted-foreground mb-8">
							我们重视您的意见！在这里查看其他用户的建议，为您喜欢的功能投票，
							或者提交您自己的想法来帮助我们改进产品。
						</p>

						{/* 产品选择器 */}
						{products.length > 1 && (
							<div className="flex justify-center">
								<div className="flex gap-2 bg-muted rounded-lg p-1">
									{products.map((product) => (
										<Button
											key={product.id}
											variant={
												selectedProduct === product.id
													? "secondary"
													: "ghost"
											}
											size="sm"
											onClick={() =>
												setSelectedProduct(product.id)
											}
											className="min-w-[120px]"
										>
											{product.name}
										</Button>
									))}
								</div>
							</div>
						)}
					</div>
				</div>
			</div>

			{/* 功能投票模块 */}
			<div className="container mx-auto py-12 px-4">
				{selectedProduct ? (
					<FeatureVotingBoard
						allowAnonymousVoting={true}
						allowAnonymousSubmissions={true}
						showVoteCounts={true}
						defaultQuery={{
							productId: selectedProduct,
						}}
						className="max-w-6xl mx-auto"
					/>
				) : (
					<Card>
						<CardContent className="pt-6">
							<p className="text-center text-muted-foreground">
								请选择一个产品来查看相关的功能建议
							</p>
						</CardContent>
					</Card>
				)}
			</div>

			{/* 页面底部说明 */}
			<div className="bg-muted/50 border-t">
				<div className="container mx-auto py-12 px-4">
					<div className="max-w-4xl mx-auto">
						<h2 className="text-2xl font-semibold text-center mb-8">
							如何参与
						</h2>
						<div className="grid md:grid-cols-3 gap-8">
							<div className="text-center">
								<div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
									<span className="text-primary font-bold text-lg">
										1
									</span>
								</div>
								<h3 className="font-semibold mb-2">浏览建议</h3>
								<p className="text-sm text-muted-foreground">
									查看其他用户提交的功能建议，了解产品的发展方向
								</p>
							</div>
							<div className="text-center">
								<div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
									<span className="text-primary font-bold text-lg">
										2
									</span>
								</div>
								<h3 className="font-semibold mb-2">投票支持</h3>
								<p className="text-sm text-muted-foreground">
									为您感兴趣的功能投票，帮助我们了解用户的真实需求
								</p>
							</div>
							<div className="text-center">
								<div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4">
									<span className="text-primary font-bold text-lg">
										3
									</span>
								</div>
								<h3 className="font-semibold mb-2">提交想法</h3>
								<p className="text-sm text-muted-foreground">
									有新的想法？点击"提交建议"按钮分享您的创意
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
