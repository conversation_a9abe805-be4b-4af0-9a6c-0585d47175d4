"use client";

import { Globe } from "lucide-react";
import { useTravelStatTranslations } from "../../../hooks/useTravelStatTranslations";
import type { AtmosphereTheme } from "../../types";
import { ATMOSPHERE_CONFIGS } from "../../types";
import { ATMOSPHERE_THEME_ICONS } from "../constants";
import { PopoverContainer } from "./PopoverContainer";

interface AtmospherePopoverProps {
	atmosphereTheme: AtmosphereTheme;
	onAtmosphereChange: (theme: AtmosphereTheme) => void;
}

export function AtmospherePopover({
	atmosphereTheme,
	onAtmosphereChange,
}: AtmospherePopoverProps) {
	const t = useTravelStatTranslations();

	// 所有大气层主题
	const atmosphereThemes: AtmosphereTheme[] = [
		"day",
		"night",
		"sunset",
		"dawn",
		"aurora",
		"deep-space",
		"ocean",
		"minimal",
	];

	return (
		<PopoverContainer>
			{/* 大气层主题选择 */}
			<div className="grid grid-cols-2 gap-2 mb-4">
				{atmosphereThemes.map((themeId) => {
					const Icon = ATMOSPHERE_THEME_ICONS[themeId];
					const isActive = themeId === atmosphereTheme;
					const config = ATMOSPHERE_CONFIGS[themeId];
					const themeName = t.atmosphere.names[themeId]();
					const themeDescription =
						t.atmosphere.descriptions[themeId]();

					return (
						<button
							key={themeId}
							type="button"
							onClick={(e) => {
								e.stopPropagation();
								onAtmosphereChange(themeId);
							}}
							className={`relative p-3 rounded-lg border-2 transition-all duration-200 text-left ${
								isActive
									? "border-sky-400 bg-sky-50 shadow-md"
									: "border-gray-200 hover:border-sky-300 hover:bg-sky-25"
							}`}
						>
							{/* 激活指示器 */}
							{isActive && (
								<div className="absolute top-1 right-1 w-2 h-2 bg-sky-500 rounded-full" />
							)}

							{/* 颜色预览条 */}
							<div className="absolute top-2 right-2 flex gap-1">
								<div
									className="w-2 h-8 rounded-sm border border-white/50"
									style={{
										backgroundColor: config.color,
									}}
								/>
								<div
									className="w-2 h-8 rounded-sm border border-white/50"
									style={{
										backgroundColor: config["high-color"],
									}}
								/>
								<div
									className="w-2 h-8 rounded-sm border border-white/50"
									style={{
										backgroundColor: config["space-color"],
									}}
								/>
							</div>

							<div className="flex items-center gap-2 mb-1">
								<Icon
									className={`w-4 h-4 ${
										isActive
											? "text-sky-600"
											: "text-gray-500"
									}`}
								/>
								<span
									className={`text-sm font-medium ${
										isActive
											? "text-sky-800"
											: "text-gray-700"
									}`}
								>
									{themeName}
								</span>
							</div>

							<p
								className={`text-xs pr-8 ${
									isActive ? "text-sky-600" : "text-gray-500"
								}`}
							>
								{themeDescription}
							</p>

							{/* 星星强度指示器 */}
							{config["star-intensity"] > 0 && (
								<div className="flex items-center gap-1 mt-2">
									{Array.from({ length: 5 }, (_, i) => (
										<div
											key={i}
											className={`w-1 h-1 rounded-full ${
												i < config["star-intensity"] * 5
													? "bg-yellow-400"
													: "bg-gray-300"
											}`}
										/>
									))}
									<span className="text-xs text-gray-500 ml-1">
										{t.atmosphere.popover.starfield()}
									</span>
								</div>
							)}
						</button>
					);
				})}
			</div>

			{/* 当前大气层详情 */}
			<div className="p-3 bg-gradient-to-r from-sky-50 to-blue-50 rounded-lg border border-sky-200">
				<div className="flex items-center gap-2 mb-2">
					<Globe className="w-4 h-4 text-sky-500" />
					<span className="text-sm font-medium text-sky-800">
						{t.atmosphere.popover.currentAtmosphere(
							t.atmosphere.names[atmosphereTheme](),
						)}
					</span>
				</div>
				<p className="text-xs text-sky-600 mb-2">
					{t.atmosphere.descriptions[atmosphereTheme]()}
				</p>

				{/* 配置详情 */}
				<div className="grid grid-cols-2 gap-2 text-xs">
					<div className="text-sky-600">
						{t.atmosphere.popover.starIntensity()}:{" "}
						{Math.round(
							ATMOSPHERE_CONFIGS[atmosphereTheme][
								"star-intensity"
							] * 100,
						)}
						%
					</div>
					<div className="text-sky-600">
						{t.atmosphere.popover.horizonBlend()}:{" "}
						{Math.round(
							ATMOSPHERE_CONFIGS[atmosphereTheme][
								"horizon-blend"
							] * 100,
						)}
						%
					</div>
				</div>
			</div>
		</PopoverContainer>
	);
}
