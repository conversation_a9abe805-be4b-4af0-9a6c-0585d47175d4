"use client";

import { useCallback, useMemo } from "react";
import type { MapStoryResult, UseMapStoryProps } from "./types";
import { useMapStoryAnimation } from "./use-map-story-animation";
import { useMapStoryEffects } from "./use-map-story-effects";
import { useMapStoryInteraction } from "./use-map-story-interaction";
import { useMapStoryNavigation } from "./use-map-story-navigation";
import { useMapStoryState } from "./use-map-story-state";
import { useMapStoryTimers } from "./use-map-story-timers";

/**
 * 地图故事钩子 - 管理地图上的路线和点位动画展示
 *
 * 该钩子实现了地图故事的完整生命周期，包括：
 * - 初始状态管理（概览模式）
 * - 点位间动画过渡
 * - 信息面板和图片展示
 * - 自动播放和用户交互控制
 * - 完成状态和回顾功能
 *
 * @param props 地图故事配置和回调
 * @returns 地图故事状态和控制方法
 */
export function useMapStory(props: UseMapStoryProps): MapStoryResult {
	// 1. 创建基本状态
	const stateResult = useMapStoryState(props);
	const state = stateResult[0];
	const setters = stateResult[1];
	const refs = stateResult[2];

	// 2. 构建上下文
	const baseContext = { props, state, setters, refs };

	// 3. 添加计时器功能
	const timers = useMapStoryTimers(baseContext);
	const timerContext = { ...baseContext, timers };

	// 4. 添加动画功能
	const animation = useMapStoryAnimation(timerContext);
	const animationContext = { ...timerContext, animation };

	// 5. 添加导航功能
	const navigation = useMapStoryNavigation(animationContext);
	const navigationContext = { ...animationContext, navigation };

	// 6. 添加交互功能
	const interaction = useMapStoryInteraction(navigationContext);

	// 7. 计算当前点位和图片
	const currentPoint = useMemo(() => {
		// 当索引为0时返回null（处于概览状态）
		if (state.currentPointIndex <= 0 || !props.points.length) return null;

		// 使用数组索引（currentPointIndex - 1 因为UI显示从1开始）
		const pointIndex = state.currentPointIndex - 1;
		return pointIndex < props.points.length
			? props.points[pointIndex]
			: null;
	}, [props.points, state.currentPointIndex]);

	// 获取当前点位的图片
	const currentImages = useMemo(() => {
		return currentPoint?.images
			? currentPoint.images.map((image) =>
					typeof image === "string" ? image : image.url,
				)
			: [];
	}, [currentPoint]);

	// 处理打字机完成回调
	const handleTypingComplete = useCallback(() => {
		// 标记打字已完成
		setters.setIsTypingCompleted(true);

		// 如果存在等待打字完成的Promise的resolver，调用它
		if (refs.typingPromiseResolverRef.current) {
			refs.typingPromiseResolverRef.current();
			refs.typingPromiseResolverRef.current = null; // 重置 resolver
		}
	}, [setters, refs]); // 依赖于 setters 和 refs 对象

	// 应用副作用
	useMapStoryEffects({
		...baseContext,
		timers,
		animation,
		navigation,
	});

	// 返回API
	return {
		// 状态
		storyState: state.storyState,
		isPlaying: state.isPlaying,
		isMapReady: state.isMapReady,
		currentPointIndex: state.currentPointIndex,
		showPointInfo: state.showPointInfo,
		showImages: state.showImages,
		lightboxOpen: state.lightboxOpen,
		selectedImageIndex: state.selectedImageIndex,
		flyToPointIndex: state.flyToPointIndex,
		fitBoundsToPoints: state.fitBoundsToPoints,
		spotlightPosition: state.spotlightPosition,
		blinkingPoints: state.blinkingPoints || [],
		autoPlayCountdown: state.autoPlayCountdown,
		showStartPrompt: state.showStartPrompt,
		showRestartPrompt: state.showRestartPrompt,
		isFinishing: state.isFinishing,
		manuallyReset: state.manuallyReset,

		// 计算值
		currentPoint,
		currentImages,

		// Refs
		activeMarkerRef: refs.activeMarkerRef,

		// 方法 - 基本状态修改
		setLightboxOpen: setters.setLightboxOpen,
		setFlyToPointIndex: setters.setFlyToPointIndex,
		setMapInstance: setters.setMapInstance,

		// 方法 - 导航
		...navigation,

		// 方法 - 交互
		...interaction,

		// 打字机回调
		onTypingComplete: handleTypingComplete,
	};
}

// 重新导出类型
export * from "./types";

// 导出日志工具
export { createLogger, logWithTime } from "./utils";
