"use client";

import type {
	TravelDiary,
	TravelPointImage,
} from "@repo/database/src/types/travel-diary";
import { cn } from "@ui/lib/utils";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";
import Image from "next/image";
import React, { useEffect, useRef, useState } from "react";
import HTMLFlipBook from "react-pageflip";
import { fetchTravelDiary } from "../../../mock-data";

// 定义页面组件的属性类型
interface PageProps {
	number: number;
	children: React.ReactNode;
	className?: string;
}

// 单页组件
const Page = React.forwardRef<HTMLDivElement, PageProps>(
	({ number, children, className }, ref) => {
		const { scale } = useBookSize();

		return (
			<div
				className={cn(
					"page relative h-full w-full bg-[#f8f5f0] shadow-md overflow-hidden", // Outer container clips overflow
					"bg-[url('/images/paper-texture.jpg')] bg-cover",
					className,
				)}
				ref={ref}
			>
				<div
					className="page-content w-full h-full relative overflow-y-auto overflow-x-hidden" // Added overflow-x-hidden here
					style={{
						padding: `${Math.max(12, 24 * scale)}px`,
						fontSize: `${Math.max(0.85, scale)}em`,
					}}
				>
					{children}
				</div>
				{number > 0 && (
					<div className="absolute bottom-3 w-full text-center">
						<span className="text-sm font-handwriting text-neutral-500">
							{number}
						</span>
					</div>
				)}
			</div>
		);
	},
);

Page.displayName = "Page";

// 封面组件
const Cover = React.forwardRef<
	HTMLDivElement,
	{ diary: TravelDiary; isBack?: boolean }
>(({ diary, isBack = false }, ref) => {
	const { title, subtitle, coverImage } = diary;
	const { scale } = useBookSize();

	// 计算旅行日期范围
	const startDate = diary.content.timelines[0]?.date;
	const endDate =
		diary.content.timelines[diary.content.timelines.length - 1]?.date;

	const dateRange =
		startDate && endDate
			? `${format(new Date(startDate), "yyyy.MM.dd", { locale: zhCN })} - ${format(new Date(endDate), "yyyy.MM.dd", { locale: zhCN })}`
			: "";

	return (
		<div
			className={cn(
				"cover relative h-full w-full ",
				isBack ? "bg-[#e2dfd8]" : "bg-[#503f34]",
			)}
			ref={ref}
		>
			{!isBack ? (
				// 封面内容
				<div className="flex flex-col items-center justify-center h-full text-white">
					{coverImage && (
						<div
							className="relative mb-8 w-4/5 overflow-hidden rounded-lg border-4 border-[#e2dfd8] shadow-lg"
							style={{ height: `${40 * scale}%` }}
						>
							<Image
								src={coverImage}
								alt={title || "旅行封面图片"}
								fill
								className="object-cover"
							/>
						</div>
					)}
					<h1
						className="mb-2 text-center"
						style={{
							fontSize: `${Math.max(1.5, 3 * scale)}rem`,
							fontWeight: "bold",
						}}
					>
						{title}
					</h1>
					<h2
						className="mb-6 text-center opacity-90"
						style={{ fontSize: `${Math.max(1, 1.8 * scale)}rem` }}
					>
						{subtitle}
					</h2>
					{dateRange && (
						<div
							className="mb-8 px-4 py-2 bg-[#ffffff20] rounded-full"
							style={{
								fontSize: `${Math.max(0.75, 0.875 * scale)}rem`,
							}}
						>
							{dateRange}
						</div>
					)}
					<div
						className="mt-auto opacity-70 font-handwriting"
						style={{
							fontSize: `${Math.max(0.75, 0.875 * scale)}rem`,
						}}
					>
						旅行手账 by Travel Memo
					</div>
				</div> // 封底内容
			) : (
				<div
					className="flex flex-col items-center justify-center h-full text-[#503f34]"
					style={{ padding: `${Math.max(16, 32 * scale)}px` }}
				>
					<div
						className="text-center font-handwriting mb-6"
						style={{
							fontSize: `${Math.max(1.1, 1.25 * scale)}rem`,
						}}
					>
						旅行的意义
					</div>
					<div
						className="text-center mb-10 max-w-sm opacity-80"
						style={{
							fontSize: `${Math.max(0.75, 0.875 * scale)}rem`,
						}}
					>
						我们的旅行不仅是为了到达目的地，更是为了在路上发现美好的风景和难忘的回忆。
						希望这本旅行手账能够珍藏下这些珍贵的时刻。
					</div>
					<div className="w-16 h-16 opacity-30">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							viewBox="0 0 24 24"
							fill="currentColor"
						>
							<title>Home icon</title>
							<path d="M11.47 3.84a.75.75 0 011.06 0l8.69 8.69a.75.75 0 101.06-1.06l-8.689-8.69a2.25 2.25 0 00-3.182 0l-8.69 8.69a.75.75 0 001.061 1.06l8.69-8.69z" />
							<path d="M12 5.432l8.159 8.159c.03.03.06.058.091.086v6.198c0 1.035-.84 1.875-1.875 1.875H15a.75.75 0 01-.75-.75v-4.5a.75.75 0 00-.75-.75h-3a.75.75 0 00-.75.75V21a.75.75 0 01-.75.75H5.625a1.875 1.875 0 01-1.875-1.875v-6.198a2.29 2.29 0 00.091-.086L12 5.43z" />
						</svg>
					</div>
					<div
						className="mt-auto opacity-70"
						style={{
							fontSize: `${Math.max(0.75, 0.875 * scale)}rem`,
						}}
					>
						© {new Date().getFullYear()} Travel Memo
					</div>
				</div>
			)}
		</div>
	);
});

Cover.displayName = "Cover";

/**
 * 日记地图组件
 */
const DiaryDayMap = ({
	coordinates,
}: { coordinates: { lat: number; lng: number }[] }) => {
	const { scale } = useBookSize();

	return (
		<div
			className="w-full bg-[#f0eee8] rounded-lg overflow-hidden mb-4 relative"
			style={{ height: `${Math.max(80, 144 * scale)}px` }}
		>
			<div className="absolute inset-0 flex items-center justify-center">
				<div
					className="text-neutral-400"
					style={{ fontSize: `${Math.max(0.75, 0.875 * scale)}rem` }}
				>
					地图加载中...
				</div>
			</div>
			{/* 这里后续可以集成真实地图 */}
			<div className="absolute inset-0 opacity-10">
				{coordinates.map((coord, index: number) => (
					<div
						key={index}
						className="absolute w-2 h-2 bg-primary rounded-full"
						style={{
							left: `${(coord.lng - 135) * 100 + 50}%`,
							top: `${(35 - coord.lat) * 100 + 50}%`,
						}}
					/>
				))}
			</div>
		</div>
	);
};

/**
 * 足迹内容组件
 */
const FootprintContent = ({
	footprint,
}: {
	footprint: TravelDiary["content"]["timelines"][0]["points"][0];
}) => {
	const { location, description, images, iconType } = footprint;
	const { scale } = useBookSize();

	// 根据不同类型选择图标
	const iconMap = {
		landmark: "🏛️",
		food: "🍜",
		park: "🌳",
		hotel: "🏨",
	};

	const icon = iconMap[iconType as keyof typeof iconMap] || "📍";

	return (
		<div className="mb-6 last:mb-0">
			<div className="flex items-center mb-2">
				<span className="mr-2">{icon}</span>
				<h3
					className="font-medium text-neutral-800"
					style={{ fontSize: `${Math.max(1, 1.125 * scale)}rem` }}
				>
					{location}
				</h3>
			</div>
			<p
				className="text-neutral-600 mb-3 leading-relaxed"
				style={{ fontSize: `${Math.max(0.75, 0.875 * scale)}rem` }}
			>
				{description}
			</p>
			{images && images.length > 0 && (
				<div className="flex flex-wrap gap-2">
					{images.map((image: TravelPointImage, idx: number) => (
						<div
							key={idx}
							className="relative w-full rounded-md overflow-hidden border border-neutral-200 shadow-sm"
							style={{
								height: `${Math.max(120, 160 * scale)}px`,
							}}
						>
							<Image
								src={image.url}
								alt={image.alt || `${location} 图片 ${idx + 1}`}
								fill
								className="object-cover"
							/>
						</div>
					))}
				</div>
			)}
		</div>
	);
};

/**
 * 创建内容页面
 */
function createContentPage(
	points: TravelDiary["content"]["timelines"][0]["points"],
	timelineIndex: number,
	pageNumber: number, // 直接使用页码参数
): React.ReactElement {
	return (
		<Page
			key={`timeline-${timelineIndex}-page-${pageNumber}`}
			number={pageNumber}
		>
			<div className="h-full">
				{points.map((point) => (
					<FootprintContent
						key={`point-${point.id}`}
						footprint={point}
					/>
				))}
			</div>
		</Page>
	);
}

/**
 * 估算内容高度
 */
function estimateContentHeight(
	point: TravelDiary["content"]["timelines"][0]["points"][0],
	scale: number,
	pageWidth: number,
): number {
	// 基础高度：标题 + 间距（根据缩放比例调整）
	let pointHeight = 80 * scale;

	// 添加描述文本高度（假设每100个字符约占25px高度，并根据缩放比例调整）
	pointHeight += Math.ceil(point.description.length / 100) * 25 * scale;

	// 添加图片高度（根据页面宽度和缩放比例动态计算）
	if (point.images && point.images.length > 0) {
		// 图片容器宽度约为页面宽度减去内边距
		const contentWidth = pageWidth - 24 * scale * 2; // 减去左右内边距

		// 每张图片高度按照其纵横比计算（假设图片纵横比约为3:4，加上边距和边框）
		const imageHeight = Math.max(120, 160 * scale); // 基础高度
		pointHeight += point.images.length * (imageHeight + 8); // 加上间距
	}

	return pointHeight;
}

/**
 * 为每天的行程生成页面
 */
function generateDayPages(
	timeline: TravelDiary["content"]["timelines"][0],
	timelineIndex: number,
	startPageNumber: number, // 添加起始页码参数
	bookSize: BookSizeContextType, // 通过参数传递 bookSize，避免在函数内部调用 Hook
): React.ReactElement[] {
	const { points, title, date } = timeline;
	const dayPages: React.ReactElement[] = [];
	const { scale, width, height } = bookSize;
	// 所有点位的坐标
	const dayCoordinates = points.map(
		(p: TravelDiary["content"]["timelines"][0]["points"][0]) => ({
			lat: p.latitude,
			lng: p.longitude,
		}),
	);
	// 第一页固定包含时间线标题、日期、地图和简介
	dayPages.push(
		<Page key={`timeline-${timelineIndex}-intro`} number={startPageNumber}>
			<div className="h-full flex flex-col">
				<div className="mb-6 border-b border-neutral-200 pb-4">
					<h2
						className="font-bold text-neutral-800 mb-2"
						style={{
							fontSize: `${Math.max(1.25, 1.5 * scale)}rem`,
						}}
					>
						{title}
					</h2>
					<div
						className="text-neutral-500"
						style={{
							fontSize: `${Math.max(0.75, 0.875 * scale)}rem`,
						}}
					>
						{format(new Date(date), "yyyy年MM月dd日", {
							locale: zhCN,
						})}
					</div>
				</div>

				{/* 当天行程地图 */}
				<DiaryDayMap coordinates={dayCoordinates} />

				<div
					className="text-neutral-600 mb-4 leading-relaxed"
					style={{
						fontSize: `${Math.max(0.75, 0.875 * scale)}rem`,
					}}
				>
					这一天，我们将在{title}探索{points.length}个精彩地点。
					{points.map(
						(
							p: TravelDiary["content"]["timelines"][0]["points"][0],
							i: number,
						) => (
							<span key={i} className="inline-block mx-1">
								{i === points.length - 1
									? "和"
									: i > 0
										? "、"
										: ""}
								{p.location}
							</span>
						),
					)}
					。
				</div>

				{/* 装饰元素 */}
				<div className="w-full my-2 flex justify-center">
					<div className="w-1/3 h-px bg-neutral-300" />
				</div>
			</div>
		</Page>,
	); // 智能内容分页 - 根据内容高度动态分配
	// 设置最大页面内容高度 - 根据书本实际尺寸和内边距动态计算
	// 考虑内边距和页码高度，可用高度约为总高度的85%
	const availableHeight = bookSize.height * 0.85;
	// 根据当前缩放比例调整最大页面内容高度
	const MAX_PAGE_CONTENT_HEIGHT = Math.floor(availableHeight - 48 * scale); // 减去上下内边距

	// 用于每一页的内容收集
	let currentPageContent: TravelDiary["content"]["timelines"][0]["points"] =
		[];
	let estimatedContentHeight = 0;
	// 当前处理的页码，从起始页码开始递增
	let currentPageNumber = startPageNumber + 1;
	// 根据内容高度预估分配到不同页面
	points.forEach(
		(point: TravelDiary["content"]["timelines"][0]["points"][0]) => {
			const pointHeight = estimateContentHeight(point, scale, width);

			// 如果当前页已经放不下这个点位，创建新页
			if (
				estimatedContentHeight + pointHeight >
					MAX_PAGE_CONTENT_HEIGHT &&
				currentPageContent.length > 0
			) {
				// 创建新页面并添加当前收集的内容
				dayPages.push(
					createContentPage(
						currentPageContent,
						timelineIndex,
						currentPageNumber++,
					),
				);

				// 重置当前页内容和高度计数
				currentPageContent = [point];
				estimatedContentHeight = pointHeight;
			} else {
				// 当前页还能放下，添加到当前页
				currentPageContent.push(point);
				estimatedContentHeight += pointHeight;
			}
		},
	);

	// 处理最后一页剩余内容
	if (currentPageContent.length > 0) {
		dayPages.push(
			createContentPage(
				currentPageContent,
				timelineIndex,
				currentPageNumber++,
			),
		);
	}

	return dayPages;
}

/**
 * 旅行日记手账翻页演示页面
 */
// 定义书本尺寸上下文
interface BookSizeContextType {
	width: number;
	height: number;
	scale: number;
}

const BookSizeContext = React.createContext<BookSizeContextType>({
	width: 500,
	height: 733,
	scale: 1,
});

// 使用Hook获取书本尺寸
function useBookSize() {
	return React.useContext(BookSizeContext);
}

export default function TravelDiaryBookDemo() {
	const [diary, setDiary] = useState<TravelDiary | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [currentPage, setCurrentPage] = useState(0);
	const bookRef = useRef<any>(null);

	// 基础尺寸
	const baseWidth = 500;
	const baseHeight = 733;
	const [bookSize, setBookSize] = useState<BookSizeContextType>({
		width: baseWidth,
		height: baseHeight,
		scale: 1,
	});

	// 计算书本响应式尺寸 - 全屏调整
	useEffect(() => {
		const calculateBookSize = () => {
			const availableWidth = window.innerWidth;
			const availableHeight = window.innerHeight;

			// 计算适应视口的缩放比例，保持书本宽高比
			// 书本展开是两页，所以宽度是 baseWidth * 2
			const scale = Math.min(
				availableWidth / (baseWidth * 2),
				availableHeight / baseHeight,
			);

			setBookSize({
				width: baseWidth, // 保持基础单页宽度
				height: baseHeight, // 保持基础单页高度
				scale: Math.max(0.1, scale), // 应用计算出的缩放比例，加个最小值防止过小
			});
		};

		calculateBookSize();
		window.addEventListener("resize", calculateBookSize);
		return () => {
			window.removeEventListener("resize", calculateBookSize);
		};
	}, [baseWidth, baseHeight]); // 依赖基础尺寸

	// 加载旅行日记数据
	useEffect(() => {
		const loadDiary = async () => {
			try {
				setIsLoading(true);
				// 这里使用关西旅行的示例数据
				const data = await fetchTravelDiary("kansai-trip");
				if (data) {
					// 将mock数据转换为数据库类型结构
					const mockData = data as any;
					const transformedData: TravelDiary = {
						...mockData,
						content: {
							timelines: mockData.timelines || [],
						},
					};
					setDiary(transformedData);
				}
			} catch (error) {
				console.error("加载旅行日记失败", error);
			} finally {
				setIsLoading(false);
			}
		};

		loadDiary();
	}, []);

	// 页面翻动回调
	const handlePageFlip = (e: { data: number }) => {
		setCurrentPage(e.data);
	};

	if (isLoading) {
		return (
			<div className="flex items-center justify-center min-h-screen bg-neutral-50 text-neutral-800">
				<div className="flex flex-col items-center">
					<div className="w-16 h-16 border-4 border-t-primary border-neutral-200 rounded-full animate-spin mb-4" />
					<p className="text-xl">正在加载旅行手账...</p>
				</div>
			</div>
		);
	}

	if (!diary) {
		return (
			<div className="flex items-center justify-center min-h-screen bg-neutral-50 text-neutral-800">
				<p className="text-xl">未找到旅行日记数据</p>
			</div>
		);
	} // 生成所有页面
	const allPages: React.ReactElement[] = [];

	// 添加封面
	allPages.push(<Cover key="front-cover" diary={diary} />);

	// 跟踪当前页码
	let currentPageNumber = 1;
	// 添加每天的内容页
	diary.content.timelines.forEach(
		(
			timeline: TravelDiary["content"]["timelines"][0],
			timelineIndex: number,
		) => {
			// 使用当前页码作为这个时间线的起始页码
			const dayPages = generateDayPages(
				timeline,
				timelineIndex,
				currentPageNumber,
				bookSize, // 传递 bookSize 对象给函数
			);
			allPages.push(...dayPages);

			// 更新页码计数，为下一个时间线准备
			currentPageNumber += dayPages.length;
		},
	);

	// 添加封底
	allPages.push(<Cover key="back-cover" diary={diary} isBack={true} />);

	// 计算总页数
	const pageCount = allPages.length;
	// 翻书组件属性配置
	const flipBookOptions = {
		width: 500, // 单页宽度，双页显示时两页总宽度为1000
		height: 733, // 书本高度，接近A5纸张比例
		size: "strench", // 固定大小
		minWidth: 250, // 最小宽度
		maxWidth: 800, // 最大宽度
		minHeight: 333, // 最小高度
		maxHeight: 1067, // 最大高度
		maxShadowOpacity: 0.5, // 最大阴影透明度
		showCover: true, // 显示封面
		mobileScrollSupport: true, // 移动端滚动支持
		useMouseEvents: true, // 使用鼠标事件
		swipeDistance: 30, // 滑动翻页的最小距离
		flippingTime: 1000, // 翻页动画时间
		usePortrait: false, // 设置为横向模式，实现双页显示
		autoSize: false, // 自动调整大小
		startPage: 0, // 起始页编号
		drawShadow: true, // 绘制阴影
		clickEventForward: true, // 转发点击事件
		showPageCorners: true, // 显示页面角落
		disableFlipByClick: false, // 不禁用点击翻页
	};
	console.log("bookSize (fullscreen)", bookSize);

	return (
		// 1. 最外层容器调整: 移除 padding, h-screen
		<div className="flex flex-col justify-center h-screen bg-neutral-100">
			{/* 2. 移除内层宽度限制和边距 */}
			<div className="w-full h-full">
				{/* 5. 移除标题 */}
				{/* <h1 className="text-2xl font-medium text-center mb-4 text-neutral-800">
                    {diary.title} - 旅行手账
                </h1> */}

				{/* 4. HTMLFlipBook 容器调整: 宽高占满，居中 */}
				<div className="w-full h-full flex justify-center items-center overflow-hidden bg-neutral-200">
					<BookSizeContext.Provider value={bookSize}>
						{/* 应用缩放 */}
						<div
						// style={{
						// 	transform: `scale(${bookSize.scale})`,
						// 	transformOrigin: "center center", // 居中缩放
						// }}
						>
							<HTMLFlipBook
								ref={bookRef}
								onFlip={handlePageFlip}
								className="book-container shadow-2xl" // 添加阴影
								width={bookSize.width} // 使用基础单页宽度
								height={bookSize.height} // 使用基础单页高度
								size="fixed"
								minWidth={250}
								maxWidth={800}
								minHeight={333}
								maxHeight={1067}
								maxShadowOpacity={0.5}
								showCover={true}
								mobileScrollSupport={true}
								useMouseEvents={true}
								swipeDistance={30}
								flippingTime={1000}
								usePortrait={false}
								autoSize={false}
								startPage={0}
								drawShadow={false}
								clickEventForward={true}
								showPageCorners={true}
								startZIndex={0}
								disableFlipByClick={false}
								style={{ backgroundColor: "#f8f5f0" }}
							>
								{allPages}
							</HTMLFlipBook>
						</div>
					</BookSizeContext.Provider>
				</div>

				{/* 5. 移除控制按钮 */}
				{/* <div className="flex justify-center gap-4 mt-6"> ... buttons ... </div> */}
			</div>
		</div>
	);
}
