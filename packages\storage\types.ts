export type CreateBucketHandler = (
	name: string,
	options?: {
		public?: boolean;
	},
) => Promise<void>;

export type GetSignedUploadUrlHandler = (
	path: string,
	options: {
		bucket: string;
	},
) => Promise<string>;

export type GetSignedUrlHander = (
	path: string,
	options: {
		bucket: string;
		expiresIn?: number;
	},
) => Promise<string>;

/**
 * 批量获取签名上传URL的参数
 */
export interface BatchUploadOptions {
	/**
	 * 存储桶名称
	 */
	bucket: string;

	/**
	 * 文件路径数组
	 */
	paths: string[];

	/**
	 * 内容类型
	 */
	contentType?: string;

	/**
	 * 区域（对于特定提供商如腾讯云COS）
	 */
	region?: string;
}

/**
 * 批量签名上传URL的结果
 */
export interface BatchUploadResult {
	/**
	 * 路径到签名URL的映射
	 */
	urls: Record<string, string>;

	/**
	 * 失败的路径列表
	 */
	failedPaths?: string[];

	/**
	 * 路径到错误信息的映射
	 */
	errors?: Record<string, string>;
}

/**
 * 批量删除对象的参数
 */
export interface BatchDeleteOptions {
	/**
	 * 存储桶名称
	 */
	bucket: string;

	/**
	 * 要删除的对象路径数组
	 */
	paths: string[];

	/**
	 * 区域（对于特定提供商如腾讯云COS）
	 */
	region?: string;
}

/**
 * 批量删除对象的结果
 */
export interface BatchDeleteResult {
	/**
	 * 成功删除的路径数组
	 */
	deletedPaths: string[];

	/**
	 * 删除失败的路径数组
	 */
	failedPaths?: string[];

	/**
	 * 路径到错误信息的映射
	 */
	errors?: Record<string, string>;
}

/**
 * 文件上传选项
 */
export interface UploadFileOptions {
	/**
	 * 存储桶名称
	 */
	bucket: string;

	/**
	 * 文件在存储桶中的路径
	 */
	path: string;

	/**
	 * 文件内容，可以是 Buffer 或 ReadableStream
	 */
	file: Buffer | NodeJS.ReadableStream;

	/**
	 * 文件的内容类型
	 */
	contentType?: string;

	/**
	 * 是否公开访问
	 */
	isPublic?: boolean;

	/**
	 * 区域（对于特定提供商如腾讯云COS）
	 */
	region?: string;

	/**
	 * 其他上传选项
	 */
	options?: Record<string, any>;
}

/**
 * 文件上传结果
 */
export interface UploadFileResult {
	/**
	 * 上传是否成功
	 */
	success: boolean;

	/**
	 * 已上传文件的路径（成功时）
	 */
	path?: string;

	/**
	 * 文件的ETag（如果有）
	 */
	etag?: string;

	/**
	 * 错误信息（失败时）
	 */
	error?: string;
}

/**
 * 存储提供商接口
 * 定义统一的对象存储访问方法
 */
export interface StorageProvider {
	/**
	 * 获取存储提供商名称
	 */
	getProviderName(): string;

	/**
	 * 创建存储桶
	 * @param name 存储桶名称
	 * @param options 创建选项
	 */
	createBucket(name: string, options?: { public?: boolean }): Promise<void>;

	/**
	 * 获取用于上传文件的签名URL
	 * @param path 文件路径
	 * @param options 选项，包括存储桶名称
	 */
	getSignedUploadUrl(
		path: string,
		options: { bucket: string; contentType?: string; region?: string },
	): Promise<string>;

	/**
	 * 获取用于访问文件的签名URL
	 * @param path 文件路径
	 * @param options 选项，包括存储桶名称和过期时间
	 */
	getSignedUrl(
		path: string,
		options: { bucket: string; expiresIn?: number; region?: string },
	): Promise<string>;

	/**
	 * 批量获取用于上传文件的签名URL
	 * @param options 批量上传选项
	 */
	getBatchSignedUploadUrls(
		options: BatchUploadOptions,
	): Promise<BatchUploadResult>;

	/**
	 * 批量删除对象
	 * @param options 批量删除选项
	 */
	batchDeleteObjects(options: BatchDeleteOptions): Promise<BatchDeleteResult>;

	/**
	 * 直接上传文件（服务端）
	 * 此方法用于服务端直接上传文件到存储提供商，而不通过客户端
	 * @param options 上传选项
	 */
	uploadFile(options: UploadFileOptions): Promise<UploadFileResult>;
}

/**
 * 存储提供商配置接口
 */
export interface StorageProviderConfig {
	/**
	 * 服务端点
	 */
	endpoint?: string;

	/**
	 * 区域
	 */
	region?: string;

	/**
	 * 访问密钥ID
	 */
	accessKeyId?: string;

	/**
	 * 访问密钥密码
	 */
	secretAccessKey?: string;

	/**
	 * 额外配置选项
	 */
	options?: Record<string, any>;
}
