"use client";

import { useEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";

export type TooltipPosition = "top" | "bottom" | "left" | "right";
export type TooltipMode = "static" | "dynamic" | "global";

interface BaseTooltipProps {
	isVisible: boolean;
	mode: TooltipMode;
	mousePosition?: { x: number; y: number };
	children: React.ReactNode;
	className?: string;
	onClose?: () => void;
	closeOnClickOutside?: boolean;
}

export function BaseTooltip({
	isVisible,
	mode,
	mousePosition,
	children,
	className = "",
	onClose,
	closeOnClickOutside = true,
}: BaseTooltipProps) {
	const tooltipRef = useRef<HTMLDivElement>(null);
	const [position, setPosition] = useState<TooltipPosition>("top");

	// 智能定位逻辑 - 只在动态和全局模式下执行
	useEffect(() => {
		if (
			!isVisible ||
			!tooltipRef.current ||
			!mousePosition ||
			mode === "static"
		) {
			return;
		}

		const tooltip = tooltipRef.current;
		const rect = tooltip.getBoundingClientRect();
		const viewportWidth = window.innerWidth;
		const viewportHeight = window.innerHeight;

		let newPosition: TooltipPosition = "top";

		// 检查上方空间
		if (mousePosition.y - rect.height - 16 < 0) {
			newPosition = "bottom";
		}

		// 检查下方空间（如果上方也不够）
		if (
			newPosition === "bottom" &&
			mousePosition.y + rect.height + 16 > viewportHeight
		) {
			// 检查左右空间
			if (mousePosition.x + rect.width + 16 <= viewportWidth) {
				newPosition = "right";
			} else if (mousePosition.x - rect.width - 16 >= 0) {
				newPosition = "left";
			}
		}

		setPosition(newPosition);
	}, [isVisible, mousePosition, mode]);

	// 点击外部关闭
	useEffect(() => {
		if (!isVisible || !closeOnClickOutside || !onClose) {
			return;
		}

		const handleClickOutside = (event: MouseEvent) => {
			if (
				tooltipRef.current &&
				!tooltipRef.current.contains(event.target as Node)
			) {
				onClose();
			}
		};

		// 延迟添加事件监听器，避免立即触发
		const timer = setTimeout(() => {
			document.addEventListener("mousedown", handleClickOutside);
		}, 100);

		return () => {
			clearTimeout(timer);
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [isVisible, closeOnClickOutside, onClose]);

	const getPositionClasses = () => {
		if (mode === "static") {
			return "relative";
		}

		switch (position) {
			case "bottom":
				return "absolute top-full left-1/2 transform -translate-x-1/2 mt-2";
			case "left":
				return "absolute right-full top-1/2 transform -translate-y-1/2 mr-2";
			case "right":
				return "absolute left-full top-1/2 transform -translate-y-1/2 ml-2";
			default: // top
				return "absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2";
		}
	};

	const getArrowClasses = () => {
		if (mode === "static") {
			return "absolute top-full left-1/2 transform -translate-x-1/2 border-t-[6px] border-l-[6px] border-r-[6px] border-transparent border-t-white";
		}

		switch (position) {
			case "bottom":
				return "absolute bottom-full left-1/2 transform -translate-x-1/2 border-b-[6px] border-l-[6px] border-r-[6px] border-transparent border-b-white";
			case "left":
				return "absolute left-full top-1/2 transform -translate-y-1/2 border-l-[6px] border-t-[6px] border-b-[6px] border-transparent border-l-white";
			case "right":
				return "absolute right-full top-1/2 transform -translate-y-1/2 border-r-[6px] border-t-[6px] border-b-[6px] border-transparent border-r-white";
			default: // top
				return "absolute top-full left-1/2 transform -translate-x-1/2 border-t-[6px] border-l-[6px] border-r-[6px] border-transparent border-t-white";
		}
	};

	if (!isVisible) {
		return null;
	}

	const tooltipContent = (
		<div
			ref={tooltipRef}
			className={`z-50 animate-in fade-in-0 zoom-in-95 duration-200 ${getPositionClasses()} ${className}`}
		>
			{/* 主容器 */}
			<div className="relative">
				{children}
				{/* 箭头指针 */}
				<div className={getArrowClasses()} />
			</div>
		</div>
	);

	// 全局模式使用Portal渲染到body
	if (mode === "global" && mousePosition) {
		return createPortal(
			<div
				className="fixed pointer-events-none z-[9999]"
				style={{
					left: mousePosition.x,
					top: mousePosition.y,
				}}
			>
				<div className="pointer-events-auto">{tooltipContent}</div>
			</div>,
			document.body,
		);
	}

	return tooltipContent;
}
