{"extends": "@repo/tsconfig/nextjs.json", "compilerOptions": {"jsx": "preserve", "plugins": [{"name": "next"}], "paths": {"@analytics": ["./modules/analytics"], "@marketing/*": ["./modules/marketing/*"], "@saas/*": ["./modules/saas/*"], "@ui/*": ["./modules/ui/*"], "@i18n": ["./modules/i18n"], "@i18n/*": ["./modules/i18n/*"], "@shared/*": ["./modules/shared/*"], "@modules/*": ["./modules/*"], "@utils/*": ["../../packages/utils/*"], "content-collections": ["./.content-collections/generated"], "@packages/ai": ["../../packages/ai"], "@packages/ai/*": ["../../packages/ai/*"], "@packages/api": ["../../packages/api"], "@packages/api/*": ["../../packages/api/*"], "@packages/auth": ["../../packages/auth"], "@packages/auth/*": ["../../packages/auth/*"], "@packages/database": ["../../packages/database"], "@packages/database/*": ["../../packages/database/*"], "@packages/i18n": ["../../packages/i18n"], "@packages/i18n/*": ["../../packages/i18n/*"], "@packages/logs": ["../../packages/logs"], "@packages/logs/*": ["../../packages/logs/*"], "@packages/mail": ["../../packages/mail"], "@packages/mail/*": ["../../packages/mail/*"], "@packages/payments": ["../../packages/payments"], "@packages/payments/*": ["../../packages/payments/*"], "@packages/storage": ["../../packages/storage"], "@packages/storage/*": ["../../packages/storage/*"], "@packages/utils": ["../../packages/utils"], "@packages/utils/*": ["../../packages/utils/*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.cjs", "**/*.mjs", ".next/types/**/*.ts"], "exclude": ["node_modules"]}