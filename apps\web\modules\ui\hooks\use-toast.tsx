// 这个文件基于 shadcn ui toast 实现，提供 toast 通知功能
import { type ReactNode, createContext, useContext, useState } from "react";

// toast 通知类型
export type ToastProps = {
	id: string;
	title?: ReactNode;
	description?: ReactNode;
	action?: ReactNode;
	variant?: "default" | "destructive" | "success";
	duration?: number;
	open?: boolean;
	onOpenChange?: (open: boolean) => void;
};

type ToastActionElement = React.ReactElement<{
	onClick?: () => void;
}>;

type ToastContextValue = {
	toasts: ToastProps[];
	toast: (props: Omit<ToastProps, "id" | "open" | "onOpenChange">) => void;
	dismiss: (toastId: string) => void;
	dismissAll: () => void;
};

const ToastContext = createContext<ToastContextValue>({
	toasts: [],
	toast: () => {},
	dismiss: () => {},
	dismissAll: () => {},
});

export function ToastProvider({ children }: { children: ReactNode }) {
	const [toasts, setToasts] = useState<ToastProps[]>([]);

	function toast(props: Omit<ToastProps, "id" | "open" | "onOpenChange">) {
		const id = Math.random().toString(36).slice(2);
		const newToast = { ...props, id, open: true };

		setToasts((toasts) => [...toasts, newToast]);

		return id;
	}

	function dismiss(toastId: string) {
		setToasts((toasts) => {
			const targetToast = toasts.find((toast) => toast.id === toastId);
			if (!targetToast) return toasts;

			return toasts.map((toast) => {
				if (toast.id === toastId) {
					return { ...toast, open: false };
				}
				return toast;
			});
		});

		// 删除已关闭的toast (在动画结束后)
		setTimeout(() => {
			setToasts((toasts) =>
				toasts.filter((toast) => toast.id !== toastId),
			);
		}, 300);
	}

	function dismissAll() {
		setToasts((toasts) =>
			toasts.map((toast) => ({ ...toast, open: false })),
		);
	}

	return (
		<ToastContext.Provider value={{ toasts, toast, dismiss, dismissAll }}>
			{children}
		</ToastContext.Provider>
	);
}

export function useToast() {
	const context = useContext(ToastContext);

	if (!context) {
		throw new Error("useToast 必须在 ToastProvider 中使用");
	}

	return context;
}

// 提供一个直接访问 toast 的方法，无需使用 hook
// 这在非组件环境中很有用
export const toast = (
	props: Omit<ToastProps, "id" | "open" | "onOpenChange">,
) => {
	const toastFunction =
		typeof document !== "undefined"
			? // 如果在浏览器环境，获取上下文中的toast函数
				useToast().toast
			: // 如果在服务器环境，返回一个空函数
				() => "";

	return toastFunction(props);
};
