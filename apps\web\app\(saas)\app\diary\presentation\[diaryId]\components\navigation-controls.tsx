"use client";

interface NavigationControlsProps {
	currentIndex: number;
	totalItems: number;
	onPrevious: () => void;
	onNext: () => void;
}

export function NavigationControls({
	currentIndex,
	totalItems,
	onPrevious,
	onNext,
}: NavigationControlsProps) {
	const isFirst = currentIndex === 0;
	const isLast = currentIndex === totalItems - 1;

	return (
		<div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-20 flex items-center gap-2 md:gap-4 bg-black/50 backdrop-blur-sm rounded-full px-4 py-2">
			{/* 上一个按钮 */}
			<button
				onClick={onPrevious}
				disabled={isFirst}
				className="p-2 rounded-full bg-white/10 hover:bg-white/20 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
				aria-label="上一个足迹"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					width="20"
					height="20"
					viewBox="0 0 24 24"
					fill="none"
					stroke="currentColor"
					strokeWidth="2"
					strokeLinecap="round"
					strokeLinejoin="round"
					className="text-white"
				>
					<path d="m15 18-6-6 6-6" />
				</svg>
			</button>

			{/* 进度指示 */}
			<div className="text-white text-sm md:text-base font-medium">
				{currentIndex + 1} / {totalItems}
			</div>

			{/* 下一个按钮 */}
			<button
				onClick={onNext}
				disabled={isLast}
				className="p-2 rounded-full bg-white/10 hover:bg-white/20 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
				aria-label="下一个足迹"
			>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					width="20"
					height="20"
					viewBox="0 0 24 24"
					fill="none"
					stroke="currentColor"
					strokeWidth="2"
					strokeLinecap="round"
					strokeLinejoin="round"
					className="text-white"
				>
					<path d="m9 18 6-6-6-6" />
				</svg>
			</button>
		</div>
	);
}
