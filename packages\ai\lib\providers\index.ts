import { AIProviderType } from "../types";
import { GeminiProvider } from "./gemini";
import { OpenAIProvider } from "./openai";
import { VolcengineProvider } from "./volcengine";

export { OpenAIProvider, GeminiProvider, VolcengineProvider };

// 工厂函数，根据提供商类型创建相应的AI提供商实例
export function createProvider(type: AIProviderType, options?: any) {
	switch (type) {
		case AIProviderType.OPENAI:
			return new OpenAIProvider(
				options?.textModel,
				options?.imageModel,
				options?.audioModel,
				options?.apiKey,
			);
		case AIProviderType.GEMINI:
			return new GeminiProvider(options?.apiKey, options?.apiEndpoint);
		case AIProviderType.VOLCENGINE:
			return new VolcengineProvider(
				options?.apiKey,
				options?.secretKey,
				options?.apiEndpoint,
			);
		default:
			throw new Error(`未支持的AI提供商类型: ${type}`);
	}
}
