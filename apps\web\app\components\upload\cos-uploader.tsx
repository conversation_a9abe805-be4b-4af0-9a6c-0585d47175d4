import { type ChangeEvent, useState } from "react";
import { toast } from "sonner";

interface COSUploaderProps {
	bucket: string;
	region?: string;
	path?: string; // 可选的固定路径
	contentType?: string;
	onSuccess?: (result: { path: string; url?: string }) => void;
	onError?: (error: string) => void;
}

export function COSUploader({
	bucket,
	region = "ap-shanghai",
	path,
	contentType,
	onSuccess,
	onError,
}: COSUploaderProps) {
	const [isUploading, setIsUploading] = useState(false);
	const [progress, setProgress] = useState(0);

	// 处理文件选择
	const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		if (!file) return;

		try {
			setIsUploading(true);
			setProgress(10);

			// 1. 生成随机文件路径
			const filePath = path || `uploads/${Date.now()}-${file.name}`;

			// 2. 获取上传 URL
			const uploadUrlResponse = await fetch(
				"/api/storage/get-upload-urls",
				{
					method: "POST",
					headers: { "Content-Type": "application/json" },
					body: JSON.stringify({
						bucket,
						path: filePath,
						contentType: contentType || file.type,
						region,
						provider: "tencent-cos", // 明确指定提供商
					}),
				},
			);

			if (!uploadUrlResponse.ok) {
				const error = await uploadUrlResponse.text();
				throw new Error(`获取上传URL失败: ${error}`);
			}

			const { url } = await uploadUrlResponse.json();
			if (!url) {
				throw new Error("获取上传URL失败: 返回的URL为空");
			}

			setProgress(30);
			console.log("获取到上传URL:", url);

			// 3. 使用fetch上传文件，注意设置正确的请求头
			const uploadResponse = await fetch(url, {
				method: "PUT",
				headers: {
					"Content-Type": contentType || file.type,
					"x-cos-acl": "private",
				},
				body: file,
			});

			setProgress(90);

			if (!uploadResponse.ok) {
				const errorText = await uploadResponse.text();
				console.error("上传失败详情:", errorText);
				throw new Error(
					`上传失败，HTTP状态码: ${uploadResponse.status}`,
				);
			}

			// 4. 处理成功上传
			setProgress(100);
			toast.success(`文件 ${file.name} 上传成功！`);

			if (onSuccess) {
				onSuccess({ path: filePath });
			}
		} catch (error) {
			console.error("上传过程中出错:", error);
			const errorMessage =
				error instanceof Error
					? error.message
					: "上传文件时发生未知错误";

			toast.error(errorMessage);

			if (onError) {
				onError(errorMessage);
			}
		} finally {
			// 延迟清理状态，以便显示进度完成
			setTimeout(() => {
				setIsUploading(false);
				setProgress(0);
			}, 800);
		}
	};

	// 显示更详细的进度
	const progressLabel = isUploading
		? progress === 100
			? "上传完成!"
			: progress < 30
				? "准备上传..."
				: progress < 90
					? "正在上传..."
					: "处理中..."
		: "选择文件";

	return (
		<div className="flex flex-col gap-3">
			<div className="flex items-center gap-2">
				<label
					htmlFor="cos-file-input"
					className={`px-3 py-2 rounded cursor-pointer transition-colors ${
						isUploading
							? "bg-blue-100 text-blue-700"
							: "bg-blue-500 text-white hover:bg-blue-600"
					}`}
				>
					{progressLabel}
				</label>
				<input
					id="cos-file-input"
					type="file"
					onChange={handleFileChange}
					disabled={isUploading}
					className="hidden"
				/>
			</div>

			{isUploading && (
				<div className="w-full bg-gray-200 rounded-full h-2">
					<div
						className="bg-blue-600 rounded-full h-2 transition-all duration-300"
						style={{ width: `${progress}%` }}
					></div>
				</div>
			)}

			<div className="text-sm text-gray-500">
				{isUploading && <p>正在上传到存储桶: {bucket}</p>}
			</div>
		</div>
	);
}
