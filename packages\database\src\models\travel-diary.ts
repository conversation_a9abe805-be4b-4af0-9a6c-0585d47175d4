import type { JSONContent } from "@tiptap/core";
// 旅行日记数据库操作
import { db } from "../client";
import type { TravelDiaryContent, TravelPoint } from "../types/travel-diary"; // 确保 TravelPoint 已导出或在此定义

/**
 * 获取指定用户的所有旅行日记
 * @param userId 用户ID
 */
export async function getUserTravelDiaries(userId: string) {
	return await db.travelDiary.findMany({
		where: {
			userId,
			status: 0, // 只查询未删除的记录（status=0）
		},
		orderBy: {
			updatedAt: "desc",
		},
	});
}

/**
 * 获取单个旅行日记
 * @param id 日记ID
 * @param userId 可选的用户ID，用于权限检查
 * @param isExportMode 是否为视频导出模式，此模式下将忽略权限检查
 */
export async function getTravelDiary(
	id: string,
	userId?: string,
	isExportMode = false,
) {
	// 如果是导出模式，直接忽略权限检查
	if (isExportMode) {
		return await db.travelDiary.findFirst({
			where: {
				id,
				status: 0, // 只查询未删除的记录
			},
		});
	}

	// 正常权限检查逻辑
	return await db.travelDiary.findFirst({
		where: {
			id,
			status: 0, // 只查询未删除的记录
			// 如果提供了用户ID，则只返回该用户的日记或公开日记
			...(userId
				? {
						OR: [{ userId }, { isPublic: true }],
					}
				: { isPublic: true }), // 如果没有提供用户ID，则只返回公开日记
		},
	});
}

/**
 * 创建旅行日记
 * @param userId 用户ID
 * @param data 日记数据
 */
export async function createTravelDiary(
	userId: string,
	data: {
		title: string;
		subtitle?: string | null;
		coverImage?: string | null;
		content: TravelDiaryContent;
		isPublic?: boolean;
	},
) {
	return await db.travelDiary.create({
		data: {
			...data,
			userId,
			content: data.content as any,
		},
	});
}

/**
 * 更新旅行日记
 * @param id 日记ID
 * @param userId 用户ID
 * @param data 更新的数据
 */
export async function updateTravelDiary(
	id: string,
	userId: string,
	data: Partial<{
		title: string;
		subtitle?: string | null;
		coverImage?: string | null;
		content: TravelDiaryContent;
		isPublic: boolean;
	}>,
) {
	// 确保用户只能更新自己的日记
	return await db.travelDiary.updateMany({
		where: {
			id,
			userId,
		},
		data: {
			...data,
			...(data.content && { content: data.content as any }),
		},
	});
}

/**
 * 删除旅行日记
 * @param id 日记ID
 * @param userId 用户ID
 */
export async function deleteTravelDiary(id: string, userId: string) {
	// 确保用户只能删除自己的日记
	return await db.travelDiary.deleteMany({
		where: {
			id,
			userId,
		},
	});
}

/**
 * 获取公开的旅行日记
 */
export async function getPublicTravelDiaries() {
	return await db.travelDiary.findMany({
		where: {
			isPublic: true,
		},
		orderBy: {
			updatedAt: "desc",
		},
	});
}

/**
 * 获取用户的所有足迹点核心信息
 * @param userId 用户ID
 */
export async function getUserFootprints(
	userId: string,
): Promise<
	(Pick<
		TravelPoint,
		| "id"
		| "latitude"
		| "longitude"
		| "location"
		| "date"
		| "country"
		| "city"
		| "images"
		| "description"
		| "iconType"
	> & { diaryId: string; timelineId?: string; timelineTitle?: string })[]
> {
	const diaries = await db.travelDiary.findMany({
		where: { userId },
		select: {
			id: true, // 添加日记ID
			content: true, // 只选择 content 字段
		},
	});

	const footprints: (Pick<
		TravelPoint,
		| "id"
		| "latitude"
		| "longitude"
		| "location"
		| "date"
		| "country"
		| "city"
		| "images"
		| "description"
		| "iconType"
	> & { diaryId: string; timelineId?: string; timelineTitle?: string })[] =
		[];

	for (const diary of diaries) {
		if (diary.content && typeof diary.content === "object") {
			const content = diary.content as unknown as TravelDiaryContent; // 类型断言
			if (content.timelines && Array.isArray(content.timelines)) {
				for (const timeline of content.timelines) {
					if (timeline.points && Array.isArray(timeline.points)) {
						for (const point of timeline.points) {
							// 确保 point 包含所需字段
							if (
								point.latitude !== undefined &&
								point.longitude !== undefined
							) {
								footprints.push({
									id: point.id,
									latitude: point.latitude,
									longitude: point.longitude,
									location: point.location || "",
									date: point.date,
									country: point.country || undefined,
									city: point.city || undefined,
									images: point.images || [],
									description: point.description || "",
									iconType: point.iconType || "",
									diaryId: diary.id,
									timelineId: timeline.id,
									timelineTitle: timeline.title,
								});
							}
						}
					}
				}
			}
		}
	}

	return footprints;
}

/**
 * 保存日记的富文本草稿内容
 */
export async function saveDiaryRichTextDraft(
	diaryId: string,
	userId: string,
	draftContent: JSONContent,
) {
	return db.travelDiary.update({
		where: {
			id: diaryId,
			userId: userId,
		},
		data: {
			richTextDraftContent: draftContent,
		},
	});
}

/**
 * 获取日记的富文本草稿内容
 */
export async function getDiaryRichTextDraft(diaryId: string, userId: string) {
	const diary = await db.travelDiary.findUnique({
		where: {
			id: diaryId,
			userId: userId,
		},
		select: {
			richTextDraftContent: true,
		},
	});

	return diary?.richTextDraftContent as JSONContent | null;
}
