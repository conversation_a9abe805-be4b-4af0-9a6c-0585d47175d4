import { useMemo } from "react";
import {
	MAP_PROJECTION_CONFIGS,
	type MapProjectionType,
} from "../components/types";
import { useTravelStatTranslations } from "./useTravelStatTranslations";

export function useTranslatedProjections() {
	const t = useTravelStatTranslations();

	const translatedProjections = useMemo(() => {
		const projections = Object.keys(
			MAP_PROJECTION_CONFIGS,
		) as MapProjectionType[];
		return projections.map((projKey) => ({
			id: projKey,
			...MAP_PROJECTION_CONFIGS[projKey],
			name: t.mapProjections.names[projKey](),
			description: t.mapProjections.descriptions[projKey](),
			shortDesc: t.mapProjections.shortDescs[projKey](),
			badge: t.mapProjections.badges[projKey](),
		}));
	}, [t]);

	const getTranslatedProjection = (projKey: MapProjectionType) => {
		return translatedProjections.find((p) => p.id === projKey);
	};

	return {
		t,
		translatedProjections,
		getTranslatedProjection,
	};
}
