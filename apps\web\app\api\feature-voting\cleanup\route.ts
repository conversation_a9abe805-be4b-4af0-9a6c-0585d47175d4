import { NextResponse } from "next/server";
import { cleanupTestData } from "../../../(saas)/app/feature-voting/test-data";

/**
 * POST /api/feature-voting/cleanup
 * 清理Feature Voting测试数据
 */
export async function POST() {
	try {
		console.log("开始清理Feature Voting测试数据...");

		await cleanupTestData();

		return NextResponse.json({
			success: true,
			message: "测试数据清理成功",
			timestamp: new Date().toISOString(),
		});
	} catch (error) {
		console.error("清理测试数据失败:", error);

		return NextResponse.json(
			{
				success: false,
				message: "测试数据清理失败",
				error: error instanceof Error ? error.message : "未知错误",
				timestamp: new Date().toISOString(),
			},
			{ status: 500 },
		);
	}
}
