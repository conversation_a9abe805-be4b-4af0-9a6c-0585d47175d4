"use client";

import type { JSONContent } from "@tiptap/core";
import Image from "@tiptap/extension-image";
import Link from "@tiptap/extension-link";
import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { Skeleton } from "@ui/components/skeleton";
import debounce from "lodash.debounce";
import React, { useEffect, useState, useRef, useCallback } from "react";
import { toast } from "sonner";
import { AutoSaveIndicator } from "./AutoSaveIndicator";
import { ContentValidator } from "./ContentValidator";
import { KeyboardShortcuts } from "./KeyboardShortcuts";
import { MenuBar } from "./MenuBar";
import { CustomTravelPointNode, CustomTravelTimelineNode } from "./extensions";

// 自定义操作按钮接口
interface CustomAction {
	icon: React.ReactNode;
	title: string;
	onClick: () => void;
	disabled?: boolean;
}

interface RichTextDiaryEditorProps {
	initialContent: JSONContent | null;
	onContentChange: (content: JSONContent) => void;
	className?: string;
	isLoading?: boolean;
	customActions?: CustomAction[]; // 新增：自定义操作按钮
	// 新增：自动保存相关属性
	isModified?: boolean;
	isSaving?: boolean;
	lastSaved?: Date;
	onManualSave?: () => void;
	// 新增：验证器开关
	showValidator?: boolean;
}

export const RichTextDiaryEditor = ({
	initialContent,
	onContentChange,
	className,
	isLoading = false,
	customActions,
	isModified = false,
	isSaving = false,
	lastSaved,
	onManualSave,
	showValidator = true,
}: RichTextDiaryEditorProps) => {
	const [isInitialized, setIsInitialized] = useState(false);
	const [isMenuBarFixed, setIsMenuBarFixed] = useState(false);
	const [isFormatting, setIsFormatting] = useState(false);
	const containerRef = useRef<HTMLDivElement>(null);
	const editorContentRef = useRef<HTMLDivElement>(null);
	const menuBarRef = useRef<HTMLDivElement>(null);
	const menuBarHeight = useRef(0);

	// 添加日志以跟踪初始内容
	useEffect(() => {
		console.log(
			"RichTextDiaryEditor - 接收到的initialContent:",
			initialContent,
		);
	}, [initialContent]);

	// 创建防抖处理函数，减少内容变化时的回调频率
	// eslint-disable-next-line react-hooks/exhaustive-deps
	const debouncedContentChange = useCallback(
		debounce((content: JSONContent) => {
			onContentChange(content);
		}, 1000), // 设置1秒的防抖时间
		[onContentChange],
	);

	const editor = useEditor({
		extensions: [
			StarterKit.configure({
				heading: {
					levels: [1, 2, 3],
				},
			}),
			Image.configure({
				inline: false,
				allowBase64: true,
				HTMLAttributes: {
					class: "max-w-full rounded-md",
				},
			}),
			Link.configure({
				openOnClick: false,
				HTMLAttributes: {
					class: "text-blue-600 underline",
					target: "_blank",
					rel: "noopener noreferrer",
				},
			}),
			CustomTravelPointNode,
			CustomTravelTimelineNode,
		],
		content: "",
		onUpdate: ({ editor: currentEditor }) => {
			console.log("[DEBUG] onUpdate 回调触发", {
				isInitialized,
				contentSize: currentEditor.getJSON().content?.length || 0,
				timestamp: new Date().toISOString(),
				editorDestroyed: currentEditor.isDestroyed,
			});

			if (isInitialized) {
				console.log("[DEBUG] 编辑器已初始化，触发防抖内容变化处理");
				// 使用防抖函数处理内容变化
				debouncedContentChange(currentEditor.getJSON());
			} else {
				console.log("[DEBUG] 编辑器未初始化，跳过内容变化处理");
			}
		},
		editorProps: {
			attributes: {
				class: "prose prose-blue max-w-none focus:outline-none min-h-[300px] p-4",
			},
		},
		immediatelyRender: false,
		// 添加这个选项来避免 flushSync 错误
		shouldRerenderOnTransaction: false,
		onBeforeCreate: ({ editor }) => {
			console.log("[DEBUG] onBeforeCreate 回调", {
				timestamp: new Date().toISOString(),
				extensions: editor.extensionManager.extensions.map(
					(ext) => ext.name,
				),
			});
		},
		onCreate: ({ editor }) => {
			console.log("[DEBUG] onCreate 回调", {
				timestamp: new Date().toISOString(),
				nodeTypes: Object.keys(editor.schema.nodes),
				editorDestroyed: editor.isDestroyed,
			});
		},
		onDestroy: () => {
			console.log("[DEBUG] onDestroy 回调", {
				timestamp: new Date().toISOString(),
			});
		},
		onTransaction: ({ editor, transaction }) => {
			console.log("[DEBUG] onTransaction 回调", {
				timestamp: new Date().toISOString(),
				docChanged: transaction.docChanged,
				steps: transaction.steps.length,
				editorDestroyed: editor.isDestroyed,
			});
		},
	});

	// 初始化编辑器内容
	useEffect(() => {
		console.log("[DEBUG] 初始化编辑器内容 useEffect 触发", {
			hasEditor: !!editor,
			editorDestroyed: editor?.isDestroyed,
			isInitialized,
			hasInitialContent: !!initialContent,
			initialContentType: initialContent?.type,
			timestamp: new Date().toISOString(),
		});

		if (!editor) {
			console.log("[DEBUG] editor为空，跳过初始化");
			return;
		}

		if (editor.isDestroyed) {
			console.log("[DEBUG] editor已销毁，跳过初始化");
			return;
		}

		console.log("[DEBUG] 开始初始化编辑器内容", {
			editorDestroyed: editor.isDestroyed,
			isInitialized,
			hasInitialContent: !!initialContent,
		});

		// 使用 setTimeout 确保在下一个事件循环中执行，避免在渲染周期内更新状态
		const initializeContent = () => {
			console.log("[DEBUG] 执行初始化函数", {
				timestamp: new Date().toISOString(),
				editorDestroyed: editor.isDestroyed,
			});

			if (editor.isDestroyed) {
				console.log("[DEBUG] 初始化函数执行时editor已销毁，跳过");
				return;
			}

			if (initialContent) {
				console.log("[DEBUG] 处理initialContent", {
					type: initialContent.type,
					contentCount: initialContent.content?.length || 0,
					rawContent: `${JSON.stringify(initialContent).substring(0, 200)}...`,
				});

				try {
					// 检查initialContent的结构是否符合预期
					const safeContent = validateContent(initialContent);
					console.log("[DEBUG] 验证后的safeContent:", {
						type: safeContent.type,
						contentCount: safeContent.content?.length || 0,
						firstNodeType: safeContent.content?.[0]?.type,
					});

					// 使用 requestAnimationFrame 确保在下一帧执行，避免 flushSync 错误
					console.log(
						"[DEBUG] 准备设置编辑器内容，使用requestAnimationFrame",
					);
					requestAnimationFrame(() => {
						console.log("[DEBUG] requestAnimationFrame 回调执行", {
							editorDestroyed: editor.isDestroyed,
							timestamp: new Date().toISOString(),
						});

						if (editor.isDestroyed) {
							console.log(
								"[DEBUG] requestAnimationFrame回调执行时editor已销毁，跳过",
							);
							return;
						}

						// 使用 queueMicrotask 来确保在微任务队列中执行
						queueMicrotask(() => {
							console.log(
								"[DEBUG] queueMicrotask 回调执行，准备调用setContent",
								{
									editorDestroyed: editor.isDestroyed,
									timestamp: new Date().toISOString(),
								},
							);

							if (editor.isDestroyed) {
								console.log(
									"[DEBUG] queueMicrotask回调执行时editor已销毁，跳过",
								);
								return;
							}

							try {
								// 设置编辑器内容
								console.log(
									"[DEBUG] 调用 editor.commands.setContent",
								);
								editor.commands.setContent(safeContent);
								console.log(
									"[DEBUG] setContent 调用完成，当前内容:",
									editor.getJSON(),
								);

								// 标记为已初始化
								if (!isInitialized) {
									console.log("[DEBUG] 标记编辑器为已初始化");
									setIsInitialized(true);
								}
							} catch (setContentError) {
								console.error(
									"[DEBUG] setContent 调用出错:",
									setContentError,
								);
								throw setContentError;
							}
						});
					});
				} catch (err) {
					console.error("[DEBUG] 设置编辑器内容时出错:", err);
					// 错误恢复
					requestAnimationFrame(() => {
						setTimeout(() => {
							if (!editor.isDestroyed) {
								console.log(
									"[DEBUG] 错误恢复：清空内容并设置错误提示",
								);
								editor.commands.clearContent();
								editor.commands.insertContent(
									"<p>编辑器初始化出错，请重试</p>",
								);
								setIsInitialized(true);
							}
						}, 0);
					});
				}
			} else if (!isInitialized) {
				console.log("[DEBUG] 没有initialContent，设置为空文档");
				requestAnimationFrame(() => {
					setTimeout(() => {
						if (!editor.isDestroyed) {
							console.log("[DEBUG] 设置空文档");
							editor.commands.setContent({
								type: "doc",
								content: [{ type: "paragraph" }],
							});
							console.log(
								"[DEBUG] 空文档设置完成，标记为已初始化",
							);
							setIsInitialized(true);
						}
					}, 0);
				});
			}
		};

		// 使用 setTimeout 延迟执行，确保不在渲染周期内
		console.log("[DEBUG] 设置初始化延迟执行");
		const timeoutId = setTimeout(initializeContent, 300); // 增加延迟到300ms

		return () => {
			console.log("[DEBUG] 清理初始化 useEffect");
			clearTimeout(timeoutId);
		};
	}, [editor, initialContent]);

	// 验证并修复内容结构
	const validateContent = (content: JSONContent): JSONContent => {
		console.log("[DEBUG] validateContent 开始验证", {
			hasContent: !!content,
			contentType: content?.type,
			timestamp: new Date().toISOString(),
		});

		if (!content) {
			console.log("[DEBUG] validateContent: 内容为空，返回默认段落");
			return { type: "doc", content: [{ type: "paragraph" }] };
		}

		// 检查是否为嵌套结构 {content: {type: 'doc', ...}}
		if (
			content.content &&
			typeof content.content === "object" &&
			"type" in content.content &&
			content.content.type === "doc" &&
			!content.type
		) {
			console.log(
				"[DEBUG] validateContent: 检测到嵌套content结构，提取内部content",
			);
			return validateContent(content.content as JSONContent);
		}

		// 确保内容有正确的根节点类型
		if (content.type !== "doc") {
			console.log("[DEBUG] validateContent: 内容根节点类型错误", {
				expectedType: "doc",
				actualType: content.type || "未定义",
				hasContentArray: Array.isArray(content.content),
			});

			// 如果缺少type，但有content数组，创建一个doc包装它
			return {
				type: "doc",
				content: Array.isArray(content.content)
					? content.content
					: [{ type: "paragraph" }],
			};
		}

		// 确保内容数组存在且有效
		if (!Array.isArray(content.content) || content.content.length === 0) {
			console.log("[DEBUG] validateContent: 内容数组无效或为空", {
				isArray: Array.isArray(content.content),
				length: content.content?.length || 0,
			});
			return {
				type: "doc",
				content: [{ type: "paragraph" }],
			};
		}

		console.log("[DEBUG] validateContent: 验证内容节点类型分布", {
			nodeTypes: content.content.map((node) => node.type),
			totalNodes: content.content.length,
		});

		// 创建有效的安全内容文档
		const safeContent: JSONContent = {
			type: "doc",
			content: [],
		};

		// 验证并过滤每个顶级节点
		safeContent.content = content.content
			.filter((node) => {
				const isValid = node && typeof node === "object" && node.type;
				if (!isValid) {
					console.log(
						"[DEBUG] validateContent: 过滤掉无效节点:",
						node,
					);
				}
				return isValid;
			})
			.map((node) => {
				// 特殊处理时间线和点位节点
				if (node.type === "travelTimeline") {
					console.log("[DEBUG] validateContent: 处理时间线节点", {
						timelineId: node.attrs?.timelineId,
						title: node.attrs?.title,
						childCount: node.content?.length || 0,
					});
					const sanitizedNode = sanitizeTimelineNode(node);
					console.log("[DEBUG] validateContent: 时间线节点处理完成", {
						timelineId: sanitizedNode.attrs?.timelineId,
						pointCount: sanitizedNode.content?.length || 0,
					});
					return sanitizedNode;
				}
				if (node.type === "travelPoint") {
					console.log("[DEBUG] validateContent: 处理点位节点", {
						pointId: node.attrs?.pointId,
						location: node.attrs?.location,
					});
					return sanitizePointNode(node);
				}
				console.log(
					"[DEBUG] validateContent: 保留其他类型节点:",
					node.type,
				);
				return node;
			});

		// 如果过滤后没有内容，添加一个默认段落
		if (safeContent.content.length === 0) {
			console.log(
				"[DEBUG] validateContent: 过滤后内容为空，添加默认段落",
			);
			safeContent.content.push({ type: "paragraph" });
		}

		console.log("[DEBUG] validateContent: 验证完成", {
			finalNodeCount: safeContent.content.length,
			finalNodeTypes: safeContent.content.map((node) => node.type),
		});

		return safeContent;
	};

	// 净化时间线节点
	const sanitizeTimelineNode = (node: JSONContent): JSONContent => {
		console.log("sanitizeTimelineNode - 处理前节点:", {
			type: node.type,
			attrs: node.attrs,
			hasContent: !!node.content,
			contentLength: node.content?.length || 0,
			contentTypes:
				node.content?.map((item) => item.type).join(", ") || "无",
		});

		// 确保timelineId唯一性
		const timelineId = node.attrs?.timelineId || `timeline_${Date.now()}`;

		// 使用默认结构保证节点完整性
		const sanitizedNode: JSONContent = {
			type: "travelTimeline",
			attrs: {
				timelineId,
				title: node.attrs?.title || "未命名时间线",
				timelineDate:
					node.attrs?.timelineDate || new Date().toISOString(),
			},
			// 过滤无效的子节点
			content: Array.isArray(node.content)
				? node.content
						.filter(
							(childNode) =>
								childNode &&
								typeof childNode === "object" &&
								childNode.type === "travelPoint",
						)
						.map((childNode) => {
							// 确保时间线内的点位节点有正确的结构
							if (childNode.type === "travelPoint") {
								return sanitizePointNode(childNode);
							}
							return childNode;
						})
				: [],
		};

		// 如果没有子节点，添加一个空的点位
		if (!sanitizedNode.content || sanitizedNode.content.length === 0) {
			console.log("时间线没有点位，添加默认点位");
			sanitizedNode.content = [
				{
					type: "travelPoint",
					attrs: {
						pointId: `point_${Date.now()}`,
						location: "默认地点",
						pointDate: new Date().toISOString(),
						images: [],
					},
					content: [
						{
							type: "paragraph",
							content: [],
						},
					],
				},
			];
		}

		console.log("sanitizeTimelineNode - 处理后节点:", {
			type: sanitizedNode.type,
			attrs: sanitizedNode.attrs,
			contentLength: sanitizedNode.content?.length || 0,
			contentTypes:
				sanitizedNode.content?.map((item) => item.type).join(", ") ||
				"无",
		});

		return sanitizedNode;
	};

	// 净化点位节点
	const sanitizePointNode = (node: JSONContent): JSONContent => {
		console.log("sanitizePointNode - 处理前节点:", {
			type: node.type,
			attrs: node.attrs,
			hasContent: !!node.content,
			contentLength: node.content?.length || 0,
		});

		// 确保pointId唯一性
		const pointId = node.attrs?.pointId || `point_${Date.now()}`;

		// 保证节点结构完整
		const sanitizedNode: JSONContent = {
			type: "travelPoint",
			attrs: {
				pointId,
				location: node.attrs?.location || "未命名地点",
				pointDate: node.attrs?.pointDate || new Date().toISOString(),
				images: Array.isArray(node.attrs?.images)
					? node.attrs?.images
					: [],
			},
			// 确保content中至少有一个段落
			content:
				Array.isArray(node.content) && node.content.length > 0
					? node.content
					: [{ type: "paragraph", content: [] }],
		};

		console.log("sanitizePointNode - 处理后节点:", {
			type: sanitizedNode.type,
			attrs: sanitizedNode.attrs,
			contentLength: sanitizedNode.content?.length || 0,
		});

		return sanitizedNode;
	};

	// 监听滚动事件
	useEffect(() => {
		if (!editorContentRef.current || !menuBarRef.current) return;

		// 记录菜单栏高度
		menuBarHeight.current = menuBarRef.current.offsetHeight;

		const handleScroll = () => {
			const scrollTop = editorContentRef.current?.scrollTop || 0;
			const shouldBeFixed = scrollTop > 5; // 添加一些阈值，避免轻微滚动时就触发

			if (isMenuBarFixed !== shouldBeFixed) {
				setIsMenuBarFixed(shouldBeFixed);
			}
		};

		const editorEl = editorContentRef.current;
		editorEl.addEventListener("scroll", handleScroll);

		return () => {
			editorEl.removeEventListener("scroll", handleScroll);
		};
	}, [isMenuBarFixed]);

	// 组件卸载时取消未执行的防抖函数
	useEffect(() => {
		return () => {
			debouncedContentChange.cancel();
		};
	}, [debouncedContentChange]);

	// 将结构化数据转换为富文本编辑器内容
	const convertDiaryDataToEditorContent = useCallback(
		(diaryData: any): JSONContent => {
			console.log("开始转换日记数据为编辑器内容:", diaryData);

			const editorContent: JSONContent = {
				type: "doc",
				content: [],
			};

			if (
				!diaryData?.diary?.timelines ||
				!Array.isArray(diaryData.diary.timelines)
			) {
				console.warn("日记数据格式不正确，返回空文档");
				return {
					type: "doc",
					content: [
						{
							type: "paragraph",
							content: [
								{ type: "text", text: "格式化后的内容为空" },
							],
						},
					],
				};
			}

			// 遍历时间线数据
			diaryData.diary.timelines.forEach(
				(timeline: any, timelineIndex: number) => {
					console.log(`处理时间线 #${timelineIndex}:`, timeline);

					// 确保日期格式正确
					let timelineDate: string;
					if (timeline.date) {
						// 如果是日期字符串（YYYY-MM-DD格式），转换为ISO格式
						if (
							typeof timeline.date === "string" &&
							timeline.date.match(/^\d{4}-\d{2}-\d{2}$/)
						) {
							timelineDate = new Date(
								`${timeline.date}T00:00:00.000Z`,
							).toISOString();
						} else {
							timelineDate = new Date(
								timeline.date,
							).toISOString();
						}
					} else {
						timelineDate = new Date().toISOString();
					}

					// 创建时间线节点
					const timelineNode: JSONContent = {
						type: "travelTimeline",
						attrs: {
							timelineId:
								timeline.id ||
								`timeline_${Date.now()}_${timelineIndex}`,
							title: timeline.title || "未命名时间线",
							timelineDate: timelineDate,
						},
						content: [],
					};

					// 添加时间线内的点位
					if (timeline.points && Array.isArray(timeline.points)) {
						timeline.points.forEach(
							(point: any, pointIndex: number) => {
								console.log(`处理点位 #${pointIndex}:`, point);

								// 确保点位日期格式正确
								let pointDate: string;
								if (point.date) {
									pointDate = new Date(
										point.date,
									).toISOString();
								} else {
									pointDate = timelineDate; // 使用时间线日期作为默认值
								}

								const pointNode: JSONContent = {
									type: "travelPoint",
									attrs: {
										pointId:
											point.id ||
											`point_${Date.now()}_${pointIndex}`,
										location:
											point.location || "未命名地点",
										pointDate: pointDate,
										images: Array.isArray(point.images)
											? point.images
											: [],
									},
									content: [
										{
											type: "paragraph",
											content: point.description
												? [
														{
															type: "text",
															text: point.description,
														},
													]
												: [],
										},
									],
								};

								timelineNode.content?.push(pointNode);
							},
						);
					}

					// 如果时间线没有点位，添加一个空的点位
					if (
						!timelineNode.content ||
						timelineNode.content.length === 0
					) {
						console.log(
							`时间线 #${timelineIndex} 没有点位，添加默认点位`,
						);
						timelineNode.content = [
							{
								type: "travelPoint",
								attrs: {
									pointId: `point_default_${Date.now()}_${timelineIndex}`,
									location: "默认地点",
									pointDate: timelineDate,
									images: [],
								},
								content: [{ type: "paragraph", content: [] }],
							},
						];
					}

					editorContent.content?.push(timelineNode);
					console.log(
						`时间线 #${timelineIndex} 已添加，包含 ${timelineNode.content?.length || 0} 个点位`,
					);
				},
			);

			// 如果没有时间线，添加一个默认段落
			if (!editorContent.content || editorContent.content.length === 0) {
				console.log("没有时间线数据，添加默认段落");
				editorContent.content = [
					{
						type: "paragraph",
						content: [
							{
								type: "text",
								text: "格式化完成，但没有识别到有效的旅行内容",
							},
						],
					},
				];
			}

			console.log("转换完成的编辑器内容:", editorContent);
			return editorContent;
		},
		[],
	);

	// 添加格式化富文本内容的功能
	const handleFormatContent = useCallback(async () => {
		if (!editor) {
			toast.error("编辑器尚未初始化");
			return;
		}

		const content = editor.getJSON();
		if (!content || !content.content || content.content.length === 0) {
			toast.error("富文本内容为空，无法格式化");
			return;
		}

		setIsFormatting(true);
		try {
			const response = await fetch("/api/diaries/format-richtext", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ content }),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "格式化请求失败");
			}

			const result = await response.json();

			if (result.success) {
				console.log("格式化结果:", result.data);

				// 将结构化数据转换为编辑器内容
				const newEditorContent = convertDiaryDataToEditorContent(
					result.data,
				);

				// 使用 setTimeout 确保在下一个事件循环中更新内容
				setTimeout(() => {
					try {
						// 设置新的编辑器内容
						editor.commands.setContent(newEditorContent);

						// 格式化成功提示
						toast.success(
							"富文本格式化成功！内容已更新为结构化格式",
						);

						console.log("编辑器内容已更新为结构化格式");
					} catch (err) {
						console.error("更新编辑器内容失败:", err);
						toast.error("更新编辑器内容失败");
					}
				}, 0);

				return result.data;
			}

			throw new Error(result.error || "格式化失败");
		} catch (error) {
			console.error("格式化富文本内容失败:", error);
			toast.error(
				`格式化失败: ${error instanceof Error ? error.message : String(error)}`,
			);
			return null;
		} finally {
			setIsFormatting(false);
		}
	}, [editor, convertDiaryDataToEditorContent]);

	// 创建增强的自定义操作数组
	const enhancedCustomActions = useCallback(() => {
		const baseActions = customActions || [];

		// 添加格式化按钮
		const formatAction: CustomAction = {
			icon: isFormatting ? (
				<div className="animate-spin">⚙️</div>
			) : (
				<span>🔄</span>
			),
			title: isFormatting
				? "正在AI分析并格式化..."
				: "AI智能格式化为旅行时间线",
			onClick: handleFormatContent,
			disabled: isFormatting || !editor || !isInitialized,
		};

		return [...baseActions, formatAction];
	}, [
		customActions,
		isFormatting,
		handleFormatContent,
		editor,
		isInitialized,
	]);

	if (isLoading) {
		return (
			<div
				className={`tiptap-editor-container border border-gray-300 rounded-md flex flex-col ${className} relative overflow-hidden`}
			>
				<div className="sticky top-0 z-20 bg-gray-50">
					<Skeleton className="h-10 w-full" />
				</div>
				<div className="flex-grow p-4">
					<Skeleton className="h-6 w-3/4 mb-4" />
					<Skeleton className="h-4 w-full mb-2" />
					<Skeleton className="h-4 w-full mb-2" />
					<Skeleton className="h-4 w-2/3 mb-6" />
					<Skeleton className="h-6 w-1/2 mb-4" />
					<Skeleton className="h-4 w-full mb-2" />
					<Skeleton className="h-4 w-5/6 mb-6" />
				</div>
			</div>
		);
	}

	if (!editor) {
		return null;
	}

	return (
		<div
			ref={containerRef}
			className={`tiptap-editor-container border border-gray-300 rounded-md flex flex-col ${className} relative overflow-hidden`}
		>
			{/* 键盘快捷键支持 */}
			<KeyboardShortcuts editor={editor} />

			{/* 菜单栏 */}
			<div
				ref={menuBarRef}
				className={`sticky top-0 z-20 transition-all duration-300 ${
					isMenuBarFixed
						? "bg-white/95 backdrop-blur-sm shadow-md"
						: "bg-gray-50"
				}`}
				style={{
					width: "100%",
				}}
			>
				<MenuBar
					editor={editor}
					customActions={enhancedCustomActions()}
				/>
			</div>

			{/* 编辑器内容区 */}
			<EditorContent
				editor={editor}
				className="overflow-y-auto flex-grow scrollbar-artistic"
				ref={editorContentRef}
			/>

			{/* 底部状态栏 */}
			<div className="border-t border-gray-200 bg-gray-50">
				<div className="flex items-center justify-between px-3 py-2 gap-6">
					{/* 左侧：自动保存状态指示器 */}
					<div className="flex-shrink-0">
						<AutoSaveIndicator
							isModified={isModified}
							isSaving={isSaving}
							lastSaved={lastSaved}
							onManualSave={onManualSave}
							className=""
						/>
					</div>

					{/* 中间：内容验证区域 - 紧密布局 */}
					{showValidator && (
						<div className="flex-1 flex items-center justify-center">
							<div className="flex items-center gap-3 bg-white rounded-lg px-3 py-1.5 border border-gray-200 shadow-sm">
								<ContentValidator
									editor={editor}
									className="compact-mode"
								/>
							</div>
						</div>
					)}

					{/* 右侧：快捷键提示 */}
					<div className="flex-shrink-0 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-md border">
						<kbd className="text-xs font-mono bg-white px-1 py-0.5 rounded border shadow-sm">
							F1
						</kbd>
						<span className="ml-1">快捷键</span>
					</div>
				</div>
			</div>
		</div>
	);
};
