import type { CountryData, GeocodeFeature, TravelPoint } from "../types";
import { extractCountryInfo } from "../utils/dataUtils";
import type { IDataStorage } from "./interfaces";
import { LocalStorageProvider } from "./providers/LocalStorageProvider";
import type { DataOperationResult, TravelFootprintData } from "./types";
import { DEFAULT_TRAVEL_DATA, STORAGE_KEYS } from "./types";

/**
 * 旅行足迹数据管理器
 * 提供统一的数据操作接口，封装存储细节
 */
export class TravelFootprintDataManager {
	private storage: IDataStorage;
	private data: TravelFootprintData | null = null;
	private autoSaveTimer: NodeJS.Timeout | null = null;

	constructor(storage?: IDataStorage) {
		this.storage =
			storage ||
			new LocalStorageProvider({
				keyPrefix: "travel-footprint:",
				errorStrategy: "log",
			});
	}

	/**
	 * 初始化数据管理器
	 */
	async initialize(): Promise<DataOperationResult<TravelFootprintData>> {
		try {
			// 尝试加载现有数据
			const savedData = await this.storage.get<TravelFootprintData>(
				STORAGE_KEYS.MAIN_DATA,
			);

			if (savedData) {
				// 检查是否需要迁移数据
				this.data = await this.migrateDataIfNeeded(savedData);
			} else {
				// 检查是否有旧版本数据需要迁移
				this.data = await this.migrateLegacyData();
			}

			// 如果没有任何数据，使用默认数据
			if (!this.data) {
				this.data = { ...DEFAULT_TRAVEL_DATA };
			}

			// 启动自动保存
			this.startAutoSave();

			return {
				success: true,
				data: this.data,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : String(error),
				timestamp: new Date().toISOString(),
			};
		}
	}

	/**
	 * 获取完整数据
	 */
	getData(): TravelFootprintData | null {
		return this.data;
	}

	/**
	 * 获取旅行数据
	 */
	getTravelData(): { points: TravelPoint[]; countries: CountryData[] } {
		return this.data?.travel || { points: [], countries: [] };
	}

	/**
	 * 获取地图配置
	 */
	getMapConfig() {
		return this.data?.mapConfig || DEFAULT_TRAVEL_DATA.mapConfig;
	}

	/**
	 * 获取样式配置
	 */
	getStyleConfig() {
		return this.data?.styleConfig || DEFAULT_TRAVEL_DATA.styleConfig;
	}

	/**
	 * 获取用户偏好
	 */
	getPreferences() {
		return this.data?.preferences || DEFAULT_TRAVEL_DATA.preferences;
	}

	/**
	 * 获取卡片配置
	 */
	getCardConfig() {
		return this.data?.cardConfig || DEFAULT_TRAVEL_DATA.cardConfig;
	}

	/**
	 * 添加旅行点
	 */
	async addTravelPoint(
		feature: GeocodeFeature,
	): Promise<DataOperationResult<TravelPoint>> {
		try {
			if (!this.data) {
				throw new Error("数据管理器未初始化");
			}

			const { country, countryCode } = extractCountryInfo(feature);
			const city = feature.place_name.split(",")[0];

			const newPoint: TravelPoint = {
				id: `${Date.now()}-${Math.random()}`,
				name: feature.place_name,
				coordinates: feature.center,
				country,
				city,
				timestamp: Date.now(),
			};

			// 更新旅行点
			this.data.travel.points.push(newPoint);

			// 更新国家统计
			const existingCountry = this.data.travel.countries.find(
				(c) => c.name === country,
			);

			if (existingCountry) {
				existingCountry.visitCount += 1;
				existingCountry.totalPoints += 1;
				existingCountry.lastVisit = new Date().toISOString();
				// 添加城市到访问列表（如果不存在）
				if (city && !existingCountry.visitedCities.includes(city)) {
					existingCountry.visitedCities.push(city);
				}
			} else if (country) {
				const currentDate = new Date().toISOString();
				this.data.travel.countries.push({
					name: country,
					code: countryCode,
					visitCount: 1,
					visitedCities: city ? [city] : [],
					totalPoints: 1,
					firstVisit: currentDate,
					lastVisit: currentDate,
				});
			}

			// 更新时间戳
			this.data.lastUpdated = new Date().toISOString();

			// 保存数据
			await this.saveData();

			return {
				success: true,
				data: newPoint,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : String(error),
				timestamp: new Date().toISOString(),
			};
		}
	}

	/**
	 * 删除旅行点
	 */
	async removeTravelPoint(
		pointId: string,
	): Promise<DataOperationResult<void>> {
		try {
			if (!this.data) {
				throw new Error("数据管理器未初始化");
			}

			const pointIndex = this.data.travel.points.findIndex(
				(p) => p.id === pointId,
			);

			if (pointIndex === -1) {
				throw new Error("未找到指定的旅行点");
			}

			const pointToRemove = this.data.travel.points[pointIndex];

			// 删除旅行点
			this.data.travel.points.splice(pointIndex, 1);

			// 更新国家统计
			const countryIndex = this.data.travel.countries.findIndex(
				(c) => c.name === pointToRemove.country,
			);

			if (countryIndex !== -1) {
				this.data.travel.countries[countryIndex].visitCount -= 1;
				this.data.travel.countries[countryIndex].totalPoints -= 1;

				// 如果访问次数为0或更少，则删除该国家
				if (this.data.travel.countries[countryIndex].visitCount <= 0) {
					this.data.travel.countries.splice(countryIndex, 1);
				}
			}

			// 更新时间戳
			this.data.lastUpdated = new Date().toISOString();

			// 保存数据
			await this.saveData();

			return {
				success: true,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : String(error),
				timestamp: new Date().toISOString(),
			};
		}
	}

	/**
	 * 更新地图配置
	 */
	async updateMapConfig(
		config: Partial<TravelFootprintData["mapConfig"]>,
	): Promise<DataOperationResult<void>> {
		try {
			if (!this.data) {
				throw new Error("数据管理器未初始化");
			}

			this.data.mapConfig = { ...this.data.mapConfig, ...config };
			this.data.lastUpdated = new Date().toISOString();

			await this.saveData();

			return {
				success: true,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : String(error),
				timestamp: new Date().toISOString(),
			};
		}
	}

	/**
	 * 更新样式配置
	 */
	async updateStyleConfig(
		config: Partial<TravelFootprintData["styleConfig"]>,
	): Promise<DataOperationResult<void>> {
		try {
			if (!this.data) {
				throw new Error("数据管理器未初始化");
			}

			this.data.styleConfig = { ...this.data.styleConfig, ...config };
			this.data.lastUpdated = new Date().toISOString();

			await this.saveData();

			return {
				success: true,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : String(error),
				timestamp: new Date().toISOString(),
			};
		}
	}

	/**
	 * 更新卡片配置
	 */
	async updateCardConfig(
		config: Partial<TravelFootprintData["cardConfig"]>,
	): Promise<DataOperationResult<void>> {
		try {
			if (!this.data) {
				throw new Error("数据管理器未初始化");
			}

			this.data.cardConfig = { ...this.data.cardConfig, ...config };
			this.data.lastUpdated = new Date().toISOString();

			await this.saveData();

			return {
				success: true,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : String(error),
				timestamp: new Date().toISOString(),
			};
		}
	}

	/**
	 * 更新卡片自定义配置
	 */
	async updateCardCustomization(
		customization: Partial<
			TravelFootprintData["cardConfig"]["customization"]
		>,
	): Promise<DataOperationResult<void>> {
		try {
			if (!this.data) {
				throw new Error("数据管理器未初始化");
			}

			this.data.cardConfig.customization = {
				...this.data.cardConfig.customization,
				...customization,
			};
			this.data.lastUpdated = new Date().toISOString();

			await this.saveData();

			return {
				success: true,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : String(error),
				timestamp: new Date().toISOString(),
			};
		}
	}

	/**
	 * 更新用户资料
	 */
	async updateUserProfile(
		userProfile: Partial<TravelFootprintData["cardConfig"]["userProfile"]>,
	): Promise<DataOperationResult<void>> {
		try {
			if (!this.data) {
				throw new Error("数据管理器未初始化");
			}

			this.data.cardConfig.userProfile = {
				...this.data.cardConfig.userProfile,
				...userProfile,
			};
			this.data.lastUpdated = new Date().toISOString();

			await this.saveData();

			return {
				success: true,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : String(error),
				timestamp: new Date().toISOString(),
			};
		}
	}

	/**
	 * 更新导出设置
	 */
	async updateExportSettings(
		exportSettings: Partial<
			TravelFootprintData["cardConfig"]["exportSettings"]
		>,
	): Promise<DataOperationResult<void>> {
		try {
			if (!this.data) {
				throw new Error("数据管理器未初始化");
			}

			this.data.cardConfig.exportSettings = {
				...this.data.cardConfig.exportSettings,
				...exportSettings,
			};
			this.data.lastUpdated = new Date().toISOString();

			await this.saveData();

			return {
				success: true,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : String(error),
				timestamp: new Date().toISOString(),
			};
		}
	}

	/**
	 * 更新用户偏好设置
	 */
	async updatePreferences(
		preferences: Partial<TravelFootprintData["preferences"]>,
	): Promise<DataOperationResult<void>> {
		try {
			if (!this.data) {
				throw new Error("数据管理器未初始化");
			}

			this.data.preferences = {
				...this.data.preferences,
				...preferences,
			};
			this.data.lastUpdated = new Date().toISOString();

			await this.saveData();

			return {
				success: true,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : String(error),
				timestamp: new Date().toISOString(),
			};
		}
	}

	/**
	 * 清空所有数据
	 */
	async clearAllData(): Promise<DataOperationResult<void>> {
		try {
			this.data = { ...DEFAULT_TRAVEL_DATA };
			await this.storage.clear();

			return {
				success: true,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : String(error),
				timestamp: new Date().toISOString(),
			};
		}
	}

	/**
	 * 导入数据
	 */
	async importData(
		data: TravelFootprintData,
	): Promise<DataOperationResult<void>> {
		try {
			// 验证数据格式
			if (!this.validateData(data)) {
				throw new Error("导入的数据格式无效");
			}

			// 创建备份
			if (this.data) {
				await this.storage.set(STORAGE_KEYS.BACKUP_DATA, this.data);
			}

			// 应用导入的数据
			this.data = { ...data };
			this.data.lastUpdated = new Date().toISOString();

			await this.saveData();

			return {
				success: true,
				timestamp: new Date().toISOString(),
			};
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : String(error),
				timestamp: new Date().toISOString(),
			};
		}
	}

	/**
	 * 导出数据
	 */
	exportData(): TravelFootprintData | null {
		return this.data ? { ...this.data } : null;
	}

	/**
	 * 保存数据到存储
	 */
	private async saveData(): Promise<void> {
		if (this.data) {
			await this.storage.set(STORAGE_KEYS.MAIN_DATA, this.data);
		}
	}

	/**
	 * 启动自动保存
	 */
	private startAutoSave(): void {
		if (this.autoSaveTimer) {
			clearInterval(this.autoSaveTimer);
		}

		const interval = this.data?.preferences.autoSaveInterval || 5000;
		this.autoSaveTimer = setInterval(() => {
			if (this.data) {
				this.saveData().catch(console.error);
			}
		}, interval);
	}

	/**
	 * 停止自动保存
	 */
	stopAutoSave(): void {
		if (this.autoSaveTimer) {
			clearInterval(this.autoSaveTimer);
			this.autoSaveTimer = null;
		}
	}

	/**
	 * 验证数据格式
	 */
	private validateData(data: any): data is TravelFootprintData {
		return (
			data &&
			typeof data === "object" &&
			typeof data.version === "string" &&
			typeof data.lastUpdated === "string" &&
			data.travel &&
			Array.isArray(data.travel.points) &&
			Array.isArray(data.travel.countries) &&
			data.mapConfig &&
			data.styleConfig &&
			data.preferences
		);
	}

	/**
	 * 迁移旧版本数据
	 */
	private async migrateLegacyData(): Promise<TravelFootprintData | null> {
		try {
			// 尝试读取旧版本的 localStorage 数据
			const oldPoints =
				await this.storage.get<TravelPoint[]>("travelFootprints");
			const oldCountries =
				await this.storage.get<CountryData[]>("visitedCountries");
			const oldAtmosphere =
				await this.storage.get<string>("atmosphereTheme");
			const oldMapStyle = await this.storage.get<string>("mapStyle");
			const oldColorTheme = await this.storage.get<string>(
				"travel-footprint-color-theme",
			);

			if (oldPoints || oldCountries) {
				const migratedData: TravelFootprintData = {
					...DEFAULT_TRAVEL_DATA,
					travel: {
						points: oldPoints || [],
						countries: oldCountries || [],
					},
				};

				// 迁移配置
				if (oldAtmosphere) {
					migratedData.mapConfig.atmosphereTheme =
						oldAtmosphere as any;
				}
				if (oldMapStyle) {
					migratedData.mapConfig.mapStyle = oldMapStyle as any;
				}
				if (oldColorTheme) {
					migratedData.styleConfig.colorTheme = oldColorTheme as any;
				}

				// 清理旧数据
				await this.storage.remove("travelFootprints");
				await this.storage.remove("visitedCountries");
				await this.storage.remove("atmosphereTheme");
				await this.storage.remove("mapStyle");
				await this.storage.remove("travel-footprint-color-theme");

				return migratedData;
			}

			return null;
		} catch (error) {
			console.error("迁移旧版本数据失败:", error);
			return null;
		}
	}

	/**
	 * 检查并迁移数据版本
	 */
	private async migrateDataIfNeeded(
		data: TravelFootprintData,
	): Promise<TravelFootprintData> {
		// 如果版本相同，直接返回
		if (data.version === DEFAULT_TRAVEL_DATA.version) {
			return data;
		}

		// TODO: 实现数据版本迁移逻辑
		console.log(
			`数据版本从 ${data.version} 迁移到 ${DEFAULT_TRAVEL_DATA.version}`,
		);

		return {
			...data,
			version: DEFAULT_TRAVEL_DATA.version,
			lastUpdated: new Date().toISOString(),
		};
	}

	/**
	 * 销毁数据管理器
	 */
	destroy(): void {
		this.stopAutoSave();
		this.data = null;
	}
}
