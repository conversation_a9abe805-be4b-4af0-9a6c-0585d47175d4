import {
	Cloud,
	Globe,
	Layers,
	MapIcon,
	Minimize,
	Moon,
	Mountain,
	Navigation,
	Satellite,
	Sparkles,
	Star,
	Stars,
	Sun,
	Sunset,
	Waves,
	X,
	Zap,
} from "lucide-react";
import type {
	AnimationTheme,
	AtmosphereTheme,
	MapStyleType,
} from "../../types";

// 大气层主题图标映射
export const ATMOSPHERE_THEME_ICONS = {
	day: Sun,
	night: Moon,
	sunset: Sunset,
	dawn: Cloud,
	aurora: Zap,
	"deep-space": Globe,
	ocean: Waves,
	minimal: Mountain,
} as const satisfies Record<AtmosphereTheme, any>;

// 动画主题图标映射
export const ANIMATION_THEME_ICONS = {
	"shooting-stars": Star,
	"floating-particles": Sparkles,
	aurora: Zap,
	minimal: Minimize,
	galaxy: Stars,
	none: X,
} as const satisfies Record<AnimationTheme, any>;

// 地图样式图标映射
export const STYLE_ICONS = {
	streets: MapIcon,
	outdoors: Mountain,
	light: Globe,
	dark: Layers,
	satellite: Satellite,
	"satellite-streets": Satellite,
	"navigation-day": Navigation,
	"navigation-night": Navigation,
} as const satisfies Record<MapStyleType, any>;
