import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { Check, Plus, X } from "lucide-react";
import { useState } from "react";
import type { TravelPoint } from "./SortablePointItem";
import { EditableDate } from "./inline-editing/EditableDate";
import { EditableDescription } from "./inline-editing/EditableDescription";
import { EditableImages } from "./inline-editing/EditableImages";
import { EditableLocation } from "./inline-editing/EditableLocation";

interface AddNewPointItemProps {
	onAddPoint: (point: TravelPoint) => void;
	getIconBackgroundColor: (iconType: string) => string;
	renderPointIcon: (iconType: string) => React.ReactNode;
	onImagePreview: (point: TravelPoint, index: number) => void;
	className?: string;
}

export function AddNewPointItem({
	onAddPoint,
	getIconBackgroundColor,
	renderPointIcon,
	onImagePreview,
	className,
}: AddNewPointItemProps) {
	const [isAdding, setIsAdding] = useState(false);
	const [newPoint, setNewPoint] = useState<Partial<TravelPoint>>({
		location: "",
		description: "",
		date: new Date(),
		images: [],
		iconType: "PIN",
		coordinates: { lat: 0, lng: 0 },
	});

	const handleStartAdding = () => {
		setIsAdding(true);
		// 重置模板
		setNewPoint({
			location: "",
			description: "",
			date: new Date(),
			images: [],
			iconType: "PIN",
			coordinates: { lat: 0, lng: 0 },
		});
	};

	const handleSave = () => {
		// 验证必填字段
		if (!newPoint.location?.trim()) {
			return; // 位置为必填
		}

		// 生成新的点位对象
		const finalPoint: TravelPoint = {
			id: `temp-${Date.now()}`, // 临时ID，后端会生成真实ID
			location: newPoint.location.trim(),
			description: newPoint.description || "",
			date: newPoint.date || new Date(),
			images: newPoint.images || [],
			iconType: newPoint.iconType || "PIN",
			coordinates: newPoint.coordinates || { lat: 0, lng: 0 },
		};

		onAddPoint(finalPoint);
		setIsAdding(false);
		setNewPoint({
			location: "",
			description: "",
			date: new Date(),
			images: [],
			iconType: "PIN",
			coordinates: { lat: 0, lng: 0 },
		});
	};

	const handleCancel = () => {
		setIsAdding(false);
		setNewPoint({
			location: "",
			description: "",
			date: new Date(),
			images: [],
			iconType: "PIN",
			coordinates: { lat: 0, lng: 0 },
		});
	};

	const updateField = (fieldName: string, value: any) => {
		setNewPoint((prev) => ({
			...prev,
			[fieldName]: value,
		}));
	};

	const updateLocationField = (
		location: string,
		coordinates?: { lat: number; lng: number },
	) => {
		setNewPoint((prev) => ({
			...prev,
			location,
			coordinates: coordinates || prev.coordinates,
		}));
	};

	const updateImagesField = (images: string[]) => {
		const imageObjects = images.map((url, index) => ({
			url,
			alt: undefined,
			caption: undefined,
			description: undefined,
			uploadedAt: new Date().toISOString(),
		}));
		setNewPoint((prev) => ({
			...prev,
			images: imageObjects,
		}));
	};

	if (isAdding) {
		return (
			<div
				className={cn(
					"relative flex items-start bg-blue-50/50 border-2 border-dashed border-blue-300 p-4 rounded-lg transition-all duration-200",
					className,
				)}
			>
				{/* 图标 */}
				<div
					className={cn(
						"mr-4 w-10 h-10 rounded-full flex items-center justify-center text-white flex-shrink-0 mt-1 z-10 relative",
						getIconBackgroundColor(newPoint.iconType || "PIN"),
					)}
				>
					{renderPointIcon(newPoint.iconType || "PIN")}
				</div>

				{/* 内容 */}
				<div className="flex-1 min-w-0">
					<div className="flex items-start justify-between flex-wrap sm:flex-nowrap">
						<div className="pr-2 mb-1 sm:mb-0 flex-1">
							{/* 可编辑的位置 */}
							<EditableLocation
								value={newPoint.location || ""}
								isEditing={true}
								onEnterEdit={() => {}}
								onExitEdit={() => {}}
								onValueChange={updateLocationField}
								onIconTypeChange={(iconType) => {
									// 当地点类型自动识别出图标类型时，同步更新新点位的图标类型
									updateField("iconType", iconType);
								}}
							/>

							{/* 可编辑的日期 */}
							<EditableDate
								value={newPoint.date || new Date()}
								isEditing={true}
								onEnterEdit={() => {}}
								onExitEdit={() => {}}
								onValueChange={(value) =>
									updateField("date", value)
								}
							/>
						</div>

						{/* 操作按钮 */}
						<div className="flex items-center gap-1 ml-auto sm:ml-0">
							<Button
								variant="primary"
								size="sm"
								onClick={handleSave}
								disabled={!newPoint.location?.trim()}
								className="h-8 px-3 text-xs"
							>
								<Check className="mr-1 h-3 w-3" />
								保存
							</Button>
							<Button
								variant="outline"
								size="sm"
								onClick={handleCancel}
								className="h-8 px-3 text-xs"
							>
								<X className="mr-1 h-3 w-3" />
								取消
							</Button>
						</div>
					</div>

					{/* 可编辑的描述 */}
					<EditableDescription
						value={newPoint.description || ""}
						isEditing={true}
						onEnterEdit={() => {}}
						onExitEdit={() => {}}
						onValueChange={(value) =>
							updateField("description", value)
						}
					/>

					{/* 可编辑的图片 */}
					<EditableImages
						value={newPoint.images?.map((img) => img.url) || []}
						isEditing={true}
						onEnterEdit={() => {}}
						onExitEdit={() => {}}
						onValueChange={updateImagesField}
						onImagePreview={(images, index) => {
							// 创建临时的 TravelPoint 对象用于图片预览
							const tempPoint = {
								...newPoint,
								id: "temp-preview",
								location: newPoint.location || "新点位",
								description: newPoint.description || "",
								date: newPoint.date || new Date(),
								images: images.map((url, idx) => ({
									url,
									id: `temp-${idx}`,
								})),
								iconType: newPoint.iconType || "PIN",
								coordinates: newPoint.coordinates || {
									lat: 0,
									lng: 0,
								},
							} as TravelPoint;
							onImagePreview(tempPoint, index);
						}}
					/>

					{/* 提示文本 */}
					<div className="mt-3 text-xs text-blue-600 bg-blue-50 border border-blue-200 rounded p-2">
						💡 提示：请至少填写位置信息，其他字段可选填
					</div>
				</div>
			</div>
		);
	}

	// 非编辑模式 - 显示添加按钮
	return (
		<button
			type="button"
			className={cn(
				"w-full flex items-center justify-center gap-3 p-6 border-2 border-dashed border-muted-foreground/30 rounded-lg hover:border-blue-400 hover:bg-blue-50/30 transition-all duration-200 group",
				className,
			)}
			onClick={handleStartAdding}
		>
			<div className="w-10 h-10 rounded-full bg-blue-500/10 group-hover:bg-blue-500/20 flex items-center justify-center transition-colors">
				<Plus className="h-5 w-5 text-blue-500" />
			</div>
			<div className="text-left">
				<p className="font-medium text-blue-600 group-hover:text-blue-700">
					添加新点位
				</p>
				<p className="text-sm text-muted-foreground">
					记录您的旅行足迹
				</p>
			</div>
		</button>
	);
}
