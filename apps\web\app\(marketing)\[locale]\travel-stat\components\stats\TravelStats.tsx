"use client";

import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Building2, Flag, MapPin } from "lucide-react";

import { useTravelStatTranslations } from "../../hooks/useTravelStatTranslations";

interface TravelStatsProps {
	totalPoints: number;
	citiesCount: number;
	countriesCount: number;
}

export function TravelStats({
	totalPoints,
	citiesCount,
	countriesCount,
}: TravelStatsProps) {
	const travelStatT = useTravelStatTranslations();

	const stats = [
		{
			title: travelStatT.stats.points.title(),
			value: totalPoints,
			icon: <MapPin className="w-4 h-4" />,
			color: "text-sky-600",
			bgColor: "bg-sky-50",
		},
		{
			title: travelStatT.stats.cities.title(),
			value: citiesCount,
			icon: <Building2 className="w-4 h-4" />,
			color: "text-blue-600",
			bgColor: "bg-blue-50",
		},
		{
			title: travelStatT.stats.countries.title(),
			value: countriesCount,
			icon: <Flag className="w-4 h-4" />,
			color: "text-indigo-600",
			bgColor: "bg-indigo-50",
		},
	];

	return (
		<Card className="bg-white/90 backdrop-blur-sm border-sky-200">
			<CardHeader className="px-3 py-2 pb-1">
				<CardTitle className="text-sm font-medium flex items-center gap-2">
					<MapPin className="w-4 h-4 text-sky-500" />
					{travelStatT.stats.title()}
				</CardTitle>
			</CardHeader>
			<CardContent className="px-3 pb-3 pt-3">
				<div className="grid grid-cols-3 gap-3">
					{stats.map((stat) => (
						<div key={stat.title} className="text-center">
							<div
								className={`w-10 h-10 rounded-full ${stat.bgColor} flex items-center justify-center mx-auto mb-2`}
							>
								<div className={stat.color}>{stat.icon}</div>
							</div>
							<div className="text-lg font-bold text-gray-900">
								{stat.value}
							</div>
							<div className="text-xs text-gray-600">
								{stat.title}
							</div>
						</div>
					))}
				</div>
			</CardContent>
		</Card>
	);
}
