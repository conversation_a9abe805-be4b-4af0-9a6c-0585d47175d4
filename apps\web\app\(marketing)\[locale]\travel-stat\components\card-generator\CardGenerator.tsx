"use client";

import { Badge } from "@ui/components/badge";
import { But<PERSON> } from "@ui/components/button";
import { Card } from "@ui/components/card";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import {} from "@ui/components/tabs";
import {
	AlertCircle,
	Camera,
	CheckCircle,
	Download,
	Eye,
	Loader2,
	Palette,
	Share2,
} from "lucide-react";
import React, { useState, useCallback, useMemo } from "react";

import { TemplateSelector } from "./components/TemplateSelector";
import { cardTemplates } from "./templates";
import type {
	CardCustomization,
	CardExportOptions,
	CardTemplate,
	CountryData,
	ExportQuality,
	SocialPlatform,
	TravelPoint,
} from "./types/cardTypes";
import {
	copyToClipboard,
	exportCard,
	shareToSocialMedia,
} from "./utils/cardExportUtils";

interface CardGeneratorProps {
	travelPoints: TravelPoint[];
	visitedCountries: CountryData[];
	mapImageData?: {
		dataURL: string;
		dimensions: { width: number; height: number };
	};
	className?: string;
}

export function CardGenerator({
	travelPoints = [],
	visitedCountries = [],
	mapImageData,
	className = "",
}: CardGeneratorProps) {
	// 状态管理
	const [selectedPlatform, setSelectedPlatform] =
		useState<SocialPlatform>("instagram");
	const [selectedTemplate, setSelectedTemplate] = useState<CardTemplate>(
		cardTemplates[0],
	);
	const [customization, setCustomization] = useState<CardCustomization>(
		() => {
			const template = cardTemplates[0];
			return {
				colors: template.colors,
				typography: template.typography,
				layout: template.layout,
				content: {
					showUserInfo: true,
					showDetailedStats: true,
					customTitle: "✈️ 我的旅行足迹",
					customFooter: "Map Moment · 记录每一次美好旅程",
				},
			};
		},
	);
	const [isExporting, setIsExporting] = useState(false);
	const [exportStatus, setExportStatus] = useState<
		"idle" | "success" | "error"
	>("idle");
	const [exportMessage, setExportMessage] = useState("");

	// 默认地图图片数据
	const defaultMapImageData = useMemo(
		() => ({
			dataURL:
				"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjYwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iYmciIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojNjc4OGVhO3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiM3NjRiYTI7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2JnKSIvPgogIDx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iMC4zZW0iPuWcsOWbvuWKoOi9veS4rS4uLjwvdGV4dD4KPC9zdmc+",
			dimensions: { width: 800, height: 600 },
		}),
		[],
	);

	// 获取实际的地图数据
	const actualMapImageData = mapImageData || defaultMapImageData;

	// 平台配置
	const platforms: {
		value: SocialPlatform;
		label: string;
		icon: string;
		dimensions: { width: number; height: number };
		aspectRatio: string;
		description: string;
		features: string[];
	}[] = [
		{
			value: "instagram",
			label: "Instagram",
			icon: "📷",
			dimensions: { width: 1080, height: 1080 },
			aspectRatio: "1:1",
			description: "Instagram 动态和快拍",
			features: ["正方形格式", "高对比度", "简洁设计"],
		},
		{
			value: "wechat",
			label: "微信朋友圈",
			icon: "💬",
			dimensions: { width: 1200, height: 900 },
			aspectRatio: "4:3",
			description: "微信朋友圈分享",
			features: ["横向布局", "适合文字阅读", "中国用户友好"],
		},
		{
			value: "weibo",
			label: "新浪微博",
			icon: "🔥",
			dimensions: { width: 1080, height: 1350 },
			aspectRatio: "4:5",
			description: "新浪微博动态",
			features: ["竖向布局", "信息密度高", "话题传播"],
		},
		{
			value: "twitter",
			label: "Twitter",
			icon: "🐦",
			dimensions: { width: 1200, height: 675 },
			aspectRatio: "16:9",
			description: "Twitter 推文图片",
			features: ["宽屏格式", "全球传播", "简洁明了"],
		},
		{
			value: "facebook",
			label: "Facebook",
			icon: "👥",
			dimensions: { width: 1200, height: 630 },
			aspectRatio: "1.91:1",
			description: "Facebook 动态分享",
			features: ["链接预览", "社交互动", "多样化内容"],
		},
	];

	// 导出质量选项
	const qualityOptions: {
		value: ExportQuality;
		label: string;
		description: string;
	}[] = [
		{ value: "low", label: "标准", description: "快速导出，适合预览" },
		{ value: "medium", label: "高清", description: "平衡质量与文件大小" },
		{ value: "high", label: "超清", description: "最佳质量，文件较大" },
	];

	// 处理平台选择
	const handlePlatformChange = useCallback((platform: string) => {
		setSelectedPlatform(platform as SocialPlatform);
		// 重置模板选择，确保兼容性
		const availableTemplates = cardTemplates;
		if (availableTemplates.length > 0) {
			const newTemplate = availableTemplates[0];
			setSelectedTemplate(newTemplate);
			setCustomization({
				colors: newTemplate.colors,
				typography: newTemplate.typography,
				layout: newTemplate.layout,
				content: {
					showUserInfo: true,
					showDetailedStats: true,
					customTitle: "✈️ 我的旅行足迹",
					customFooter: "Map Moment · 记录每一次美好旅程",
				},
			});
		}
	}, []);

	// 处理模板选择
	const handleTemplateSelect = useCallback((template: CardTemplate) => {
		setSelectedTemplate(template);
		setCustomization({
			colors: template.colors,
			typography: template.typography,
			layout: template.layout,
			content: {
				showUserInfo: true,
				showDetailedStats: true,
				customTitle: "✈️ 我的旅行足迹",
				customFooter: "Map Moment · 记录每一次美好旅程",
			},
		});
	}, []);

	// 导出卡片
	const handleExport = useCallback(
		async (quality: ExportQuality = "medium") => {
			console.log("🚀 开始导出卡片", {
				quality,
				selectedTemplate: selectedTemplate?.name,
			});

			if (!selectedTemplate) {
				console.error("❌ 未选择模板");
				return;
			}

			console.log("📊 导出参数:", {
				platform: selectedPlatform,
				quality,
				filename: `travel-card-${selectedTemplate.id}-${Date.now()}`,
				travelPointsCount: travelPoints.length,
				countriesCount: visitedCountries.length,
				mapImageData: !!actualMapImageData,
				customization,
			});

			setIsExporting(true);
			setExportStatus("idle");

			try {
				const options: CardExportOptions = {
					platform: selectedPlatform,
					quality,
					filename: `travel-card-${selectedTemplate.id}-${Date.now()}`,
				};

				console.log("🔄 调用 exportCard 函数...");
				const result = await exportCard(
					selectedTemplate,
					actualMapImageData,
					travelPoints,
					visitedCountries,
					customization,
					options,
				);

				console.log("📤 导出结果:", result);

				if (result.success) {
					setExportStatus("success");
					setExportMessage("卡片导出成功！");
					console.log("✅ 导出成功");
				} else {
					setExportStatus("error");
					setExportMessage(result.error || "导出失败");
					console.error("❌ 导出失败:", result.error);
				}
			} catch (error) {
				console.error("💥 导出过程中发生错误:", error);
				setExportStatus("error");
				setExportMessage(
					error instanceof Error ? error.message : "导出时发生错误",
				);
			} finally {
				console.log("🏁 导出过程结束");
				setIsExporting(false);
				// 3秒后清除状态
				setTimeout(() => {
					setExportStatus("idle");
					setExportMessage("");
				}, 3000);
			}
		},
		[
			selectedTemplate,
			selectedPlatform,
			actualMapImageData,
			travelPoints,
			visitedCountries,
			customization,
		],
	);

	// 复制到剪贴板
	const handleCopy = useCallback(async () => {
		if (!selectedTemplate) return;

		try {
			await copyToClipboard(
				selectedTemplate,
				actualMapImageData,
				travelPoints,
				visitedCountries,
				customization,
				{ platform: selectedPlatform, quality: "medium" },
			);
			setExportStatus("success");
			setExportMessage("已复制到剪贴板");
		} catch (error) {
			setExportStatus("error");
			setExportMessage("复制失败");
		}
	}, [
		selectedTemplate,
		selectedPlatform,
		actualMapImageData,
		travelPoints,
		visitedCountries,
		customization,
	]);

	// 分享到社交媒体
	const handleShare = useCallback(async () => {
		if (!selectedTemplate) return;

		try {
			await shareToSocialMedia(
				selectedTemplate,
				actualMapImageData,
				travelPoints,
				visitedCountries,
				customization,
				{ platform: selectedPlatform, quality: "medium" },
			);
			setExportStatus("success");
			setExportMessage("已打开分享");
		} catch (error) {
			setExportStatus("error");
			setExportMessage("分享失败");
		}
	}, [
		selectedTemplate,
		selectedPlatform,
		actualMapImageData,
		travelPoints,
		visitedCountries,
		customization,
	]);

	// 渲染卡片预览
	const renderCardPreview = () => {
		if (!selectedTemplate) return null;

		const TemplateComponent = selectedTemplate.component;

		return (
			<div className="flex justify-center">
				<TemplateComponent
					mapImageData={actualMapImageData}
					travelPoints={travelPoints}
					visitedCountries={visitedCountries}
					customization={customization}
					platform={selectedPlatform}
				/>
			</div>
		);
	};

	return (
		<div className={`space-y-6 ${className}`}>
			{/* 标题区域 */}
			<div className="text-center">
				<h2 className="text-3xl font-bold text-gray-900 mb-3">
					✨ 卡片生成器
				</h2>
				<p className="text-lg text-gray-600">
					生成精美的旅行足迹卡片，分享你的美好旅程
				</p>
			</div>

			{/* 状态消息 */}
			{exportStatus !== "idle" && (
				<Card className="p-4 max-w-md mx-auto">
					<div
						className={`flex items-center gap-2 justify-center ${
							exportStatus === "success"
								? "text-green-600"
								: "text-red-600"
						}`}
					>
						{exportStatus === "success" ? (
							<CheckCircle className="w-5 h-5" />
						) : (
							<AlertCircle className="w-5 h-5" />
						)}
						<span className="font-medium">{exportMessage}</span>
					</div>
				</Card>
			)}

			{/* 平台格式详情 */}
			<Card className="p-4 bg-blue-50 border-blue-200">
				<h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
					<Camera className="w-5 h-5 text-blue-600" />
					平台格式详情
				</h3>

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
					{platforms.map((platform) => {
						const isSelected = platform.value === selectedPlatform;
						return (
							<button
								key={platform.value}
								type="button"
								className={`p-3 rounded-lg border-2 transition-all cursor-pointer w-full ${
									isSelected
										? "border-blue-500 bg-blue-100 shadow-md"
										: "border-gray-200 bg-white hover:border-gray-300"
								}`}
								onClick={() =>
									handlePlatformChange(platform.value)
								}
							>
								<div className="text-center">
									<div className="text-2xl mb-2">
										{platform.icon}
									</div>
									<div className="font-semibold text-sm mb-1">
										{platform.label}
									</div>
									<div className="text-xs text-gray-600 mb-2">
										{platform.description}
									</div>

									<div className="space-y-1">
										<div className="text-xs font-medium text-gray-700">
											尺寸: {platform.dimensions.width} ×{" "}
											{platform.dimensions.height}
										</div>
										<div className="text-xs font-medium text-gray-700">
											比例: {platform.aspectRatio}
										</div>
									</div>

									<div className="mt-2 space-y-1">
										{platform.features.map(
											(feature, index) => (
												<div
													key={index}
													className="text-xs bg-gray-100 px-2 py-1 rounded"
												>
													{feature}
												</div>
											),
										)}
									</div>
								</div>
							</button>
						);
					})}
				</div>
			</Card>

			{/* 顶部配置栏 */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
				{/* 平台选择 */}
				<Card className="p-4">
					<h3 className="text-sm font-semibold mb-3 flex items-center gap-2">
						<Camera className="w-4 h-4" />
						发布平台
					</h3>
					<Select
						value={selectedPlatform}
						onValueChange={handlePlatformChange}
					>
						<SelectTrigger className="w-full">
							<SelectValue placeholder="选择平台" />
						</SelectTrigger>
						<SelectContent>
							{platforms.map((platform) => (
								<SelectItem
									key={platform.value}
									value={platform.value}
								>
									<div className="flex items-center gap-2">
										<span>{platform.icon}</span>
										<span className="font-medium">
											{platform.label}
										</span>
										<span className="text-xs text-gray-500">
											{platform.dimensions.width} ×{" "}
											{platform.dimensions.height} (
											{platform.aspectRatio})
										</span>
									</div>
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</Card>

				{/* 自定义标题 */}
				<Card className="p-4">
					<h3 className="text-sm font-semibold mb-3">卡片标题</h3>
					<input
						type="text"
						value={customization.content?.customTitle || ""}
						onChange={(e) =>
							setCustomization((prev) => ({
								...prev,
								content: {
									...prev.content,
									customTitle: e.target.value,
								},
							}))
						}
						className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
						placeholder="输入自定义标题"
					/>
				</Card>

				{/* 页脚文字 */}
				<Card className="p-4">
					<h3 className="text-sm font-semibold mb-3">页脚文字</h3>
					<input
						type="text"
						value={customization.content?.customFooter || ""}
						onChange={(e) =>
							setCustomization((prev) => ({
								...prev,
								content: {
									...prev.content,
									customFooter: e.target.value,
								},
							}))
						}
						className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
						placeholder="输入页脚文字"
					/>
				</Card>
			</div>

			{/* 中央预览区域 */}
			<div className="flex justify-center">
				<Card className="inline-block p-4 bg-gradient-to-br from-gray-50 to-gray-100 border-2 border-dashed border-gray-200">
					<div className="text-center mb-3">
						<h3 className="text-lg font-semibold mb-1 flex items-center justify-center gap-2">
							<Eye className="w-4 h-4" />
							实时预览
						</h3>
						<div className="flex items-center justify-center gap-2 text-xs text-gray-600">
							<Badge className="text-xs">
								{selectedTemplate.name}
							</Badge>
							<span>•</span>
							<Badge className="text-xs">
								{
									platforms.find(
										(p) => p.value === selectedPlatform,
									)?.label
								}
							</Badge>
						</div>
					</div>

					<div className="bg-white rounded-lg shadow-inner p-4 mb-3">
						{renderCardPreview()}
					</div>

					<div className="text-center">
						<p className="text-xs text-gray-500 mb-2">
							预览为真实尺寸，所见即所得
						</p>

						{/* 快速导出按钮 */}
						<div className="flex justify-center gap-2 flex-wrap">
							{qualityOptions.map((option) => (
								<Button
									key={option.value}
									onClick={() => handleExport(option.value)}
									disabled={isExporting}
									variant={
										option.value === "medium"
											? "primary"
											: "outline"
									}
									size="sm"
								>
									{isExporting ? (
										<Loader2 className="w-3 h-3 animate-spin mr-1" />
									) : (
										<Download className="w-3 h-3 mr-1" />
									)}
									{option.label}
								</Button>
							))}
						</div>
					</div>
				</Card>
			</div>

			{/* 底部模板画廊 */}
			<div className="bg-white rounded-lg border border-gray-200 p-6">
				<h3 className="text-xl font-semibold mb-6 text-center flex items-center justify-center gap-2">
					<Palette className="w-5 h-5" />
					选择模板风格
				</h3>

				<TemplateSelector
					selectedTemplate={selectedTemplate}
					selectedPlatform={selectedPlatform}
					onTemplateSelect={handleTemplateSelect}
					variant="gallery"
				/>

				<div className="mt-4 text-center">
					<p className="text-sm text-gray-500">
						点击模板卡片切换不同风格 • 支持{" "}
						{
							platforms.find((p) => p.value === selectedPlatform)
								?.label
						}{" "}
						平台
					</p>
				</div>
			</div>

			{/* 高级配置和其他操作 */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* 其他操作 */}
				<Card className="p-6">
					<h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
						<Share2 className="w-5 h-5" />
						分享与操作
					</h3>
					<div className="grid grid-cols-2 gap-3">
						<Button
							onClick={handleCopy}
							disabled={isExporting}
							variant="outline"
							className="w-full"
						>
							复制图片
						</Button>
						<Button
							onClick={handleShare}
							disabled={isExporting}
							variant="outline"
							className="w-full"
						>
							<Share2 className="w-4 h-4 mr-2" />
							分享
						</Button>
					</div>
				</Card>

				{/* 数据统计 */}
				<Card className="p-6">
					<h3 className="text-lg font-semibold mb-4">数据统计</h3>
					<div className="grid grid-cols-3 gap-4 text-center">
						<div>
							<div className="text-2xl font-bold text-blue-600">
								{travelPoints.length}
							</div>
							<div className="text-sm text-gray-600">足迹点</div>
						</div>
						<div>
							<div className="text-2xl font-bold text-green-600">
								{new Set(travelPoints.map((p) => p.city)).size}
							</div>
							<div className="text-sm text-gray-600">城市</div>
						</div>
						<div>
							<div className="text-2xl font-bold text-purple-600">
								{visitedCountries.length}
							</div>
							<div className="text-sm text-gray-600">国家</div>
						</div>
					</div>
				</Card>
			</div>
		</div>
	);
}
