
@repo/web:dev: start watching ...
@repo/web:dev:    ▲ Next.js 15.1.7 (Turbopack)
@repo/web:dev:    - Local:        http://localhost:3000
@repo/web:dev:    - Network:      http://************:3000
@repo/web:dev:    - Environments: .env.local
@repo/web:dev:    - Experiments (use with caution):
@repo/web:dev:      · turbo
@repo/web:dev: 
@repo/web:dev:  ✓ Starting...
@repo/web:dev:  ✓ Compiled in 373ms
@repo/web:dev:  ✓ Ready in 1700ms
@repo/web:dev:  ○ Compiling /api/[[...rest]] ...
@repo/web:dev:  ✓ Compiled /api/[[...rest]] in 3.3s
@repo/web:dev:  ⚠ ./packages/storage/provider/cloudflare-r2
@repo/web:dev: Package @aws-sdk/client-s3 can't be external
@repo/web:dev: The request @aws-sdk/client-s3 matches serverExternalPackages (or the default list).
@repo/web:dev: The package resolves to a different version when requested from the project directory (3.437.0) compared to the package requested from the importing module (3.782.0).
@repo/web:dev: Make sure to install the same version of the package in both locations.
@repo/web:dev: 
@repo/web:dev: 
@repo/web:dev:  ⚠ ./packages/storage/provider/s3
@repo/web:dev: Package @aws-sdk/client-s3 can't be external
@repo/web:dev: The request @aws-sdk/client-s3 matches serverExternalPackages (or the default list).
@repo/web:dev: The package resolves to a different version when requested from the project directory (3.437.0) compared to the package requested from the importing module (3.782.0).
@repo/web:dev: Make sure to install the same version of the package in both locations.
@repo/web:dev: 
@repo/web:dev: 
@repo/web:dev: <-- POST /api/sign-in/email
@repo/web:dev: --> POST /api/sign-in/email 404 2ms
@repo/web:dev:  POST /api/sign-in/email 404 in 5364ms
@repo/web:dev:  ⚠ ./packages/storage/provider/cloudflare-r2
@repo/web:dev: Package @aws-sdk/client-s3 can't be external
@repo/web:dev: The request @aws-sdk/client-s3 matches serverExternalPackages (or the default list).
@repo/web:dev: The package resolves to a different version when requested from the project directory (3.437.0) compared to the package requested from the importing module (3.782.0).
@repo/web:dev: Make sure to install the same version of the package in both locations.
@repo/web:dev: 
@repo/web:dev: 
@repo/web:dev:  ⚠ ./packages/storage/provider/s3
@repo/web:dev: Package @aws-sdk/client-s3 can't be external
@repo/web:dev: The request @aws-sdk/client-s3 matches serverExternalPackages (or the default list).
@repo/web:dev: The package resolves to a different version when requested from the project directory (3.437.0) compared to the package requested from the importing module (3.782.0).
@repo/web:dev: Make sure to install the same version of the package in both locations.
@repo/web:dev: 
@repo/web:dev: 
@repo/web:dev: <-- POST /api/sign-in/email
@repo/web:dev: --> POST /api/sign-in/email 404 0ms
@repo/web:dev:  POST /api/sign-in/email 404 in 84ms
@repo/web:dev:  ⚠ ./packages/storage/provider/cloudflare-r2
@repo/web:dev: Package @aws-sdk/client-s3 can't be external
@repo/web:dev: The request @aws-sdk/client-s3 matches serverExternalPackages (or the default list).
@repo/web:dev: The package resolves to a different version when requested from the project directory (3.437.0) compared to the package requested from the importing module (3.782.0).
@repo/web:dev: Make sure to install the same version of the package in both locations.
@repo/web:dev: 
@repo/web:dev: 
@repo/web:dev:  ⚠ ./packages/storage/provider/s3
@repo/web:dev: Package @aws-sdk/client-s3 can't be external
@repo/web:dev: The request @aws-sdk/client-s3 matches serverExternalPackages (or the default list).
@repo/web:dev: The package resolves to a different version when requested from the project directory (3.437.0) compared to the package requested from the importing module (3.782.0).
@repo/web:dev: Make sure to install the same version of the package in both locations.
@repo/web:dev: 
@repo/web:dev: 
@repo/web:dev: <-- GET /api/auth/get-session?disableCookieCache=true
@repo/web:dev: --> GET /api/auth/get-session?disableCookieCache=true 404 0ms
@repo/web:dev:  GET /api/auth/get-session?disableCookieCache=true 404 in 79ms
@repo/web:dev:  ○ Compiling /auth/login ...
@repo/web:dev:  ✓ Compiled /auth/login in 2.7s
@repo/web:dev:  GET /auth/login?redirectTo=%2Fapp%2Ftravel-memo 200 in 3116ms
@repo/web:dev:  ✓ Compiled /favicon.ico in 81ms
@repo/web:dev:  GET /favicon.ico?favicon.563af232.ico 200 in 185ms
@repo/web:dev:  ⚠ ./packages/storage/provider/cloudflare-r2
@repo/web:dev: Package @aws-sdk/client-s3 can't be external
@repo/web:dev: The request @aws-sdk/client-s3 matches serverExternalPackages (or the default list).
@repo/web:dev: The package resolves to a different version when requested from the project directory (3.437.0) compared to the package requested from the importing module (3.782.0).
@repo/web:dev: Make sure to install the same version of the package in both locations.
@repo/web:dev: 
@repo/web:dev: 
@repo/web:dev:  ⚠ ./packages/storage/provider/s3
@repo/web:dev: Package @aws-sdk/client-s3 can't be external
@repo/web:dev: The request @aws-sdk/client-s3 matches serverExternalPackages (or the default list).
@repo/web:dev: The package resolves to a different version when requested from the project directory (3.437.0) compared to the package requested from the importing module (3.782.0).
@repo/web:dev: Make sure to install the same version of the package in both locations.
@repo/web:dev: 
@repo/web:dev: 
@repo/web:dev: <-- GET /api/auth/get-session?disableCookieCache=true
@repo/web:dev: --> GET /api/auth/get-session?disableCookieCache=true 404 0ms
@repo/web:dev:  GET /api/auth/get-session?disableCookieCache=true 404 in 87ms
@repo/web:dev:  ⚠ ./packages/storage/provider/cloudflare-r2
@repo/web:dev: Package @aws-sdk/client-s3 can't be external
@repo/web:dev: The request @aws-sdk/client-s3 matches serverExternalPackages (or the default list).
@repo/web:dev: The package resolves to a different version when requested from the project directory (3.437.0) compared to the package requested from the importing module (3.782.0).
@repo/web:dev: Make sure to install the same version of the package in both locations.
@repo/web:dev: 
@repo/web:dev: 
@repo/web:dev:  ⚠ ./packages/storage/provider/s3
@repo/web:dev: Package @aws-sdk/client-s3 can't be external
@repo/web:dev: The request @aws-sdk/client-s3 matches serverExternalPackages (or the default list).
@repo/web:dev: The package resolves to a different version when requested from the project directory (3.437.0) compared to the package requested from the importing module (3.782.0).
@repo/web:dev: Make sure to install the same version of the package in both locations.
@repo/web:dev: 
@repo/web:dev: 
@repo/web:dev: <-- POST /api/auth/sign-in/email
@repo/web:dev: --> POST /api/auth/sign-in/email 404 1ms
@repo/web:dev:  POST /api/auth/sign-in/email 404 in 84ms
