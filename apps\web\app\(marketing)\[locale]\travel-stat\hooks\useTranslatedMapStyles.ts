import { useMemo } from "react";
import { MAP_STYLE_CONFIGS, STYLE_CATEGORIES } from "../components/types";
import type { MapStyleType } from "../components/types";
import { useTravelStatTranslations } from "./useTravelStatTranslations";

export function useTranslatedMapStyles() {
	const t = useTravelStatTranslations();

	const translatedMapStyles = useMemo(() => {
		const styles = Object.keys(MAP_STYLE_CONFIGS) as MapStyleType[];
		return styles.map((styleKey) => ({
			id: styleKey,
			...MAP_STYLE_CONFIGS[styleKey],
			name: t.mapStyles.names[styleKey](),
			description: t.mapStyles.descriptions[styleKey](),
		}));
	}, [t]);

	const translatedStyleCategories = useMemo(() => {
		const categories = Object.keys(
			STYLE_CATEGORIES,
		) as (keyof typeof STYLE_CATEGORIES)[];
		return categories.map((catKey) => ({
			id: catKey,
			...STYLE_CATEGORIES[catKey as keyof typeof STYLE_CATEGORIES],
			name: t.mapStyles.categories[
				catKey as keyof typeof t.mapStyles.categories
			](),
		}));
	}, [t]);

	const getTranslatedMapStyle = (styleKey: MapStyleType) => {
		return translatedMapStyles.find((s) => s.id === styleKey);
	};

	return {
		t,
		translatedMapStyles,
		translatedStyleCategories,
		getTranslatedMapStyle,
	};
}
