import { z } from "zod";

// 新增：图片对象的schema
export const travelPointImageSchema = z.object({
	url: z.string().url("图片URL必须是有效的URL"),
	description: z.string().optional(), // 图片描述，可选
	alt: z.string().optional(), // 图片alt文本，可选
	caption: z.string().optional(), // 图片标题，可选
	uploadedAt: z.string().optional(), // 上传时间，可选
});

// Zod schema for TravelPointIconType - 改为简单的字符串类型
const travelPointIconTypeSchema = z.string();

// Zod schema for TravelPoint
export const travelPointSchema = z.object({
	id: z.string(), // Assuming CUID2 based on potential Prisma usage
	location: z.string(),
	description: z.string(),
	date: z
		.string()
		.regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format"), // Validate ISO date string format (YYYY-MM-DD)
	images: z.array(travelPointImageSchema), // 修改：从字符串数组改为图片对象数组
	iconType: travelPointIconTypeSchema,
	latitude: z.number(),
	longitude: z.number(),
	order: z.number().int(),
	country: z.string().optional(), // 新增
	city: z.string().optional(), // 新增
});

// Zod schema for TravelTimeline
export const travelTimelineSchema = z.object({
	id: z.string(),
	title: z.string(),
	date: z
		.string()
		.regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format"),
	points: z.array(travelPointSchema),
});

// Zod schema for TravelDiaryContent
export const travelDiaryContentSchema = z.object({
	timelines: z.array(travelTimelineSchema),
});

// Optional: Infer TypeScript type from Zod schema if needed elsewhere
// export type TravelDiaryContent = z.infer<typeof travelDiaryContentSchema>;
