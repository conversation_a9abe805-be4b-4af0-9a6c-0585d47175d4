import { getLocale, getTranslations } from "next-intl/server";
import { ChangelogSection } from "../../../../modules/marketing/changelog/components/ChangelogSection";

export default async function ChangelogPage() {
	const t = await getTranslations("changelog");
	const locale = await getLocale();

	// 从i18n系统获取changelog数据，确保只获取当前语言的内容
	const changelogData = [
		{
			key: "2025-06-15",
			date: "2025-06-15",
		},
		{
			key: "2025-06-12",
			date: "2025-06-12",
		},
		{
			key: "2025-06-10",
			date: "2025-06-10",
		},
		{
			key: "2025-06-08",
			date: "2025-06-08",
		},
		{
			key: "2025-06-05",
			date: "2025-06-05",
		},
		{
			key: "2025-06-02",
			date: "2025-06-02",
		},
	];

	const changelogItems = changelogData
		.map((item) => {
			try {
				// 使用具体的翻译键值，避免动态构建
				const changes = t.raw(
					`items.${item.key as "2025-06-15" | "2025-06-12" | "2025-06-10" | "2025-06-08" | "2025-06-05" | "2025-06-02"}.changes`,
				) as string[];

				// 调试信息：检查获取到的changes数组
				if (process.env.NODE_ENV === "development") {
					console.log(
						`[Changelog] Locale: ${locale}, Item: ${item.key}, Changes count: ${changes?.length || 0}`,
					);
					if (changes?.length > 0) {
						console.log(
							`[Changelog] First change: ${changes[0]?.substring(0, 50)}...`,
						);
					}
				}

				if (!changes || changes.length === 0) {
					return null;
				}
				return {
					date: item.date,
					changes,
				};
			} catch (error) {
				console.warn(
					`Missing translation for changelog item: ${item.key}`,
					error,
				);
				return null;
			}
		})
		.filter(
			(item): item is { date: string; changes: string[] } =>
				item !== null,
		)
		.sort(
			(a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
		);

	return (
		<div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20">
			{/* 背景装饰元素 */}
			<div className="fixed inset-0 overflow-hidden pointer-events-none">
				<div className="absolute -top-40 -left-40 w-80 h-80 bg-gradient-to-r from-blue-200/30 to-purple-200/30 rounded-full blur-3xl animate-pulse" />
				<div className="absolute top-1/3 -right-20 w-60 h-60 bg-gradient-to-r from-pink-200/20 to-blue-200/20 rounded-full blur-3xl animate-pulse delay-1000" />
				<div className="absolute bottom-20 left-1/4 w-40 h-40 bg-gradient-to-r from-purple-200/20 to-pink-200/20 rounded-full blur-3xl animate-pulse delay-2000" />
			</div>

			<div className="relative container max-w-5xl pt-20 pb-20">
				{/* 头部区域 */}
				<div className="mb-16 text-center">
					<div className="relative inline-block">
						{/* 标题装饰 */}
						<div className="absolute -inset-4 bg-gradient-to-r from-blue-400/20 via-purple-400/20 to-pink-400/20 rounded-2xl blur-xl opacity-70 animate-pulse" />
						<h1 className="relative mb-4 font-bold text-6xl md:text-7xl bg-gradient-to-r from-slate-800 via-blue-600 to-purple-600 bg-clip-text text-transparent">
							{t("title")}
						</h1>
					</div>

					<div className="relative max-w-2xl mx-auto">
						<p className="text-xl md:text-2xl text-slate-600 leading-relaxed font-light">
							{t("description")}
						</p>
						{/* 描述文字下方的装饰线 */}
						<div className="flex justify-center mt-6">
							<div className="w-24 h-1 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 rounded-full opacity-60" />
						</div>
					</div>

					{/* 浮动装饰元素 */}
					<div className="absolute top-8 left-1/4 w-3 h-3 bg-blue-400 rounded-full animate-bounce opacity-60" />
					<div className="absolute top-12 right-1/3 w-2 h-2 bg-purple-400 rounded-full animate-bounce delay-500 opacity-60" />
					<div className="absolute top-16 right-1/4 w-2.5 h-2.5 bg-pink-400 rounded-full animate-bounce delay-1000 opacity-60" />
				</div>

				{/* 内容区域 */}
				<div className="relative">
					<ChangelogSection items={changelogItems} />
				</div>

				{/* 页脚装饰 */}
				<div className="mt-20 text-center">
					<div className="flex justify-center items-center gap-4 text-slate-400">
						<div className="w-8 h-0.5 bg-gradient-to-r from-transparent to-slate-300 rounded-full" />
						<span className="text-sm font-medium tracking-wide">
							Made with ❤️ for our community
						</span>
						<div className="w-8 h-0.5 bg-gradient-to-l from-transparent to-slate-300 rounded-full" />
					</div>
				</div>
			</div>
		</div>
	);
}
