import { <PERSON>, <PERSON>, <PERSON>, Palette, Sparkles } from "lucide-react";
import type {
	AnimationTheme,
	AtmosphereTheme,
	ColorThemeType,
	MapProjectionType,
	MapStyleType,
	MarkerStyleType,
} from "../../types";
import type { ControlItem } from "../types";

interface CreateControlItemsProps {
	mapLoaded: boolean;
	atmosphereTheme: AtmosphereTheme;
	mapStyle: MapStyleType;
	animationTheme: AnimationTheme;
	mapProjection: MapProjectionType;
	currentColorTheme: ColorThemeType;
	markerStyle: MarkerStyleType;
	colorThemeName: string; // 翻译后的颜色主题名称
	mapStyleName: string; // 翻译后的地图样式名称
	projectionName: string; // 翻译后的投影名称
	animationThemeName: string; // 翻译后的动画主题名称
	atmosphereThemeName: string; // 翻译后的大气层主题名称
	markerStyleName: string; // 翻译后的标记样式名称
	// 新增显示控制参数
	showTooltips: boolean;
	showMarkers: boolean;
	displayModeName: string; // 当前显示模式名称
	translations: any; // 翻译函数
}

export function createControlItems({
	mapLoaded,
	atmosphereTheme,
	mapStyle,
	animationTheme,
	mapProjection,
	currentColorTheme,
	markerStyle,
	colorThemeName,
	mapStyleName,
	projectionName,
	animationThemeName,
	atmosphereThemeName,
	markerStyleName,
	showTooltips,
	showMarkers,
	displayModeName,
	translations,
}: CreateControlItemsProps): ControlItem[] {
	return [
		{
			id: "style",
			icon: <Palette className="w-4 h-4" />,
			title: translations.mapControls.styles.title(),
			buttonText: mapStyleName,
			hasPopover: true,
		},
		{
			id: "markers",
			icon: <Sparkles className="w-4 h-4" />,
			title: translations.mapControls.styles.markers(),
			buttonText: markerStyleName,
			hasPopover: true,
		},
		{
			id: "display",
			icon: <Eye className="w-4 h-4" />,
			title: translations.mapControls.display.title(),
			buttonText: displayModeName,
			hasPopover: true,
		},
		{
			id: "atmosphere",
			icon: <Cloud className="w-4 h-4" />,
			title: translations.mapControls.styles.atmosphere(),
			buttonText: atmosphereThemeName,
			hasPopover: true,
		},
		{
			id: "animation",
			icon: <Sparkles className="w-4 h-4" />,
			title: translations.mapControls.styles.animation(),
			buttonText: animationThemeName,
			hasPopover: true,
		},
		{
			id: "colors",
			icon: <Palette className="w-4 h-4" />,
			title: translations.mapControls.styles.colors(),
			buttonText: colorThemeName,
			hasPopover: true,
		},
		{
			id: "projection",
			icon: <Globe className="w-4 h-4" />,
			title: translations.mapControls.styles.projection(),
			buttonText: projectionName,
			hasPopover: true,
		},
	];
}
