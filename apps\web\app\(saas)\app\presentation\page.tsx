"use client";

import Link from "next/link";
import { useEffect, useState } from "react";
import { fetchTravelDiaries } from "../mock-data";
import type { TravelDiary } from "../mock-data";
import { LoadingSpinner } from "./components/common/LoadingSpinner";
import { formatDateRange } from "./utils/date-utils";

export default function TravelStoryListPage() {
	const [diaries, setDiaries] = useState<TravelDiary[]>([]);
	const [isLoading, setIsLoading] = useState(true);

	// 加载日记列表
	useEffect(() => {
		async function loadDiaries() {
			try {
				setIsLoading(true);
				const diaryData = await fetchTravelDiaries();
				setDiaries(diaryData);
			} catch (err) {
				console.error("加载日记列表失败", err);
			} finally {
				setIsLoading(false);
			}
		}

		loadDiaries();
	}, []);

	if (isLoading) {
		return <LoadingSpinner message="加载旅行故事列表..." />;
	}

	if (diaries.length === 0) {
		return (
			<div className="flex flex-col items-center justify-center min-h-screen p-4">
				<div className="text-4xl mb-4">🧳</div>
				<h1 className="text-2xl font-semibold mb-2">还没有旅行故事</h1>
				<p className="text-muted-foreground mb-6 text-center max-w-md">
					您尚未创建任何旅行日记，或者现有的日记没有足够的数据来生成故事。
				</p>
				<Link
					href="/app/diary"
					className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background bg-primary text-primary-foreground hover:bg-primary/90 h-10 py-2 px-4"
				>
					去创建旅行日记
				</Link>
			</div>
		);
	}

	return (
		<div className="container py-8 px-4 mx-auto">
			<div className="mb-8 text-center">
				<h1 className="text-3xl font-bold mb-2">旅行地图故事</h1>
				<p className="text-muted-foreground">
					选择一个旅行日记，开始您的地图故事之旅
				</p>
			</div>

			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
				{diaries.map((diary) => {
					// 计算统计信息
					const allPoints = diary.timelines
						.flatMap((timeline) => timeline.points)
						.sort(
							(a, b) =>
								new Date(a.date).getTime() -
								new Date(b.date).getTime(),
						);

					const startDate =
						allPoints.length > 0
							? new Date(allPoints[0].date)
							: null;
					const endDate =
						allPoints.length > 0
							? new Date(allPoints[allPoints.length - 1].date)
							: null;

					const dateRangeText =
						startDate && endDate
							? formatDateRange(startDate, endDate)
							: "";

					return (
						<Link
							key={diary.id}
							href={`/app/mapbox/${diary.id}`}
							className="group block"
						>
							<div className="rounded-lg overflow-hidden bg-white shadow-md hover:shadow-lg transition-shadow">
								{/* 封面图 */}
								<div className="relative h-48">
									<div
										className="w-full h-full bg-cover bg-center"
										style={{
											backgroundImage: `url(${diary.coverImage || "https://images.unsplash.com/photo-1488085061387-422e29b40080?q=80&w=1000"})`,
										}}
									/>
									<div className="absolute inset-0 bg-black/40 group-hover:bg-black/30 transition-colors" />

									{/* 标题 */}
									<div className="absolute inset-0 flex flex-col justify-end p-4 text-white">
										<h2 className="text-xl font-semibold truncate">
											{diary.title}
										</h2>
										<p className="text-sm text-white/90 truncate">
											{diary.subtitle}
										</p>
									</div>
								</div>

								{/* 信息 */}
								<div className="p-4">
									<div className="text-xs text-muted-foreground mb-3">
										{dateRangeText}
									</div>

									<div className="flex items-center justify-between text-sm">
										<div className="flex items-center space-x-3">
											<div className="flex items-center">
												<span className="text-primary mr-1">
													📍
												</span>
												<span>
													{allPoints.length} 地点
												</span>
											</div>

											<div className="flex items-center">
												<span className="text-primary mr-1">
													📷
												</span>
												<span>
													{allPoints.reduce(
														(sum, point) =>
															sum +
															(point.images
																?.length || 0),
														0,
													)}{" "}
													照片
												</span>
											</div>
										</div>

										<div className="text-primary font-medium group-hover:underline">
											查看故事
										</div>
									</div>
								</div>
							</div>
						</Link>
					);
				})}
			</div>
		</div>
	);
}
