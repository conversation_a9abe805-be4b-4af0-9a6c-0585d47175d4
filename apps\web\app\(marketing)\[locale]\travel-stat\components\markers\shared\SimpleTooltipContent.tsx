"use client";

import { X } from "lucide-react";
import type { TravelPoint } from "../../../types";

interface SimpleTooltipContentProps {
	point: TravelPoint;
	onRemovePoint: (id: string) => void;
	showDeleteButton?: boolean;
	variant?: "transparent" | "solid";
}

export function SimpleTooltipContent({
	point,
	onRemovePoint,
	showDeleteButton = true,
	variant = "transparent",
}: SimpleTooltipContentProps) {
	const containerClass =
		variant === "transparent"
			? "bg-white/80 backdrop-blur-sm shadow-sm border border-gray-200/50"
			: "bg-white shadow-lg border border-gray-200";

	return (
		<div
			className={`${containerClass} rounded-md overflow-hidden min-w-0 group`}
		>
			{/* 内容区域 */}
			<div className="px-2 py-1.5 pr-6 min-w-0 max-w-[200px]">
				{/* 城市名称 - 单行显示，超长截断 */}
				<div
					className="font-medium text-xs text-gray-700 leading-tight truncate"
					title={point.city} // 悬停显示完整内容
				>
					{point.city}
				</div>

				{/* 国家信息 - 单行显示，超长截断 */}
				{point.country && (
					<div
						className="text-[10px] text-gray-400 leading-tight truncate"
						title={point.country} // 悬停显示完整内容
					>
						{point.country}
					</div>
				)}
			</div>

			{/* 删除按钮 - 只在hover时显示 */}
			{showDeleteButton && (
				<button
					type="button"
					onClick={(e) => {
						e.stopPropagation();
						onRemovePoint(point.id);
					}}
					className="absolute top-0.5 right-0.5 w-4 h-4 bg-gray-100/80 hover:bg-red-100 hover:text-red-600 text-gray-400 rounded-full flex items-center justify-center transition-all duration-200 border border-gray-200/50 hover:border-red-200 opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100"
					aria-label="删除标记点"
				>
					<X className="w-2.5 h-2.5" strokeWidth={2} />
				</button>
			)}
		</div>
	);
}
