// =============================================================================
// 统一工具函数导出 - Travel Stat 模块
// =============================================================================

// ===== 数据处理工具 =====
export {
	extractCountryInfo,
	calculateStats,
	exportTravelData,
	importTravelData,
} from "./dataUtils";

// ===== 颜色处理工具 =====
export {
	getStrategyManager,
	setStrategyManager,
	updateStrategyTheme,
	calculateColor,
	getColorHex,
	getColorRgba,
	batchCalculateColors,
	getAvailableColorThemes,
	getColorThemesByCategory,
	getRecommendedColorThemes,
	getCurrentStrategyInfo,
	getCurrentThemeInfo,
	createIndependentStrategyManager,
} from "./colorUtils";
