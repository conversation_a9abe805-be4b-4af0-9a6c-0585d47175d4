import { z } from "zod";

// Basic, permissive schema for Tiptap JSONContent.
// A more strict schema would require defining nodes (doc, paragraph, text, etc.)
// and marks recursively, which can be complex.
export const TiptapContentSchema = z
	.object({
		type: z.literal("doc"),
		content: z.array(z.record(z.any())).optional(), // Array of node objects
	})
	.passthrough(); // Allow unknown keys typical in Tiptap nodes/marks

export type TiptapContent = z.infer<typeof TiptapContentSchema>;

// Example of a more specific node (adjust as needed)
// const TextNodeSchema = z.object({
//   type: z.literal('text'),
//   text: z.string(),
//   marks: z.array(z.record(z.any())).optional(),
// }).passthrough();

// const ParagraphNodeSchema = z.object({
//   type: z.literal('paragraph'),
//   content: z.array(TextNodeSchema).optional(), // Example: Paragraph contains text nodes
// }).passthrough();

// More complete schema would involve a recursive definition
// export const TiptapNodeSchema: z.ZodTypeAny = z.lazy(() =>
//   z.object({
//     type: z.string(),
//     attrs: z.record(z.any()).optional(),
//     content: z.array(TiptapNodeSchema).optional(),
//     marks: z.array(z.record(z.any())).optional(),
//     text: z.string().optional(),
//   }).passthrough()
// );

// export const StrictTiptapContentSchema = z.object({
//   type: z.literal('doc'),
//   content: z.array(TiptapNodeSchema).optional(),
// });
