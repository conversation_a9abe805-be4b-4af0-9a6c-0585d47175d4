export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function generateMetadata() {
	return {
		title: "选择套餐 - AI图片生成器",
		description: "选择适合您的AI图片生成套餐",
	};
}

export default function ChoosePlanPage() {
	return (
		<div className="min-h-screen bg-gray-50 py-12">
			<div className="max-w-5xl mx-auto px-4">
				<div className="mb-8 text-center">
					<h1 className="text-center font-bold text-3xl lg:text-4xl bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
						选择您的AI创作套餐
					</h1>
					<p className="text-muted-foreground text-lg lg:text-xl mt-4">
						解锁无限创意，开始您的AI图片生成之旅
					</p>
				</div>

				<div className="space-y-8">
					{/* AI图片生成器特色说明 */}
					<div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
						<div className="text-center p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg">
							<div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
								<svg
									className="w-6 h-6 text-white"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<title>AI艺术创作图标</title>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
									/>
								</svg>
							</div>
							<h3 className="font-semibold text-lg mb-2">
								AI艺术创作
							</h3>
							<p className="text-sm text-gray-600">
								使用最先进的AI技术，将您的想象变为现实
							</p>
						</div>

						<div className="text-center p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg">
							<div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
								<svg
									className="w-6 h-6 text-white"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<title>快速生成图标</title>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M13 10V3L4 14h7v7l9-11h-7z"
									/>
								</svg>
							</div>
							<h3 className="font-semibold text-lg mb-2">
								快速生成
							</h3>
							<p className="text-sm text-gray-600">
								秒级生成高质量图片，提升您的创作效率
							</p>
						</div>

						<div className="text-center p-6 bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-lg">
							<div className="w-12 h-12 bg-indigo-500 rounded-full flex items-center justify-center mx-auto mb-4">
								<svg
									className="w-6 h-6 text-white"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<title>多种风格图标</title>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
									/>
								</svg>
							</div>
							<h3 className="font-semibold text-lg mb-2">
								多种风格
							</h3>
							<p className="text-sm text-gray-600">
								支持多种艺术风格，满足不同创作需求
							</p>
						</div>
					</div>

					{/* 简化的价格表 */}
					<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
						<div className="bg-white p-6 rounded-lg shadow-md">
							<h3 className="text-xl font-semibold mb-4">
								基础版
							</h3>
							<div className="text-3xl font-bold mb-4">
								¥29
								<span className="text-sm font-normal">/月</span>
							</div>
							<ul className="space-y-2 mb-6">
								<li>• 每月100张图片</li>
								<li>• 基础AI模型</li>
								<li>• 标准分辨率</li>
							</ul>
							<button
								type="button"
								className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700"
							>
								选择基础版
							</button>
						</div>

						<div className="bg-white p-6 rounded-lg shadow-md border-2 border-purple-500">
							<h3 className="text-xl font-semibold mb-4">
								专业版
							</h3>
							<div className="text-3xl font-bold mb-4">
								¥99
								<span className="text-sm font-normal">/月</span>
							</div>
							<ul className="space-y-2 mb-6">
								<li>• 每月500张图片</li>
								<li>• 高级AI模型</li>
								<li>• 高清分辨率</li>
								<li>• 优先处理</li>
							</ul>
							<button
								type="button"
								className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700"
							>
								选择专业版
							</button>
						</div>

						<div className="bg-white p-6 rounded-lg shadow-md">
							<h3 className="text-xl font-semibold mb-4">
								企业版
							</h3>
							<div className="text-3xl font-bold mb-4">
								¥299
								<span className="text-sm font-normal">/月</span>
							</div>
							<ul className="space-y-2 mb-6">
								<li>• 无限制图片</li>
								<li>• 最新AI模型</li>
								<li>• 超高清分辨率</li>
								<li>• 专属客服</li>
							</ul>
							<button
								type="button"
								className="w-full bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700"
							>
								选择企业版
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
