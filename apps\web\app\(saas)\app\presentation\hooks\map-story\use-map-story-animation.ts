"use client";

import mapboxgl from "mapbox-gl";
import { useCallback } from "react";
import { ANIMATION_DURATION, StoryState } from "../../constants";
import type { MapStoryAnimation, MapStoryTimerContext } from "./types";
import { createLogger, delay } from "./utils";

// 创建动画模块专用的日志记录器
const logger = createLogger("MapStoryAnimation");

export function useMapStoryAnimation(
	context: MapStoryTimerContext,
): MapStoryAnimation {
	const { props, state, setters, refs, timers } = context;
	const { points } = props;
	const { mapInstance, storyState } = state;
	const {
		setShowPointInfo,
		setShowImages,
		setFlyToPointIndex,
		setStoryState,
		setCurrentPointIndex,
		setIsPlaying,
		setShowRestartPrompt,
		setSpotlightPosition,
	} = setters;
	const { isAnimatingRef } = refs;
	const { clearPlayTimer } = timers;

	// 提取导出模式配置
	const isExportMode = props.config?.isExportMode || false;
	const exportPointDuration = props.config?.pointDuration || 5;

	// 更新聚光灯效果位置
	const updateSpotlightPosition = useCallback(() => {
		if (
			!mapInstance ||
			state.currentPointIndex <= 0 ||
			state.currentPointIndex > points.length
		)
			return;

		const pointIndex = state.currentPointIndex - 1;
		const currentPoint = points[pointIndex];

		// 检查当前点位坐标是否有效
		if (
			!currentPoint ||
			!currentPoint.coordinates ||
			typeof currentPoint.coordinates.lng !== "number" ||
			typeof currentPoint.coordinates.lat !== "number"
		) {
			logger.error("当前点位坐标无效，无法更新聚光灯位置", {
				pointIndex,
				currentPoint,
			});
			return;
		}

		// 将地理坐标转换为屏幕像素坐标
		try {
			const pixelCoords = mapInstance.project([
				currentPoint.coordinates.lng,
				currentPoint.coordinates.lat,
			]);

			setSpotlightPosition({ x: pixelCoords.x, y: pixelCoords.y });
		} catch (error) {
			logger.error("计算聚光灯位置失败", error);
		}
	}, [mapInstance, state.currentPointIndex, points, setSpotlightPosition]);

	// 完成演示，相机上升并切换到完成状态
	const finishPresentation = useCallback(async () => {
		// 避免重复触发或在不适当的状态下触发
		if (
			isAnimatingRef.current &&
			storyState !== StoryState.POINT_ARRIVED_DISPLAYING
		) {
			logger.info("finishPresentation: 动画已在进行中，跳过", {
				storyState,
			});
			return;
		}

		if (storyState === StoryState.COMPLETED) {
			logger.info("finishPresentation: 已经处于完成状态，跳过");
			return;
		}

		// 标记动画开始
		logger.info("finishPresentation: 开始结束动画流程");
		isAnimatingRef.current = true;

		// 停止自动播放和清除计时器
		setIsPlaying(false);
		clearPlayTimer();
		setFlyToPointIndex(null); // 防止触发新的飞行动画

		// 如果刚刚显示了最后一个点位的信息，给用户一些时间查看
		const pauseDuration = isExportMode
			? exportPointDuration * 1000
			: ANIMATION_DURATION.pauseBeforeCompletion;

		logger.debug(`finishPresentation: 等待${pauseDuration}ms后继续`);
		await delay(pauseDuration);

		// 隐藏点位信息和图片
		setShowPointInfo(false);
		setShowImages(false);

		// 设置为相机动画状态
		setStoryState(StoryState.ANIMATING_TO_COMPLETED_VIEW);

		if (mapInstance) {
			try {
				logger.info("finishPresentation: 调整地图视角以显示所有点位", {
					pointsCount: points.length,
				});

				// 计算所有点位的边界
				const bounds = new mapboxgl.LngLatBounds();
				const validPoints = points.filter(
					(point) =>
						point.coordinates &&
						typeof point.coordinates.lng === "number" &&
						typeof point.coordinates.lat === "number",
				);

				if (validPoints.length === 0) {
					throw new Error("没有有效坐标的点位，无法调整地图视角");
				}

				validPoints.forEach((point) => {
					bounds.extend([
						point.coordinates.lng,
						point.coordinates.lat,
					]);
				});

				// 调整地图视角以显示所有点位
				await new Promise<void>((resolve, reject) => {
					try {
						mapInstance.fitBounds(bounds, {
							padding: 100,
							duration: ANIMATION_DURATION.overviewFit,
							essential: true,
						});

						// 监听动画完成事件
						mapInstance.once("moveend", () => resolve());

						// 添加安全超时，避免动画卡住
						setTimeout(() => {
							resolve(); // 即使没收到moveend事件也继续执行
						}, ANIMATION_DURATION.overviewFit + 1000);
					} catch (error) {
						reject(error);
					}
				});

				logger.info("finishPresentation: 地图视角调整完成");
			} catch (error) {
				// 出错时记录但继续流程
				logger.error("finishPresentation: 地图视角调整失败", error);
			}
		}

		// 设置为完成状态
		setStoryState(StoryState.COMPLETED);
		setCurrentPointIndex(points.length); // 设置为最后一个点位的索引，确保所有点位显示为已访问

		// 短暂等待后显示重新开始提示
		await delay(ANIMATION_DURATION.contentFade);
		setShowRestartPrompt(true);

		// 标记动画完成
		isAnimatingRef.current = false;
		logger.info("finishPresentation: 结束动画完成");
	}, [
		storyState,
		isAnimatingRef,
		setIsPlaying,
		clearPlayTimer,
		setFlyToPointIndex,
		isExportMode,
		exportPointDuration,
		setShowPointInfo,
		setShowImages,
		setStoryState,
		mapInstance,
		points,
		setCurrentPointIndex,
		setShowRestartPrompt,
	]);

	// 前进到指定点位
	const advanceToPoint = useCallback(
		async (targetPointArrayIndex: number) => {
			// 记录调用参数，用于排查问题
			logger.info("advanceToPoint: 被调用，准备前进到点位", {
				targetPointArrayIndex,
				isAnimating: isAnimatingRef.current,
				storyState,
			});

			// 避免重复动画或在不适当的状态下触发
			if (isAnimatingRef.current) {
				// 如果是首次前往第一个点位（索引为0），则允许覆盖锁定
				const isInitialPointNavigation =
					targetPointArrayIndex === 0 &&
					(storyState === StoryState.OVERVIEW ||
						storyState === StoryState.INITIALIZING);

				if (isInitialPointNavigation) {
					logger.info(
						"advanceToPoint: 尽管动画锁定，但允许首次导航到第一个点位",
						{
							targetPointArrayIndex,
							storyState,
							override: true,
						},
					);
					// 继续执行，覆盖锁定
				} else {
					logger.info("advanceToPoint: 动画已在进行中，跳过", {
						targetPointArrayIndex,
						storyState,
						override: false,
					});
					return;
				}
			}

			// 检查目标索引是否有效
			if (
				targetPointArrayIndex < 0 ||
				targetPointArrayIndex >= points.length
			) {
				logger.error(
					`advanceToPoint: 目标索引 ${targetPointArrayIndex} 超出范围`,
					{
						validRange: `[0, ${points.length - 1}]`,
					},
				);

				// 如果试图前进到超出点位数量的索引，并且尚未完成，则触发完成动画
				if (
					targetPointArrayIndex >= points.length &&
					storyState !== StoryState.COMPLETED &&
					storyState !== StoryState.ANIMATING_TO_COMPLETED_VIEW
				) {
					logger.info("advanceToPoint: 触发完成动画");
					await finishPresentation();
				}
				return;
			}

			// 标记动画开始
			logger.info("advanceToPoint: 开始前进到点位索引", {
				targetPointArrayIndex,
				uiIndex: targetPointArrayIndex + 1,
				pointTitle: points[targetPointArrayIndex]?.title || "未知点位",
			});
			isAnimatingRef.current = true;

			// 清除任何可能的先前动画计时器
			clearPlayTimer();

			// 设置为飞行状态并隐藏之前的点位信息
			setStoryState(StoryState.FLYING_TO_POINT);
			setShowPointInfo(false);
			setShowImages(false);

			// 触发地图组件飞行到目标点位
			// MapComponent 会通过 flyToPointIndex 识别要飞往的点位
			setFlyToPointIndex(targetPointArrayIndex);

			// 注意：handlePointArrival 将在飞行完成后被调用，它会重置 isAnimatingRef.current
		},
		[
			isAnimatingRef,
			points.length,
			storyState,
			finishPresentation,
			setStoryState,
			setShowPointInfo,
			setShowImages,
			setFlyToPointIndex,
			clearPlayTimer,
		],
	);

	return {
		updateSpotlightPosition,
		finishPresentation,
		advanceToPoint,
	};
}
