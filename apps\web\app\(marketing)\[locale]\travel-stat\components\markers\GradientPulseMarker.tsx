"use client";

import { MapPin } from "lucide-react";
import { useMemo } from "react";

import type {
	BaseMarkerProps,
	GradientPulseConfig,
	GradientPulseTheme,
} from "./types";
import { GRADIENT_PULSE_THEMES } from "./types";

interface GradientPulseMarkerProps extends BaseMarkerProps {
	theme?: GradientPulseTheme;
	customConfig?: Partial<GradientPulseConfig>;
	hideOutline?: boolean;
}

export function GradientPulseMarker({
	point,
	onRemovePoint,
	isSelected = false,
	scale = 1,
	animationSpeed = "normal",
	theme = "ocean",
	customConfig,
	hideOutline = false,
}: GradientPulseMarkerProps) {
	// 合并主题配置和自定义配置
	const config = useMemo(() => {
		const baseConfig = GRADIENT_PULSE_THEMES[theme];
		return {
			...baseConfig,
			...customConfig,
		};
	}, [theme, customConfig]);

	// 根据动画速度调整脉动频率
	const speedMultiplier = useMemo(() => {
		switch (animationSpeed) {
			case "slow":
				return 0.6;
			case "fast":
				return 1.8;
			default:
				return 1;
		}
	}, [animationSpeed]);

	const finalPulseSpeed = config.pulseSpeed * speedMultiplier;
	const finalGlowSize = config.glowSize * scale * 1.5; // 增大光晕范围

	// 生成唯一的渐变ID，避免冲突
	const gradientId = `gradient-${point.id}`;
	const glowId = `glow-${point.id}`;
	const outerGlowId = `outer-glow-${point.id}`;

	return (
		<div className="relative group">
			{hideOutline ? (
				// 无轮廓模式 - 只显示图标
				<div
					className="flex items-center justify-center cursor-pointer hover:scale-110 transition-all duration-300"
					style={{
						width: "36px",
						height: "36px",
						transform: isSelected ? "scale(1.3)" : "scale(1)",
					}}
				>
					<MapPin
						className="w-6 h-6 drop-shadow-lg"
						style={{ color: config.primaryColor }}
					/>
				</div>
			) : (
				// 标准模式 - 带所有光晕效果
				<>
					{/* SVG滤镜和渐变定义 */}
					<svg
						className="absolute inset-0 w-0 h-0"
						style={{ position: "absolute" }}
						aria-hidden="true"
					>
						<defs>
							{/* 径向渐变定义 */}
							<radialGradient
								id={gradientId}
								cx="50%"
								cy="50%"
								r="50%"
							>
								<stop
									offset="0%"
									stopColor={config.primaryColor}
									stopOpacity="1"
								/>
								<stop
									offset="70%"
									stopColor={config.secondaryColor}
									stopOpacity="0.6"
								/>
								<stop
									offset="100%"
									stopColor={config.secondaryColor}
									stopOpacity="0"
								/>
							</radialGradient>

							{/* 发光滤镜 */}
							<filter
								id={glowId}
								x="-50%"
								y="-50%"
								width="200%"
								height="200%"
							>
								<feGaussianBlur
									stdDeviation="4"
									result="coloredBlur"
								/>
								<feMerge>
									<feMergeNode in="coloredBlur" />
									<feMergeNode in="SourceGraphic" />
								</feMerge>
							</filter>

							{/* 外层强烈发光滤镜 */}
							<filter
								id={outerGlowId}
								x="-100%"
								y="-100%"
								width="300%"
								height="300%"
							>
								<feGaussianBlur
									stdDeviation="8"
									result="strongBlur"
								/>
								<feColorMatrix
									values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1.5 0"
									result="intensified"
								/>
								<feMerge>
									<feMergeNode in="intensified" />
									<feMergeNode in="SourceGraphic" />
								</feMerge>
							</filter>
						</defs>
					</svg>

					{/* 最外层超大光晕 - 增强版 */}
					<div
						className="absolute rounded-full"
						style={{
							width: `${finalGlowSize * 1.4}px`,
							height: `${finalGlowSize * 1.4}px`,
							background: `radial-gradient(circle, ${config.primaryColor}60 0%, ${config.secondaryColor}40 30%, transparent 70%)`,
							animation: `gradient-pulse-massive ${finalPulseSpeed * 1.2}s ease-in-out infinite`,
							transform: "translate(-50%, -50%)",
							top: "50%",
							left: "50%",
							filter: `url(#${outerGlowId})`,
						}}
					/>

					{/* 外层脉动光晕 - 大圈 */}
					<div
						className="absolute rounded-full"
						style={{
							width: `${finalGlowSize}px`,
							height: `${finalGlowSize}px`,
							background: `radial-gradient(circle, ${config.primaryColor}80 0%, ${config.secondaryColor}50 50%, transparent 80%)`,
							animation: `gradient-pulse-outer ${finalPulseSpeed}s ease-in-out infinite`,
							transform: "translate(-50%, -50%)",
							top: "50%",
							left: "50%",
						}}
					/>

					{/* 中层脉动光晕 - 中圈 */}
					<div
						className="absolute rounded-full"
						style={{
							width: `${finalGlowSize * 0.8}px`,
							height: `${finalGlowSize * 0.8}px`,
							background: `radial-gradient(circle, ${config.primaryColor}90 0%, ${config.secondaryColor}60 60%, transparent 90%)`,
							animation: `gradient-pulse-middle ${finalPulseSpeed * 0.8}s ease-in-out infinite`,
							animationDelay: `${finalPulseSpeed * 0.15}s`,
							transform: "translate(-50%, -50%)",
							top: "50%",
							left: "50%",
						}}
					/>

					{/* 内层强烈光晕 - 小圈 */}
					<div
						className="absolute rounded-full"
						style={{
							width: `${finalGlowSize * 0.5}px`,
							height: `${finalGlowSize * 0.5}px`,
							background: `radial-gradient(circle, ${config.primaryColor} 0%, ${config.secondaryColor}80 40%, transparent 70%)`,
							animation: `gradient-pulse-inner ${finalPulseSpeed * 0.6}s ease-in-out infinite`,
							animationDelay: `${finalPulseSpeed * 0.1}s`,
							transform: "translate(-50%, -50%)",
							top: "50%",
							left: "50%",
							filter: `url(#${glowId})`,
						}}
					/>

					{/* 核心点位 */}
					<div
						className="relative z-10 rounded-full border-3 border-white shadow-2xl cursor-pointer hover:scale-110 transition-all duration-300 flex items-center justify-center"
						style={{
							width: "36px",
							height: "36px",
							background: `radial-gradient(circle, ${config.primaryColor} 0%, ${config.secondaryColor} 70%, ${config.primaryColor} 100%)`,
							filter: `url(#${glowId})`,
							transform: isSelected ? "scale(1.3)" : "scale(1)",
							boxShadow: `0 0 20px ${config.primaryColor}80, 0 0 40px ${config.secondaryColor}60`,
						}}
					>
						<MapPin className="w-5 h-5 text-white drop-shadow-lg" />
					</div>
				</>
			)}

			{/* CSS动画样式注入 */}
			<style jsx>{`
        @keyframes gradient-pulse-massive {
          0%, 100% {
            transform: translate(-50%, -50%) scale(0.6) rotate(0deg);
            opacity: 0.2;
          }
          25% {
            transform: translate(-50%, -50%) scale(1.0) rotate(90deg);
            opacity: 0.4;
          }
          50% {
            transform: translate(-50%, -50%) scale(1.3) rotate(180deg);
            opacity: 0.1;
          }
          75% {
            transform: translate(-50%, -50%) scale(1.1) rotate(270deg);
            opacity: 0.3;
          }
        }
        
        @keyframes gradient-pulse-outer {
          0%, 100% {
            transform: translate(-50%, -50%) scale(0.7);
            opacity: 0.6;
          }
          33% {
            transform: translate(-50%, -50%) scale(1.1);
            opacity: 0.8;
          }
          66% {
            transform: translate(-50%, -50%) scale(1.3);
            opacity: 0.3;
          }
        }
        
        @keyframes gradient-pulse-middle {
          0%, 100% {
            transform: translate(-50%, -50%) scale(0.8);
            opacity: 0.7;
          }
          50% {
            transform: translate(-50%, -50%) scale(1.2);
            opacity: 0.4;
          }
        }
        
        @keyframes gradient-pulse-inner {
          0%, 100% {
            transform: translate(-50%, -50%) scale(0.9);
            opacity: 0.9;
          }
          50% {
            transform: translate(-50%, -50%) scale(1.1);
            opacity: 0.6;
          }
        }
        

      `}</style>
		</div>
	);
}
