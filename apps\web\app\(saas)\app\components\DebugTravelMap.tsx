"use client";
import { GoogleMap, useJsApiLoader } from "@react-google-maps/api";
import type { Footprint } from "@repo/api/src/routes/diaries/schemas";
import { cn } from "@ui/lib";
import { Loader2 } from "lucide-react";
import { useEffect, useState } from "react";

// 使用Google Maps测试API密钥（仅用于开发环境）
const DEV_API_KEY = "AIzaSyB41DRUbKWJHPxaFjMAwdrzWzbVKartNGg";
const GOOGLE_MAPS_API_KEY =
	process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || DEV_API_KEY;

interface DebugTravelMapProps {
	footprints: Footprint[];
	className?: string;
	height?: string | number;
}

/**
 * 简化版地图组件 - 用于调试和排除地图加载问题
 */
export default function DebugTravelMap({
	footprints = [],
	className = "",
	height = "500px",
}: DebugTravelMapProps) {
	// 状态管理
	const [mapInstance, setMapInstance] = useState<google.maps.Map | null>(
		null,
	);
	const [markers, setMarkers] = useState<google.maps.Marker[]>([]);
	const [loadingStage, setLoadingStage] = useState<string>("初始化组件");
	const [error, setError] = useState<string | null>(null);

	// 加载Google Maps API
	const { isLoaded, loadError } = useJsApiLoader({
		googleMapsApiKey: GOOGLE_MAPS_API_KEY,
		libraries: ["places"] as any,
	});

	// 容器样式
	const containerStyle = {
		height: typeof height === "number" ? `${height}px` : height,
		width: "100%",
	};

	// 默认地图配置
	const center = { lat: 20, lng: 0 };
	const zoom = 2;

	// 地图加载完成回调
	const handleMapLoad = (map: google.maps.Map) => {
		console.log("🌍 [DebugMap] 地图实例已加载");
		setMapInstance(map);
		setLoadingStage("地图已加载");
	};

	// 创建标记 - 在地图加载完成后
	useEffect(() => {
		// 如果没有地图实例或没有足迹数据，不进行处理
		if (!mapInstance || footprints.length === 0) {
			return;
		}

		setLoadingStage("添加标记中...");
		console.log(`🌍 [DebugMap] 处理${footprints.length}个足迹点`);

		try {
			// 清除现有标记
			markers.forEach((marker) => marker.setMap(null));

			// 创建边界对象，用于自动调整视图
			const bounds = new google.maps.LatLngBounds();
			const newMarkers: google.maps.Marker[] = [];

			// 为每个足迹点创建标记
			footprints.forEach((fp) => {
				const position = { lat: fp.latitude, lng: fp.longitude };
				bounds.extend(position);

				const marker = new google.maps.Marker({
					position,
					map: mapInstance,
					title: fp.location,
					animation: google.maps.Animation.DROP,
				});

				newMarkers.push(marker);
			});

			// 保存标记引用
			setMarkers(newMarkers);

			// 自动调整地图视图以显示所有标记
			if (footprints.length > 0) {
				mapInstance.fitBounds(bounds);

				// 避免过度缩放
				google.maps.event.addListenerOnce(mapInstance, "idle", () => {
					const currentZoom = mapInstance.getZoom();
					if (currentZoom !== undefined && currentZoom > 15) {
						mapInstance.setZoom(15);
					}
				});
			}

			setLoadingStage("标记添加完成");
			console.log(`🌍 [DebugMap] 已添加${newMarkers.length}个标记`);
		} catch (err) {
			console.error("🌍 [DebugMap] 添加标记错误:", err);
			setError(
				`添加标记失败: ${err instanceof Error ? err.message : String(err)}`,
			);
		}
	}, [mapInstance, footprints]);

	// 加载错误
	if (loadError) {
		return (
			<div
				style={containerStyle}
				className={cn(
					"flex items-center justify-center bg-destructive/10 border border-destructive/50 text-destructive p-4 rounded-md",
					className,
				)}
			>
				Google Maps API加载失败: {loadError.message}
			</div>
		);
	}

	// 自定义错误
	if (error) {
		return (
			<div
				style={containerStyle}
				className={cn(
					"flex items-center justify-center bg-destructive/10 border border-destructive/50 text-destructive p-4 rounded-md",
					className,
				)}
			>
				{error}
			</div>
		);
	}

	// API仍在加载中
	if (!isLoaded) {
		return (
			<div
				style={containerStyle}
				className={cn(
					"flex flex-col items-center justify-center bg-muted rounded-md",
					className,
				)}
			>
				<Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
				<span className="mt-2 text-sm text-muted-foreground">
					正在加载Google Maps API...
				</span>
			</div>
		);
	}

	// 渲染地图
	return (
		<div
			className={cn("relative rounded-md overflow-hidden", className)}
			style={containerStyle}
		>
			<GoogleMap
				mapContainerStyle={{ width: "100%", height: "100%" }}
				center={center}
				zoom={zoom}
				onLoad={handleMapLoad}
				options={{
					gestureHandling: "greedy",
					zoomControl: true,
					fullscreenControl: true,
					mapTypeControl: false,
					streetViewControl: false,
				}}
			/>

			{/* 调试信息 */}
			{process.env.NODE_ENV === "development" && (
				<div className="absolute bottom-0 left-0 bg-black/70 text-white text-xs p-1 z-10">
					状态: {loadingStage} | 足迹: {footprints.length} | 标记:{" "}
					{markers.length}
				</div>
			)}
		</div>
	);
}
