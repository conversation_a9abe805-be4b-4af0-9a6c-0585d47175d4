import { MapPin } from "lucide-react";
import type { GeocodingResult, TravelPoint } from "./types";

interface LocationPreviewProps {
	geocodingResults: GeocodingResult;
	formData: Partial<TravelPoint>;
}

export function LocationPreview({
	geocodingResults,
	formData,
}: LocationPreviewProps) {
	return (
		<div className="bg-muted/30 p-4 rounded-lg border border-border">
			<h3 className="font-medium mb-2">位置确认</h3>
			<div className="flex flex-col md:flex-row gap-4">
				<div className="min-w-[180px] h-[120px] bg-muted rounded-md overflow-hidden relative flex-shrink-0">
					{geocodingResults.placeDetails?.photos?.[0] ? (
						<img
							src={geocodingResults.placeDetails.photos[0].getUrl(
								{
									maxWidth: 400,
									maxHeight: 300,
								},
							)}
							alt={formData.location || "位置图片"}
							className="w-full h-full object-cover"
							onError={() => console.error("位置图片加载失败")}
						/>
					) : (
						<div className="w-full h-full flex items-center justify-center bg-muted">
							<MapPin className="w-10 h-10 text-muted-foreground/50" />
						</div>
					)}
					<div className="absolute bottom-0 left-0 right-0 bg-black/50 px-2 py-1">
						<p className="text-xs text-white truncate">
							{geocodingResults.lat.toFixed(6)},{" "}
							{geocodingResults.lng.toFixed(6)}
						</p>
					</div>
				</div>

				<div className="flex-1">
					<p className="font-medium">
						{geocodingResults.placeDetails?.name ||
							formData.location}
					</p>
					{geocodingResults.formattedAddress && (
						<p className="text-sm text-muted-foreground mt-1">
							{geocodingResults.formattedAddress}
						</p>
					)}
					{geocodingResults.placeDetails?.vicinity && (
						<p className="text-sm text-muted-foreground mt-1">
							{geocodingResults.placeDetails.vicinity}
						</p>
					)}
					{geocodingResults.placeDetails?.types && (
						<div className="flex flex-wrap gap-1 mt-2">
							{geocodingResults.placeDetails.types
								.slice(0, 3)
								.map((type, index) => (
									<span
										key={index}
										className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full"
									>
										{type.replace(/_/g, " ")}
									</span>
								))}
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
