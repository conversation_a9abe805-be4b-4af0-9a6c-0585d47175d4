/**
 * 地理编码提供商基础抽象类
 */

import { logger } from "@repo/logs";
import type {
	GeocodingConfig,
	GeocodingOptions,
	GeocodingProvider,
	ProviderResponse,
	UnifiedGeocodingResult,
} from "./types";

/**
 * 地理编码提供商基础抽象类
 */
export abstract class BaseGeocodingProvider {
	protected config: GeocodingConfig;
	protected stats = {
		requests: 0,
		successes: 0,
		failures: 0,
		totalResponseTime: 0,
		lastUsed: new Date(),
		consecutiveFailures: 0, // 连续失败次数
		lastFailureTime: null as Date | null, // 最后失败时间
	};

	constructor(config: GeocodingConfig) {
		this.config = config;
	}

	/**
	 * 获取提供商类型
	 */
	abstract getProviderType(): GeocodingProvider;

	/**
	 * 正向地理编码（地址转坐标）
	 */
	async geocode(
		address: string,
		options: GeocodingOptions = {},
	): Promise<UnifiedGeocodingResult | null> {
		const startTime = Date.now();
		this.stats.requests++;
		this.stats.lastUsed = new Date();

		try {
			logger.info("开始地理编码", {
				provider: this.getProviderType(),
				address,
				options,
			});

			const result = await this.performGeocode(address, options);
			const responseTime = Date.now() - startTime;

			if (result) {
				result.responseTime = responseTime;
				result.provider = this.getProviderType();
				this.stats.successes++;
				this.stats.consecutiveFailures = 0; // 重置连续失败计数
				this.stats.totalResponseTime += responseTime;

				logger.info("地理编码成功", {
					provider: this.getProviderType(),
					address,
					responseTime,
					result: {
						longitude: result.longitude,
						latitude: result.latitude,
						formattedAddress: result.formattedAddress,
					},
				});

				return result;
			}

			this.stats.failures++;
			this.stats.consecutiveFailures++;
			this.stats.lastFailureTime = new Date();

			logger.warn("地理编码无结果", {
				provider: this.getProviderType(),
				address,
				responseTime,
			});

			return null;
		} catch (error) {
			const responseTime = Date.now() - startTime;
			this.stats.failures++;
			this.stats.consecutiveFailures++;
			this.stats.lastFailureTime = new Date();

			logger.error("地理编码失败", {
				provider: this.getProviderType(),
				address,
				error: error instanceof Error ? error.message : String(error),
				responseTime,
			});

			return null;
		}
	}

	/**
	 * 反向地理编码（坐标转地址）
	 */
	async reverseGeocode(
		longitude: number,
		latitude: number,
		options: GeocodingOptions = {},
	): Promise<UnifiedGeocodingResult | null> {
		const startTime = Date.now();
		this.stats.requests++;
		this.stats.lastUsed = new Date();

		try {
			logger.info("开始反向地理编码", {
				provider: this.getProviderType(),
				longitude,
				latitude,
				options,
			});

			const result = await this.performReverseGeocode(
				longitude,
				latitude,
				options,
			);
			const responseTime = Date.now() - startTime;

			if (result) {
				result.responseTime = responseTime;
				result.provider = this.getProviderType();
				this.stats.successes++;
				this.stats.totalResponseTime += responseTime;

				logger.info("反向地理编码成功", {
					provider: this.getProviderType(),
					longitude,
					latitude,
					responseTime,
					result: {
						formattedAddress: result.formattedAddress,
					},
				});

				return result;
			}

			this.stats.failures++;
			logger.warn("反向地理编码无结果", {
				provider: this.getProviderType(),
				longitude,
				latitude,
				responseTime,
			});

			return null;
		} catch (error) {
			const responseTime = Date.now() - startTime;
			this.stats.failures++;

			logger.error("反向地理编码失败", {
				provider: this.getProviderType(),
				longitude,
				latitude,
				error: error instanceof Error ? error.message : String(error),
				responseTime,
			});

			return null;
		}
	}

	/**
	 * 获取提供商统计信息
	 */
	getStats() {
		return {
			...this.stats,
			averageResponseTime:
				this.stats.successes > 0
					? this.stats.totalResponseTime / this.stats.successes
					: 0,
			successRate:
				this.stats.requests > 0
					? this.stats.successes / this.stats.requests
					: 0,
		};
	}

	/**
	 * 重置统计信息
	 */
	resetStats() {
		this.stats = {
			requests: 0,
			successes: 0,
			failures: 0,
			totalResponseTime: 0,
			lastUsed: new Date(),
			consecutiveFailures: 0,
			lastFailureTime: null,
		};
	}

	/**
	 * 检查提供商是否可用
	 */
	isAvailable(): boolean {
		// 基础可用性检查
		const basicAvailable =
			this.config.enabled !== false &&
			!!this.config.apiKey &&
			(!this.config.dailyQuota ||
				(this.config.usedQuota || 0) < this.config.dailyQuota);

		if (!basicAvailable) return false;

		// 动态禁用检查：连续失败超过5次且最近失败在5分钟内
		if (this.stats.consecutiveFailures >= 5 && this.stats.lastFailureTime) {
			const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
			if (this.stats.lastFailureTime > fiveMinutesAgo) {
				logger.warn("提供商因连续失败被暂时禁用", {
					provider: this.getProviderType(),
					consecutiveFailures: this.stats.consecutiveFailures,
					lastFailureTime: this.stats.lastFailureTime,
				});
				return false;
			}
		}

		return true;
	}

	/**
	 * 获取提供商权重
	 */
	getWeight(): number {
		if (!this.isAvailable()) return 0;

		const baseWeight = this.config.weight || 1;
		const successRate = this.getStats().successRate;

		// 根据成功率调整权重
		return baseWeight * (successRate > 0 ? successRate : 1);
	}

	/**
	 * 获取提供商优先级
	 */
	getPriority(): number {
		return this.config.priority || 999;
	}

	/**
	 * 更新配额使用情况
	 */
	updateQuotaUsage(increment = 1) {
		this.config.usedQuota = (this.config.usedQuota || 0) + increment;
	}

	/**
	 * 执行具体的地理编码（由子类实现）
	 */
	protected abstract performGeocode(
		address: string,
		options: GeocodingOptions,
	): Promise<UnifiedGeocodingResult | null>;

	/**
	 * 执行具体的反向地理编码（由子类实现）
	 */
	protected abstract performReverseGeocode(
		longitude: number,
		latitude: number,
		options: GeocodingOptions,
	): Promise<UnifiedGeocodingResult | null>;

	/**
	 * 发送HTTP请求的通用方法
	 */
	protected async makeRequest(
		url: string,
		options: RequestInit = {},
	): Promise<ProviderResponse> {
		const startTime = Date.now();

		try {
			// 智能超时设置：根据提供商类型调整
			const providerType = this.getProviderType();
			const defaultTimeout = this.getProviderTimeout(providerType);
			const timeout = (options as any).timeout || defaultTimeout;

			const controller = new AbortController();
			const timeoutId = setTimeout(() => controller.abort(), timeout);

			const response = await fetch(url, {
				...options,
				signal: controller.signal,
			});

			clearTimeout(timeoutId);

			if (!response.ok) {
				throw new Error(
					`HTTP ${response.status}: ${response.statusText}`,
				);
			}

			const data = await response.json();
			const responseTime = Date.now() - startTime;

			return {
				provider: this.getProviderType(),
				rawData: data,
				success: true,
				responseTime,
			};
		} catch (error) {
			const responseTime = Date.now() - startTime;

			return {
				provider: this.getProviderType(),
				rawData: null,
				success: false,
				error: error instanceof Error ? error.message : String(error),
				responseTime,
			};
		}
	}

	/**
	 * 根据提供商类型获取合适的超时时间
	 */
	private getProviderTimeout(providerType: string): number {
		const timeouts = {
			amap: 3000, // 高德地图：3秒
			baidu: 3000, // 百度地图：3秒
			google: 5000, // Google Maps：5秒（可能有网络问题）
			bing: 4000, // Bing Maps：4秒
			mapbox: 4000, // Mapbox：4秒
		};

		return timeouts[providerType as keyof typeof timeouts] || 5000;
	}

	/**
	 * 延迟函数
	 */
	protected delay(ms: number): Promise<void> {
		return new Promise((resolve) => setTimeout(resolve, ms));
	}

	/**
	 * 验证坐标是否有效
	 */
	protected isValidCoordinates(longitude: number, latitude: number): boolean {
		return (
			typeof longitude === "number" &&
			typeof latitude === "number" &&
			longitude >= -180 &&
			longitude <= 180 &&
			latitude >= -90 &&
			latitude <= 90 &&
			!Number.isNaN(longitude) &&
			!Number.isNaN(latitude)
		);
	}
}
