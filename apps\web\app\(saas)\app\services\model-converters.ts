import type {
	FrontendTravelDiary,
	FrontendTravelPoint,
	FrontendTravelTimeline,
	TravelDiary,
	TravelDiaryContent,
	TravelPointIconType,
	TravelPointImage,
	TravelTimeline,
} from "@repo/database/src/types/travel-diary";

/**
 * 转换图片数据，确保所有图片都是对象格式
 */
function convertImagesToObjectFormat(images: any[]): TravelPointImage[] {
	if (!images || !Array.isArray(images)) {
		return [];
	}

	return images
		.map((image, index) => {
			// 如果已经是对象格式，直接返回
			if (typeof image === "object" && image !== null && "url" in image) {
				return {
					url: image.url,
					description: image.description || undefined,
					alt: image.alt || undefined,
					caption: image.caption || undefined,
					uploadedAt: image.uploadedAt || new Date().toISOString(),
				} as TravelPointImage;
			}

			// 如果是字符串格式，转换为对象格式
			if (typeof image === "string") {
				return {
					url: image,
					description: undefined,
					alt: undefined,
					caption: undefined,
					uploadedAt: new Date().toISOString(),
				} as TravelPointImage;
			}

			// 其他情况，记录警告并跳过
			console.warn(`无效的图片数据格式，索引 ${index}:`, image);
			return null;
		})
		.filter((image): image is TravelPointImage => image !== null);
}

/**
 * 将前端日记模型转换为数据库存储格式
 */
export function toDatabaseModel(frontendDiary: FrontendTravelDiary): {
	content: TravelDiaryContent;
	[key: string]: any;
} {
	const { timelines, ...rest } = frontendDiary;

	// 转换时间线数据，处理日期格式
	const convertedTimelines: TravelTimeline[] =
		timelines?.map((timeline) => ({
			id: timeline.id,
			title: timeline.title,
			// 将Date对象转换为ISO字符串
			date:
				timeline.date instanceof Date
					? timeline.date.toISOString().split("T")[0]
					: String(timeline.date),
			// 递归处理点位数据
			points:
				timeline.points?.map((point) => ({
					id: point.id,
					date:
						point.timestamp instanceof Date
							? point.timestamp.toISOString().split("T")[0]
							: String(point.timestamp),
					location: point.title || "", // 前端使用 title，后端使用 location
					description: point.description || "",
					latitude: point.latitude,
					longitude: point.longitude,
					iconType: point.iconType as TravelPointIconType,
					images: convertImagesToObjectFormat(point.images || []),
					order: 0, // 默认值
					country: undefined, // 可选字段
					city: undefined, // 可选字段
				})) || [],
		})) || [];

	return {
		...rest,
		content: {
			timelines: convertedTimelines,
		},
	};
}

/**
 * 将数据库日记模型转换为前端使用格式
 */
export function toFrontendModel(dbDiary: TravelDiary): FrontendTravelDiary {
	const { content, ...rest } = dbDiary;
	const typedContent = content as unknown as TravelDiaryContent;

	// 转换时间线数据，处理日期格式
	const convertedTimelines: FrontendTravelTimeline[] =
		typedContent.timelines?.map((timeline) => {
			// 转换时间线中的点位
			const points: FrontendTravelPoint[] =
				timeline.points?.map((point) => ({
					id: point.id,
					timelineId: timeline.id,
					title: point.location, // 后端使用 location，前端使用 title
					description: point.description || null,
					latitude: point.latitude,
					longitude: point.longitude,
					// 添加coordinates属性，与经纬度值对应
					coordinates: {
						lat: point.latitude,
						lng: point.longitude,
					},
					iconType: point.iconType as TravelPointIconType,
					timestamp:
						typeof point.date === "string"
							? new Date(point.date)
							: point.date,
					images: convertImagesToObjectFormat(point.images || []),
					address: null, // 默认值
				})) || [];

			return {
				id: timeline.id,
				title: timeline.title,
				date:
					typeof timeline.date === "string"
						? new Date(timeline.date)
						: timeline.date,
				points,
			};
		}) || [];

	// 安全地访问richTextDraftContent
	// 在TypeScript类型系统中，我们无法直接访问不在类型定义中的属性
	// 所以使用类型断言处理可能的附加字段
	const diary = dbDiary as unknown as { richTextDraftContent?: any };

	return {
		id: dbDiary.id,
		title: dbDiary.title,
		subtitle: dbDiary.subtitle,
		coverImage: dbDiary.coverImage,
		timelines: convertedTimelines,
		isPublic: dbDiary.isPublic,
		richTextDraftContent: diary.richTextDraftContent || null,
	};
}
