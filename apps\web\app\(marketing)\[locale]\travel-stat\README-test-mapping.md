# 🧪 国家名称映射测试脚本使用说明

## 概述

`test-mapping.js` 是一个全面的测试脚本，用于验证我们的国家名称映射表 (`countryNameMapping.ts`) 是否能够正确处理 Mapbox Geocoding API 返回的国家名称。

## 功能特性

✅ **全面覆盖**: 测试世界主要国家（100+ 个国家）  
✅ **实时验证**: 调用真实的 Mapbox Geocoding API  
✅ **详细报告**: 生成完整的测试报告和建议  
✅ **自动化**: 支持 CI/CD 集成  
✅ **智能分析**: 识别缺失映射和不匹配情况  

## 使用方法

### 1. 前置条件

确保您有有效的 Mapbox 访问令牌：

```bash
# 在 .env.local 文件中设置
NEXT_PUBLIC_MAPBOX_TOKEN=pk.eyJ1IjoieW91ci11c2VybmFtZSIsImEiOiJjbGV5dWZsOW4wbnR5M3lvMGc5b3l6eTh2In0.example
```

### 2. 运行测试

```bash
# 基本运行
node apps/web/app/\(marketing\)/\[locale\]/travel-stat/test-mapping.js

# 或使用 npm script（如果已配置）
npm run test:country-mapping
```

### 3. 测试过程

脚本将：
1. 📡 向 Mapbox API 查询每个国家
2. 🔍 提取返回的国家名称
3. ✅ 检查是否能在映射表中找到对应映射
4. 📊 生成详细的测试报告

## 输出示例

```
🧪 开始测试国家名称映射覆盖率...

📍 将测试 100 个世界主要国家

[1/100] 正在测试: China (CN)
  📍 API 返回: "China"
  ✅ 映射正确: "China" -> "China"

[2/100] 正在测试: United States (US)
  📍 API 返回: "United States"
  ✅ 映射正确: "United States" -> "United States of America"

[3/100] 正在测试: Some Country (XX)
  📍 API 返回: "Some Country Name"
  ❌ 缺少映射: "Some Country Name" -> ?

...

================================================================================
📊 测试结果汇总
================================================================================
总测试国家数: 100
✅ 成功映射: 95 (95.0%)
❌ API 错误: 2 (2.0%)
⚠️  缺少映射: 3 (3.0%)
🔄 映射不匹配: 0 (0.0%)

==================================================
❌ 缺少映射的国家 (需要添加到映射表):
==================================================
1. "Some API Country Name" (来自 Expected Country)
   建议添加映射: "Some API Country Name": "Expected Country",

==================================================
📈 覆盖率评估:
==================================================
有效测试覆盖率: 97.0% (排除API错误)
🎉 优秀! 映射覆盖率非常高

🎯 建议下一步:
1. 将上述缺失的映射添加到 countryNameMapping.ts
3. 定期运行此测试以确保映射表的完整性

✨ 测试完成!
```

## 测试国家列表

脚本测试以下地区的主要国家：

### 🌏 亚洲 (25个)
- 中国、日本、韩国、印度、印度尼西亚、泰国、越南、马来西亚、新加坡等

### 🇪🇺 欧洲 (31个)  
- 英国、法国、德国、意大利、西班牙、俄罗斯、荷兰、瑞士、北欧国家等

### 🌎 美洲 (15个)
- 美国、加拿大、墨西哥、巴西、阿根廷、智利等南北美洲国家

### 🌍 非洲 (18个)
- 埃及、南非、尼日利亚、肯尼亚、摩洛哥等非洲主要国家

### 🌊 大洋洲 (8个)
- 澳大利亚、新西兰、斐济、巴布亚新几内亚等

## 错误排查

### 常见问题

**❌ "请设置有效的 MAPBOX_TOKEN"**
```bash
# 解决方案：检查环境变量
echo $NEXT_PUBLIC_MAPBOX_TOKEN
```

**❌ "HTTP 401: Unauthorized"**
- 检查 Mapbox token 是否有效
- 确认 token 有 Geocoding API 权限

**❌ "HTTP 429: Too Many Requests"**
- API 配额已用完
- 脚本已内置 100ms 延迟，正常情况下不会触发

**❌ "No results found"** 
- 某些国家名称可能无法通过 Geocoding API 查找到
- 这是正常现象，会在报告中标记为 API 错误

## 自定义配置

### 修改测试国家列表

编辑 `WORLD_COUNTRIES` 数组：

```javascript
const WORLD_COUNTRIES = [
  { code: "CN", name: "China", capital: "Beijing" },
  { code: "YOUR_CODE", name: "Your Country", capital: "Your Capital" },
  // ... 更多国家
];
```

### 调整 API 延迟

修改延迟时间以适应不同的 API 限制：

```javascript
// 在循环中的延迟设置
await delay(200); // 增加到 200ms
```

### 自定义验证逻辑

修改 `checkMapping` 函数以实现自定义的映射验证逻辑。

## 集成到 CI/CD

可以将此测试集成到持续集成流程中：

```yaml
# GitHub Actions 示例
- name: Test Country Name Mappings
  run: node apps/web/app/\(marketing\)/\[locale\]/travel-stat/test-mapping.js
  env:
    NEXT_PUBLIC_MAPBOX_TOKEN: ${{ secrets.MAPBOX_TOKEN }}
```

## 维护建议

1. **定期运行**: 建议每月运行一次，确保映射表保持最新
2. **版本控制**: 将测试结果保存为文档，跟踪映射覆盖率的变化
3. **扩展测试**: 根据用户反馈添加更多国家到测试列表
4. **性能监控**: 监控测试执行时间，如果过长可能需要优化

## 贡献指南

如果发现缺失的国家映射：

1. 运行测试脚本识别缺失的映射
2. 根据测试报告的建议添加映射到 `countryNameMapping.ts`
3. 重新运行测试验证修复效果
4. 提交 PR 时包含测试结果

---

*此测试脚本是国家名称映射系统的重要组成部分，确保了旅行足迹工具的准确性和可靠性。* 