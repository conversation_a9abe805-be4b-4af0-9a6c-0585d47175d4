"use client";

import { Badge } from "@ui/components/badge";
import { Card } from "@ui/components/card";
import { CheckCircle, Map as MapIcon } from "lucide-react";
import {
	MAP_STYLE_CONFIGS,
	type MapStyleType,
	STYLE_CATEGORIES,
} from "./MapStyleSwitcher";

interface MapStyleDemoProps {
	style: MapStyleType;
	currentAtmosphere?: string;
	currentAnimation?: string;
	className?: string;
}

export function MapStyleDemo({
	style,
	currentAtmosphere,
	currentAnimation,
	className = "",
}: MapStyleDemoProps) {
	const config = MAP_STYLE_CONFIGS[style];
	const categoryInfo = STYLE_CATEGORIES[config.category];
	const CategoryIcon = categoryInfo.icon;

	// 检查当前搭配是否推荐
	const isAtmosphereRecommended = currentAtmosphere
		? config.bestAtmosphere.includes(currentAtmosphere)
		: false;
	const isAnimationRecommended = currentAnimation
		? config.bestAnimation.includes(currentAnimation)
		: false;

	return (
		<Card
			className={`p-4 bg-white/80 backdrop-blur-sm border-sky-200 ${className}`}
		>
			<div className="flex items-center gap-2 mb-3">
				<MapIcon className="w-4 h-4 text-sky-500" />
				<span className="text-sm font-medium text-gray-800">
					当前地图样式
				</span>
				<Badge className="text-xs">{config.name}</Badge>
			</div>

			{/* 样式信息 */}
			<div className="mb-3">
				<div className="flex items-center gap-2 mb-2">
					<CategoryIcon className="w-4 h-4 text-gray-600" />
					<span className="text-sm text-gray-700">
						{categoryInfo.name}
					</span>
				</div>
				<p className="text-xs text-gray-600">{config.description}</p>
			</div>

			{/* 搭配状态 */}
			<div className="space-y-2 text-xs">
				{/* 大气层搭配 */}
				<div className="flex justify-between items-center">
					<span className="text-gray-600">大气层搭配</span>
					<div className="flex items-center gap-1">
						{isAtmosphereRecommended && (
							<CheckCircle className="w-3 h-3 text-green-500" />
						)}
						<span
							className={`text-xs ${
								isAtmosphereRecommended
									? "text-green-600"
									: "text-gray-500"
							}`}
						>
							{isAtmosphereRecommended ? "推荐" : "一般"}
						</span>
					</div>
				</div>

				{/* 动画搭配 */}
				<div className="flex justify-between items-center">
					<span className="text-gray-600">动画搭配</span>
					<div className="flex items-center gap-1">
						{isAnimationRecommended && (
							<CheckCircle className="w-3 h-3 text-green-500" />
						)}
						<span
							className={`text-xs ${
								isAnimationRecommended
									? "text-green-600"
									: "text-gray-500"
							}`}
						>
							{isAnimationRecommended ? "推荐" : "一般"}
						</span>
					</div>
				</div>
			</div>

			{/* 推荐的大气层 */}
			{config.bestAtmosphere.length > 0 && (
				<div className="mt-3">
					<h5 className="text-xs font-medium text-gray-700 mb-1">
						推荐大气层
					</h5>
					<div className="flex flex-wrap gap-1">
						{config.bestAtmosphere.map((atmosphere) => (
							<span
								key={atmosphere}
								className={`text-xs px-2 py-1 rounded ${
									currentAtmosphere === atmosphere
										? "bg-green-100 text-green-700 border border-green-300"
										: "bg-gray-100 text-gray-600"
								}`}
							>
								{atmosphere}
							</span>
						))}
					</div>
				</div>
			)}

			{/* 推荐的动画 */}
			{config.bestAnimation.length > 0 && (
				<div className="mt-3">
					<h5 className="text-xs font-medium text-gray-700 mb-1">
						推荐动画
					</h5>
					<div className="flex flex-wrap gap-1">
						{config.bestAnimation.map((animation) => (
							<span
								key={animation}
								className={`text-xs px-2 py-1 rounded ${
									currentAnimation === animation
										? "bg-blue-100 text-blue-700 border border-blue-300"
										: "bg-gray-100 text-gray-600"
								}`}
							>
								{animation}
							</span>
						))}
					</div>
				</div>
			)}

			{/* 综合搭配评分 */}
			<div className="mt-3 p-2 bg-gradient-to-r from-gray-50 to-blue-50 rounded text-xs">
				<div className="flex justify-between items-center">
					<span className="text-gray-700">搭配评分</span>
					<div className="flex items-center gap-1">
						{Array.from({ length: 5 }, (_, i) => {
							const score =
								(isAtmosphereRecommended ? 2 : 0) +
								(isAnimationRecommended ? 2 : 0) +
								1;
							return (
								<div
									key={i}
									className={`w-2 h-2 rounded-full ${
										i < score
											? "bg-yellow-400"
											: "bg-gray-300"
									}`}
								/>
							);
						})}
						<span className="ml-1 text-gray-700">
							{isAtmosphereRecommended && isAnimationRecommended
								? "完美"
								: isAtmosphereRecommended ||
										isAnimationRecommended
									? "良好"
									: "一般"}
						</span>
					</div>
				</div>
			</div>
		</Card>
	);
}
