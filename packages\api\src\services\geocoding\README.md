# 统一地理编码服务

一个高度模块化、支持多提供商的地理编码服务，内置负载均衡、故障转移和缓存机制。

## 🌟 特性

- **多提供商支持**: Google Maps, Bing Maps, Mapbox, 百度地图, 高德地图
- **智能负载均衡**: 6种负载均衡策略（随机、轮询、加权、优先级、性能、智能）
- **故障转移**: 自动切换到可用的提供商
- **缓存机制**: 内置缓存减少API调用
- **统计监控**: 详细的性能和使用统计
- **中文优化**: 针对中文地址的特殊优化
- **类型安全**: 完整的TypeScript类型定义

## 🚀 快速开始

### 基础使用

```typescript
import { createGeocodingService } from './services/geocoding-service';

// 使用环境变量自动配置
const geocodingService = createGeocodingService();

// 地理编码
const result = await geocodingService.geocode("北京天安门广场");
console.log(result);
// {
//   longitude: 116.397128,
//   latitude: 39.903738,
//   address: "中国北京市东城区天安门广场",
//   confidence: "high",
//   provider: "amap"
// }
```

### 环境变量配置

在 `.env.local` 文件中配置API密钥：

```bash
# Google Maps (推荐 - 全球覆盖最佳)
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# 高德地图 (推荐 - 中国地址最准确)
AMAP_API_KEY=your_amap_api_key

# 百度地图 (备选 - 中国地址)
BAIDU_MAPS_API_KEY=your_baidu_maps_api_key

# Bing Maps (备选 - 全球覆盖)
BING_MAPS_API_KEY=your_bing_maps_api_key

# Mapbox (备选 - 全球覆盖)
MAPBOX_API_KEY=your_mapbox_api_key
```

## 📖 详细使用

### 1. 自定义配置

```typescript
import { 
  UnifiedGeocodingService, 
  type GeocodingServiceConfig 
} from './services/geocoding';

const config: GeocodingServiceConfig = {
  providers: [
    {
      provider: "amap",
      apiKey: "your_amap_key",
      weight: 3,
      priority: 1,
      enabled: true,
      dailyQuota: 10000,
    },
    {
      provider: "google",
      apiKey: "your_google_key",
      weight: 2,
      priority: 2,
      enabled: true,
    }
  ],
  strategy: "smart",
  enableCache: true,
  cacheTTL: 3600, // 1小时
  defaultOptions: {
    language: "zh-CN",
    region: "CN",
  }
};

const service = new UnifiedGeocodingService(config);
```

### 2. 中国优化配置

```typescript
import { createChinaOptimizedConfig, UnifiedGeocodingService } from './services/geocoding';

const config = createChinaOptimizedConfig({
  amap: "your_amap_key",
  baidu: "your_baidu_key",
  google: "your_google_key", // 备选
});

const service = new UnifiedGeocodingService(config);
```

### 3. 智能地理编码

```typescript
// 智能地理编码会尝试多种策略
const result = await service.smartGeocode("北京市朝阳区三里屯", {
  language: "zh-CN",
  region: "CN"
});
```

### 4. 批量地理编码

```typescript
const addresses = [
  "北京市天安门广场",
  "上海市外滩",
  "广州市珠江新城"
];

const results = await service.batchGeocode(addresses);
console.log(results);
// [
//   { address: "北京市天安门广场", result: {...}, provider: "amap" },
//   { address: "上海市外滩", result: {...}, provider: "amap" },
//   { address: "广州市珠江新城", result: null, error: "..." }
// ]
```

### 5. 反向地理编码

```typescript
const result = await service.reverseGeocode(116.397128, 39.903738);
console.log(result.formattedAddress); // "中国北京市东城区天安门广场"
```

## ⚖️ 负载均衡策略

### 1. 智能策略 (推荐)

```typescript
service.setStrategy("smart");
```

智能策略综合考虑：
- 成功率 (40%)
- 响应时间 (20%)
- 权重配置 (20%)
- 优先级 (20%)
- 地址特征匹配（中文地址优先国内提供商）

### 2. 其他策略

```typescript
// 随机选择
service.setStrategy("random");

// 轮询
service.setStrategy("round_robin");

// 加权轮询
service.setStrategy("weighted");

// 优先级
service.setStrategy("priority");

// 性能优先
service.setStrategy("performance");
```

## 📊 监控和统计

### 获取统计信息

```typescript
const stats = service.getStats();
console.log(stats);
// {
//   totalRequests: 150,
//   successfulRequests: 142,
//   failedRequests: 8,
//   averageResponseTime: 245,
//   providerStats: {
//     amap: { requests: 80, successes: 78, failures: 2, ... },
//     google: { requests: 70, successes: 64, failures: 6, ... }
//   }
// }
```

### 重置统计

```typescript
service.resetStats();
```

### 清除缓存

```typescript
service.clearCache();
```

## 🌐 API 路由

服务提供了完整的HTTP API接口：

### 地理编码

```bash
POST /api/geocoding/geocode
Content-Type: application/json

{
  "address": "北京天安门广场",
  "language": "zh-CN",
  "region": "CN"
}
```

### 反向地理编码

```bash
POST /api/geocoding/reverse-geocode
Content-Type: application/json

{
  "longitude": 116.397128,
  "latitude": 39.903738,
  "language": "zh-CN"
}
```

### 批量地理编码

```bash
POST /api/geocoding/batch-geocode
Content-Type: application/json

{
  "addresses": ["北京市", "上海市", "广州市"],
  "language": "zh-CN"
}
```

### 智能地理编码

```bash
POST /api/geocoding/smart-geocode
Content-Type: application/json

{
  "address": "北京市朝阳区三里屯",
  "language": "zh-CN",
  "region": "CN"
}
```

### 获取统计信息

```bash
GET /api/geocoding/stats
```

### 健康检查

```bash
GET /api/geocoding/health
```

## 🧪 测试

运行测试脚本验证服务功能：

```bash
# 测试统一地理编码服务
pnpm test:unified-geocoding

# 测试Google Maps连接
pnpm test:google-maps

# 测试富文本分析
pnpm test:format-richtext
```

## 🔧 提供商配置

### Google Maps

- **优势**: 全球覆盖最佳，数据准确性高
- **费用**: 按请求计费，有免费额度
- **限制**: 需要信用卡验证

```typescript
{
  provider: "google",
  apiKey: "your_google_key",
  weight: 2,
  priority: 1,
}
```

### 高德地图

- **优势**: 中国地址最准确，免费额度大
- **费用**: 个人开发者免费额度充足
- **限制**: 主要覆盖中国

```typescript
{
  provider: "amap",
  apiKey: "your_amap_key",
  weight: 3,
  priority: 1,
}
```

### 百度地图

- **优势**: 中国地址准确，支持全球
- **费用**: 有免费额度
- **限制**: 需要实名认证

```typescript
{
  provider: "baidu",
  apiKey: "your_baidu_key",
  weight: 2,
  priority: 2,
}
```

### Bing Maps

- **优势**: 微软服务，稳定性好
- **费用**: 有免费额度
- **限制**: 中文地址准确性一般

```typescript
{
  provider: "bing",
  apiKey: "your_bing_key",
  weight: 1,
  priority: 3,
}
```

### Mapbox

- **优势**: 地图样式丰富，开发者友好
- **费用**: 有免费额度
- **限制**: 中文地址支持有限

```typescript
{
  provider: "mapbox",
  apiKey: "your_mapbox_key",
  weight: 1,
  priority: 4,
}
```

## 💡 最佳实践

### 1. 提供商选择

**推荐配置（中国用户）**:
1. 高德地图（主要）- 中文地址最准确
2. 百度地图（备选）- 中文地址备选
3. Google Maps（备选）- 国外地址和备用

**推荐配置（国际用户）**:
1. Google Maps（主要）- 全球覆盖最佳
2. Bing Maps（备选）- 稳定的备选方案
3. Mapbox（备选）- 开发者友好

### 2. 缓存策略

```typescript
{
  enableCache: true,
  cacheTTL: 3600, // 1小时，地址信息相对稳定
}
```

### 3. 错误处理

```typescript
try {
  const result = await service.geocode(address);
  if (!result) {
    // 处理未找到结果的情况
    console.log("未找到地理编码结果");
  }
} catch (error) {
  // 处理服务错误
  console.error("地理编码服务错误:", error);
}
```

### 4. 性能优化

- 启用缓存减少重复请求
- 使用批量接口处理多个地址
- 根据使用场景选择合适的负载均衡策略
- 定期监控统计信息优化配置

## 🔍 故障排除

### 常见问题

1. **所有提供商都失败**
   - 检查API密钥是否正确
   - 检查网络连接
   - 检查API配额是否用完

2. **中文地址识别不准确**
   - 优先使用高德地图或百度地图
   - 设置 `language: "zh-CN"` 和 `region: "CN"`

3. **响应时间过长**
   - 检查网络连接
   - 考虑使用缓存
   - 调整超时设置

4. **API配额不足**
   - 监控使用统计
   - 配置多个提供商分散请求
   - 启用缓存减少重复请求

### 调试模式

设置环境变量启用详细日志：

```bash
DEBUG=geocoding:*
```

## �� 许可证

MIT License 