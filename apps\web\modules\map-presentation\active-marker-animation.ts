"use client";

import type { MapPoint, PulseEffectRef } from "./types";

/**
 * 创建脉冲动画效果
 *
 * @param map - Google Maps 地图实例
 * @param point - 地图点位
 * @returns 脉冲效果圆圈实例
 */
export function createPulseEffect(
	map: google.maps.Map,
	point: MapPoint,
): PulseEffectRef {
	// 创建脉冲圆形效果
	const pulseCircle = new google.maps.Circle({
		strokeColor: "#FF5722",
		strokeOpacity: 0.8,
		strokeWeight: 2,
		fillColor: "#FF5722",
		fillOpacity: 0.35,
		map: map,
		center: {
			lat: point.coordinates.lat,
			lng: point.coordinates.lng,
		},
		radius: 30, // 初始半径（米）
		zIndex: -1, // 确保圆在标记下方
	});

	// 启动脉冲动画
	startPulseAnimation(pulseCircle);

	return pulseCircle as PulseEffectRef;
}

/**
 * 启动脉冲动画
 *
 * @param circle - Google Maps Circle 实例
 * @returns 动画帧ID，用于取消动画
 */
export function startPulseAnimation(circle: google.maps.Circle): number {
	// 动画参数
	let radius = 30;
	let opacity = 0.35;
	let expanding = true;
	const maxRadius = 50; // 最大半径（米）
	const minRadius = 20; // 最小半径（米）
	const opacityMin = 0.15;
	const opacityMax = 0.35;

	// 动画帧函数
	const animate = () => {
		// 根据当前状态调整半径和透明度
		if (expanding) {
			radius += 0.5;
			opacity -= 0.005;
			if (radius >= maxRadius) {
				expanding = false;
			}
		} else {
			radius -= 0.5;
			opacity += 0.005;
			if (radius <= minRadius) {
				expanding = true;
			}
		}

		// 应用新属性到圆形
		circle.setRadius(radius);
		circle.setOptions({
			fillOpacity: Math.max(opacityMin, Math.min(opacityMax, opacity)),
			strokeOpacity: Math.max(0.3, Math.min(0.8, opacity + 0.3)),
		});

		// 继续动画循环
		return window.requestAnimationFrame(animate);
	};

	// 启动动画
	return window.requestAnimationFrame(animate);
}

/**
 * 停止脉冲动画
 *
 * @param animationId - 动画帧ID
 */
export function stopPulseAnimation(animationId: number): void {
	if (animationId) {
		window.cancelAnimationFrame(animationId);
	}
}
