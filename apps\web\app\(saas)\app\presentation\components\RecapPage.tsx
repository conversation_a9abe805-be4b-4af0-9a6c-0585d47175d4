"use client";

import { Button } from "@ui/components/button";
import "@fancyapps/ui/dist/fancybox/fancybox.css";
import { Fancybox } from "@fancyapps/ui";
import { Share } from "lucide-react";
import { LngLatBounds } from "mapbox-gl";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import type { FrontendTravelPoint, TravelDiary } from "../types";
import { formatDateRange, getDaysDifference } from "../utils/date-utils";
import { ExportVideoButton } from "./ExportVideoButton";
import { MapComponent } from "./MapComponent";
import { MapStoryStyles } from "./MapStoryStyles";
import { GlobalFontStyles } from "./common/GlobalFontStyles";

interface RecapPageProps {
	diary: TravelDiary;
	points: FrontendTravelPoint[];
	stats: {
		totalLocations: number;
		totalImages: number;
		startDate: Date | null;
		endDate: Date | null;
	};
}

// 照片样式预计算接口
interface PhotoStyle {
	rotate: number;
	offsetY: number;
	delay: number;
	captionText: string;
}

export function RecapPage({ diary, points, stats }: RecapPageProps) {
	const t = useTranslations("travelMemo.recapPage");
	const [allImages, setAllImages] = useState<string[]>([]);
	const [isSharing, setIsSharing] = useState(false);
	const [scrollY, setScrollY] = useState(0);
	const [contentHeight, setContentHeight] = useState(0);
	const [mapLoaded, setMapLoaded] = useState(false);
	const [mapOpacity, setMapOpacity] = useState(0);

	// 确保点位按时间顺序排序，用于正确的路径连接
	const sortedPoints = useMemo(() => {
		const sorted = [...points].sort((a, b) => {
			const timeA = new Date(a.timestamp).getTime();
			const timeB = new Date(b.timestamp).getTime();
			return timeA - timeB;
		});

		// 添加调试信息
		if (process.env.NODE_ENV === "development") {
			console.log("RecapPage 点位排序调试:", {
				原始顺序: points.map((p, i) => ({
					index: i,
					id: p.id,
					address: p.address || p.title,
					timestamp: p.timestamp,
					coordinates: [p.coordinates?.lng, p.coordinates?.lat],
				})),
				排序后: sorted.map((p, i) => ({
					index: i,
					id: p.id,
					address: p.address || p.title,
					timestamp: p.timestamp,
					coordinates: [p.coordinates?.lng, p.coordinates?.lat],
				})),
			});
		}

		return sorted;
	}, [points]);

	// 引用
	const contentRef = useRef<HTMLDivElement>(null);
	const contentContainerRef = useRef<HTMLDivElement>(null);
	const mapContainerRef = useRef<HTMLDivElement>(null);
	const photoWallRef = useRef<HTMLDivElement>(null);

	// 照片样式预计算 - 确保每次渲染使用相同的随机值
	const photoStyles = useMemo(() => {
		const captions = [
			t("photoCaptions.diary"),
			t("photoCaptions.memories"),
			t("photoCaptions.moments"),
			t("photoCaptions.scenery"),
			t("photoCaptions.time"),
		];

		return allImages.map((_, idx) => {
			return {
				rotate: -5 + Math.random() * 10, // 随机旋转角度 (-5度到5度)
				offsetY: Math.floor(Math.random() * 15), // 随机垂直偏移
				delay: Math.random() * 0.5, // 随机动画延迟
				captionText: captions[idx % captions.length],
			} as PhotoStyle;
		});
	}, [allImages.length]); // 只依赖于图片数量，不会因为滚动重新计算

	// 更新内容高度
	const updateContentHeight = useCallback(() => {
		if (contentContainerRef.current) {
			const height = contentContainerRef.current.scrollHeight;
			setContentHeight(height);

			// 更新地图容器高度
			if (mapContainerRef.current) {
				// 设置地图容器高度为内容高度的 1.2 倍，确保足够覆盖所有内容
				const mapHeight = Math.max(
					height * 1.2,
					window.innerHeight * 1.5,
				);
				mapContainerRef.current.style.height = `${mapHeight}px`;

				// 触发地图重新渲染的标记
				if (mapLoaded && mapContainerRef.current) {
					// 短暂延迟，确保高度变化后再通知地图调整
					setTimeout(() => {
						// 通过重设地图容器样式来触发地图尺寸更新事件
						if (mapContainerRef.current) {
							const mapContainer =
								mapContainerRef.current.querySelector(
									".mapboxgl-map",
								);
							if (mapContainer) {
								(mapContainer as HTMLElement).style.height =
									"100%";
							}

							// 发布一个自定义事件，触发窗口大小调整
							window.dispatchEvent(new Event("resize"));
						}
					}, 200);
				}
			}
		}
	}, [mapLoaded]);

	// 添加图片加载监听函数
	const handleImagesLoaded = useCallback(() => {
		// 延迟执行以确保DOM完全更新
		setTimeout(() => {
			updateContentHeight();
		}, 100);
	}, [updateContentHeight]);

	// 视差滚动效果
	useEffect(() => {
		const handleScroll = () => {
			if (contentRef.current) {
				// 获取当前滚动位置
				const scrollPosition = contentRef.current.scrollTop;
				// 计算最大可滚动距离
				const maxScroll =
					contentRef.current.scrollHeight -
					contentRef.current.clientHeight;
				// 设置滚动位置
				setScrollY(scrollPosition);

				// 移除地图容器的视差效果，避免影响路径绘制
				// 地图背景保持固定，不应用transform变换
			}
		};

		const contentElement = contentRef.current;
		if (contentElement) {
			contentElement.addEventListener("scroll", handleScroll);
			// 初始加载时触发一次，确保正确定位
			handleScroll();
		}

		return () => {
			if (contentElement) {
				contentElement.removeEventListener("scroll", handleScroll);
			}
		};
	}, []);

	// 更新内容高度并设置地图高度
	useEffect(() => {
		// 首次加载和内容变化时更新高度
		updateContentHeight();

		// MutationObserver监听内容变化
		const observer = new MutationObserver(() => {
			updateContentHeight();
		});

		if (contentContainerRef.current) {
			observer.observe(contentContainerRef.current, {
				childList: true,
				subtree: true,
				attributes: true,
				characterData: true,
			});
		}

		return () => {
			observer.disconnect();
		};
	}, [updateContentHeight, allImages]);

	// 监听图片加载完成事件
	useEffect(() => {
		const imgElements = document.querySelectorAll(".photo-wall img");
		let loadedCount = 0;
		const totalCount = imgElements.length;

		if (totalCount === 0) return;

		const handleLoad = () => {
			loadedCount++;
			if (loadedCount === totalCount) {
				handleImagesLoaded();
			}
		};

		imgElements.forEach((img) => {
			if ((img as HTMLImageElement).complete) {
				handleLoad();
			} else {
				img.addEventListener("load", handleLoad);
			}
		});

		return () => {
			imgElements.forEach((img) => {
				img.removeEventListener("load", handleLoad);
			});
		};
	}, [allImages, handleImagesLoaded]);

	// 确保在窗口大小变化时重新计算
	useEffect(() => {
		const handleResize = () => {
			updateContentHeight();
			if (contentRef.current) {
				const scrollPosition = contentRef.current.scrollTop;
				setScrollY(scrollPosition);
			}
		};

		window.addEventListener("resize", handleResize);
		return () => {
			window.removeEventListener("resize", handleResize);
		};
	}, [updateContentHeight]);

	// 初始化 Fancybox
	useEffect(() => {
		Fancybox.bind("[data-fancybox]", {
			// 自定义选项
			Thumbs: {
				type: "modern",
				showOnStart: true,
			},
			Toolbar: {
				display: {
					left: ["infobar"],
					middle: [
						"zoomIn",
						"zoomOut",
						"toggle1to1",
						"rotateCCW",
						"rotateCW",
					],
					right: [
						"slideshow",
						"fullscreen",
						"download",
						"thumbs",
						"close",
					],
				},
			},
			Images: {
				Panzoom: {
					maxScale: 2,
				},
			},
			Carousel: {
				transition: "slide",
			},
			on: {
				done: (fancybox: any, slide: any) => {
					// 当图片加载完成时
				},
			},
		});

		return () => {
			Fancybox.destroy();
		};
	}, []);

	// 收集所有图片
	useEffect(() => {
		const images = sortedPoints
			.flatMap((point) => point.images || [])
			.filter(Boolean)
			.map((image) => (typeof image === "string" ? image : image.url)); // 提取 URL 字符串

		setAllImages(images);

		// 在图片数组变化后，设置一个延迟更新高度
		// 这是为了确保在渲染新图片后重新计算高度
		setTimeout(() => {
			updateContentHeight();
		}, 300);
	}, [sortedPoints, updateContentHeight]);

	// 地图加载效果
	useEffect(() => {
		// 地图组件挂载后设置加载状态
		setTimeout(() => {
			setMapLoaded(true);
			setTimeout(() => {
				setMapOpacity(1);
				// 地图完全加载后，再次触发一次窗口大小调整事件
				window.dispatchEvent(new Event("resize"));
			}, 100);
		}, 500);
	}, []);

	// 计算旅行天数
	const travelDays =
		stats.startDate && stats.endDate
			? getDaysDifference(stats.startDate, stats.endDate) + 1 // 包含第一天
			: 0;

	// 格式化日期范围
	const dateRangeText =
		stats.startDate && stats.endDate
			? formatDateRange(stats.startDate, stats.endDate)
			: "";

	// 处理分享
	const handleShare = () => {
		setIsSharing(true);

		try {
			if (navigator.share) {
				navigator
					.share({
						title: diary.title,
						text: t("header.shareStoryButtonLabel"),
						url: window.location.href,
					})
					.catch((err) => {
						console.error(t("header.logShareFailed"), err);
						// 复制链接作为备用选项
						copyToClipboard();
					});
			} else {
				// 浏览器不支持原生分享，复制链接
				copyToClipboard();
			}
		} catch (err) {
			console.error(t("header.logShareError"), err);
		}

		setTimeout(() => {
			setIsSharing(false);
		}, 2000);
	};

	// 复制链接到剪贴板
	const copyToClipboard = () => {
		navigator.clipboard
			.writeText(window.location.href)
			.then(() => {
				// 显示提示
				const toast = document.createElement("div");
				toast.className =
					"fixed top-4 left-0 right-0 mx-auto w-max px-4 py-2 bg-black/80 text-white rounded-lg text-sm";
				toast.innerText = t("header.toastLinkCopied");
				document.body.appendChild(toast);

				// 2秒后移除提示
				setTimeout(() => {
					document.body.removeChild(toast);
				}, 2000);
			})
			.catch((err) => {
				console.error(t("header.toastCopyLinkFailed"), err);
			});
	};

	// 计算地图背景高度 - 确保覆盖所有内容
	const mapBackgroundHeight = Math.max(
		contentHeight * 1.2,
		window.innerHeight * 1.5,
	);

	// Mapbox Token - 实际项目中应从环境变量获取
	const mapboxToken = process.env.NEXT_PUBLIC_MAPBOX_TOKEN || "";

	// 计算初始视角 - 基于所有点位的中心
	const initialViewState = useMemo(() => {
		if (sortedPoints.length === 0) {
			return {
				longitude: 0,
				latitude: 20,
				zoom: 2,
				pitch: 0,
				bearing: 0,
			};
		}

		// 计算所有有效点位的中心
		const validPoints = sortedPoints.filter(
			(p) =>
				p.coordinates &&
				typeof p.coordinates.lng === "number" &&
				typeof p.coordinates.lat === "number",
		);

		if (validPoints.length === 0) {
			return {
				longitude: 0,
				latitude: 20,
				zoom: 2,
				pitch: 0,
				bearing: 0,
			};
		}

		const centerLng =
			validPoints.reduce((sum, p) => sum + p.coordinates.lng, 0) /
			validPoints.length;
		const centerLat =
			validPoints.reduce((sum, p) => sum + p.coordinates.lat, 0) /
			validPoints.length;

		return {
			longitude: centerLng,
			latitude: centerLat,
			zoom: 6, // 中等缩放级别，后续会通过 fitBounds 调整
			pitch: 0,
			bearing: 0,
		};
	}, [sortedPoints]);

	// 内部组件：回顾页面头部
	const RecapHeader = () => (
		<header className="relative z-10 text-center py-12 md:py-20 bg-gradient-to-b from-slate-900 via-slate-800 to-transparent px-4">
			<h1 className="text-4xl md:text-6xl font-extrabold text-white drop-shadow-lg title-font mb-2">
				{diary.title}
			</h1>
			{diary.subtitle && (
				<p className="text-lg md:text-2xl text-slate-300 drop-shadow-md max-w-3xl mx-auto subtitle-font mb-6">
					{diary.subtitle}
				</p>
			)}
			<p className="text-md md:text-lg text-amber-400 font-semibold drop-shadow-sm date-range-font mb-8">
				{dateRangeText}
			</p>
			<div className="text-white/90 text-sm md:text-base stats-font mb-8">
				{t("header.summary", {
					days: travelDays,
					locations: stats.totalLocations,
					images: stats.totalImages,
				})}
			</div>
			<div className="flex justify-center space-x-4">
				<ExportVideoButton diaryId={diary.id} />
				<Button
					variant="outline"
					className="text-white border-white/50 hover:bg-white/10 hover:text-white rounded-lg transition-all duration-300 ease-in-out transform hover:scale-105 shadow-lg hover:shadow-amber-400/30 text-sm md:text-base"
					onClick={handleShare}
					disabled={isSharing}
					aria-label={t("header.shareStoryButtonLabel")}
				>
					<Share size={18} className="mr-2" />
					{t("header.shareStoryButtonLabel")}
				</Button>
			</div>
		</header>
	);

	// 内部组件：照片墙
	const PhotoWall = () => (
		<section className="py-12 md:py-20 bg-slate-800/30 backdrop-blur-sm relative z-10">
			<div className="container mx-auto px-4">
				<h2 className="text-3xl md:text-4xl font-bold text-white text-center mb-12 title-font">
					{t("photoWall.title", { count: allImages.length })}
				</h2>
				{allImages.length > 0 ? (
					<div
						ref={photoWallRef}
						className="photo-wall grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 md:gap-6 justify-center items-start"
						style={{ perspective: "1500px" }}
					>
						{allImages.map((image, idx) => (
							<a
								href={image}
								data-fancybox="gallery"
								key={idx}
								className="block bg-white p-2 md:p-3 shadow-2xl rounded-sm polaroid-hover relative overflow-hidden aspect-[3/4]"
								style={{
									transform: `rotate(${photoStyles[idx]?.rotate ?? 0}deg) translateY(${photoStyles[idx]?.offsetY ?? 0}px)`,
									animation: `tiltAnimation 15s infinite ease-in-out ${photoStyles[idx]?.delay ?? 0}s`,
								}}
							>
								<img
									src={image}
									alt={`${t("photoCaptions.diary")} ${idx + 1}`}
									className="w-full h-full object-cover"
									loading="lazy"
								/>
								<p className="absolute bottom-1 md:bottom-2 left-0 right-0 text-center text-xs md:text-sm text-slate-700 bg-white/70 py-0.5 md:py-1 photo-caption-font">
									{photoStyles[idx]?.captionText ??
										t("photoCaptions.diary")}
								</p>
							</a>
						))}
					</div>
				) : (
					<p className="text-center text-slate-400">
						{t("photoWall.emptyState")}
					</p>
				)}
			</div>
		</section>
	);

	// 内部组件：时间轴区域
	const TimelineSection = () => {
		// 将点位按日期分组
		const groupedPoints = useMemo(() => {
			return sortedPoints.reduce(
				(acc, point) => {
					// 使用timestamp替代date字段
					const dateObj = point.timestamp;
					const dateStr = dateObj.toISOString().split("T")[0];
					if (!acc[dateStr]) {
						acc[dateStr] = [];
					}
					acc[dateStr].push(point);
					return acc;
				},
				{} as Record<string, FrontendTravelPoint[]>,
			);
		}, [sortedPoints]);

		const sortedDates = Object.keys(groupedPoints).sort(
			(a, b) => new Date(a).getTime() - new Date(b).getTime(),
		);

		if (sortedPoints.length === 0) {
			return (
				<div className="text-center py-10 text-slate-400">
					{t("timeline.emptyState")}
				</div>
			);
		}

		return (
			<section className="py-12 md:py-20 bg-slate-800/60 backdrop-blur-md relative z-10">
				<div className="container mx-auto px-4">
					{sortedDates.map((dateStr, dateIdx) => {
						const dayPoints = groupedPoints[dateStr];
						const formattedDate = new Date(
							dateStr,
						).toLocaleDateString(undefined, {
							year: "numeric",
							month: "long",
							day: "numeric",
						});

						return (
							<div
								key={dateStr}
								className="mb-16 last:mb-0 timeline-day-block"
							>
								<h3 className="text-2xl md:text-3xl font-semibold text-amber-400 mb-8 text-center md:text-left date-title-font">
									{formattedDate}
								</h3>
								<div className="space-y-12">
									{dayPoints.map((point, pointIdx) => (
										<div
											key={point.id}
											className={`flex flex-col md:flex-row items-start gap-6 md:gap-8 timeline-point-item ${
												pointIdx % 2 === 0
													? "md:flex-row-reverse"
													: ""
											}`}
										>
											{/* 图片展示区域 */}
											{point.images &&
												point.images.length > 0 && (
													<div className="w-full md:w-2/5 lg:w-1/3 flex-shrink-0 grid grid-cols-2 gap-2 md:gap-3">
														{point.images
															.slice(0, 4)
															.map(
																(
																	image,
																	imgIdx,
																) => (
																	<a
																		href={
																			typeof image ===
																			"string"
																				? image
																				: image.url
																		}
																		data-fancybox={`gallery-${point.id}`}
																		data-caption={
																			point.address ||
																			point.title
																		}
																		key={
																			imgIdx
																		}
																		className="aspect-square block rounded-lg overflow-hidden shadow-lg hover:scale-105 transition-transform duration-300 ease-in-out polaroid-hover"
																	>
																		<img
																			src={
																				typeof image ===
																				"string"
																					? image
																					: image.url
																			}
																			alt={`${point.address || point.title} ${imgIdx + 1}`}
																			className="w-full h-full object-cover"
																			loading="lazy"
																		/>
																	</a>
																),
															)}
													</div>
												)}
											{/* 文字描述区域 */}
											<div className="flex-grow bg-slate-700/50 p-4 md:p-6 rounded-lg shadow-xl backdrop-blur-sm">
												<h4 className="text-xl md:text-2xl font-semibold text-white mb-2 point-title-font">
													{point.address ||
														point.title}
												</h4>
												<p className="text-sm text-amber-300 mb-3 location-font">
													{t(
														"timeline.itemTitleFormat",
														{
															date: point.timestamp.toLocaleTimeString(
																undefined,
																{
																	hour: "2-digit",
																	minute: "2-digit",
																},
															),
															location:
																point.address ||
																point.title,
														},
													)}
												</p>
												{point.description && (
													<p className="text-slate-300 text-sm md:text-base leading-relaxed description-font whitespace-pre-wrap">
														{point.description}
													</p>
												)}
											</div>
										</div>
									))}
								</div>
							</div>
						);
					})}
				</div>
			</section>
		);
	};

	// 内部组件：回顾页面页脚
	const RecapFooter = () => (
		<footer className="py-12 md:py-20 bg-gradient-to-t from-slate-900 via-slate-800 to-transparent text-center relative z-10">
			<Button
				size="lg"
				className="bg-amber-500 hover:bg-amber-600 text-slate-900 font-semibold rounded-lg shadow-lg hover:shadow-amber-500/40 transition-all duration-300 ease-in-out transform hover:scale-105 text-base md:text-lg mb-4 md:mb-0 md:mr-4"
				onClick={() => {
					window.location.href = "/app";
				}}
			>
				{t("footer.exploreMoreButton")}
			</Button>
			<span className="text-slate-400 mx-2 hidden md:inline">
				{t("footer.orSeparator")}
			</span>
			<Button
				variant="outline"
				size="lg"
				className="text-white border-white/50 hover:bg-white/10 hover:text-white rounded-lg transition-all duration-300 ease-in-out transform hover:scale-105 shadow-lg hover:shadow-white/20 text-base md:text-lg mt-4 md:mt-0"
				onClick={() => {
					window.location.href = "/";
				}}
			>
				{t("footer.backToHomeButton")}
			</Button>
		</footer>
	);

	return (
		<div
			ref={contentRef}
			className="relative h-full w-full overflow-auto custom-scrollbar travel-memo-content"
			style={{
				perspective: "1px",
				perspectiveOrigin: "center top",
				scrollbarWidth: "thin",
				scrollbarColor: "rgba(255,255,255,0.3) rgba(0,0,0,0)",
			}}
		>
			{/* 添加地图样式 */}
			<MapStoryStyles />

			{/* 添加全局字体样式 */}
			<GlobalFontStyles />

			{/* 添加自定义滚动条样式 */}
			<style jsx global>{`
				.custom-scrollbar::-webkit-scrollbar {
					width: 8px;
				}
				.custom-scrollbar::-webkit-scrollbar-track {
					background: rgba(0, 0, 0, 0.2);
				}
				.custom-scrollbar::-webkit-scrollbar-thumb {
					background: rgba(255, 255, 255, 0.3);
					border-radius: 10px;
				}
				.custom-scrollbar::-webkit-scrollbar-thumb:hover {
					background: rgba(255, 255, 255, 0.5);
				}
				
				@keyframes tiltAnimation {
					0% { transform: perspective(1000px) rotateY(0deg); }
					50% { transform: perspective(1000px) rotateY(1deg); }
					100% { transform: perspective(1000px) rotateY(0deg); }
				}
				
				.polaroid-hover {
					transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
				}
				
				.polaroid-hover:hover {
					z-index: 10;
					transform: scale(1.05) rotate(0deg) !important;
					box-shadow: 0 10px 20px rgba(0,0,0,0.3);
				}
			`}</style>

			{/* 固定背景层 - 始终可见 */}
			<div className="fixed inset-0 bg-slate-900 -z-10" />

			{/* 地图背景 - 使用MapComponent */}
			<div
				ref={mapContainerRef}
				className="absolute inset-0 z-0 will-change-transform"
				style={{
					height: mapBackgroundHeight,
					minHeight: "150vh",
					opacity: mapOpacity,
					transition: "opacity 1.5s ease-in-out",
					overflow: "hidden", // 确保地图不会溢出容器
				}}
			>
				{/* 使用MapComponent显示整个旅行路线，包裹在相对定位的容器内 */}
				<div className="w-full h-full overflow-hidden relative">
					{mapboxToken && (
						<div className="absolute inset-0">
							<MapComponent
								mapboxToken={mapboxToken}
								mapStyle="mapbox://styles/mapbox/dark-v10" // 使用深色地图样式
								points={sortedPoints}
								currentPointIndex={sortedPoints.length - 1} // 设置为最后一个点，显示完整路线
								showCompletedMode={true} // 使用完成模式，展示所有点位和路线
								fitBoundsToPoints={true} // 自动调整视角以显示所有点位
								hideControls={true} // 隐藏地图控件
								interactiveLayerIds={[]} // 禁用交互
								// 设置初始视角，在点位加载前提供合理的默认视图
								initialViewState={initialViewState}
								// 添加额外属性确保地图正确填充
								onMapLoad={(map) => {
									// 保存地图实例引用
									if (map) {
										// 调整地图以适应当前视口
										setTimeout(() => {
											// 重新计算边界以显示所有点位
											if (sortedPoints.length > 0) {
												const bounds =
													new LngLatBounds();
												sortedPoints
													.filter(
														(point) =>
															point.coordinates &&
															typeof point
																.coordinates
																.lng ===
																"number" &&
															typeof point
																.coordinates
																.lat ===
																"number",
													)
													.forEach((point) => {
														bounds.extend([
															point.coordinates
																.lng,
															point.coordinates
																.lat,
														]);
													});

												// 确保有有效坐标的点位
												if (bounds.isEmpty()) {
													console.error(
														"没有有效坐标的点位，无法调整地图视角",
													);
													return;
												}

												// 使用合适的padding确保所有点位都可见
												const screenWidth =
													window.innerWidth;
												const isMobile =
													screenWidth < 768;

												map.fitBounds(bounds, {
													padding: {
														top: isMobile ? 60 : 80, // 移动端顶部留出较少空间
														bottom: isMobile
															? 80
															: 120, // 移动端底部留出较少空间
														left: isMobile
															? 40
															: 60, // 移动端左右边距较小
														right: isMobile
															? 40
															: 60,
													},
													duration: 0, // 立即应用，无动画
													maxZoom: isMobile ? 14 : 16, // 移动端限制更低的最大缩放
												});

												// 获取调整后的缩放级别，确保有足够的概览效果
												const adjustedZoom =
													map.getZoom();
												// 适度缩小以提供更好的概览效果，移动端缩小更多
												const zoomMultiplier = isMobile
													? 0.85
													: 0.9;
												const minZoom = isMobile
													? 6
													: 8; // 移动端允许更小的缩放
												const finalZoom = Math.max(
													adjustedZoom *
														zoomMultiplier,
													minZoom,
												);
												map.setZoom(finalZoom);

												// 禁用所有地图交互
												map.dragPan.disable(); // 禁用拖动
												map.scrollZoom.disable(); // 禁用滚轮缩放
												map.doubleClickZoom.disable(); // 禁用双击缩放
												map.touchZoomRotate.disable(); // 禁用触摸缩放和旋转
												map.keyboard.disable(); // 禁用键盘控制

												// 隐藏所有控制按钮
												const controlButtons =
													document.querySelectorAll(
														".mapboxgl-ctrl-top-right, .mapboxgl-ctrl-bottom-right",
													);
												controlButtons.forEach(
													(button) => {
														if (
															button instanceof
															HTMLElement
														) {
															button.style.display =
																"none";
														}
													},
												);
											}
										}, 500);
									}
								}}
								// 监听地图移动事件，确保视图覆盖所有区域
								onMapMove={(evt) => {
									// 可以在这里添加逻辑以确保地图始终覆盖需要的区域
								}}
							/>
						</div>
					)}

					{/* 地图叠加渐变遮罩 - 增强对比度和沉浸感 */}
					<div className="absolute inset-0 bg-gradient-to-b from-black/80 via-black/40 to-black/90 pointer-events-none" />
				</div>
			</div>

			{/* 内容 - 正常滚动 */}
			<div ref={contentContainerRef} className="relative z-10">
				{/* 标题 - 半视差效果 */}
				<div
					className="text-center py-12 px-4 bg-black/30 backdrop-blur-md"
					style={{
						transform: `translateY(${scrollY * 0.2}px)`,
						transition: "transform 0.1s ease-out",
					}}
				>
					<h1 className="text-4xl md:text-6xl font-bold mb-4 text-white drop-shadow-md travel-memo-title">
						{diary.title}
					</h1>
					<p className="text-xl md:text-2xl text-white/80 mb-3 drop-shadow-sm travel-memo-subtitle">
						{diary.subtitle}
					</p>
					<p className="text-sm md:text-base text-white/70 drop-shadow-sm travel-memo-text">
						{dateRangeText}
					</p>
				</div>

				{/* 内容容器 */}
				<div className="py-8">
					{/* 照片墙 - 全宽，现在移至顶部 - 移除所有可能导致抖动的类和样式 */}
					{allImages.length > 0 && (
						<div
							ref={photoWallRef}
							className="w-full px-4 md:px-8 mb-16"
							style={{
								willChange: "auto", // 防止浏览器优化导致的问题
								position: "relative", // 确保位置稳定
							}}
						>
							<h2 className="text-2xl md:text-3xl font-semibold text-center mb-8 text-white drop-shadow-lg travel-memo-title">
								旅行回忆
							</h2>

							{/* 拍立得风格照片墙 - 使用固定定位确保稳定，不受滚动影响 */}
							<div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 md:gap-6 py-4 photo-wall">
								{allImages.map((image, idx) => {
									// 使用预计算的样式，避免在滚动时重新计算
									const style = photoStyles[idx] || {
										rotate: 0,
										offsetY: 0,
										delay: 0,
										captionText: "旅行照片",
									};

									return (
										<div
											key={idx}
											className="group relative polaroid-hover"
											style={{
												transformOrigin: "center 80%",
												transform: `rotate(${style.rotate}deg)`,
												marginTop: `${style.offsetY}px`,
												// 移除可能导致抖动的属性
												position: "relative",
												zIndex: 1,
											}}
										>
											<a
												href={image}
												data-fancybox="gallery"
												data-caption={`旅行照片 ${idx + 1}`}
												data-thumb={image}
												className="block relative w-full pb-[120%] bg-white shadow-[0_5px_15px_rgba(0,0,0,0.3)] rounded overflow-hidden"
											>
												{/* 拍立得照片区域 */}
												<div className="absolute inset-x-0 top-0 bottom-[15%] p-3">
													<div className="w-full h-full overflow-hidden">
														<img
															src={image}
															alt={`旅行照片 ${idx + 1}`}
															className="w-full h-full object-cover hover:opacity-95 transition-opacity duration-300 saturate-[1.1] contrast-[1.05]"
															loading="lazy"
															onLoad={
																handleImagesLoaded
															}
														/>
														{/* 照片表面的光泽反光效果 */}
														<div className="absolute inset-0 bg-gradient-to-tr from-transparent via-white/5 to-white/20 pointer-events-none" />
													</div>
												</div>

												{/* 拍立得底部空白区域 */}
												<div className="absolute inset-x-0 bottom-0 h-[15%] bg-white flex items-center justify-center">
													<div className="text-xs text-gray-500 italic travel-memo-emphasis">
														{style.captionText}
													</div>
												</div>

												{/* 照片四周的细边框 */}
												<div className="absolute inset-3 bottom-[15%] border border-gray-200 pointer-events-none" />

												{/* 悬停效果遮罩 */}
												<div className="absolute inset-0 flex items-center justify-center bg-black/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
													<div className="px-3 py-2 bg-white/90 rounded text-gray-800 text-sm transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300 shadow-md travel-memo-button">
														点击查看
													</div>
												</div>
											</a>

											{/* 拍立得投影效果 */}
											<div className="absolute -inset-1 bg-black/10 -z-10 rounded transform translate-y-2 translate-x-1 blur-[1px]" />
										</div>
									);
								})}
							</div>
						</div>
					)}

					{/* 统计信息 - 带入场动画和视差效果 */}
					<div
						className="container mx-auto px-4 py-8 md:py-12"
						style={{
							transform: `translateY(${scrollY * 0.05}px)`,
							transition: "transform 0.2s ease-out",
						}}
					>
						<div className="max-w-4xl mx-auto">
							<h2 className="text-2xl md:text-3xl font-semibold text-center mb-8 text-white drop-shadow-lg travel-memo-title">
								旅行数据
							</h2>

							<div className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8 mb-16">
								{/* 统计卡片 - 更加精美的设计 */}
								<div className="bg-black/40 backdrop-blur-sm rounded-lg p-5 text-center shadow-lg border border-white/10 transform transition-all duration-500 hover:scale-105 hover:bg-black/50 hover:border-white/20">
									<div className="text-3xl md:text-4xl font-bold text-white mb-2 travel-memo-number">
										{travelDays}
									</div>
									<div className="text-sm text-white/70 travel-memo-label">
										旅行天数
									</div>
									<div className="w-12 h-1 bg-gradient-to-r from-blue-500 to-green-400 mx-auto mt-3 rounded-full" />
								</div>

								<div className="bg-black/40 backdrop-blur-sm rounded-lg p-5 text-center shadow-lg border border-white/10 transform transition-all duration-500 hover:scale-105 hover:bg-black/50 hover:border-white/20">
									<div className="text-3xl md:text-4xl font-bold text-white mb-2 travel-memo-number">
										{stats.totalLocations}
									</div>
									<div className="text-sm text-white/70 travel-memo-label">
										游览地点
									</div>
									<div className="w-12 h-1 bg-gradient-to-r from-purple-500 to-pink-400 mx-auto mt-3 rounded-full" />
								</div>

								<div className="bg-black/40 backdrop-blur-sm rounded-lg p-5 text-center shadow-lg border border-white/10 transform transition-all duration-500 hover:scale-105 hover:bg-black/50 hover:border-white/20">
									<div className="text-3xl md:text-4xl font-bold text-white mb-2 travel-memo-number">
										{stats.totalImages}
									</div>
									<div className="text-sm text-white/70 travel-memo-label">
										拍摄照片
									</div>
									<div className="w-12 h-1 bg-gradient-to-r from-yellow-500 to-red-400 mx-auto mt-3 rounded-full" />
								</div>

								<div className="bg-black/40 backdrop-blur-sm rounded-lg p-5 text-center shadow-lg border border-white/10 transform transition-all duration-500 hover:scale-105 hover:bg-black/50 hover:border-white/20">
									<div className="text-3xl md:text-4xl font-bold text-white mb-2 travel-memo-number">
										{Math.round(
											(sortedPoints.length / travelDays) *
												10,
										) / 10 || 0}
									</div>
									<div className="text-sm text-white/70 travel-memo-label">
										日均地点
									</div>
									<div className="w-12 h-1 bg-gradient-to-r from-green-500 to-teal-400 mx-auto mt-3 rounded-full" />
								</div>
							</div>

							{/* 底部操作按钮 */}
							<div className="flex justify-center mt-12">
								<Button
									onClick={handleShare}
									disabled={isSharing}
									className="rounded-full px-8 py-6 bg-white/10 hover:bg-white/20 text-white border border-white/20 backdrop-blur-sm transform transition-all duration-300 hover:scale-105 hover:shadow-lg travel-memo-button mr-4"
								>
									<Share className="w-5 h-5 mr-2" />
									{isSharing ? "分享中..." : "分享旅行故事"}
								</Button>

								<ExportVideoButton diaryId={diary.id} />
							</div>

							{/* 制作信息 */}
							<div className="text-center text-xs text-white/40 mt-16 mb-4 travel-memo-text">
								由 Travel Memo 精心制作
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
