"use client";

import { useCallback, useEffect, useMemo, useState } from "react";
import type { MarkerStyleType } from "../types/markerTypes";
import { MARKER_STYLES } from "../types/markerTypes";

export function useMarkerStyle() {
	// 点位风格状态
	const [markerStyle, setMarkerStyle] = useState<MarkerStyleType>(
		MARKER_STYLES.GRADIENT_PULSE,
	);
	const [markerTheme, setMarkerTheme] = useState<string>("ocean");

	// Emoji相关状态
	const [currentEmoji, setCurrentEmoji] = useState<string>("📍");
	const [currentEmojiColor, setCurrentEmojiColor] =
		useState<string>("transparent");

	// 轮廓控制状态
	const [hideOutline, setHideOutline] = useState<boolean>(false);

	// 从 localStorage 加载设置
	useEffect(() => {
		const savedMarkerStyle = localStorage.getItem("markerStyle");
		const savedMarkerTheme = localStorage.getItem("markerTheme");
		const savedCurrentEmoji = localStorage.getItem("currentEmoji");
		const savedCurrentEmojiColor =
			localStorage.getItem("currentEmojiColor");
		const savedHideOutline = localStorage.getItem("hideOutline");

		if (savedMarkerStyle) {
			try {
				setMarkerStyle(savedMarkerStyle as MarkerStyleType);
			} catch (error) {
				console.error("加载marker样式失败:", error);
			}
		}

		if (savedMarkerTheme) {
			try {
				setMarkerTheme(savedMarkerTheme);
			} catch (error) {
				console.error("加载marker主题失败:", error);
			}
		}

		if (savedCurrentEmoji) {
			try {
				setCurrentEmoji(savedCurrentEmoji);
			} catch (error) {
				console.error("加载emoji失败:", error);
			}
		}

		if (savedCurrentEmojiColor) {
			try {
				setCurrentEmojiColor(savedCurrentEmojiColor);
			} catch (error) {
				console.error("加载emoji颜色失败:", error);
			}
		}

		if (savedHideOutline !== null) {
			try {
				setHideOutline(savedHideOutline === "true");
			} catch (error) {
				console.error("加载轮廓显示设置失败:", error);
			}
		}
	}, []);

	// 保存设置到 localStorage
	useEffect(() => {
		localStorage.setItem("markerStyle", markerStyle);
	}, [markerStyle]);

	useEffect(() => {
		localStorage.setItem("markerTheme", markerTheme);
	}, [markerTheme]);

	useEffect(() => {
		localStorage.setItem("currentEmoji", currentEmoji);
	}, [currentEmoji]);

	useEffect(() => {
		localStorage.setItem("currentEmojiColor", currentEmojiColor);
	}, [currentEmojiColor]);

	useEffect(() => {
		localStorage.setItem("hideOutline", hideOutline.toString());
	}, [hideOutline]);

	// 更改点位风格
	const changeMarkerStyle = useCallback((style: MarkerStyleType) => {
		setMarkerStyle(style);
	}, []);

	// 更改点位主题
	const changeMarkerTheme = useCallback((theme: string) => {
		setMarkerTheme(theme);
	}, []);

	// 更改emoji
	const changeEmoji = useCallback((emoji: string) => {
		setCurrentEmoji(emoji);
	}, []);

	// 更改emoji颜色
	const changeEmojiColor = useCallback((color: string) => {
		setCurrentEmojiColor(color);
	}, []);

	// 更改轮廓显示状态
	const changeHideOutline = useCallback((hide: boolean) => {
		setHideOutline(hide);
	}, []);

	return useMemo(() => {
		return {
			markerStyle,
			markerTheme,
			currentEmoji,
			currentEmojiColor,
			hideOutline,
			changeMarkerStyle,
			changeMarkerTheme,
			changeEmoji,
			changeEmojiColor,
			changeHideOutline,
		};
	}, [
		markerStyle,
		markerTheme,
		currentEmoji,
		currentEmojiColor,
		hideOutline,
		changeMarkerStyle,
		changeMarkerTheme,
		changeEmoji,
		changeEmojiColor,
		changeHideOutline,
	]);
}

export type UseMarkerStyleReturn = ReturnType<typeof useMarkerStyle>;
