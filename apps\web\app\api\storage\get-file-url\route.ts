import { createStorageProviderFromEnv } from "@repo/storage";
import type { StorageProviderType } from "@repo/storage";
import { type NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
	try {
		const searchParams = request.nextUrl.searchParams;
		const provider = searchParams.get(
			"provider",
		) as StorageProviderType | null;
		const bucket = searchParams.get("bucket");
		const path = searchParams.get("path");
		const expiresIn = searchParams.get("expiresIn");
		const region = searchParams.get("region");

		if (!bucket || !path) {
			return NextResponse.json(
				{ error: "缺少必要参数 bucket 或 path" },
				{ status: 400 },
			);
		}

		// 创建存储提供商实例
		let storageProvider;

		try {
			// 如果指定了provider，使用指定的provider
			if (provider) {
				storageProvider = createStorageProviderFromEnv(provider);
			} else {
				// 否则尝试从环境变量中检测默认提供商
				storageProvider = createStorageProviderFromEnv();
			}
		} catch (error) {
			console.error("创建存储提供商失败:", error);
			return NextResponse.json(
				{
					error:
						error instanceof Error
							? error.message
							: "无法创建存储提供商",
				},
				{ status: 500 },
			);
		}

		// 获取签名访问URL
		const url = await storageProvider.getSignedUrl(path, {
			bucket,
			expiresIn: expiresIn ? Number.parseInt(expiresIn) : 3600,
			region: region || undefined,
		});

		return NextResponse.json({ url });
	} catch (error) {
		console.error("获取文件URL错误:", error);
		return NextResponse.json(
			{
				error:
					error instanceof Error ? error.message : "获取文件URL失败",
			},
			{ status: 500 },
		);
	}
}
