/* 水墨风装饰元素 */

/* 水墨动画效果 */
@keyframes ink-spread {
	0% {
		opacity: 0;
		transform: scale(0.95);
		filter: blur(3px);
	}
	50% {
		opacity: 0.7;
		filter: blur(1px);
	}
	100% {
		opacity: 1;
		transform: scale(1);
		filter: blur(0);
	}
}

.ink-animation {
	animation: ink-spread 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 水墨卡片 */
.ink-card {
	position: relative;
	border-radius: 0.5rem;
	overflow: hidden;
	background-color: white;
	transition: all 0.3s ease;
}

.ink-card::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cpath fill='%2393c5fd' fill-opacity='0.08' d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z'%3E%3C/path%3E%3C/svg%3E");
	opacity: 0.5;
	z-index: 0;
}

.ink-card:hover {
	transform: translateY(-3px);
	box-shadow: 0 10px 20px -10px rgba(147, 197, 253, 0.3);
}

/* 水墨边框 */
.ink-border {
	position: relative;
	border: 1px solid rgba(147, 197, 253, 0.2);
}

.ink-border::after {
	content: "";
	position: absolute;
	top: 5px;
	left: 5px;
	right: 5px;
	bottom: 5px;
	border: 1px solid rgba(147, 197, 253, 0.15);
	pointer-events: none;
}

/* 水墨菜单 */
.ink-menu-item {
	position: relative;
	transition: all 0.3s ease;
}

.ink-menu-item::before {
	content: "";
	position: absolute;
	bottom: -3px;
	left: 0;
	width: 0;
	height: 2px;
	background: linear-gradient(
		to right,
		rgba(147, 197, 253, 0.8),
		rgba(96, 165, 250, 0.4)
	);
	transition: width 0.3s ease;
}

.ink-menu-item:hover::before,
.ink-menu-item.active::before {
	width: 100%;
}

/* 水墨分割线 */
.ink-divider {
	position: relative;
	height: 1px;
	background: linear-gradient(
		to right,
		transparent,
		rgba(147, 197, 253, 0.3),
		transparent
	);
}

.ink-divider::before {
	content: "";
	position: absolute;
	top: -6px;
	left: 50%;
	transform: translateX(-50%);
	width: 12px;
	height: 12px;
	background-color: white;
	border: 1px solid rgba(147, 197, 253, 0.3);
	border-radius: 50%;
}

/* 水墨日期标签 */
.ink-date {
	font-family: var(--font-serif);
	color: rgba(55, 65, 81, 0.8);
	font-size: 0.875rem;
	line-height: 1.25rem;
	font-weight: 400;
	letter-spacing: 0.025em;
}

/* 水墨片段 */
.ink-fragment {
	position: relative;
}

.ink-fragment::before {
	content: "";
	position: absolute;
	top: 0;
	right: 0;
	width: 30px;
	height: 30px;
	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100' fill='none' stroke='rgba(147, 197, 253, 0.2)' stroke-width='1'%3E%3Cpath d='M100,0 C100,50 50,50 50,50 C50,50 50,100 0,100'/%3E%3C/svg%3E");
	background-size: contain;
	background-repeat: no-repeat;
	opacity: 0.5;
}
