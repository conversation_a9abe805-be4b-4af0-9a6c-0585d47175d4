import { type StorageProviderType, uploadFile } from "@repo/storage";
import { type NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
	try {
		// 由于需要处理文件流，使用 formData 解析请求数据
		const formData = await request.formData();
		const provider = formData.get("provider") as StorageProviderType | null;
		const bucket = formData.get("bucket") as string;
		const path = formData.get("path") as string;
		const region = formData.get("region") as string | null;
		const file = formData.get("file") as File | null;

		if (!bucket || !path || !file) {
			return NextResponse.json(
				{ error: "缺少必要参数 bucket、path 或 file" },
				{ status: 400 },
			);
		}

		// 读取文件内容
		const arrayBuffer = await file.arrayBuffer();
		const buffer = Buffer.from(arrayBuffer);

		// 直接使用存储服务的uploadFile方法
		const result = await uploadFile({
			bucket,
			path,
			file: buffer,
			contentType: file.type || undefined,
			region: region || undefined,
		});

		if (!result.success) {
			return NextResponse.json(
				{ error: result.error || "上传失败" },
				{ status: 500 },
			);
		}

		return NextResponse.json(result);
	} catch (error) {
		console.error("上传文件错误:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "上传文件失败",
			},
			{ status: 500 },
		);
	}
}
