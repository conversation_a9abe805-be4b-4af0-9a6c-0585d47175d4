"use client";

import type { Editor } from "@tiptap/react";
import { useEffect } from "react";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";

interface KeyboardShortcutsProps {
	editor: Editor | null;
}

export const KeyboardShortcuts: React.FC<KeyboardShortcutsProps> = ({
	editor,
}) => {
	useEffect(() => {
		if (!editor) return;

		const handleKeyDown = (event: KeyboardEvent) => {
			// 检查是否按下了Ctrl/Cmd键
			const isCtrlOrCmd = event.ctrlKey || event.metaKey;
			const isShift = event.shiftKey;
			const isAlt = event.altKey;

			// 快捷键映射
			if (isCtrlOrCmd && !isShift && !isAlt) {
				switch (event.key.toLowerCase()) {
					case "t": {
						// Ctrl/Cmd + T: 插入时间线
						event.preventDefault();
						editor
							.chain()
							.focus()
							.insertContent({
								type: "travelTimeline",
								attrs: {
									timelineId: uuidv4(),
									title: `第${new Date().toLocaleDateString("zh-CN")}天`,
									timelineDate: new Date().toISOString(),
								},
								content: [
									{
										type: "travelPoint",
										attrs: {
											pointId: uuidv4(),
											location: "新地点",
											pointDate: new Date().toISOString(),
										},
										content: [{ type: "paragraph" }],
									},
								],
							})
							.run();
						toast.success("已插入时间线 (Ctrl+T)");
						break;
					}
					case "p": {
						// Ctrl/Cmd + P: 插入点位
						event.preventDefault();
						editor
							.chain()
							.focus()
							.insertContent({
								type: "travelPoint",
								attrs: {
									pointId: uuidv4(),
									location: "新地点",
									pointDate: new Date().toISOString(),
								},
								content: [{ type: "paragraph" }],
							})
							.run();
						toast.success("已插入点位 (Ctrl+P)");
						break;
					}
					case "k": {
						// Ctrl/Cmd + K: 插入链接
						event.preventDefault();
						const url = window.prompt("输入链接URL:");
						if (url) {
							const fullUrl = url.startsWith("http")
								? url
								: `https://${url}`;
							editor
								.chain()
								.focus()
								.setLink({ href: fullUrl })
								.run();
							toast.success("已插入链接 (Ctrl+K)");
						}
						break;
					}
					case "s": {
						// Ctrl/Cmd + S: 保存提示
						event.preventDefault();
						toast.info("自动保存已启用 (Ctrl+S)");
						break;
					}
				}
			}

			// Shift + 快捷键
			if (isCtrlOrCmd && isShift && !isAlt) {
				switch (event.key.toLowerCase()) {
					case "i": {
						// Ctrl/Cmd + Shift + I: 插入图片
						event.preventDefault();
						const url = window.prompt("输入图片URL:");
						if (url) {
							editor.chain().focus().setImage({ src: url }).run();
							toast.success("已插入图片 (Ctrl+Shift+I)");
						}
						break;
					}
					case "l": {
						// Ctrl/Cmd + Shift + L: 切换无序列表
						event.preventDefault();
						editor.chain().focus().toggleBulletList().run();
						toast.success("已切换无序列表 (Ctrl+Shift+L)");
						break;
					}
					case "o": {
						// Ctrl/Cmd + Shift + O: 切换有序列表
						event.preventDefault();
						editor.chain().focus().toggleOrderedList().run();
						toast.success("已切换有序列表 (Ctrl+Shift+O)");
						break;
					}
				}
			}

			// Alt + 快捷键
			if (isAlt && !isCtrlOrCmd && !isShift) {
				switch (event.key.toLowerCase()) {
					case "1": {
						// Alt + 1: 标题1
						event.preventDefault();
						editor
							.chain()
							.focus()
							.toggleHeading({ level: 1 })
							.run();
						toast.success("已切换标题1 (Alt+1)");
						break;
					}
					case "2": {
						// Alt + 2: 标题2
						event.preventDefault();
						editor
							.chain()
							.focus()
							.toggleHeading({ level: 2 })
							.run();
						toast.success("已切换标题2 (Alt+2)");
						break;
					}
					case "0": {
						// Alt + 0: 普通段落
						event.preventDefault();
						editor.chain().focus().setParagraph().run();
						toast.success("已设置为段落 (Alt+0)");
						break;
					}
				}
			}

			// 特殊功能键
			switch (event.key) {
				case "F1": {
					// F1: 显示快捷键帮助
					event.preventDefault();
					showShortcutsHelp();
					break;
				}
				case "Escape": {
					// ESC: 取消当前操作
					if (editor.isActive("link")) {
						editor.chain().focus().unsetLink().run();
						toast.info("已取消链接");
					}
					break;
				}
			}
		};

		// 显示快捷键帮助
		const showShortcutsHelp = () => {
			const shortcuts = [
				"Ctrl+T - 插入时间线",
				"Ctrl+P - 插入点位",
				"Ctrl+K - 插入链接",
				"Ctrl+S - 保存提示",
				"Ctrl+Shift+I - 插入图片",
				"Ctrl+Shift+L - 无序列表",
				"Ctrl+Shift+O - 有序列表",
				"Alt+1 - 标题1",
				"Alt+2 - 标题2",
				"Alt+0 - 普通段落",
				"F1 - 显示帮助",
				"ESC - 取消操作",
			];

			toast.info(
				<div>
					<div className="font-medium mb-2">键盘快捷键</div>
					<div className="text-xs space-y-1">
						{shortcuts.map((shortcut, index) => (
							<div key={index}>{shortcut}</div>
						))}
					</div>
				</div>,
				{ duration: 8000 },
			);
		};

		// 添加事件监听器
		document.addEventListener("keydown", handleKeyDown);

		// 清理函数
		return () => {
			document.removeEventListener("keydown", handleKeyDown);
		};
	}, [editor]);

	// 这个组件不渲染任何内容，只处理键盘事件
	return null;
};
