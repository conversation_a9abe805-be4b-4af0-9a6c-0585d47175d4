{"extends": "@repo/tsconfig/nextjs.json", "compilerOptions": {"outDir": "dist", "declaration": true, "declarationMap": true, "sourceMap": true, "noEmitOnError": false, "skipLibCheck": true, "strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "strictBindCallApply": false, "strictPropertyInitialization": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "noUncheckedIndexedAccess": false, "noImplicitOverride": false, "allowUnreachableCode": true, "allowUnusedLabels": true, "exactOptionalPropertyTypes": false, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": false}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}