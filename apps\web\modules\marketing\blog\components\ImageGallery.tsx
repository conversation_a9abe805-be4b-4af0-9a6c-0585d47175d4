"use client";

import { cn } from "@ui/lib";
import { ChevronLeft, ChevronRight, ZoomIn } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

interface ImageGalleryProps {
	images: Array<{
		src: string;
		alt: string;
		title?: string;
		description?: string;
	}>;
	layout?: "grid" | "masonry" | "slider" | "hero";
	columns?: 2 | 3 | 4;
	className?: string;
	aspectRatio?: "square" | "video" | "auto";
}

export function ImageGallery({
	images,
	layout = "grid",
	columns = 3,
	className,
	aspectRatio = "auto",
}: ImageGalleryProps) {
	const [selectedImage, setSelectedImage] = useState<string | null>(null);
	const [currentSlide, setCurrentSlide] = useState(0);

	const getGridCols = () => {
		switch (columns) {
			case 2:
				return "md:grid-cols-2";
			case 3:
				return "md:grid-cols-3";
			case 4:
				return "md:grid-cols-2 lg:grid-cols-4";
			default:
				return "md:grid-cols-3";
		}
	};

	const getAspectRatio = () => {
		switch (aspectRatio) {
			case "square":
				return "aspect-square";
			case "video":
				return "aspect-video";
			default:
				return "aspect-auto";
		}
	};

	const nextSlide = () => {
		setCurrentSlide((prev) => (prev + 1) % images.length);
	};

	const prevSlide = () => {
		setCurrentSlide((prev) => (prev - 1 + images.length) % images.length);
	};

	if (layout === "hero" && images.length > 0) {
		const heroImage = images[0];
		return (
			<div className={cn("relative w-full mb-8", className)}>
				<div className="relative aspect-video overflow-hidden rounded-2xl bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
					<Image
						src={heroImage.src}
						alt={heroImage.alt}
						fill
						className="object-cover"
						sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
					/>
					<div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent" />
					{heroImage.title && (
						<div className="absolute bottom-6 left-6 right-6">
							<h3 className="text-2xl font-bold text-white mb-2 drop-shadow-lg">
								{heroImage.title}
							</h3>
							{heroImage.description && (
								<p className="text-white/90 drop-shadow-md">
									{heroImage.description}
								</p>
							)}
						</div>
					)}
				</div>
			</div>
		);
	}

	if (layout === "slider") {
		return (
			<div className={cn("relative w-full mb-8", className)}>
				<div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
					<div className="relative aspect-video">
						<Image
							src={images[currentSlide].src}
							alt={images[currentSlide].alt}
							fill
							className="object-cover transition-transform duration-500"
							sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
						/>
						<div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent" />

						{/* 控制按钮 */}
						<button
							type="button"
							onClick={prevSlide}
							className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 backdrop-blur-sm border border-white/30 rounded-full p-2 transition-all duration-200"
							aria-label="Previous image"
						>
							<ChevronLeft className="w-5 h-5 text-white" />
						</button>
						<button
							type="button"
							onClick={nextSlide}
							className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 backdrop-blur-sm border border-white/30 rounded-full p-2 transition-all duration-200"
							aria-label="Next image"
						>
							<ChevronRight className="w-5 h-5 text-white" />
						</button>

						{/* 指示器 */}
						<div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
							{images.map((_, index) => (
								<button
									key={index}
									onClick={() => setCurrentSlide(index)}
									className={cn(
										"w-2 h-2 rounded-full transition-all duration-200",
										index === currentSlide
											? "bg-white"
											: "bg-white/50 hover:bg-white/70",
									)}
								/>
							))}
						</div>
					</div>

					{/* 图片信息 */}
					{images[currentSlide].title && (
						<div className="absolute bottom-6 left-6 right-16">
							<h3 className="text-xl font-bold text-white mb-1 drop-shadow-lg">
								{images[currentSlide].title}
							</h3>
							{images[currentSlide].description && (
								<p className="text-white/90 text-sm drop-shadow-md">
									{images[currentSlide].description}
								</p>
							)}
						</div>
					)}
				</div>
			</div>
		);
	}

	// Grid 或 Masonry 布局
	return (
		<>
			<div
				className={cn(
					"grid gap-4 mb-8",
					layout === "masonry"
						? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
						: `grid-cols-1 ${getGridCols()}`,
					className,
				)}
			>
				{images.map((image, index) => (
					<button
						key={index}
						type="button"
						className={cn(
							"group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 cursor-pointer transition-all duration-300 hover:scale-[1.02] hover:shadow-lg border-0 bg-transparent p-0 w-full h-full",
							layout === "masonry" && index === 0
								? "md:col-span-2 md:row-span-2"
								: "",
							getAspectRatio(),
						)}
						onClick={() => setSelectedImage(image.src)}
						onKeyDown={(e) => {
							if (e.key === "Enter" || e.key === " ") {
								e.preventDefault();
								setSelectedImage(image.src);
							}
						}}
						aria-label={`查看图片: ${image.alt}`}
					>
						<Image
							src={image.src}
							alt={image.alt}
							fill
							className="object-cover transition-transform duration-300 group-hover:scale-105"
							sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
						/>
						<div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

						{/* 放大镜图标 */}
						<div className="absolute top-3 right-3 bg-white/20 backdrop-blur-sm border border-white/30 rounded-full p-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
							<ZoomIn className="w-4 h-4 text-white" />
						</div>

						{/* 图片信息 */}
						{(image.title || image.description) && (
							<div className="absolute bottom-0 left-0 right-0 p-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
								{image.title && (
									<h3 className="font-semibold mb-1 drop-shadow-lg">
										{image.title}
									</h3>
								)}
								{image.description && (
									<p className="text-sm text-white/90 drop-shadow-md">
										{image.description}
									</p>
								)}
							</div>
						)}
					</button>
				))}
			</div>

			{/* 全屏预览模态框 */}
			{selectedImage && (
				<div
					className="fixed inset-0 z-50 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4"
					onClick={() => setSelectedImage(null)}
				>
					<div className="relative max-w-4xl max-h-[90vh] w-full h-full">
						<Image
							src={selectedImage}
							alt="预览图片"
							fill
							className="object-contain"
							sizes="(max-width: 768px) 100vw, 80vw"
						/>
						<button
							onClick={() => setSelectedImage(null)}
							className="absolute top-4 right-4 bg-white/20 hover:bg-white/30 backdrop-blur-sm border border-white/30 rounded-full p-2 text-white transition-all duration-200"
						>
							<svg
								className="w-6 h-6"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M6 18L18 6M6 6l12 12"
								/>
							</svg>
						</button>
					</div>
				</div>
			)}
		</>
	);
}
