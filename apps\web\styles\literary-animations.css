/* 文艺清新风格动画 */

@keyframes elegant-fade-in {
	0% {
		opacity: 0;
		transform: translateY(8px);
	}
	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes gentle-pulse {
	0%,
	100% {
		opacity: 0.8;
		transform: scale(1);
	}
	50% {
		opacity: 1;
		transform: scale(1.05);
	}
}

@keyframes light-shimmer {
	0% {
		background-position: -200% 0;
	}
	100% {
		background-position: 200% 0;
	}
}

/* 标题优雅渐入效果 */
.animate-elegant-title {
	animation: elegant-fade-in 1.2s cubic-bezier(0.22, 1, 0.36, 1);
}

/* 内容文字动画 */
.animate-content-appear {
	opacity: 0;
	transform: translateY(12px);
	animation: elegant-fade-in 0.8s cubic-bezier(0.22, 1, 0.36, 1) forwards;
	animation-delay: 0.3s;
}

/* 卡片渐入动画 */
.card-enter {
	opacity: 0;
	transform: translateY(15px);
}

.card-enter-active {
	opacity: 1;
	transform: translateY(0);
	transition: opacity 0.5s cubic-bezier(0.22, 1, 0.36, 1), transform 0.5s
		cubic-bezier(0.22, 1, 0.36, 1);
}

/* 诗意渐变效果 */
.gradient-title {
	background: linear-gradient(120deg, #3b82f6, #60a5fa, #93c5fd);
	background-size: 200% auto;
	background-clip: text;
	-webkit-background-clip: text;
	color: transparent;
	animation: light-shimmer 8s linear infinite;
}

/* 按钮悬浮效果 */
.diary-button {
	transition: all 0.3s cubic-bezier(0.22, 1, 0.36, 1);
}

.diary-button:hover {
	transform: translateY(-2px);
	box-shadow: 0 5px 15px -5px rgba(59, 130, 246, 0.3);
}

/* 自然呼吸效果 - 用于装饰元素 */
.breathing-decoration {
	animation: gentle-pulse 6s ease-in-out infinite;
}

/* 文字渐隐渐显动画 */
@keyframes text-fade-cycle {
	0%,
	100% {
		opacity: 0.5;
	}
	50% {
		opacity: 1;
	}
}

.text-cycle {
	animation: text-fade-cycle 8s ease-in-out infinite;
}

/* 水墨展开效果 */
@keyframes ink-expand {
	0% {
		opacity: 0;
		filter: blur(10px);
		transform: scale(0.9);
	}
	100% {
		opacity: 1;
		filter: blur(0);
		transform: scale(1);
	}
}

.ink-expanding {
	animation: ink-expand 1.8s cubic-bezier(0.19, 1, 0.22, 1);
}

/* 笔触动画效果 */
@keyframes brush-stroke {
	0% {
		width: 0;
		opacity: 0;
	}
	50% {
		opacity: 1;
	}
	100% {
		width: 100%;
		opacity: 0.8;
	}
}

.brush-line {
	position: relative;
}

.brush-line::after {
	content: "";
	position: absolute;
	bottom: -4px;
	left: 0;
	height: 2px;
	width: 0;
	background: linear-gradient(
		to right,
		transparent,
		rgba(147, 197, 253, 0.6),
		transparent
	);
	animation: brush-stroke 3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 文字轻微浮动效果 */
@keyframes gentle-float {
	0%,
	100% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-4px);
	}
}

.floating-text {
	animation: gentle-float 4s ease-in-out infinite;
	display: inline-block;
}
