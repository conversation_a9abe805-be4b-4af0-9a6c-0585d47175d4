"use client";

// 注意：需要安装 @fancyapps/ui
// 导入 Fancybox CSS 和 JS
import { Fancybox } from "@fancyapps/ui";
import "@fancyapps/ui/dist/fancybox/fancybox.css";
import {
	NodeViewContent,
	type NodeViewProps,
	NodeViewWrapper,
} from "@tiptap/react";
import { Calendar as CalendarComponent } from "@ui/components/calendar";
import { Input } from "@ui/components/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import {
	Calendar,
	CalendarDays,
	Copy,
	GripVertical,
	Image,
	Loader2,
	MapIcon,
	MapPin,
	Settings,
	Trash2,
	XCircle,
} from "lucide-react";
import React, { useState, useEffect, useRef } from "react";
import { toast } from "sonner";

export const TravelPointNodeView: React.FC<NodeViewProps> = ({
	node,
	updateAttributes,
	editor,
	getPos,
}) => {
	console.log("[DEBUG] TravelPointNodeView 渲染", {
		pointId: node.attrs.pointId,
		location: node.attrs.location,
		timestamp: new Date().toISOString(),
		editorDestroyed: editor.isDestroyed,
		nodeSize: node.nodeSize,
		imagesCount: Array.isArray(node.attrs.images)
			? node.attrs.images.length
			: 0,
	});

	const pointId = node.attrs.pointId || null;
	const location = node.attrs.location || "";
	const pointDate = node.attrs.pointDate || "";
	const initialImages = Array.isArray(node.attrs.images)
		? node.attrs.images
		: [];
	const [showImageForm, setShowImageForm] = useState(false);
	const [images, setImages] = useState<string[]>(initialImages);
	const [isUploading, setIsUploading] = useState(false);
	const [uploadingFiles, setUploadingFiles] = useState<
		Record<string, boolean>
	>({});
	const [date, setDate] = useState<Date | undefined>(
		pointDate ? new Date(pointDate) : new Date(),
	);
	const [isCalendarOpen, setIsCalendarOpen] = useState(false);

	// 用于初始化Fancybox
	const fancyboxRef = useRef<HTMLDivElement>(null);

	// 组件挂载时的调试日志
	useEffect(() => {
		console.log("[DEBUG] TravelPointNodeView 组件挂载", {
			pointId,
			location,
			timestamp: new Date().toISOString(),
		});

		return () => {
			console.log("[DEBUG] TravelPointNodeView 组件卸载", {
				pointId,
				location,
				timestamp: new Date().toISOString(),
			});
		};
	}, []);

	// 格式化日期显示
	const displayDate = formatDate(pointDate);

	// 当节点属性中的图片数组发生变化时更新本地状态
	useEffect(() => {
		console.log("[DEBUG] TravelPointNodeView 图片数组更新", {
			pointId,
			newImagesCount: Array.isArray(node.attrs.images)
				? node.attrs.images.length
				: 0,
			timestamp: new Date().toISOString(),
		});

		if (node.attrs.images && Array.isArray(node.attrs.images)) {
			setImages(node.attrs.images);
		}
	}, [node.attrs.images, pointId]);

	// 更新日期属性
	useEffect(() => {
		console.log("[DEBUG] TravelPointNodeView 日期更新", {
			pointId,
			newDate: date?.toISOString(),
			timestamp: new Date().toISOString(),
		});

		if (date) {
			updateAttributes({ pointDate: date.toISOString() });
		}
	}, [date, updateAttributes, pointId]);

	// 初始化Fancybox
	useEffect(() => {
		// 这里不需要依赖fancyboxRef，因为我们使用选择器，不是DOM引用
		const initFancybox = () => {
			if (images.length > 0) {
				try {
					// 确保fancybox在DOM准备好后初始化
					Fancybox.unbind("[data-fancybox='travel-point-images']");

					Fancybox.bind("[data-fancybox='travel-point-images']", {
						// 最小配置，减少出错可能性
						Image: {
							zoom: true,
						},
					});
					console.log("Fancybox initialized for travel points");
				} catch (error) {
					console.error("Fancybox初始化错误:", error);
				}
			}
		};

		// 使用一个小延迟确保DOM已完全渲染
		const timer = setTimeout(initFancybox, 100);

		// 当组件卸载时清理
		return () => {
			clearTimeout(timer);
			try {
				Fancybox.unbind("[data-fancybox='travel-point-images']");
				Fancybox.close();
			} catch (error) {
				console.error("Fancybox清理错误:", error);
			}
		};
	}, [images.length]);

	// 处理图片添加到编辑器
	const handleAddImage = (imageUrl: string) => {
		editor.chain().focus().setImage({ src: imageUrl }).run();
	};

	const handleLocationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		updateAttributes({ location: e.target.value });
	};

	// 删除当前点位
	const handleDelete = () => {
		if (typeof getPos === "function") {
			editor.chain().focus().deleteNode("travelPoint").run();
		}
	};

	// 复制点位
	const handleDuplicate = () => {
		if (typeof getPos === "function") {
			const pos = getPos();
			const duplicatedNode = {
				...node.toJSON(),
				attrs: {
					...node.attrs,
					pointId: `${node.attrs.pointId}_copy_${Date.now()}`,
					location: `${node.attrs.location} (副本)`,
				},
			};

			editor
				.chain()
				.focus()
				.insertContentAt(pos + node.nodeSize, duplicatedNode)
				.run();
		}
	};

	// 快速设置点位属性
	const handleQuickSettings = () => {
		const newLocation = window.prompt("修改地点名称", location);
		if (newLocation !== null) {
			updateAttributes({ location: newLocation });
		}
	};

	// 处理图片上传
	const handleImageUpload = async (
		e: React.ChangeEvent<HTMLInputElement>,
	) => {
		if (!e.target.files || e.target.files.length === 0) return;

		const file = e.target.files[0];
		setIsUploading(true);

		try {
			// 检查文件大小，限制在5MB以内
			if (file.size > 5 * 1024 * 1024) {
				toast.error("图片大小不能超过5MB");
				return;
			}

			// 创建临时预览URL和临时ID
			const tempId = `${file.name}-${Date.now()}`;
			const previewUrl = URL.createObjectURL(file);

			// 标记此临时ID为正在上传状态
			setUploadingFiles((prev) => ({ ...prev, [tempId]: true }));

			// 先添加预览图片到本地状态
			const newImages = [...images, previewUrl];
			setImages(newImages);

			// 更新节点属性中的图片数组
			updateAttributes({ images: newImages });

			// 1. 获取预签名 URL 和永久 URL
			const response = await fetch("/api/storage/upload", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					filename: file.name,
					contentType: file.type,
				}),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(
					`获取上传URL失败: ${errorData.error || response.statusText}`,
				);
			}

			const { uploadUrl, permanentUrl } = await response.json();

			if (!uploadUrl || !permanentUrl) {
				throw new Error("API响应缺少 uploadUrl 或 permanentUrl");
			}

			// 2. 上传文件到存储服务
			const uploadResponse = await fetch(uploadUrl, {
				method: "PUT",
				headers: {
					"Content-Type": file.type,
				},
				body: file,
			});

			if (!uploadResponse.ok) {
				throw new Error(`图片上传失败: ${uploadResponse.statusText}`);
			}

			// 标记此文件上传完成
			setUploadingFiles((prev) => ({ ...prev, [tempId]: false }));

			// 3. 替换临时预览URL为服务器返回的永久URL
			const updatedImages = images.map((img) =>
				img === previewUrl ? permanentUrl : img,
			);
			setImages(updatedImages);
			updateAttributes({ images: updatedImages });

			setShowImageForm(false);
			toast.success("图片已上传");
		} catch (error) {
			console.error("图片上传失败:", error);
			toast.error("图片上传失败，请重试");

			// 移除临时预览
			const filteredImages = images.filter(
				(img) => !img.startsWith("blob:"),
			);
			setImages(filteredImages);
			updateAttributes({ images: filteredImages });
		} finally {
			setIsUploading(false);
			e.target.value = "";
		}
	};

	// 删除图片
	const handleRemoveImage = (index: number) => {
		const newImages = [...images];
		newImages.splice(index, 1);
		setImages(newImages);
		updateAttributes({ images: newImages });
	};

	// 打开地图位置选择器（占位功能）
	const handleOpenMap = () => {
		// 这里将来可以集成实际的地图选择功能
		toast.info("地图位置选择功能即将实现");
	};

	// 添加键盘事件处理函数
	const handleDateKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter" || e.key === " ") {
			setIsCalendarOpen(true);
		}
	};

	return (
		<NodeViewWrapper className="travel-point-node my-4 p-4 border border-blue-200 rounded-lg bg-blue-50 hover:shadow-md transition-all duration-200 group">
			{/* 顶部工具栏 */}
			<div className="flex justify-between items-center mb-2">
				{/* 拖拽手柄 */}
				<div
					className="drag-handle opacity-0 group-hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing p-1 hover:bg-blue-200 rounded"
					data-drag-handle
					title="拖拽移动点位"
				>
					<GripVertical className="h-4 w-4 text-gray-500" />
				</div>

				{/* 操作按钮组 */}
				<div className="flex text-gray-400 gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
					<button
						onClick={() => setShowImageForm(!showImageForm)}
						className={cn(
							"p-1 rounded hover:bg-blue-100 transition-colors",
							showImageForm ? "bg-blue-100 text-blue-600" : "",
						)}
						title="添加图片"
						type="button"
					>
						<Image className="h-4 w-4" />
					</button>
					<button
						onClick={handleOpenMap}
						className="p-1 rounded hover:bg-blue-100 transition-colors"
						title="选择位置"
						type="button"
					>
						<MapIcon className="h-4 w-4" />
					</button>
					<Popover
						open={isCalendarOpen}
						onOpenChange={setIsCalendarOpen}
					>
						<PopoverTrigger asChild>
							<button
								className="p-1 rounded hover:bg-blue-100 transition-colors"
								title="选择日期"
								type="button"
							>
								<Calendar className="h-4 w-4" />
							</button>
						</PopoverTrigger>
						<PopoverContent className="w-auto p-0" align="end">
							<CalendarComponent
								mode="single"
								selected={date}
								onSelect={(newDate) => {
									setDate(newDate);
									setIsCalendarOpen(false);
								}}
								initialFocus
							/>
						</PopoverContent>
					</Popover>
					<button
						onClick={handleDuplicate}
						className="p-1 rounded hover:bg-green-100 text-green-600 transition-colors"
						title="复制点位"
						type="button"
					>
						<Copy className="h-4 w-4" />
					</button>
					<button
						onClick={handleQuickSettings}
						className="p-1 rounded hover:bg-gray-100 transition-colors"
						title="点位设置"
						type="button"
					>
						<Settings className="h-4 w-4" />
					</button>
					<button
						onClick={handleDelete}
						className="p-1 rounded hover:bg-red-100 text-red-500 transition-colors"
						title="删除点位"
						type="button"
					>
						<Trash2 className="h-4 w-4" />
					</button>
				</div>
			</div>

			{/* 标题和日期区域 */}
			<div className="flex items-start gap-2 mb-2">
				<MapPin className="h-5 w-5 text-blue-600 mt-1 flex-shrink-0" />
				<Input
					type="text"
					value={location}
					onChange={handleLocationChange}
					placeholder="地点名称"
					className="text-md font-medium flex-grow bg-transparent border-0 shadow-none p-0 focus:ring-0 focus:border-b focus:border-blue-400 disabled:opacity-70"
					disabled={!editor.isEditable}
				/>
			</div>
			<div className="flex items-center gap-2 mb-2 text-sm text-gray-600 ml-7">
				<CalendarDays className="h-4 w-4 text-gray-500 flex-shrink-0" />
				<button
					className={cn(
						"text-xs bg-transparent p-0 cursor-pointer border-0 text-left",
						isCalendarOpen ? "text-blue-600" : "text-gray-600",
					)}
					onClick={() => setIsCalendarOpen(true)}
					onKeyDown={handleDateKeyDown}
					aria-label="选择日期"
					type="button"
				>
					{displayDate}
				</button>
			</div>

			{/* 图片上传表单 */}
			{showImageForm && (
				<div className="mb-3 ml-7 p-2 bg-white/50 rounded border border-blue-100">
					<div className="text-sm font-medium mb-1">添加图片</div>
					<div className="flex gap-2 items-center">
						<Input
							type="file"
							className="text-xs flex-grow"
							accept="image/*"
							onChange={handleImageUpload}
							disabled={isUploading}
						/>
						{isUploading && (
							<Loader2 className="h-4 w-4 animate-spin text-blue-500" />
						)}
					</div>
					{Object.keys(uploadingFiles).length > 0 && (
						<p className="text-xs text-blue-500 mt-1">
							正在上传图片，请稍候...
						</p>
					)}
				</div>
			)}

			{/* 图片预览区域 - 使用Fancybox */}
			{images.length > 0 && (
				<div
					ref={fancyboxRef}
					className={cn(
						"ml-7 mb-3",
						images.length > 1 ? "grid gap-2" : "block",
						images.length === 2
							? "grid-cols-2"
							: images.length === 3
								? "grid-cols-3"
								: images.length > 3
									? "grid-cols-4 md:grid-cols-5"
									: "",
					)}
				>
					{images.map((img, i) => (
						<div
							key={i}
							className={cn(
								"relative rounded overflow-hidden group",
								images.length === 1
									? "max-w-full max-h-[300px]"
									: "aspect-square",
							)}
						>
							{/* 使用简单的链接结构，降低错误可能性 */}
							<a
								href={img}
								data-fancybox="travel-point-images"
								data-caption={`${location} - 图片 ${i + 1}`}
								className="block w-full h-full"
							>
								<img
									src={img}
									alt={`${location} - 图片 ${i + 1}`}
									loading="lazy"
									className={cn(
										"rounded shadow-sm transition-shadow hover:shadow-md",
										images.length === 1
											? "object-contain max-h-[300px] max-w-full"
											: "w-full h-full object-cover",
									)}
									onError={(e) => {
										// 图片加载失败时替换为替代文本
										console.error(`图片加载失败: ${img}`);
										(e.target as HTMLImageElement).alt =
											"图片加载失败";
										// 添加一个灰色背景
										(
											e.target as HTMLImageElement
										).style.backgroundColor = "#f3f4f6";
									}}
								/>
							</a>

							{/* 将删除按钮放到链接外部，避免冲突 */}
							<button
								onClick={() => handleRemoveImage(i)}
								className="absolute top-1 right-1 bg-white/70 rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-100"
								title="删除图片"
								type="button"
							>
								<XCircle className="h-4 w-4 text-red-500" />
							</button>
						</div>
					))}
				</div>
			)}

			{/* 内容区域 */}
			<div className="travel-point-content ml-7">
				<NodeViewContent className="content" />
			</div>
		</NodeViewWrapper>
	);
};

// 格式化日期显示的辅助函数
function formatDate(dateString: string): string {
	try {
		if (!dateString) return "";

		const date = new Date(dateString);
		if (Number.isNaN(date.getTime())) return "";

		return format(date, "yyyy年MM月dd日");
	} catch (error) {
		console.error("日期格式化错误:", error);
		return "";
	}
}
