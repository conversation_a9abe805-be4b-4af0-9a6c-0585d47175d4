/**
 * 提示词配置接口
 */
export interface PromptOptions {
	/** 系统提示词 - 定义AI的角色和行为 */
	systemPrompt?: string;
	/** 其他可选提示词配置参数 */
	[key: string]: any;
}

/**
 * 生成产品名称的提示词
 *
 * @param {string} topic - 要为其生成产品名称的主题
 * @param {PromptOptions} options - 提示词选项，包括系统提示词
 * @return {object} 包含用户提示和系统提示的对象
 */
export function promptListProductNames(topic: string, options?: PromptOptions) {
	return {
		systemPrompt:
			options?.systemPrompt ||
			"你是一个创意产品命名专家，擅长创造有趣且吸引人的产品名称。",
		userPrompt: `为${topic}列出五个有趣的产品名称。`,
	};
}

/**
 * 生成旅行计划的提示词
 *
 * @param {string} destination - 目的地
 * @param {number} days - 天数
 * @param {string[]} interests - 兴趣爱好
 * @param {PromptOptions} options - 提示词选项，包括系统提示词
 * @return {object} 包含用户提示和系统提示的对象
 */
export function promptTravelPlan(
	destination: string,
	days: number,
	interests: string[] = [],
	options?: PromptOptions,
) {
	const interestsText =
		interests.length > 0
			? `，我对以下内容特别感兴趣：${interests.join("、")}`
			: "";

	return {
		systemPrompt:
			options?.systemPrompt ||
			"你是一位专业的旅行规划师，擅长根据客户需求制定个性化的旅行计划。",
		userPrompt: `请为我规划一个${days}天的${destination}旅行计划${interestsText}。`,
	};
}

/**
 * 生成内容摘要的提示词
 *
 * @param {string} content - 需要摘要的内容
 * @param {PromptOptions} options - 提示词选项，包括系统提示词
 * @return {object} 包含用户提示和系统提示的对象
 */
export function promptSummarize(content: string, options?: PromptOptions) {
	return {
		systemPrompt:
			options?.systemPrompt ||
			"你是一位专业的内容编辑，擅长提取文章的关键信息并生成简洁明了的摘要。",
		userPrompt: `请为以下内容生成一个简洁的摘要：\n\n${content}`,
	};
}
