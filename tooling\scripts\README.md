# Scripts 工具集

这个目录包含各种有用的脚本工具。

## Google Play 评论爬虫

### 🎯 推荐方案：使用开源库（最新）

我们现在使用 [google-play-scraper](https://github.com/facundoolano/google-play-scraper) 开源库，这是最稳定、最高效的方案：

**优势：**
- ✅ **高效快速** - 无需启动浏览器，纯API调用
- ✅ **稳定可靠** - 专业维护的开源库，有社区支持
- ✅ **功能完整** - 支持分页、节流、多种排序方式
- ✅ **数据丰富** - 包含评论ID、开发者回复等完整信息
- ✅ **不易被封** - 模拟真实API请求，降低被检测风险

### 🚀 推荐使用方法

```bash
# 从根目录运行（推荐）
pnpm run google-play-crawler com.polarsteps

# 从 tooling/scripts 目录运行
pnpm run google-play-scraper com.polarsteps

# 指定抓取数量（默认500条）
pnpm run google-play-scraper com.polarsteps 1000

# 指定输出文件名
pnpm run google-play-scraper com.polarsteps 500 my-reviews.csv
```

### 📦 备用方案：浏览器自动化版本

如果开源库遇到问题，可以使用基于 Puppeteer 的备用版本：

**google-play-crawler-final** - 健壮的浏览器版本（备用方案）

```bash
# 备用方案
pnpm run google-play-crawler-final com.polarsteps
```

### 功能特性

#### 开源库版本特性
- 🚀 **超快速度** - 无浏览器开销，秒级响应
- 📊 **大量数据** - 支持抓取数千条评论
- 🔄 **智能分页** - 自动处理分页，支持继续抓取
- 💬 **完整信息** - 评论ID、开发者回复、有用数等
- 🛡️ **内置节流** - 防止请求过快被封IP
- 📈 **详细统计** - 自动生成评分分布和统计信息

#### 通用特性
- 📋 导出为 CSV 格式文件（中文标题）
- 📈 自动生成评论统计信息
- 🔧 支持自定义输出文件名
- 🌍 支持多语言和地区设置

### 使用方法

#### 基本用法（开源库版本）

```bash
# 抓取 Polarsteps 应用的评论（默认500条）
pnpm run google-play-crawler com.polarsteps

# 抓取更多评论
pnpm run google-play-scraper com.polarsteps 1000

# 抓取其他应用
pnpm run google-play-scraper com.whatsapp 200
pnpm run google-play-scraper com.instagram.android 300

# 指定输出文件名
pnpm run google-play-scraper com.polarsteps 500 polarsteps-reviews.csv
```

#### 如果开源库有问题，使用备用方案

```bash
# 尝试最健壮的浏览器版本
pnpm run google-play-crawler-final com.polarsteps
```

### 输出格式

#### 开源库版本 CSV 字段：

| 字段 | 描述 |
|------|------|
| 评论ID | 唯一的评论标识符 |
| 评论者 | 评论用户的姓名 |
| 评分 | 1-5 星评分 |
| 日期 | 评论发布日期 |
| 评论内容 | 评论的具体内容 |
| 有用数 | 其他用户觉得有用的数量 |
| 开发者回复 | 开发者的回复内容（如果有） |
| 开发者回复日期 | 开发者回复的日期（如果有） |

### 输出目录

- CSV 文件会保存在 `output/` 目录中
- 如果目录不存在，会自动创建
- 文件名格式：`google-play-reviews-lib-{时间戳}.csv`

### 版本对比

#### google-play-scraper（开源库 - 推荐）
- **特点**: 最快、最稳定、最可靠
- **适用场景**: 大部分情况下的首选方案
- **数据质量**: 最高，包含完整的评论信息
- **速度**: 极快，无浏览器开销
- **抓取量**: 支持数千条评论
- **维护状态**: 活跃维护的开源项目

#### google-play-crawler-final（浏览器版本）
- **特点**: 最健壮的浏览器自动化版本
- **适用场景**: 开源库不可用时的备用方案
- **数据质量**: 高，但可能受页面结构变化影响
- **速度**: 较慢，需要启动浏览器
- **抓取量**: 受页面加载限制

### 最新测试结果

**开源库版本测试（推荐）：**
- ✅ 成功抓取 50 条评论
- ✅ 平均评分: 4.66 分
- ✅ 88% 的评论包含开发者回复
- ✅ 完整的评论ID和时间戳
- ✅ 执行时间：< 30 秒

**浏览器版本测试：**
- ✅ 成功找到并点击"See all reviews"按钮
- ✅ 提取了 28 条高质量评论
- ✅ 过滤掉了无关的应用信息数据
- ✅ 执行时间：2-3 分钟

### 应用 ID 查找

要找到应用的 ID（包名），访问 Google Play 中的应用页面，URL 中 `id=` 后面的部分就是应用 ID。

例如：
- `https://play.google.com/store/apps/details?id=com.polarsteps`
- 应用 ID 为：`com.polarsteps`

### 常见应用 ID

```bash
# 社交媒体
pnpm run google-play-scraper com.whatsapp 200          # WhatsApp
pnpm run google-play-scraper com.instagram.android 300 # Instagram
pnpm run google-play-scraper com.facebook.katana 200   # Facebook

# 旅游
pnpm run google-play-scraper com.polarsteps 500        # Polarsteps
pnpm run google-play-scraper com.tripadvisor.tripadvisor 400 # TripAdvisor

# 游戏
pnpm run google-play-scraper com.king.candycrushsaga 300 # Candy Crush
```

### 故障排除

#### 开源库版本
1. **请求失败**: 检查网络连接和应用ID是否正确
2. **被限流**: 库内置了节流机制，会自动重试
3. **数据异常**: 尝试调整抓取数量或检查应用是否存在

#### 浏览器版本
1. **页面加载失败**: 检查网络连接和应用ID是否正确
2. **没有找到评论**: 尝试不同版本的爬虫或使用调试模式
3. **爬取中断**: 可以重新运行脚本，会生成新的文件

### 参数说明

#### 开源库版本参数
```bash
pnpm run google-play-scraper [appId] [maxReviews] [filename]
```
- `appId`: 应用ID（必须）
- `maxReviews`: 最大抓取数量（可选，默认500）
- `filename`: 输出文件名（可选，自动生成）

### 性能对比

| 特性 | 开源库版本 | 浏览器版本 |
|------|-----------|-----------|
| 速度 | ⭐⭐⭐⭐⭐ | ⭐⭐ |
| 稳定性 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| 数据质量 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| 资源消耗 | ⭐⭐⭐⭐⭐ | ⭐⭐ |
| 抓取量 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |

### 法律声明

使用此爬虫时请遵守：
- Google Play 的服务条款
- 相关法律法规
- 网站的 robots.txt 文件
- 合理使用原则（不要过度频繁请求）

此工具仅供学习和研究目的使用。

### 依赖项

**开源库版本：**
- `google-play-scraper`: Google Play 数据抓取核心库
- `csv-writer`: 生成 CSV 文件
- `dotenv-cli`: 环境变量支持

**浏览器版本：**
- `puppeteer`: 控制浏览器进行网页自动化
- `csv-writer`: 生成 CSV 文件
- `dotenv-cli`: 环境变量支持 