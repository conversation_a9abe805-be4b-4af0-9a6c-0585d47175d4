"use client";
import type { TravelPointImage } from "@repo/database/src/types/travel-diary";
import { cn } from "@ui/lib";
import { extractImageExifData } from "@utils/lib/image-exif";
import { Edit3, Upload, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import type { FormFieldProps } from "./types";

import {
	DndContext,
	type DragEndEvent,
	KeyboardSensor,
	PointerSensor,
	closestCenter,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import {
	SortableContext,
	arrayMove,
	rectSortingStrategy, // Using rectSortingStrategy for grid layout
	sortableKeyboardCoordinates,
	useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Button } from "@ui/components/button";
import {
	Dialog,
	DialogContent,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { Input } from "@ui/components/input";
import { Textarea } from "@ui/components/textarea";

// 图片编辑对话框组件
interface ImageEditDialogProps {
	isOpen: boolean;
	onClose: () => void;
	image: TravelPointImage;
	onSave: (updatedImage: TravelPointImage) => void;
}

function ImageEditDialog({
	isOpen,
	onClose,
	image,
	onSave,
}: ImageEditDialogProps) {
	const t = useTranslations();
	const [editingImage, setEditingImage] = useState<TravelPointImage>(image);

	useEffect(() => {
		setEditingImage(image);
	}, [image]);

	const handleSave = () => {
		onSave(editingImage);
		onClose();
	};

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-md">
				<DialogHeader>
					<DialogTitle>编辑图片信息</DialogTitle>
				</DialogHeader>
				<div className="space-y-4">
					<div className="aspect-video w-full overflow-hidden rounded-md">
						<img
							src={editingImage.url}
							alt={editingImage.alt || "图片预览"}
							className="h-full w-full object-cover"
						/>
					</div>
					<div className="space-y-3">
						<div>
							<label
								htmlFor="image-caption"
								className="text-sm font-medium"
							>
								图片标题
							</label>
							<Input
								id="image-caption"
								value={editingImage.caption || ""}
								onChange={(e) =>
									setEditingImage((prev) => ({
										...prev,
										caption: e.target.value,
									}))
								}
								placeholder="为图片添加标题..."
							/>
						</div>
						<div>
							<label
								htmlFor="image-description"
								className="text-sm font-medium"
							>
								图片描述
							</label>
							<Textarea
								id="image-description"
								value={editingImage.description || ""}
								onChange={(e) =>
									setEditingImage((prev) => ({
										...prev,
										description: e.target.value,
									}))
								}
								placeholder="描述这张图片..."
								rows={3}
							/>
						</div>
						<div>
							<label
								htmlFor="image-alt"
								className="text-sm font-medium"
							>
								Alt文本
							</label>
							<Input
								id="image-alt"
								value={editingImage.alt || ""}
								onChange={(e) =>
									setEditingImage((prev) => ({
										...prev,
										alt: e.target.value,
									}))
								}
								placeholder="图片的替代文本..."
							/>
						</div>
					</div>
					<div className="flex justify-end gap-2">
						<Button variant="outline" onClick={onClose}>
							取消
						</Button>
						<Button onClick={handleSave}>保存</Button>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}

// SortableImage Item Component - 更新为处理图片对象
interface SortableImageProps {
	id: string;
	image: TravelPointImage;
	onRemove: (id: string, e: React.MouseEvent) => void;
	onEdit: (image: TravelPointImage) => void;
	isUploading: boolean;
}

function SortableImage({
	id,
	image,
	onRemove,
	onEdit,
	isUploading,
}: SortableImageProps) {
	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
	} = useSortable({ id });

	const style = {
		transform: CSS.Transform.toString(transform),
		transition: transition || undefined,
		zIndex: isDragging ? 10 : undefined,
		touchAction: "manipulation",
	};

	return (
		<div
			ref={setNodeRef}
			style={style}
			{...attributes}
			{...listeners}
			className="relative group aspect-square"
		>
			<img
				src={image.url}
				alt={image.alt || `上传预览 ${id}`}
				className="h-full w-full object-cover rounded-md"
				onError={(e) => {
					(e.target as HTMLImageElement).src =
						"/images/placeholder.png";
					(e.target as HTMLImageElement).alt = "图片加载失败";
				}}
			/>
			<div
				className={cn(
					"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all rounded-md",
					isDragging && "opacity-50",
				)}
			/>
			{/* 图片信息显示 */}
			{(image.caption || image.description) && (
				<div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-2 rounded-b-md">
					{image.caption && (
						<p className="text-white text-xs font-medium truncate">
							{image.caption}
						</p>
					)}
					{image.description && (
						<p className="text-white/80 text-xs truncate">
							{image.description}
						</p>
					)}
				</div>
			)}
			{/* 操作按钮 */}
			<div className="absolute top-1 right-1 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
				<button
					type="button"
					onClick={(e) => {
						e.stopPropagation();
						onEdit(image);
					}}
					className="bg-black/60 text-white rounded-full p-1.5 hover:bg-black/80 disabled:opacity-50 disabled:cursor-not-allowed"
					title="编辑图片信息"
					disabled={isUploading}
				>
					<Edit3 size={12} />
				</button>
				<button
					type="button"
					onClick={(e) => onRemove(id, e)}
					className="bg-black/60 text-white rounded-full p-1.5 hover:bg-black/80 disabled:opacity-50 disabled:cursor-not-allowed"
					title="删除图片"
					disabled={isUploading}
				>
					<X size={12} />
				</button>
			</div>
		</div>
	);
}

export function ImageUploader({ formData, setFormData }: FormFieldProps) {
	const t = useTranslations();
	const [isDraggingOverDropzone, setIsDraggingOverDropzone] = useState(false); // Renamed from isDragging to avoid conflict
	const fileInputRef = useRef<HTMLInputElement>(null);
	// 使用本地状态跟踪上传的图片，现在存储的是 permanentUrl
	const [localImages, setLocalImages] = useState<TravelPointImage[]>(
		formData.images || [],
	);
	// 增加上传状态跟踪
	const [uploadingFiles, setUploadingFiles] = useState<
		Record<string, boolean>
	>({}); // { [tempId]: true/false }
	const [editingImage, setEditingImage] = useState<TravelPointImage | null>(
		null,
	);
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

	// 当父组件传入的 formData.images 变化时，更新本地状态
	// 保持这个 useEffect，以便在编辑现有条目时加载初始图片
	useEffect(() => {
		console.log("formData.images 更新:", formData.images);
		setLocalImages(formData.images || []);
	}, [formData.images]);

	// 处理图片添加到表单
	const addImagesToForm = useCallback(
		async (files: FileList | File[]) => {
			if (!files.length) {
				console.log("没有选择文件");
				return;
			}

			console.log("选择了文件:", files.length, "个");

			const filesArray = Array.from(files);

			// 提取图片EXIF数据（位置和时间信息） - 保持不变
			let exifDataExtracted = false;
			if (filesArray.length > 0) {
				try {
					const exifData = await extractImageExifData(filesArray[0]);
					console.log("提取到的EXIF数据:", exifData);

					if (exifData.location && !formData.coordinates) {
						setFormData((prevData) => ({
							...prevData,
							coordinates: exifData.location,
						}));
						if (window.google?.maps) {
							try {
								const geocoder =
									new window.google.maps.Geocoder();
								const response = await geocoder.geocode({
									location: exifData.location,
								});
								if (response.results?.[0]?.formatted_address) {
									setFormData((prevData) => ({
										...prevData,
										location:
											response.results[0]
												.formatted_address,
									}));
									toast.success("已从图片提取位置信息");
									exifDataExtracted = true;
								}
							} catch (error) {
								console.error("反向地理编码出错:", error);
							}
						}
					}

					if (exifData.dateTime && !formData.date) {
						setFormData((prevData) => ({
							...prevData,
							date: exifData.dateTime,
						}));
						toast.success("已从图片提取时间信息");
						exifDataExtracted = true;
					}
				} catch (error) {
					console.error("提取图片EXIF数据时出错:", error);
				}
			}

			// --- 开始上传流程 ---
			const uploadPromises = filesArray.map(async (file) => {
				const tempId = `${file.name}-${Date.now()}`; // 临时ID用于跟踪状态
				try {
					setUploadingFiles((prev) => ({ ...prev, [tempId]: true }));

					// 1. 获取预签名 URL 和永久 URL
					const response = await fetch("/api/storage/upload", {
						method: "POST",
						headers: {
							"Content-Type": "application/json",
						},
						body: JSON.stringify({
							filename: file.name,
							contentType: file.type,
						}),
					});

					if (!response.ok) {
						const errorData = await response.json();
						throw new Error(
							`获取上传URL失败: ${errorData.error || response.statusText}`,
						);
					}

					// 获取 uploadUrl 和 permanentUrl
					const { uploadUrl, permanentUrl } = await response.json();

					if (!uploadUrl || !permanentUrl) {
						throw new Error(
							"API响应缺少 uploadUrl 或 permanentUrl",
						);
					}

					// 2. 上传文件到存储服务 (使用 uploadUrl)
					const uploadResponse = await fetch(uploadUrl, {
						method: "PUT",
						headers: {
							"Content-Type": file.type,
						},
						body: file,
					});

					if (!uploadResponse.ok) {
						throw new Error(
							`文件上传失败: ${uploadResponse.statusText}`,
						);
					}

					console.log("文件上传成功，永久URL:", permanentUrl);
					setUploadingFiles((prev) => ({ ...prev, [tempId]: false }));

					// 返回 TravelPointImage 对象而不是字符串
					return {
						url: permanentUrl,
						description: undefined,
						alt: undefined,
						caption: undefined,
						uploadedAt: new Date().toISOString(),
					} as TravelPointImage;
				} catch (error) {
					console.error(`处理文件 ${file.name} 时出错:`, error);
					let errorMessage = `上传 ${file.name} 失败`;
					if (error instanceof Error) {
						errorMessage = `${errorMessage}: ${error.message}`;
					}
					toast.error(errorMessage);
					setUploadingFiles((prev) => ({ ...prev, [tempId]: false }));
					return null; // 返回 null 表示失败
				}
			});

			// 等待所有上传完成
			const results = await Promise.all(uploadPromises);
			const successfulUploadImages = results.filter(
				(img): img is TravelPointImage => img !== null,
			);

			// 更新本地状态和父组件状态
			if (successfulUploadImages.length > 0) {
				const newImages = [
					...(localImages || []),
					...successfulUploadImages,
				];
				setLocalImages(newImages);
				setFormData((prevData) => ({
					...prevData,
					images: newImages,
				}));
			}
		},
		[formData.coordinates, formData.date, setFormData, localImages], // Added localImages to dependency array
	);

	// 处理文件选择
	const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
		console.log("文件选择触发");
		if (!e.target.files?.length) {
			console.log("没有选择文件");
			return;
		}

		e.stopPropagation();
		console.log("选择了文件，数量:", e.target.files.length);

		const filesArray = Array.from(e.target.files);
		e.target.value = "";

		setTimeout(() => {
			addImagesToForm(filesArray);
		}, 50);
	};

	// 处理点击上传区域
	const handleAreaClick = (e: React.MouseEvent) => {
		console.log("点击上传区域");
		e.stopPropagation();

		if (fileInputRef.current) {
			fileInputRef.current.click();
		}
	};

	// Handle keyboard interaction for the dropzone for accessibility
	const handleAreaKeyDown = (e: React.KeyboardEvent<HTMLButtonElement>) => {
		if (e.key === "Enter" || e.key === " ") {
			e.preventDefault();
			if (fileInputRef.current) {
				fileInputRef.current.click();
			}
		}
	};

	// 处理拖拽事件
	const handleDragOverDropzone = useCallback((e: React.DragEvent) => {
		e.preventDefault();
		e.stopPropagation();

		if (e.type === "dragenter" || e.type === "dragover") {
			setIsDraggingOverDropzone(true);
		} else if (e.type === "dragleave" || e.type === "drop") {
			setIsDraggingOverDropzone(false);
		}
	}, []);

	// 处理文件放置
	const handleDropOnDropzone = useCallback(
		(e: React.DragEvent) => {
			e.preventDefault();
			e.stopPropagation();
			setIsDraggingOverDropzone(false);
			console.log("文件拖放触发");

			if (e.dataTransfer.files?.length) {
				console.log("拖放了文件，数量:", e.dataTransfer.files.length);
				const filesArray = Array.from(e.dataTransfer.files);
				addImagesToForm(filesArray);
			}
		},
		[addImagesToForm],
	);

	// 处理移除图片 (by ID/URL)
	const handleRemoveImageById = (
		imageUrlToRemove: string,
		e: React.MouseEvent,
	) => {
		e.stopPropagation();
		console.log("移除图片，URL:", imageUrlToRemove);

		const updatedImages = (localImages || []).filter(
			(img) => img.url !== imageUrlToRemove,
		);

		setLocalImages(updatedImages);
		console.log("移除后的本地图片数组:", updatedImages);

		setFormData((prevData) => ({
			...prevData,
			images: updatedImages,
		}));
	};

	// 处理编辑图片信息
	const handleEditImage = (image: TravelPointImage) => {
		setEditingImage(image);
		setIsEditDialogOpen(true);
	};

	// 保存编辑后的图片信息
	const handleSaveImageEdit = (updatedImage: TravelPointImage) => {
		const updatedImages = localImages.map((img) =>
			img.url === updatedImage.url ? updatedImage : img,
		);

		setLocalImages(updatedImages);
		setFormData((prevData) => ({
			...prevData,
			images: updatedImages,
		}));
	};

	// dnd-kit sensors
	const sensors = useSensors(
		useSensor(PointerSensor, {
			activationConstraint: {
				distance: 5,
			},
		}),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		}),
	);

	// dnd-kit drag end handler
	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;

		if (over && active.id !== over.id) {
			setLocalImages((items) => {
				const oldIndex = items.findIndex(
					(item) => item.url === active.id,
				);
				const newIndex = items.findIndex(
					(item) => item.url === over.id,
				);
				const newOrder = arrayMove(items, oldIndex, newIndex);

				// Update formData as well
				setFormData((prevData) => ({
					...prevData,
					images: newOrder,
				}));
				return newOrder;
			});
		}
	};

	// 这个效果只在组件挂载时运行一次，用于调试
	useEffect(() => {
		console.log("ImageUploader 组件已挂载，初始图片:", formData.images);
	}, [formData.images]);

	const hasImages = localImages.length > 0;
	const isUploadingAnyFile = Object.values(uploadingFiles).some(
		(status) => status === true,
	);

	return (
		<div className="space-y-2">
			<p className="block text-sm font-medium">
				{t("travelMemo.travelPointForm.labels.photos")}
			</p>

			<button
				type="button"
				className={cn(
					"border-2 border-dashed rounded-lg p-4 transition-all cursor-pointer",
					"flex items-center justify-center gap-3 text-center w-full",
					"bg-transparent hover:bg-transparent focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
					isDraggingOverDropzone
						? "border-primary bg-primary/5"
						: "border-muted-foreground/25 hover:border-primary/50",
					hasImages && "pb-3",
					isUploadingAnyFile && "cursor-not-allowed opacity-70",
				)}
				onDragEnter={handleDragOverDropzone}
				onDragOver={handleDragOverDropzone}
				onDragLeave={handleDragOverDropzone}
				onDrop={isUploadingAnyFile ? undefined : handleDropOnDropzone}
				onClick={isUploadingAnyFile ? undefined : handleAreaClick}
				onKeyDown={isUploadingAnyFile ? undefined : handleAreaKeyDown}
			>
				<Upload className="h-5 w-5 text-muted-foreground/70 flex-shrink-0" />
				<div>
					<p className="text-sm">
						{isUploadingAnyFile
							? "正在上传图片..."
							: "拖拽图片到此处上传，或点击上传"}
						<span className="text-xs text-primary/80 block mt-0.5">
							支持JPG、PNG、WEBP格式（将自动提取位置和时间信息）
						</span>
					</p>
				</div>

				<input
					ref={fileInputRef}
					type="file"
					multiple
					accept="image/*"
					className="hidden"
					onChange={handleFileSelect}
					disabled={isUploadingAnyFile}
				/>
			</button>

			{hasImages ? (
				<DndContext
					sensors={sensors}
					collisionDetection={closestCenter}
					onDragEnd={handleDragEnd}
				>
					<SortableContext
						items={localImages.map((img) => img.url)} // 使用URL作为唯一ID
						strategy={rectSortingStrategy}
					>
						<div className="w-full grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 mt-4">
							{localImages.map((image) => (
								<SortableImage
									key={image.url}
									id={image.url} // URL作为唯一ID
									image={image}
									onRemove={handleRemoveImageById}
									onEdit={handleEditImage}
									isUploading={isUploadingAnyFile}
								/>
							))}
						</div>
					</SortableContext>
				</DndContext>
			) : null}

			<p className="text-xs text-gray-500">
				{hasImages ? `已上传 ${localImages.length} 张图片` : ""}
				{isUploadingAnyFile ? (
					<span className="ml-2 text-blue-500">正在上传...</span>
				) : (
					""
				)}
			</p>

			{/* 图片编辑对话框 */}
			{editingImage && (
				<ImageEditDialog
					isOpen={isEditDialogOpen}
					onClose={() => {
						setIsEditDialogOpen(false);
						setEditingImage(null);
					}}
					image={editingImage}
					onSave={handleSaveImageEdit}
				/>
			)}
		</div>
	);
}
