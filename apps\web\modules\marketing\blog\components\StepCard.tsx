import { cn } from "@ui/lib";
import Image from "next/image";

interface StepCardProps {
	step: number;
	title: string;
	description: string;
	image?: string;
	imageAlt?: string;
	className?: string;
}

export function StepCard({
	step,
	title,
	description,
	image,
	imageAlt,
	className,
}: StepCardProps) {
	return (
		<div className={cn("group relative", className)}>
			{/* 背景装饰 */}
			<div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-purple-50/50 dark:from-blue-900/10 dark:to-purple-900/10 rounded-2xl -rotate-1 group-hover:rotate-0 transition-transform duration-300" />

			{/* 主卡片 */}
			<div className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-white/50 dark:border-gray-700/50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
				{/* 步骤编号 */}
				<div className="flex items-center gap-4 mb-4">
					<div className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
						{step}
					</div>
					<h3 className="text-xl font-bold text-gray-800 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">
						{title}
					</h3>
				</div>

				{/* 图片 */}
				{image && (
					<div className="relative mb-4 overflow-hidden rounded-xl bg-gradient-to-br from-blue-50/30 to-purple-50/30 dark:from-blue-900/20 dark:to-purple-900/20">
						<Image
							src={image}
							alt={imageAlt || title}
							width={800}
							height={600}
							className="w-full h-auto transition-transform duration-300 group-hover:scale-105"
							sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
						/>
					</div>
				)}

				{/* 描述 */}
				<p className="text-gray-600 dark:text-gray-300 leading-relaxed">
					{description}
				</p>

				{/* 装饰元素 */}
				<div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
					<div className="w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-pulse" />
				</div>
			</div>
		</div>
	);
}
