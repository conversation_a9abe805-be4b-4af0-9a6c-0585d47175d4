import { Textarea } from "@ui/components/textarea";
import { cn } from "@ui/lib";
import { Check, X } from "lucide-react";
import { useEffect, useRef, useState } from "react";

interface EditableDescriptionProps {
	value: string;
	isEditing: boolean;
	onEnterEdit: () => void;
	onExitEdit: (save: boolean) => void;
	onValueChange: (value: string) => void;
	className?: string;
}

export function EditableDescription({
	value,
	isEditing,
	onEnterEdit,
	onExitEdit,
	onValueChange,
	className,
}: EditableDescriptionProps) {
	const [localValue, setLocalValue] = useState(value);
	const textareaRef = useRef<HTMLTextAreaElement>(null);

	// 当编辑状态改变时，同步本地值并自动聚焦
	useEffect(() => {
		if (isEditing) {
			setLocalValue(value);
			// 延迟聚焦和调整高度
			setTimeout(() => {
				if (textareaRef.current) {
					textareaRef.current.focus();
					textareaRef.current.select();
					// 自动调整高度
					autoResizeTextarea();
				}
			}, 0);
		}
	}, [isEditing, value]);

	// 自动调整textarea高度
	const autoResizeTextarea = () => {
		if (textareaRef.current) {
			textareaRef.current.style.height = "auto";
			textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
		}
	};

	const handleSave = () => {
		onValueChange(localValue.trim());
		onExitEdit(true);
	};

	const handleCancel = () => {
		setLocalValue(value);
		onExitEdit(false);
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		e.stopPropagation(); // 防止触发父组件的键盘事件

		if (e.key === "Enter" && e.ctrlKey) {
			// Ctrl+Enter 保存
			e.preventDefault();
			handleSave();
		} else if (e.key === "Escape") {
			e.preventDefault();
			handleCancel();
		}
	};

	const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
		setLocalValue(e.target.value);
		autoResizeTextarea();
	};

	const handleBlur = (e: React.FocusEvent) => {
		// 检查焦点是否移到了保存/取消按钮
		const relatedTarget = e.relatedTarget as HTMLElement;
		if (relatedTarget?.closest(".edit-actions")) {
			return;
		}
		handleSave();
	};

	if (isEditing) {
		return (
			<div className={cn("w-full", className)}>
				<div className="flex flex-col gap-2">
					<Textarea
						ref={textareaRef}
						value={localValue}
						onChange={handleInputChange}
						onKeyDown={handleKeyDown}
						onBlur={handleBlur}
						placeholder="添加描述..."
						className="min-h-[60px] text-sm resize-none overflow-hidden"
						rows={2}
					/>
					<div className="edit-actions flex items-center gap-2 justify-end">
						<button
							type="button"
							onClick={handleSave}
							className="flex items-center gap-1 px-2 py-1 text-xs rounded hover:bg-green-100 text-green-600 transition-colors"
							title="保存 (Ctrl+Enter)"
						>
							<Check className="h-3 w-3" />
							保存
						</button>
						<button
							type="button"
							onClick={handleCancel}
							className="flex items-center gap-1 px-2 py-1 text-xs rounded hover:bg-red-100 text-red-600 transition-colors"
							title="取消 (Esc)"
						>
							<X className="h-3 w-3" />
							取消
						</button>
					</div>
				</div>
			</div>
		);
	}

	// 显示模式：如果没有描述则显示提示，否则显示描述
	if (!value || value.trim() === "") {
		return (
			<button
				type="button"
				className={cn(
					"mt-2 text-sm text-muted-foreground italic cursor-pointer hover:bg-muted/50 rounded px-1 py-0.5 transition-colors text-left w-full",
					className,
				)}
				onClick={onEnterEdit}
				onKeyDown={(e) => {
					if (e.key === "Enter" || e.key === " ") {
						e.preventDefault();
						onEnterEdit();
					}
				}}
				title="点击添加描述"
			>
				点击添加描述...
			</button>
		);
	}

	return (
		<button
			type="button"
			className={cn(
				"mt-2 text-sm cursor-pointer hover:bg-muted/50 rounded px-1 py-0.5 transition-colors text-left w-full",
				className,
			)}
			onClick={onEnterEdit}
			onKeyDown={(e) => {
				if (e.key === "Enter" || e.key === " ") {
					e.preventDefault();
					onEnterEdit();
				}
			}}
			title="点击编辑描述"
		>
			{value}
		</button>
	);
}
