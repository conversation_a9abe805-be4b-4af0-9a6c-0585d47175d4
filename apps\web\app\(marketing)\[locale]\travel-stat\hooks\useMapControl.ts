"use client";

import { useAnalytics } from "@modules/analytics";
import mapboxgl from "mapbox-gl";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import type {
	AnimationTheme,
	AtmosphereTheme,
	MapProjectionType,
	MapStyleType,
} from "../components/types";
import { ATMOSPHERE_CONFIGS } from "../components/types";
import { DEFAULT_CENTER, DEFAULT_ZOOM } from "../constants/mapConfig";
import type { TravelPoint } from "../types";

export function useMapControl() {
	const { trackEvent } = useAnalytics();
	const [mapLoaded, setMapLoaded] = useState(false);
	const [viewState, setViewState] = useState({
		latitude: DEFAULT_CENTER.lat,
		longitude: DEFAULT_CENTER.lng,
		zoom: DEFAULT_ZOOM,
	});
	const [atmosphereTheme, setAtmosphereTheme] =
		useState<AtmosphereTheme>("day");
	const [mapStyle, setMapStyle] = useState<MapStyleType>("outdoors");
	const [animationTheme, setAnimationTheme] =
		useState<AnimationTheme>("shooting-stars");
	const [mapProjection, setMapProjection] =
		useState<MapProjectionType>("globe");
	const [worldCountriesData, setWorldCountriesData] = useState<any>(null);

	// 显示控制状态
	const [showTooltips, setShowTooltips] = useState(true);
	const [showMarkers, setShowMarkers] = useState(true);

	// 全局tooltip状态
	const [polaroidTooltipState, setPolaroidTooltipState] = useState<{
		isVisible: boolean;
		point: TravelPoint | null;
		mousePosition: { x: number; y: number } | null;
	}>({
		isVisible: false,
		point: null,
		mousePosition: null,
	});

	const mapRef = useRef<mapboxgl.Map | null>(null);

	// 从 localStorage 加载设置
	useEffect(() => {
		const savedAtmosphere = localStorage.getItem("atmosphereTheme");
		const savedMapStyle = localStorage.getItem("mapStyle");
		const savedProjection = localStorage.getItem("mapProjection");
		const savedShowTooltips = localStorage.getItem("showTooltips");
		const savedShowMarkers = localStorage.getItem("showMarkers");

		if (savedAtmosphere) {
			try {
				setAtmosphereTheme(savedAtmosphere as AtmosphereTheme);
			} catch (error) {
				console.error("加载大气层主题失败:", error);
			}
		}

		if (savedMapStyle) {
			try {
				setMapStyle(savedMapStyle as MapStyleType);
			} catch (error) {
				console.error("加载地图样式失败:", error);
			}
		}

		if (savedProjection) {
			try {
				setMapProjection(savedProjection as MapProjectionType);
			} catch (error) {
				console.error("加载地图投影失败:", error);
			}
		}

		if (savedShowTooltips !== null) {
			try {
				setShowTooltips(savedShowTooltips === "true");
			} catch (error) {
				console.error("加载tooltip显示设置失败:", error);
			}
		}

		if (savedShowMarkers !== null) {
			try {
				setShowMarkers(savedShowMarkers === "true");
			} catch (error) {
				console.error("加载marker显示设置失败:", error);
			}
		}
	}, []);

	// 保存设置到 localStorage
	useEffect(() => {
		localStorage.setItem("atmosphereTheme", atmosphereTheme);
	}, [atmosphereTheme]);

	useEffect(() => {
		localStorage.setItem("mapStyle", mapStyle);
	}, [mapStyle]);

	useEffect(() => {
		localStorage.setItem("mapProjection", mapProjection);
	}, [mapProjection]);

	useEffect(() => {
		localStorage.setItem("showTooltips", showTooltips.toString());
	}, [showTooltips]);

	useEffect(() => {
		localStorage.setItem("showMarkers", showMarkers.toString());
	}, [showMarkers]);

	// 加载世界国家边界数据
	useEffect(() => {
		const loadWorldCountries = async () => {
			// 使用配置的数据源
			const { getAllDataSources } = await import(
				"../constants/geoDataSources"
			);
			const dataSources = getAllDataSources();

			for (const source of dataSources) {
				try {
					console.log(
						`🌍 尝试从 ${source.name} (${source.url}) 加载世界国家数据...`,
					);

					// 处理本地文件URL，确保绕过国际化路由
					let requestUrl = source.url;
					if (
						source.url.startsWith("/") &&
						!source.url.startsWith("//")
					) {
						// 本地相对路径，确保使用正确的域名而不受路由影响
						requestUrl = `${window.location.origin}${source.url}`;
						console.log(
							`🔧 [DEBUG] 本地文件路径转换: ${source.url} -> ${requestUrl}`,
						);
					}

					console.log(`🌐 [DEBUG] 实际请求URL: ${requestUrl}`);
					const response = await fetch(requestUrl);
					console.log(
						`📊 [DEBUG] 响应状态: ${response.status} ${response.statusText}`,
					);

					if (response.ok) {
						const data = await response.json();

						// 处理不同数据格式
						const geoJsonData = data;

						// 如果是 TopoJSON 格式 (from cdn.jsdelivr.net)，转换为 GeoJSON
						if (
							data.type === "Topology" &&
							data.objects &&
							data.objects.countries
						) {
							console.log("📐 检测到 TopoJSON 格式，需要转换...");
							// 对于 TopoJSON，我们需要使用 topojson-client 库或者选择 GeoJSON 格式
							// 暂时跳过这个源，使用下一个
							continue;
						}

						// 验证 GeoJSON 格式
						if (
							geoJsonData.type === "FeatureCollection" &&
							geoJsonData.features
						) {
							console.log(
								`✅ 成功从 ${source.name} 加载数据，包含 ${geoJsonData.features.length} 个国家`,
							);
							setWorldCountriesData(geoJsonData);
							return;
						}
					}
				} catch (error) {
					console.warn(
						`❌ 从 ${source.name} 加载失败:`,
						error instanceof Error ? error.message : String(error),
					);
				}
			}

			console.error("💥 所有数据源都加载失败");
		};

		loadWorldCountries();
	}, []);

	// 应用大气层效果到地图
	const applyAtmosphereToMap = useCallback(
		(theme: AtmosphereTheme) => {
			if (mapRef.current && mapLoaded) {
				const config = ATMOSPHERE_CONFIGS[theme];
				try {
					mapRef.current.setFog(config);
				} catch (error) {
					console.error("应用大气层效果失败:", error);
				}
			}
		},
		[mapLoaded],
	);

	// 当大气层主题改变时应用效果
	useEffect(() => {
		applyAtmosphereToMap(atmosphereTheme);
	}, [atmosphereTheme, applyAtmosphereToMap]);

	// 地图加载完成
	const handleMapLoad = useCallback(
		(event: any) => {
			const map = event.target;
			mapRef.current = map;
			setMapLoaded(true);

			// 追踪地图加载完成事件
			trackEvent("travel_stat_map_loaded", {
				initial_style: mapStyle,
				initial_projection: mapProjection,
				initial_atmosphere: atmosphereTheme,
			});

			// 应用大气层
			setTimeout(() => {
				applyAtmosphereToMap(atmosphereTheme);
			}, 100);

			// 设置初始投影
			handleProjectionChange(mapProjection);
		},
		[atmosphereTheme, mapProjection, mapStyle, trackEvent],
	);

	// 处理投影切换
	const handleProjectionChange = useCallback(
		(projection: MapProjectionType) => {
			if (mapRef.current && mapLoaded) {
				try {
					const oldProjection = mapProjection;

					// 根据投影类型构建配置对象
					const projectionConfig: any = { name: projection };

					// 为圆锥投影添加中心点和平行线参数（暂时注释，需要类型定义）
					// if (config.center && config.parallels) {
					// 	projectionConfig = {
					// 		name: projection,
					// 		center: config.center,
					// 		parallels: config.parallels,
					// 	};
					// }

					console.log("设置投影:", projectionConfig);
					mapRef.current.setProjection(projectionConfig);
					setMapProjection(projection);

					// 追踪投影切换事件
					trackEvent("travel_stat_projection_change", {
						from_projection: oldProjection,
						to_projection: projection,
					});
				} catch (error) {
					console.error("设置投影失败:", error);

					// 追踪投影切换失败事件
					trackEvent("travel_stat_projection_change_failed", {
						target_projection: projection,
						error_message:
							error instanceof Error
								? error.message
								: "Unknown error",
					});
				}
			}
		},
		[mapLoaded, mapProjection, trackEvent],
	);

	// 处理大气层主题切换
	const handleAtmosphereChange = useCallback(
		(theme: AtmosphereTheme) => {
			const oldTheme = atmosphereTheme;
			setAtmosphereTheme(theme);

			// 追踪大气层主题切换事件
			trackEvent("travel_stat_atmosphere_change", {
				from_theme: oldTheme,
				to_theme: theme,
			});
		},
		[atmosphereTheme, trackEvent],
	);

	// 处理地图样式切换
	const handleMapStyleChange = useCallback(
		(style: MapStyleType) => {
			const oldStyle = mapStyle;
			setMapStyle(style);

			// 追踪地图样式切换事件
			trackEvent("travel_stat_map_style_change", {
				from_style: oldStyle,
				to_style: style,
			});
		},
		[mapStyle, trackEvent],
	);

	// 处理动画主题切换
	const handleAnimationChange = useCallback(
		(theme: AnimationTheme) => {
			const oldTheme = animationTheme;
			setAnimationTheme(theme);

			// 追踪动画主题切换事件
			trackEvent("travel_stat_animation_change", {
				from_theme: oldTheme,
				to_theme: theme,
			});
		},
		[animationTheme, trackEvent],
	);

	// 重置视图以显示所有足迹点
	const resetMapView = useCallback(
		(travelPoints: TravelPoint[]) => {
			if (!mapRef.current || !mapLoaded) {
				return;
			}

			// 追踪地图视图重置事件
			trackEvent("travel_stat_map_view_reset", {
				points_count: travelPoints.length,
				projection: mapProjection,
			});

			const map = mapRef.current;

			if (travelPoints.length === 0) {
				// 没有足迹点，回到默认视图
				map.flyTo({
					center: [DEFAULT_CENTER.lng, DEFAULT_CENTER.lat],
					zoom: DEFAULT_ZOOM,
					duration: 1500,
				});
			} else if (travelPoints.length === 1) {
				// 只有一个点，聚焦到该点
				const point = travelPoints[0];
				map.flyTo({
					center: point.coordinates,
					zoom: 8,
					duration: 1500,
				});
			} else {
				// 有多个足迹点，调整视图以显示所有点位
				const coordinates = travelPoints.map(
					(point) => point.coordinates,
				);

				// 创建边界对象
				const bounds = coordinates.reduce(
					(bounds, coord) => bounds.extend(coord),
					new mapboxgl.LngLatBounds(coordinates[0], coordinates[0]),
				);

				// 调整地图视图
				map.fitBounds(bounds, {
					padding: 100,
					duration: 1500,
				});
			}
		},
		[mapLoaded, mapProjection, trackEvent],
	);

	// 飞行到指定点位
	const flyToPoint = useCallback(
		(coordinates: [number, number], order: number) => {
			if (!mapRef.current || !mapLoaded) {
				return;
			}

			// 追踪点位聚焦事件
			trackEvent("travel_stat_point_focus", {
				point_order: order,
				zoom_level: order === 1 ? 6 : 8,
			});

			const map = mapRef.current;

			// 根据点位数量调整缩放级别
			const zoomLevel = order === 1 ? 6 : 8;

			map.flyTo({
				center: coordinates,
				zoom: zoomLevel,
				duration: 1500,
			});
		},
		[mapLoaded, trackEvent],
	);

	// 飞行到指定国家（国家级别聚焦）
	const flyToCountry = useCallback(
		(coordinates: [number, number], countryName?: string) => {
			if (!mapRef.current || !mapLoaded) {
				return;
			}

			const map = mapRef.current;

			// 根据国家名称设置不同的缩放级别
			let zoomLevel = 4; // 默认国家级别缩放

			// 针对不同大小的国家调整缩放级别
			if (countryName) {
				const largeCountries = [
					"中国",
					"China",
					"美国",
					"United States",
					"俄罗斯",
					"Russia",
					"加拿大",
					"Canada",
					"巴西",
					"Brazil",
					"澳大利亚",
					"Australia",
				];
				const mediumCountries = [
					"印度",
					"India",
					"阿根廷",
					"Argentina",
					"哈萨克斯坦",
					"Kazakhstan",
					"阿尔及利亚",
					"Algeria",
				];
				const smallCountries = [
					"日本",
					"Japan",
					"英国",
					"United Kingdom",
					"德国",
					"Germany",
					"法国",
					"France",
					"意大利",
					"Italy",
				];

				if (
					largeCountries.some(
						(country) =>
							countryName.includes(country) ||
							country.includes(countryName),
					)
				) {
					zoomLevel = 3.5; // 大国用更小的缩放级别
				} else if (
					mediumCountries.some(
						(country) =>
							countryName.includes(country) ||
							country.includes(countryName),
					)
				) {
					zoomLevel = 4; // 中等国家
				} else if (
					smallCountries.some(
						(country) =>
							countryName.includes(country) ||
							country.includes(countryName),
					)
				) {
					zoomLevel = 5; // 小国用更大的缩放级别
				}
			}

			// 追踪国家聚焦事件
			trackEvent("travel_stat_country_focus", {
				country_name: countryName || "unknown",
				zoom_level: zoomLevel,
			});

			map.flyTo({
				center: coordinates,
				zoom: zoomLevel,
				duration: 1500,
			});
		},
		[mapLoaded, trackEvent],
	);

	// 全局tooltip控制函数 (保持向后兼容的命名)
	const showPolaroidTooltip = useCallback(
		(point: TravelPoint, mousePosition: { x: number; y: number }) => {
			setPolaroidTooltipState({
				isVisible: true,
				point,
				mousePosition,
			});
		},
		[],
	);

	const hidePolaroidTooltip = useCallback(() => {
		setPolaroidTooltipState({
			isVisible: false,
			point: null,
			mousePosition: null,
		});
	}, []);

	return useMemo(() => {
		return {
			mapLoaded,
			viewState,
			atmosphereTheme,
			setAtmosphereTheme: handleAtmosphereChange,
			mapStyle,
			setMapStyle: handleMapStyleChange,
			animationTheme,
			setAnimationTheme: handleAnimationChange,
			mapProjection,
			setMapProjection: handleProjectionChange,
			worldCountriesData,
			handleMapLoad,
			resetMapView,
			flyToPoint,
			flyToCountry,
			mapRef,
			// 显示控制
			showTooltips,
			setShowTooltips,
			showMarkers,
			setShowMarkers,
			// 全局tooltip (保持向后兼容的命名)
			polaroidTooltipState,
			showPolaroidTooltip,
			hidePolaroidTooltip,
		};
	}, [
		mapLoaded,
		viewState,
		atmosphereTheme,
		mapStyle,
		animationTheme,
		mapProjection,
		worldCountriesData,
		showTooltips,
		showMarkers,
		polaroidTooltipState,
		showPolaroidTooltip,
		hidePolaroidTooltip,
	]);
}

export type UseMapControlReturn = ReturnType<typeof useMapControl>;
