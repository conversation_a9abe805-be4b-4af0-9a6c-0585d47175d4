/* 博客画廊组件样式优化 */

/* 重置 prose 中的组件样式 */
.prose .blog-gallery-container {
	@apply not-prose mb-8;
}

.prose .step-card-container {
	@apply not-prose mb-6;
}

.prose .feature-grid-container {
	@apply not-prose mb-8;
}

/* 响应式调整 */
@media (max-width: 768px) {
	.prose .blog-gallery-container,
	.prose .step-card-container,
	.prose .feature-grid-container {
		@apply mx-[-1rem];
	}
}

/* 画廊悬浮效果增强 */
.blog-gallery-item {
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.blog-gallery-item:hover {
	transform: translateY(-2px) scale(1.02);
	box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* 步骤卡片动画 */
.step-card {
	animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(30px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

/* 特性网格项目悬浮效果 */
.feature-item {
	transition: all 0.3s ease;
}

.feature-item:hover {
	transform: translateY(-4px);
	box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

/* 图片加载渐入效果 */
.gallery-image {
	opacity: 0;
	transition: opacity 0.5s ease-in-out;
}

.gallery-image.loaded {
	opacity: 1;
}

/* 背景装饰动画 */
.floating-decoration {
	animation: float 6s ease-in-out infinite;
}

@keyframes float {
	0%,
	100% {
		transform: translateY(0px) rotate(0deg);
	}
	33% {
		transform: translateY(-10px) rotate(1deg);
	}
	66% {
		transform: translateY(-5px) rotate(-1deg);
	}
}

/* 模态框动画 */
.gallery-modal {
	animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
	from {
		opacity: 0;
		transform: scale(0.95);
	}
	to {
		opacity: 1;
		transform: scale(1);
	}
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
	.blog-gallery-item {
		border-color: rgba(255, 255, 255, 0.1);
	}

	.step-card,
	.feature-item {
		backdrop-filter: blur(20px);
		background: rgba(30, 41, 59, 0.8);
	}
}
