"use client";

import { useEffect, useMemo, useState } from "react";
import { HAND_DRAWN_THEMES } from "../../types/markerTypes";

import type { BaseMarkerProps, HandDrawnTheme } from "./types";

interface HandDrawnMarkerProps extends BaseMarkerProps {
	theme?: HandDrawnTheme;
	customRoughness?: number;
	hideOutline?: boolean;
}

// 清新文艺色彩配置
const ARTISTIC_COLORS = {
	pencil: {
		primary: "rgb(139, 146, 156)", // 温柔灰蓝
		secondary: "rgb(176, 190, 197)", // 淡雅灰青
		accent: "rgb(211, 220, 227)", // 清雅白蓝
	},
	pen: {
		primary: "rgb(108, 117, 125)", // 优雅深灰
		secondary: "rgb(134, 142, 150)", // 中性灰
		accent: "rgb(173, 181, 189)", // 浅雅灰
	},
	charcoal: {
		primary: "rgb(87, 96, 111)", // 深邃蓝灰
		secondary: "rgb(116, 125, 140)", // 沉静蓝灰
		accent: "rgb(156, 163, 175)", // 柔和灰
	},
	marker: {
		primary: "rgb(99, 179, 237)", // 温和天蓝
		secondary: "rgb(147, 197, 253)", // 清新浅蓝
		accent: "rgb(186, 230, 253)", // 淡雅蓝白
	},
} as const;

// 旅行手账图案生成函数
const TRAVEL_PATTERNS = {
	star: (roughness: number): string => {
		// 手绘五角星
		const jitter = () => (Math.random() - 0.5) * roughness * 2;
		const outerRadius = 8;
		const innerRadius = 3;

		let path = "M";
		for (let i = 0; i < 10; i++) {
			const angle = (i * Math.PI) / 5;
			const radius = i % 2 === 0 ? outerRadius : innerRadius;
			const x = 20 + radius * Math.cos(angle - Math.PI / 2) + jitter();
			const y = 20 + radius * Math.sin(angle - Math.PI / 2) + jitter();

			if (i === 0) {
				path += `${x} ${y}`;
			} else {
				path += ` L${x} ${y}`;
			}
		}
		return `${path} Z`;
	},

	heart: (roughness: number): string => {
		// 手绘爱心
		const jitter = () => (Math.random() - 0.5) * roughness * 1.5;
		const points = [
			[20, 30],
			[15, 25],
			[10, 20],
			[10, 14],
			[13, 7],
			[17, 7],
			[18, 7],
			[19, 8],
			[20, 9],
			[21, 8],
			[22, 7],
			[23, 7],
			[27, 7],
			[30, 10],
			[30, 14],
			[25, 25],
			[20, 30],
		];

		let path = "M";
		points.forEach(([x, y], i) => {
			const jx = x + jitter();
			const jy = y + jitter();
			if (i === 0) {
				path += `${jx} ${jy}`;
			} else {
				path += ` L${jx} ${jy}`;
			}
		});
		return `${path} Z`;
	},

	flag: (roughness: number): { flagPole: string; flag: string } => {
		// 手绘小旗帜
		const jitter = () => (Math.random() - 0.5) * roughness * 1;
		const flagPole = `M${15 + jitter()},10 L${15 + jitter()},30`;
		const flag = `M${15 + jitter()},10 L${28 + jitter()},12 L${26 + jitter()},18 L${15 + jitter()},16 Z`;
		return { flagPole, flag };
	},

	sun: (roughness: number): { center: string; rays: string[] } => {
		// 手绘太阳
		const jitter = () => (Math.random() - 0.5) * roughness * 1.5;
		const center = "M20,20 m-6,0 a6,6 0 1,0 12,0 a6,6 0 1,0 -12,0";

		const rays = [];
		for (let i = 0; i < 8; i++) {
			const angle = (i * Math.PI) / 4;
			const x1 = 20 + 9 * Math.cos(angle) + jitter();
			const y1 = 20 + 9 * Math.sin(angle) + jitter();
			const x2 = 20 + 12 * Math.cos(angle) + jitter();
			const y2 = 20 + 12 * Math.sin(angle) + jitter();
			rays.push(`M${x1},${y1} L${x2},${y2}`);
		}

		return { center, rays };
	},
} as const;

// 主题对应的图案
const THEME_PATTERNS: Record<HandDrawnTheme, keyof typeof TRAVEL_PATTERNS> = {
	pencil: "star",
	pen: "heart",
	charcoal: "flag",
	marker: "sun",
};

export function HandDrawnMarker({
	point,
	onRemovePoint,
	isSelected = false,
	scale = 1,
	theme = "pencil",
	customRoughness,
	hideOutline = false,
}: HandDrawnMarkerProps) {
	// 安全获取主题配置
	const themeConfig = HAND_DRAWN_THEMES[theme] || HAND_DRAWN_THEMES.pencil;
	const colorConfig = ARTISTIC_COLORS[theme] || ARTISTIC_COLORS.pencil;
	const roughness = customRoughness || themeConfig.roughness;

	// 根据主题选择图案
	const patternType = THEME_PATTERNS[theme] || "star";

	// 生成手绘图案
	const pattern = useMemo(() => {
		return TRAVEL_PATTERNS[patternType](roughness);
	}, [patternType, roughness]);

	// 添加轻微的抖动状态
	const [shake, setShake] = useState(false);

	// 定期触发轻微抖动，模拟手绘的自然感
	useEffect(() => {
		const interval = setInterval(
			() => {
				setShake(true);
				setTimeout(() => setShake(false), 150);
			},
			4000 + Math.random() * 3000,
		); // 4-7秒随机间隔

		return () => clearInterval(interval);
	}, []);

	const size = 40 * scale;

	// 渲染不同的图案
	const renderPattern = () => {
		const commonProps = {
			stroke: colorConfig.primary,
			strokeWidth: themeConfig.strokeWidth,
			fill: "none",
			opacity: themeConfig.opacity,
			strokeLinecap: "round" as const,
			strokeLinejoin: "round" as const,
		};

		switch (patternType) {
			case "star": {
				return (
					<path
						d={pattern as string}
						{...commonProps}
						fill={colorConfig.accent}
						fillOpacity="0.6"
						className="hand-drawn-stroke"
					/>
				);
			}

			case "heart": {
				return (
					<path
						d={pattern as string}
						{...commonProps}
						fill={colorConfig.secondary}
						fillOpacity="0.7"
						className="hand-drawn-stroke"
					/>
				);
			}

			case "flag": {
				const flagPattern = pattern as {
					flagPole: string;
					flag: string;
				};
				return (
					<g className="hand-drawn-stroke">
						<path
							d={flagPattern.flagPole}
							{...commonProps}
							strokeWidth={themeConfig.strokeWidth + 0.5}
						/>
						<path
							d={flagPattern.flag}
							{...commonProps}
							fill={colorConfig.secondary}
							fillOpacity="0.8"
						/>
					</g>
				);
			}

			case "sun": {
				const sunPattern = pattern as {
					center: string;
					rays: string[];
				};
				return (
					<g className="hand-drawn-stroke">
						<path
							d={sunPattern.center}
							{...commonProps}
							fill={colorConfig.accent}
							fillOpacity="0.7"
						/>
						{sunPattern.rays.map((ray, i) => (
							<path
								key={i}
								d={ray}
								{...commonProps}
								stroke={colorConfig.secondary}
								strokeWidth={themeConfig.strokeWidth - 0.5}
							/>
						))}
					</g>
				);
			}

			default:
				return <path d={pattern as string} {...commonProps} />;
		}
	};

	// 获取图案的中文名称和描述
	const getPatternInfo = () => {
		const info: Record<
			keyof typeof TRAVEL_PATTERNS,
			{ name: string; desc: string }
		> = {
			star: { name: "星星", desc: "闪闪发光的回忆" },
			heart: { name: "爱心", desc: "心动的美好时光" },
			flag: { name: "旗帜", desc: "到达的成就感" },
			sun: { name: "太阳", desc: "温暖明媚的日子" },
		};
		return info[patternType] || { name: "图案", desc: "美好记录" };
	};

	const patternInfo = getPatternInfo();

	return (
		<div className="relative group">
			{hideOutline ? (
				// 无轮廓模式 - 只显示图案
				<div
					className={`transition-all duration-200 cursor-pointer ${
						shake ? "animate-pulse" : ""
					}`}
					style={{
						transform: isSelected ? "scale(1.2)" : "scale(1)",
						filter: shake ? "blur(0.2px)" : "none",
					}}
				>
					<svg
						width={size}
						height={size}
						viewBox="0 0 40 40"
						className="hand-drawn-marker hover:scale-110 transition-transform duration-300"
						aria-label={`手绘${patternInfo.name}标记`}
					>
						{renderPattern()}
					</svg>
				</div>
			) : (
				// 标准模式 - 带背景圆圈
				<div
					className={`transition-all duration-200 cursor-pointer ${
						shake ? "animate-pulse" : ""
					}`}
					style={{
						transform: isSelected ? "scale(1.2)" : "scale(1)",
						filter: shake ? "blur(0.2px)" : "none",
					}}
				>
					<svg
						width={size}
						height={size}
						viewBox="0 0 40 40"
						className="hand-drawn-marker hover:scale-110 transition-transform duration-300"
						aria-label={`手绘${patternInfo.name}标记`}
					>
						{/* 背景淡色圆圈 */}
						<circle
							cx="20"
							cy="20"
							r="16"
							fill={colorConfig.accent}
							opacity="0.2"
							stroke="none"
						/>

						{renderPattern()}
					</svg>
				</div>
			)}

			{/* CSS样式 */}
			<style jsx>{`
        .hand-drawn-stroke {
          filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.08));
        }
        
        .hand-drawn-marker:hover .hand-drawn-stroke {
          stroke-width: ${themeConfig.strokeWidth + 0.3};
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.12));
        }
        
        @keyframes hand-drawn-wobble {
          0%, 100% { transform: translateX(0) translateY(0) rotate(0deg); }
          25% { transform: translateX(0.2px) translateY(0.1px) rotate(0.05deg); }
          50% { transform: translateX(-0.1px) translateY(0.2px) rotate(-0.05deg); }
          75% { transform: translateX(0.1px) translateY(-0.1px) rotate(0.03deg); }
        }
      `}</style>
		</div>
	);
}
