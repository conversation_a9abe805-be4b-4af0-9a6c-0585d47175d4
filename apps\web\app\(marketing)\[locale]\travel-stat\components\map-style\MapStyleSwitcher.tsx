"use client";

import { Badge } from "@ui/components/badge";
import { But<PERSON> } from "@ui/components/button";
import { Card } from "@ui/components/card";
import {
	Globe2,
	Layers,
	Map as MapIcon,
	Mountain,
	Navigation,
	Paintbrush,
	Satellite,
	X,
} from "lucide-react";
import { useEffect, useState } from "react";

// 地图样式类型定义
export type MapStyleType =
	| "streets"
	| "outdoors"
	| "light"
	| "dark"
	| "satellite"
	| "satellite-streets"
	| "navigation-day"
	| "navigation-night";

// 地图样式配置接口
export interface MapStyleConfig {
	url: string;
	name: string;
	description: string;
	category: "basic" | "satellite" | "navigation" | "artistic";
	bestAtmosphere: string[];
	bestAnimation: string[];
}

// 地图样式配置
export const MAP_STYLE_CONFIGS: Record<MapStyleType, MapStyleConfig> = {
	streets: {
		url: "mapbox://styles/mapbox/streets-v12",
		name: "街道地图",
		description: "经典街道视图，详细显示道路和地名",
		category: "basic",
		bestAtmosphere: ["day", "minimal", "ocean"],
		bestAnimation: ["minimal", "floating-particles"],
	},
	outdoors: {
		url: "mapbox://styles/mapbox/outdoors-v12",
		name: "户外地图",
		description: "适合徒步和户外活动，显示地形和步道",
		category: "basic",
		bestAtmosphere: ["day", "dawn", "ocean"],
		bestAnimation: ["floating-particles", "aurora"],
	},
	light: {
		url: "mapbox://styles/mapbox/light-v11",
		name: "浅色地图",
		description: "简洁明亮的浅色主题，适合数据展示",
		category: "basic",
		bestAtmosphere: ["day", "dawn", "minimal"],
		bestAnimation: ["minimal", "floating-particles"],
	},
	dark: {
		url: "mapbox://styles/mapbox/dark-v11",
		name: "深色地图",
		description: "优雅的深色主题，减少眼部疲劳",
		category: "basic",
		bestAtmosphere: ["night", "deep-space", "aurora"],
		bestAnimation: ["shooting-stars", "galaxy", "aurora"],
	},
	satellite: {
		url: "mapbox://styles/mapbox/satellite-v9",
		name: "卫星图像",
		description: "真实的卫星影像，展现地球原貌",
		category: "satellite",
		bestAtmosphere: ["day", "ocean", "minimal"],
		bestAnimation: ["floating-particles", "minimal"],
	},
	"satellite-streets": {
		url: "mapbox://styles/mapbox/satellite-streets-v12",
		name: "卫星街道",
		description: "卫星图像叠加街道信息，最佳组合",
		category: "satellite",
		bestAtmosphere: ["day", "sunset", "dawn"],
		bestAnimation: ["floating-particles", "aurora"],
	},
	"navigation-day": {
		url: "mapbox://styles/mapbox/navigation-day-v1",
		name: "导航白天",
		description: "专为导航优化的白天模式",
		category: "navigation",
		bestAtmosphere: ["day", "dawn", "ocean"],
		bestAnimation: ["minimal", "floating-particles"],
	},
	"navigation-night": {
		url: "mapbox://styles/mapbox/navigation-night-v1",
		name: "导航夜间",
		description: "专为夜间导航设计的深色模式",
		category: "navigation",
		bestAtmosphere: ["night", "deep-space", "aurora"],
		bestAnimation: ["shooting-stars", "galaxy"],
	},
};

// 样式分类
export const STYLE_CATEGORIES = {
	basic: { name: "基础样式", icon: MapIcon },
	satellite: { name: "卫星影像", icon: Satellite },
	navigation: { name: "导航样式", icon: Navigation },
	artistic: { name: "艺术风格", icon: Paintbrush },
} as const;

// 样式图标映射
const STYLE_ICONS = {
	streets: MapIcon,
	outdoors: Mountain,
	light: Globe2,
	dark: Layers,
	satellite: Satellite,
	"satellite-streets": Satellite,
	"navigation-day": Navigation,
	"navigation-night": Navigation,
} as const;

interface MapStyleSwitcherProps {
	currentStyle: MapStyleType;
	onStyleChange: (style: MapStyleType) => void;
	currentAtmosphere?: string;
	currentAnimation?: string;
	className?: string;
}

export function MapStyleSwitcher({
	currentStyle,
	onStyleChange,
	currentAtmosphere,
	currentAnimation,
	className = "",
}: MapStyleSwitcherProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [isMounted, setIsMounted] = useState(false);
	const [selectedCategory, setSelectedCategory] = useState<
		keyof typeof STYLE_CATEGORIES | "all"
	>("all");

	// 确保只在客户端渲染
	useEffect(() => {
		setIsMounted(true);
	}, []);

	const categories = Object.keys(STYLE_CATEGORIES) as Array<
		keyof typeof STYLE_CATEGORIES
	>;
	const styles = Object.keys(MAP_STYLE_CONFIGS) as MapStyleType[];

	// 过滤样式
	const filteredStyles =
		selectedCategory === "all"
			? styles
			: styles.filter(
					(style) =>
						MAP_STYLE_CONFIGS[style].category === selectedCategory,
				);

	// 检查样式是否推荐用于当前大气层/动画
	const isRecommended = (style: MapStyleType): boolean => {
		const config = MAP_STYLE_CONFIGS[style];
		const atmosphereMatch = currentAtmosphere
			? config.bestAtmosphere.includes(currentAtmosphere)
			: false;
		const animationMatch = currentAnimation
			? config.bestAnimation.includes(currentAnimation)
			: false;
		return atmosphereMatch || animationMatch;
	};

	// 如果还没有挂载，显示简化版本
	if (!isMounted) {
		return (
			<Button
				variant="outline"
				size="sm"
				className="bg-white/90 backdrop-blur-sm border-sky-200 shadow-lg transition-all duration-200"
				disabled
			>
				<MapIcon className="w-4 h-4 mr-1" />
				<span className="text-xs">地图样式</span>
			</Button>
		);
	}

	return (
		<div className={`relative ${className}`}>
			{/* 触发按钮 */}
			<Button
				onClick={() => setIsOpen(!isOpen)}
				variant="outline"
				size="sm"
				className="bg-white/90 backdrop-blur-sm border-sky-200 hover:bg-sky-50 shadow-lg transition-all duration-200"
				title="切换地图样式"
			>
				<MapIcon className="w-4 h-4 mr-1" />
				<span className="text-xs">地图样式</span>
			</Button>

			{/* 样式选择面板 */}
			{isOpen && (
				<>
					{/* 背景遮罩 */}
					<div
						className="fixed inset-0 z-40"
						onClick={() => setIsOpen(false)}
						onKeyDown={(e) => {
							if (e.key === "Escape") {
								setIsOpen(false);
							}
						}}
						role="button"
						tabIndex={0}
						aria-label="关闭地图样式选择面板"
					/>

					{/* 样式面板 */}
					<Card className="absolute top-full left-0 mt-2 z-50 w-96 bg-white/95 backdrop-blur-sm border-sky-200 shadow-xl max-h-[500px] overflow-hidden">
						<div className="p-4">
							<div className="flex items-center justify-between mb-3">
								<h3 className="text-sm font-semibold text-gray-800">
									选择地图样式
								</h3>
								<Button
									onClick={() => setIsOpen(false)}
									variant="ghost"
									size="sm"
									className="h-6 w-6 p-0"
								>
									<X className="w-4 h-4" />
								</Button>
							</div>

							{/* 分类过滤器 */}
							<div className="flex gap-1 mb-4 overflow-x-auto">
								<Button
									onClick={() => setSelectedCategory("all")}
									variant={
										selectedCategory === "all"
											? "secondary"
											: "outline"
									}
									size="sm"
									className="text-xs whitespace-nowrap"
								>
									全部
								</Button>
								{categories.map((category) => {
									const categoryInfo =
										STYLE_CATEGORIES[category];
									const Icon = categoryInfo.icon;
									return (
										<Button
											key={category}
											onClick={() =>
												setSelectedCategory(category)
											}
											variant={
												selectedCategory === category
													? "secondary"
													: "outline"
											}
											size="sm"
											className="text-xs whitespace-nowrap"
										>
											<Icon className="w-3 h-3 mr-1" />
											{categoryInfo.name}
										</Button>
									);
								})}
							</div>

							{/* 样式列表 */}
							<div className="space-y-2 max-h-72 overflow-y-auto">
								{filteredStyles.map((style) => {
									const Icon = STYLE_ICONS[style];
									const config = MAP_STYLE_CONFIGS[style];
									const isActive = style === currentStyle;
									const recommended = isRecommended(style);

									return (
										<button
											key={style}
											type="button"
											onClick={() => {
												onStyleChange(style);
												setIsOpen(false);
											}}
											className={`
												w-full p-3 rounded-lg border-2 transition-all duration-200 text-left
												${
													isActive
														? "border-sky-400 bg-sky-50 shadow-md"
														: "border-gray-200 hover:border-sky-300 hover:bg-sky-25"
												}
											`}
										>
											<div className="flex items-start justify-between">
												<div className="flex items-center gap-2 mb-1">
													<Icon
														className={`w-4 h-4 ${isActive ? "text-sky-600" : "text-gray-500"}`}
													/>
													<span
														className={`text-sm font-medium ${isActive ? "text-sky-800" : "text-gray-700"}`}
													>
														{config.name}
													</span>
													{isActive && (
														<div className="w-2 h-2 bg-sky-500 rounded-full" />
													)}
												</div>

												{/* 推荐标签 */}
												{recommended && (
													<Badge className="text-xs">
														推荐
													</Badge>
												)}
											</div>

											<p
												className={`text-xs mb-2 ${isActive ? "text-sky-600" : "text-gray-500"}`}
											>
												{config.description}
											</p>

											{/* 最佳搭配提示 */}
											{(config.bestAtmosphere.length >
												0 ||
												config.bestAnimation.length >
													0) && (
												<div className="space-y-1">
													{config.bestAtmosphere
														.length > 0 && (
														<div className="flex flex-wrap gap-1">
															<span className="text-xs text-gray-400">
																大气层:
															</span>
															{config.bestAtmosphere
																.slice(0, 3)
																.map(
																	(
																		atmosphere,
																	) => (
																		<span
																			key={
																				atmosphere
																			}
																			className={`text-xs px-1 py-0.5 rounded ${
																				currentAtmosphere ===
																				atmosphere
																					? "bg-green-100 text-green-700"
																					: "bg-gray-100 text-gray-600"
																			}`}
																		>
																			{
																				atmosphere
																			}
																		</span>
																	),
																)}
														</div>
													)}
													{config.bestAnimation
														.length > 0 && (
														<div className="flex flex-wrap gap-1">
															<span className="text-xs text-gray-400">
																动画:
															</span>
															{config.bestAnimation
																.slice(0, 3)
																.map(
																	(
																		animation,
																	) => (
																		<span
																			key={
																				animation
																			}
																			className={`text-xs px-1 py-0.5 rounded ${
																				currentAnimation ===
																				animation
																					? "bg-blue-100 text-blue-700"
																					: "bg-gray-100 text-gray-600"
																			}`}
																		>
																			{
																				animation
																			}
																		</span>
																	),
																)}
														</div>
													)}
												</div>
											)}
										</button>
									);
								})}
							</div>

							{/* 当前样式信息 */}
							<div className="mt-4 p-3 bg-gradient-to-r from-sky-50 to-blue-50 rounded-lg border border-sky-200">
								<div className="flex items-center gap-2 mb-2">
									<MapIcon className="w-4 h-4 text-sky-500" />
									<span className="text-sm font-medium text-sky-800">
										当前样式:{" "}
										{MAP_STYLE_CONFIGS[currentStyle].name}
									</span>
								</div>
								<p className="text-xs text-sky-600 mb-2">
									{
										MAP_STYLE_CONFIGS[currentStyle]
											.description
									}
								</p>

								{/* 搭配建议 */}
								{isRecommended(currentStyle) && (
									<div className="flex items-center gap-1">
										<span className="text-xs text-green-600">
											✓ 与当前效果搭配良好
										</span>
									</div>
								)}
							</div>
						</div>
					</Card>
				</>
			)}
		</div>
	);
}
