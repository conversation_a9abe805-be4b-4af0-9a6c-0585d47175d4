"use client";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Layer, Marker, Source } from "react-map-gl";
import type { MarkerStyleType, TravelPoint } from "../../types";
import { MarkerFactory } from "../markers/MarkerFactory";
import { UnifiedTooltip } from "../markers/UnifiedTooltip";

interface ClusterManagerProps {
	travelPoints: TravelPoint[];
	mapRef: any;
	mapZoom: number;
	showMarkers: boolean;
	showTooltips: boolean;
	markerStyle: string;
	markerTheme: string;
	currentEmoji: string;
	currentEmojiColor: string;
	hideOutline: boolean;
	getMarkerColor: (point: TravelPoint) => string;
	onRemovePoint: (id: string) => void;
	onMarkerClick?: (
		point: TravelPoint,
		mousePosition: { x: number; y: number },
	) => void;
	polaroidTooltipState: any;
}

// 性能配置
const PERFORMANCE_CONFIG = {
	// 当点位数量超过此值时启用聚合（降低阈值）
	CLUSTER_THRESHOLD: 50, // 从50降到10，让更多用户能看到聚类效果
	// 聚合半径（像素）
	CLUSTER_RADIUS: 50,
	// 最大缩放级别，超过此级别不再聚合（提高限制）
	MAX_CLUSTER_ZOOM: 15, // 从12提高到15，让更高缩放级别也能看到聚类
	// DOM marker显示的最大数量
	MAX_DOM_MARKERS: 100,
	// 视窗扩展系数（扩大视窗范围以提前加载附近的marker）
	VIEWPORT_EXPAND_FACTOR: 0.2, // 20%的扩展

	// 图片质量配置 - 与PolaroidMarker保持一致
	// 图片尺寸配置
	ULTRA_LOW_QUALITY_SIZE: 250,
	LOW_QUALITY_SIZE: 500,
	MEDIUM_QUALITY_SIZE: 1000,
	HIGH_QUALITY_SIZE: 1500,

	// 缩放级别阈值配置 - 与PolaroidMarker保持一致
	ZOOM_THRESHOLDS: {
		ULTRA_LOW: 3, // 6级及以下使用超低质量
		LOW: 6, // 6-10级使用低质量
		MEDIUM: 10, // 10-13级使用中等质量
		HIGH: 16, // 13级以上使用高质量
	},
};

export function ClusterManager({
	travelPoints,
	mapRef,
	mapZoom,
	showMarkers,
	showTooltips,
	markerStyle,
	markerTheme,
	currentEmoji,
	currentEmojiColor,
	hideOutline,
	getMarkerColor,
	onRemovePoint,
	onMarkerClick,
	polaroidTooltipState,
}: ClusterManagerProps) {
	const clusterSourceRef = useRef<any>(null);

	// 视窗bounds状态
	const [viewportBounds, setViewportBounds] = useState<{
		north: number;
		south: number;
		east: number;
		west: number;
	} | null>(null);

	// 判断点位是否在视窗内（带扩展区域）
	const isPointInViewport = useCallback(
		(point: TravelPoint): boolean => {
			if (!viewportBounds) return true; // 没有bounds信息时默认显示

			const [lng, lat] = point.coordinates;
			const expandFactor = PERFORMANCE_CONFIG.VIEWPORT_EXPAND_FACTOR;

			// 计算扩展的bounds
			const lngRange = Math.abs(
				viewportBounds.east - viewportBounds.west,
			);
			const latRange = Math.abs(
				viewportBounds.north - viewportBounds.south,
			);

			const expandedBounds = {
				north: viewportBounds.north + latRange * expandFactor,
				south: viewportBounds.south - latRange * expandFactor,
				east: viewportBounds.east + lngRange * expandFactor,
				west: viewportBounds.west - lngRange * expandFactor,
			};

			// 处理跨越180度经线的情况
			if (expandedBounds.west > expandedBounds.east) {
				// 跨越180度经线
				return (
					lat >= expandedBounds.south &&
					lat <= expandedBounds.north &&
					(lng >= expandedBounds.west || lng <= expandedBounds.east)
				);
			}

			// 正常情况
			return (
				lat >= expandedBounds.south &&
				lat <= expandedBounds.north &&
				lng >= expandedBounds.west &&
				lng <= expandedBounds.east
			);
		},
		[viewportBounds],
	);

	// 监听地图视窗变化
	useEffect(() => {
		if (!mapRef.current) return;

		const map = mapRef.current;

		const updateViewportBounds = () => {
			const bounds = map.getBounds();
			if (bounds) {
				const newBounds = {
					north: bounds.getNorth(),
					south: bounds.getSouth(),
					east: bounds.getEast(),
					west: bounds.getWest(),
				};

				setViewportBounds(newBounds);

				// 调试信息
				console.log("🗺️ 视窗bounds更新:", {
					bounds: newBounds,
					totalPoints: travelPoints.length,
					visiblePoints: travelPoints.filter((point) => {
						const [lng, lat] = point.coordinates;
						return (
							lat >= newBounds.south &&
							lat <= newBounds.north &&
							lng >= newBounds.west &&
							lng <= newBounds.east
						);
					}).length,
				});
			}
		};

		// 初始化bounds
		updateViewportBounds();

		// 监听地图移动事件
		map.on("moveend", updateViewportBounds);
		map.on("zoomend", updateViewportBounds);

		return () => {
			map.off("moveend", updateViewportBounds);
			map.off("zoomend", updateViewportBounds);
		};
	}, [mapRef, travelPoints.length]);

	// 判断是否需要启用聚合
	const shouldUseCluster = useMemo(() => {
		const clusterEnabled =
			travelPoints.length > PERFORMANCE_CONFIG.CLUSTER_THRESHOLD &&
			mapZoom <= PERFORMANCE_CONFIG.MAX_CLUSTER_ZOOM;

		// 详细的聚类调试信息
		console.log("🔄 聚类状态评估:", {
			totalPoints: travelPoints.length,
			threshold: PERFORMANCE_CONFIG.CLUSTER_THRESHOLD,
			currentZoom: mapZoom,
			maxClusterZoom: PERFORMANCE_CONFIG.MAX_CLUSTER_ZOOM,
			pointsExceedThreshold:
				travelPoints.length > PERFORMANCE_CONFIG.CLUSTER_THRESHOLD,
			zoomWithinLimit: mapZoom <= PERFORMANCE_CONFIG.MAX_CLUSTER_ZOOM,
			clusterEnabled,
		});

		return clusterEnabled;
	}, [travelPoints.length, mapZoom]);

	// 准备GeoJSON数据用于聚合
	const clusterGeoJSON = useMemo(() => {
		if (!shouldUseCluster) return null;

		const geoJSON = {
			type: "FeatureCollection" as const,
			features: travelPoints.map((point) => ({
				type: "Feature" as const,
				geometry: {
					type: "Point" as const,
					coordinates: [point.coordinates[0], point.coordinates[1]],
				},
				properties: {
					id: point.id,
					city: point.city,
					country: point.country,
					description: point.description,
					date: point.date,
					// 标记是否有图片，用于聚合样式
					hasImages: !!(point.images?.length || point.photos?.length),
					// 图片数量
					imageCount:
						(point.images?.length || 0) +
						(point.photos?.length || 0),
				},
			})),
		};

		console.log("📊 聚类GeoJSON数据:", {
			enabled: shouldUseCluster,
			featuresCount: geoJSON.features.length,
			hasImagesCount: geoJSON.features.filter(
				(f) => f.properties.hasImages,
			).length,
		});

		return geoJSON;
	}, [travelPoints, shouldUseCluster]);

	// 获取可见的点位（优化：视窗内 + 数量限制）
	const visiblePoints = useMemo(() => {
		// 首先过滤出视窗内的点位
		const pointsInViewport = travelPoints.filter(isPointInViewport);

		console.log("📍 视窗marker过滤结果:", {
			totalPoints: travelPoints.length,
			pointsInViewport: pointsInViewport.length,
			shouldUseCluster,
			clusterThreshold: PERFORMANCE_CONFIG.CLUSTER_THRESHOLD,
			currentZoom: mapZoom,
			maxClusterZoom: PERFORMANCE_CONFIG.MAX_CLUSTER_ZOOM,
			viewportBounds: viewportBounds ? "available" : "not_available",
		});

		if (shouldUseCluster) {
			// 🔑 关键修复：聚合模式下大幅减少DOM marker数量，避免与聚类层冲突
			// 只显示极少数重要的点位（比如当前选中的、最近的等）
			const importantPoints = pointsInViewport
				.filter((point) => point.images?.length || point.photos?.length) // 有图片的点位
				.slice(0, Math.min(5, PERFORMANCE_CONFIG.MAX_DOM_MARKERS / 20)); // 最多5个，避免与聚类冲突

			console.log("🎯 聚类模式DOM marker:", {
				reducedCount: importantPoints.length,
				originalCount: pointsInViewport.length,
				reduction: `${Math.round((1 - importantPoints.length / pointsInViewport.length) * 100)}%`,
			});

			return importantPoints;
		}

		// 非聚合模式下，正常显示视窗内DOM marker
		if (pointsInViewport.length > PERFORMANCE_CONFIG.MAX_DOM_MARKERS) {
			// 按照有图片的优先级排序，然后限制数量
			const sortedPoints = pointsInViewport.sort((a, b) => {
				const aHasImages = !!(a.images?.length || a.photos?.length);
				const bHasImages = !!(b.images?.length || b.photos?.length);

				if (aHasImages && !bHasImages) return -1;
				if (!aHasImages && bHasImages) return 1;
				return 0;
			});

			return sortedPoints.slice(0, PERFORMANCE_CONFIG.MAX_DOM_MARKERS);
		}

		return pointsInViewport;
	}, [
		travelPoints,
		shouldUseCluster,
		isPointInViewport,
		viewportBounds,
		mapZoom,
	]);

	// 处理聚合点击
	const handleClusterClick = useCallback(
		(e: any) => {
			const feature = e.features?.[0];
			if (!feature) return;

			const clusterId = feature.properties.cluster_id;
			const source = mapRef.current?.getSource("travel-points-cluster");

			if (source?.getClusterExpansionZoom) {
				source.getClusterExpansionZoom(
					clusterId,
					(err: any, zoom: number) => {
						if (err) return;

						mapRef.current?.easeTo({
							center: feature.geometry.coordinates,
							zoom: Math.min(zoom, 16),
							duration: 500,
						});
					},
				);
			}
		},
		[mapRef],
	);

	// 处理未聚合点位点击
	const handleUnclusteredClick = useCallback(
		(e: any) => {
			const feature = e.features?.[0];
			if (!feature) return;

			const pointId = feature.properties.id;
			const point = travelPoints.find((p) => p.id === pointId);

			if (point && onMarkerClick) {
				onMarkerClick(point, {
					x: e.originalEvent.clientX,
					y: e.originalEvent.clientY,
				});
			}
		},
		[travelPoints, onMarkerClick],
	);

	// 注册点击事件
	useEffect(() => {
		if (!mapRef.current || !shouldUseCluster) return;

		const map = mapRef.current;
		map.on("click", "clusters", handleClusterClick);
		map.on("click", "unclustered-point", handleUnclusteredClick);

		// 鼠标样式
		map.on("mouseenter", "clusters", () => {
			map.getCanvas().style.cursor = "pointer";
		});
		map.on("mouseleave", "clusters", () => {
			map.getCanvas().style.cursor = "";
		});
		map.on("mouseenter", "unclustered-point", () => {
			map.getCanvas().style.cursor = "pointer";
		});
		map.on("mouseleave", "unclustered-point", () => {
			map.getCanvas().style.cursor = "";
		});

		return () => {
			map.off("click", "clusters", handleClusterClick);
			map.off("click", "unclustered-point", handleUnclusteredClick);
			map.off("mouseenter", "clusters");
			map.off("mouseleave", "clusters");
			map.off("mouseenter", "unclustered-point");
			map.off("mouseleave", "unclustered-point");
		};
	}, [mapRef, shouldUseCluster, handleClusterClick, handleUnclusteredClick]);

	if (!showMarkers) return null;

	return (
		<>
			{/* 聚合模式：使用 Mapbox 原生层 */}
			{shouldUseCluster && clusterGeoJSON && (
				<Source
					id="travel-points-cluster"
					type="geojson"
					data={clusterGeoJSON}
					cluster={true}
					clusterMaxZoom={PERFORMANCE_CONFIG.MAX_CLUSTER_ZOOM}
					clusterRadius={PERFORMANCE_CONFIG.CLUSTER_RADIUS}
					clusterProperties={{
						// 聚合属性：有图片的点位数量
						imageCount: ["+", ["case", ["get", "hasImages"], 1, 0]],
						// 总图片数量
						totalImages: ["+", ["get", "imageCount"]],
					}}
				>
					{/* 聚合圆圈 */}
					<Layer
						id="clusters"
						type="circle"
						filter={["has", "point_count"]}
						paint={{
							"circle-color": [
								"step",
								["get", "point_count"],
								"#3B82F6", // 1-10个点位：蓝色 (更鲜艳)
								10,
								"#1D4ED8", // 11-30个点位：深蓝色
								30,
								"#1E40AF", // 31+个点位：更深蓝色
							],
							"circle-radius": [
								"step",
								["get", "point_count"],
								20, // 增大基础大小，从15到20
								10,
								28, // 10+个点位，从20到28
								30,
								35, // 30+个点位，从25到35
							],
							"circle-stroke-width": 3, // 增加边框宽度
							"circle-stroke-color": "#FFFFFF",
							"circle-opacity": 0.8, // 稍微透明，让背景可见
						}}
					/>

					{/* 聚合数字标签 */}
					<Layer
						id="cluster-count"
						type="symbol"
						filter={["has", "point_count"]}
						layout={{
							"text-field": [
								"format",
								["get", "point_count"],
								{ "font-scale": 1.2 }, // 增大主数字
								"\n",
								{},
								["concat", "📍", ["get", "totalImages"]],
								{ "font-scale": 0.7 }, // 调整副标签大小
							],
							"text-font": [
								"Open Sans Bold", // 使用粗体
								"Arial Unicode MS Bold",
							],
							"text-size": 14, // 增大字体
							"text-allow-overlap": true,
							"text-ignore-placement": true,
							"text-anchor": "center",
						}}
						paint={{
							"text-color": "#FFFFFF", // 白色字体，更明显
							"text-halo-color": "#1E40AF", // 添加光晕效果
							"text-halo-width": 1,
						}}
					/>

					{/* 未聚合的点位 */}
					<Layer
						id="unclustered-point"
						type="circle"
						filter={["!", ["has", "point_count"]]}
						paint={{
							"circle-color": [
								"case",
								["get", "hasImages"],
								"#EF4444", // 有图片：红色 (更鲜艳)
								"#10B981", // 无图片：绿色 (更鲜艳)
							],
							"circle-radius": [
								"interpolate",
								["linear"],
								["zoom"],
								0,
								6, // 增大最小尺寸
								10,
								10, // 中等缩放
								15,
								14, // 增大最大尺寸
							],
							"circle-stroke-width": 2,
							"circle-stroke-color": "#FFFFFF",
							"circle-opacity": 0.9,
						}}
					/>
				</Source>
			)}

			{/* DOM Markers：性能优化后的拍立得 */}
			{visiblePoints.map((point) => (
				<Marker
					key={point.id}
					latitude={point.coordinates[1]}
					longitude={point.coordinates[0]}
					anchor="bottom"
				>
					<div className="relative">
						<MarkerFactory
							point={point}
							onRemovePoint={onRemovePoint}
							style={markerStyle as MarkerStyleType}
							theme={markerTheme}
							mapZoom={mapZoom}
							customProps={{
								color: getMarkerColor(point),
								emoji: currentEmoji,
								backgroundColor: currentEmojiColor,
								hideOutline: hideOutline,
								size: "medium",
								showDescription: true,
								maxDescriptionLength: 20,
								// 拍立得专用props
								onPhotoClick: undefined,
								onMarkerClick: onMarkerClick,
								// 视窗和优先级props（for PolaroidMarker）
								isVisible: isPointInViewport(point),
								priority: (() => {
									if (!viewportBounds) return "medium";

									const [lng, lat] = point.coordinates;
									const centerLng =
										(viewportBounds.east +
											viewportBounds.west) /
										2;
									const centerLat =
										(viewportBounds.north +
											viewportBounds.south) /
										2;

									// 计算距离视窗中心的相对距离
									const lngRange = Math.abs(
										viewportBounds.east -
											viewportBounds.west,
									);
									const latRange = Math.abs(
										viewportBounds.north -
											viewportBounds.south,
									);

									const distanceFromCenter = Math.sqrt(
										((lng - centerLng) / lngRange) ** 2 +
											((lat - centerLat) / latRange) ** 2,
									);

									// 根据距离设置优先级
									if (distanceFromCenter < 0.3) return "high"; // 中心30%区域
									if (distanceFromCenter < 0.6)
										return "medium"; // 中等60%区域
									return "low"; // 边缘区域
								})(),
							}}
						/>

						{/* 静态tooltip - 只在非拍立得样式下显示 */}
						{showTooltips &&
							!polaroidTooltipState.isVisible &&
							markerStyle !== "polaroid" && (
								<div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50">
									<UnifiedTooltip
										isVisible={true}
										type="simple"
										mode="static"
										point={point}
										onRemovePoint={onRemovePoint}
									/>
								</div>
							)}
					</div>
				</Marker>
			))}

			{/* 性能提示 */}
			{travelPoints.length > PERFORMANCE_CONFIG.CLUSTER_THRESHOLD && (
				<div
					className="absolute top-4 left-4 bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-800 shadow-sm z-50"
					style={{ pointerEvents: "none" }}
				>
					<div className="flex items-center gap-2">
						<div className="w-2 h-2 bg-blue-500 rounded-full" />
						<span>
							{shouldUseCluster
								? `🔄 聚类模式：${travelPoints.length} 个点位已聚合 (zoom ${mapZoom})`
								: `📍 普通模式：显示 ${visiblePoints.length}/${travelPoints.length} 个点位`}
						</span>
					</div>
					{shouldUseCluster && (
						<div className="text-xs text-blue-600 mt-1 space-y-1">
							<div>🔍 点击聚类圆圈放大查看详细内容</div>
							<div>
								⚡ 缩放到{" "}
								{PERFORMANCE_CONFIG.MAX_CLUSTER_ZOOM + 1}{" "}
								级以上显示所有marker
							</div>
						</div>
					)}
					{!shouldUseCluster &&
						travelPoints.length <=
							PERFORMANCE_CONFIG.CLUSTER_THRESHOLD && (
							<div className="text-xs text-blue-600 mt-1">
								💡 添加更多点位 ({">"}
								{PERFORMANCE_CONFIG.CLUSTER_THRESHOLD})
								启用智能聚类
							</div>
						)}
				</div>
			)}

			{/* 视窗性能监控 - 开发环境显示 */}
			{process.env.NODE_ENV === "development" && viewportBounds && (
				<div
					className="absolute top-4 right-4 bg-green-50 border border-green-200 rounded-lg p-3 text-sm text-green-800 shadow-sm z-50"
					style={{ pointerEvents: "none" }}
				>
					<div className="flex items-center gap-2 mb-1">
						<div className="w-2 h-2 bg-green-500 rounded-full" />
						<span className="font-medium">视窗优化</span>
					</div>
					<div className="text-xs text-green-600 space-y-1">
						<div>总点位: {travelPoints.length}</div>
						<div>
							视窗内:{" "}
							{travelPoints.filter(isPointInViewport).length}
						</div>
						<div>DOM渲染: {visiblePoints.length}</div>
						<div>
							性能提升:{" "}
							{Math.round(
								(1 -
									visiblePoints.length /
										travelPoints.length) *
									100,
							)}
							%
						</div>

						{/* 🔧 新增：图片质量信息 */}
						<div className="mt-2 pt-2 border-t border-green-200">
							<div className="font-medium">图片质量设置</div>
							<div>
								缩放级别:{" "}
								<span className="font-medium">
									{mapZoom.toFixed(1)}
								</span>
							</div>
							<div>
								当前质量:{" "}
								<span className="font-medium">
									{mapZoom <=
									PERFORMANCE_CONFIG.ZOOM_THRESHOLDS.ULTRA_LOW
										? `超低质量 (${PERFORMANCE_CONFIG.ULTRA_LOW_QUALITY_SIZE}px)`
										: mapZoom <=
												PERFORMANCE_CONFIG
													.ZOOM_THRESHOLDS.LOW
											? `低质量 (${PERFORMANCE_CONFIG.LOW_QUALITY_SIZE}px)`
											: mapZoom <=
													PERFORMANCE_CONFIG
														.ZOOM_THRESHOLDS.MEDIUM
												? `中质量 (${PERFORMANCE_CONFIG.MEDIUM_QUALITY_SIZE}px)`
												: `高质量 (${PERFORMANCE_CONFIG.HIGH_QUALITY_SIZE}px)`}
								</span>
							</div>
							<div>
								阈值:{" "}
								<span className="text-xs">
									{
										PERFORMANCE_CONFIG.ZOOM_THRESHOLDS
											.ULTRA_LOW
									}
									/{PERFORMANCE_CONFIG.ZOOM_THRESHOLDS.LOW}/
									{PERFORMANCE_CONFIG.ZOOM_THRESHOLDS.MEDIUM}/
									{PERFORMANCE_CONFIG.ZOOM_THRESHOLDS.HIGH}
								</span>
							</div>
						</div>
					</div>
				</div>
			)}
		</>
	);
}
