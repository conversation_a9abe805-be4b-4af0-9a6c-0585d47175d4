"use client";

import { Badge } from "@ui/components/badge";
import { MapIcon } from "lucide-react";
import { useTranslatedMapStyles } from "../../../hooks/useTranslatedMapStyles";
import type { MapStyleType } from "../../types";
import { STYLE_ICONS } from "../constants/themeIcons";
import type { StyleCategoryKey } from "../constants/types";
import { PopoverContainer } from "./PopoverContainer";

interface MapStylePopoverProps {
	mapStyle: MapStyleType;
	selectedStyleCategory: StyleCategoryKey | "all";
	onMapStyleChange: (style: MapStyleType) => void;
	onCategoryChange: (category: StyleCategoryKey | "all") => void;
	isStyleRecommended: (style: MapStyleType) => boolean;
}

export function MapStylePopover({
	mapStyle,
	selectedStyleCategory,
	onMapStyleChange,
	onCategoryChange,
	isStyleRecommended,
}: MapStylePopoverProps) {
	const {
		t,
		translatedMapStyles,
		translatedStyleCategories,
		getTranslatedMapStyle,
	} = useTranslatedMapStyles();

	const currentStyleInfo = getTranslatedMapStyle(mapStyle);

	return (
		<PopoverContainer>
			{/* 样式类别过滤 */}
			<div className="flex flex-wrap gap-1 mb-3">
				<button
					type="button"
					onClick={() => onCategoryChange("all")}
					className={`px-2 py-1 text-xs rounded ${
						selectedStyleCategory === "all"
							? "bg-sky-100 text-sky-700"
							: "bg-gray-100 text-gray-600 hover:bg-gray-200"
					}`}
				>
					{t.mapStyles.categories.all()}
				</button>
				{translatedStyleCategories.map((category) => (
					<button
						key={category.id}
						type="button"
						onClick={() =>
							onCategoryChange(category.id as StyleCategoryKey)
						}
						className={`px-2 py-1 text-xs rounded flex items-center gap-1 ${
							selectedStyleCategory === category.id
								? "bg-sky-100 text-sky-700"
								: "bg-gray-100 text-gray-600 hover:bg-gray-200"
						}`}
					>
						<category.icon className="w-3 h-3" />
						{category.name}
					</button>
				))}
			</div>

			{/* 样式选择网格 */}
			<div className="space-y-2 mb-4">
				{translatedMapStyles
					.filter((style) => {
						if (selectedStyleCategory === "all") return true;
						return style.category === selectedStyleCategory;
					})
					.map((style) => {
						const Icon = STYLE_ICONS[style.id as MapStyleType];
						const isActive = style.id === mapStyle;
						const recommended = isStyleRecommended(
							style.id as MapStyleType,
						);

						return (
							<button
								key={style.id}
								type="button"
								onClick={(e) => {
									e.stopPropagation();
									onMapStyleChange(style.id as MapStyleType);
								}}
								className={`w-full p-3 rounded-lg border-2 transition-all duration-200 text-left ${
									isActive
										? "border-sky-400 bg-sky-50 shadow-md"
										: "border-gray-200 hover:border-sky-300 hover:bg-sky-25"
								}`}
							>
								<div className="flex items-start justify-between">
									<div className="flex items-center gap-2 mb-1">
										<Icon
											className={`w-4 h-4 ${
												isActive
													? "text-sky-600"
													: "text-gray-500"
											}`}
										/>
										<span
											className={`text-sm font-medium ${
												isActive
													? "text-sky-800"
													: "text-gray-700"
											}`}
										>
											{style.name}
										</span>
										{isActive && (
											<div className="w-2 h-2 bg-sky-500 rounded-full" />
										)}
									</div>
									{recommended && (
										<Badge
											status="info"
											className="text-xs"
										>
											{t.mapStyles.popover.recommended()}
										</Badge>
									)}
								</div>
								<p
									className={`text-xs mb-2 ${
										isActive
											? "text-sky-600"
											: "text-gray-500"
									}`}
								>
									{style.description}
								</p>
							</button>
						);
					})}
			</div>

			{/* 当前样式信息 */}
			{currentStyleInfo && (
				<div className="mt-4 p-3 bg-gradient-to-r from-sky-50 to-blue-50 rounded-lg border border-sky-200">
					<div className="flex items-center gap-2 mb-2">
						<MapIcon className="w-4 h-4 text-sky-500" />
						<span className="text-sm font-medium text-sky-800">
							{t.mapStyles.popover.currentStyle(
								currentStyleInfo.name,
							)}
						</span>
					</div>
					<p className="text-xs text-sky-600 mb-2">
						{currentStyleInfo.description}
					</p>
					{isStyleRecommended(mapStyle) && (
						<div className="flex items-center gap-1">
							<span className="text-xs text-green-600">
								{t.mapStyles.popover.goodWithEffects()}
							</span>
						</div>
					)}
				</div>
			)}
		</PopoverContainer>
	);
}
