"use client";

import type { Editor } from "@tiptap/react";
import { But<PERSON> } from "@ui/components/button";
import { Input } from "@ui/components/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import {
	Bold,
	CalendarRange,
	CheckCircle,
	ChevronDown,
	FileText,
	Heading1,
	Heading2,
	Image as ImageIcon,
	Italic,
	Link as LinkIcon,
	List,
	ListOrdered,
	MapPin,
	MoreHorizontal,
	Redo,
	Settings,
	SortAsc,
	Undo,
} from "lucide-react";
import React, { useState, useCallback } from "react";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";

// 自定义操作按钮接口
interface CustomAction {
	icon: React.ReactNode;
	title: string;
	onClick: () => void;
	disabled?: boolean;
}

interface MenuBarProps {
	editor: Editor;
	customActions?: CustomAction[]; // 添加自定义操作按钮参数
}

export const MenuBar: React.FC<MenuBarProps> = ({ editor, customActions }) => {
	const [imageUrl, setImageUrl] = useState("");
	const [linkUrl, setLinkUrl] = useState("");
	const [linkText, setLinkText] = useState("");

	if (!editor) {
		return null;
	}

	const addTravelPoint = useCallback(() => {
		editor
			.chain()
			.focus()
			.insertContent({
				type: "travelPoint",
				attrs: {
					pointId: `point_${Date.now()}`,
					location: "新地点",
					pointDate: new Date().toISOString(),
				},
				content: [
					{
						type: "paragraph",
					},
				],
			})
			.run();
	}, [editor]);

	const addTimeline = useCallback(() => {
		editor
			.chain()
			.focus()
			.insertContent({
				type: "travelTimeline",
				attrs: {
					timelineId: uuidv4(),
					title: `第${new Date().toLocaleDateString("zh-CN")}天`,
					timelineDate: new Date().toISOString(),
				},
				content: [
					{
						type: "travelPoint",
						attrs: {
							pointId: uuidv4(),
							location: "新地点",
							pointDate: new Date().toISOString(),
						},
						content: [
							{
								type: "paragraph",
							},
						],
					},
				],
			})
			.run();
	}, [editor]);

	// 新增：时间线模板功能
	const addTimelineFromTemplate = useCallback(
		(templateType: "oneDay" | "multiDay" | "custom") => {
			const baseDate = new Date();

			switch (templateType) {
				case "oneDay": {
					const timelineContent = {
						type: "travelTimeline",
						attrs: {
							timelineId: uuidv4(),
							title: `${baseDate.toLocaleDateString("zh-CN")} 一日游`,
							timelineDate: baseDate.toISOString(),
						},
						content: [
							{
								type: "travelPoint",
								attrs: {
									pointId: uuidv4(),
									location: "出发地",
									pointDate: baseDate.toISOString(),
								},
								content: [
									{
										type: "paragraph",
										content: [
											{
												type: "text",
												text: "开始今天的旅程",
											},
										],
									},
								],
							},
							{
								type: "travelPoint",
								attrs: {
									pointId: uuidv4(),
									location: "目的地",
									pointDate: new Date(
										baseDate.getTime() + 4 * 60 * 60 * 1000,
									).toISOString(),
								},
								content: [
									{
										type: "paragraph",
										content: [
											{
												type: "text",
												text: "到达主要景点",
											},
										],
									},
								],
							},
							{
								type: "travelPoint",
								attrs: {
									pointId: uuidv4(),
									location: "返程",
									pointDate: new Date(
										baseDate.getTime() + 8 * 60 * 60 * 1000,
									).toISOString(),
								},
								content: [
									{
										type: "paragraph",
										content: [
											{
												type: "text",
												text: "结束行程，准备返回",
											},
										],
									},
								],
							},
						],
					};
					editor.chain().focus().insertContent(timelineContent).run();
					toast.success("已插入一日游模板");
					break;
				}
				case "multiDay": {
					// 创建3天的行程模板
					for (let i = 0; i < 3; i++) {
						const dayDate = new Date(
							baseDate.getTime() + i * 24 * 60 * 60 * 1000,
						);
						const dayTimeline = {
							type: "travelTimeline",
							attrs: {
								timelineId: uuidv4(),
								title: `第${i + 1}天 - ${dayDate.toLocaleDateString("zh-CN")}`,
								timelineDate: dayDate.toISOString(),
							},
							content: [
								{
									type: "travelPoint",
									attrs: {
										pointId: uuidv4(),
										location: `第${i + 1}天目的地`,
										pointDate: dayDate.toISOString(),
									},
									content: [
										{
											type: "paragraph",
											content: [
												{
													type: "text",
													text: `第${i + 1}天的行程安排`,
												},
											],
										},
									],
								},
							],
						};
						editor.chain().focus().insertContent(dayTimeline).run();
					}
					toast.success("已插入3天行程模板");
					break;
				}
				default: {
					// 自定义模板
					const timelineContent = {
						type: "travelTimeline",
						attrs: {
							timelineId: uuidv4(),
							title: "自定义行程",
							timelineDate: baseDate.toISOString(),
						},
						content: [
							{
								type: "travelPoint",
								attrs: {
									pointId: uuidv4(),
									location: "新地点",
									pointDate: baseDate.toISOString(),
								},
								content: [{ type: "paragraph" }],
							},
						],
					};
					editor.chain().focus().insertContent(timelineContent).run();
					toast.success("已插入自定义时间线");
					break;
				}
			}
		},
		[editor],
	);

	// 新增：批量操作功能
	const batchOperations = useCallback(() => {
		// 获取所有时间线节点
		const { state } = editor;
		const timelineNodes: Array<{ node: any; pos: number }> = [];

		state.doc.descendants((node: any, pos: number) => {
			if (node.type.name === "travelTimeline") {
				timelineNodes.push({ node, pos });
			}
		});

		return {
			sortByDate: () => {
				// 按日期排序所有时间线
				const sortedNodes = timelineNodes.sort((a, b) => {
					const dateA = new Date(a.node.attrs.timelineDate);
					const dateB = new Date(b.node.attrs.timelineDate);
					return dateA.getTime() - dateB.getTime();
				});

				// 重新排列节点（这里需要更复杂的实现）
				toast.success(`已按日期排序 ${sortedNodes.length} 个时间线`);
			},
			validateStructure: () => {
				// 验证节点结构的完整性
				let issues = 0;
				timelineNodes.forEach(({ node }) => {
					if (!node.attrs.timelineId || !node.attrs.title) {
						issues++;
					}
				});

				if (issues > 0) {
					toast.warning(`发现 ${issues} 个结构问题`);
				} else {
					toast.success("所有时间线结构完整");
				}
			},
		};
	}, [editor]);

	const addImage = useCallback(() => {
		const url = window.prompt("图片URL");

		if (url) {
			editor.chain().focus().setImage({ src: url }).run();
		}
	}, [editor]);

	const setLink = useCallback(() => {
		const previousUrl = editor.getAttributes("link").href;
		const url = window.prompt("链接URL", previousUrl);

		// 如果对话框被取消，则保持链接不变
		if (url === null) {
			return;
		}

		// 如果输入为空，则移除链接
		if (url === "") {
			editor.chain().focus().unsetLink().run();
			return;
		}

		// 添加http前缀如果用户没有输入
		const fullUrl = url.startsWith("http") ? url : `https://${url}`;

		// 更新链接
		editor.chain().focus().setLink({ href: fullUrl }).run();
	}, [editor]);

	// 处理文件上传
	const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
		const files = e.target.files;
		if (!files || files.length === 0) return;

		const file = files[0];

		try {
			// 创建一个本地预览链接
			const localUrl = URL.createObjectURL(file);

			// 先插入本地预览图片
			editor.chain().focus().setImage({ src: localUrl }).run();

			// 这里应该添加真实的图片上传逻辑
			// 例如上传到服务器或云存储
			// 成功后替换为真实URL

			toast.success("图片已插入");
		} catch (error) {
			console.error("图片处理失败:", error);
			toast.error("图片插入失败");
		}

		// 清除文件输入值，以便可以上传相同的文件
		e.target.value = "";
	};

	return (
		<div className="flex flex-wrap gap-1 p-2 border-b border-gray-300 bg-gray-50 backdrop-blur-sm transition-all duration-200 w-full z-10">
			{/* 历史操作 */}
			<Button
				onClick={() => editor.chain().focus().undo().run()}
				disabled={!editor.can().undo()}
				variant="ghost"
				size="icon"
				className="h-8 w-8"
				type="button"
			>
				<Undo className="h-4 w-4" />
			</Button>
			<Button
				onClick={() => editor.chain().focus().redo().run()}
				disabled={!editor.can().redo()}
				variant="ghost"
				size="icon"
				className="h-8 w-8"
				type="button"
			>
				<Redo className="h-4 w-4" />
			</Button>

			{/* 分隔线 */}
			<div className="border-r border-gray-300 h-6 mx-1 self-center" />

			{/* 文本格式 */}
			<Button
				onClick={() => editor.chain().focus().toggleBold().run()}
				className={`h-8 w-8 ${editor.isActive("bold") ? "bg-gray-200" : ""}`}
				variant="ghost"
				size="icon"
				type="button"
			>
				<Bold className="h-4 w-4" />
			</Button>
			<Button
				onClick={() => editor.chain().focus().toggleItalic().run()}
				className={`h-8 w-8 ${editor.isActive("italic") ? "bg-gray-200" : ""}`}
				variant="ghost"
				size="icon"
				type="button"
			>
				<Italic className="h-4 w-4" />
			</Button>

			{/* 分隔线 */}
			<div className="border-r border-gray-300 h-6 mx-1 self-center" />

			{/* 标题 */}
			<Button
				onClick={() =>
					editor.chain().focus().toggleHeading({ level: 1 }).run()
				}
				className={`h-8 w-8 ${editor.isActive("heading", { level: 1 }) ? "bg-gray-200" : ""}`}
				variant="ghost"
				size="icon"
				type="button"
			>
				<Heading1 className="h-4 w-4" />
			</Button>
			<Button
				onClick={() =>
					editor.chain().focus().toggleHeading({ level: 2 }).run()
				}
				className={`h-8 w-8 ${editor.isActive("heading", { level: 2 }) ? "bg-gray-200" : ""}`}
				variant="ghost"
				size="icon"
				type="button"
			>
				<Heading2 className="h-4 w-4" />
			</Button>

			{/* 分隔线 */}
			<div className="border-r border-gray-300 h-6 mx-1 self-center" />

			{/* 对齐方式功能已移除（需要TextAlign扩展） */}

			{/* 分隔线 */}
			<div className="border-r border-gray-300 h-6 mx-1 self-center" />

			{/* 列表 */}
			<Button
				onClick={() => editor.chain().focus().toggleBulletList().run()}
				className={`h-8 w-8 ${editor.isActive("bulletList") ? "bg-gray-200" : ""}`}
				variant="ghost"
				size="icon"
				type="button"
			>
				<List className="h-4 w-4" />
			</Button>
			<Button
				onClick={() => editor.chain().focus().toggleOrderedList().run()}
				className={`h-8 w-8 ${editor.isActive("orderedList") ? "bg-gray-200" : ""}`}
				variant="ghost"
				size="icon"
				type="button"
			>
				<ListOrdered className="h-4 w-4" />
			</Button>

			{/* 分隔线 */}
			<div className="border-r border-gray-300 h-6 mx-1 self-center" />

			{/* 图片插入 - 弹出窗口 */}
			<Popover>
				<PopoverTrigger asChild>
					<Button
						variant="ghost"
						size="icon"
						className="h-8 w-8"
						type="button"
					>
						<ImageIcon className="h-4 w-4" />
					</Button>
				</PopoverTrigger>
				<PopoverContent className="w-80">
					<div className="space-y-2">
						<h3 className="font-medium text-sm">插入图片</h3>
						<div className="grid gap-2">
							<Input
								type="text"
								value={imageUrl}
								onChange={(e) => setImageUrl(e.target.value)}
								placeholder="输入图片URL"
								className="text-xs"
							/>
							<Button
								onClick={addImage}
								size="sm"
								className="text-xs"
								disabled={!imageUrl}
							>
								插入链接图片
							</Button>
							<div className="text-center text-xs text-gray-500">
								或
							</div>
							<div className="flex items-center gap-2">
								<Input
									type="file"
									onChange={handleFileUpload}
									accept="image/*"
									className="text-xs flex-grow"
								/>
							</div>
						</div>
					</div>
				</PopoverContent>
			</Popover>

			{/* 链接 - 弹出窗口 */}
			<Popover>
				<PopoverTrigger asChild>
					<Button
						variant="ghost"
						size="icon"
						className="h-8 w-8"
						type="button"
					>
						<LinkIcon className="h-4 w-4" />
					</Button>
				</PopoverTrigger>
				<PopoverContent className="w-80">
					<div className="space-y-2">
						<h3 className="font-medium text-sm">插入链接</h3>
						<div className="grid gap-2">
							<Input
								type="text"
								value={linkUrl}
								onChange={(e) => setLinkUrl(e.target.value)}
								placeholder="输入链接URL"
								className="text-xs"
							/>
							<Input
								type="text"
								value={linkText}
								onChange={(e) => setLinkText(e.target.value)}
								placeholder="输入链接文本 (可选)"
								className="text-xs"
							/>
							<Button
								onClick={setLink}
								size="sm"
								className="text-xs"
								disabled={!linkUrl}
							>
								插入链接
							</Button>
						</div>
						<div className="text-xs text-gray-500 mt-1">
							提示:
							先选择文本再点击链接按钮可直接将选中文本转为链接
						</div>
					</div>
				</PopoverContent>
			</Popover>

			{/* 分隔线 */}
			<div className="border-r border-gray-300 h-6 mx-1 self-center" />

			{/* 旅行相关按钮 */}
			<Button
				onClick={addTimeline}
				className="h-8 gap-1 bg-blue-50 hover:bg-blue-100 text-blue-700 border border-blue-200"
				variant="outline"
				size="sm"
				type="button"
				title="插入空白时间线"
			>
				<CalendarRange className="h-4 w-4" />
				<span>时间线</span>
			</Button>

			{/* 时间线模板下拉菜单 */}
			<Popover>
				<PopoverTrigger asChild>
					<Button
						className="h-8 gap-1 bg-green-50 hover:bg-green-100 text-green-700 border border-green-200"
						variant="outline"
						size="sm"
						type="button"
						title="选择时间线模板"
					>
						<FileText className="h-4 w-4" />
						<span>模板</span>
						<ChevronDown className="h-3 w-3" />
					</Button>
				</PopoverTrigger>
				<PopoverContent className="w-56">
					<div className="space-y-2">
						<h3 className="font-medium text-sm">选择时间线模板</h3>
						<div className="grid gap-1">
							<Button
								onClick={() =>
									addTimelineFromTemplate("oneDay")
								}
								variant="ghost"
								size="sm"
								className="justify-start text-xs"
							>
								<CalendarRange className="h-4 w-4 mr-2" />
								一日游模板
								<span className="ml-auto text-xs text-gray-500">
									3个点位
								</span>
							</Button>
							<Button
								onClick={() =>
									addTimelineFromTemplate("multiDay")
								}
								variant="ghost"
								size="sm"
								className="justify-start text-xs"
							>
								<CalendarRange className="h-4 w-4 mr-2" />
								多日游模板
								<span className="ml-auto text-xs text-gray-500">
									3天
								</span>
							</Button>
							<Button
								onClick={() =>
									addTimelineFromTemplate("custom")
								}
								variant="ghost"
								size="sm"
								className="justify-start text-xs"
							>
								<Settings className="h-4 w-4 mr-2" />
								自定义模板
								<span className="ml-auto text-xs text-gray-500">
									空白
								</span>
							</Button>
						</div>
					</div>
				</PopoverContent>
			</Popover>

			<Button
				onClick={addTravelPoint}
				className="h-8 gap-1 bg-blue-50 hover:bg-blue-100 text-blue-700 border border-blue-200"
				variant="outline"
				size="sm"
				type="button"
				title="插入旅行点位"
			>
				<MapPin className="h-4 w-4" />
				<span>点位</span>
			</Button>

			{/* 批量操作下拉菜单 */}
			<Popover>
				<PopoverTrigger asChild>
					<Button
						className="h-8 gap-1 bg-purple-50 hover:bg-purple-100 text-purple-700 border border-purple-200"
						variant="outline"
						size="sm"
						type="button"
						title="批量操作"
					>
						<MoreHorizontal className="h-4 w-4" />
						<span>批量</span>
						<ChevronDown className="h-3 w-3" />
					</Button>
				</PopoverTrigger>
				<PopoverContent className="w-48">
					<div className="space-y-2">
						<h3 className="font-medium text-sm">批量操作</h3>
						<div className="grid gap-1">
							<Button
								onClick={() => batchOperations().sortByDate()}
								variant="ghost"
								size="sm"
								className="justify-start text-xs"
							>
								<SortAsc className="h-4 w-4 mr-2" />
								按日期排序
							</Button>
							<Button
								onClick={() =>
									batchOperations().validateStructure()
								}
								variant="ghost"
								size="sm"
								className="justify-start text-xs"
							>
								<CheckCircle className="h-4 w-4 mr-2" />
								验证结构
							</Button>
						</div>
					</div>
				</PopoverContent>
			</Popover>

			{/* 渲染自定义操作按钮 */}
			{customActions && customActions.length > 0 && (
				<>
					{/* 分隔线 */}
					<div className="border-r border-gray-300 h-6 mx-1 self-center" />

					{/* 自定义按钮 */}
					{customActions.map((action, index) => (
						<Button
							key={`custom-action-${index}`}
							onClick={action.onClick}
							className="h-8 gap-1 bg-blue-50 hover:bg-blue-100 text-blue-700 border border-blue-200"
							variant="outline"
							size="sm"
							type="button"
							title={action.title}
							disabled={action.disabled}
						>
							{action.icon}
							<span>{action.title}</span>
						</Button>
					))}
				</>
			)}
		</div>
	);
};
