/* Mapbox 控件自定义样式 - 匹配左侧面板风格 */

/* 通用控件容器样式 - 匹配Card组件 - 使用更强的特殊性 */
.mapboxgl-map .mapboxgl-ctrl-group,
.mapboxgl-ctrl-group {
	background: rgba(255, 255, 255, 0.9) !important;
	backdrop-filter: blur(8px) !important;
	border: 1px solid rgb(224, 242, 254) !important;
	border-radius: 12px !important; /* 12px - 匹配Card的圆角 */
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important; /* 匹配Card的阴影 */
	padding: 4px !important; /* 4px */
	margin: 8px !important; /* 8px */
	/* 确保在卡片缩放环境下控件仍可交互 */
	z-index: 100 !important;
	position: relative !important;
}

/* 控件按钮样式 - 匹配Button组件 - 使用更强的特殊性 */
.mapboxgl-map .mapboxgl-ctrl-group button,
.mapboxgl-ctrl-group button,
.mapboxgl-ctrl button {
	background: transparent !important;
	border: 1px solid transparent !important;
	border-radius: 8px !important; /* 8px - 匹配Button的圆角 */
	width: 32px !important; /* 32px */
	height: 32px !important; /* 32px */
	margin: 2px !important; /* 2px */
	display: inline-flex !important;
	align-items: center !important;
	justify-content: center !important;
	transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1) !important; /* 匹配Button的过渡 */
	color: rgb(71, 85, 105) !important; /* slate-600 */
	position: relative !important;
	outline: none !important;
	font-size: 14px !important; /* 14px */
	font-weight: 500 !important;
	/* 确保按钮在缩放环境下可点击 */
	z-index: 101 !important;
	pointer-events: auto !important;
	cursor: pointer !important;
}

/* 按钮悬停效果 - 匹配Button outline variant - 使用更强的特殊性 */
.mapboxgl-map .mapboxgl-ctrl-group button:hover,
.mapboxgl-ctrl-group button:hover,
.mapboxgl-ctrl button:hover {
	background: rgb(248, 250, 252) !important; /* slate-50 */
	border-color: rgb(226, 232, 240) !important; /* slate-200 */
	color: rgb(51, 65, 85) !important; /* slate-700 */
}

/* 按钮激活状态 */
.mapboxgl-ctrl-group button:active {
	background: rgb(241, 245, 249) !important; /* slate-100 */
	transform: none !important;
}

/* 禁用状态 */
.mapboxgl-ctrl-group button:disabled {
	background: transparent !important;
	color: rgb(156, 163, 175) !important; /* gray-400 */
	cursor: not-allowed !important;
	opacity: 0.5 !important;
}

/* 导航控件特定样式 */
.mapboxgl-ctrl-zoom-in,
.mapboxgl-ctrl-zoom-out,
.mapboxgl-ctrl-compass {
	background: transparent !important;
}

/* 导航控件图标优化 */
.mapboxgl-ctrl-zoom-in .mapboxgl-ctrl-icon,
.mapboxgl-ctrl-zoom-out .mapboxgl-ctrl-icon,
.mapboxgl-ctrl-compass .mapboxgl-ctrl-icon {
	background-size: 18px 18px !important;
	opacity: 0.8 !important;
	filter: none !important;
}

/* 全屏控件 */
.mapboxgl-ctrl-fullscreen {
	background: transparent !important;
}

.mapboxgl-ctrl-fullscreen .mapboxgl-ctrl-icon {
	background-size: 18px 18px !important;
	opacity: 0.8 !important;
}

/* 定位控件 */
.mapboxgl-ctrl-geolocate {
	background: transparent !important;
}

.mapboxgl-ctrl-geolocate .mapboxgl-ctrl-icon {
	background-size: 18px 18px !important;
	opacity: 0.8 !important;
}

/* 定位控件激活状态 */
.mapboxgl-ctrl-geolocate.mapboxgl-ctrl-geolocate-active {
	background: rgb(239, 246, 255) !important; /* blue-50 */
	border-color: rgb(147, 197, 253) !important; /* blue-300 */
}

.mapboxgl-ctrl-geolocate.mapboxgl-ctrl-geolocate-active .mapboxgl-ctrl-icon {
	background-color: rgb(59, 130, 246) !important; /* blue-500 */
}

/* 比例尺控件 - 匹配Card样式 */
.mapboxgl-ctrl-scale {
	background: rgba(255, 255, 255, 0.9) !important;
	backdrop-filter: blur(8px) !important;
	border: 1px solid rgb(224, 242, 254) !important;
	border-radius: 0.5rem !important; /* 8px */
	color: rgb(71, 85, 105) !important; /* slate-600 */
	font-size: 0.75rem !important; /* 12px */
	font-weight: 500 !important;
	padding: 0.25rem 0.5rem !important; /* 4px 8px */
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
	margin: 0.5rem !important; /* 8px */
}

/* 控件组之间的间距调整 */
.mapboxgl-ctrl-top-right .mapboxgl-ctrl {
	margin: 0.25rem 0.25rem 0 0 !important; /* 4px */
}

.mapboxgl-ctrl-bottom-left .mapboxgl-ctrl {
	margin: 0 0 0.25rem 0.25rem !important; /* 4px */
}

/* 移除默认的分隔线 */
.mapboxgl-ctrl-group:not(.mapboxgl-ctrl-vertical) > button:not(:first-child) {
	border-top: none !important;
	margin-top: 0.125rem !important; /* 2px */
}

.mapboxgl-ctrl-group.mapboxgl-ctrl-vertical > button:not(:first-child) {
	border-left: none !important;
	margin-left: 0.125rem !important; /* 2px */
}

/* 响应式调整 */
@media (max-width: 768px) {
	.mapboxgl-ctrl-group {
		border-radius: 0.625rem !important; /* 10px */
		padding: 0.1875rem !important; /* 3px */
		margin: 0.375rem !important; /* 6px */
	}

	.mapboxgl-ctrl-group button {
		width: 1.75rem !important; /* 28px */
		height: 1.75rem !important; /* 28px */
		border-radius: 0.375rem !important; /* 6px */
	}

	.mapboxgl-ctrl-scale {
		border-radius: 0.375rem !important; /* 6px */
		font-size: 0.6875rem !important; /* 11px */
		padding: 0.1875rem 0.375rem !important; /* 3px 6px */
		margin: 0.375rem !important; /* 6px */
	}

	/* 移动端图标稍小 */
	.mapboxgl-ctrl-zoom-in .mapboxgl-ctrl-icon,
	.mapboxgl-ctrl-zoom-out .mapboxgl-ctrl-icon,
	.mapboxgl-ctrl-compass .mapboxgl-ctrl-icon,
	.mapboxgl-ctrl-fullscreen .mapboxgl-ctrl-icon,
	.mapboxgl-ctrl-geolocate .mapboxgl-ctrl-icon {
		background-size: 16px 16px !important;
	}
}

/* 按钮焦点状态 */
.mapboxgl-ctrl-group button:focus {
	outline: 2px solid rgb(147, 197, 253) !important; /* blue-300 */
	outline-offset: 2px !important;
}
