import type { FeatureStatusBadgeProps } from "../types";
import { FEATURE_STATUS_CONFIG } from "../types";

/**
 * 特性状态徽章组件
 */
export function FeatureStatusBadge({
	status,
	className,
	ui,
}: FeatureStatusBadgeProps) {
	const config = FEATURE_STATUS_CONFIG[status];

	return (
		<span
			className={ui.cn(
				"inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium",
				config.color,
				config.bgColor,
				className,
			)}
			title={config.description}
		>
			{config.label}
		</span>
	);
}
