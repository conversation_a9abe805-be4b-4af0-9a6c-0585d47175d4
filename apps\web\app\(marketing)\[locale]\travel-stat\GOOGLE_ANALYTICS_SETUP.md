# Google Analytics 4 设置指南 - 旅行统计工具

## 🎯 为什么选择 Google Analytics 4

作为独立开发者，Google Analytics 4 (GA4) 是最佳的免费分析解决方案：

- ✅ **完全免费** - 无月费，无使用限制
- ✅ **功能强大** - 提供详细的用户行为分析
- ✅ **成熟稳定** - Google 官方产品，技术支持完善
- ✅ **易于集成** - 简单的JavaScript追踪代码
- ✅ **实时数据** - 可以看到实时用户活动
- ✅ **深度洞察** - 支持自定义事件和转化追踪

## 🚀 快速设置步骤

### 1. 创建 Google Analytics 账户

1. 访问 [Google Analytics](https://analytics.google.com/)
2. 点击"开始免费使用"
3. 创建账户（选择"为我自己"）
4. 设置媒体资源：
   - **媒体资源名称**: "旅行统计工具"
   - **报告时区**: 选择您的时区
   - **货币**: 选择您的货币

### 2. 创建数据流

1. 选择"网站"平台
2. 填写网站信息：
   - **网站网址**: `https://yourdomain.com`
   - **数据流名称**: "旅行统计工具主站"
3. 点击"创建数据流"

### 3. 获取测量ID

创建数据流后，您会看到类似这样的测量ID：
```
G-XXXXXXXXXX
```

### 4. 配置环境变量

在您的项目中设置环境变量：

```bash
# .env.local
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
```

```bash
# .env.production
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
```

### 5. 部署验证

部署后，在浏览器控制台验证：
```javascript
// 检查 gtag 函数是否加载
console.log(typeof window.gtag); // 应该输出 "function"

// 检查 dataLayer
console.log(window.dataLayer); // 应该看到数组数据
```

## 📊 GA4 中的旅行统计工具事件配置

### 自定义事件设置

所有旅行统计工具的事件都会自动发送到 GA4。主要事件包括：

#### 1. 核心用户行为
- `travel_stat_page_visit` - 页面访问
- `travel_stat_point_added` - 添加旅行点位
- `travel_stat_point_removed` - 删除旅行点位
- `travel_stat_search_query` - 搜索查询

#### 2. 功能使用
- `travel_stat_mode_switch` - 模式切换
- `travel_stat_map_style_change` - 地图样式切换
- `travel_stat_export_completed` - 导出完成

### 在 GA4 中查看事件

1. 登录 Google Analytics
2. 选择您的媒体资源
3. 在左侧菜单选择 **报告 > 参与度 > 事件**
4. 您将看到所有自定义事件的列表

### 设置转化事件

将关键行为标记为转化：

1. 在 GA4 中进入 **配置 > 事件**
2. 找到以下事件并标记为转化：
   - `travel_stat_point_added` (用户激活)
   - `travel_stat_export_completed` (深度使用)
   - `travel_stat_mode_switch` (功能探索)

## 📈 关键报告和分析

### 1. 实时报告
- **路径**: 报告 > 实时
- **用途**: 查看当前用户活动和实时事件

### 2. 用户行为分析
- **路径**: 报告 > 参与度 > 事件
- **关注指标**:
  - 事件计数最高的功能
  - 用户参与时长
  - 事件转化率

### 3. 用户获取分析
- **路径**: 报告 > 生命周期 > 获取
- **关注指标**:
  - 流量来源
  - 首次访问用户比例
  - 渠道效果

### 4. 自定义报告

创建专门的旅行统计工具报告：

1. 进入 **报告 > 库**
2. 点击"创建新报告"
3. 选择"详细报告"
4. 配置维度和指标：

**建议的自定义报告配置**:
- **维度**: 事件名称、页面标题、国家/地区
- **指标**: 事件计数、用户数、参与度时长

## 🎛️ 高级分析设置

### 1. 用户细分

创建有价值的用户群体：
- **活跃用户**: 添加了3个以上点位的用户
- **探索者**: 使用了多种地图样式的用户
- **创作者**: 使用了导出功能的用户

### 2. 漏斗分析

设置用户行为漏斗：
1. 页面访问 → 2. 首次搜索 → 3. 添加点位 → 4. 导出分享

### 3. 受众群体分析

了解用户特征：
- 地理位置分布
- 设备类型偏好
- 访问时间模式

## 🔍 数据洞察示例

通过 GA4，您可以回答这些关键问题：

### 用户行为问题
- 🌍 **用户来自哪里？** - 地理报告显示用户分布
- 📱 **使用什么设备？** - 技术报告显示设备偏好
- ⏰ **何时最活跃？** - 时间报告显示使用模式

### 功能使用问题  
- 🗺️ **最受欢迎的地图样式？** - 自定义事件参数分析
- 🎨 **颜色主题偏好？** - 事件参数对比
- 📤 **导出功能使用率？** - 转化漏斗分析

### 产品优化问题
- 🚀 **哪些功能需要优化？** - 低使用率功能识别
- 💡 **用户卡在哪里？** - 行为流分析
- 📊 **什么驱动用户留存？** - 留存报告分析

## 🛠️ 调试和故障排除

### 常见问题解决

#### 1. 事件不显示
```javascript
// 检查控制台是否有错误
console.log('gtag function:', typeof window.gtag);

// 手动发送测试事件
gtag('event', 'test_event', {
  custom_parameter: 'test_value'
});
```

#### 2. 数据延迟
- GA4 数据通常有 4-8 小时延迟
- 实时报告显示最近 30 分钟的数据
- 标准报告显示 24-48 小时前的完整数据

#### 3. 事件参数限制
- 每个事件最多 25 个自定义参数
- 参数名称不能超过 40 个字符
- 参数值不能超过 100 个字符

### 开发环境调试

```typescript
// 在开发环境中启用调试
if (process.env.NODE_ENV === 'development') {
  // 启用 GA4 调试模式
  gtag('config', 'G-XXXXXXXXXX', {
    debug_mode: true
  });
}
```

## 📊 GA4 vs Plausible 对比

| 特性 | Google Analytics 4 | Plausible |
|------|-------------------|-----------|
| **价格** | 🟢 完全免费 | 🔴 $9/月起 |
| **数据详细度** | 🟢 非常详细 | 🟡 基础数据 |
| **实时数据** | 🟢 支持 | 🟢 支持 |
| **隐私友好** | 🟡 需要配置 | 🟢 默认友好 |
| **学习曲线** | 🔴 较陡峭 | 🟢 简单直观 |
| **自定义事件** | 🟢 无限制 | 🟡 有限制 |
| **数据保留** | 🟢 26个月 | 🟢 永久 |
| **GDPR合规** | 🟡 需要配置 | 🟢 内置合规 |

## 💡 成本节省建议

作为独立开发者，使用 GA4 可以节省：
- **年费用**: $108/年 (Plausible) → $0/年 (GA4)
- **功能获得**: 更详细的用户行为分析
- **扩展性**: 无用户量限制，支持业务增长

## 📞 支持资源

- **GA4 帮助中心**: [support.google.com/analytics](https://support.google.com/analytics)
- **GA4 学院**: [analytics.google.com/analytics/academy](https://analytics.google.com/analytics/academy)
- **开发者文档**: [developers.google.com/analytics](https://developers.google.com/analytics)

通过这个配置，您的旅行统计工具将拥有完整的免费分析功能，帮助您了解用户行为并优化产品体验！🚀 