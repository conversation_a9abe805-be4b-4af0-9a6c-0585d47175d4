import { zValidator } from "@hono/zod-validator";
import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { adminMiddleware } from "../../middleware/admin";

// 产品创建和更新的验证schema
const createProductSchema = z.object({
	name: z
		.string()
		.min(1, "产品名称不能为空")
		.max(100, "产品名称不能超过100个字符"),
	description: z.string().optional(),
});

const updateProductSchema = createProductSchema.partial();

// 查询参数验证
const querySchema = z.object({
	page: z
		.string()
		.optional()
		.transform((val) => (val ? Number.parseInt(val, 10) : 1)),
	limit: z
		.string()
		.optional()
		.transform((val) => (val ? Number.parseInt(val, 10) : 10)),
	search: z.string().optional(),
});

export const productRouter = new Hono()
	.use(adminMiddleware)
	// 获取产品列表
	.get("/products", zValidator("query", querySchema), async (c) => {
		try {
			const { page, limit, search } = c.req.valid("query");
			const offset = (page - 1) * limit;

			// 构建查询条件
			const where = search
				? {
						OR: [
							{
								name: {
									contains: search,
									mode: "insensitive" as const,
								},
							},
							{
								description: {
									contains: search,
									mode: "insensitive" as const,
								},
							},
						],
					}
				: {};

			// 获取总数和数据
			const [total, products] = await Promise.all([
				db.product.count({ where }),
				db.product.findMany({
					where,
					skip: offset,
					take: limit,
					orderBy: { createdAt: "desc" },
					include: {
						_count: {
							select: {
								featureRequests: true,
							},
						},
					},
				}),
			]);

			return c.json({
				success: true,
				data: {
					products,
					pagination: {
						page,
						limit,
						total,
						totalPages: Math.ceil(total / limit),
					},
				},
			});
		} catch (error) {
			console.error("获取产品列表失败:", error);
			return c.json(
				{
					success: false,
					error: "获取产品列表失败",
				},
				500,
			);
		}
	})

	// 获取单个产品详情
	.get("/products/:id", async (c) => {
		try {
			const id = c.req.param("id");

			const product = await db.product.findUnique({
				where: { id },
				include: {
					featureRequests: {
						orderBy: { createdAt: "desc" },
						include: {
							_count: {
								select: {
									votes: true,
									comments: true,
								},
							},
						},
					},
					_count: {
						select: {
							featureRequests: true,
						},
					},
				},
			});

			if (!product) {
				return c.json(
					{
						success: false,
						error: "产品不存在",
					},
					404,
				);
			}

			return c.json({
				success: true,
				data: product,
			});
		} catch (error) {
			console.error("获取产品详情失败:", error);
			return c.json(
				{
					success: false,
					error: "获取产品详情失败",
				},
				500,
			);
		}
	})

	// 创建产品
	.post("/products", zValidator("json", createProductSchema), async (c) => {
		try {
			const data = c.req.valid("json");

			// 检查产品名称是否已存在
			const existingProduct = await db.product.findUnique({
				where: { name: data.name },
			});

			if (existingProduct) {
				return c.json(
					{
						success: false,
						error: "产品名称已存在",
					},
					400,
				);
			}

			const product = await db.product.create({
				data,
				include: {
					_count: {
						select: {
							featureRequests: true,
						},
					},
				},
			});

			return c.json(
				{
					success: true,
					data: product,
					message: "产品创建成功",
				},
				201,
			);
		} catch (error) {
			console.error("创建产品失败:", error);
			return c.json(
				{
					success: false,
					error: "创建产品失败",
				},
				500,
			);
		}
	})

	// 更新产品
	.put(
		"/products/:id",
		zValidator("json", updateProductSchema),
		async (c) => {
			try {
				const id = c.req.param("id");
				const data = c.req.valid("json");

				// 检查产品是否存在
				const existingProduct = await db.product.findUnique({
					where: { id },
				});

				if (!existingProduct) {
					return c.json(
						{
							success: false,
							error: "产品不存在",
						},
						404,
					);
				}

				// 如果更新名称，检查是否与其他产品重复
				if (data.name && data.name !== existingProduct.name) {
					const duplicateProduct = await db.product.findUnique({
						where: { name: data.name },
					});

					if (duplicateProduct) {
						return c.json(
							{
								success: false,
								error: "产品名称已存在",
							},
							400,
						);
					}
				}

				const product = await db.product.update({
					where: { id },
					data,
					include: {
						_count: {
							select: {
								featureRequests: true,
							},
						},
					},
				});

				return c.json({
					success: true,
					data: product,
					message: "产品更新成功",
				});
			} catch (error) {
				console.error("更新产品失败:", error);
				return c.json(
					{
						success: false,
						error: "更新产品失败",
					},
					500,
				);
			}
		},
	)

	// 删除产品
	.delete("/products/:id", async (c) => {
		try {
			const id = c.req.param("id");

			// 检查产品是否存在
			const existingProduct = await db.product.findUnique({
				where: { id },
				include: {
					_count: {
						select: {
							featureRequests: true,
						},
					},
				},
			});

			if (!existingProduct) {
				return c.json(
					{
						success: false,
						error: "产品不存在",
					},
					404,
				);
			}

			// 检查是否有关联的特性请求
			if (existingProduct._count.featureRequests > 0) {
				return c.json(
					{
						success: false,
						error: `无法删除产品，该产品下还有 ${existingProduct._count.featureRequests} 个特性请求`,
					},
					400,
				);
			}

			await db.product.delete({
				where: { id },
			});

			return c.json({
				success: true,
				message: "产品删除成功",
			});
		} catch (error) {
			console.error("删除产品失败:", error);
			return c.json(
				{
					success: false,
					error: "删除产品失败",
				},
				500,
			);
		}
	});
