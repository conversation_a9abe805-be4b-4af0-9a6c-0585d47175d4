import { PostListItem } from "@marketing/blog/components/PostListItem";
import { allPosts } from "content-collections";
import { getLocale, getTranslations } from "next-intl/server";

export async function generateMetadata() {
	const t = await getTranslations();
	return {
		title: t("blog.title"),
	};
}

export default async function BlogListPage() {
	const locale = await getLocale();
	const t = await getTranslations();

	const publishedPosts = allPosts
		.filter((post) => post.published && locale === post.locale)
		.sort(
			(a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
		);

	return (
		<section className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 dark:from-slate-900 dark:via-blue-900/20 dark:to-purple-900/10">
			{/* 背景装饰元素 */}
			<div className="fixed inset-0 overflow-hidden pointer-events-none">
				<div className="absolute -top-40 -left-40 w-80 h-80 bg-gradient-to-r from-blue-200/30 to-purple-200/30 dark:from-blue-800/20 dark:to-purple-800/20 rounded-full blur-3xl animate-pulse" />
				<div className="absolute top-1/3 -right-20 w-60 h-60 bg-gradient-to-r from-pink-200/20 to-blue-200/20 dark:from-pink-800/15 dark:to-blue-800/15 rounded-full blur-3xl animate-pulse delay-1000" />
				<div className="absolute bottom-20 left-1/4 w-40 h-40 bg-gradient-to-r from-purple-200/20 to-pink-200/20 dark:from-purple-800/15 dark:to-pink-800/15 rounded-full blur-3xl animate-pulse delay-2000" />
			</div>

			{/* 页面标题区域 */}
			<div className="relative container mx-auto px-4 pt-20 pb-12">
				<div className="text-center">
					{/* 浮动装饰元素 */}
					<div className="absolute top-8 left-1/4 w-3 h-3 bg-blue-400 dark:bg-blue-300 rounded-full animate-bounce opacity-60" />
					<div className="absolute top-12 right-1/3 w-2 h-2 bg-purple-400 dark:bg-purple-300 rounded-full animate-bounce delay-500 opacity-60" />
					<div className="absolute top-16 right-1/4 w-2.5 h-2.5 bg-pink-400 dark:bg-pink-300 rounded-full animate-bounce delay-1000 opacity-60" />

					{/* 标题 */}
					<div className="group relative">
						<div className="absolute inset-0 bg-gradient-to-r from-blue-200/20 via-purple-200/20 to-pink-200/20 dark:from-blue-800/20 dark:via-purple-800/20 dark:to-pink-800/20 rounded-3xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10" />
						<div className="relative bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border border-white/30 dark:border-gray-700/50 rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
							<h1 className="mb-4 font-bold text-4xl md:text-5xl bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
								{t("blog.title")}
							</h1>
							<p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
								{t("blog.description")}
							</p>

							{/* 统计信息 */}
							<div className="mt-6 flex justify-center items-center gap-6 text-sm text-gray-500 dark:text-gray-400">
								<div className="flex items-center gap-2">
									<div className="w-2 h-2 bg-blue-400 rounded-full" />
									<span>
										{publishedPosts.length}{" "}
										{publishedPosts.length === 1
											? "Article"
											: "Articles"}
									</span>
								</div>
								<div className="flex items-center gap-2">
									<div className="w-2 h-2 bg-purple-400 rounded-full" />
									<span>Updated Regularly</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* 博客文章列表 */}
			<div className="relative container mx-auto px-4 pb-16">
				{publishedPosts.length > 0 ? (
					<div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
						{publishedPosts.map((post, index) => (
							<div
								key={post.path}
								style={{
									animationDelay: `${index * 100}ms`,
								}}
								className="animate-in slide-in-from-bottom-5 duration-500"
							>
								<PostListItem post={post} />
							</div>
						))}
					</div>
				) : (
					<div className="text-center py-20">
						<div className="group relative inline-block">
							<div className="absolute inset-0 bg-gradient-to-r from-gray-200/20 to-gray-300/20 dark:from-gray-700/20 dark:to-gray-600/20 rounded-2xl blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
							<div className="relative bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border border-white/30 dark:border-gray-700/50 rounded-2xl p-12 shadow-lg">
								<div className="text-6xl mb-4">📝</div>
								<h3 className="text-xl font-semibold text-gray-600 dark:text-gray-300 mb-2">
									No articles yet
								</h3>
								<p className="text-gray-500 dark:text-gray-400">
									Check back soon for exciting content!
								</p>
							</div>
						</div>
					</div>
				)}
			</div>
		</section>
	);
}
