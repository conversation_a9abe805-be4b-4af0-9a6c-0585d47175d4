import { createStorageProviderFromEnv } from "@repo/storage";
import type { StorageProviderType } from "@repo/storage";
import { type NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
	try {
		const searchParams = request.nextUrl.searchParams;
		const provider = searchParams.get(
			"provider",
		) as StorageProviderType | null;
		const bucket = searchParams.get("bucket");
		const region = searchParams.get("region");

		if (!bucket) {
			return NextResponse.json(
				{ error: "缺少必要参数 bucket" },
				{ status: 400 },
			);
		}

		// 创建存储提供商实例
		let storageProvider;

		try {
			// 如果指定了provider，使用指定的provider
			if (provider) {
				storageProvider = createStorageProviderFromEnv(provider);
			} else {
				// 否则尝试从环境变量中检测默认提供商
				storageProvider = createStorageProviderFromEnv();
			}
		} catch (error) {
			console.error("创建存储提供商失败:", error);
			return NextResponse.json(
				{
					error:
						error instanceof Error
							? error.message
							: "无法创建存储提供商",
				},
				{ status: 500 },
			);
		}

		// 检查存储提供商是否支持 listObjects 方法
		if (typeof (storageProvider as any).listObjects === "function") {
			try {
				const files = await (storageProvider as any).listObjects({
					bucket,
					region: region || undefined,
				});
				return NextResponse.json({ files });
			} catch (listError) {
				console.log("列出文件失败，使用模拟数据", listError);
				// 如果列出失败，回退到模拟数据
			}
		}

		// 模拟的文件列表
		const mockFiles = [
			{
				name: "示例图片1.jpg",
				path: "uploads/example-image1.jpg",
				url: "https://example.com/sample1.jpg",
			},
			{
				name: "示例文档.pdf",
				path: "uploads/example-doc.pdf",
				url: "https://example.com/sample-doc.pdf",
			},
			{
				name: "测试文件.txt",
				path: "uploads/test-file.txt",
				url: "https://example.com/test.txt",
			},
		];

		return NextResponse.json({
			files: mockFiles,
			isMockData: true,
			message:
				"注意：这是模拟数据，因为当前存储提供商不支持列出文件或列出失败",
		});
	} catch (error) {
		console.error("列出文件错误:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "列出文件失败",
			},
			{ status: 500 },
		);
	}
}
