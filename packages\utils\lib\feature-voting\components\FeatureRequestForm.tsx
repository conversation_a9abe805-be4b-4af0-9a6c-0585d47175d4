"use client";

import React from "react";
import { useFeatureRequestForm } from "../hooks/use-feature-request-form";
import type { CreateFeatureRequestData, Product } from "../types";

interface FeatureRequestFormProps {
	products: Product[];
	onSubmit: (data: CreateFeatureRequestData) => Promise<void>;
	defaultProductId?: string;
	showProductSelect?: boolean;
	showAuthorFields?: boolean;
	requireAuthorInfo?: boolean;
	className?: string;
	onCancel?: () => void;
	submitButtonText?: string;
	cancelButtonText?: string;
}

export function FeatureRequestForm({
	products,
	onSubmit,
	defaultProductId = "",
	showProductSelect = true,
	showAuthorFields = true,
	requireAuthorInfo = false,
	className = "",
	onCancel,
	submitButtonText = "提交建议",
	cancelButtonText = "取消",
}: FeatureRequestFormProps) {
	const {
		formData,
		errors,
		isSubmitting,
		isSubmitted,
		handleSubmit,
		handleFieldChange,
		resetForm,
		validateField,
	} = useFeatureRequestForm({
		onSubmit,
		requireAuthorInfo,
	});

	// 设置默认产品
	React.useEffect(() => {
		if (defaultProductId && !formData.productId) {
			handleFieldChange("productId", defaultProductId);
		}
	}, [defaultProductId, formData.productId, handleFieldChange]);

	// 提交成功后显示成功消息
	if (isSubmitted) {
		return (
			<div
				className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}
			>
				<div className="text-center">
					<div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100 mb-4">
						<svg
							className="h-6 w-6 text-green-600"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
							xmlns="http://www.w3.org/2000/svg"
							aria-label="成功图标"
						>
							<title>成功</title>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M5 13l4 4L19 7"
							/>
						</svg>
					</div>
					<h3 className="text-lg font-medium text-gray-900 mb-2">
						提交成功！
					</h3>
					<p className="text-gray-600 mb-4">
						感谢您的建议！我们会认真考虑您的反馈。
					</p>
					<div className="flex justify-center gap-3">
						<button
							type="button"
							onClick={resetForm}
							className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
						>
							继续提交
						</button>
						{onCancel && (
							<button
								type="button"
								onClick={onCancel}
								className="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
							>
								完成
							</button>
						)}
					</div>
				</div>
			</div>
		);
	}

	return (
		<form
			onSubmit={handleSubmit}
			className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}
		>
			<div className="mb-6">
				<h2 className="text-xl font-semibold text-gray-900 mb-2">
					提交功能建议
				</h2>
				<p className="text-gray-600">
					描述您希望看到的新功能或改进建议，我们会认真考虑每一个反馈。
				</p>
			</div>

			<div className="space-y-6">
				{/* 产品选择 */}
				{showProductSelect && (
					<div>
						<label
							htmlFor="productId"
							className="block text-sm font-medium text-gray-700 mb-2"
						>
							产品 <span className="text-red-500">*</span>
						</label>
						<select
							id="productId"
							value={formData.productId}
							onChange={(e) =>
								handleFieldChange("productId", e.target.value)
							}
							onBlur={() => validateField("productId")}
							className={`block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
								errors.productId
									? "border-red-300"
									: "border-gray-300"
							}`}
						>
							<option value="">请选择产品...</option>
							{products.map((product) => (
								<option key={product.id} value={product.id}>
									{product.name}
								</option>
							))}
						</select>
						{errors.productId && (
							<p className="mt-1 text-sm text-red-600">
								{errors.productId}
							</p>
						)}
					</div>
				)}

				{/* 标题 */}
				<div>
					<label
						htmlFor="title"
						className="block text-sm font-medium text-gray-700 mb-2"
					>
						功能标题 <span className="text-red-500">*</span>
					</label>
					<input
						type="text"
						id="title"
						value={formData.title}
						onChange={(e) =>
							handleFieldChange("title", e.target.value)
						}
						onBlur={() => validateField("title")}
						placeholder="简要描述您希望的功能..."
						maxLength={200}
						className={`block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
							errors.title ? "border-red-300" : "border-gray-300"
						}`}
					/>
					<div className="mt-1 flex justify-between">
						{errors.title ? (
							<p className="text-sm text-red-600">
								{errors.title}
							</p>
						) : (
							<p className="text-sm text-gray-500">
								简洁明了的标题有助于其他用户理解
							</p>
						)}
						<p className="text-sm text-gray-400">
							{formData.title.length}/200
						</p>
					</div>
				</div>

				{/* 详细描述 */}
				<div>
					<label
						htmlFor="description"
						className="block text-sm font-medium text-gray-700 mb-2"
					>
						详细描述 <span className="text-red-500">*</span>
					</label>
					<textarea
						id="description"
						rows={5}
						value={formData.description}
						onChange={(e) =>
							handleFieldChange("description", e.target.value)
						}
						onBlur={() => validateField("description")}
						placeholder="详细描述这个功能的用途、使用场景和预期效果..."
						maxLength={2000}
						className={`block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
							errors.description
								? "border-red-300"
								: "border-gray-300"
						}`}
					/>
					<div className="mt-1 flex justify-between">
						{errors.description ? (
							<p className="text-sm text-red-600">
								{errors.description}
							</p>
						) : (
							<p className="text-sm text-gray-500">
								详细的描述有助于我们更好地理解您的需求
							</p>
						)}
						<p className="text-sm text-gray-400">
							{formData.description.length}/2000
						</p>
					</div>
				</div>

				{/* 作者信息 */}
				{showAuthorFields && (
					<div className="border-t border-gray-200 pt-6">
						<h3 className="text-lg font-medium text-gray-900 mb-4">
							联系信息
							{!requireAuthorInfo && (
								<span className="text-sm font-normal text-gray-500">
									（可选）
								</span>
							)}
						</h3>
						<p className="text-sm text-gray-600 mb-4">
							留下您的联系信息，我们可能会就您的建议与您进一步沟通。
						</p>

						<div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
							{/* 姓名 */}
							<div>
								<label
									htmlFor="authorName"
									className="block text-sm font-medium text-gray-700 mb-2"
								>
									姓名{" "}
									{requireAuthorInfo && (
										<span className="text-red-500">*</span>
									)}
								</label>
								<input
									type="text"
									id="authorName"
									value={formData.authorName}
									onChange={(e) =>
										handleFieldChange(
											"authorName",
											e.target.value,
										)
									}
									onBlur={() => validateField("authorName")}
									placeholder="请输入您的姓名"
									className={`block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
										errors.authorName
											? "border-red-300"
											: "border-gray-300"
									}`}
								/>
								{errors.authorName && (
									<p className="mt-1 text-sm text-red-600">
										{errors.authorName}
									</p>
								)}
							</div>

							{/* 邮箱 */}
							<div>
								<label
									htmlFor="authorEmail"
									className="block text-sm font-medium text-gray-700 mb-2"
								>
									邮箱地址
								</label>
								<input
									type="email"
									id="authorEmail"
									value={formData.authorEmail}
									onChange={(e) =>
										handleFieldChange(
											"authorEmail",
											e.target.value,
										)
									}
									onBlur={() => validateField("authorEmail")}
									placeholder="<EMAIL>"
									className={`block w-full px-3 py-2 border rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
										errors.authorEmail
											? "border-red-300"
											: "border-gray-300"
									}`}
								/>
								{errors.authorEmail ? (
									<p className="mt-1 text-sm text-red-600">
										{errors.authorEmail}
									</p>
								) : (
									<p className="mt-1 text-sm text-gray-500">
										我们会在功能状态更新时通知您
									</p>
								)}
							</div>
						</div>
					</div>
				)}

				{/* 通用错误信息 */}
				{errors.general && (
					<div className="bg-red-50 border border-red-200 rounded-md p-4">
						<div className="flex">
							<svg
								className="h-5 w-5 text-red-400"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
								xmlns="http://www.w3.org/2000/svg"
								aria-label="错误图标"
							>
								<title>错误</title>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
								/>
							</svg>
							<div className="ml-3">
								<p className="text-sm text-red-800">
									{errors.general}
								</p>
							</div>
						</div>
					</div>
				)}
			</div>

			{/* 表单按钮 */}
			<div className="mt-8 flex justify-end gap-3">
				{onCancel && (
					<button
						type="button"
						onClick={onCancel}
						disabled={isSubmitting}
						className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
					>
						{cancelButtonText}
					</button>
				)}
				<button
					type="submit"
					disabled={isSubmitting}
					className="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
				>
					{isSubmitting ? (
						<>
							<svg
								className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
								fill="none"
								viewBox="0 0 24 24"
								xmlns="http://www.w3.org/2000/svg"
								aria-label="加载图标"
							>
								<title>加载中</title>
								<circle
									className="opacity-25"
									cx="12"
									cy="12"
									r="10"
									stroke="currentColor"
									strokeWidth="4"
								/>
								<path
									className="opacity-75"
									fill="currentColor"
									d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
								/>
							</svg>
							提交中...
						</>
					) : (
						submitButtonText
					)}
				</button>
			</div>
		</form>
	);
}
