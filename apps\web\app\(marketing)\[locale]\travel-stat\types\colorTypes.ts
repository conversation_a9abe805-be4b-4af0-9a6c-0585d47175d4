// 颜色主题系统的类型定义

// 单个访问级别的颜色配置
export interface VisitLevelColor {
	rgba: string; // 用于地图填充的RGBA颜色
	hex: string; // 用于UI显示的十六进制颜色
	description: string; // 访问级别描述
}

// 颜色主题配置
export interface ColorTheme {
	id: string;
	name: string;
	description: string;
	category: "classic" | "modern" | "nature" | "vibrant" | "minimal";
	colors: {
		unvisited: VisitLevelColor;
		level1: VisitLevelColor; // 1次访问
		level2: VisitLevelColor; // 2次访问
		level3: VisitLevelColor; // 3次访问
		level4: VisitLevelColor; // 4次访问
		level5: VisitLevelColor; // 5次访问
		level6to10: VisitLevelColor; // 6-10次访问
		level10plus: VisitLevelColor; // 10+次访问
	};
	// 主题预览颜色（用于选择器显示）
	previewColors: string[];
	// 是否推荐用于特定地图样式
	recommendedForMapStyles?: string[];
}

// 颜色主题类型
export type ColorThemeType =
	| "classic-blue-green" // 经典蓝绿色系
	| "warm-sunset" // 暖色日落色系
	| "cool-ocean" // 冷色海洋色系
	| "vibrant-rainbow" // 彩虹色系
	| "earth-tones" // 大地色系
	| "purple-pink" // 紫粉色系
	| "monochrome" // 单色系
	| "high-contrast" // 高对比度
	| "pastel-soft" // 柔和糖果色
	| "neon-bright"; // 霓虹亮色
