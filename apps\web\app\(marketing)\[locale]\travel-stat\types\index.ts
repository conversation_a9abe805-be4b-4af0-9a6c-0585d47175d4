// =============================================================================
// 统一类型导出 - Travel Stat 模块
// 提供所有类型的统一导入入口，简化导入路径
// =============================================================================

// ===== 基础业务类型 =====
export interface TravelPoint {
	id: string;
	name: string;
	coordinates: [number, number]; // [lng, lat]
	country: string;
	city: string;
	timestamp: number;
	photos?: string[]; // 保持向后兼容
	images?: import("@repo/database/src/types/travel-diary").TravelPointImage[]; // 新的图片数组
	description?: string;
	tags?: string[];
	date?: Date; // 用户选择的日期
}

export interface CountryData {
	name: string;
	code: string;
	visitCount: number;
	visitedCities: string[];
	totalPoints: number;
	firstVisit: string;
	lastVisit: string;
}

export interface GeocodeFeature {
	id: string;
	place_name: string;
	center: [number, number];
	context?: Array<{ id: string; text: string; short_code?: string }>;
}

export interface GeocodeResponse {
	features: GeocodeFeature[];
}

// ===== 地图相关类型 =====
export type MapStyleType =
	| "streets"
	| "outdoors"
	| "light"
	| "dark"
	| "satellite"
	| "satellite-streets"
	| "navigation-day"
	| "navigation-night";

export type MapProjectionType =
	| "mercator"
	| "globe"
	| "albers"
	| "lambertConformalConic"
	| "equalEarth"
	| "equirectangular"
	| "naturalEarth"
	| "winkelTripel";

// ===== 大气层相关类型 =====
export type AtmosphereTheme =
	| "day"
	| "night"
	| "sunset"
	| "dawn"
	| "aurora"
	| "deep-space"
	| "ocean"
	| "minimal";

export interface AtmosphereConfig {
	color: string;
	"high-color": string;
	"space-color": string;
	"star-intensity": number;
	"horizon-blend": number;
	range: [number, number];
	"vertical-range"?: [number, number];
}

// ===== 动画相关类型 =====
export type AnimationTheme =
	| "shooting-stars"
	| "floating-particles"
	| "aurora"
	| "minimal"
	| "galaxy"
	| "none";

export interface AnimationConfig {
	theme: AnimationTheme;
	intensity: "low" | "medium" | "high";
	colors: {
		primary: string;
		secondary: string;
		accent: string;
	};
	speed: number;
}

// ===== 标记相关类型 =====
export type MarkerStyleType =
	| "classic"
	| "gradient-pulse"
	| "neon-glow"
	| "particle-effect"
	| "constellation"
	| "seasonal"
	| "artistic"
	| "badge";

export interface BaseMarkerProps {
	point: TravelPoint;
	onRemovePoint: (pointId: string) => void;
	isSelected?: boolean;
	scale?: number;
	animationSpeed?: "slow" | "normal" | "fast";
	colorTheme?: string;
}

export type GradientPulseTheme =
	| "ocean"
	| "sunset"
	| "forest"
	| "aurora"
	| "cosmic"
	| "fire"
	| "ice"
	| "electric";

export interface GradientPulseConfig {
	primaryColor: string;
	secondaryColor: string;
	pulseSpeed: number;
	glowSize: number;
	intensity: number;
}

// ===== 颜色主题类型 =====
export type ColorThemeType =
	| "classic-blue-green"
	| "warm-sunset"
	| "cool-ocean"
	| "vibrant-rainbow"
	| "earth-tones"
	| "purple-pink"
	| "monochrome"
	| "high-contrast"
	| "pastel-soft"
	| "neon-bright";

export interface VisitLevelColor {
	rgba: string;
	hex: string;
	description: string;
}

export interface ColorTheme {
	id: string;
	name: string;
	description: string;
	category: "classic" | "modern" | "nature" | "vibrant" | "minimal";
	colors: {
		unvisited: VisitLevelColor;
		level1: VisitLevelColor;
		level2: VisitLevelColor;
		level3: VisitLevelColor;
		level4: VisitLevelColor;
		level5: VisitLevelColor;
		level6to10: VisitLevelColor;
		level10plus: VisitLevelColor;
	};
	previewColors: string[];
	recommendedForMapStyles?: string[];
}

// ===== 卡片生成器类型 =====
export interface TravelStats {
	totalPoints: number;
	citiesCount: number;
	countriesCount: number;
	totalDistance?: number;
	totalDays?: number;
}

export interface UserProfile {
	username?: string;
	avatar?: string;
	bio?: string;
}

export interface CardCustomization {
	colors?: {
		primary?: string;
		secondary?: string;
		accent?: string;
		background?: string;
		text?: string;
	};
	typography?: {
		fontFamily?: string;
		headerSize?: number;
		bodySize?: number;
		titleWeight?: number;
	};
	layout?: {
		padding?: number;
		spacing?: number;
		borderRadius?: number;
		showShadow?: boolean;
	};
	content?: {
		showDate?: boolean;
		showUserInfo?: boolean;
		showDetailedStats?: boolean;
		customTitle?: string;
		customFooter?: string;
	};
}

export type SocialPlatform =
	| "instagram"
	| "wechat"
	| "weibo"
	| "twitter"
	| "facebook";
export type ExportQuality = "low" | "medium" | "high";

// ===== React 相关类型 =====
export type { ReactNode, RefObject } from "react";

// ===== 全局类型声明 =====
declare global {
	namespace mapboxgl {
		interface Map {}
	}
}

// ===== 类型工具 =====
export type ArrayElement<T> = T extends readonly (infer U)[] ? U : never;
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// ===== 导出工具类型 =====
export interface ExportOptions {
	format: "png" | "jpeg";
	quality: number;
	scale: number;
	addWatermark: boolean;
	addStats: boolean;
	filename?: string;
	method?: "html2canvas" | "layered" | "svg" | "basic";
}

export interface CardExportOptions {
	platform: SocialPlatform;
	quality: ExportQuality;
	filename?: string;
}

export interface CardExportResult {
	success: boolean;
	blob?: Blob;
	dataURL?: string;
	filename?: string;
	width?: number;
	height?: number;
	error?: string;
}
