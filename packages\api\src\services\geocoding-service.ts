/**
 * 地理编码服务 - 统一多提供商接口
 * 支持 Google Maps, Bing Maps, Mapbox, 百度地图, 高德地图
 * 内置负载均衡和故障转移机制
 */

import { logger } from "@repo/logs";
import {
	type GeocodingOptions,
	type GeocodingServiceConfig,
	type UnifiedGeocodingResult,
	UnifiedGeocodingService,
	createChinaOptimizedConfig,
} from "./geocoding";

/**
 * 兼容性接口 - 保持向后兼容
 */
export interface GeocodingResult {
	longitude: number;
	latitude: number;
	address: string;
	country?: string;
	province?: string;
	city?: string;
	district?: string;
	street?: string;
	confidence: "high" | "medium" | "low";
	placeId?: string;
}

/**
 * 地理编码服务
 * 统一封装多个地理编码提供商，实现负载均衡和故障转移
 */
export class GeocodingService {
	private unifiedService: UnifiedGeocodingService;

	constructor(config?: GeocodingServiceConfig) {
		// 如果没有提供配置，使用环境变量自动配置
		const finalConfig = config || this.createConfigFromEnv();

		this.unifiedService = new UnifiedGeocodingService(finalConfig);

		logger.info("地理编码服务初始化完成", {
			providersCount: finalConfig.providers.length,
			strategy: finalConfig.strategy,
		});
	}

	/**
	 * 正向地理编码 - 地址转坐标
	 */
	async geocode(
		address: string,
		options: GeocodingOptions = {},
	): Promise<GeocodingResult | null> {
		try {
			const result = await this.unifiedService.geocode(address, options);
			return result ? this.convertToLegacyFormat(result) : null;
		} catch (error) {
			logger.error("地理编码失败", {
				address,
				error: error instanceof Error ? error.message : String(error),
			});
			return null;
		}
	}

	/**
	 * 反向地理编码 - 坐标转地址
	 */
	async reverseGeocode(
		longitude: number,
		latitude: number,
		options: GeocodingOptions = {},
	): Promise<GeocodingResult | null> {
		try {
			const result = await this.unifiedService.reverseGeocode(
				longitude,
				latitude,
				options,
			);
			return result ? this.convertToLegacyFormat(result) : null;
		} catch (error) {
			logger.error("反向地理编码失败", {
				longitude,
				latitude,
				error: error instanceof Error ? error.message : String(error),
			});
			return null;
		}
	}

	/**
	 * 批量地理编码
	 */
	async batchGeocode(
		addresses: string[],
		options: GeocodingOptions = {},
	): Promise<{ address: string; result: GeocodingResult | null }[]> {
		try {
			const results = await this.unifiedService.batchGeocode(
				addresses,
				options,
			);
			return results.map((item) => ({
				address: item.address,
				result: item.result
					? this.convertToLegacyFormat(item.result)
					: null,
			}));
		} catch (error) {
			logger.error("批量地理编码失败", {
				addressesCount: addresses.length,
				error: error instanceof Error ? error.message : String(error),
			});
			return addresses.map((address) => ({ address, result: null }));
		}
	}

	/**
	 * 智能地理编码：尝试多种策略获取最佳结果
	 */
	async smartGeocode(
		address: string,
		options: GeocodingOptions = {},
	): Promise<GeocodingResult | null> {
		try {
			const result = await this.unifiedService.smartGeocode(
				address,
				options,
			);
			return result ? this.convertToLegacyFormat(result) : null;
		} catch (error) {
			logger.error("智能地理编码失败", {
				address,
				error: error instanceof Error ? error.message : String(error),
			});
			return null;
		}
	}

	/**
	 * 获取统一服务实例（用于高级功能）
	 */
	getUnifiedService(): UnifiedGeocodingService {
		return this.unifiedService;
	}

	/**
	 * 获取服务统计信息
	 */
	getStats() {
		return this.unifiedService.getStats();
	}

	/**
	 * 重置统计信息
	 */
	resetStats() {
		this.unifiedService.resetStats();
	}

	/**
	 * 清除缓存
	 */
	clearCache() {
		this.unifiedService.clearCache();
	}

	/**
	 * 从环境变量创建配置
	 */
	private createConfigFromEnv(): GeocodingServiceConfig {
		const apiKeys = {
			google: process.env.GOOGLE_MAPS_API_KEY,
			bing: process.env.BING_MAPS_API_KEY,
			mapbox: process.env.MAPBOX_API_KEY,
			baidu: process.env.BAIDU_MAPS_API_KEY,
			amap: process.env.AMAP_API_KEY,
		};

		// 过滤掉未配置的API密钥
		const filteredApiKeys = Object.fromEntries(
			Object.entries(apiKeys).filter(([_, value]) => value),
		) as any;

		if (Object.keys(filteredApiKeys).length === 0) {
			logger.warn("未找到任何地理编码API密钥，将使用默认配置");
			// 返回一个空配置，让服务在运行时处理
			return {
				providers: [],
				strategy: "smart",
				enableCache: true,
				cacheTTL: 3600,
			};
		}

		// 使用中国优化配置
		const config = createChinaOptimizedConfig(filteredApiKeys);

		logger.info("从环境变量创建地理编码配置", {
			providers: Object.keys(filteredApiKeys),
			strategy: config.strategy,
		});

		return config;
	}

	/**
	 * 转换为兼容格式
	 */
	private convertToLegacyFormat(
		result: UnifiedGeocodingResult,
	): GeocodingResult {
		return {
			longitude: result.longitude,
			latitude: result.latitude,
			address: result.formattedAddress,
			country: result.addressComponents.country,
			province: result.addressComponents.province,
			city: result.addressComponents.city,
			district: result.addressComponents.district,
			street: result.addressComponents.street,
			confidence: result.confidence,
			placeId: result.placeId,
		};
	}

	/**
	 * 验证坐标是否有效
	 */
	static isValidCoordinates(longitude: number, latitude: number): boolean {
		return UnifiedGeocodingService.isValidCoordinates(longitude, latitude);
	}

	/**
	 * 计算两点间距离（公里）
	 */
	static calculateDistance(
		lon1: number,
		lat1: number,
		lon2: number,
		lat2: number,
	): number {
		return UnifiedGeocodingService.calculateDistance(
			lon1,
			lat1,
			lon2,
			lat2,
		);
	}
}

/**
 * 创建地理编码服务的便捷工厂函数
 */
export function createGeocodingService(
	config?: GeocodingServiceConfig,
): GeocodingService {
	return new GeocodingService(config);
}

/**
 * 创建针对中国优化的地理编码服务
 */
export function createChinaGeocodingService(apiKeys: {
	amap?: string;
	baidu?: string;
	google?: string;
	bing?: string;
	mapbox?: string;
}): GeocodingService {
	const config = createChinaOptimizedConfig(apiKeys);
	return new GeocodingService(config);
}
