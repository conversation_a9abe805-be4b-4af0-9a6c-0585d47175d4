"use client";

import type { Editor } from "@tiptap/react";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { AlertTriangle, CheckCircle, RefreshCw, Wrench } from "lucide-react";
import React, { useState, useEffect, useRef } from "react";
import { toast } from "sonner";
import { v4 as uuidv4 } from "uuid";

interface ValidationIssue {
	type: "error" | "warning" | "info";
	message: string;
	position?: number;
	fix?: () => void;
}

interface ContentValidatorProps {
	editor: Editor | null;
	className?: string;
}

export const ContentValidator: React.FC<ContentValidatorProps> = ({
	editor,
	className,
}) => {
	const [issues, setIssues] = useState<ValidationIssue[]>([]);
	const [isValidating, setIsValidating] = useState(false);
	const [lastValidation, setLastValidation] = useState<Date | null>(null);
	const validationTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const lastValidationContentRef = useRef<string>("");

	// 验证内容结构
	const validateContent = () => {
		if (!editor) {
			console.log(
				"[ContentValidator] validateContent: editor为空，跳过验证",
			);
			return;
		}

		// 检查是否正在验证中
		if (isValidating) {
			console.log(
				"[ContentValidator] validateContent: 正在验证中，跳过重复验证",
			);
			return;
		}

		// 检查内容是否有变化
		const currentContent = editor.getHTML();
		if (currentContent === lastValidationContentRef.current) {
			console.log(
				"[ContentValidator] validateContent: 内容未变化，跳过验证",
			);
			return;
		}

		console.log("[ContentValidator] validateContent: 开始验证内容", {
			timestamp: new Date().toISOString(),
			isValidating,
			editorDestroyed: editor.isDestroyed,
			contentChanged: currentContent !== lastValidationContentRef.current,
		});

		// 更新最后验证的内容
		lastValidationContentRef.current = currentContent;
		setIsValidating(true);
		const foundIssues: ValidationIssue[] = [];

		try {
			const { state } = editor;
			const doc = state.doc;

			console.log("[ContentValidator] 文档结构:", {
				nodeCount: doc.nodeSize,
				childCount: doc.childCount,
				content: doc.content.toString(),
			});

			// 检查时间线节点
			const timelineNodes: Array<{ node: any; pos: number }> = [];
			const pointNodes: Array<{ node: any; pos: number }> = [];

			doc.descendants((node: any, pos: number) => {
				if (node.type.name === "travelTimeline") {
					timelineNodes.push({ node, pos });
				} else if (node.type.name === "travelPoint") {
					pointNodes.push({ node, pos });
				}
			});

			// 验证时间线节点
			timelineNodes.forEach(({ node, pos }) => {
				// 检查必需属性
				if (!node.attrs.timelineId) {
					foundIssues.push({
						type: "error",
						message: `时间线缺少ID (位置: ${pos})`,
						position: pos,
						fix: () => {
							editor
								.chain()
								.focus()
								.command(({ tr }) => {
									tr.setNodeMarkup(pos, undefined, {
										...node.attrs,
										timelineId: uuidv4(),
									});
									return true;
								})
								.run();
						},
					});
				}

				if (!node.attrs.title || node.attrs.title.trim() === "") {
					foundIssues.push({
						type: "warning",
						message: `时间线标题为空 (位置: ${pos})`,
						position: pos,
						fix: () => {
							editor
								.chain()
								.focus()
								.command(({ tr }) => {
									tr.setNodeMarkup(pos, undefined, {
										...node.attrs,
										title: `第${new Date().toLocaleDateString("zh-CN")}天`,
									});
									return true;
								})
								.run();
						},
					});
				}

				if (!node.attrs.timelineDate) {
					foundIssues.push({
						type: "warning",
						message: `时间线缺少日期 (位置: ${pos})`,
						position: pos,
						fix: () => {
							editor
								.chain()
								.focus()
								.command(({ tr }) => {
									tr.setNodeMarkup(pos, undefined, {
										...node.attrs,
										timelineDate: new Date().toISOString(),
									});
									return true;
								})
								.run();
						},
					});
				}

				// 检查是否有子点位
				if (!node.content || node.content.childCount === 0) {
					foundIssues.push({
						type: "info",
						message: `时间线没有点位 (位置: ${pos})`,
						position: pos,
						fix: () => {
							editor
								.chain()
								.focus()
								.command(({ commands }) => {
									const pointContent = {
										type: "travelPoint",
										attrs: {
											pointId: uuidv4(),
											location: "新地点",
											pointDate: new Date().toISOString(),
										},
										content: [{ type: "paragraph" }],
									};
									return commands.insertContentAt(
										pos + node.nodeSize - 2,
										pointContent,
									);
								})
								.run();
						},
					});
				}
			});

			// 验证点位节点
			pointNodes.forEach(({ node, pos }) => {
				if (!node.attrs.pointId) {
					foundIssues.push({
						type: "error",
						message: `点位缺少ID (位置: ${pos})`,
						position: pos,
						fix: () => {
							editor
								.chain()
								.focus()
								.command(({ tr }) => {
									tr.setNodeMarkup(pos, undefined, {
										...node.attrs,
										pointId: uuidv4(),
									});
									return true;
								})
								.run();
						},
					});
				}

				if (!node.attrs.location || node.attrs.location.trim() === "") {
					foundIssues.push({
						type: "warning",
						message: `点位地点名称为空 (位置: ${pos})`,
						position: pos,
						fix: () => {
							editor
								.chain()
								.focus()
								.command(({ tr }) => {
									tr.setNodeMarkup(pos, undefined, {
										...node.attrs,
										location: "新地点",
									});
									return true;
								})
								.run();
						},
					});
				}

				if (!node.attrs.pointDate) {
					foundIssues.push({
						type: "warning",
						message: `点位缺少日期 (位置: ${pos})`,
						position: pos,
						fix: () => {
							editor
								.chain()
								.focus()
								.command(({ tr }) => {
									tr.setNodeMarkup(pos, undefined, {
										...node.attrs,
										pointDate: new Date().toISOString(),
									});
									return true;
								})
								.run();
						},
					});
				}
			});

			// 检查孤立的点位（不在时间线内的点位）
			const orphanedPoints = pointNodes.filter(({ pos }) => {
				// 检查这个点位是否在某个时间线内
				let isInTimeline = false;
				timelineNodes.forEach(
					({ node: timelineNode, pos: timelinePos }) => {
						if (
							pos > timelinePos &&
							pos < timelinePos + timelineNode.nodeSize
						) {
							isInTimeline = true;
						}
					},
				);
				return !isInTimeline;
			});

			orphanedPoints.forEach(({ node, pos }) => {
				foundIssues.push({
					type: "warning",
					message: `发现孤立的点位 "${node.attrs.location}" (位置: ${pos})`,
					position: pos,
					fix: () => {
						// 为孤立点位创建一个新的时间线
						const timelineContent = {
							type: "travelTimeline",
							attrs: {
								timelineId: uuidv4(),
								title: `${node.attrs.location}行程`,
								timelineDate:
									node.attrs.pointDate ||
									new Date().toISOString(),
							},
							content: [node.toJSON()],
						};

						editor
							.chain()
							.focus()
							.command(({ tr }) => {
								// 删除原来的孤立点位
								tr.delete(pos, pos + node.nodeSize);
								// 在相同位置插入新的时间线
								tr.insert(
									pos,
									editor.schema.nodeFromJSON(timelineContent),
								);
								return true;
							})
							.run();
					},
				});
			});

			setIssues(foundIssues);
			setLastValidation(new Date());

			console.log("[ContentValidator] 验证完成:", {
				issuesCount: foundIssues.length,
				timelineNodes: timelineNodes.length,
				pointNodes: pointNodes.length,
				orphanedPoints: orphanedPoints?.length || 0,
				issues: foundIssues.map((issue) => ({
					type: issue.type,
					message: issue.message,
				})),
			});

			// 显示验证结果
			if (foundIssues.length === 0) {
				console.log(
					"[ContentValidator] 显示成功toast: 内容结构验证通过",
				);
				toast.success("内容结构验证通过！");
			} else {
				const errorCount = foundIssues.filter(
					(issue) => issue.type === "error",
				).length;
				const warningCount = foundIssues.filter(
					(issue) => issue.type === "warning",
				).length;
				const infoCount = foundIssues.filter(
					(issue) => issue.type === "info",
				).length;

				console.log("[ContentValidator] 显示警告toast:", {
					total: foundIssues.length,
					errors: errorCount,
					warnings: warningCount,
					infos: infoCount,
				});

				toast.warning(
					`发现 ${foundIssues.length} 个问题: ${errorCount} 个错误, ${warningCount} 个警告, ${infoCount} 个提示`,
				);
			}
		} catch (error) {
			console.error("[ContentValidator] 内容验证失败:", error);
			toast.error("内容验证过程中发生错误");
		} finally {
			console.log("[ContentValidator] 验证结束，设置isValidating为false");
			setIsValidating(false);
		}
	};

	// 修复所有问题
	const fixAllIssues = () => {
		if (issues.length === 0) return;

		let fixedCount = 0;
		issues.forEach((issue) => {
			if (issue.fix) {
				try {
					issue.fix();
					fixedCount++;
				} catch (error) {
					console.error("修复问题失败:", error);
				}
			}
		});

		toast.success(`已修复 ${fixedCount} 个问题`);

		// 重新验证
		setTimeout(() => {
			validateContent();
		}, 500);
	};

	// 自动验证（当编辑器内容变化时）
	useEffect(() => {
		if (!editor) {
			console.log(
				"[ContentValidator] useEffect: editor为空，跳过事件监听",
			);
			return;
		}

		console.log("[ContentValidator] useEffect: 设置编辑器更新监听", {
			editorDestroyed: editor.isDestroyed,
		});

		const handleUpdate = () => {
			console.log(
				"[ContentValidator] handleUpdate: 编辑器内容更新，准备延迟验证",
			);

			// 清除之前的验证定时器
			if (validationTimeoutRef.current) {
				console.log(
					"[ContentValidator] handleUpdate: 清除之前的验证定时器",
				);
				clearTimeout(validationTimeoutRef.current);
			}

			// 设置新的验证定时器
			validationTimeoutRef.current = setTimeout(() => {
				console.log("[ContentValidator] handleUpdate: 执行延迟验证");
				validationTimeoutRef.current = null;
				validateContent();
			}, 2000);
		};

		editor.on("update", handleUpdate);

		return () => {
			console.log(
				"[ContentValidator] useEffect cleanup: 移除编辑器更新监听",
			);

			// 清理定时器
			if (validationTimeoutRef.current) {
				clearTimeout(validationTimeoutRef.current);
				validationTimeoutRef.current = null;
			}

			editor.off("update", handleUpdate);
		};
	}, [editor]);

	// 组件卸载时清理定时器
	useEffect(() => {
		return () => {
			if (validationTimeoutRef.current) {
				console.log("[ContentValidator] 组件卸载，清理验证定时器");
				clearTimeout(validationTimeoutRef.current);
			}
		};
	}, []);

	// 获取问题统计
	const getIssueStats = () => {
		const errorCount = issues.filter(
			(issue) => issue.type === "error",
		).length;
		const warningCount = issues.filter(
			(issue) => issue.type === "warning",
		).length;
		const infoCount = issues.filter(
			(issue) => issue.type === "info",
		).length;

		return { errorCount, warningCount, infoCount };
	};

	const { errorCount, warningCount, infoCount } = getIssueStats();

	// 检查是否为紧凑模式
	const isCompactMode = className?.includes("compact-mode");

	if (isCompactMode) {
		// 紧凑模式：单行显示所有验证信息
		return (
			<div className="flex items-center gap-3">
				{/* 验证按钮 */}
				<Button
					onClick={validateContent}
					size="sm"
					variant="outline"
					disabled={isValidating}
					className="h-6 px-2 text-xs border-gray-300 hover:border-gray-400"
				>
					{isValidating ? (
						<RefreshCw className="h-3 w-3 mr-1 animate-spin" />
					) : (
						<CheckCircle className="h-3 w-3 mr-1" />
					)}
					验证
				</Button>

				{/* 修复按钮（仅在有问题时显示） */}
				{issues.length > 0 && (
					<Button
						onClick={fixAllIssues}
						size="sm"
						variant="outline"
						className="h-6 px-2 text-xs text-blue-600 border-blue-300 hover:bg-blue-50"
					>
						<Wrench className="h-3 w-3 mr-1" />
						修复
					</Button>
				)}

				{/* 状态指示器 */}
				<div className="flex items-center gap-2 text-xs">
					{errorCount > 0 && (
						<span className="flex items-center gap-1 text-red-600 bg-red-50 px-2 py-0.5 rounded">
							<AlertTriangle className="h-3 w-3" />
							{errorCount}
						</span>
					)}
					{warningCount > 0 && (
						<span className="flex items-center gap-1 text-yellow-600 bg-yellow-50 px-2 py-0.5 rounded">
							<AlertTriangle className="h-3 w-3" />
							{warningCount}
						</span>
					)}
					{infoCount > 0 && (
						<span className="flex items-center gap-1 text-blue-600 bg-blue-50 px-2 py-0.5 rounded">
							<AlertTriangle className="h-3 w-3" />
							{infoCount}
						</span>
					)}
					{issues.length === 0 && lastValidation && (
						<span className="flex items-center gap-1 text-green-600 bg-green-50 px-2 py-0.5 rounded">
							<CheckCircle className="h-3 w-3" />
							正常
						</span>
					)}
				</div>

				{/* 最后验证时间 */}
				{lastValidation && (
					<div className="text-xs text-gray-400 border-l border-gray-300 pl-2">
						{lastValidation.toLocaleTimeString("zh-CN", {
							hour: "2-digit",
							minute: "2-digit",
						})}
					</div>
				)}
			</div>
		);
	}

	// 完整模式：原有的详细布局
	return (
		<div className={cn("space-y-2", className)}>
			{/* 验证控制栏 */}
			<div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg border">
				<div className="flex items-center gap-2">
					<Button
						onClick={validateContent}
						size="sm"
						variant="outline"
						disabled={isValidating}
						className="h-7 px-2 text-xs"
					>
						{isValidating ? (
							<RefreshCw className="h-3 w-3 mr-1 animate-spin" />
						) : (
							<CheckCircle className="h-3 w-3 mr-1" />
						)}
						验证内容
					</Button>

					{issues.length > 0 && (
						<Button
							onClick={fixAllIssues}
							size="sm"
							variant="outline"
							className="h-7 px-2 text-xs text-blue-600 border-blue-200 hover:bg-blue-50"
						>
							<Wrench className="h-3 w-3 mr-1" />
							修复全部
						</Button>
					)}
				</div>

				{/* 问题统计 */}
				<div className="flex items-center gap-2 text-xs">
					{errorCount > 0 && (
						<span className="text-red-600">{errorCount} 错误</span>
					)}
					{warningCount > 0 && (
						<span className="text-yellow-600">
							{warningCount} 警告
						</span>
					)}
					{infoCount > 0 && (
						<span className="text-blue-600">{infoCount} 提示</span>
					)}
					{issues.length === 0 && lastValidation && (
						<span className="text-green-600 flex items-center gap-1">
							<CheckCircle className="h-3 w-3" />
							无问题
						</span>
					)}
				</div>
			</div>

			{/* 问题列表 */}
			{issues.length > 0 && (
				<div className="space-y-1 max-h-40 overflow-y-auto">
					{issues.map((issue, index) => (
						<div
							key={index}
							className={cn(
								"flex items-center justify-between p-2 rounded border text-xs",
								issue.type === "error" &&
									"bg-red-50 border-red-200 text-red-700",
								issue.type === "warning" &&
									"bg-yellow-50 border-yellow-200 text-yellow-700",
								issue.type === "info" &&
									"bg-blue-50 border-blue-200 text-blue-700",
							)}
						>
							<div className="flex items-center gap-2">
								<AlertTriangle className="h-3 w-3 flex-shrink-0" />
								<span>{issue.message}</span>
							</div>

							{issue.fix && (
								<Button
									onClick={issue.fix}
									size="sm"
									variant="ghost"
									className="h-5 px-1 text-xs"
								>
									修复
								</Button>
							)}
						</div>
					))}
				</div>
			)}

			{/* 最后验证时间 */}
			{lastValidation && (
				<div className="text-xs text-gray-500 text-center">
					最后验证: {lastValidation.toLocaleTimeString("zh-CN")}
				</div>
			)}
		</div>
	);
};
