import { PrismaClient } from "@prisma/client";

declare global {
	// eslint-disable-next-line no-var
	var __travelMemoPrisma: PrismaClient | undefined;
}

export const travelMemoPrisma =
	globalThis.__travelMemoPrisma ??
	new PrismaClient({
		log:
			process.env.NODE_ENV === "development"
				? ["query", "error", "warn"]
				: ["error"],
	});

if (process.env.NODE_ENV !== "production") {
	globalThis.__travelMemoPrisma = travelMemoPrisma;
}
