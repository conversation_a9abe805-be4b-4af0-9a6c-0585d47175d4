"use client";

import { LocaleLink } from "@i18n/routing";
import type { Post } from "content-collections";
import Image from "next/image";

export function PostListItem({ post }: { post: Post }) {
	const { title, excerpt, authorName, image, date, path, authorImage, tags } =
		post;

	return (
		<article className="group relative h-full">
			{/* 悬浮背景效果 */}
			<div className="absolute inset-0 bg-gradient-to-r from-blue-200/20 via-purple-200/20 to-pink-200/20 dark:from-blue-800/20 dark:via-purple-800/20 dark:to-pink-800/20 rounded-2xl blur-sm opacity-0 group-hover:opacity-100 transition-all duration-300 -z-10 scale-105" />

			{/* 主卡片 */}
			<div className="relative h-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-white/30 dark:border-gray-700/50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
				{/* 封面图片 */}
				{image && (
					<div className="relative mb-4 aspect-16/9 overflow-hidden rounded-t-2xl">
						<Image
							src={image}
							alt={title}
							fill
							sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
							className="object-cover object-center transition-transform duration-300 group-hover:scale-105"
						/>
						<div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
						<LocaleLink
							href={`/blog/${path}`}
							className="absolute inset-0 z-10"
						/>
					</div>
				)}

				{/* 内容区域 */}
				<div className="p-6">
					{/* 标签 */}
					{tags && tags.length > 0 && (
						<div className="mb-3 flex flex-wrap gap-2">
							{tags.slice(0, 3).map((tag) => (
								<span
									key={tag}
									className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 text-blue-800 dark:text-blue-300 border border-blue-200/50 dark:border-blue-700/50"
								>
									#{tag}
								</span>
							))}
							{tags.length > 3 && (
								<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400">
									+{tags.length - 3}
								</span>
							)}
						</div>
					)}

					{/* 标题 */}
					<LocaleLink
						href={`/blog/${path}`}
						className="block group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200"
					>
						<h2 className="font-bold text-xl mb-3 line-clamp-2 leading-tight">
							{title}
						</h2>
					</LocaleLink>

					{/* 摘要 */}
					{excerpt && (
						<p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3 leading-relaxed">
							{excerpt}
						</p>
					)}

					{/* 作者和日期信息 */}
					<div className="flex items-center justify-between pt-4 border-t border-gray-200/50 dark:border-gray-700/50">
						{authorName && (
							<div className="flex items-center gap-3">
								{authorImage && (
									<div className="relative size-8 overflow-hidden rounded-full ring-2 ring-white/50 dark:ring-gray-600/50">
										<Image
											src={authorImage}
											alt={authorName}
											fill
											sizes="32px"
											className="object-cover object-center"
										/>
									</div>
								)}
								<div>
									<p className="font-medium text-sm text-gray-700 dark:text-gray-300">
										{authorName}
									</p>
								</div>
							</div>
						)}

						<div className="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
							<div className="w-1.5 h-1.5 bg-gray-400 rounded-full" />
							<time dateTime={date}>
								{new Intl.DateTimeFormat("en-US", {
									month: "short",
									day: "numeric",
									year: "numeric",
								}).format(new Date(date))}
							</time>
						</div>
					</div>
				</div>

				{/* 阅读按钮 */}
				<div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
					<LocaleLink
						href={`/blog/${path}`}
						className="inline-flex items-center gap-2 px-3 py-1.5 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white text-xs font-medium rounded-full shadow-lg hover:shadow-xl transition-all duration-200"
					>
						Read More
						<svg
							className="w-3 h-3"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<title>Arrow Right</title>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M9 5l7 7-7 7"
							/>
						</svg>
					</LocaleLink>
				</div>
			</div>
		</article>
	);
}
