import { NextResponse } from "next/server";
import { initializeAllTestData } from "../../../(saas)/app/feature-voting/test-data";

/**
 * POST /api/feature-voting/init
 * 初始化Feature Voting测试数据
 */
export async function POST() {
	try {
		console.log("开始初始化Feature Voting测试数据...");

		await initializeAllTestData();

		return NextResponse.json({
			success: true,
			message: "测试数据初始化成功",
			timestamp: new Date().toISOString(),
		});
	} catch (error) {
		console.error("初始化测试数据失败:", error);

		return NextResponse.json(
			{
				success: false,
				message: "测试数据初始化失败",
				error: error instanceof Error ? error.message : "未知错误",
				timestamp: new Date().toISOString(),
			},
			{ status: 500 },
		);
	}
}
