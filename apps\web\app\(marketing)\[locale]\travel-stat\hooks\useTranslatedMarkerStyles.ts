import { useTranslations } from "next-intl";
import type { MarkerStyleType } from "../types/markerTypes";

export function useTranslatedMarkerStyles() {
	const t = useTranslations("travelStat.markerStyles");

	const getMarkerStyleName = (style: MarkerStyleType): string => {
		switch (style) {
			case "classic":
				return t("names.classic");
			case "gradient-pulse":
				return t("names.gradient-pulse");
			case "particle-effect":
				return t("names.particle-effect");
			case "hand-drawn":
				return t("names.hand-drawn");
			case "emoji":
				return t("names.emoji");
			case "polaroid":
				return t("names.polaroid");
			default:
				return style;
		}
	};

	const getMarkerStyleDescription = (style: MarkerStyleType): string => {
		switch (style) {
			case "classic":
				return t("descriptions.classic");
			case "gradient-pulse":
				return t("descriptions.gradient-pulse");
			case "particle-effect":
				return t("descriptions.particle-effect");
			case "hand-drawn":
				return t("descriptions.hand-drawn");
			case "emoji":
				return t("descriptions.emoji");
			case "polaroid":
				return t("descriptions.polaroid");
			default:
				return "";
		}
	};

	return {
		t,
		getMarkerStyleName,
		getMarkerStyleDescription,
	};
}
