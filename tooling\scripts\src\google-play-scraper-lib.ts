import fs from "node:fs";
import path from "node:path";
import * as createCsvWriter from "csv-writer";
import gplay from "google-play-scraper";

interface Review {
	reviewId: string;
	reviewer: string;
	rating: number;
	date: string;
	content: string;
	helpful: number;
	developerReply?: string;
	developerReplyDate?: string;
}

class GooglePlayScraperLib {
	private reviews: Review[] = [];
	private maxReviews: number;

	constructor(maxReviews = 500) {
		this.maxReviews = maxReviews;
	}

	async scrapeReviews(appId: string) {
		console.log(`🎯 开始使用开源库抓取应用 ${appId} 的评论...`);
		console.log(`📊 最大抓取数量: ${this.maxReviews} 条评论`);

		let nextPaginationToken: string | undefined;
		let totalReviews = 0;
		let page = 1;

		try {
			while (totalReviews < this.maxReviews) {
				console.log(`📄 正在抓取第 ${page} 页评论...`);

				const options: any = {
					appId,
					sort: gplay.sort.NEWEST, // 按最新排序
					num: 100, // 每页100条评论
					lang: "en",
					country: "us",
					throttle: 5, // 每秒最多5个请求，避免被封IP
				};

				// 如果有分页token，添加到选项中
				if (nextPaginationToken) {
					options.nextPaginationToken = nextPaginationToken;
				}

				const result = await gplay.reviews(options);

				if (!result.data || result.data.length === 0) {
					console.log("⚠️ 没有更多评论数据");
					break;
				}

				// 转换数据格式
				const pageReviews: Review[] = result.data.map(
					(review: any) => ({
						reviewId: review.id || "",
						reviewer: review.userName || "Unknown",
						rating: review.score || 0,
						date: review.date
							? new Date(review.date).toLocaleDateString()
							: "",
						content: review.text || "",
						helpful: review.thumbsUp || 0,
						developerReply: review.replyText || undefined,
						developerReplyDate: review.replyDate
							? new Date(review.replyDate).toLocaleDateString()
							: undefined,
					}),
				);

				this.reviews.push(...pageReviews);
				totalReviews += pageReviews.length;

				console.log(
					`✅ 第 ${page} 页: 获取 ${pageReviews.length} 条评论 (总计: ${totalReviews})`,
				);

				// 检查是否还有下一页
				nextPaginationToken = result.nextPaginationToken;
				if (!nextPaginationToken) {
					console.log("📝 已到达最后一页");
					break;
				}

				// 如果已达到最大数量，截取到指定数量
				if (totalReviews >= this.maxReviews) {
					this.reviews = this.reviews.slice(0, this.maxReviews);
					console.log(`🎯 已达到最大抓取数量 ${this.maxReviews} 条`);
					break;
				}

				page++;

				// 添加延迟避免请求过快
				await new Promise((resolve) => setTimeout(resolve, 1000));
			}

			console.log(`🎉 抓取完成！总共获得 ${this.reviews.length} 条评论`);
			return this.reviews;
		} catch (error) {
			console.error("❌ 抓取过程中出现错误:", error);
			throw error;
		}
	}

	async exportToCsv(filename?: string) {
		if (this.reviews.length === 0) {
			console.log("⚠️ 没有评论数据可导出");
			return;
		}

		const outputDir = path.join(process.cwd(), "output");
		if (!fs.existsSync(outputDir)) {
			fs.mkdirSync(outputDir, { recursive: true });
		}

		const csvFilename =
			filename || `google-play-reviews-lib-${Date.now()}.csv`;
		const csvPath = path.join(outputDir, csvFilename);

		const csvWriter = createCsvWriter.createObjectCsvWriter({
			path: csvPath,
			header: [
				{ id: "reviewId", title: "评论ID" },
				{ id: "reviewer", title: "评论者" },
				{ id: "rating", title: "评分" },
				{ id: "date", title: "日期" },
				{ id: "content", title: "评论内容" },
				{ id: "helpful", title: "有用数" },
				{ id: "developerReply", title: "开发者回复" },
				{ id: "developerReplyDate", title: "开发者回复日期" },
			],
			encoding: "utf8",
		});

		await csvWriter.writeRecords(this.reviews);
		console.log(`📊 成功导出 ${this.reviews.length} 条评论到: ${csvPath}`);

		return csvPath;
	}

	printStats() {
		if (this.reviews.length === 0) {
			console.log("\n📈 没有评论数据");
			return;
		}

		const ratings = this.reviews.map((r) => r.rating).filter((r) => r > 0);

		console.log("\n📈 评论统计:");
		console.log(`总评论数: ${this.reviews.length}`);

		if (ratings.length > 0) {
			const avgRating =
				ratings.reduce((sum, rating) => sum + rating, 0) /
				ratings.length;
			const ratingCounts = [1, 2, 3, 4, 5].map(
				(star) => ratings.filter((rating) => rating === star).length,
			);

			console.log(`平均评分: ${avgRating.toFixed(2)}`);
			console.log("评分分布:");
			[1, 2, 3, 4, 5].forEach((star, index) => {
				console.log(
					`  ${star}星: ${ratingCounts[index]} (${((ratingCounts[index] / ratings.length) * 100).toFixed(1)}%)`,
				);
			});
		} else {
			console.log("⚠️ 没有有效的评分数据");
		}

		// 显示有开发者回复的评论数量
		const repliesCount = this.reviews.filter(
			(r) => r.developerReply,
		).length;
		if (repliesCount > 0) {
			console.log(
				`💬 包含开发者回复: ${repliesCount} 条 (${((repliesCount / this.reviews.length) * 100).toFixed(1)}%)`,
			);
		}
	}

	// 显示评论样本
	printSamples(count = 3) {
		if (this.reviews.length === 0) return;

		console.log("\n📄 评论样本:");
		this.reviews.slice(0, count).forEach((review, index) => {
			console.log(`\n${index + 1}. 评论者: ${review.reviewer}`);
			console.log(`   评分: ${review.rating} 星`);
			console.log(`   内容: ${review.content.substring(0, 100)}...`);
			console.log(`   日期: ${review.date}`);
			console.log(`   有用数: ${review.helpful}`);
			if (review.developerReply) {
				console.log(
					`   开发者回复: ${review.developerReply.substring(0, 50)}...`,
				);
			}
		});
	}
}

// 主函数
async function main() {
	const appId = process.argv[2] || "com.polarsteps";
	const maxReviews = Number(process.argv[3]) || 500;
	const filename = process.argv[4];

	console.log("🚀 使用 google-play-scraper 开源库");
	console.log(`📱 应用 ID: ${appId}`);
	console.log(`📊 最大评论数: ${maxReviews}`);

	const scraper = new GooglePlayScraperLib(maxReviews);

	try {
		await scraper.scrapeReviews(appId);
		scraper.printStats();
		scraper.printSamples();
		await scraper.exportToCsv(filename);
	} catch (error) {
		console.error("❌ 程序执行失败:", error);
		process.exit(1);
	}
}

// 如果直接运行此脚本
if (require.main === module) {
	main().catch(console.error);
}

export { GooglePlayScraperLib, type Review };
