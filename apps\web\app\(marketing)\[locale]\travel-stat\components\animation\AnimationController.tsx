"use client";

import { Badge } from "@ui/components/badge";
import { But<PERSON> } from "@ui/components/button";
import { Card } from "@ui/components/card";
import {
	Globe,
	Layers,
	Monitor,
	Palette,
	Settings,
	Sparkles,
	Star,
	Zap,
} from "lucide-react";
import { useState } from "react";
import {
	TSPARTICLES_THEMES,
	TSParticlesBackground,
	type TSParticlesTheme,
} from "./TSParticlesBackgroundSimple";
import {
	THREEJS_THEMES,
	ThreeJsBackground,
	type ThreeJsTheme,
} from "./ThreeJsBackground";

// 动画类型定义
export type AnimationType = "none" | "threejs" | "tsparticles" | "original";

interface AnimationOption {
	type: AnimationType;
	theme?: ThreeJsTheme | TSParticlesTheme;
	label: string;
	description: string;
	icon: React.ComponentType<{ className?: string }>;
	color: string;
	badge?: string;
}

interface AnimationControllerProps {
	className?: string;
	onAnimationChange?: (type: AnimationType, theme?: string) => void;
}

// 动画选项配置
const ANIMATION_OPTIONS: AnimationOption[] = [
	{
		type: "none",
		label: "无动画",
		description: "专注地图本身",
		icon: Monitor,
		color: "bg-gray-500",
	},
	{
		type: "original",
		label: "原始动画",
		description: "简单的CSS动画效果",
		icon: Settings,
		color: "bg-blue-500",
	},
	{
		type: "threejs",
		theme: "starfield",
		label: "Three.js 星空",
		description: "3D粒子星空背景",
		icon: Star,
		color: "bg-purple-500",
		badge: "3D",
	},
	{
		type: "threejs",
		theme: "galaxy",
		label: "银河系",
		description: "旋转星环与星云",
		icon: Globe,
		color: "bg-indigo-500",
		badge: "酷炫",
	},
	{
		type: "threejs",
		theme: "wormhole",
		label: "虫洞",
		description: "扭曲的时空效果",
		icon: Zap,
		color: "bg-violet-500",
		badge: "科幻",
	},
	{
		type: "tsparticles",
		theme: "meteor-shower",
		label: "流星雨",
		description: "梦幻流星雨效果",
		icon: Sparkles,
		color: "bg-yellow-500",
		badge: "梦幻",
	},
	{
		type: "tsparticles",
		theme: "floating-cities",
		label: "浮动城市",
		description: "连接的城市网络",
		icon: Layers,
		color: "bg-cyan-500",
		badge: "未来",
	},
	{
		type: "tsparticles",
		theme: "aurora-waves",
		label: "极光波浪",
		description: "绚丽的极光效果",
		icon: Palette,
		color: "bg-green-500",
		badge: "绚丽",
	},
];

export function AnimationController({
	className = "",
	onAnimationChange,
}: AnimationControllerProps) {
	const [currentAnimation, setCurrentAnimation] = useState<{
		type: AnimationType;
		theme?: string;
	}>({ type: "none" });
	const [isOpen, setIsOpen] = useState(false);

	const handleAnimationSelect = (option: AnimationOption) => {
		const newAnimation = {
			type: option.type,
			theme: option.theme,
		};
		setCurrentAnimation(newAnimation);
		onAnimationChange?.(option.type, option.theme);
		setIsOpen(false);
	};

	const renderCurrentAnimation = () => {
		switch (currentAnimation.type) {
			case "threejs":
				return (
					<ThreeJsBackground
						theme={currentAnimation.theme as ThreeJsTheme}
						className="opacity-60"
					/>
				);
			case "tsparticles":
				return (
					<TSParticlesBackground
						theme={currentAnimation.theme as TSParticlesTheme}
						className="opacity-60"
						density={0.8}
					/>
				);
			case "original":
				// 这里可以引入原始的背景动画组件
				return null;
			default:
				return null;
		}
	};

	const getCurrentLabel = () => {
		const option = ANIMATION_OPTIONS.find(
			(opt) =>
				opt.type === currentAnimation.type &&
				opt.theme === currentAnimation.theme,
		);
		return option?.label || "选择动画";
	};

	return (
		<>
			{/* 动画背景层 - 不阻挡交互 */}
			<div
				className={`absolute inset-0 pointer-events-none ${className}`}
			>
				{renderCurrentAnimation()}
			</div>

			{/* 控制按钮层 - 可以交互 */}
			<div className="fixed bottom-4 right-4 z-50 pointer-events-auto">
				<Button
					onClick={() => setIsOpen(!isOpen)}
					variant="outline"
					className="bg-white/90 backdrop-blur-sm border-sky-200 shadow-lg hover:bg-white/95"
				>
					<Sparkles className="w-4 h-4 mr-2" />
					{getCurrentLabel()}
				</Button>
			</div>

			{/* 动画选择面板 - 可以交互 */}
			{isOpen && (
				<div className="fixed bottom-16 right-4 z-50 pointer-events-auto">
					<Card className="bg-white/95 backdrop-blur-sm border-sky-200 shadow-xl w-80 max-h-96 overflow-y-auto">
						<div className="p-4">
							<div className="flex items-center justify-between mb-4">
								<h3 className="text-lg font-semibold text-gray-800">
									背景动画
								</h3>
								<Button
									onClick={() => setIsOpen(false)}
									size="sm"
									variant="ghost"
									className="p-1 hover:bg-gray-100"
								>
									×
								</Button>
							</div>

							<div className="space-y-2">
								{ANIMATION_OPTIONS.map((option, index) => {
									const Icon = option.icon;
									const isActive =
										currentAnimation.type === option.type &&
										currentAnimation.theme === option.theme;

									return (
										<button
											key={`${option.type}-${option.theme || "default"}-${index}`}
											onClick={() =>
												handleAnimationSelect(option)
											}
											className={`w-full p-3 text-left rounded-lg border transition-all duration-200 relative overflow-hidden ${
												isActive
													? "border-sky-300 bg-sky-50"
													: "border-gray-200 hover:border-sky-300 hover:shadow-sm"
											}`}
										>
											{/* 背景渐变 */}
											<div
												className={`absolute inset-0 ${option.color} opacity-5`}
											/>

											<div className="relative flex items-start gap-3">
												<div
													className={`mt-0.5 p-2 rounded-lg ${option.color} text-white`}
												>
													<Icon className="w-4 h-4" />
												</div>

												<div className="flex-1 min-w-0">
													<div className="flex items-center gap-2 mb-1">
														<span className="text-sm font-medium text-gray-800">
															{option.label}
														</span>
														{option.badge && (
															<Badge
																status="info"
																className="text-xs"
															>
																{option.badge}
															</Badge>
														)}
														{isActive && (
															<Badge
																status="success"
																className="text-xs"
															>
																当前
															</Badge>
														)}
													</div>
													<div className="text-xs text-gray-500">
														{option.description}
													</div>
												</div>
											</div>
										</button>
									);
								})}
							</div>

							{/* 性能提示 */}
							<div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
								<div className="text-xs text-amber-700">
									💡 <strong>性能提示:</strong>{" "}
									3D动画可能会影响性能，建议在高配置设备上使用。
								</div>
							</div>
						</div>
					</Card>
				</div>
			)}
		</>
	);
}

// 主题预设导出
export const ANIMATION_THEMES = {
	threejs: THREEJS_THEMES,
	tsparticles: TSPARTICLES_THEMES,
};
