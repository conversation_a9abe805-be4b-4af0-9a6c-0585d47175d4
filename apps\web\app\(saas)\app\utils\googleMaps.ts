"use client";

/**
 * 加载 Google Maps API
 * 这个函数负责异步加载 Google Maps JavaScript API
 *
 * @returns Promise<void> 加载完成后的Promise
 */
export async function loadGoogleMapsApi(): Promise<void> {
	// 检查 Google Maps API 是否已加载
	if (window.google?.maps) {
		return Promise.resolve();
	}

	// 从环境变量获取 API 密钥
	const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;

	if (!apiKey) {
		console.warn(
			"Google Maps API 密钥未设置。请在 .env.local 文件中配置 NEXT_PUBLIC_GOOGLE_MAPS_API_KEY",
		);
	}

	// 创建并返回一个加载 Google Maps API 的 Promise
	return new Promise<void>((resolve, reject) => {
		try {
			// 创建 script 元素
			const script = document.createElement("script");
			script.type = "text/javascript";
			script.async = true;
			script.defer = true;
			script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=initGoogleMaps`;

			// 定义全局回调函数
			window.initGoogleMaps = () => {
				if (window.google?.maps) {
					resolve();
				} else {
					reject(new Error("Google Maps API 加载失败"));
				}
				// 清理全局回调
				window.initGoogleMaps = undefined as any;
			};

			// 处理加载错误
			script.onerror = () => {
				reject(new Error("Google Maps API 加载失败"));
				// 清理全局回调
				window.initGoogleMaps = undefined as any;
			};

			// 将 script 添加到文档
			document.head.appendChild(script);
		} catch (error) {
			reject(error);
		}
	});
}

// 声明全局 initGoogleMaps 类型
declare global {
	interface Window {
		initGoogleMaps?: () => void;
	}
}
