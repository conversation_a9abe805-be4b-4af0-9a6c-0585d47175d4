import { logger } from "@repo/logs";
import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../types";

export const send: SendEmailHandler = async ({ to, subject, text, html }) => {
	let formattedOutput = `📧 Sending email to ${to} with subject "${subject}"\n\n`;

	if (text) {
		formattedOutput += `📝 Text Content:\n${text}\n\n`;
	}

	if (html) {
		formattedOutput += `🌐 HTML Content:\n${html}\n\n`;
	}

	formattedOutput += `${"=".repeat(50)}\n`;

	logger.log(formattedOutput);
};
