// 世界国家地理数据源配置
export const GEO_DATA_SOURCES = {
	// 主要数据源 (按优先级排序)
	primary: [
		{
			url: "/data/countries.geo.json",
			name: "本地文件",
			type: "geojson",
			reliable: true,
		},
		{
			url: "https://unpkg.com/world-atlas@1/countries-110m.json",
			name: "unpkg CDN",
			type: "topojson", // 注意：这是 TopoJSON 格式
			reliable: true,
		},
		{
			url: "https://cdn.jsdelivr.net/npm/world-atlas@1/countries-110m.json",
			name: "jsDelivr CDN",
			type: "topojson",
			reliable: true,
		},
	],

	// 备用数据源
	fallback: [
		{
			url: "https://raw.githubusercontent.com/johan/world.geo.json/master/countries.geo.json",
			name: "GitHub Raw",
			type: "geojson",
			reliable: false, // GitHub raw 可能不稳定
		},
		{
			url: "https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/world.geojson",
			name: "D3 Gallery备份",
			type: "geojson",
			reliable: false,
		},
	],
};

// 国家名称标准化映射表
export const COUNTRY_NAME_MAPPING = {
	// 美国的各种变体 -> GeoJSON 标准名称
	"United States": "United States of America",
	USA: "United States of America",
	US: "United States of America",
	America: "United States of America",

	// 中国的各种变体
	"People's Republic of China": "China",
	PRC: "China",

	// 英国的各种变体
	"United Kingdom of Great Britain and Northern Ireland": "United Kingdom",
	UK: "United Kingdom",
	Britain: "United Kingdom",

	// 其他常见变体
	Russia: "Russian Federation",
	"South Korea": "Republic of Korea",
	"North Korea": "Democratic People's Republic of Korea",
	Iran: "Islamic Republic of Iran",
	Syria: "Syrian Arab Republic",
	Venezuela: "Bolivarian Republic of Venezuela",
} as const;

// 获取所有数据源
export function getAllDataSources() {
	return [...GEO_DATA_SOURCES.primary, ...GEO_DATA_SOURCES.fallback];
}

// 标准化国家名称
export function normalizeCountryName(countryName: string): string {
	const name = countryName.trim();
	return (
		COUNTRY_NAME_MAPPING[name as keyof typeof COUNTRY_NAME_MAPPING] || name
	);
}
