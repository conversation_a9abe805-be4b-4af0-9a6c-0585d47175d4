import { useTranslations } from "next-intl";
import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "sonner";

// 引入前端类型
import type {
	FrontendTravelDiary,
	FrontendTravelPoint,
	FrontendTravelTimeline,
} from "@packages/database/src/types";

import { Skeleton } from "@ui/components/skeleton";

// 引入组件
import DaySelector from "./DaySelector";
import { DiaryHeader } from "./DiaryHeader";
import JourneyMap from "./JourneyMap";
import TravelPointList from "./TravelPointList";

import { useAnalytics } from "@modules/analytics";
// 引入自定义hooks
import { useDiaryData } from "./hooks/useDiaryData";
import { useDiaryModes } from "./hooks/useDiaryModes";
import { useInlineEdit } from "./hooks/useInlineEdit";
import { useTravelPoints } from "./hooks/useTravelPoints";

// 引入工具函数
import {
	transformPointsForMap,
	transformToFormTravelPoint,
} from "./utils/diaryTransforms";

// 引入RichText相关组件（将在后续步骤中实现）
// import { RichTextEditor } from "./RichTextEditor";
// import { RichTextPreview } from "./RichTextPreview";

interface DiaryEditorProps {
	diaryId: string;
}

interface ImagePreviewState {
	images: string[];
	locationName: string;
	initialIndex: number;
	isOpen: boolean;
}

const DiaryEditor = ({ diaryId }: DiaryEditorProps) => {
	const t = useTranslations("travelMemo.diaryEditor");
	const { trackEvent } = useAnalytics();

	// UI状态
	const [activeTimelineId, setActiveTimelineId] = useState<string>("");
	const [isHeaderCompact, setIsHeaderCompact] = useState<boolean>(false);
	const [isHeaderManuallyToggled, setIsHeaderManuallyToggled] =
		useState<boolean>(false);
	const [leftPanelWidth, setLeftPanelWidth] = useState<number>(50);
	const [isLargeScreen, setIsLargeScreen] = useState<boolean>(false);
	const [mapResizeTrigger, setMapResizeTrigger] = useState<number>(0);
	const [imagePreview, setImagePreview] = useState<ImagePreviewState | null>(
		null,
	);

	// 引用
	const contentRef = useRef<HTMLDivElement>(null);
	const containerRef = useRef<HTMLDivElement>(null);
	const resizingRef = useRef<boolean>(false);

	// 自定义hooks
	const {
		diary,
		isLoading,
		isSaving,
		updateDiary,
		updateTimelines,
		saveDiaryData,
		markAsModified,
		setDiary,
		isInitialLoad,
		isDiaryModifiedRef,
	} = useDiaryData({ diaryId });

	const {
		isRichTextMode,
		richTextDraftContent,
		isLoadingDraft,
		isModeTransitioning,
		toggleEditMode,
		handleRichTextChange,
		loadRichTextDraft,
	} = useDiaryModes({
		diaryId,
		onSaveDiary: saveDiaryData,
		isDiaryModified: isDiaryModifiedRef.current,
	});

	// 创建onDiaryUpdate回调，用于处理完整的diary更新
	const handleDiaryUpdate = useCallback(
		(updatedDiary: FrontendTravelDiary) => {
			markAsModified();
			setDiary(updatedDiary);
		},
		[markAsModified, setDiary],
	);

	const {
		activePointId,
		isAddPointDialogOpen,
		handleAddPoint,
		handleEditPoint,
		handleDeletePoint,
		handleReorderPoints,
		handleInsertPoint,
		handlePointSelect,
		handleOpenAddPointDialog,
		handleAddPointDialogClose,
		setActivePointId,
	} = useTravelPoints({
		diary,
		activeTimelineId,
		onDiaryUpdate: handleDiaryUpdate, // 使用新的回调
		onMarkAsModified: markAsModified,
	});

	// 内联编辑功能
	const {
		editState,
		enterEditMode,
		exitEditMode,
		updateField,
		getFieldValue,
		isEditing,
		cleanup,
	} = useInlineEdit({
		onPointUpdate: handleEditPoint,
		onPointAdd: handleAddPoint,
		autoSaveDelay: 1500,
	});

	// 获取当前时间线数据
	const activeTimeline: FrontendTravelTimeline | undefined =
		diary?.timelines.find(
			(timeline: FrontendTravelTimeline) =>
				timeline.id === activeTimelineId,
		);
	const currentTimelinePoints: FrontendTravelPoint[] =
		activeTimeline?.points || [];
	const formTimelinePoints = currentTimelinePoints.map(
		transformToFormTravelPoint,
	);

	// 初始化active timeline
	useEffect(() => {
		if (diary && diary.timelines.length > 0 && !activeTimelineId) {
			setActiveTimelineId(diary.timelines[0].id);
		}
	}, [diary, activeTimelineId]);

	// 监听时间线变化，触发地图更新
	useEffect(() => {
		if (activeTimelineId && currentTimelinePoints.length > 0) {
			// 触发地图重新渲染和适配
			setMapResizeTrigger(Date.now());

			// 清除当前选中的点位，让地图显示整个时间线
			if (activePointId) {
				setActivePointId("");
			}

			console.log(
				`[DiaryEditor] 切换到时间线: ${activeTimelineId}, 点位数量: ${currentTimelinePoints.length}`,
			);
		} else if (activeTimelineId && currentTimelinePoints.length === 0) {
			// 如果时间线没有点位，也要清除选中状态
			if (activePointId) {
				setActivePointId("");
			}
			console.log(`[DiaryEditor] 切换到空时间线: ${activeTimelineId}`);
		}
	}, [
		activeTimelineId,
		currentTimelinePoints.length,
		activePointId,
		setActivePointId,
	]);

	// 加载富文本草稿
	useEffect(() => {
		if (diary) {
			void loadRichTextDraft();
		}
	}, [diary, loadRichTextDraft]);

	// 处理标题变化
	const handleTitleChange = useCallback(
		(e: React.ChangeEvent<HTMLInputElement>) => {
			updateDiary({ title: e.target.value });
		},
		[updateDiary],
	);

	// 处理副标题变化
	const handleSubtitleChange = useCallback(
		(e: React.ChangeEvent<HTMLTextAreaElement>) => {
			updateDiary({ subtitle: e.target.value });
		},
		[updateDiary],
	);

	// 处理键盘事件
	const handleKeyDown = useCallback(
		(e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
			if (e.key === "Enter" && !e.shiftKey) {
				e.preventDefault();
				e.currentTarget.blur();

				if (isDiaryModifiedRef.current) {
					void saveDiaryData({ silent: false });
					toast.success(t("toasts.changesSaved"));
				}
			}
		},
		[saveDiaryData, t, isDiaryModifiedRef],
	);

	// 切换头部模式
	const toggleHeaderMode = useCallback(() => {
		setIsHeaderCompact(!isHeaderCompact);
		setIsHeaderManuallyToggled(true);

		setTimeout(() => {
			setIsHeaderManuallyToggled(false);
		}, 5000);
	}, [isHeaderCompact]);

	// 响应式布局处理
	useEffect(() => {
		function handleResize() {
			const largeScreen = window.innerWidth >= 1024;
			setIsLargeScreen(largeScreen);
			if (!largeScreen) {
				setLeftPanelWidth(50);
			}
		}
		handleResize();
		window.addEventListener("resize", handleResize);
		return () => window.removeEventListener("resize", handleResize);
	}, []);

	// 拖拽调整布局
	const handleResizeStart = useCallback(
		(e: React.MouseEvent) => {
			if (!isLargeScreen) return;
			e.preventDefault();
			resizingRef.current = true;
			document.addEventListener("mousemove", handleResize);
			document.addEventListener("mouseup", handleResizeEnd);
		},
		[isLargeScreen],
	);

	const handleResize = useCallback(
		(e: MouseEvent) => {
			if (!resizingRef.current || !containerRef.current || !isLargeScreen)
				return;

			const containerRect = containerRef.current.getBoundingClientRect();
			const newWidth =
				((e.clientX - containerRect.left) / containerRect.width) * 100;

			if (newWidth >= 20 && newWidth <= 80) {
				setLeftPanelWidth(newWidth);
			}
		},
		[isLargeScreen],
	);

	const handleResizeEnd = useCallback(() => {
		if (resizingRef.current) {
			resizingRef.current = false;
			document.removeEventListener("mousemove", handleResize);
			document.removeEventListener("mouseup", handleResizeEnd);
			setMapResizeTrigger(Date.now());
		}
	}, [handleResize]);

	// 头部滚动处理
	useEffect(() => {
		const handleScroll = () => {
			if (!contentRef.current || isHeaderManuallyToggled) {
				return;
			}

			if (contentRef.current.scrollTop > 100 && !isHeaderCompact) {
				setIsHeaderCompact(true);
			} else if (contentRef.current.scrollTop <= 100 && isHeaderCompact) {
				setIsHeaderCompact(false);
			}
		};

		const currentRef = contentRef.current;
		if (currentRef) {
			currentRef.addEventListener("scroll", handleScroll);
		}

		return () => {
			if (currentRef) {
				currentRef.removeEventListener("scroll", handleScroll);
			}
		};
	}, [isHeaderCompact, isHeaderManuallyToggled]);

	// 清理定时器
	useEffect(() => {
		return cleanup;
	}, [cleanup]);

	// 内存清理
	useEffect(() => {
		return () => {
			document.removeEventListener("mousemove", handleResize);
			document.removeEventListener("mouseup", handleResizeEnd);
		};
	}, [handleResize, handleResizeEnd]);

	// 加载状态
	if (isLoading) {
		return (
			<div className="p-6 animate-fade-in h-screen flex flex-col bg-gradient-to-b from-blue-50 to-sky-50 overflow-hidden">
				<div className="mb-6">
					<Skeleton className="h-10 w-1/3 mb-2" />
					<Skeleton className="h-5 w-1/2" />
				</div>
				<Skeleton className="h-16 w-full mb-4" />
				<div className="flex-1 flex gap-4 min-h-0 overflow-hidden">
					<Skeleton className="h-full w-1/3" />
					<Skeleton className="h-full w-2/3" />
				</div>
			</div>
		);
	}

	// 日记不存在
	if (!diary) {
		return (
			<div className="p-6 flex flex-col items-center justify-center h-screen bg-gradient-to-b from-blue-50 via-sky-50 to-indigo-50 poetic-bg">
				<div className="ink-animation ink-card p-8 rounded-lg shadow-md max-w-md w-full text-center">
					<h2 className="text-2xl font-bold text-card-foreground mb-4 diary-title gradient-title">
						未找到旅行日记
					</h2>
					<p className="text-muted-foreground content-text mb-6">
						无法加载旅行日记数据，请稍后再试或创建新的旅行记忆。
					</p>
					<div className="ink-divider my-4" />
					<p className="literary-quote mt-6">
						"旅行不在于你去了多远，而在于你看到了什么，记住了什么。"
					</p>
				</div>
			</div>
		);
	}

	return (
		<div
			className={`h-screen flex flex-col bg-gradient-to-b from-blue-50 via-sky-50 to-indigo-50 overflow-hidden animate-fade-in transition-all duration-500 relative ${
				isRichTextMode ? "bg-white" : ""
			} ${isModeTransitioning ? "opacity-90" : "opacity-100"}`}
		>
			{/* 装饰背景元素 */}
			{!isRichTextMode && (
				<>
					<div className="absolute top-[10%] right-[5%] w-28 h-28 rounded-full bg-blue-100/30 animate-float-slow pointer-events-none" />
					<div
						className="absolute bottom-[15%] left-[8%] w-20 h-20 rounded-full bg-sky-100/25 animate-float-alt pointer-events-none"
						style={{ animationDelay: "1.5s" }}
					/>
					<div
						className="absolute top-[30%] left-[15%] w-16 h-16 rounded-full bg-cyan-100/25 animate-float-medium pointer-events-none"
						style={{ animationDelay: "2.2s" }}
					/>
					<div
						className="absolute bottom-[35%] right-[12%] w-14 h-14 rounded-full bg-indigo-100/20 animate-float-fast pointer-events-none"
						style={{ animationDelay: "1s" }}
					/>
				</>
			)}

			{/* 头部区域 */}
			<DiaryHeader
				diary={diary}
				diaryId={diaryId}
				isHeaderCompact={isHeaderCompact}
				isRichTextMode={isRichTextMode}
				isModeTransitioning={isModeTransitioning}
				onToggleHeaderMode={toggleHeaderMode}
				onToggleEditMode={toggleEditMode}
				onTitleChange={handleTitleChange}
				onSubtitleChange={handleSubtitleChange}
				onKeyDown={handleKeyDown}
			/>

			{/* 主内容区域 */}
			<div
				ref={contentRef}
				className={`flex-1 flex flex-col overflow-auto scrollbar-artistic transition-all duration-300 ease-in-out ${
					isRichTextMode ? "p-0" : "p-6 poetic-bg"
				} ${isModeTransitioning ? "scale-98 opacity-80" : "scale-100 opacity-100"}`}
			>
				{isRichTextMode ? (
					// 富文本模式
					<div className="flex-1 flex flex-col bg-white overflow-hidden relative animate-fade-in">
						{/* 富文本编辑器将在后续实现 */}
						<div className="flex-1 flex items-center justify-center">
							<p className="text-gray-500">
								富文本编辑器正在开发中...
							</p>
						</div>
					</div>
				) : (
					// 传统模式
					<div
						ref={containerRef}
						className={`flex flex-1 min-h-0 overflow-hidden animate-fade-in ${
							isLargeScreen ? "flex-row gap-0" : "flex-col gap-4"
						}`}
					>
						{/* 左侧面板 */}
						<div
							className="flex-shrink-0 overflow-hidden flex flex-col bg-white/95 shadow-sm rounded-lg border border-blue-100 poetic-border corner-decoration"
							style={{
								width: isLargeScreen
									? `${leftPanelWidth}%`
									: "100%",
								height: isLargeScreen ? "100%" : "auto",
								minHeight: isLargeScreen ? 0 : "40vh",
								maxHeight: isLargeScreen ? "100%" : "50vh",
							}}
						>
							<div className="flex-shrink-0 border-b border-blue-100/70">
								<DaySelector
									diaryDays={diary.timelines}
									activeDay={activeTimelineId}
									setActiveDay={setActiveTimelineId}
									setDiaryDays={updateTimelines}
									onAddPointClick={handleOpenAddPointDialog}
									className="p-2 ink-fragment"
								/>
							</div>
							<div className="flex-1 overflow-auto p-2 scrollbar-artistic">
								<TravelPointList
									points={formTimelinePoints}
									onAddPoint={handleAddPoint}
									onEditPoint={handleEditPoint}
									onDeletePoint={handleDeletePoint}
									onReorderPoints={handleReorderPoints}
									onPointSelect={handlePointSelect}
									activePointId={activePointId}
									isAddDialogOpen={isAddPointDialogOpen}
									onAddDialogClose={handleAddPointDialogClose}
									onInsertPoint={handleInsertPoint}
								/>
							</div>
						</div>

						{/* 调整分隔线 */}
						{isLargeScreen && (
							<div className="relative flex-shrink-0 group">
								<div
									className="absolute top-0 bottom-0 left-0 w-[1px] bg-blue-200/70"
									style={{ height: "100%" }}
								/>
								<div
									className="w-[10px] h-full cursor-col-resize flex items-center justify-center bg-blue-50 hover:bg-sky-100/80 transition-colors group"
									onMouseDown={handleResizeStart}
									title={t("resizeHandle.tooltip")}
								>
									<div className="flex flex-col items-center gap-2">
										<div className="w-[3px] h-8 rounded-full bg-blue-300/60 group-hover:bg-blue-400/80 transition-colors" />
										<div className="w-[3px] h-8 rounded-full bg-blue-300/60 group-hover:bg-blue-400/80 transition-colors" />
										<div className="w-[3px] h-8 rounded-full bg-blue-300/60 group-hover:bg-blue-400/80 transition-colors" />
									</div>
									<div className="absolute opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-white/95 shadow-md px-2 py-1 rounded text-xs text-gray-700 pointer-events-none select-none top-2 left-1/2 transform -translate-x-1/2 z-10 diary-subtitle">
										{t("resizeHandle.hoverText")}
									</div>
								</div>
								<div
									className="absolute top-0 bottom-0 right-0 w-[1px] bg-blue-200/60"
									style={{ height: "100%" }}
								/>
							</div>
						)}

						{/* 右侧地图区域 */}
						<div
							className="flex-grow overflow-hidden bg-white/95 shadow-sm rounded-lg border border-blue-100 poetic-border corner-decoration p-0"
							style={{
								width: isLargeScreen
									? `calc(${100 - leftPanelWidth}% - 12px)`
									: "100%",
								height: isLargeScreen ? "100%" : "auto",
								minHeight: "400px",
							}}
						>
							<div className="w-full h-full">
								<JourneyMap
									points={transformPointsForMap(
										currentTimelinePoints,
									)}
									activePointId={activePointId}
									onPointSelect={handlePointSelect}
									className="w-full h-full"
									mapResizeTrigger={mapResizeTrigger}
								/>
							</div>
						</div>
					</div>
				)}
			</div>
		</div>
	);
};

export default DiaryEditor;
