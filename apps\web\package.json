{"dependencies": {"@aws-sdk/client-s3": "^3.782.0", "@aws-sdk/s3-request-presigner": "3.437.0", "@fancyapps/ui": "^5.0.36", "@fumadocs/content-collections": "^1.1.8", "@googlemaps/js-api-loader": "^1.16.8", "@googlemaps/markerclusterer": "^2.5.3", "@hookform/resolvers": "^4.1.2", "@mapbox/search-js-react": "^1.0.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.8", "@react-google-maps/api": "^2.20.6", "@react-spring/three": "^9.7.5", "@repo/api": "workspace:*", "@repo/auth": "workspace:*", "@repo/config": "workspace:*", "@repo/database": "workspace:*", "@repo/i18n": "workspace:*", "@repo/logs": "workspace:*", "@repo/mail": "workspace:*", "@repo/payments": "workspace:*", "@repo/storage": "workspace:*", "@repo/utils": "workspace:*", "@sindresorhus/slugify": "^2.2.1", "@tanstack/react-query": "^5.66.9", "@tanstack/react-table": "^8.21.2", "@tiptap/core": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@types/lodash": "^4.17.16", "ai": "^4.1.46", "better-auth": "1.1.21", "boring-avatars": "^1.11.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cropperjs": "1.6.2", "date-fns": "^4.1.0", "deepmerge": "^4.3.1", "embla-carousel-react": "^8.3.0", "fireworks-js": "^2.10.8", "framer-motion": "^12.6.3", "fumadocs-core": "^15.0.13", "fumadocs-ui": "^15.0.13", "hono": "^4.7.2", "html2canvas-pro": "^1.5.11", "input-otp": "^1.2.4", "jotai": "2.12.1", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.476.0", "mapbox-gl": "2.15.0", "next": "15.1.7", "next-intl": "3.26.5", "next-themes": "^0.4.4", "nextjs-toploader": "^3.7.15", "nprogress": "^0.2.0", "nuqs": "^2.4.0", "oslo": "^1.2.1", "prettier": "3.4.2", "prosemirror-markdown": "^1.13.2", "prosemirror-model": "^1.25.1", "prosemirror-schema-basic": "^1.2.4", "prosemirror-schema-list": "^1.5.1", "react": "19.0.0", "react-cropper": "^2.3.3", "react-day-picker": "^8.10.1", "react-dom": "19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.54.2", "react-map-gl": "7.1.6", "react-pageflip": "^2.0.3", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "server-only": "^0.0.1", "sharp": "^0.33.5", "slugify": "^1.6.6", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "typewriter-effect": "^2.22.0", "ufo": "^1.5.4", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "vaul": "^0.9.3", "zod": "^3.24.2", "zod-openapi": "^4.2.3", "zustand": "^5.0.3"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@content-collections/core": "^0.8.0", "@content-collections/mdx": "^0.2.0", "@content-collections/next": "^0.2.4", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@mdx-js/mdx": "^3.1.0", "@playwright/test": "^1.50.1", "@repo/auth": "workspace:*", "@repo/tailwind-config": "workspace:*", "@repo/tsconfig": "workspace:*", "@shikijs/rehype": "^3.2.2", "@tailwindcss/postcss": "^4.1.3", "@types/fancybox": "^3.5.7", "@types/google.maps": "^3.58.1", "@types/js-cookie": "^3.0.4", "@types/mapbox-gl": "^3.4.1", "@types/mdx": "^2.0.13", "@types/node": "22.14.0", "@types/nprogress": "^0.2.3", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@types/uuid": "^10.0.0", "autoprefixer": "10.4.21", "dotenv": "^16.5.0", "dotenv-cli": "^8.0.0", "file-loader": "^6.2.0", "markdown-toc": "^1.2.0", "mdx": "^0.3.1", "postcss": "8.5.3", "rehype-img-size": "^1.0.1", "start-server-and-test": "^2.0.10", "tailwindcss": "4.1.3"}, "name": "@repo/web", "private": true, "scripts": {"build": "next build", "build:strict": "rm -rf .next && rm -rf node_modules/.cache && next build", "build:ci": "npm run build:strict", "dev": "next dev --turbopack", "e2e": "pnpm exec playwright test --ui", "e2e:ci": "pnpm exec playwright install && pnpm exec playwright test", "lint": "biome check", "lint:fix": "biome check --write", "shadcn-ui": "pnpm dlx shadcn@latest", "start": "next start", "type-check": "tsc --noEmit", "type-check:strict": "tsc --noEmit --strict"}, "version": "0.0.0"}