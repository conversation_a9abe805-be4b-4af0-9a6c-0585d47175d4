"use client";

import type { FrontendTravelDiary } from "@repo/database/src/types/travel-diary";
import { motion } from "framer-motion";
import Image from "next/image";

interface ClosingSectionProps {
	diary: FrontendTravelDiary;
	onReturnToOpening: () => void;
}

export function ClosingSection({
	diary,
	onReturnToOpening,
}: ClosingSectionProps) {
	// 挑选代表性照片，这里简化为使用第一个足迹的第一张照片
	const representativeImage =
		diary.timelines[0]?.points[0]?.images?.[0]?.url ||
		diary.coverImage ||
		"https://images.unsplash.com/photo-1492571350019-22de08371fd3?q=80&w=1000";

	// 统计足迹总数
	const totalFootprints = diary.timelines.reduce(
		(sum: number, timeline) => sum + timeline.points.length,
		0,
	);

	// 统计时间范围
	const startDate = diary.timelines[0]?.date || new Date();
	const endDate =
		diary.timelines[diary.timelines.length - 1]?.date || new Date();
	const dayCount =
		Math.round(
			(endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24),
		) + 1;

	return (
		<motion.div
			initial={{ opacity: 0 }}
			animate={{ opacity: 1 }}
			exit={{ opacity: 0 }}
			transition={{ duration: 0.8 }}
			className="absolute inset-0 w-full h-full flex flex-col items-center justify-center overflow-hidden"
		>
			{/* 背景图片 */}
			<div className="absolute inset-0 w-full h-full overflow-hidden">
				<Image
					src={representativeImage}
					alt="旅行回忆"
					fill
					className="object-cover"
					priority
				/>
				<div className="absolute inset-0 bg-black/70" />
			</div>

			{/* 内容叠加层 */}
			<div className="relative z-10 max-w-2xl px-4 text-center">
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.8, delay: 0.2 }}
					className="mb-6"
				>
					<span className="text-6xl">✨</span>
				</motion.div>

				<motion.h2
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.8, delay: 0.4 }}
					className="text-3xl md:text-4xl font-bold mb-6 text-white"
				>
					旅行回顾
				</motion.h2>

				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.8, delay: 0.6 }}
					className="text-white/80 text-lg mb-8 space-y-3"
				>
					<p>
						<span className="font-semibold">{diary.title}</span>{" "}
						之旅已结束
					</p>
					<p>
						在 {dayCount} 天的时间里，我们探索了{" "}
						{diary.timelines.length} 个城市
					</p>
					<p>留下了 {totalFootprints} 个美好足迹</p>
					<p className="italic mt-6">
						"旅行的意义不在于到达目的地，而在于沿途的风景和所结识的人"
					</p>
				</motion.div>

				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.8, delay: 0.8 }}
					className="flex flex-col md:flex-row gap-4 justify-center"
				>
					{/* 返回首页按钮 */}
					<button
						type="button"
						onClick={onReturnToOpening}
						className="px-6 py-3 rounded-lg bg-white text-black font-medium hover:bg-white/90 transition-colors"
					>
						返回首页
					</button>

					{/* 分享按钮 */}
					<button
						type="button"
						className="px-6 py-3 rounded-lg bg-primary text-white font-medium hover:bg-primary/90 transition-colors flex items-center justify-center gap-2"
						onClick={() => {
							// 实际应用中会调用分享API或显示分享对话框
							alert("分享功能正在开发中");
						}}
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							width="18"
							height="18"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							strokeWidth="2"
							strokeLinecap="round"
							strokeLinejoin="round"
							aria-hidden="true"
						>
							<path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8" />
							<polyline points="16 6 12 2 8 6" />
							<line x1="12" y1="2" x2="12" y2="15" />
						</svg>
						分享这段旅程
					</button>
				</motion.div>
			</div>
		</motion.div>
	);
}
