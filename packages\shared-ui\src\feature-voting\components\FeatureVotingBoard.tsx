"use client";

import { useFeatureVoting } from "@repo/utils/lib/feature-voting";
import type {
	FeatureRequestQuery,
	FeatureVotingConfig,
} from "@repo/utils/lib/feature-voting";
import { FeatureRequestForm } from "@repo/utils/lib/feature-voting/components/FeatureRequestForm";
import { FeatureRequestList } from "@repo/utils/lib/feature-voting/components/FeatureRequestList";
import React, { useState } from "react";

interface FeatureVotingBoardProps extends FeatureVotingConfig {
	className?: string;
	title?: string;
	description?: string;
	showSubmitForm?: boolean;
	defaultQuery?: FeatureRequestQuery;
}

export function FeatureVotingBoard({
	apiBaseUrl = "/api",
	showVoteCounts = true,
	allowAnonymousVoting = true,
	allowAnonymousSubmissions = true,
	className = "",
	title = "功能投票与建议",
	description = "查看产品路线图，为您感兴趣的功能投票，或提交新的功能建议",
	showSubmitForm = true,
	defaultQuery = {},
}: FeatureVotingBoardProps) {
	const [showForm, setShowForm] = useState(false);
	const [selectedFeatureRequest, setSelectedFeatureRequest] = useState(null);

	const {
		featureRequests,
		products,
		isLoading,
		error,
		pagination,
		vote,
		unvote,
		submitFeatureRequest,
		loadFeatureRequests,
		loadProducts,
		votingStates,
		submissionState,
	} = useFeatureVoting({
		apiBaseUrl,
		autoLoadProducts: true,
		autoLoadFeatureRequests: true,
		defaultQuery,
	});

	const handleVote = async (featureRequestId: string) => {
		if (!allowAnonymousVoting) {
			console.warn("Anonymous voting is disabled");
			return;
		}
		await vote(featureRequestId);
	};

	const handleUnvote = async (featureRequestId: string) => {
		if (!allowAnonymousVoting) {
			console.warn("Anonymous voting is disabled");
			return;
		}
		await unvote(featureRequestId);
	};

	const handleSubmitFeatureRequest = async (data: any) => {
		if (!allowAnonymousSubmissions) {
			console.warn("Anonymous submissions are disabled");
			return;
		}
		await submitFeatureRequest(data);
		// 表单提交成功后自动关闭
		if (submissionState.success) {
			setShowForm(false);
		}
	};

	const handleQueryChange = (query: FeatureRequestQuery) => {
		loadFeatureRequests(query);
	};

	const handlePageChange = (page: number) => {
		loadFeatureRequests({ ...defaultQuery, page });
	};

	const handleFeatureRequestClick = (featureRequest: any) => {
		setSelectedFeatureRequest(featureRequest);
		// 这里可以扩展为显示详情弹窗或跳转到详情页面
		console.log("Feature request clicked:", featureRequest);
	};

	return (
		<div className={`max-w-4xl mx-auto ${className}`}>
			{/* 头部 */}
			<div className="mb-8">
				<h1 className="text-3xl font-bold text-gray-900 mb-4">
					{title}
				</h1>
				<p className="text-lg text-gray-600 mb-6">{description}</p>

				{/* 操作按钮 */}
				<div className="flex flex-wrap gap-4">
					{showSubmitForm && allowAnonymousSubmissions && (
						<button
							type="button"
							onClick={() => setShowForm(!showForm)}
							className="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
						>
							<svg
								className="w-4 h-4 mr-2"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
								xmlns="http://www.w3.org/2000/svg"
								aria-label="添加图标"
							>
								<title>添加</title>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M12 4v16m8-8H4"
								/>
							</svg>
							{showForm ? "取消提交" : "提交建议"}
						</button>
					)}

					{/* 刷新按钮 */}
					<button
						type="button"
						onClick={() => {
							loadFeatureRequests();
							loadProducts();
						}}
						disabled={isLoading}
						className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
					>
						<svg
							className={`w-4 h-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
							xmlns="http://www.w3.org/2000/svg"
							aria-label="刷新图标"
						>
							<title>刷新</title>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
							/>
						</svg>
						刷新
					</button>
				</div>
			</div>

			{/* 统计信息 */}
			{pagination && (
				<div className="bg-gray-50 rounded-lg p-4 mb-6">
					<div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
						<div className="text-center">
							<div className="text-2xl font-bold text-gray-900">
								{pagination.total}
							</div>
							<div className="text-sm text-gray-500">
								总功能请求
							</div>
						</div>
						<div className="text-center">
							<div className="text-2xl font-bold text-blue-600">
								{products.length}
							</div>
							<div className="text-sm text-gray-500">
								产品数量
							</div>
						</div>
						<div className="text-center">
							<div className="text-2xl font-bold text-green-600">
								{featureRequests.reduce(
									(sum, fr) => sum + fr.voteCount,
									0,
								)}
							</div>
							<div className="text-sm text-gray-500">
								总投票数
							</div>
						</div>
					</div>
				</div>
			)}

			{/* 提交表单 */}
			{showForm && showSubmitForm && (
				<div className="mb-8">
					<FeatureRequestForm
						products={products}
						onSubmit={handleSubmitFeatureRequest}
						onCancel={() => setShowForm(false)}
						showAuthorFields={allowAnonymousSubmissions}
						requireAuthorInfo={false}
					/>
				</div>
			)}

			{/* 功能请求列表 */}
			<FeatureRequestList
				featureRequests={featureRequests}
				onVote={handleVote}
				onUnvote={handleUnvote}
				votingStates={votingStates}
				showVoteCount={showVoteCounts}
				showProduct={products.length > 1}
				showAuthor={true}
				showDescription={true}
				onFeatureRequestClick={handleFeatureRequestClick}
				loading={isLoading}
				error={error}
				pagination={pagination}
				onPageChange={handlePageChange}
				onQueryChange={handleQueryChange}
			/>

			{/* 底部信息 */}
			<div className="mt-12 text-center text-gray-500 text-sm">
				<p>感谢您的参与！您的反馈对我们的产品发展非常重要。</p>
				{allowAnonymousVoting && (
					<p className="mt-2">
						您的投票和建议会匿名保存，我们承诺保护您的隐私。
					</p>
				)}
			</div>
		</div>
	);
}
