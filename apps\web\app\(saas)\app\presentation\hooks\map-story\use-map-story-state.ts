"use client";

import { useEffect, useRef, useState } from "react";
import { StoryState } from "../../constants";
import type {
	MapStoryRefs,
	MapStorySetters,
	MapStoryState,
	UseMapStoryProps,
} from "./types";
import { createLogger } from "./utils";

// 创建状态管理模块专用的日志记录器
const logger = createLogger("MapStoryState");

export function useMapStoryState(
	props: UseMapStoryProps,
): [MapStoryState, MapStorySetters, MapStoryRefs] {
	// 提取导出模式配置
	const isExportMode = props?.config?.isExportMode || false;
	const exportPointDuration = props?.config?.pointDuration || 5;
	const autoPlayEnabled = props?.config?.autoPlay ?? !isExportMode; // 默认在非导出模式下自动播放

	logger.debug("初始化地图故事状态", {
		isExportMode,
		exportPointDuration,
		autoPlayEnabled,
	});

	// 地图故事状态
	const [storyState, setStoryState] = useState<StoryState>(
		StoryState.OVERVIEW,
	);
	const [currentPointIndex, setCurrentPointIndex] = useState<number>(0);
	const [isPlaying, setIsPlaying] = useState<boolean>(false);
	const [isMapReady, setIsMapReady] = useState<boolean>(false);
	const [showPointInfo, setShowPointInfo] = useState<boolean>(false);
	const [showImages, setShowImages] = useState<boolean>(false);
	const [lightboxOpen, setLightboxOpen] = useState<boolean>(false);
	const [selectedImageIndex, setSelectedImageIndex] = useState<number>(0);
	const [flyToPointIndex, setFlyToPointIndex] = useState<number | null>(null);
	const [fitBoundsToPoints, setFitBoundsToPoints] = useState<boolean>(false);
	const [mapInstance, setMapInstance] = useState<mapboxgl.Map | null>(null);
	const [blinkingPoints, setBlinkingPoints] = useState<number[]>([]);
	const [showStartPrompt, setShowStartPrompt] = useState<boolean>(false);
	const [showRestartPrompt, setShowRestartPrompt] = useState<boolean>(false);
	const [autoPlayCountdown, setAutoPlayCountdown] = useState<number>(3);
	const [manuallyReset, setManuallyReset] = useState<boolean>(false);
	const [spotlightPosition, setSpotlightPosition] = useState<{
		x: number;
		y: number;
	} | null>(null);
	const [isFinishing, setIsFinishing] = useState<boolean>(false);
	const [lastPointTimerSet, setLastPointTimerSet] = useState<boolean>(false);
	const [isTypingCompleted, setIsTypingCompleted] = useState<boolean>(false);

	// 引用类型 - 用于跨渲染周期共享值
	const isAnimatingRef = useRef<boolean>(false);
	const currentPointDisplayTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const autoPlayTimerRef = useRef<NodeJS.Timeout | null>(null);
	const blinkTimerRef = useRef<NodeJS.Timeout | null>(null);
	const activeMarkerRef = useRef<HTMLDivElement | null>(null);
	const typingPromiseResolverRef = useRef<(() => void) | null>(null);

	// 每当storyState改变时记录日志
	useEffect(() => {
		logger.debug("状态变化", { newState: storyState });
	}, [storyState]);

	// 将状态合并为一个对象
	const state: MapStoryState = {
		storyState,
		currentPointIndex,
		isPlaying,
		isMapReady,
		showPointInfo,
		showImages,
		lightboxOpen,
		selectedImageIndex,
		flyToPointIndex,
		fitBoundsToPoints,
		mapInstance,
		blinkingPoints,
		showStartPrompt,
		showRestartPrompt,
		autoPlayCountdown,
		manuallyReset,
		spotlightPosition,
		isFinishing,
		lastPointTimerSet,
		isTypingCompleted,
	};

	// 将所有 setState 方法合并为一个对象
	const setters: MapStorySetters = {
		setStoryState,
		setCurrentPointIndex,
		setIsPlaying,
		setIsMapReady,
		setShowPointInfo,
		setShowImages,
		setLightboxOpen,
		setSelectedImageIndex,
		setFlyToPointIndex,
		setFitBoundsToPoints,
		setMapInstance,
		setBlinkingPoints,
		setShowStartPrompt,
		setShowRestartPrompt,
		setAutoPlayCountdown,
		setManuallyReset,
		setSpotlightPosition,
		setIsFinishing,
		setLastPointTimerSet,
		setIsTypingCompleted,
	};

	// 将所有 ref 合并为一个对象
	const refs: MapStoryRefs = {
		isAnimatingRef,
		currentPointDisplayTimeoutRef,
		autoPlayTimerRef,
		blinkTimerRef,
		activeMarkerRef,
		typingPromiseResolverRef,
	};

	return [state, setters, refs];
}
