"use client";

import React from "react";
import type { SocialPlatform } from "../types/cardTypes";

interface SmartCardWrapperProps {
	platform: SocialPlatform;
	children: React.ReactNode;
	className?: string;
	style?: React.CSSProperties;
	isPreview?: boolean;
}

// 平台尺寸配置
const PLATFORM_DIMENSIONS = {
	instagram: { width: 1080, height: 1080, aspectRatio: 1 },
	wechat: { width: 1200, height: 900, aspectRatio: 4 / 3 },
	weibo: { width: 1080, height: 1350, aspectRatio: 4 / 5 },
	twitter: { width: 1200, height: 675, aspectRatio: 16 / 9 },
	facebook: { width: 1200, height: 630, aspectRatio: 1.91 },
} as const;

// 基准尺寸（Instagram 正方形）
const BASE_DIMENSIONS = { width: 1080, height: 1080 };

function calculatePlatformScale(platform: SocialPlatform): number {
	const dimensions =
		PLATFORM_DIMENSIONS[platform] || PLATFORM_DIMENSIONS.instagram;

	// 基于面积的智能缩放
	const baseArea = BASE_DIMENSIONS.width * BASE_DIMENSIONS.height;
	const platformArea = dimensions.width * dimensions.height;
	const areaScale = Math.sqrt(platformArea / baseArea);

	// 限制缩放范围，避免过大或过小
	return Math.max(0.7, Math.min(1.5, areaScale));
}

export function SmartCardWrapper({
	platform,
	children,
	className = "",
	style = {},
	isPreview = false,
}: SmartCardWrapperProps) {
	const dimensions =
		PLATFORM_DIMENSIONS[platform] || PLATFORM_DIMENSIONS.instagram;
	const scale = calculatePlatformScale(platform);

	// 在预览模式下，提供额外的适配逻辑
	const finalStyle: React.CSSProperties = {
		width: dimensions.width,
		height: dimensions.height,
		position: "relative",
		overflow: "hidden",
		// 平滑过渡效果
		transition: "all 0.3s ease-in-out",
		...style,
	};

	// 为子元素提供缩放上下文
	return (
		<div
			className={`smart-card-wrapper ${className}`}
			style={finalStyle}
			data-platform={platform}
			data-scale={scale}
			data-is-preview={isPreview}
		>
			{children}
		</div>
	);
}

// 导出工具函数供其他组件使用
export { calculatePlatformScale, PLATFORM_DIMENSIONS };
