"use client";

import { ArrowDown, Book } from "lucide-react";
import { useTranslations } from "next-intl";

interface CompletionPromptProps {
	isVisible: boolean;
	onViewMemories: () => void;
}

export function CompletionPrompt({
	isVisible,
	onViewMemories,
}: CompletionPromptProps) {
	const t = useTranslations("travelMemo.completionPrompt");
	if (!isVisible) return null;

	return (
		<div className="absolute inset-0 z-20 pointer-events-none">
			{/* 渐变背景 */}
			<div
				className="absolute inset-0 opacity-70 transition-opacity duration-1000"
				style={{
					background:
						"linear-gradient(to bottom, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.4) 100%)",
				}}
			/>

			{/* 中央文字区域 */}
			<div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-full max-w-lg text-center">
				<h2 className="text-4xl font-serif font-light text-white mb-3 drop-shadow-lg tracking-wide">
					{t("title")}
				</h2>
				<p className="text-white/90 text-lg mb-6 font-light max-w-md mx-auto">
					{t("description")}
				</p>

				{/* 查看回顾按钮 */}
				<button
					type="button"
					onClick={onViewMemories}
					className="pointer-events-auto mx-auto mb-6 px-6 py-3 bg-white/90 hover:bg-white text-slate-800 rounded-full shadow-lg flex items-center justify-center transition-all duration-200 transform hover:scale-105 active:scale-95"
				>
					<Book className="h-5 w-5 mr-2" />
					<span className="font-medium">
						{t("viewMemoriesButton")}
					</span>
				</button>

				{/* 向下滑动提示 */}
				<div className="animate-float mx-auto w-12 h-12 flex flex-col items-center justify-center text-white/80">
					<ArrowDown className="h-6 w-6 animate-bounce" />
				</div>
			</div>
		</div>
	);
}
