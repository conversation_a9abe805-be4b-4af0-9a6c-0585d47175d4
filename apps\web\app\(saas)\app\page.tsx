"use client";
import { Camera, Globe, MapPin, Plus, Ruler } from "lucide-react";
import { useTranslations } from "next-intl";
import dynamic from "next/dynamic";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import type {
	DiaryStats,
	Footprint,
} from "@repo/api/src/routes/diaries/schemas";
import type { FrontendTravelDiary } from "@repo/database/src/types/travel-diary";
import { AppWrapper } from "@saas/shared/components/AppWrapper";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Skeleton } from "@ui/components/skeleton";
import { useToast } from "@ui/components/use-toast";

import DiaryCard from "./components/DiaryCard";
import StatsCard from "./components/StatsCard";
import {
	deleteDiary,
	getDiaryStats,
	getFootprints,
	getUserDiaries,
	transformDiaryContent,
} from "./services/diary-service";

// 替换为新的Mapbox版本
// 使用dynamic导入Mapbox地图组件，禁用SSR
const TravelMapMapbox = dynamic(() => import("./components/TravelMapMapbox"), {
	ssr: false,
	loading: () => <Skeleton className="h-[500px] w-full" />,
});

// 初始统计数据结构
const initialStats: DiaryStats = {
	countriesVisited: 0,
	citiesExplored: 0,
	footprintsLeft: 0,
	photosTaken: 0,
};

const Dashboard = () => {
	const t = useTranslations("travelMemo.dashboard");
	const [diaries, setDiaries] = useState<FrontendTravelDiary[]>([]);
	const [isLoadingDiaries, setIsLoadingDiaries] = useState(true);
	const [diariesError, setDiariesError] = useState<string | null>(null);
	const [stats, setStats] = useState<DiaryStats>(initialStats);
	const [isLoadingStats, setIsLoadingStats] = useState(true);
	const [statsError, setStatsError] = useState<string | null>(null);
	const [footprints, setFootprints] = useState<Footprint[]>([]);
	const [isLoadingFootprints, setIsLoadingFootprints] = useState(true);
	const [footprintsError, setFootprintsError] = useState<string | null>(null);
	// 新增删除状态管理
	const [isDeletingDiary, setIsDeletingDiary] = useState(false);

	const router = useRouter();
	const { toast } = useToast();

	console.log("[Dashboard] Component Render Start"); // Add log
	// 获取日记列表
	useEffect(() => {
		console.log("[Dashboard] useEffect fetchDiaries - Mount"); // Add log
		const fetchDiaries = async () => {
			try {
				setIsLoadingDiaries(true);
				setDiariesError(null);
				const dbDiaries = await getUserDiaries();
				const frontendDiaries = dbDiaries.map(transformDiaryContent);
				setDiaries(frontendDiaries);
				console.log(
					"[Dashboard] useEffect fetchDiaries - Success, data: ",
					frontendDiaries,
				);
			} catch (err) {
				console.error("获取日记列表失败", err);
				setDiariesError(t("errors.loadDiariesFailed"));
			} finally {
				setIsLoadingDiaries(false);
			}
		};
		fetchDiaries();
	}, []);
	// 删除日记的处理函数
	const handleDeleteDiary = async (id: string) => {
		if (isDeletingDiary) return; // 防止重复点击

		try {
			setIsDeletingDiary(true);
			await deleteDiary(id);

			// 从本地状态中移除已删除的日记
			setDiaries((prevDiaries) =>
				prevDiaries.filter((diary) => diary.id !== id),
			);

			// 更新统计数据
			const fetchedStats = await getDiaryStats();
			setStats(fetchedStats);

			// 更新足迹点数据
			const fetchedFootprints = await getFootprints();
			setFootprints(fetchedFootprints);

			// 刷新路由以确保顶部加载进度条完成
			router.refresh();

			// 显示成功通知
			toast({
				title: t("toast.deleteSuccessTitle"),
				description: t("toast.deleteSuccessDescription"),
				variant: "default",
			});
		} catch (error) {
			console.error("删除日记失败:", error);
			// 显示错误通知
			toast({
				title: t("toast.deleteFailedTitle"),
				description:
					error instanceof Error
						? error.message
						: t("toast.deleteFailedDescriptionGeneric"),
				variant: "destructive",
			});
		} finally {
			setIsDeletingDiary(false);
		}
	};

	// 获取统计数据
	useEffect(() => {
		console.log("[Dashboard] useEffect fetchStats - Mount"); // Add log
		const fetchStats = async () => {
			try {
				setIsLoadingStats(true);
				setStatsError(null);
				const fetchedStats = await getDiaryStats();
				setStats(fetchedStats);
			} catch (err) {
				console.error("获取统计数据失败", err);
				setStatsError(t("errors.loadStatsFailed"));
			} finally {
				setIsLoadingStats(false);
			}
		};
		fetchStats();
	}, []);

	// 获取足迹点数据
	useEffect(() => {
		console.log("[Dashboard] useEffect fetchFootprints - Mount"); // Add log
		const fetchFootprintsData = async () => {
			try {
				console.log("[Dashboard] fetchFootprints - Start"); // Add log
				setIsLoadingFootprints(true);
				setFootprintsError(null);
				const fetchedFootprints = await getFootprints();
				console.log(
					`[Dashboard] fetchFootprints - Success, count: ${fetchedFootprints.length}`,
				); // Add log
				setFootprints(fetchedFootprints);
			} catch (err) {
				console.error("[Dashboard] 获取足迹点失败", err); // Add log
				setFootprintsError(t("errors.loadFootprintsFailed"));
			} finally {
				console.log("[Dashboard] fetchFootprints - Finish"); // Add log
				setIsLoadingFootprints(false);
			}
		};
		fetchFootprintsData();
	}, []);

	// 将统计数据映射到 StatsCard 需要的格式
	const statsCardsData = [
		{
			title: t("stats.countriesVisited"),
			value: stats.countriesVisited,
			icon: Globe,
			isLoading: isLoadingStats,
			error: statsError,
		},
		{
			title: t("stats.citiesExplored"),
			value: stats.citiesExplored,
			icon: MapPin,
			isLoading: isLoadingStats,
			error: statsError,
		},
		{
			title: t("stats.footprintsLeft"),
			value: stats.footprintsLeft,
			icon: Ruler,
			isLoading: isLoadingStats,
			error: statsError,
		},
		{
			title: t("stats.photosTaken"),
			value: stats.photosTaken,
			icon: Camera,
			isLoading: isLoadingStats,
			error: statsError,
		},
	];

	console.log(
		`[Dashboard] Render - isLoadingFootprints: ${isLoadingFootprints}, footprintsError: ${footprintsError}, footprints.length: ${footprints.length}`,
	);

	return (
		<AppWrapper>
			<div className="w-full">
				{/* ... Header ... */}
				<div className="flex justify-between items-center mb-6">
					<h1 className="text-3xl font-bold">{t("headerTitle")}</h1>
					<Link href="/app/diary/new">
						<Button className="bg-travel-primary text-travel-dark hover:bg-travel-primary/90">
							<Plus size={16} className="mr-2" />
							{t("newDiaryButton")}
						</Button>
					</Link>
				</div>

				{/* Stats Section */}
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
					{statsCardsData.map((stat, index) =>
						stat.isLoading ? (
							<Skeleton key={index} className="h-[110px]" /> // 骨架屏
						) : stat.error ? (
							<Card
								key={index}
								className="border-destructive/50 bg-destructive/10"
							>
								<CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
									<CardTitle className="text-sm font-medium text-destructive">
										{stat.title}
									</CardTitle>
									<stat.icon className="h-4 w-4 text-destructive/80" />
								</CardHeader>
								<CardContent>
									<p className="text-xs text-destructive">
										{stat.error}
									</p>
								</CardContent>
							</Card>
						) : (
							<StatsCard
								key={index}
								title={stat.title}
								value={stat.value}
								icon={stat.icon}
							/>
						),
					)}
				</div>

				{/* Map Section */}
				<div className="mb-8 w-full">
					<h2 className="text-2xl font-semibold mb-4">
						{t("map.title")}
					</h2>
					{/* Loading Skeleton - ONLY during initial load */}
					{isLoadingFootprints && footprints.length === 0 && (
						<div>
							<Skeleton className="h-[500px] w-full" />
						</div>
					)}
					{/* Error Message */}
					{footprintsError && (
						<div>
							<div className="h-[500px] w-full flex items-center justify-center bg-muted rounded-md border border-destructive/50 text-destructive">
								{footprintsError}
							</div>
						</div>
					)}
					{/* Map Component - Render once loading is false, regardless of footprints content */}
					{!isLoadingFootprints && !footprintsError && (
						<div className="w-full">
							<TravelMapMapbox
								height="600px" // 增加地图高度
								footprints={footprints} // 传递足迹数据 (can be empty initially)
								// onMarkerClick={(id) => console.log("Dashboard: Marker clicked:", id)} // 可选：处理标记点击
							/>
						</div>
					)}
				</div>

				{/* Diaries Section */}
				<div className="mt-8">
					{/* ... Diaries header ... */}
					<div className="flex justify-between items-center mb-4">
						<h2 className="text-2xl font-semibold">
							{t("diaries.title")}
						</h2>
						<Link href="/app/diary/new">
							<Button variant="outline">
								<Plus size={16} className="mr-2" />
								{t("newDiaryButton")}
							</Button>
						</Link>
					</div>
					{/* Diaries Loading */}
					{isLoadingDiaries && (
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
							{[1, 2, 3].map((i) => (
								<Skeleton key={i} className="h-[300px]" />
							))}
						</div>
					)}
					{/* Diaries Error */}
					{diariesError && (
						<div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-md">
							{diariesError}
						</div>
					)}
					{/* No Diaries Message */}
					{!isLoadingDiaries &&
						!diariesError &&
						diaries.length === 0 && (
							<div className="bg-amber-50 border border-amber-200 text-amber-700 p-6 rounded-md text-center">
								<p className="mb-3 font-medium">
									{t("diaries.emptyState.title")}
								</p>
								<Link href="/app/diary/new">
									<Button
										variant="outline"
										className="border-amber-300 hover:border-amber-400 text-amber-700"
									>
										<Plus size={16} className="mr-2" />
										{t("diaries.emptyState.cta")}
									</Button>
								</Link>
							</div>
						)}{" "}
					{/* Diaries List */}
					{!isLoadingDiaries && diaries.length > 0 && (
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
							{diaries.map((diary) => (
								<DiaryCard
									key={diary.id}
									id={diary.id}
									title={diary.title}
									excerpt={
										diary.subtitle ||
										t("diaries.card.defaultExcerpt")
									}
									imageUrl={
										diary.coverImage ||
										"https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&q=80"
									}
									// 确保安全访问可能不存在的属性
									location={
										diary.timelines?.[0]?.points?.[0]
											?.title ||
										t("diaries.card.defaultLocation")
									}
									date={(() => {
										// 如果没有时间线，使用当前日期
										if (!diary.timelines?.length) {
											return new Date()
												.toISOString()
												.split("T")[0];
										}

										// 收集所有有效日期
										const allDates: Date[] = [];
										diary.timelines.forEach((timeline) => {
											if (!timeline.date) return;

											let dateObj: Date | null = null;
											if (
												typeof timeline.date ===
												"string"
											) {
												dateObj = new Date(
													timeline.date,
												);
											} else if (
												timeline.date instanceof Date
											) {
												dateObj = timeline.date;
											}

											if (
												dateObj &&
												!Number.isNaN(dateObj.getTime())
											) {
												allDates.push(dateObj);
											}
										});

										// 如果没有有效日期，使用当前日期
										if (allDates.length === 0) {
											return new Date()
												.toISOString()
												.split("T")[0];
										}

										// 找出最早的日期
										const earliestDate = new Date(
											Math.min(
												...allDates.map((d) =>
													d.getTime(),
												),
											),
										);
										return earliestDate
											.toISOString()
											.split("T")[0];
									})()} // 计算日期范围：找出所有时间点中最早和最晚的日期
									endDate={(() => {
										// 如果没有时间线或者只有一个时间点，则不设置结束日期
										if (
											!diary.timelines?.length ||
											diary.timelines.length <= 1
										)
											return undefined;

										// 找出所有有效日期
										const allDates = diary.timelines.reduce<
											Date[]
										>((dates, timeline) => {
											if (!timeline.date) return dates;

											// 处理字符串日期
											if (
												typeof timeline.date ===
												"string"
											) {
												dates.push(
													new Date(timeline.date),
												);
											}
											// 处理Date对象
											else if (
												timeline.date instanceof Date
											) {
												dates.push(timeline.date);
											}

											return dates;
										}, []);

										if (allDates.length <= 1)
											return undefined;

										// 找出最晚的日期
										const latestDate = new Date(
											Math.max(
												...allDates.map((date) =>
													date.getTime(),
												),
											),
										);
										return latestDate
											.toISOString()
											.split("T")[0];
									})()} // 添加足迹数量 - 所有时间线中所有点位的总数
									footprintsCount={
										diary.timelines?.reduce(
											(count, timeline) =>
												count +
												(timeline.points?.length || 0),
											0,
										) || 0
									}
									// 添加照片数量 - 所有点位中图片数量的总和
									photosCount={
										diary.timelines?.reduce(
											(count, timeline) => {
												if (!timeline.points?.length)
													return count;

												// 累加每个点位中的图片数量（注意使用images属性而不是photos）
												return (
													count +
													timeline.points.reduce(
														(photoCount, point) =>
															photoCount +
															(point.images
																?.length || 0),
														0,
													)
												);
											},
											0,
										) || 0
									}
									// 添加删除功能回调
									onDelete={handleDeleteDiary}
								/>
							))}
						</div>
					)}
				</div>
			</div>
		</AppWrapper>
	);
};

export default Dashboard;
