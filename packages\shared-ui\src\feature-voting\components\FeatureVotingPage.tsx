"use client";
import { useState } from "react";
import { useFeatureVoting } from "../hooks";
import type { FeatureVotingPageProps, SubmitFeatureRequest } from "../types";
import { FeatureRequestList } from "./FeatureRequestList";
import { ProductSelector } from "./ProductSelector";
import { SubmitFeatureForm } from "./SubmitFeatureForm";

/**
 * Feature Voting 完整页面组件
 * 展示如何组合使用所有feature-voting组件
 */
export function FeatureVotingPage({
	className,
	defaultProductId,
	showSubmitForm = true,
	allowVoting = true,
	allowComments = true,
	ui,
}: FeatureVotingPageProps) {
	const [selectedProductId, setSelectedProductId] = useState<
		string | undefined
	>(defaultProductId);
	const [activeTab, setActiveTab] = useState("list");

	const {
		Card,
		CardContent,
		CardHeader,
		CardTitle,
		Tabs,
		TabsContent,
		TabsList,
		TabsTrigger,
		Lightbulb,
		TrendingUp,
		Plus,
		cn,
	} = ui;

	const { products, isLoading, error, submitFeature } = useFeatureVoting({
		productId: selectedProductId,
		showVoteCounts: true,
		autoRefresh: true,
	});

	const handleSubmitFeature = async (data: SubmitFeatureRequest) => {
		await submitFeature(data);
		setActiveTab("list"); // 切换到列表页面
	};

	const handleProductChange = (productId: string) => {
		setSelectedProductId(productId);
	};

	return (
		<div className={cn("w-full max-w-6xl mx-auto space-y-6", className)}>
			{/* 页面标题 */}
			<div className="text-center space-y-2">
				<h1 className="text-3xl font-bold flex items-center justify-center gap-2">
					<Lightbulb className="h-8 w-8 text-primary" />
					特性投票
				</h1>
				<p className="text-muted-foreground">
					提交您的想法，为喜欢的特性投票，共同塑造产品未来
				</p>
			</div>

			{/* 产品选择器 */}
			{products.length > 1 && (
				<Card>
					<CardContent className="pt-6">
						<ProductSelector
							products={products}
							selectedProductId={selectedProductId}
							onProductChange={handleProductChange}
							ui={ui}
						/>
					</CardContent>
				</Card>
			)}

			{/* 主要内容区域 */}
			<Tabs
				value={activeTab}
				onValueChange={setActiveTab}
				className="w-full"
			>
				<TabsList className="grid w-full grid-cols-2">
					<TabsTrigger
						value="list"
						className="flex items-center gap-2"
					>
						<TrendingUp className="h-4 w-4" />
						特性列表
					</TabsTrigger>
					{showSubmitForm && (
						<TabsTrigger
							value="submit"
							className="flex items-center gap-2"
						>
							<Plus className="h-4 w-4" />
							提交想法
						</TabsTrigger>
					)}
				</TabsList>

				{/* 特性列表标签页 */}
				<TabsContent value="list" className="space-y-6">
					<FeatureRequestList
						productId={selectedProductId}
						showVoteCounts={true}
						allowVoting={allowVoting}
						allowComments={allowComments}
						ui={ui}
					/>
				</TabsContent>

				{/* 提交表单标签页 */}
				{showSubmitForm && (
					<TabsContent value="submit" className="space-y-6">
						<SubmitFeatureForm
							products={products}
							onSubmit={handleSubmitFeature}
							isLoading={isLoading}
							ui={ui}
						/>
					</TabsContent>
				)}
			</Tabs>

			{/* 使用说明 */}
			<Card className="bg-muted/50">
				<CardHeader>
					<CardTitle className="text-lg">如何使用</CardTitle>
				</CardHeader>
				<CardContent className="space-y-2 text-sm text-muted-foreground">
					<p>
						• <strong>浏览特性：</strong> 查看所有用户提交的特性请求
					</p>
					<p>
						• <strong>投票支持：</strong>{" "}
						点击投票按钮为您喜欢的特性投票
					</p>
					<p>
						• <strong>提交想法：</strong>{" "}
						在"提交想法"标签页中提交您的新特性建议
					</p>
					<p>
						• <strong>匿名使用：</strong>{" "}
						无需注册即可投票和提交，我们会记住您的偏好
					</p>
				</CardContent>
			</Card>
		</div>
	);
}
