"use client";

import { Building2, Calendar, Crown, Flag, MapPin } from "lucide-react";
import React from "react";
import { getDateDisplay } from "../utils/dateUtils";

import type {
	CardCustomization,
	CardTemplateProps,
	CountryData,
	TravelPoint,
} from "../types/cardTypes";

interface ElegantCardProps extends CardTemplateProps {
	mapImageData?: {
		dataURL: string;
		dimensions: { width: number; height: number };
	};
	mapComponent?: React.ReactNode;
	travelPoints: TravelPoint[];
	visitedCountries: CountryData[];
	customization: CardCustomization;
	platform?: string;
	isPreview?: boolean;
}

export function ElegantCard({
	mapImageData,
	mapComponent,
	travelPoints,
	visitedCountries,
	customization,
	platform = "instagram",
	isPreview = false,
}: ElegantCardProps) {
	// 默认值
	const colors = customization.colors || {
		primary: "#1a1a1a",
		secondary: "#4a4a4a",
		accent: "#d4af37",
		background: "#000000",
		text: "#ffffff",
	};

	const typography = customization.typography || {
		fontFamily: "'Playfair Display', serif",
		headerSize: 36,
		bodySize: 20,
		titleWeight: 700,
	};

	const layout = customization.layout || {
		padding: 40,
		spacing: 24,
		borderRadius: 0,
		showShadow: true,
	};

	const content = customization.content || {
		showUserInfo: true,
		showDetailedStats: true,
		customTitle: "Journey of Distinction",
		customFooter: "Curated by Excellence",
	};

	// Ensure showUserInfo and showDetailedStats have proper defaults
	const safeContent = {
		showUserInfo: content.showUserInfo ?? true,
		showDetailedStats: content.showDetailedStats ?? true,
		customTitle: content.customTitle || "Journey of Distinction",
		customFooter: content.customFooter || "Curated by Excellence",
	};

	// 计算统计数据
	const stats = {
		totalPoints: travelPoints.length,
		totalCountries: visitedCountries.length,
		totalCities: new Set(travelPoints.map((p) => p.city)).size,
		earliestDate:
			travelPoints.length > 0
				? travelPoints.reduce((earliest, point) =>
						new Date(point.date) < new Date(earliest.date)
							? point
							: earliest,
					).date
				: null,
		latestDate:
			travelPoints.length > 0
				? travelPoints.reduce((latest, point) =>
						new Date(point.date) > new Date(latest.date)
							? point
							: latest,
					).date
				: null,
	};

	// 平台尺寸配置
	const platformDimensions = {
		instagram: { width: 1080, height: 1080, aspectRatio: 1 },
		wechat: { width: 1200, height: 900, aspectRatio: 4 / 3 },
		weibo: { width: 1080, height: 1350, aspectRatio: 4 / 5 },
		twitter: { width: 1200, height: 675, aspectRatio: 16 / 9 },
		facebook: { width: 1200, height: 630, aspectRatio: 1.91 },
	};

	const dimensions =
		platformDimensions[platform as keyof typeof platformDimensions] ||
		platformDimensions.instagram;

	// 根据宽高比决定布局策略
	const isWideFormat = dimensions.aspectRatio > 1.5; // Twitter, Facebook
	const isTallFormat = dimensions.aspectRatio < 0.9; // Weibo
	const isSquareFormat =
		dimensions.aspectRatio >= 0.9 && dimensions.aspectRatio <= 1.3; // Instagram, WeChat

	const scale = Math.min(dimensions.width / 1080, 1.5);

	const adjustedTypography = {
		titleSize: Math.round(isWideFormat ? 42 * scale : 52 * scale),
		statsSize: Math.round(isWideFormat ? 64 * scale : 84 * scale),
		headerSize: Math.round((typography.headerSize || 36) * scale),
		bodySize: Math.round((typography.bodySize || 20) * scale),
		smallSize: Math.round(16 * scale),
		titleWeight: typography.titleWeight || 700,
		fontFamily: typography.fontFamily || "'Playfair Display', serif",
	};

	const getLayoutValue = (
		key: "padding" | "spacing" | "borderRadius",
		defaultValue: number,
	): number => {
		const value = layout[key];
		return typeof value === "number" ? value : defaultValue;
	};

	const getDateRange = () => {
		return getDateDisplay(customization, travelPoints, "");
	};

	// 渲染宽屏布局 (Twitter, Facebook)
	if (isWideFormat) {
		return (
			<div
				data-card-element
				className="relative overflow-hidden"
				style={{
					width: dimensions.width,
					height: dimensions.height,
					fontFamily: adjustedTypography.fontFamily,
					background:
						"linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #000000 100%)",
					padding: getLayoutValue("padding", 40),
					position: "relative",
					display: "flex",
					flexDirection: "row",
					alignItems: "stretch",
				}}
			>
				{/* 高端背景纹理 */}
				<div className="absolute inset-0">
					<div
						style={{
							position: "absolute",
							top: "20px",
							left: "20px",
							right: "20px",
							bottom: "20px",
							border: "2px solid",
							borderImage:
								"linear-gradient(135deg, #d4af37, #f4e4bc, #d4af37) 1",
							pointerEvents: "none",
						}}
					/>
					{/* 金色角落装饰 */}
					<div
						style={{
							position: "absolute",
							top: "30px",
							left: "30px",
							width: "40px",
							height: "40px",
							borderTop: "3px solid #d4af37",
							borderLeft: "3px solid #d4af37",
						}}
					/>
					<div
						style={{
							position: "absolute",
							top: "30px",
							right: "30px",
							width: "40px",
							height: "40px",
							borderTop: "3px solid #d4af37",
							borderRight: "3px solid #d4af37",
						}}
					/>
					<div
						style={{
							position: "absolute",
							bottom: "30px",
							left: "30px",
							width: "40px",
							height: "40px",
							borderBottom: "3px solid #d4af37",
							borderLeft: "3px solid #d4af37",
						}}
					/>
					<div
						style={{
							position: "absolute",
							bottom: "30px",
							right: "30px",
							width: "40px",
							height: "40px",
							borderBottom: "3px solid #d4af37",
							borderRight: "3px solid #d4af37",
						}}
					/>
				</div>

				{/* 左侧内容区域 */}
				<div
					className="flex flex-col justify-between relative z-10"
					style={{
						width: "45%",
						paddingRight: getLayoutValue("spacing", 24) * 2,
					}}
				>
					{/* Header - 高端标题区域 */}
					{safeContent.showUserInfo && (
						<div className="text-center relative">
							<div
								style={{
									display: "flex",
									justifyContent: "center",
									marginBottom: getLayoutValue("spacing", 24),
								}}
							>
								<Crown
									size={adjustedTypography.bodySize + 4}
									style={{
										color: "#d4af37",
										filter: "drop-shadow(0 4px 8px rgba(212, 175, 55, 0.3))",
									}}
								/>
							</div>

							<h1
								style={{
									fontSize: adjustedTypography.titleSize,
									fontWeight: 400,
									color: "#ffffff",
									margin: 0,
									lineHeight: 1.2,
									textAlign: "center",
									letterSpacing: "2px",
									textTransform: "uppercase",
									marginBottom: getLayoutValue("spacing", 24),
								}}
							>
								{safeContent.customTitle}
							</h1>

							{getDateRange() && (
								<div
									style={{
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										gap: "12px",
										color: "#d4af37",
										fontSize:
											adjustedTypography.bodySize - 2,
										fontWeight: 300,
										letterSpacing: "1px",
									}}
								>
									<div
										style={{
											width: "20px",
											height: "1px",
											background: "#d4af37",
										}}
									/>
									<Calendar
										size={adjustedTypography.bodySize - 2}
									/>
									<span>{getDateRange()}</span>
									<div
										style={{
											width: "20px",
											height: "1px",
											background: "#d4af37",
										}}
									/>
								</div>
							)}
						</div>
					)}

					{/* Stats - 垂直布局 */}
					{safeContent.showDetailedStats && (
						<div className="space-y-6">
							{/* 足迹点 */}
							<div className="text-center">
								<div
									style={{
										fontSize: adjustedTypography.statsSize,
										fontWeight: 300,
										color: "#d4af37",
										lineHeight: 1,
										marginBottom: "10px",
										fontFamily: "'Playfair Display', serif",
									}}
								>
									{stats.totalPoints}
								</div>
								<div
									style={{
										fontSize:
											adjustedTypography.bodySize - 4,
										color: "#ffffff",
										fontWeight: 300,
										letterSpacing: "1px",
										textTransform: "uppercase",
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										gap: "8px",
									}}
								>
									<MapPin
										size={adjustedTypography.bodySize - 4}
									/>
									PLACES
								</div>
							</div>

							{/* 城市 */}
							<div className="text-center">
								<div
									style={{
										fontSize: adjustedTypography.statsSize,
										fontWeight: 300,
										color: "#d4af37",
										lineHeight: 1,
										marginBottom: "10px",
										fontFamily: "'Playfair Display', serif",
									}}
								>
									{stats.totalCities}
								</div>
								<div
									style={{
										fontSize:
											adjustedTypography.bodySize - 4,
										color: "#ffffff",
										fontWeight: 300,
										letterSpacing: "1px",
										textTransform: "uppercase",
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										gap: "8px",
									}}
								>
									<Building2
										size={adjustedTypography.bodySize - 4}
									/>
									CITIES
								</div>
							</div>

							{/* 国家 */}
							<div className="text-center">
								<div
									style={{
										fontSize: adjustedTypography.statsSize,
										fontWeight: 300,
										color: "#d4af37",
										lineHeight: 1,
										marginBottom: "10px",
										fontFamily: "'Playfair Display', serif",
									}}
								>
									{stats.totalCountries}
								</div>
								<div
									style={{
										fontSize:
											adjustedTypography.bodySize - 4,
										color: "#ffffff",
										fontWeight: 300,
										letterSpacing: "1px",
										textTransform: "uppercase",
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										gap: "8px",
									}}
								>
									<Flag
										size={adjustedTypography.bodySize - 4}
									/>
									COUNTRIES
								</div>
							</div>
						</div>
					)}

					{/* Footer - 高端署名 */}
					{safeContent.customFooter && (
						<div
							className="text-center relative"
							style={{
								paddingTop: getLayoutValue("spacing", 24),
								borderTop: "1px solid rgba(212, 175, 55, 0.3)",
							}}
						>
							<p
								style={{
									fontSize: adjustedTypography.bodySize - 4,
									color: "#d4af37",
									margin: 0,
									fontWeight: 300,
									textAlign: "center",
									letterSpacing: "1px",
									fontStyle: "italic",
								}}
							>
								{safeContent.customFooter}
							</p>
						</div>
					)}
				</div>

				{/* 右侧地图区域 */}
				<div
					className="relative"
					style={{
						width: "55%",
						padding: "8px",
						background:
							"linear-gradient(45deg, #d4af37, #f4e4bc, #d4af37)",
					}}
				>
					<div
						style={{
							width: "100%",
							height: "100%",
							overflow: "hidden",
							position: "relative",
							background: "#000",
						}}
					>
						{mapComponent ? (
							<div style={{ width: "100%", height: "100%" }}>
								{mapComponent}
							</div>
						) : (
							mapImageData && (
								<img
									src={mapImageData.dataURL}
									alt="Travel Map"
									style={{
										width: "100%",
										height: "100%",
										objectFit: "cover",
										filter: "sepia(10%) contrast(1.1) brightness(0.9)",
									}}
								/>
							)
						)}

						{/* 高端覆盖层 */}
						<div
							className="absolute inset-0"
							style={{
								background:
									"linear-gradient(135deg, rgba(212, 175, 55, 0.1), transparent, rgba(212, 175, 55, 0.1))",
							}}
						/>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div
			data-card-element
			className="relative overflow-hidden flex flex-col"
			style={{
				width: dimensions.width,
				height: dimensions.height,
				fontFamily: adjustedTypography.fontFamily,
				background:
					"linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #000000 100%)",
				padding: getLayoutValue("padding", 40),
				position: "relative",
			}}
		>
			{/* 高端背景纹理 */}
			<div className="absolute inset-0">
				{/* 金色装饰边框 */}
				<div
					style={{
						position: "absolute",
						top: "20px",
						left: "20px",
						right: "20px",
						bottom: "20px",
						border: "2px solid",
						borderImage:
							"linear-gradient(135deg, #d4af37, #f4e4bc, #d4af37) 1",
						pointerEvents: "none",
					}}
				/>

				{/* 优雅的角落装饰 */}
				<div
					style={{
						position: "absolute",
						top: "30px",
						left: "30px",
						width: "60px",
						height: "60px",
						borderTop: "3px solid #d4af37",
						borderLeft: "3px solid #d4af37",
					}}
				/>
				<div
					style={{
						position: "absolute",
						top: "30px",
						right: "30px",
						width: "60px",
						height: "60px",
						borderTop: "3px solid #d4af37",
						borderRight: "3px solid #d4af37",
					}}
				/>
				<div
					style={{
						position: "absolute",
						bottom: "30px",
						left: "30px",
						width: "60px",
						height: "60px",
						borderBottom: "3px solid #d4af37",
						borderLeft: "3px solid #d4af37",
					}}
				/>
				<div
					style={{
						position: "absolute",
						bottom: "30px",
						right: "30px",
						width: "60px",
						height: "60px",
						borderBottom: "3px solid #d4af37",
						borderRight: "3px solid #d4af37",
					}}
				/>
			</div>

			{/* Header - 高端标题区域 */}
			{safeContent.showUserInfo && (
				<div
					className="text-center relative flex-shrink-0"
					style={{
						marginBottom: getLayoutValue("spacing", 24),
						paddingTop: getLayoutValue("spacing", 16),
					}}
				>
					{/* 皇冠装饰 */}
					<div
						style={{
							display: "flex",
							justifyContent: "center",
							marginBottom: getLayoutValue("spacing", 16),
						}}
					>
						<Crown
							size={adjustedTypography.bodySize + 4}
							style={{
								color: "#d4af37",
								filter: "drop-shadow(0 4px 8px rgba(212, 175, 55, 0.3))",
							}}
						/>
					</div>

					<h1
						style={{
							fontSize: adjustedTypography.titleSize,
							fontWeight: 400,
							color: "#ffffff",
							margin: 0,
							lineHeight: 1.2,
							textAlign: "center",
							letterSpacing: "2px",
							textTransform: "uppercase",
							marginBottom: getLayoutValue("spacing", 16),
						}}
					>
						{safeContent.customTitle}
					</h1>

					{getDateRange() && (
						<div
							style={{
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								gap: "12px",
								color: "#d4af37",
								fontSize: adjustedTypography.bodySize - 2,
								fontWeight: 300,
								letterSpacing: "1px",
							}}
						>
							<div
								style={{
									width: "30px",
									height: "1px",
									background: "#d4af37",
								}}
							/>
							<Calendar size={adjustedTypography.bodySize - 2} />
							<span>{getDateRange()}</span>
							<div
								style={{
									width: "30px",
									height: "1px",
									background: "#d4af37",
								}}
							/>
						</div>
					)}
				</div>
			)}

			{/* 精简的统计数据 - 水平排列 */}
			{safeContent.showDetailedStats && (
				<div
					className="relative flex-shrink-0"
					style={{
						display: "flex",
						justifyContent: "space-between",
						alignItems: "center",
						marginBottom: getLayoutValue("spacing", 24),
						padding: `${getLayoutValue("spacing", 16)}px 0`,
						borderTop: "1px solid rgba(212, 175, 55, 0.3)",
						borderBottom: "1px solid rgba(212, 175, 55, 0.3)",
					}}
				>
					{/* 足迹点 */}
					<div className="text-center flex-1">
						<div
							style={{
								fontSize: adjustedTypography.statsSize * 0.8,
								fontWeight: 300,
								color: "#d4af37",
								lineHeight: 1,
								marginBottom: "8px",
								fontFamily: "'Playfair Display', serif",
							}}
						>
							{stats.totalPoints}
						</div>
						<div
							style={{
								fontSize: adjustedTypography.bodySize - 6,
								color: "#ffffff",
								fontWeight: 300,
								letterSpacing: "1px",
								textTransform: "uppercase",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								gap: "4px",
							}}
						>
							<MapPin size={adjustedTypography.bodySize - 6} />
							PLACES
						</div>
					</div>

					{/* 城市 */}
					<div className="text-center flex-1">
						<div
							style={{
								fontSize: adjustedTypography.statsSize * 0.8,
								fontWeight: 300,
								color: "#d4af37",
								lineHeight: 1,
								marginBottom: "8px",
								fontFamily: "'Playfair Display', serif",
							}}
						>
							{stats.totalCities}
						</div>
						<div
							style={{
								fontSize: adjustedTypography.bodySize - 6,
								color: "#ffffff",
								fontWeight: 300,
								letterSpacing: "1px",
								textTransform: "uppercase",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								gap: "4px",
							}}
						>
							<Building2 size={adjustedTypography.bodySize - 6} />
							CITIES
						</div>
					</div>

					{/* 国家 */}
					<div className="text-center flex-1">
						<div
							style={{
								fontSize: adjustedTypography.statsSize * 0.8,
								fontWeight: 300,
								color: "#d4af37",
								lineHeight: 1,
								marginBottom: "8px",
								fontFamily: "'Playfair Display', serif",
							}}
						>
							{stats.totalCountries}
						</div>
						<div
							style={{
								fontSize: adjustedTypography.bodySize - 6,
								color: "#ffffff",
								fontWeight: 300,
								letterSpacing: "1px",
								textTransform: "uppercase",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								gap: "4px",
							}}
						>
							<Flag size={adjustedTypography.bodySize - 6} />
							COUNTRIES
						</div>
					</div>
				</div>
			)}

			{/* Map Section - 高端边框设计，占据更多空间 */}
			<div
				className="relative flex-1"
				style={{
					marginBottom: getLayoutValue("spacing", 24),
					padding: "8px",
					background:
						"linear-gradient(45deg, #d4af37, #f4e4bc, #d4af37)",
					minHeight: "200px",
				}}
			>
				<div
					style={{
						width: "100%",
						height: "100%",
						overflow: "hidden",
						position: "relative",
						background: "#000",
					}}
				>
					{/* 渲染地图 - 支持真实地图组件或图片 */}
					{mapComponent ? (
						// 预览模式：使用真实地图组件
						<div
							style={{
								width: "100%",
								height: "100%",
							}}
						>
							{mapComponent}
						</div>
					) : (
						// 导出模式：使用地图截图
						mapImageData && (
							<img
								src={mapImageData.dataURL}
								alt="Travel Map"
								style={{
									width: "100%",
									height: "100%",
									objectFit: "cover",
									filter: "sepia(10%) contrast(1.1) brightness(0.9)",
								}}
							/>
						)
					)}

					{/* 高端覆盖层 - 不阻挡地图交互 */}
					<div
						className="absolute inset-0"
						style={{
							background:
								"linear-gradient(135deg, rgba(212, 175, 55, 0.1), transparent, rgba(212, 175, 55, 0.1))",
							// 确保装饰层不阻挡地图交互
							pointerEvents: "none",
							zIndex: 20, // 在地图之上但不阻挡交互
						}}
					/>

					{/* 水印文字 - 放在地图右下角 */}
					{safeContent.customFooter && (
						<div
							className="absolute bottom-6 right-6 text-center"
							style={{
								background: "rgba(0, 0, 0, 0.7)",
								padding: "8px 16px",
								border: "1px solid rgba(212, 175, 55, 0.5)",
								maxWidth: "80%",
							}}
						>
							<p
								style={{
									fontSize: adjustedTypography.bodySize - 4,
									color: "#d4af37",
									margin: 0,
									fontWeight: 300,
									textAlign: "center",
									letterSpacing: "1px",
									fontStyle: "italic",
								}}
							>
								{safeContent.customFooter}
							</p>
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
