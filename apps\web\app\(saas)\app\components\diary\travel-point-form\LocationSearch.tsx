"use client";

import { MapPin } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { StandaloneSearchBox } from "./StandaloneSearchBox";
import type { FormFieldProps, GeocodingResult } from "./types";

interface LocationSearchProps extends FormFieldProps {
	isOpen: boolean;
	geocodingResults: GeocodingResult | null;
	setGeocodingResults: React.Dispatch<
		React.SetStateAction<GeocodingResult | null>
	>;
}

export function LocationSearch({
	isOpen,
	formData,
	setFormData,
	geocodingResults,
	setGeocodingResults,
}: LocationSearchProps) {
	const t = useTranslations();
	const [isMapboxReady, setIsMapboxReady] = useState(false);

	// 检查Mapbox token是否可用
	useEffect(() => {
		if (process.env.NEXT_PUBLIC_MAPBOX_TOKEN) {
			setIsMapboxReady(true);
		} else {
			console.error("Mapbox token未设置，请检查环境变量");
		}
	}, []);

	// 处理地点选择
	const handlePlaceSelect = (place: any) => {
		if (!place.geometry?.location) {
			toast.error(
				t("travelMemo.travelPointForm.errors.coordinatesError"),
			);
			return;
		}

		// 更新表单数据
		const locationName = place.name || place.formatted_address || "";
		setFormData((prevData) => ({
			...prevData,
			location: locationName,
		}));

		// 准备地点类型数据 - Mapbox可能没有与Google Maps相同的types
		const placeTypes = [];

		// 添加基本类型
		placeTypes.push("point_of_interest");

		// 如果有Mapbox特有数据，添加相应类型
		if (place.mapbox_data) {
			if (place.mapbox_data.country) placeTypes.push("country");
			if (place.mapbox_data.region)
				placeTypes.push("administrative_area_level_1");
			if (place.mapbox_data.place) placeTypes.push("locality");
		}

		// 设置坐标和位置详情
		setGeocodingResults({
			lat: place.geometry.location.lat(),
			lng: place.geometry.location.lng(),
			formattedAddress: place.formatted_address || "",
			placeDetails: {
				name: place.name || "",
				photos: place.photos || [],
				types: placeTypes,
			},
		});

		toast.success(
			t("travelMemo.travelPointForm.messages.locationSelected"),
		);
	};

	return (
		<div>
			<label
				htmlFor="location-search"
				className="block text-sm font-medium mb-1"
			>
				{t("travelMemo.travelPointForm.labels.location")}
			</label>
			<div className="flex items-start">
				<MapPin className="w-5 h-5 mr-2 text-travel-dark/60 flex-shrink-0 mt-2" />
				<div className="flex flex-col w-full">
					<div className="relative w-full">
						{!isMapboxReady ? (
							<div className="border border-input rounded-md h-10 px-3 py-2 text-sm relative">
								<div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground pl-6">
									{t(
										"travelMemo.travelPointForm.placeholders.loadingMapSearch",
									)}
								</div>
							</div>
						) : (
							<StandaloneSearchBox
								onPlaceSelect={handlePlaceSelect}
								initialValue={formData.location || ""}
							/>
						)}
					</div>

					{!isMapboxReady && (
						<div className="text-xs text-red-600 mt-1">
							{t(
								"travelMemo.travelPointForm.errors.mapLoadError",
							)}
						</div>
					)}
				</div>
			</div>

			{/* 刷新按钮 - 仅在加载失败时显示 */}
			{isOpen && !isMapboxReady && (
				<div className="text-center mt-1">
					<button
						type="button"
						onClick={() => window.location.reload()}
						className="text-xs text-primary underline"
					>
						{t("travelMemo.travelPointForm.buttons.refreshPage")}
					</button>
				</div>
			)}
		</div>
	);
}
