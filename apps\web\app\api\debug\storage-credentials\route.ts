import {
	detectProviderTypeFromEnv,
	getProviderConfigFromEnv,
} from "@repo/storage";
import { NextResponse } from "next/server";

/**
 * 用于检查存储提供商凭证配置的调试路由
 * 注意：此路由仅用于开发环境，不应在生产环境中使用
 */
export async function GET() {
	// 检查是否为开发环境
	if (process.env.NODE_ENV === "production") {
		return NextResponse.json(
			{ error: "此路由仅在开发环境中可用" },
			{ status: 403 },
		);
	}

	try {
		// 检测当前环境中配置的存储提供商类型
		const providerType = detectProviderTypeFromEnv();

		if (!providerType) {
			return NextResponse.json(
				{ error: "未检测到任何存储提供商配置" },
				{ status: 404 },
			);
		}

		// 获取提供商配置
		const config = getProviderConfigFromEnv(providerType);

		// 创建掩码版本的凭证，只显示前4位和后4位
		const maskedConfig = {
			providerType,
			endpoint: config.endpoint,
			region: config.region,
			accessKeyId: maskString(config.accessKeyId),
			secretAccessKey: maskString(config.secretAccessKey),
			rawAccessKeyIdLength: config.accessKeyId?.length,
			rawSecretAccessKeyLength: config.secretAccessKey?.length,
		};

		return NextResponse.json({
			message: "当前存储提供商配置信息（敏感信息已部分隐藏）",
			config: maskedConfig,
			// 为腾讯云 COS 添加原始环境变量名称提示
			environmentVariables:
				providerType === "tencent-cos"
					? {
							accessKeyId: "TENCENT_COS_SECRET_ID",
							secretAccessKey: "TENCENT_COS_SECRET_KEY",
							region: "TENCENT_COS_REGION",
						}
					: undefined,
		});
	} catch (error) {
		console.error("获取存储提供商配置失败:", error);
		return NextResponse.json(
			{
				error: "获取存储提供商配置失败",
				message: error instanceof Error ? error.message : String(error),
			},
			{ status: 500 },
		);
	}
}

/**
 * 对敏感信息进行掩码处理，只显示前4位和后4位
 */
function maskString(value?: string): string | undefined {
	if (!value) return undefined;
	if (value.length <= 8) return "********"; // 如果字符串较短，全部掩码

	const prefix = value.substring(0, 4);
	const suffix = value.substring(value.length - 4);
	const maskedPart = "*".repeat(Math.min(value.length - 8, 10));

	return `${prefix}${maskedPart}${suffix}`;
}
