import type { TravelStatTranslations } from "../../hooks/useTravelStatTranslations";

interface SeoFaqProps {
	translations: TravelStatTranslations;
}

interface FaqItemProps {
	question: string;
	answer: string;
}

function FaqItem({ question, answer }: FaqItemProps) {
	return (
		<div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-sky-100 dark:border-gray-700">
			<h4 className="font-semibold text-gray-900 dark:text-white mb-2">
				{question}
			</h4>
			<p className="text-gray-600 dark:text-gray-300 text-sm">{answer}</p>
		</div>
	);
}

export function SeoFaq({ translations }: SeoFaqProps) {
	const faqItems = [
		{
			question:
				translations.seo?.faq?.items?.isItFree?.question() ||
				"Is Map Moment free to use?",
			answer:
				translations.seo?.faq?.items?.isItFree?.answer() ||
				"Yes! Map Moment is completely free to use. Create unlimited memory maps, generate beautiful cards, and preserve your travel moments without any cost.",
		},
		{
			question:
				translations.seo?.faq?.items?.dataStorage?.question() ||
				"How are my Map Moment memories stored?",
			answer:
				translations.seo?.faq?.items?.dataStorage?.answer() ||
				"Your precious memories are stored securely in your browser. Map Moment respects your privacy - we don't collect or store your personal travel data on our servers.",
		},
		{
			question:
				translations.seo?.faq?.items?.deviceSupport?.question() ||
				"Can I access Map Moment on different devices?",
			answer:
				translations.seo?.faq?.items?.deviceSupport?.answer() ||
				"Absolutely! Map Moment works beautifully on desktop, tablet, and mobile devices. Your memory collection is accessible wherever you are, whenever inspiration strikes.",
		},
		{
			question:
				translations.seo?.faq?.items?.socialPlatforms?.question() ||
				"Where can I share my Map Moment cards?",
			answer:
				translations.seo?.faq?.items?.socialPlatforms?.answer() ||
				"Share your memory cards anywhere! Map Moment generates cards optimized for Instagram, Twitter, Facebook, and other social platforms. Perfect for inspiring friends and family with your adventures.",
		},
	];

	return (
		<div className="space-y-6">
			<h2 className="text-2xl font-bold text-gray-900 dark:text-white text-center">
				{translations.seo?.faq?.title() ||
					"Frequently Asked Questions About Map Moment"}
			</h2>
			<div className="grid md:grid-cols-2 gap-6">
				{faqItems.map((item, index) => (
					<FaqItem
						key={index}
						question={item.question}
						answer={item.answer}
					/>
				))}
			</div>
		</div>
	);
}
