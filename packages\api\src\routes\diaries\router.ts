// 需要从 database 包导入 cuid 或类似库来生成 ID，或者让数据库生成
// 暂时使用简单方式，推荐后端生成更可靠的唯一ID
import { createId } from "@paralleldrive/cuid2";
import {} from "@repo/auth";
import { db } from "@repo/database/src/client";
import {
	createTravelDiary,
	deleteTravelDiary,
	getDiaryRichTextDraft,
	getPublicTravelDiaries,
	getTravelDiary,
	getUserFootprints, // 导入新的数据库函数
	getUserTravelDiaries,
	// 富文本草稿操作的函数导入
	saveDiaryRichTextDraft,
	updateTravelDiary,
} from "@repo/database/src/models/travel-diary";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { resolver, validator } from "hono-openapi/zod";
import { HTTPException } from "hono/http-exception";
import { EXPORT_AUTH_TOKEN, authMiddleware } from "../../middleware/auth";
import { userInfoMiddleware } from "../../middleware/user-info";
import { GeocodingService } from "../../services/geocoding-service";
import { queueVideoExportTask } from "../../services/queue-client";
import { RichTextAnalyzer } from "../../services/richtext-analyzer";
import { TravelAIAnalyzer } from "../../services/travel-ai-analyzer";
import { TravelDiaryGenerator } from "../../services/travel-diary-generator";
// 延迟导入视频导出服务，避免在构建时被包含
// import { exportVideoService } from "../../services/video-export";
import {
	type DiaryResponseOutput as DiaryResponse,
	type DiaryStats,
	type DiaryStatsResponse,
	type Footprint,
	type FootprintListResponse,
	type OperationResult,
	createDiarySchema,
	diaryIdParamSchema,
	diaryResponseSchema,
	diaryStatsResponseSchema,
	exportVideoTaskParamSchema,
	footprintListResponseSchema,
	operationResultSchema,
	updateDiarySchema,
} from "./schemas"; // 导入 schema 和类型

// 创建一个简化的响应模式用于列表接口
const diaryListResponseSchema = {
	diaries: {
		type: "array",
		items: {
			type: "object",
			properties: {
				id: { type: "string" },
				title: { type: "string" },
				subtitle: { type: "string", nullable: true },
				coverImage: { type: "string", nullable: true },
				isPublic: { type: "boolean" },
				createdAt: { type: "string" },
				updatedAt: { type: "string" },
			},
		},
	},
};

export const diariesRouter = new Hono()
	// 1. 获取当前用户的所有日记 (根路径)
	.get(
		"/diary",
		authMiddleware,
		describeRoute({
			tags: ["Diaries"],
			summary: "获取用户旅行日记列表",
			description: "获取当前用户的所有旅行日记",
			responses: {
				200: {
					description: "成功获取旅行日记列表",
					content: {
						"application/json": {
							schema: {
								type: "object",
								properties: {
									diaries: {
										type: "array",
										items: {
											type: "object",
										},
									},
								},
							},
						},
					},
				},
			},
		}),
		async (c) => {
			const user = c.get("user");

			try {
				const diaries = await getUserTravelDiaries(user.id);
				// 转换日期格式
				const formattedDiaries = diaries.map((diary) => ({
					...diary,
					createdAt: diary.createdAt.toISOString(),
					updatedAt: diary.updatedAt.toISOString(),
				}));
				return c.json({ diaries: formattedDiaries });
			} catch (error) {
				console.error("获取日记列表失败:", error);
				throw new HTTPException(500, { message: "获取日记列表失败" });
			}
		},
	)
	// 2. 创建新日记 (根路径 POST)
	.post(
		"/diary",
		authMiddleware,
		validator("json", createDiarySchema),
		async (c) => {
			const user = c.get("user");
			const data = c.req.valid("json");

			try {
				const diary = await createTravelDiary(user.id, data as any);
				// 转换日期格式以匹配前端期望
				const formattedDiary = {
					...diary,
					createdAt: diary.createdAt.toISOString(),
					updatedAt: diary.updatedAt.toISOString(),
				};
				return c.json({ diary: formattedDiary }, 201);
			} catch (error) {
				console.error("创建日记失败:", error);
				throw new HTTPException(500, { message: "创建日记失败" });
			}
		},
	)
	// 3. 获取公开日记列表
	.get(
		"/public",
		describeRoute({
			tags: ["Diaries"],
			summary: "获取公开旅行日记列表",
			description: "获取所有公开的旅行日记",
			responses: {
				200: {
					description: "成功获取公开旅行日记列表",
					content: {
						"application/json": {
							schema: {
								type: "object",
								properties: {
									diaries: {
										type: "array",
										items: {
											type: "object",
										},
									},
								},
							},
						},
					},
				},
			},
		}),
		async (c) => {
			try {
				const diaries = await getPublicTravelDiaries();
				// 转换日期格式
				const formattedDiaries = diaries.map((diary) => ({
					...diary,
					createdAt: diary.createdAt.toISOString(),
					updatedAt: diary.updatedAt.toISOString(),
				}));
				return c.json({ diaries: formattedDiaries });
			} catch (error) {
				console.error("获取公开日记列表失败:", error);
				throw new HTTPException(500, {
					message: "获取公开日记列表失败",
				});
			}
		},
	)
	// 4. 创建空日记路由 (具体路径优先)
	.post(
		"/create-empty",
		authMiddleware,
		describeRoute({
			tags: ["Diaries"],
			summary: "创建一个空的旅行日记草稿",
			description: "为当前用户创建一个包含默认内容的旅行日记",
			responses: {
				201: {
					description: "成功创建空日记",
					content: {
						"application/json": {
							schema: resolver(diaryResponseSchema),
						},
					},
				},
				500: {
					description: "服务器错误",
				},
			},
		}),
		async (c) => {
			const user = c.get("user");

			try {
				// 定义默认的空日记内容
				const defaultContent = {
					timelines: [
						{
							// 推荐使用 cuid() 或数据库默认值生成更可靠的 ID
							id: createId(),
							title: "第一天",
							// 获取当前日期 YYYY-MM-DD
							date: new Date().toISOString().split("T")[0],
							points: [],
						},
					],
				};

				const defaultData = {
					title: "未命名日记",
					subtitle: "",
					coverImage: null,
					content: defaultContent,
					isPublic: false,
				};

				const diary = await createTravelDiary(user.id, defaultData);
				// 转换日期格式以匹配schema期望
				const formattedDiary = {
					...diary,
					createdAt: diary.createdAt.toISOString(),
					updatedAt: diary.updatedAt.toISOString(),
				};
				const response: DiaryResponse = {
					diary: formattedDiary as any,
				};
				return c.json(response, 201);
			} catch (error) {
				console.error("创建空日记失败:", error);
				throw new HTTPException(500, { message: "创建空日记失败" });
			}
		},
	)
	// 5. 获取旅行统计信息 (具体路径优先)
	.get(
		"/stats",
		authMiddleware,
		describeRoute({
			tags: ["Diaries"],
			summary: "获取用户旅行统计信息",
			description: "计算并返回用户的旅行统计数据",
			responses: {
				200: {
					description: "成功获取统计信息",
					content: {
						"application/json": {
							schema: resolver(diaryStatsResponseSchema),
						},
					},
				},
				500: {
					description: "服务器错误",
				},
			},
		}),
		async (c) => {
			const user = c.get("user");

			try {
				const footprintsData = await getUserFootprints(user.id);

				const countries = new Set<string>();
				const cities = new Set<string>();
				let photoCount = 0;

				footprintsData.forEach((fp) => {
					if (fp.country) {
						countries.add(fp.country);
					}
					if (fp.city) {
						cities.add(fp.city);
					}
					photoCount += fp.images?.length || 0;
				});

				const stats: DiaryStats = {
					countriesVisited: countries.size,
					citiesExplored: cities.size,
					footprintsLeft: footprintsData.length,
					photosTaken: photoCount,
				};

				const response: DiaryStatsResponse = { stats };
				return c.json(response);
			} catch (error) {
				console.error("获取统计信息失败:", error);
				return c.json({ error: "获取统计信息失败" }, 500);
			}
		},
	)
	// 6. 获取所有足迹点 (具体路径优先)
	.get(
		"/footprints",
		authMiddleware,
		describeRoute({
			tags: ["Diaries"],
			summary: "获取用户所有足迹点（用于地图）",
			description: "返回用户所有旅行足迹点的地理位置信息",
			responses: {
				200: {
					description: "成功获取足迹点列表",
					content: {
						"application/json": {
							schema: resolver(footprintListResponseSchema),
						},
					},
				},
				500: {
					description: "服务器错误",
				},
			},
		}),
		async (c) => {
			const user = c.get("user");

			try {
				const footprintsData = await getUserFootprints(user.id);

				// 获取所有涉及的日记ID
				const diaryIds = [
					...Array.from(
						new Set(footprintsData.map((fp) => fp.diaryId)),
					),
				];

				// 获取日记标题信息
				const diaries = await db.travelDiary.findMany({
					where: {
						id: { in: diaryIds },
					},
					select: {
						id: true,
						title: true,
					},
				});

				// 创建日记ID到标题的映射
				const diaryTitles = Object.fromEntries(
					diaries.map((diary) => [diary.id, diary.title]),
				);

				// 转换数据以匹配 Footprint schema
				const footprints: Footprint[] = footprintsData.map((fp) => ({
					id: fp.id,
					latitude: fp.latitude,
					longitude: fp.longitude,
					location: fp.location,
					diaryId: fp.diaryId,
					date: fp.date,
					country: fp.country,
					city: fp.city,
					images: fp.images,
					description: fp.description || undefined,
					iconType: fp.iconType || undefined,
					timelineId: fp.timelineId || undefined,
					timelineTitle: fp.timelineTitle || undefined,
					// 添加日记标题
					diaryTitle: diaryTitles[fp.diaryId] || "未命名日记",
				}));
				const response: FootprintListResponse = { footprints };
				return c.json(response);
			} catch (error) {
				console.error("获取足迹点失败:", error);
				return c.json({ error: "获取足迹点失败" }, 500);
			}
		},
	)
	// 7. 获取单个日记 (带参数的路径放在后面)
	.get(
		"/diary/:id",
		userInfoMiddleware,
		validator("param", diaryIdParamSchema),
		describeRoute({
			tags: ["Diaries"],
			summary: "获取单个旅行日记",
			description: "根据ID获取旅行日记详情",
			parameters: [
				{
					name: "id",
					in: "path",
					required: true,
					schema: {
						type: "string",
					},
				},
			],
			responses: {
				200: {
					description: "成功获取日记详情",
					content: {
						"application/json": {
							schema: resolver(diaryResponseSchema),
						},
					},
				},
				404: {
					description: "日记不存在",
				},
			},
		}),
		async (c) => {
			const { id } = c.req.valid("param");
			const user = c.get("user");

			// 检查是否是视频导出请求
			const isExportMode = c.req.query("export") === "video";
			const exportToken = c.req.query("exportToken");
			const isValidExport =
				isExportMode && exportToken === EXPORT_AUTH_TOKEN;

			try {
				// 传递导出模式标识给数据库函数
				const diary = await getTravelDiary(id, user?.id, isValidExport);

				if (isValidExport) {
					logger.info(`视频导出模式：获取日记 ${id} - 绕过权限检查`, {
						path: c.req.path,
						query: c.req.query(),
						diaryId: id,
					});
				} else {
					logger.info(
						`常规模式：获取日记 ${id} - 用户: ${user?.id || "未认证"}`,
					);
				}

				if (!diary) {
					throw new HTTPException(404, { message: "日记不存在" });
				}
				// 转换日期格式
				const formattedDiary = {
					...diary,
					createdAt: diary.createdAt.toISOString(),
					updatedAt: diary.updatedAt.toISOString(),
				};
				return c.json({ diary: formattedDiary });
			} catch (error) {
				if (error instanceof HTTPException) {
					throw error; // Re-throw HTTPException
				}
				console.error("获取日记详情失败:", error);
				throw new HTTPException(500, { message: "获取日记详情失败" });
			}
		},
	)
	// 8. 更新日记 (带参数的路径放在后面)
	.put(
		"/diary/:id",
		authMiddleware,
		validator("param", diaryIdParamSchema),
		validator("json", updateDiarySchema),
		describeRoute({
			tags: ["Diaries"],
			summary: "更新旅行日记",
			description: "更新现有的旅行日记",
			parameters: [
				{
					name: "id",
					in: "path",
					required: true,
					schema: {
						type: "string",
					},
				},
			],
			requestBody: {
				content: {
					"application/json": {
						schema: resolver(updateDiarySchema) as any,
					},
				},
			},
			responses: {
				200: {
					description: "成功更新日记",
					content: {
						"application/json": {
							schema: resolver(operationResultSchema),
						},
					},
				},
				404: {
					description: "日记不存在",
				},
			},
		}),
		async (c) => {
			const { id } = c.req.valid("param");
			const data = c.req.valid("json");
			const user = c.get("user");

			try {
				const result = await updateTravelDiary(
					id,
					user.id,
					data as any,
				);
				if (result.count === 0) {
					throw new HTTPException(404, {
						message: "日记不存在或无权更新",
					});
				}
				const response: OperationResult = { success: true };
				return c.json(response);
			} catch (error) {
				if (error instanceof HTTPException) {
					throw error;
				}
				console.error("更新日记失败:", error);
				throw new HTTPException(500, { message: "更新日记失败" });
			}
		},
	)
	// 9. 删除日记 (带参数的路径放在后面)
	.delete(
		"/diary/:id",
		authMiddleware,
		validator("param", diaryIdParamSchema),
		describeRoute({
			tags: ["Diaries"],
			summary: "删除旅行日记",
			description: "删除一篇旅行日记",
			parameters: [
				{
					name: "id",
					in: "path",
					required: true,
					schema: {
						type: "string",
					},
				},
			],
			responses: {
				200: {
					description: "成功删除日记",
					content: {
						"application/json": {
							schema: resolver(operationResultSchema),
						},
					},
				},
				404: {
					description: "日记不存在",
				},
			},
		}),
		async (c) => {
			const { id } = c.req.valid("param");
			const user = c.get("user");

			try {
				const result = await deleteTravelDiary(id, user.id);
				if (result.count === 0) {
					throw new HTTPException(404, {
						message: "日记不存在或无权删除",
					});
				}
				const response: OperationResult = { success: true };
				return c.json(response);
			} catch (error) {
				if (error instanceof HTTPException) {
					throw error;
				}
				console.error("删除日记失败:", error);
				throw new HTTPException(500, { message: "删除日记失败" });
			}
		},
	)
	// 10. 导出日记为视频 (新添加的端点)
	.post(
		"/diary/:id/export-video",
		authMiddleware,
		validator("param", diaryIdParamSchema),
		async (c) => {
			const { id } = c.req.valid("param");
			const user = c.get("user");
			const requestTime = new Date().toISOString();

			// 记录请求开始
			logger.info(`视频导出任务创建请求开始 [${requestTime}]`, {
				diaryId: id,
				userId: user.id,
				timestamp: requestTime,
				path: c.req.path,
			});

			try {
				const {
					resolution = "720p",
					fps = 30,
					pointDuration = 5,
				} = await c.req.json();

				// 记录导出参数
				logger.info(`视频导出参数 [${requestTime}]`, {
					diaryId: id,
					userId: user.id,
					params: { resolution, fps, pointDuration },
					timestamp: new Date().toISOString(),
				});

				// 先检查日记是否存在
				const diary = await getTravelDiary(id, user.id);
				if (!diary) {
					logger.warn(`视频导出失败：日记不存在 [${requestTime}]`, {
						diaryId: id,
						userId: user.id,
						timestamp: new Date().toISOString(),
					});
					throw new HTTPException(404, { message: "日记不存在" });
				}

				// 生成任务ID
				const taskId = createId();

				// 创建任务记录
				await db.videoExportTask.create({
					data: {
						id: taskId,
						diaryId: id,
						userId: user.id,
						status: "pending",
						options: {
							resolution,
							fps,
							pointDuration,
						},
						progress: 0,
					},
				});

				// 记录任务创建成功
				logger.info(`视频导出任务创建成功 [${requestTime}]`, {
					diaryId: id,
					userId: user.id,
					taskId,
					params: { resolution, fps, pointDuration },
					timestamp: new Date().toISOString(),
				});

				// 添加到队列
				await queueVideoExportTask({
					taskId,
					diaryId: id,
					userId: user.id,
					options: {
						resolution,
						fps,
						pointDuration,
					},
				});

				// 记录任务加入队列
				logger.info(`视频导出任务已加入队列 [${requestTime}]`, {
					diaryId: id,
					userId: user.id,
					taskId,
					timestamp: new Date().toISOString(),
				});

				// 返回任务ID，客户端可以用它来查询进度
				return c.json(
					{
						taskId,
						message: "视频导出任务已创建，请稍候",
						requestTime,
						serverTime: new Date().toISOString(),
					},
					202,
				);
			} catch (error) {
				if (error instanceof HTTPException) {
					throw error;
				}
				const errorTime = new Date().toISOString();
				logger.error(
					`导出视频请求处理失败 [${requestTime}] -> [${errorTime}]: ${error}`,
					{
						diaryId: id,
						userId: user.id,
						error: String(error),
						timestamp: errorTime,
						duration:
							new Date().getTime() -
							new Date(requestTime).getTime(),
					},
				);
				throw new HTTPException(500, { message: "处理导出请求失败" });
			}
		},
	)
	.get(
		"/diary/:id/export-video/:taskId",
		authMiddleware,
		validator("param", exportVideoTaskParamSchema),
		async (c) => {
			const { id, taskId } = c.req.valid("param");
			const user = c.get("user");
			const requestTime = new Date().toISOString();

			// 记录请求开始
			logger.info(`视频导出任务状态查询开始 [${requestTime}]`, {
				diaryId: id,
				taskId,
				userId: user.id,
				timestamp: requestTime,
				path: c.req.path,
				query: c.req.query(),
			});

			try {
				const task = await db.videoExportTask.findFirst({
					where: {
						id: taskId,
						diaryId: id,
						userId: user.id,
					},
				});

				if (!task) {
					logger.warn(`视频导出任务不存在 [${requestTime}]`, {
						diaryId: id,
						taskId,
						userId: user.id,
						timestamp: new Date().toISOString(),
					});
					throw new HTTPException(404, { message: "任务不存在" });
				}

				// 记录任务状态
				logger.info(`视频导出任务状态查询成功 [${requestTime}]`, {
					diaryId: id,
					taskId,
					userId: user.id,
					taskStatus: task.status,
					taskProgress: task.progress,
					createdAt: task.createdAt.toISOString(),
					updatedAt: task.updatedAt.toISOString(),
					hasVideo: !!task.videoUrl,
					hasError: !!task.errorMessage,
					timestamp: new Date().toISOString(),
				});

				return c.json({
					taskId: task.id,
					status: task.status,
					progress: task.progress,
					videoUrl: task.videoUrl,
					errorMessage: task.errorMessage,
					requestTime,
					serverTime: new Date().toISOString(),
				});
			} catch (error) {
				if (error instanceof HTTPException) {
					throw error;
				}
				const errorTime = new Date().toISOString();
				logger.error(
					`获取视频导出状态失败 [${requestTime}] -> [${errorTime}]: ${error}`,
					{
						diaryId: id,
						taskId,
						userId: user.id,
						timestamp: errorTime,
						error: String(error),
						duration:
							new Date().getTime() -
							new Date(requestTime).getTime(),
					},
				);
				throw new HTTPException(500, { message: "获取任务状态失败" });
			}
		},
	)
	// 添加新路由: 保存富文本草稿
	.put(
		"/diary/:id/richtext-draft",
		authMiddleware,
		validator("param", diaryIdParamSchema),
		async (c) => {
			const { id } = c.req.valid("param");
			const user = c.get("user");

			try {
				const draftContent = await c.req.json();

				// 保存富文本草稿内容到数据库
				await saveDiaryRichTextDraft(id, user.id, draftContent);

				return c.json({ success: true });
			} catch (error) {
				console.error("保存富文本草稿失败:", error);
				throw new HTTPException(500, { message: "保存富文本草稿失败" });
			}
		},
	)

	// 添加新路由: 获取富文本草稿
	.get(
		"/diary/:id/richtext-draft",
		authMiddleware,
		validator("param", diaryIdParamSchema),
		async (c) => {
			const { id } = c.req.valid("param");
			const user = c.get("user");

			try {
				// 从数据库获取富文本草稿内容
				const draftContent = await getDiaryRichTextDraft(id, user.id);

				if (!draftContent) {
					return c.json({ content: null });
				}

				return c.json({ content: draftContent });
			} catch (error) {
				console.error("获取富文本草稿失败:", error);
				throw new HTTPException(500, { message: "获取富文本草稿失败" });
			}
		},
	)

	// 添加新路由: 将富文本内容格式化为结构化数据
	.post("/format-richtext", userInfoMiddleware, async (c) => {
		try {
			const { content } = await c.req.json();

			if (!content) {
				return c.json(
					{
						success: false,
						error: "缺少富文本内容",
					},
					400,
				);
			}

			logger.info("开始处理富文本格式化请求", {
				contentType: typeof content,
				hasContent: !!content,
			});

			// 1. 分析富文本内容
			const analysisResult = RichTextAnalyzer.analyze(content);
			logger.info("富文本分析完成", analysisResult);

			if (analysisResult.textContent.length === 0) {
				return c.json(
					{
						success: false,
						error: "富文本内容为空",
					},
					400,
				);
			}

			// 2. 创建地理编码服务 - 不传参数，让服务自动从环境变量创建配置
			if (!process.env.GOOGLE_MAPS_API_KEY) {
				logger.warn("未配置Google Maps API密钥，跳过地理编码");
				return c.json(
					{
						success: false,
						error: "地理编码服务未配置",
					},
					500,
				);
			}

			// 不传任何参数，让GeocodingService从环境变量自动创建配置
			const geocodingService = new GeocodingService();

			// 3. AI分析旅行信息
			let aiAnalyzer: TravelAIAnalyzer;

			// 尝试使用火山引擎，失败时使用OpenAI
			if (
				process.env.VOLCENGINE_API_KEY &&
				process.env.VOLCENGINE_SECRET_KEY
			) {
				try {
					aiAnalyzer = new TravelAIAnalyzer({
						provider: "volcengine",
						apiKey: process.env.VOLCENGINE_API_KEY,
						secretKey: process.env.VOLCENGINE_SECRET_KEY,
					});
					logger.info("使用火山引擎AI服务进行分析");
				} catch (error) {
					logger.warn("火山引擎AI初始化失败，回退到OpenAI", {
						error,
					});
					aiAnalyzer = new TravelAIAnalyzer({
						provider: "openai",
						apiKey: process.env.OPENAI_API_KEY,
					});
				}
			} else if (process.env.OPENAI_API_KEY) {
				aiAnalyzer = new TravelAIAnalyzer({
					provider: "openai",
					apiKey: process.env.OPENAI_API_KEY,
				});
				logger.info("使用OpenAI服务进行分析");
			} else {
				logger.warn("未配置AI服务密钥，跳过AI分析");
				return c.json(
					{
						success: false,
						error: "AI分析服务未配置",
					},
					500,
				);
			}

			const travelInfo = await aiAnalyzer.analyzeText(
				analysisResult.textContent,
			);

			logger.info("AI分析完成", {
				locationsCount: travelInfo.locations.length,
				timesCount: travelInfo.timeInfo.length,
				activitiesCount: travelInfo.activities.length,
			});

			// 4. 生成旅行日记数据
			const diaryData =
				TravelDiaryGenerator.generateFromTravelInfo(travelInfo);

			logger.info("旅行日记生成完成", {
				timelinesCount: diaryData.timelines.length,
				pointsCount: diaryData.timelines.reduce(
					(sum: number, timeline: any) =>
						sum + timeline.points.length,
					0,
				),
			});

			// 5. 返回结果
			return c.json({
				success: true,
				data: {
					diary: diaryData,
					analysis: {
						richTextAnalysis: analysisResult,
						travelInfo: travelInfo,
					},
				},
			});
		} catch (error) {
			logger.error("富文本格式化失败", {
				error: error instanceof Error ? error.message : String(error),
				stack: error instanceof Error ? error.stack : undefined,
			});

			return c.json(
				{
					success: false,
					error: "富文本格式化失败",
					details:
						error instanceof Error ? error.message : String(error),
				},
				500,
			);
		}
	});
