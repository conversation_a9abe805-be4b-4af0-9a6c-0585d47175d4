"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import {
	<PERSON>alog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@ui/components/dialog";
import { Loader2, Video } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

interface ExportVideoButtonProps {
	diaryId: string;
}

export function ExportVideoButton({ diaryId }: ExportVideoButtonProps) {
	const t = useTranslations("travelMemo.exportVideo");
	const [isExporting, setIsExporting] = useState(false);
	const [showDialog, setShowDialog] = useState(false);
	const [exportStatus, setExportStatus] = useState<
		"idle" | "exporting" | "success" | "error" | "completed"
	>("idle");
	const [taskId, setTaskId] = useState<string | null>(null);
	const [errorMessage, setErrorMessage] = useState<string | null>(null);
	const [progress, setProgress] = useState(0);
	const [videoUrl, setVideoUrl] = useState<string | null>(null);

	// 处理导出请求
	const handleExport = async () => {
		if (isExporting) return;

		try {
			setIsExporting(true);
			setExportStatus("exporting");
			setShowDialog(true);

			// 调用API导出视频
			const response = await fetch(
				`/api/diaries/diary/${diaryId}/export-video`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						resolution: "720p",
						fps: 30,
						pointDuration: 5,
					}),
				},
			);

			if (!response.ok) {
				throw new Error(
					t("errorRequestFailed", { status: response.status }),
				);
			}

			const data = await response.json();
			setTaskId(data.taskId);
			setExportStatus("success");
		} catch (error) {
			console.error("导出视频失败:", error);
			setExportStatus("error");
			setErrorMessage(
				error instanceof Error
					? error.message
					: t("dialogDescUnknownError"),
			);
		} finally {
			setIsExporting(false);
		}
	};

	// 关闭对话框
	const closeDialog = () => {
		setShowDialog(false);

		// 重置状态
		if (exportStatus === "success" || exportStatus === "error") {
			setTimeout(() => {
				setExportStatus("idle");
				setTaskId(null);
				setErrorMessage(null);
			}, 300);
		}
	};

	useEffect(() => {
		if (taskId && exportStatus === "success") {
			const interval = setInterval(async () => {
				try {
					const response = await fetch(
						`/api/diaries/diary/${diaryId}/export-video/${taskId}`,
					);

					if (!response.ok)
						throw new Error(t("errorGetStatusFailed"));

					const data = await response.json();
					setProgress(data.progress);

					if (data.status === "completed") {
						setExportStatus("completed");
						setVideoUrl(data.videoUrl);
						clearInterval(interval);
					} else if (data.status === "failed") {
						setExportStatus("error");
						setErrorMessage(
							data.errorMessage || t("errorExportFailedMessage"),
						);
						clearInterval(interval);
					}
				} catch (error) {
					console.error("获取任务状态失败:", error);
				}
			}, 3000); // 每3秒轮询一次

			return () => clearInterval(interval);
		}
	}, [taskId, exportStatus, diaryId, t]);

	return (
		<>
			<Button
				onClick={handleExport}
				disabled={isExporting}
				className="rounded-full px-8 py-6 bg-white/10 hover:bg-white/20 text-white border border-white/20 backdrop-blur-sm transform transition-all duration-300 hover:scale-105 hover:shadow-lg travel-memo-button"
			>
				<Video className="w-5 h-5 mr-2" />
				{t("buttonText")}
			</Button>

			<Dialog open={showDialog} onOpenChange={setShowDialog}>
				<DialogContent className="sm:max-w-md">
					<DialogHeader>
						<DialogTitle>
							{exportStatus === "exporting" &&
								t("dialogTitleExporting")}
							{exportStatus === "success" &&
								t("dialogTitleSubmitted")}
							{exportStatus === "error" && t("dialogTitleError")}
						</DialogTitle>
						<DialogDescription>
							{exportStatus === "exporting" && (
								<div className="flex items-center mt-4">
									<Loader2 className="h-6 w-6 animate-spin mr-2" />
									<span>{t("dialogDescPreparing")}</span>
								</div>
							)}

							{exportStatus === "success" && (
								<div className="mt-4">
									<p>{t("dialogDescSubmittedMsg1")}</p>
									<p className="mt-2">
										{t("dialogDescTaskId", { taskId })}
									</p>
									<p className="mt-4 text-sm text-muted-foreground">
										{t("dialogDescSubmittedMsg2")}
									</p>
								</div>
							)}

							{exportStatus === "error" && (
								<div className="mt-4 text-destructive">
									<p>{t("dialogDescErrorPrefix")}</p>
									<p className="mt-2 text-sm">
										{errorMessage ||
											t("dialogDescUnknownError")}
									</p>
								</div>
							)}
						</DialogDescription>
					</DialogHeader>

					<DialogFooter className="mt-6">
						<Button onClick={closeDialog} variant="outline">
							{exportStatus === "exporting"
								? t("footerButtonCancel")
								: t("footerButtonClose")}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
}
