# 旅行地图故事

这个模块提供了一个精美的旅行地图故事展示功能，可以将用户的旅行日记转换为具有动画效果的交互式地图故事。

## 功能特点

1. **封面页**：美观的封面页显示日记标题、副标题和基本统计信息。
2. **交互式地图**：基于Mapbox的全屏地图，自动播放所有点位，包含优雅的过渡动画和路线连接。
3. **回顾总结页**：所有照片以照片墙形式展示，背景为旅行路线的静态地图，显示旅行统计数据。

## 如何使用

1. 添加Mapbox Token到环境变量：
   ```
   # .env.local
   NEXT_PUBLIC_MAPBOX_TOKEN=pk.your_mapbox_access_token_here
   ```

2. 在日记页面添加查看故事按钮：
   ```tsx
   import { ViewStoryButton } from "../mapbox/components/ViewStoryButton";
   
   // 在你的日记详情页面
   <ViewStoryButton diaryId={diary.id} />
   ```

3. 访问地图故事列表页面：
   ```
   /app/mapbox
   ```

4. 访问特定日记的地图故事：
   ```
   /app/mapbox/{diaryId}
   ```

## 技术实现

- 地图使用Mapbox GL JS实现，支持完整的3D地图体验。
- 使用CSS Snap滚动实现页面平滑切换。
- 自动计算最佳缩放级别和视角。
- 照片墙使用CSS Grid和随机高宽比实现视觉美感。
- 路线动画使用自定义虚线动画增强视觉效果。

## 依赖安装

需要安装以下npm包:

```bash
npm install mapbox-gl react-map-gl
npm install --save-dev @types/mapbox-gl
```

## 故障排除

### Failed to load Mapbox GL: Cannot set property accessToken

如果您遇到以下错误:
```
Failed to load Mapbox GL: TypeError: Cannot set property accessToken of [object Module] which has only a getter
```

这是因为在动态导入模块时，无法正确设置accessToken属性。解决方法是:

1. 确保在组件外部全局设置accessToken:
   ```typescript
   import mapboxgl from 'mapbox-gl';
   import 'mapbox-gl/dist/mapbox-gl.css';
   
   // 在组件外部设置
   mapboxgl.accessToken = process.env.NEXT_PUBLIC_MAPBOX_TOKEN || 'YOUR_MAPBOX_TOKEN';
   ```

2. 确保已创建.env.local文件并设置了有效的Mapbox访问令牌:
   ```
   NEXT_PUBLIC_MAPBOX_TOKEN=pk.your_actual_mapbox_token
   ```

### 浏览器兼容性问题

如果地图无法加载或在某些浏览器中出现兼容性问题:

1. 确保您使用的浏览器支持WebGL
2. 使用更新版本的浏览器
3. 考虑添加polyfill或备用内容 