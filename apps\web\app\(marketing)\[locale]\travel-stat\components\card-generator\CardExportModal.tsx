"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { Camera, Download, Loader2, X } from "lucide-react";
import React, { useState, useCallback, useRef, useEffect } from "react";
import type { CountryData, TravelPoint } from "../../types";
import type { ColorThemeType } from "../../types/colorTypes";

interface CardExportModalProps {
	isOpen: boolean;
	onClose: () => void;
	mapRef: React.RefObject<any>;
	travelPoints: TravelPoint[];
	visitedCountries: CountryData[];
	mapLoaded: boolean;
	currentTheme: ColorThemeType;
}

export function CardExportModal({
	isOpen,
	onClose,
	mapRef,
	travelPoints,
	visitedCountries,
	mapLoaded,
	currentTheme,
}: CardExportModalProps) {
	const [isCapturing, setIsCapturing] = useState(false);
	const [capturedImage, setCapturedImage] = useState<string | null>(null);
	const modalRef = useRef<HTMLDivElement>(null);

	// 处理ESC键关闭
	useEffect(() => {
		const handleEscape = (e: KeyboardEvent) => {
			if (e.key === "Escape") {
				onClose();
			}
		};

		if (isOpen) {
			document.addEventListener("keydown", handleEscape);
			document.body.style.overflow = "hidden";
		}

		return () => {
			document.removeEventListener("keydown", handleEscape);
			document.body.style.overflow = "unset";
		};
	}, [isOpen, onClose]);

	// 处理点击外部关闭
	const handleBackdropClick = useCallback(
		(e: React.MouseEvent) => {
			if (e.target === modalRef.current) {
				onClose();
			}
		},
		[onClose],
	);

	// 捕获地图图片
	const captureMap = useCallback(async () => {
		if (!mapRef?.current || !mapLoaded) {
			alert("地图未加载完成");
			return;
		}

		setIsCapturing(true);

		try {
			// 等待地图稳定
			await new Promise((resolve) => setTimeout(resolve, 500));

			// 获取地图实例
			const map = mapRef.current.getMap
				? mapRef.current.getMap()
				: mapRef.current;

			if (!map || !map.getCanvas) {
				throw new Error("无法获取地图实例");
			}

			// 获取地图canvas
			const canvas = map.getCanvas();

			if (!canvas) {
				throw new Error("无法获取地图画布");
			}

			// 创建地图图片
			const dataURL = canvas.toDataURL("image/png", 1.0);
			setCapturedImage(dataURL);
		} catch (error) {
			console.error("地图捕获失败:", error);
			alert(
				`地图捕获失败: ${error instanceof Error ? error.message : "未知错误"}`,
			);
		} finally {
			setIsCapturing(false);
		}
	}, [mapRef, mapLoaded]);

	// 下载图片
	const downloadImage = useCallback(() => {
		if (!capturedImage) {
			return;
		}

		const link = document.createElement("a");
		link.download = `travel-footprint-${new Date().toISOString().split("T")[0]}.png`;
		link.href = capturedImage;
		link.click();
	}, [capturedImage]);

	if (!isOpen) {
		return null;
	}

	return (
		<div
			ref={modalRef}
			className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4"
			onClick={handleBackdropClick}
		>
			<Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-white">
				<div className="p-6">
					{/* 头部 */}
					<div className="flex items-center justify-between mb-6">
						<h2 className="text-2xl font-bold text-gray-900">
							导出旅行足迹卡片
						</h2>
						<Button variant="ghost" size="sm" onClick={onClose}>
							<X className="w-4 h-4" />
						</Button>
					</div>

					{/* 统计信息 */}
					<div className="mb-6 p-4 bg-gray-50 rounded-lg">
						<h3 className="font-semibold mb-2">您的旅行足迹</h3>
						<div className="grid grid-cols-3 gap-4 text-center">
							<div>
								<div className="text-2xl font-bold text-blue-600">
									{travelPoints.length}
								</div>
								<div className="text-sm text-gray-600">
									旅行点位
								</div>
							</div>
							<div>
								<div className="text-2xl font-bold text-green-600">
									{visitedCountries.length}
								</div>
								<div className="text-sm text-gray-600">
									访问国家
								</div>
							</div>
							<div>
								<div className="text-2xl font-bold text-purple-600">
									{visitedCountries.reduce(
										(sum, country) =>
											sum + country.visitCount,
										0,
									)}
								</div>
								<div className="text-sm text-gray-600">
									总访问次数
								</div>
							</div>
						</div>
					</div>

					{/* 地图捕获 */}
					<div className="mb-6">
						<h3 className="font-semibold mb-3">捕获地图</h3>
						<Button
							onClick={captureMap}
							disabled={isCapturing || !mapLoaded}
							className="w-full"
						>
							{isCapturing ? (
								<>
									<Loader2 className="w-4 h-4 mr-2 animate-spin" />
									正在捕获地图...
								</>
							) : (
								<>
									<Camera className="w-4 h-4 mr-2" />
									捕获当前地图视图
								</>
							)}
						</Button>
					</div>

					{/* 图片预览 */}
					{capturedImage && (
						<div className="mb-6">
							<h3 className="font-semibold mb-3">预览</h3>
							<div className="border rounded-lg overflow-hidden">
								<img
									src={capturedImage}
									alt="地图截图"
									className="w-full h-auto"
								/>
							</div>
						</div>
					)}

					{/* 导出操作 */}
					<div className="flex gap-3">
						<Button
							variant="outline"
							onClick={onClose}
							className="flex-1"
						>
							取消
						</Button>
						<Button
							onClick={downloadImage}
							disabled={!capturedImage}
							className="flex-1"
						>
							<Download className="w-4 h-4 mr-2" />
							下载图片
						</Button>
					</div>
				</div>
			</Card>
		</div>
	);
}
