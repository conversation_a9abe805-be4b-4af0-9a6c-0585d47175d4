/**
 * 根据 Mapbox Search Box 的地点类型自动选择合适的图标类型
 * @param mapboxData Mapbox Search Box 返回的地点数据
 * @returns 对应的图标类型
 */
export function getIconTypeFromMapboxData(mapboxData?: {
	poi_category?: string[];
	poi_category_ids?: string[];
	feature_type?: string;
	maki?: string;
	brand?: string[];
}): string {
	if (!mapboxData) return "default";

	// 优先使用 poi_category 进行映射
	if (mapboxData.poi_category && mapboxData.poi_category.length > 0) {
		const category = mapboxData.poi_category[0].toLowerCase();

		// 酒店住宿类
		if (
			category.includes("lodging") ||
			category.includes("hotel") ||
			category.includes("accommodation")
		) {
			return "hotel";
		}

		// 餐饮类
		if (
			category.includes("food") ||
			category.includes("restaurant") ||
			category.includes("cafe") ||
			category.includes("bar") ||
			category.includes("fast_food") ||
			category.includes("dining")
		) {
			return "food";
		}

		// 景点地标类
		if (
			category.includes("landmark") ||
			category.includes("tourist") ||
			category.includes("attraction") ||
			category.includes("museum") ||
			category.includes("monument") ||
			category.includes("castle") ||
			category.includes("historic") ||
			category.includes("viewpoint")
		) {
			return "landmark";
		}

		// 海滩类
		if (
			category.includes("beach") ||
			category.includes("waterfront") ||
			category.includes("seaside")
		) {
			return "beach";
		}

		// 山地类
		if (
			category.includes("mountain") ||
			category.includes("peak") ||
			category.includes("hill") ||
			category.includes("volcano") ||
			category.includes("skiing")
		) {
			return "mountain";
		}

		// 博物馆类
		if (
			category.includes("museum") ||
			category.includes("gallery") ||
			category.includes("exhibition")
		) {
			return "museum";
		}

		// 公园类
		if (
			category.includes("park") ||
			category.includes("garden") ||
			category.includes("recreation") ||
			category.includes("nature") ||
			category.includes("outdoor")
		) {
			return "park";
		}

		// 购物类
		if (
			category.includes("shopping") ||
			category.includes("retail") ||
			category.includes("store") ||
			category.includes("mall") ||
			category.includes("market")
		) {
			return "shopping";
		}

		// 咖啡类
		if (category.includes("coffee") || category.includes("cafe")) {
			return "cafe";
		}

		// 活动娱乐类
		if (
			category.includes("entertainment") ||
			category.includes("theater") ||
			category.includes("cinema") ||
			category.includes("club") ||
			category.includes("sports") ||
			category.includes("activity")
		) {
			return "activity";
		}

		// 交通类
		if (
			category.includes("transport") ||
			category.includes("station") ||
			category.includes("railway") ||
			category.includes("bus") ||
			category.includes("metro") ||
			category.includes("ferry")
		) {
			return "transport";
		}

		// 机场类
		if (
			category.includes("airport") ||
			category.includes("aviation") ||
			category.includes("flight")
		) {
			return "airport";
		}
	}

	// 使用 maki 图标进行映射
	if (mapboxData.maki) {
		const maki = mapboxData.maki.toLowerCase();

		// 根据 Mapbox Maki 图标映射
		if (maki.includes("lodging") || maki.includes("hotel")) return "hotel";
		if (
			maki.includes("restaurant") ||
			maki.includes("food") ||
			maki.includes("fast-food")
		)
			return "food";
		if (
			maki.includes("monument") ||
			maki.includes("landmark") ||
			maki.includes("attraction")
		)
			return "landmark";
		if (maki.includes("beach")) return "beach";
		if (maki.includes("mountain") || maki.includes("volcano"))
			return "mountain";
		if (maki.includes("museum") || maki.includes("art-gallery"))
			return "museum";
		if (maki.includes("park") || maki.includes("garden")) return "park";
		if (
			maki.includes("shop") ||
			maki.includes("clothing-store") ||
			maki.includes("grocery")
		)
			return "shopping";
		if (maki.includes("cafe") || maki.includes("coffee")) return "cafe";
		if (
			maki.includes("theater") ||
			maki.includes("cinema") ||
			maki.includes("stadium")
		)
			return "activity";
		if (
			maki.includes("rail") ||
			maki.includes("bus") ||
			maki.includes("ferry")
		)
			return "transport";
		if (maki.includes("airport") || maki.includes("airfield"))
			return "airport";
	}

	// 使用 feature_type 进行基础映射
	if (mapboxData.feature_type) {
		const featureType = mapboxData.feature_type.toLowerCase();

		if (featureType === "poi") return "landmark"; // POI 默认为地标
		if (featureType === "address") return "default";
		if (featureType === "place") return "landmark";
	}

	// 默认图标
	return "default";
}
