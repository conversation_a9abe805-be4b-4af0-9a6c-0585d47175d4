"use client";

import { useEffect, useState } from "react";

export function MemoriesGallery() {
	const [visibleItems, setVisibleItems] = useState<number[]>([]);

	const memories = [
		{
			type: "photo",
			title: "美丽的回忆",
			description: "上传照片，让每一帧都成为永恒",
			color: "from-rose-400 to-pink-500",
			icon: "📸",
		},
		{
			type: "text",
			title: "心情记录",
			description: "用文字记录内心的感动和思考",
			color: "from-blue-400 to-indigo-500",
			icon: "✍️",
		},
		{
			type: "location",
			title: "地标打卡",
			description: "精确记录每一个值得纪念的地方",
			color: "from-emerald-400 to-teal-500",
			icon: "📍",
		},
		{
			type: "route",
			title: "足迹路线",
			description: "连接每个地点，绘制专属旅行地图",
			color: "from-purple-400 to-violet-500",
			icon: "🗺️",
		},
		{
			type: "weather",
			title: "天气记录",
			description: "记录当时的天气，让回忆更加生动",
			color: "from-amber-400 to-orange-500",
			icon: "🌤️",
		},
		{
			type: "share",
			title: "分享故事",
			description: "与朋友分享旅行的精彩时刻",
			color: "from-cyan-400 to-blue-500",
			icon: "💫",
		},
	];

	useEffect(() => {
		const observer = new IntersectionObserver(
			(entries) => {
				entries.forEach((entry) => {
					if (entry.isIntersecting) {
						const itemIndex = Number(
							entry.target.getAttribute("data-item"),
						);
						setVisibleItems((prev) =>
							Array.from(new Set([...prev, itemIndex])),
						);
					}
				});
			},
			{ threshold: 0.3 },
		);

		// 延迟观察器的初始化，确保DOM已渲染
		const timer = setTimeout(() => {
			const items = document.querySelectorAll("[data-item]");
			items.forEach((item) => observer.observe(item));
		}, 100);

		return () => {
			clearTimeout(timer);
			observer.disconnect();
		};
	}, []);

	return (
		<section className="py-20 bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50 dark:from-slate-900 dark:via-purple-900 dark:to-indigo-900">
			<div className="container mx-auto px-4">
				{/* 标题 */}
				<div className="text-center mb-16">
					<h2 className="text-4xl md:text-5xl font-bold text-gray-800 dark:text-white mb-6">
						收藏生活中的
						<span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
							美好瞬间
						</span>
					</h2>
					<p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
						每一次旅行都值得被精心记录，每一个瞬间都应该被温柔保存。
						我们为你提供最贴心的工具，让美好永远不会褪色。
					</p>
				</div>

				{/* 功能网格 */}
				<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
					{memories.map((memory, index) => (
						<div
							key={index}
							data-item={index}
							className={`group transition-all duration-1000 transform ${
								visibleItems.includes(index)
									? "opacity-100 translate-y-0 scale-100"
									: "opacity-0 translate-y-12 scale-95"
							}`}
							style={{ transitionDelay: `${index * 150}ms` }}
						>
							<div className="relative h-full bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-white/50 dark:border-slate-700/50 group-hover:scale-105">
								{/* 背景装饰 */}
								<div
									className={`absolute top-0 right-0 w-32 h-32 bg-gradient-to-br ${memory.color} opacity-10 rounded-full blur-2xl group-hover:opacity-20 transition-opacity duration-500`}
								/>

								{/* 图标 */}
								<div
									className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${memory.color} flex items-center justify-center text-2xl mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300`}
								>
									{memory.icon}
								</div>

								{/* 内容 */}
								<h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-4 group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-300">
									{memory.title}
								</h3>
								<p className="text-gray-600 dark:text-gray-300 leading-relaxed text-lg">
									{memory.description}
								</p>

								{/* 悬浮效果指示器 */}
								<div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
									<div
										className={`w-3 h-3 rounded-full bg-gradient-to-r ${memory.color} animate-pulse`}
									/>
								</div>
							</div>
						</div>
					))}
				</div>

				{/* 底部装饰文字 */}
				<div className="text-center mt-20">
					<div className="inline-flex items-center gap-4 px-8 py-4 bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm rounded-full border border-purple-200 dark:border-purple-800">
						<span className="text-purple-600 dark:text-purple-400 text-2xl">
							✨
						</span>
						<p className="text-gray-700 dark:text-gray-300 font-medium">
							让每一次旅行都成为珍贵的艺术品
						</p>
						<span className="text-purple-600 dark:text-purple-400 text-2xl">
							✨
						</span>
					</div>
				</div>
			</div>
		</section>
	);
}
