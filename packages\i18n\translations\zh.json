{"admin": {"menu": {"organizations": "组织", "users": "用户"}, "organizations": {"backToList": "返回组织列表", "confirmDelete": {"confirm": "删除", "message": "您确定要删除此组织吗？此操作无法撤销。", "title": "删除组织"}, "create": "创建", "delete": "删除", "deleteOrganization": {"deleted": "组织已成功删除！", "deleting": "正在删除组织...", "notDeleted": "无法删除组织。请重试。"}, "edit": "编辑", "form": {"createTitle": "创建组织", "name": "组织名称", "notifications": {"error": "无法保存组织。请稍后重试。", "success": "组织已保存。"}, "save": "保存", "updateTitle": "编辑组织"}, "loading": "正在加载组织...", "membersCount": "{count} {count, plural, one {成员} other {成员}}", "search": "搜索组织...", "title": "管理组织"}, "title": "管理后台", "users": {"confirmDelete": {"confirm": "删除", "message": "您确定要删除此用户吗？此操作无法撤销。", "title": "删除用户"}, "delete": "删除", "deleteUser": {"deleted": "用户已成功删除！", "deleting": "正在删除用户...", "notDeleted": "无法删除用户。请重试。"}, "emailVerified": {"verified": "邮箱已验证", "waiting": "邮箱等待验证"}, "impersonate": "模拟用户", "impersonation": {"impersonating": "正在模拟 {name}..."}, "loading": "正在加载用户...", "resendVerificationMail": {"error": "无法重新发送验证邮件。请重试。", "submitting": "正在重新发送验证邮件...", "success": "验证邮件已发送。", "title": "重新发送验证邮件"}, "search": "搜索姓名或邮箱...", "title": "管理用户", "assignAdminRole": "分配管理员角色", "removeAdminRole": "移除管理员角色"}, "description": "管理您的应用程序。"}, "app": {"menu": {"accountSettings": "账户设置", "admin": "管理后台", "aiChatbot": "AI聊天机器人", "organizationSettings": "组织设置", "start": "开始", "dashboard": "控制面板", "travel-diary": "旅行日记"}, "userMenu": {"accountSettings": "账户设置", "colorMode": "颜色模式", "documentation": "文档", "home": "首页", "logout": "退出登录"}}, "auth": {"errors": {"invalidEmailOrPassword": "您输入的凭据无效。请检查后重试。", "unknown": "发生了错误。请重试。", "userNotFound": "此用户不存在", "failedToCreateUser": "无法创建用户。请重试。", "failedToCreateSession": "无法创建会话。请重试。", "failedToUpdateUser": "无法更新用户。请重试。", "failedToGetSession": "无法获取会话。", "invalidPassword": "输入的密码不正确。", "invalidEmail": "输入的邮箱无效。", "invalidToken": "您输入的令牌无效或已过期。", "credentialAccountNotFound": "账户未找到。", "emailCanNotBeUpdated": "邮箱无法更新。请重试。", "emailNotVerified": "请先验证您的邮箱再登录。", "failedToGetUserInfo": "无法加载用户信息。", "idTokenNotSupported": "不支持ID令牌。", "passwordTooLong": "密码过长。", "passwordTooShort": "密码过短。", "providerNotFound": "不支持此提供商。", "socialAccountAlreadyLinked": "此账户已链接到用户。", "userEmailNotFound": "邮箱未找到。", "userAlreadyExists": "此用户已存在。", "invalidInvitation": "邀请无效或已过期。", "sessionExpired": "会话已过期。", "failedToUnlinkLastAccount": "无法解除链接账户", "accountNotFound": "账户未找到"}, "forgotPassword": {"backToSignin": "返回登录", "email": "邮箱", "hints": {"linkNotSent": {"message": "很抱歉，我们无法向您发送重置密码的链接。请稍后重试。", "title": "链接未发送"}, "linkSent": {"message": "我们已向您发送了一个链接以继续。请检查您的收件箱。", "title": "链接已发送"}}, "message": "请输入您的邮箱地址，我们将向您发送重置密码的链接。", "submit": "发送链接", "title": "忘记密码？"}, "login": {"continueWith": "或继续使用", "createAnAccount": "创建账户", "dontHaveAnAccount": "还没有账户？", "forgotPassword": "忘记密码？", "hints": {"invalidCredentials": "您输入的邮箱或密码无效。请重试。", "linkSent": {"message": "我们已向您发送了一个链接以继续。请检查您的收件箱。", "title": "链接已发送"}}, "loginWithPasskey": "使用通行密钥登录", "modes": {"magicLink": "魔法链接", "password": "密码"}, "submit": "登录", "subtitle": "请输入您的凭据以登录。", "title": "欢迎回来", "sendMagicLink": "发送魔法链接"}, "resetPassword": {"backToSignin": "返回登录", "hints": {"error": "很抱歉，我们无法重置您的密码。请重试。", "success": "您的密码已成功重置。"}, "message": "请输入新密码。", "newPassword": "新密码", "submit": "重置密码", "title": "重置您的密码"}, "signup": {"alreadyHaveAccount": "已有账户？", "email": "邮箱", "hints": {"signupFailed": "很抱歉，我们无法创建您的账户。请稍后重试。", "verifyEmail": "我们已向您发送了一个验证邮箱的链接。请检查您的收件箱。"}, "message": "很高兴您想加入我们。请填写下面的表单创建您的账户。", "name": "姓名", "password": "密码", "signIn": "登录", "submit": "创建账户", "title": "创建账户"}}, "blog": {"description": "在 Map Moment 里留下你的足迹", "title": "我的精彩博客"}, "changelog": {"description": "了解我们产品的最新变化。", "title": "更新日志", "dateErrors": {"invalidDate": "无效日期", "unknownTime": "未知时间", "invalid": "无效", "unknown": "未知", "error": "错误"}, "items": {"2025-06-15": {"date": "2025年6月15日", "changes": ["🎉 Map Moment 正式发布！在互动地图上记录你的珍贵旅行时光", "✨ 智能搜索功能 - 快速找到世界各地任何地点并添加到你的旅行记忆中", "📊 旅行统计仪表板 - 实时查看你的旅行足迹，包含访问国家数、城市数等精美统计", "🗺️ 多样化地图主题 - 从卫星视图到艺术风格，为你的回忆选择完美的地图背景", "📱 完全响应式设计 - 在桌面、平板和手机上都能完美展示你的旅行地图"]}, "2025-06-12": {"date": "2025年6月12日", "changes": ["🎨 社交媒体卡片生成器 - 将你的旅行足迹转化为精美的可分享卡片", "🖼️ 多平台优化 - 支持 Instagram、Twitter、Facebook 等社交平台的最佳尺寸", "🎯 自定义卡片样式 - 个性化颜色、字体和布局，让每张卡片都独一无二", "💫 精美模板库 - 多种专业设计的模板，让你的旅行回忆更加出众"]}, "2025-06-10": {"date": "2025年6月10日", "changes": ["💾 数据备份与同步 - 安全导出你的珍贵旅行数据，永不丢失美好回忆", "📥 智能数据导入 - 轻松管理和恢复你的旅行足迹数据", "🔄 一键重置功能 - 随时开始新的旅行记录，灵活管理你的地图收藏", "🚀 性能优化 - 大幅提升地图加载速度和交互流畅度"]}, "2025-06-08": {"date": "2025年6月8日", "changes": ["🌍 全球地点搜索引擎 - 覆盖全球数百万个地点，从热门城市到隐秘小镇", "📍 智能地点标记 - 一键添加访问过的地方，自动生成精美的地图标记", "🎭 沉浸式地图体验 - 平滑的地图导航和缩放，让浏览变成一种享受", "🔍 实时搜索建议 - 输入时即时显示搜索结果，快速找到目标地点"]}, "2025-06-05": {"date": "2025年6月5日", "changes": ["🎪 动态背景效果 - 精美的粒子动画和极光效果，让你的旅行地图更加生动", "🌙 深色模式支持 - 优雅的夜间主题，保护眼睛的同时享受美丽的地图", "🎨 UI/UX 设计升级 - 现代简约的界面设计，让每次使用都是视觉享受", "⚡ 流畅的页面切换 - 无缝的模式转换动画，提供丝滑的用户体验"]}, "2025-06-02": {"date": "2025年6月2日", "changes": ["🏗️ Map Moment 项目启动！", "💡 确定产品愿景：帮助每个人在美丽的地图上保存和分享珍贵的旅行时光", "🚀 开始构建核心功能框架", "🎯 专注于用户体验和视觉设计的完美结合"]}}}, "common": {"confirmation": {"cancel": "取消", "confirm": "确认"}, "menu": {"blog": "博客", "changelog": "更新日志", "contact": "联系我们", "dashboard": "控制面板", "docs": "文档", "faq": "常见问题", "login": "登录", "pricing": "价格"}, "tableOfContents": {"title": "本页内容"}}, "contact": {"description": "我们随时为您提供帮助。请使用下面的表单与我们联系。", "form": {"email": "邮箱", "message": "消息", "name": "姓名", "notifications": {"error": "很抱歉，我们无法发送您的消息。请稍后重试。", "success": "您的消息已成功发送。我们会尽快回复您。"}, "submit": "发送消息"}, "title": "联系我们"}, "documentation": {"title": "文档"}, "faq": {"description": "您有问题吗？我们已经为您准备好了答案。", "title": "常见问题", "items": {"first": {"question": "什么是supastarter？", "answer": "supastarter是一个用于构建SaaS应用的Next.js初始套件。"}, "second": {"question": "如何开始使用？", "answer": "您可以通过克隆代码库并按照README中的说明进行操作来开始使用。"}, "third": {"question": "有免费计划吗？", "answer": "是的，有一个免费计划，您可以用它来测试产品。"}, "fourth": {"question": "我可以将其用于商业项目吗？", "answer": "是的，您可以将其用于商业项目。"}}}, "mail": {"common": {"openLinkInBrowser": "如果您想在非默认浏览器中打开链接，请复制并粘贴以下链接：", "otp": "一次性密码", "useLink": "或使用以下链接："}, "emailVerification": {"body": "您好，\n请点击下面的链接验证此新邮箱地址。", "confirmEmail": "验证邮箱", "subject": "验证您的邮箱"}, "forgotPassword": {"body": "您好，\n您请求了密码重置。\n\n请点击下面的按钮重置您的密码。", "resetPassword": "重置密码", "subject": "重置您的密码"}, "magicLink": {"body": "您好，\n您从supastarter请求了登录邮件。\n\n请点击下面的链接登录。", "login": "登录", "subject": "登录到supastarter"}, "organizationInvitation": {"body": "您好，\n您已被邀请加入组织 {organizationName}。\n\n请点击下面的按钮接受邀请。", "inviteAction": "接受邀请", "subject": "邀请加入 {organizationName}"}}, "marketing": {"footer": {"legal": {"privacy": "隐私政策", "terms": "服务条款"}, "navigation": {"about": "关于我们", "blog": "博客", "contact": "联系我们", "documentation": "文档", "faq": "常见问题", "features": "功能", "pricing": "价格", "company": "公司", "product": "产品", "resources": "资源"}, "support": "支持"}, "home": {"hero": {"getStarted": "免费开始使用", "title": "使用Next.js和Supabase在几天内构建SaaS，而非几周。"}, "features": {"title": "构建SaaS创业所需的所有功能", "items": {"authentication": {"title": "身份验证", "description": "预构建的身份验证，支持邮箱/密码、魔法链接、社交登录和通行密钥。"}, "organizations": {"title": "组织", "description": "内置组织支持，包括邀请、角色和计费功能。"}, "payments": {"title": "支付", "description": "与Stripe、Lemon Squeezy或Chargebee集成，管理订阅和支付。"}, "database": {"title": "数据库", "description": "Supabase作为PostgreSQL数据库提供商。Prisma作为ORM。"}, "storage": {"title": "存储", "description": "Supabase作为存储提供商，用于用户生成的内容，如头像或图片。"}, "mails": {"title": "邮件", "description": "预构建的邮件模板，用于常见操作如密码重置或邮箱验证。"}, "admin": {"title": "管理区域", "description": "预构建的管理区域，管理用户、组织等。"}, "internationalization": {"title": "国际化", "description": "使用next-international内置的国际化支持。"}, "darkmode": {"title": "深色模式", "description": "使用next-themes预构建的深色模式支持。"}}}, "testimonials": {"title": "客户对我们的评价", "items": {"first": {"text": "我喜欢这个产品！它对我的业务帮助很大。", "name": "约翰·多伊", "title": "CEO, Example公司"}, "second": {"text": "这是我用过的最好的产品。强烈推荐。", "name": "简·多伊", "title": "CTO, Example公司"}, "third": {"text": "我对这个产品非常满意。它易于使用且功能丰富。", "name": "彼得·史密斯", "title": "开发者, Example公司"}}}, "pricing": {"title": "随业务发展扩展的价格", "description": "我们提供多种计划来满足您的需求。选择最适合您的一种。", "getStarted": "开始使用", "contactSales": "联系销售", "features": "功能", "plans": {"free": {"title": "免费", "description": "适用于个人和小型团队", "price": "$0", "priceDescription": "每月", "features": ["功能1", "功能2", "功能3"]}, "pro": {"title": "专业版", "description": "适用于成长中的企业", "price": "$29", "priceDescription": "每月", "features": ["功能1", "功能2", "功能3", "功能4"]}, "enterprise": {"title": "企业版", "description": "适用于大型组织", "price": "定制", "priceDescription": "联系我们了解价格", "features": ["功能1", "功能2", "功能3", "功能4", "功能5"]}}}, "faq": {"title": "常见问题", "description": "您有问题吗？我们已经为您准备好了答案。", "items": {"first": {"question": "什么是supastarter？", "answer": "supastarter是一个用于构建SaaS应用的Next.js初始套件。"}, "second": {"question": "如何开始使用？", "answer": "您可以通过克隆代码库并按照README中的说明进行操作来开始使用。"}, "third": {"question": "有免费计划吗？", "answer": "是的，有一个免费计划，您可以用它来测试产品。"}, "fourth": {"question": "我可以将其用于商业项目吗？", "answer": "是的，您可以将其用于商业项目。"}}}}}, "notFound": {"backToHome": "返回首页", "description": "很抱歉，您查找的页面不存在。", "title": "页面未找到"}, "onboarding": {"form": {"continue": "继续", "finish": "完成", "name": "姓名", "notifications": {"error": "无法保存您的数据。请稍后重试。", "success": "您的数据已保存。"}, "organizationName": "组织名称", "skip": "暂时跳过", "steps": {"createOrJoinOrganization": "创建或加入组织", "personalInformation": "个人信息"}}, "goBackToApp": "返回应用", "subtitle": "很高兴您来到这里。请填写下面的表单以开始使用。", "title": "欢迎使用supastarter"}, "organizations": {"create": {"button": "创建组织", "notifications": {"error": "无法创建组织。请重试。", "limitReached": "您已达到可创建的组织数量上限。", "success": "组织已创建。"}, "placeholder": "您的组织名称", "title": "创建新组织"}, "invitation": {"accept": "接受邀请", "backToApp": "返回应用", "decline": "拒绝邀请", "loading": "正在加载邀请...", "notifications": {"accept": {"error": "无法接受邀请。请重试。", "success": "邀请已接受。"}, "decline": {"error": "无法拒绝邀请。请重试。", "success": "邀请已拒绝。"}, "notFound": "您查找的邀请不存在或已过期。"}, "title": "您已被邀请加入{organizationName}"}, "settings": {"menu": {"billing": "计费", "dangerZone": "危险区域", "general": "常规", "members": "成员"}, "title": "组织设置"}}, "payments": {"billingPortal": {"loading": "正在加载计费门户...", "notifications": {"error": "无法加载计费门户。请稍后重试。"}}, "checkout": {"back": "返回计划", "loading": "正在加载结算页面...", "notifications": {"error": "无法加载结算页面。请稍后重试。"}}, "notifications": {"subscriptionCreated": "订阅创建成功！", "subscriptionNotCreated": "无法创建订阅。请重试。"}, "plans": {"choosePlan": "选择计划", "contactSales": "联系销售", "currentPlan": "当前计划", "downgrade": "降级", "getStarted": "开始使用", "lifetimeDeal": "终身优惠", "perMonth": "/ 月", "perUserPerMonth": "/ 用户 / 月", "perYear": "/ 年", "popular": "热门", "title": "选择您的计划", "upgrade": "升级", "yearly": "年付"}}, "settings": {"account": {"menu": {"connectedAccounts": "关联账户", "deleteAccount": "删除账户", "email": "邮箱", "general": "常规", "password": "密码", "billing": "计费"}, "title": "账户设置"}, "billing": {"manageBilling": "Manage billing", "manageSubscription": "Manage subscription", "notifications": {"notSubscribed": "You are not subscribed to any plan yet."}, "plan": "Plan", "status": "Status", "title": "Billing", "trialEnds": "Trial ends on {date}"}, "dangerZone": {"deleteAccount": {"button": "删除账户", "confirm": "删除", "description": "永久删除您的账户及所有内容。", "message": "您确定要删除您的账户吗？此操作无法撤销。", "notifications": {"error": "无法删除您的账户。请重试。", "success": "您的账户已被删除。"}, "title": "删除账户"}, "deleteOrganization": {"button": "删除组织", "confirm": "删除", "description": "永久删除您的组织及其所有内容。", "message": "您确定要删除您的组织吗？此操作无法撤销。", "notifications": {"error": "无法删除组织。请重试。", "success": "组织已被删除。"}, "title": "删除组织"}, "description": "这些操作是永久性的，无法撤销。", "title": "危险区域"}, "general": {"email": {"description": "您的邮箱地址是 {email}。", "notifications": {"error": "无法更新您的邮箱。请重试。", "success": "您的邮箱已更新。我们已向您发送了一个链接以验证您的新邮箱地址。"}, "title": "邮箱", "updateButton": "更新邮箱"}, "language": {"description": "选择您想在应用中使用的语言。", "title": "语言"}, "name": {"description": "这是您的公开姓名。它将对其他用户可见。", "notifications": {"error": "无法更新您的姓名。请重试。", "success": "您的姓名已更新。"}, "title": "姓名"}, "notifications": {"notSaved": "您的更改未保存。请重试。", "saved": "您的更改已保存。"}, "organizationName": {"description": "这是您组织的名称。它将对其他用户可见。", "notifications": {"error": "无法更新组织名称。请重试。", "success": "组织名称已更新。"}, "title": "组织名称"}, "password": {"description": "更新您的密码。", "notifications": {"error": "无法更新您的密码。请重试。", "success": "您的密码已更新。"}, "title": "密码", "updateButton": "更新密码"}, "save": "保存更改", "theme": {"description": "选择您想在应用中使用的主题。", "title": "主题"}, "title": "常规"}, "members": {"confirmRemove": {"confirm": "移除", "message": "您确定要移除此成员吗？", "title": "移除成员"}, "invite": {"button": "邀请成员", "description": "邀请新成员加入您的组织。", "email": "邮箱", "notifications": {"error": "无法邀请成员。请重试。", "success": "邀请已发送。"}, "role": "角色", "submit": "发送邀请", "title": "邀请新成员"}, "notifications": {"roleNotUpdated": "无法更新角色。请重试。", "roleUpdated": "角色已更新。", "removeError": "无法移除成员。请重试。", "removeSuccess": "成员已被移除。"}, "owner": "所有者", "pending": "待定", "remove": "移除", "title": "成员"}}, "storage": {"errors": {"delete": "无法删除文件。请稍后重试。", "list": "无法列出文件。请稍后重试。", "upload": "无法上传文件。请稍后重试。"}}, "termsAndConditions": {"description": "在使用我们的服务前，请仔细阅读我们的条款和条件。", "title": "条款和条件"}, "privacyPolicy": {"description": "在使用我们的服务前，请仔细阅读我们的隐私政策。", "title": "隐私政策"}, "analytics": {"enableInfo": "请在环境变量中设置您的分析提供商以启用分析。"}, "travelMemo": {"dashboard": {"headerTitle": "旅行控制面板", "newDiaryButton": "新建旅行日记", "stats": {"countriesVisited": "已访问国家", "citiesExplored": "已探索城市", "footprintsLeft": "留下足迹", "photosTaken": "拍摄照片"}, "errors": {"loadDiariesFailed": "加载旅行日记失败。请稍后重试。", "loadStatsFailed": "加载统计数据失败。请稍后重试。", "loadFootprintsFailed": "加载地图足迹失败。请稍后重试。"}, "toast": {"deleteSuccessTitle": "删除成功", "deleteSuccessDescription": "日记已被永久删除。", "deleteFailedTitle": "删除失败", "deleteFailedDescriptionGeneric": "删除日记时发生错误。请重试。"}, "diaries": {"title": "我的旅行日记", "viewAllButton": "查看所有日记", "emptyState": {"title": "未找到旅行日记。", "cta": "从创建您的第一个旅行日记开始！"}, "card": {"defaultExcerpt": "开始记录您的旅行故事...", "defaultLocation": "未指定地点"}}, "map": {"title": "全球旅行地图", "description": "探索您在全球各地留下的足迹。"}}, "diaryCard": {"dateNotSet": "未设置日期", "footprintsCount": "{count, plural, =0 {无足迹} one {# 个足迹} other {# 个足迹}}", "photosCount": "{count, plural, =0 {无照片} one {# 张照片} other {# 张照片}}", "deleteDiaryLabel": "删除日记", "deleteConfirm": {"title": "确认删除日记", "descriptionPart1": "此操作将永久删除日记 \"{title}\" 及其所有足迹、照片和视频。删除后，它", "irreversible": "无法恢复", "descriptionPart2": "。请确认您的操作。", "cancel": "取消", "confirm": "确认删除"}}, "diaryEditor": {"toasts": {"loadFailed": "无法加载旅行日记数据", "autoSaveSuccess": "日记已自动保存", "autoSaveFailed": "自动保存失败", "changesSaved": "更改已保存", "coverImageUploading": "正在上传封面图片...", "coverImageUpdated": "封面图片已更新", "pointAdded": "旅行点已添加", "pointUpdated": "旅行点已更新", "savingChanges": "正在保存更改...", "unformattedContentWarning": "富文本编辑器中有未格式化的内容。切换后，此内容将保留在草稿中。", "noContentToImportError": "日记中没有内容，请先创建时间线和点位", "formatAndSaveButtonLabel": "格式化并预览"}, "richText": {"loadingDraft": "正在加载日记 {diaryId} 的富文本草稿...", "loadedDraft": "已获取富文本草稿内容:", "fixingNestedContent": "检测到嵌套内容格式，正在修正...", "draftSetToEditor": "富文本草稿内容已设置到编辑器状态:", "noDraftFound": "未找到富文本草稿内容", "loadDraftFailedError": "加载富文本草稿失败:", "editorPlaceholder": "开始撰写您的旅行故事..."}, "errors": {"loadDiaryDataFailed": "加载日记数据失败:", "getUploadUrlFailed": "获取上传URL失败: {detail}", "missingUrlInApiResponse": "API响应中缺少uploadUrl或permanentUrl", "coverUploadFailed": "封面图片上传失败: {detail}", "coverUploadProcessFailed": "封面图片上传处理失败:", "coverUploadFailedSimple": "封面图片上传失败"}, "logs": {"coverUploadSuccess": "封面图片上传成功，永久URL:", "importToRichTextStart": "开始从日记导入内容到富文本编辑器", "noContentToImportError": "日记中没有时间线或点位，无法导入"}, "header": {"titlePlaceholder": "给您的旅行取个名字！", "subtitlePlaceholder": "添加简短介绍（可选）", "changeCoverButton": "更换封面", "uploadCoverButton": "上传封面", "editSaveHint": "编辑 | 按回车保存", "expandButton": "展开标题", "collapseButton": "折叠标题", "presentationModeButton": "打开演示模式", "switchToClassicModeButton": "切换到经典编辑模式", "switchToRichTextModeButton": "切换到富文本编辑模式"}, "resizeHandle": {"tooltip": "拖动调整面板大小", "hoverText": "拖动调整左/右区域宽度"}, "importDialog": {"title": "确认导入", "message": "导入操作将覆盖当前的富文本内容。是否继续？", "cancelButton": "取消", "confirmButton": "确认导入"}, "markdownPreview": {"title": "Markdown内容预览", "emptyState": "没有Markdown内容可预览。", "closeButton": "关闭预览", "formatAndSaveButtonLabel": "格式化并预览"}}, "mapboxPresentation": {"pageTitle": "交互式旅行地图", "loadingMap": "正在加载地图...", "errorNotFound": "未找到日记。", "errorFailedToLoad": "加载地图数据失败。请稍后重试。"}, "presentationPage": {"errorInvalidDiaryId": "日记ID无效", "errorDiaryNotFound": "找不到指定的旅行日记", "errorLoadDiaryFailed": "加载日记失败，请稍后重试", "loadingMessage": "正在加载旅行故事...", "defaultLoadError": "加载失败"}, "mapStoryPage": {"loadingMap": "加载地图中...", "logVideoExportStoryComplete": "[视频导出] 故事播放完成，已设置完成标记", "logVideoExportMapReadyAutoPlay": "[视频导出] 地图就绪，自动开始播放故事"}, "recapPage": {"photoCaptions": {"diary": "旅行日记", "memories": "美好记忆", "moments": "精彩瞬间", "scenery": "旅途风景", "time": "珍贵时光"}, "header": {"summary": "{days, plural, one {# 天} other {# 天}} {locations, plural, one {# 个足迹} other {# 个足迹}} {images, plural, one {# 张照片} other {# 张照片}}", "shareStoryButtonLabel": "分享我的旅行", "toastLinkCopied": "链接已复制到剪贴板!", "toastCopyLinkFailed": "复制链接失败: ", "logShareFailed": "分享失败:", "logShareError": "分享出错:"}, "photoWall": {"title": "{count, plural, one {# 张照片的回忆} other {# 张照片的回忆}}", "emptyState": "暂无照片记录。"}, "timeline": {"itemTitleFormat": "{date} 在 {location}", "emptyState": "暂无详细行程记录。"}, "footer": {"exploreMoreButton": "探索更多日记", "orSeparator": "或", "backToHomeButton": "返回主页"}, "viewStoryButton": {"defaultLabel": "查看地图故事"}}, "exportVideo": {"buttonText": "导出视频", "dialogTitleExporting": "正在导出视频...", "dialogTitleSubmitted": "导出请求已提交", "dialogTitleError": "导出失败", "dialogDescPreparing": "正在准备导出，请稍候...", "dialogDescSubmittedMsg1": "您的视频导出请求已经提交。这可能需要几分钟时间。", "dialogDescTaskId": "任务 ID: {taskId}", "dialogDescSubmittedMsg2": "视频制作完成后，系统将通知您下载。您可以关闭此对话框并继续使用系统。", "dialogDescErrorPrefix": "导出视频时出现错误:", "dialogDescUnknownError": "未知错误", "footerButtonCancel": "取消", "footerButtonClose": "关闭", "errorRequestFailed": "导出请求失败: {status}", "errorGetStatusFailed": "获取任务状态失败", "errorExportFailedMessage": "导出失败"}, "coverPage": {"statsLabelLocations": "地点", "statsLabelPhotos": "照片", "startButtonAriaLabel": "开始旅行", "startJourneyPrompt": "点击或下滑开启旅程"}, "playbackControls": {"tooltipBackToStart": "回到开头", "tooltipPreviousPoint": "上一个点", "tooltipViewRecap": "查看回忆录", "tooltipPause": "暂停", "tooltipPlay": "播放", "svgLabelViewRecap": "查看回忆录", "tooltipStartJourney": "开始旅程", "tooltipNextPoint": "下一个点", "tooltipSkipToRecap": "跳至回顾页面", "sliderLabelProgress": "播放进度", "statusCompleted": "已完成", "statusOverview": "概览"}, "lightbox": {"closeButtonLabel": "关闭灯箱", "previousButtonLabel": "上一张图片", "nextButtonLabel": "下一张图片", "imageAltText": "第 {currentIndex} 张照片，共 {totalImages} 张", "imageCounterText": "{currentIndex} / {totalImages}"}, "imageGallery": {"scrollLeftButtonLabel": "向左滚动", "scrollRightButtonLabel": "向右滚动", "viewImageButtonLabel": "查看旅行照片 {indexPlusOne}", "imageAltText": "旅行景点 {indexPlusOne}"}, "completionPrompt": {"title": "旅程回顾完成", "description": "您已完整体验了这段旅行故事，向下滑动探索详细回忆", "viewMemoriesButton": "查看回忆"}, "countdownPrompt": {"autoPlayMessage": "{countdown}秒后自动播放", "manualControlHint": "点击按钮可手动控制"}}, "newDiaryPage": {"loadingIndicator": {"creating": "正在创建新旅行日记..."}, "errorDisplay": {"title": "创建失败", "unknownError": "创建日记时发生未知错误。"}, "toast": {"createFailedPrefix": "创建失败: "}}, "travelStat": {"meta": {"title": "旅行足迹统计", "description": "可视化您的全球旅行足迹并创建精美的旅行卡片"}, "navigation": {"editMode": "编辑模式", "cardGeneration": "卡片生成", "switchToEdit": "返回编辑模式", "switchToCard": "生成精美卡片", "addFootprintsFirst": "请先添加一些旅行足迹点", "addPointsToGenerate": "添加足迹点来生成卡片", "generateCard": "生成精美卡片"}, "addPointPanel": {"selectedLocation": "选中位置", "addToMap": "添加到地图", "adding": "添加中...", "addDetailedInfo": "添加详细信息", "date": "日期", "photo": "照片", "description": "描述", "clickToUpload": "点击上传", "uploading": "上传中...", "jpgPngMax20mb": "JPG、PNG，最大20MB", "shareYourFeelings": "分享你的感受...", "selectImageFile": "请选择图片文件", "imageTooLarge": "图片大小不能超过 20MB", "imageProcessFailed": "图片处理失败，请重试", "addSuccess": "✅ 地点添加成功！", "addFailed": "添加失败：{error}", "preview": "预览"}, "seo": {"mainTitle": "Map Moment - 在地图上记录你的美好旅行时光", "mainDescription": "Map Moment 帮助你在精美的互动地图上保存珍贵的旅行时刻。标记你去过的地方，创建精美的旅行卡片，将每一次旅程都变成值得珍藏的数字瞬间。", "features": {"smartSearch": {"title": "智能目的地搜索", "description": "快速找到并标记全球任意目的地。Map Moment 的智能搜索帮助你迅速定位城市、地标和那些留下珍贵时刻的特殊地方。"}, "detailedStats": {"title": "旅程统计分析", "description": "用精美的统计数据追踪你的旅行足迹。看看你探索了多少个国家、访问了多少座城市，在你的 Map Moment 收藏中捕获了多少珍贵时刻。"}, "socialCards": {"title": "可分享的时刻卡片", "description": "将你的旅行时刻转化为精美的视觉卡片。完美适配 Instagram、微博和朋友圈 - 让 Map Moment 帮你与世界分享最珍贵的旅行体验。"}, "mapStyles": {"title": "精美地图主题", "description": "选择与你的旅行风格相配的精美地图样式。从卫星视图到艺术主题，Map Moment 为你的珍贵旅行时刻提供完美的背景。"}, "dataManagement": {"title": "时刻备份与同步", "description": "永远保护你珍贵的旅行时刻。Map Moment 的备份功能确保你捕获的时刻永不丢失，支持简单的导入导出操作。"}, "responsiveDesign": {"title": "随时随地的时刻", "description": "在任何设备上访问你的 Map Moment 收藏。无论是桌面、平板还是手机，你的旅行时刻总是触手可及，精美呈现。"}}, "howToUse": {"title": "如何创建你的 Map Moment 收藏", "steps": {"searchAndAdd": {"title": "标记你的特殊地点", "description": "搜索任何你体验过特殊时刻的地方。将城市、地标或隐藏的宝藏添加到你的个人 Map Moment 收藏中。"}, "viewStatistics": {"title": "追踪你的旅程", "description": "看着你的 Map Moment 收藏不断增长！了解你探索了多少个国家、访问了多少座城市、捕获了多少珍贵时刻。"}, "generateCards": {"title": "创建时刻卡片", "description": "将你的 Map Moment 收藏转化为精美的可分享卡片。完美适配社交媒体或作为你冒险经历的数字纪念品。"}, "customizeMapStyle": {"title": "设计你的时刻风格", "description": "选择反映你旅行风格的地图主题。从卫星视图到艺术设计，让你的 Map Moment 收藏独一无二。"}, "exportBackupData": {"title": "保存你的时刻", "description": "用备份选项保护你珍贵的 Map Moment 收藏。你的旅行时刻值得永远保存。"}, "shareToSocial": {"title": "分享你的故事", "description": "让世界看到你的精彩旅程！在微博、朋友圈、Instagram 上分享你的 Map Moment 卡片，激励他人创建自己的时刻地图。"}}}, "faq": {"title": "关于 Map Moment 的常见问题", "items": {"isItFree": {"question": "Map Moment 是免费使用的吗？", "answer": "是的！Map Moment 完全免费使用。创建无限的时刻地图、生成精美卡片、保存你的旅行时刻，完全不收费。"}, "dataStorage": {"question": "我的 Map Moment 数据是如何存储的？", "answer": "你的珍贵时刻安全地存储在浏览器中。Map Moment 尊重你的隐私 - 我们不会在服务器上收集或存储你的个人旅行数据。"}, "deviceSupport": {"question": "我可以在不同设备上访问 Map Moment 吗？", "answer": "当然可以！Map Moment 在桌面、平板和手机上都能完美运行。无论你在哪里，只要灵感来袭，你的时刻收藏都触手可及。"}, "socialPlatforms": {"question": "我可以在哪里分享我的 Map Moment 卡片？", "answer": "随处分享你的时刻卡片！Map Moment 生成的卡片针对微博、朋友圈、Instagram 和其他社交平台进行了优化。完美激励朋友和家人分享他们的冒险经历。"}}}, "keywords": {"tags": "Map Moment,旅行时刻,时刻地图,美好瞬间,互动旅行地图,旅行时刻收藏,旅程瞬间,旅行故事地图,瞬间收藏,旅行卡片", "description": "Map Moment - 在精美互动地图上捕获旅行时刻的终极工具。保存旅程中的珍贵瞬间，创建精美的时刻卡片，构建你冒险经历的视觉故事。完美适合想要让最珍贵旅行时刻永远鲜活的旅行爱好者。"}}, "search": {"title": "搜索地点", "placeholder": "搜索国家、城市或地点...", "noResults": "未找到结果", "suggestions": "建议", "recent": "最近搜索", "popular": "热门目的地"}, "stats": {"title": "旅行统计", "countries": {"title": "国家", "count": "{count}个国家"}, "cities": {"title": "城市", "count": "{count}个城市"}, "points": {"title": "足迹点", "count": "{count}个足迹点"}, "continents": {"title": "大洲", "count": "{count}个大洲"}, "distance": {"title": "距离", "km": "{distance} 公里", "miles": "{distance} 英里"}}, "countryList": {"title": "已访问国家", "emptyState": "暂无访问记录", "colorGuideTitle": "颜色系统说明", "colorGuideHelpTooltip": "查看颜色系统说明", "colorGuide": {"currentTheme": "当前颜色主题", "mapColorDescription": "地图上的颜色根据访问次数进行标识", "visitCount": {"one": "1次", "two": "2次", "three": "3次", "four": "4次", "five": "5次", "sixToTen": "6-10次", "tenPlus": "10+次"}, "tip": "💡 提示：颜色根据当前选择的主题变化，直观显示您的旅行频率"}}, "operationPanel": {"title": "数据操作", "importData": "导入数据", "exportData": "导出数据", "clearData": "清空数据", "confirmClear": {"title": "确认清空数据", "description": "这是一个不可逆的操作。", "warningText": "即将清空以下数据：", "items": {"travelPoints": "所有旅行足迹点", "countryStats": "已访问国家统计", "mapData": "地图标记和路线"}, "confirmText": "确定要继续吗？", "cancelButton": "取消", "confirmButton": "确认清空"}}, "clearDataDialog": {"title": "确认清空数据", "message": "确定要清空所有旅行足迹数据吗？此操作无法撤销。", "clearImagesLabel": "同时清理图片文件", "clearImagesDescription": "勾选此选项将同时删除本地存储中的所有上传图片。不勾选将保留图片文件，便于日后导入数据时图片仍能正常显示。", "confirm": "确认清空", "cancel": "取消"}, "searchBox": {"placeholder": "搜索城市、国家或地标..."}, "templateSelector": {"noTemplatesForPlatform": "暂无支持 {platform} 的模板", "noTemplatesAvailable": "暂无可用模板", "selectTemplate": "选择模板: {templateName}", "selected": "✓ 已选择"}, "templates": {"names": {"minimal": "极简", "vibrant": "活力", "elegant": "优雅", "retro": "复古", "handDrawn": "手绘"}, "descriptions": {"minimal": "简约现代，专注内容", "vibrant": "彩虹渐变，动感十足", "elegant": "高端大气，金色装饰", "retro": "怀旧风格，温暖色调", "handDrawn": "手绘风格，轻松友好"}, "features": {"minimal": ["现代排版", "大号统计数字", "渐变设计", "3D 视觉效果"], "vibrant": ["彩虹背景", "圆形布局", "斜角设计", "星星动画"], "elegant": ["金色边框", "对称布局", "高端字体", "皇冠装饰"], "retro": ["胶片边框", "拍立得卡片", "老照片滤镜", "复古图标"], "handDrawn": ["手绘地图", "可爱图标", "温暖色调", "童趣装饰"]}, "categories": {"minimal": "极简", "vibrant": "活力", "elegant": "优雅", "retro": "复古", "handDrawn": "手绘"}, "categoryDescriptions": {"minimal": "简洁优雅", "vibrant": "色彩丰富", "elegant": "奢华典雅", "retro": "怀旧温馨", "handDrawn": "可爱童趣"}, "customization": {"labels": {"primaryColor": "主色调", "accentColor": "强调色", "decorativeColor": "装饰色", "headerSize": "标题大小", "borderRadius": "圆角大小", "showDetailedStats": "显示详细统计", "customTitle": "自定义标题", "cardTitle": "卡片标题"}, "categories": {"colors": "颜色", "typography": "字体", "layout": "布局", "content": "内容"}, "titleOptions": ["我的旅行足迹", "环游世界记录", "旅行回忆录", "足迹天下", "行走的印记", "✈️ 我的旅行足迹", "🌍 环游世界记录", "📍 足迹天下", "🗺️ 行走的印记", "🎒 旅行回忆录"], "footerOptions": ["Map Moment · 记录每一次美好旅程", "记录每一段美好的旅程 🌍", "精品定制", "品质之旅"]}}, "colorThemes": {"names": {"classic-blue-green": "经典蓝绿", "warm-sunset": "暖色日落", "cool-ocean": "冷色海洋", "vibrant-rainbow": "活力彩虹", "earth-tones": "大地色彩", "purple-pink": "紫粉梦境", "monochrome": "黑白经典", "high-contrast": "高对比度", "pastel-soft": "柔和糖果", "neon-bright": "霓虹亮色"}, "descriptions": {"classic-blue-green": "从浅蓝色到深绿色的经典渐变，适合大多数地图样式", "warm-sunset": "从浅橙到深红的暖色调，避免与地图绿色冲突", "cool-ocean": "从浅青到深蓝的冷色调，避免与地图蓝色冲突", "vibrant-rainbow": "每个级别使用不同的鲜明颜色，高辨识度", "earth-tones": "棕褐色调，与自然地图样式完美融合", "purple-pink": "从浅紫到深粉的优雅色调，现代感十足", "monochrome": "简约的黑白灰色调，适合简洁风格", "high-contrast": "强烈对比的颜色组合，易于区分", "pastel-soft": "温柔的马卡龙色调，视觉舒适", "neon-bright": "荧光色调，科技感十足"}, "levelDescriptions": {"unvisited": "未访问", "firstVisit": "首次访问", "secondVisit": "二次访问", "multipleVisits": "多次访问", "frequent": "常访问", "veryFrequent": "频繁访问", "extremelyFrequent": "非常频繁", "superFrequent": "超高频访问"}}, "colorThemeSelector": {"current": "当前", "recommendedThemes": "推荐主题", "recommendedSubtitle": "适配当前地图样式", "allThemes": "所有主题", "recommended": "推荐", "categories": {"classic": "经典", "modern": "现代", "nature": "自然", "vibrant": "鲜艳", "minimal": "简约"}}, "mapControls": {"title": "地图控制", "export": "导出地图", "exportHighQuality": "导出地图 (高质量)", "exporting": "导出中...", "styles": {"title": "地图样式", "markers": "点位风格", "atmosphere": "大气层", "animation": "动画", "colors": "颜色主题", "projection": "投影"}, "display": {"title": "显示控制", "current": "当前", "custom": "自定义", "detailedSettings": "详细设置", "showMarkers": "显示标记点", "showTooltips": "显示详情提示", "tooltipDisabledHint": "隐藏标记点时，详情提示自动禁用", "presets": {"all": {"name": "全部显示", "description": "显示标记点和详情"}, "markersOnly": {"name": "仅标记点", "description": "隐藏详情提示"}, "clean": {"name": "纯净地图", "description": "隐藏所有标记"}}}}, "common": {"settings": "设置", "close": "关闭", "closePopover": "关闭弹窗"}, "atmosphereThemes": {"names": {"day": "白天", "night": "夜晚", "sunset": "日落", "dawn": "黎明", "aurora": "极光", "deep-space": "深空", "ocean": "海洋", "minimal": "简约"}, "descriptions": {"day": "明亮清新的白天天空", "night": "深邃神秘的星空夜晚", "sunset": "温暖绚烂的日落黄昏", "dawn": "柔和清新的晨曦黎明", "aurora": "绚丽多彩的极光夜空", "deep-space": "浩瀚无垠的深邃宇宙", "ocean": "清澈湛蓝的海洋天空", "minimal": "纯净优雅的极简风格"}}, "markerStyles": {"names": {"classic": "经典圆点", "gradient-pulse": "渐变光晕", "particle-effect": "粒子特效", "hand-drawn": "手绘素描", "emoji": "表情符号", "polaroid": "拍立得照片"}, "descriptions": {"classic": "简洁清爽的圆形标记", "gradient-pulse": "渐变发光效果，带脉冲动画", "particle-effect": "动态粒子轨道特效", "hand-drawn": "艺术手绘风格标记", "emoji": "可自定义的表情符号标记", "polaroid": "复古拍立得照片风格，显示图片和描述"}, "popover": {"title": "标记样式", "currentStyle": "当前样式: {styleName}", "outlineControl": "外层轮廓", "hideOutline": "隐藏轮廓，仅显示图标", "showOutline": "显示轮廓和图标", "emojiSettings": "表情符号设置", "backgroundColor": "背景颜色", "transparent": "透明", "themeSelector": "主题选择器", "gradientPulseThemes": "渐变光晕主题", "particleEffectThemes": "粒子特效主题", "handDrawnThemes": "手绘风格主题", "selectEmoji": "选择表情符号", "travelRelated": "旅行相关", "expressions": "表情", "activities": "活动", "themes": {"gradient": {"ocean": {"name": "海洋", "description": "宁静"}, "sunset": {"name": "日落", "description": "浪漫"}, "forest": {"name": "森林", "description": "清新"}, "aurora": {"name": "极光", "description": "绚烂"}, "fire": {"name": "火焰", "description": "奔放"}}, "particle": {"fire": {"name": "火焰", "description": "热情奔放"}, "electric": {"name": "电光", "description": "动感炫酷"}, "magic": {"name": "魔法", "description": "神秘优雅"}, "nature": {"name": "自然", "description": "清新宁静"}}, "handDrawn": {"pencil": {"name": "铅笔", "description": "轻柔细腻"}, "pen": {"name": "钢笔", "description": "清晰锐利"}, "charcoal": {"name": "炭笔", "description": "粗犷厚重"}, "marker": {"name": "马克笔", "description": "饱满鲜艳"}}}, "particleCount": "{count}个粒子"}}, "cardGenerator": {"platforms": {"title": "平台", "instagram": "Instagram", "wechat": "微信", "weibo": "微博", "twitter": "Twitter", "facebook": "Facebook"}, "templates": {"title": "模板"}, "customization": {"title": "内容设置", "cardTitle": "卡片标题", "cardTitlePlaceholder": "✈️ 我的旅行足迹", "footer": "自定义页脚", "footerPlaceholder": "Map Moment · 记录每一次美好旅程", "showDate": "显示日期", "customDate": "自定义日期", "customDatePlaceholder": "例如：2023-2024 或 夏日旅行"}, "preview": {"calculating": "计算预览尺寸中...", "mapNotLoaded": "地图未加载完成，请稍后再试", "mapExportSuccess": "地图导出成功", "mapExportFailed": "导出地图失败，请重试", "cardNotReady": "卡片未准备就绪，请稍后再试", "cardExportSuccess": "成功导出卡片: {filename}", "cardExportFailed": "导出卡片失败，请重试", "exportMapOnly": "仅导出地图", "exporting": "导出中...", "exportCard": "导出卡片"}, "stats": {"destinations": "目的地", "places": "地点", "cities": "城市", "countries": "国家"}, "defaults": {"title": "品质之旅", "footer": "精品定制"}, "export": {"messages": {"completed": "卡片导出完成: {quality}"}}}, "mapStyles": {"names": {"streets": "街道地图", "outdoors": "户外地图", "light": "浅色地图", "dark": "深色地图", "satellite": "卫星图像", "satellite-streets": "卫星街道", "navigation-day": "导航白天", "navigation-night": "导航夜间"}, "descriptions": {"streets": "经典街道视图，详细显示道路和地名", "outdoors": "适合徒步和户外活动，显示地形和步道", "light": "简洁明亮的浅色主题，适合数据展示", "dark": "优雅的深色主题，减少眼部疲劳", "satellite": "真实的卫星影像，展现地球原貌", "satellite-streets": "卫星图像叠加街道信息，最佳组合", "navigation-day": "专为导航优化的白天模式", "navigation-night": "专为夜间导航设计的深色模式"}, "categories": {"all": "全部", "basic": "基础样式", "satellite": "卫星影像", "navigation": "导航样式", "artistic": "艺术风格"}, "popover": {"title": "地图样式", "recommended": "推荐", "currentStyle": "当前样式: {styleName}", "goodWithEffects": "✓ 与当前效果搭配良好"}}, "mapProjections": {"names": {"globe": "地球", "mercator": "墨卡托投影", "equalEarth": "相等地球投影", "naturalEarth": "自然地球投影", "winkelTripel": "温克尔投影", "albers": "阿尔伯斯投影", "lambertConformalConic": "兰伯特等角圆锥投影", "equirectangular": "等距矩形投影"}, "descriptions": {"globe": "在球体上渲染地图，提供最真实的地球视角。", "mercator": "将地球渲染为二维正方形地图，是Web地图的标准投影。", "equalEarth": "一种等面积伪圆柱投影，在视觉上更令人愉悦。", "naturalEarth": "一种用于制作小比例尺地图的伪圆柱投影。", "winkelTripel": "一种折衷的方位投影，是国家地理学会的标准投影。", "albers": "一种等面积圆锥投影，常用于显示大国家（如美国）的地图。", "lambertConformalConic": "一种等角圆锥投影，广泛用于航空图。", "equirectangular": "一种简单的投影，将经纬度直接映射为笛卡尔坐标。"}, "shortDescs": {"globe": "真实3D球体", "mercator": "经典导航投影", "equalEarth": "面积精确", "naturalEarth": "视觉优化", "winkelTripel": "国地推荐", "albers": "统计专用", "lambertConformalConic": "角度准确", "equirectangular": "简单矩形"}, "badges": {"globe": "3D", "mercator": "导航", "equalEarth": "等面积", "naturalEarth": "美观", "winkelTripel": "综合", "albers": "数据", "lambertConformalConic": "等角", "equirectangular": "简单"}, "categories": {"common": "常用投影", "professional": "专业投影"}}, "atmosphere": {"names": {"day": "白天", "night": "夜晚", "sunset": "日落", "dawn": "黎明", "aurora": "极光", "deep-space": "深空", "ocean": "海洋", "minimal": "简约"}, "descriptions": {"day": "明亮清新的白天天空", "night": "深邃神秘的星空夜晚", "sunset": "温暖绚烂的日落黄昏", "dawn": "柔和清新的晨曦黎明", "aurora": "绚丽多彩的极光夜空", "deep-space": "浩瀚无垠的深邃宇宙", "ocean": "清澈湛蓝的海洋天空", "minimal": "纯净优雅的极简风格"}, "popover": {"title": "大气层", "currentAtmosphere": "当前大气层: {atmosphereName}", "starIntensity": "星星强度", "horizonBlend": "混合度", "starfield": "星空"}}, "animation": {"names": {"shooting-stars": "流星雨", "floating-particles": "浮动粒子", "aurora": "烟花", "minimal": "简约", "galaxy": "银河", "none": "无动画"}, "descriptions": {"shooting-stars": "流星划过夜空，浪漫而梦幻", "floating-particles": "轻柔的粒子在空中飘舞", "aurora": "绚烂的极光在天际流动", "minimal": "简约优雅的微妙动画", "galaxy": "神秘的银河星空旋转", "none": "无背景动画，纯净清爽"}, "popover": {"title": "动画", "currentTheme": "当前主题", "currentAnimation": "当前动画: {animationName}"}}, "resizablePanel": {"dragHint": "← 拖拽调整 →", "draggingHint": "拖拽调整宽度"}}, "travelPointForm": {"modalTitle": {"add": "添加新旅行点", "edit": "编辑旅行点"}, "buttons": {"cancel": "取消", "add": "添加到旅程", "save": "保存更改"}, "toasts": {"invalidLocation": "请先输入并选择有效的地点"}, "iconOptions": {"pin": "📍 定位", "landmark": "🗿 地标", "food": "🍽️ 美食", "park": "🌳 公园", "hotel": "🏨 酒店", "shopping": "🛍️ 购物", "transport": "🚗 交通", "other": "❓ 其他"}, "logs": {"formDataUpdated": "旅行点表单数据已更新:", "lastDateSaved": "上次选择的日期已保存:", "dialogOpenedResetForm": "对话框已打开，表单数据已重置。", "formSubmitPrevented": "表单自动提交已阻止。", "iconSelected": "已选择图标类型: {iconId}"}, "labels": {"iconType": "图标类型", "location": "位置", "date": "日期", "description": "描述", "photos": "照片"}}}