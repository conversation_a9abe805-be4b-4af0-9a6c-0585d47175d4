"use client";

import { ChevronLeft, ChevronRight, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useRef } from "react";

interface LightboxProps {
	images: string[];
	initialIndex: number;
	isOpen: boolean;
	onClose: () => void;
	onPrev: () => void;
	onNext: () => void;
}

export function Lightbox({
	images,
	initialIndex,
	isOpen,
	onClose,
	onPrev,
	onNext,
}: LightboxProps) {
	const t = useTranslations("travelMemo.lightbox");
	const dialogRef = useRef<HTMLDialogElement>(null);

	// 处理对话框的打开和关闭
	useEffect(() => {
		if (!dialogRef.current) return;

		if (isOpen) {
			dialogRef.current.showModal();
		} else {
			dialogRef.current.close();
		}
	}, [isOpen]);

	// Handle keyboard navigation
	useEffect(() => {
		if (!isOpen) return;

		const handleKeyDown = (event: KeyboardEvent) => {
			if (event.key === "ArrowLeft") {
				onPrev();
			} else if (event.key === "ArrowRight") {
				onNext();
			} else if (event.key === "Escape") {
				onClose();
			}
		};

		window.addEventListener("keydown", handleKeyDown);
		return () => {
			window.removeEventListener("keydown", handleKeyDown);
		};
	}, [isOpen, onPrev, onNext, onClose]);

	if (!isOpen || images.length === 0) {
		return null;
	}

	const currentImage = images[initialIndex];

	// 处理点击背景关闭
	const handleDialogClick = (e: React.MouseEvent<HTMLDialogElement>) => {
		// 只有当点击的是对话框本身（不是内容区域）时才关闭
		const rect = dialogRef.current?.getBoundingClientRect();
		if (rect) {
			// 点击边缘区域时关闭
			const isOutsideContent =
				e.clientX < rect.left + 20 ||
				e.clientX > rect.right - 20 ||
				e.clientY < rect.top + 20 ||
				e.clientY > rect.bottom - 20;

			if (isOutsideContent) {
				onClose();
			}
		}
	};

	return (
		<dialog
			ref={dialogRef}
			className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4 max-w-full max-h-full m-0 w-full h-full"
			onClick={handleDialogClick}
			onKeyDown={(e) => {
				// 已经在上面的useEffect中处理全局键盘事件
				// 这里只是为了满足linter要求
				e.stopPropagation();
			}}
			onClose={onClose}
		>
			{/* Close Button */}
			<button
				type="button"
				className="absolute top-4 right-4 text-white p-2 rounded-full bg-black/50 hover:bg-black/70 transition-colors z-10"
				onClick={(e) => {
					e.stopPropagation(); // Prevent backdrop click
					onClose();
				}}
				aria-label={t("closeButtonLabel")}
			>
				<X className="h-6 w-6" />
			</button>

			{/* Previous Button */}
			{images.length > 1 && (
				<button
					type="button"
					className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white p-3 rounded-full bg-black/50 hover:bg-black/70 transition-colors z-10"
					onClick={(e) => {
						e.stopPropagation(); // Prevent backdrop click
						onPrev();
					}}
					aria-label={t("previousButtonLabel")}
				>
					<ChevronLeft className="h-8 w-8" />
				</button>
			)}

			{/* Image Display */}
			<div
				className="relative max-w-full max-h-full flex items-center justify-center"
				onClick={(e) => e.stopPropagation()} // Prevent backdrop click when clicking image area
				onKeyDown={(e) => e.stopPropagation()}
			>
				<img
					src={currentImage}
					alt={t("imageAltText", {
						currentIndex: initialIndex + 1,
						totalImages: images.length,
					})}
					className="max-w-full max-h-[90vh] object-contain block"
				/>
			</div>

			{/* Next Button */}
			{images.length > 1 && (
				<button
					type="button"
					className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white p-3 rounded-full bg-black/50 hover:bg-black/70 transition-colors z-10"
					onClick={(e) => {
						e.stopPropagation(); // Prevent backdrop click
						onNext();
					}}
					aria-label={t("nextButtonLabel")}
				>
					<ChevronRight className="h-8 w-8" />
				</button>
			)}

			{/* Image Counter */}
			{images.length > 1 && (
				<div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white text-sm bg-black/50 px-2 py-1 rounded">
					{t("imageCounterText", {
						currentIndex: initialIndex + 1,
						totalImages: images.length,
					})}
				</div>
			)}
		</dialog>
	);
}
