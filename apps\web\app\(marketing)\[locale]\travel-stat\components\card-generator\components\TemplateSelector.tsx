"use client";

import { Badge } from "@ui/components/badge";
import { Card } from "@ui/components/card";
import {
	Camera,
	Check,
	Crown,
	Minimize2,
	<PERSON>T<PERSON>,
	<PERSON>rkles,
	Zap,
} from "lucide-react";
import React from "react";
import { cardTemplates, templateCategories } from "../templates";
import type { CardTemplate, SocialPlatform } from "../types/cardTypes";

import { useTravelStatTranslations } from "../../../hooks/useTravelStatTranslations";

// 辅助函数：获取翻译的模板名称
function getTranslatedTemplateName(
	templateId: string,
	travelStatT: any,
): string {
	switch (templateId) {
		case "minimal":
			return travelStatT.templates.names.minimal();
		case "vibrant":
			return travelStatT.templates.names.vibrant();
		case "elegant":
			return travelStatT.templates.names.elegant();
		case "retro":
			return travelStatT.templates.names.retro();
		case "handDrawn":
			return travelStatT.templates.names.handDrawn();
		default:
			return templateId;
	}
}

// 辅助函数：获取翻译的模板描述
function getTranslatedTemplateDescription(
	templateId: string,
	travelStatT: any,
): string {
	switch (templateId) {
		case "minimal":
			return travelStatT.templates.descriptions.minimal();
		case "vibrant":
			return travelStatT.templates.descriptions.vibrant();
		case "elegant":
			return travelStatT.templates.descriptions.elegant();
		case "retro":
			return travelStatT.templates.descriptions.retro();
		case "handDrawn":
			return travelStatT.templates.descriptions.handDrawn();
		default:
			return "";
	}
}

interface TemplateSelectorProps {
	selectedTemplate?: CardTemplate;
	selectedPlatform: SocialPlatform;
	onTemplateSelect: (template: CardTemplate) => void;
	className?: string;
	variant?: "default" | "gallery";
}

export function TemplateSelector({
	selectedTemplate,
	selectedPlatform,
	onTemplateSelect,
	className = "",
	variant = "default",
}: TemplateSelectorProps) {
	const travelStatT = useTravelStatTranslations();

	// 过滤当前平台支持的模板
	const availableTemplates = cardTemplates.filter(
		(template) =>
			// 现在所有模板都支持所有平台
			true,
	);

	// 按分类组织模板
	const templatesByCategory = templateCategories
		.map((category) => ({
			...category,
			templates: availableTemplates.filter(
				(template) => template.category === category.id,
			),
		}))
		.filter((category) => category.templates.length > 0);

	if (variant === "gallery") {
		return (
			<div className={`${className}`}>
				{/* 画廊模式：水平滚动的模板卡片 */}
				<div className="flex gap-6 pb-4 overflow-x-auto scrollbar-thin scrollbar-thumb-blue-400 scrollbar-track-gray-100 hover:scrollbar-thumb-blue-500">
					{availableTemplates.map((template) => (
						<GalleryTemplateCard
							key={template.id}
							template={template}
							isSelected={selectedTemplate?.id === template.id}
							onSelect={() => onTemplateSelect(template)}
						/>
					))}
				</div>

				{availableTemplates.length === 0 && (
					<div className="text-center py-8">
						<div className="text-gray-400 mb-2">😔</div>
						<p className="text-gray-600">
							{travelStatT.templateSelector.noTemplatesForPlatform(
								getPlatformName(selectedPlatform),
							)}
						</p>
					</div>
				)}
			</div>
		);
	}

	return (
		<div className={`space-y-3 ${className}`}>
			{/* 直接显示所有模板，不分类 */}
			<div className="grid grid-cols-1 gap-3">
				{availableTemplates.map((template) => (
					<TemplateCard
						key={template.id}
						template={template}
						isSelected={selectedTemplate?.id === template.id}
						onSelect={() => onTemplateSelect(template)}
					/>
				))}
			</div>

			{availableTemplates.length === 0 && (
				<div className="text-center py-8">
					<div className="text-gray-400 mb-2 text-2xl">😔</div>
					<p className="text-sm text-gray-600">
						{travelStatT.templateSelector.noTemplatesAvailable()}
					</p>
				</div>
			)}
		</div>
	);
}

interface TemplateCardProps {
	template: CardTemplate;
	isSelected: boolean;
	onSelect: () => void;
}

function TemplateCard({ template, isSelected, onSelect }: TemplateCardProps) {
	const travelStatT = useTravelStatTranslations();

	return (
		<button
			type="button"
			className={`relative group cursor-pointer transition-all duration-300 hover:scale-[1.02] w-full text-left ${
				isSelected ? "scale-[1.02]" : ""
			}`}
			onClick={onSelect}
			aria-label={travelStatT.templateSelector.selectTemplate(
				getTranslatedTemplateName(template.id, travelStatT),
			)}
		>
			{/* 选中状态背景光晕 */}
			{isSelected && (
				<div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-xl blur-sm" />
			)}

			<Card
				className={`relative transition-all duration-300 hover:shadow-lg border-0 ${
					isSelected
						? "ring-2 ring-purple-500 shadow-xl bg-gradient-to-br from-white via-purple-50/30 to-pink-50/30"
						: "hover:shadow-md bg-gradient-to-br from-white to-gray-50/50 hover:from-purple-50/20 hover:to-pink-50/20"
				}`}
			>
				{/* 炫彩边框效果 */}
				<div
					className={`absolute inset-0 rounded-lg bg-gradient-to-r from-purple-500/20 via-pink-500/20 to-blue-500/20 transition-opacity duration-300 ${
						isSelected
							? "opacity-100"
							: "opacity-0 group-hover:opacity-50"
					}`}
					style={{ padding: "1px" }}
				>
					<div className="w-full h-full bg-white rounded-lg" />
				</div>

				<div className="relative p-4">
					{/* 横向布局：左侧预览图 + 右侧信息 */}
					<div className="flex items-center gap-4">
						{/* 左侧：炫酷的预览图 */}
						<div className="flex-shrink-0 w-16 h-16 rounded-xl overflow-hidden relative shadow-lg">
							<div
								className="w-full h-full flex items-center justify-center relative"
								style={{
									background: getTemplateBackground(
										template.id,
									),
								}}
							>
								{/* 动态光效 */}
								<div className="absolute inset-0 bg-gradient-to-tr from-white/20 to-transparent" />

								{/* 模板图标 - 替换首字母 */}
								<div className="relative z-10">
									{getTemplateIcon(template.id)}
								</div>

								{/* 底部渐变 */}
								<div className="absolute bottom-0 left-0 right-0 h-1/3 bg-gradient-to-t from-black/20 to-transparent" />
							</div>

							{/* 选中状态指示器 - 更炫酷 */}
							{isSelected && (
								<div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg">
									<Check className="w-3 h-3 text-white" />
								</div>
							)}

							{/* 悬停光效 */}
							<div
								className={`absolute inset-0 bg-gradient-to-r from-purple-400/0 via-white/10 to-purple-400/0 transition-all duration-500 ${
									isSelected
										? "opacity-100"
										: "opacity-0 group-hover:opacity-100"
								}`}
							/>
						</div>

						{/* 右侧：模板信息 */}
						<div className="flex-1 min-w-0">
							<div className="flex items-center justify-between mb-2">
								<h5 className="font-semibold text-gray-900 text-base truncate">
									{getTranslatedTemplateName(
										template.id,
										travelStatT,
									)}
								</h5>
								{isSelected && (
									<div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-medium px-2 py-1 rounded-full">
										{travelStatT.templateSelector.selected()}
									</div>
								)}
							</div>

							<p className="text-sm text-gray-600 line-clamp-2 mb-3 leading-relaxed">
								{getTranslatedTemplateDescription(
									template.id,
									travelStatT,
								)}
							</p>
						</div>
					</div>

					{/* 悬停时的底部光效 */}
					<div
						className={`absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-purple-500/50 to-transparent transition-opacity duration-300 ${
							isSelected
								? "opacity-100"
								: "opacity-0 group-hover:opacity-100"
						}`}
					/>
				</div>
			</Card>
		</button>
	);
}

interface GalleryTemplateCardProps {
	template: CardTemplate;
	isSelected: boolean;
	onSelect: () => void;
}

function GalleryTemplateCard({
	template,
	isSelected,
	onSelect,
}: GalleryTemplateCardProps) {
	return (
		<button
			type="button"
			className={`flex-shrink-0 w-52 cursor-pointer transition-all duration-300 border-none bg-transparent p-0 group ${
				isSelected ? "scale-105" : "hover:scale-102"
			}`}
			onClick={onSelect}
		>
			<Card
				className={`relative overflow-hidden backdrop-blur-sm transition-all duration-300 ${
					isSelected
						? "ring-4 ring-blue-500 shadow-2xl shadow-blue-200/50"
						: "shadow-lg hover:shadow-2xl hover:shadow-gray-200/50"
				}`}
			>
				{/* 模板预览图 - 压缩高度 */}
				<div className="aspect-[4/3] relative">
					<div
						className="w-full h-full relative overflow-hidden"
						style={{
							background: getTemplateBackground(template.id),
						}}
					>
						{/* 酷炫背景动画效果 */}
						<div className="absolute inset-0 opacity-30">
							<div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-white/20 to-transparent" />
							<div className="absolute -top-10 -right-10 w-20 h-20 bg-white/10 rounded-full blur-xl group-hover:scale-150 transition-transform duration-500" />
							<div className="absolute -bottom-5 -left-5 w-16 h-16 bg-white/10 rounded-full blur-lg group-hover:scale-125 transition-transform duration-700" />
						</div>

						{/* 模板预览内容 */}
						<div className="relative w-full h-full flex flex-col items-center justify-center text-white p-4">
							{/* 模板图标 - 替换首字母 */}
							<div className="mb-2">
								{getTemplateIcon(template.id)}
							</div>
							<div className="text-center space-y-1">
								<div className="text-base font-bold drop-shadow-md">
									{template.name}
								</div>
								<div className="text-xs opacity-90 font-medium">
									{getCategoryName(template.id)}
								</div>
							</div>
						</div>
					</div>

					{/* 酷炫的选中状态覆盖层 */}
					{isSelected && (
						<div className="absolute inset-0 bg-gradient-to-br from-blue-600/20 to-purple-600/20 flex items-center justify-center backdrop-blur-sm">
							<div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center shadow-2xl animate-pulse">
								<Check className="w-6 h-6 text-white" />
							</div>
						</div>
					)}

					{/* 推荐标签 - 更炫酷的样式 */}
					{template.id === "minimal" && (
						<div className="absolute top-2 left-2">
							<Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold shadow-lg animate-pulse">
								<Sparkles className="w-3 h-3 mr-1" />
								推荐
							</Badge>
						</div>
					)}

					{/* 光晕效果 */}
					<div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
						<div className="absolute inset-0 bg-gradient-to-t from-transparent via-transparent to-white/10" />
					</div>
				</div>

				{/* 底部信息 - 压缩空间 */}
				<div className="p-3 bg-gradient-to-b from-white to-gray-50">
					<div className="text-center space-y-1.5">
						<h5 className="font-bold text-gray-900 text-sm">
							{template.name}
						</h5>
						<p className="text-xs text-gray-600 line-clamp-1">
							{template.description}
						</p>

						{/* 平台支持 - 更紧凑的设计 */}
						<div className="flex flex-wrap gap-1 mt-2">
							{/* 显示所有平台 */}
							{[
								"instagram",
								"wechat",
								"weibo",
								"twitter",
								"facebook",
							]
								.slice(0, 3)
								.map((platform) => (
									<Badge
										key={platform}
										className="text-xs px-2 py-1 bg-gray-100 text-gray-600"
									>
										{
											{
												instagram: "📷",
												wechat: "💬",
												weibo: "🔥",
												twitter: "🐦",
												facebook: "👥",
											}[
												platform as
													| "instagram"
													| "wechat"
													| "weibo"
													| "twitter"
													| "facebook"
											]
										}
									</Badge>
								))}
						</div>
					</div>
				</div>
			</Card>
		</button>
	);
}

// 工具函数
function getPlatformName(platform: SocialPlatform): string {
	const names = {
		instagram: "Instagram",
		wechat: "微信",
		weibo: "微博",
		twitter: "Twitter",
		facebook: "Facebook",
	};
	return names[platform] || platform;
}

function getPlatformIcon(platform: SocialPlatform): string {
	const icons = {
		instagram: "📷",
		wechat: "💬",
		weibo: "🔥",
		twitter: "🐦",
		facebook: "👥",
	};
	return icons[platform] || "📱";
}

function getCategoryName(category: string): string {
	const names = {
		minimal: "极简",
		vibrant: "活力",
		elegant: "优雅",
		retro: "复古",
	};
	return names[category as keyof typeof names] || category;
}

function getTemplateGradient(category: string): string {
	const gradients = {
		minimal: "bg-gradient-to-br from-gray-100 to-gray-300",
		vibrant: "bg-gradient-to-br from-pink-400 to-purple-600",
		elegant: "bg-gradient-to-br from-gray-800 to-black",
		retro: "bg-gradient-to-br from-yellow-400 to-orange-500",
	};
	return gradients[category as keyof typeof gradients] || gradients.minimal;
}

function getTemplateBackground(templateId: string): string {
	const backgrounds = {
		minimal:
			"linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)",
		vibrant:
			"linear-gradient(135deg, #ff6b6b 0%, #ffd93d 25%, #6bcf7f 50%, #4ecdc4 75%, #45b7d1 100%)",
		elegant:
			"linear-gradient(135deg, #2c3e50 0%, #34495e 25%, #1a252f 50%, #000000 100%)",
		retro: "linear-gradient(135deg, #f7931e 0%, #ffd200 25%, #ff6b6b 50%, #c44569 75%, #f093fb 100%)",
		handdrawn:
			"linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)",
	};
	return (
		backgrounds[templateId as keyof typeof backgrounds] ||
		backgrounds.minimal
	);
}

// 获取模板对应的图标
function getTemplateIcon(templateId: string) {
	const icons = {
		minimal: Minimize2,
		vibrant: Zap,
		elegant: Crown,
		retro: Camera,
		handdrawn: PenTool,
	};

	const IconComponent = icons[templateId as keyof typeof icons] || Minimize2;
	return <IconComponent size={28} className="text-white drop-shadow-lg" />;
}
