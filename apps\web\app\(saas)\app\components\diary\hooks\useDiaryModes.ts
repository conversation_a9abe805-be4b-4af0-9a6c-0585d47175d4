import type { JSO<PERSON>ontent } from "@tiptap/core";
import { useTranslations } from "next-intl";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import {
	getDiaryRichTextDraft,
	saveDiaryRichTextDraft,
} from "../../../services/diary-service";

interface UseDiaryModesProps {
	diaryId: string;
	onSaveDiary?: (options?: { silent?: boolean }) => Promise<void>;
	isDiaryModified?: boolean;
}

export function useDiaryModes({
	diaryId,
	onSaveDiary,
	isDiaryModified,
}: UseDiaryModesProps) {
	const t = useTranslations("travelMemo.diaryEditor");

	// 状态
	const [isRichTextMode, setIsRichTextMode] = useState<boolean>(false);
	const [richTextDraftContent, setRichTextDraftContent] =
		useState<JSONContent | null>(null);
	const [isLoadingDraft, setIsLoadingDraft] = useState<boolean>(false);
	const [isModeTransitioning, setIsModeTransitioning] =
		useState<boolean>(false);

	// 加载富文本草稿
	const loadRichTextDraft = useCallback(async () => {
		setIsLoadingDraft(true);
		try {
			console.log(t("richText.loadingDraft", { diaryId }));
			const draftContent = await getDiaryRichTextDraft(diaryId);

			if (draftContent) {
				console.log(
					t("richText.loadedDraft"),
					JSON.stringify(draftContent),
				);

				let formattedContent = draftContent;

				if (
					!draftContent.type &&
					draftContent.content &&
					!Array.isArray(draftContent.content) &&
					typeof draftContent.content === "object" &&
					(draftContent.content as JSONContent).type === "doc"
				) {
					console.log(t("richText.fixingNestedContent"));
					formattedContent = draftContent.content;
				}

				Promise.resolve().then(() => {
					setRichTextDraftContent(formattedContent);
					console.log(t("richText.draftSetToEditor"), {
						type: formattedContent.type,
						contentCount: formattedContent.content?.length || 0,
					});
				});
			} else {
				console.log(t("richText.noDraftFound"));
			}
		} catch (error) {
			console.error(t("richText.loadDraftFailedError"), error);
		} finally {
			setIsLoadingDraft(false);
		}
	}, [diaryId, t]);

	// 切换编辑模式
	const toggleEditMode = useCallback(async () => {
		console.log("[useDiaryModes] toggleEditMode: 开始切换模式", {
			currentMode: isRichTextMode ? "富文本" : "传统",
			isModeTransitioning,
			isDiaryModified,
			hasRichTextDraft: !!richTextDraftContent,
		});

		if (isModeTransitioning) {
			console.log("[useDiaryModes] toggleEditMode: 正在切换中，跳过");
			return;
		}

		setIsModeTransitioning(true);

		try {
			if (!isRichTextMode && isDiaryModified && onSaveDiary) {
				console.log(
					"[useDiaryModes] toggleEditMode: 保存传统模式的更改",
				);
				toast.info(t("toasts.savingChanges"));
				await onSaveDiary();
			}

			if (isRichTextMode && richTextDraftContent) {
				console.log(
					"[useDiaryModes] toggleEditMode: 从富文本模式切换，有草稿内容",
				);
				toast.info(t("toasts.unformattedContentWarning"));
			}

			await new Promise((resolve) => setTimeout(resolve, 150));

			console.log("[useDiaryModes] toggleEditMode: 执行模式切换", {
				from: isRichTextMode ? "富文本" : "传统",
				to: !isRichTextMode ? "富文本" : "传统",
			});

			setIsRichTextMode(!isRichTextMode);
		} finally {
			setTimeout(() => {
				console.log(
					"[useDiaryModes] toggleEditMode: 切换动画完成，重置状态",
				);
				setIsModeTransitioning(false);
			}, 300);
		}
	}, [
		isRichTextMode,
		isModeTransitioning,
		isDiaryModified,
		richTextDraftContent,
		onSaveDiary,
		t,
	]);

	// 处理富文本内容变化
	const handleRichTextChange = useCallback(
		(content: JSONContent) => {
			console.log(
				"[useDiaryModes] handleRichTextChange: 富文本内容变化",
				{
					isModeTransitioning,
					contentSize: content.content?.length || 0,
					contentType: content.type,
				},
			);

			if (isModeTransitioning) {
				console.log(
					"[useDiaryModes] handleRichTextChange: 正在切换模式，忽略内容变化",
				);
				return;
			}

			setRichTextDraftContent(content);

			console.log("[useDiaryModes] handleRichTextChange: 保存富文本草稿");
			void saveDiaryRichTextDraft(diaryId, content);
		},
		[diaryId, isModeTransitioning],
	);

	return {
		// 状态
		isRichTextMode,
		richTextDraftContent,
		isLoadingDraft,
		isModeTransitioning,

		// 方法
		toggleEditMode,
		handleRichTextChange,
		loadRichTextDraft,
		setRichTextDraftContent,

		// 辅助方法
		setIsRichTextMode,
		setIsModeTransitioning,
	};
}
