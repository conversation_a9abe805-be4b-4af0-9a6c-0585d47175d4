<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="AliAccessStaticViaInstance" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AliArrayNamingShouldHaveBracket" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AliControlFlowStatementWithoutBraces" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AliDeprecation" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AliEqualsAvoidNull" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AliLongLiteralsEndingWithLowercaseL" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AliMissingOverrideAnnotation" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AliWrapperTypeEquality" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAbstractClassShouldStartWithAbstractNaming" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAbstractClassWithoutAbstractMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAccessorClassGeneration" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAccessorMethodGeneration" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAppendCharacterWithChar" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaArrayNamingShouldHaveBracket" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAssignmentToNonFinalStatic" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidApacheBeanUtilsCopy" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidBranchingStatementAsLastInLoop" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidCallStaticSimpleDateFormat" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidCallingFinalize" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidCatchExceptionInSentinelResource" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidCatchingThrowable" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidCommentBehindStatement" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidComplexCondition" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidConcurrentCompetitionRandom" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidDeeplyNestedIfStmts" enabled="true" level="MAJOR" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidDollarSigns" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidDoubleOrFloatEqualCompare" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidFieldNameMatchingMethodName" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidFieldNameMatchingTypeName" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidManuallyCreateThread" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidMissUseOfMathRandom" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidMultipleUnaryOperators" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidNegationOperator" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidNewDateGetTime" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidPatternCompileInMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidReassigningCatchVariables" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidReassigningLoopVariables" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidReassigningParameters" enabled="true" level="CRITICAL" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidReturnInFinally" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidStartWithDollarAndUnderLineNaming" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidThrowingNullPointerException" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidThrowingRawExceptionTypes" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidUseTimer" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidUsingHardCodedIP" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaAvoidUsingOctalValues" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaBigDecimalAvoidDoubleConstructor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaBigIntegerInstantiation" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaBlockExceptionHandler" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaBooleanPropertyShouldNotStartWithIs" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaBrokenNullCheck" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaCheckResultSet" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaCheckSkipResult" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaClassCastExceptionWithSubListToArrayList" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaClassCastExceptionWithToArray" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaClassMustHaveAuthor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaCloneMethodMustImplementCloneable" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaCloseResource" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaCognitiveComplexity" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaCollectionInitShouldAssignCapacity" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaCommentContent" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaCommentsMustBeJavadocFormat" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaConcurrentExceptionWithModifyOriginSubList" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaConsecutiveAppendsShouldReuse" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaConsecutiveLiteralAppends" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaConstantFieldShouldBeUpperCase" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaConstructorCallsOverridableMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaCountDownShouldInFinally" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaCouplingBetweenObjects" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaCyclomaticComplexity" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaDontModifyInForeachCircle" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaDoubleCheckedLocking" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaEmptyControlStatement" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaEqualsAvoidNull" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaExceptionAsFlowControl" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaExceptionClassShouldEndWithException" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaExceptionShouldBeHandled" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaExcessiveImports" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaExcessiveParameterList" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaExcessivePublicCount" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaFieldDeclarationsShouldBeAtStartOfClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaForLoopCanBeForeach" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaHardCodedCryptoKey" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaIbatisMethodQueryForList" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaIdempotentOperations" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaIdenticalCatchBranches" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaInefficientEmptyStringCheck" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaInefficientStringBuffering" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaInsecureCryptoIv" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaInsufficientStringBufferDeclaration" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaInvalidJavaBean" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaInvalidLogMessageFormat" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaJUnitAssertionsShouldIncludeMessage" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaJUnitSpelling" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaJUnitStaticSuite" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaJUnitTestsShouldIncludeAssert" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaJUnitUseExpected" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaLinguisticNaming" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaLiteralsFirstInComparisons" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaLockShouldWithTryFinally" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaLooseCoupling" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaLoosePackageCoupling" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaMethodReturnWrapperType" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaMethodTooLong" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaMethodWithSameNameAsEnclosingClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaMissingOverride" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaNPathComplexity" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaNcssCount" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaNeedBrace" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaNeverSuppressWarningsUnlessYouHaveTo" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaNonSerializableClass" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaNonThreadSafeSingleton" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaOverrideBothEqualsAndHashcode" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaPackageNaming" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaPojoMustOverrideToString" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaPojoMustUsePrimitiveField" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaPojoNoDefaultValue" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaPrematureDeclaration" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaPrimitiveWrapperInstantiation" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaProperCloneImplementation" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaProxyMethodCheck" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaRemoveCommentedCode" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaServiceOrDaoClassShouldEndWithImpl" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaSignatureDeclareThrowsException" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaSimplifiableTestAssertion" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaSimplifyBooleanReturns" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaSingleMethodSingleton" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaSingletonClassReturningNewInstance" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaSingularField" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaSneakyThrowsWithoutExceptionType" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaStringConcat" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaStringInstantiation" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaStringToString" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaSuspiciousHashcodeMethodName" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaSuspiciousOctalEscape" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaSwitchDensity" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaSwitchExpression" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaSwitchStatement" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaTestClassShouldEndWithTestNaming" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaThreadLocalShouldRemove" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaThreadPoolCreation" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaThreadShouldSetName" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaTransactionMustHaveRollback" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUndefineMagicConstant" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUnnecessaryCaseChange" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUnnecessaryCast" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUnnecessaryConstructor" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUnnecessaryConversionTemporary" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUnnecessaryLocalBeforeReturn" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUnnecessaryModifier" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUnnecessaryReturn" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUnsupportedExceptionWithModifyAsList" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUnsynchronizedStaticFormatter" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUnusedAssignment" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUnusedFormalParameter" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUnusedLocalVariable" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUnusedPrivateMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUpperEll" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUseCollectionIsEmpty" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUseIndexOfChar" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUseRightCaseForDateFormat" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUseStringBufferForStringAppends" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUseStringBufferLength" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUseTryWithResources" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUselessOperationOnImmutable" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUselessOverridingMethod" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaUselessStringValueOf" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="AlibabaWrapperTypeEquality" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="IncorrectHttpHeaderInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="customHeaders">
        <set>
          <option value="x-qh-id" />
        </set>
      </option>
    </inspection_tool>
    <inspection_tool class="JavadocDeclaration" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ADDITIONAL_TAGS" value="Author:,author:" />
    </inspection_tool>
    <inspection_tool class="MapOrSetKeyShouldOverrideHashCodeEquals" enabled="true" level="WARNING" enabled_by_default="true" />
  </profile>
</component>