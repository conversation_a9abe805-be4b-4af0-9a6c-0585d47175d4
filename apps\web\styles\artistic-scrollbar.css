/* 旅行主题清新蓝色滚动条样式 */

/* 全局滚动条样式 */
::-webkit-scrollbar {
	width: 8px;
	height: 8px;
}

::-webkit-scrollbar-track {
	background: rgba(240, 248, 255, 0.6);
	border-radius: 12px;
	border: 1px solid rgba(191, 219, 254, 0.3);
}

::-webkit-scrollbar-thumb {
	background: linear-gradient(
		135deg,
		rgba(59, 130, 246, 0.8) 0%,
		rgba(96, 165, 250, 0.9) 50%,
		rgba(147, 197, 253, 0.8) 100%
	);
	border-radius: 12px;
	border: 2px solid transparent;
	background-clip: content-box;
	box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2), inset 0 1px 2px
		rgba(255, 255, 255, 0.5);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

::-webkit-scrollbar-thumb:hover {
	background: linear-gradient(
		135deg,
		rgba(37, 99, 235, 0.9) 0%,
		rgba(59, 130, 246, 1) 50%,
		rgba(96, 165, 250, 0.9) 100%
	);
	box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3), inset 0 1px 3px
		rgba(255, 255, 255, 0.6);
	transform: scale(1.1);
}

::-webkit-scrollbar-corner {
	background: rgba(240, 248, 255, 0.6);
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
	::-webkit-scrollbar-track {
		background: rgba(15, 23, 42, 0.8);
		border: 1px solid rgba(30, 58, 138, 0.3);
	}

	::-webkit-scrollbar-thumb {
		background: linear-gradient(
			135deg,
			rgba(30, 58, 138, 0.9) 0%,
			rgba(59, 130, 246, 0.8) 50%,
			rgba(96, 165, 250, 0.7) 100%
		);
	}
}

/* 专用艺术风格滚动条类 */
.scrollbar-artistic::-webkit-scrollbar {
	width: 6px;
	height: 6px;
}

.scrollbar-artistic::-webkit-scrollbar-track {
	background: rgba(240, 248, 255, 0.4);
	border-radius: 10px;
}

.scrollbar-artistic::-webkit-scrollbar-thumb {
	background: linear-gradient(
		to bottom,
		rgba(147, 197, 253, 0.7),
		rgba(96, 165, 250, 0.8)
	);
	border-radius: 10px;
	border: 1px solid rgba(59, 130, 246, 0.5);
	box-shadow: 0 1px 3px rgba(59, 130, 246, 0.1);
}

.scrollbar-artistic::-webkit-scrollbar-thumb:hover {
	background: linear-gradient(
		to bottom,
		rgba(96, 165, 250, 0.9),
		rgba(59, 130, 246, 0.9)
	);
	border: 1px solid rgba(37, 99, 235, 0.7);
}

/* Firefox 支持 */
html {
	scrollbar-width: thin;
	scrollbar-color: rgba(59, 130, 246, 0.8) rgba(240, 248, 255, 0.6);
}

@media (prefers-color-scheme: dark) {
	html {
		scrollbar-color: rgba(59, 130, 246, 0.8) rgba(15, 23, 42, 0.8);
	}
}

/* 旅行主题增强效果 */
/* 渐变动画滚动条 */
.scrollbar-travel-gradient::-webkit-scrollbar {
	width: 10px;
	height: 10px;
}

.scrollbar-travel-gradient::-webkit-scrollbar-track {
	background: linear-gradient(
		45deg,
		rgba(240, 248, 255, 0.8) 0%,
		rgba(219, 234, 254, 0.6) 50%,
		rgba(240, 248, 255, 0.8) 100%
	);
	border-radius: 15px;
	border: 2px solid rgba(147, 197, 253, 0.3);
}

.scrollbar-travel-gradient::-webkit-scrollbar-thumb {
	background: linear-gradient(
		45deg,
		rgba(34, 197, 94, 0.8) 0%,
		rgba(59, 130, 246, 0.9) 30%,
		rgba(168, 85, 247, 0.8) 60%,
		rgba(236, 72, 153, 0.9) 100%
	);
	background-size: 200% 200%;
	animation: travel-gradient 3s ease-in-out infinite;
	border-radius: 15px;
	border: 2px solid transparent;
	background-clip: content-box;
	box-shadow: 0 3px 15px rgba(59, 130, 246, 0.3), inset 0 1px 3px
		rgba(255, 255, 255, 0.7);
}

.scrollbar-travel-gradient::-webkit-scrollbar-thumb:hover {
	animation-duration: 1.5s;
	box-shadow: 0 5px 20px rgba(59, 130, 246, 0.4), inset 0 2px 4px
		rgba(255, 255, 255, 0.8);
	transform: scale(1.05);
}

@keyframes travel-gradient {
	0%,
	100% {
		background-position: 0% 50%;
	}
	50% {
		background-position: 100% 50%;
	}
}

/* 海洋波浪效果滚动条 */
.scrollbar-ocean::-webkit-scrollbar {
	width: 12px;
	height: 12px;
}

.scrollbar-ocean::-webkit-scrollbar-track {
	background: radial-gradient(
		ellipse at center,
		rgba(240, 248, 255, 0.9) 0%,
		rgba(219, 234, 254, 0.7) 50%,
		rgba(191, 219, 254, 0.8) 100%
	);
	border-radius: 20px;
	position: relative;
}

.scrollbar-ocean::-webkit-scrollbar-thumb {
	background: linear-gradient(
		180deg,
		rgba(3, 105, 161, 0.9) 0%,
		rgba(59, 130, 246, 0.8) 25%,
		rgba(147, 197, 253, 0.9) 50%,
		rgba(59, 130, 246, 0.8) 75%,
		rgba(3, 105, 161, 0.9) 100%
	);
	border-radius: 20px;
	border: 3px solid rgba(255, 255, 255, 0.4);
	background-clip: content-box;
	box-shadow: 0 4px 20px rgba(59, 130, 246, 0.4), inset 0 2px 8px
		rgba(255, 255, 255, 0.6), 0 0 0 1px rgba(147, 197, 253, 0.5);
	animation: ocean-wave 2s ease-in-out infinite;
}

@keyframes ocean-wave {
	0%,
	100% {
		background-size: 100% 100%;
		filter: hue-rotate(0deg);
	}
	50% {
		background-size: 120% 120%;
		filter: hue-rotate(10deg);
	}
}

.scrollbar-ocean::-webkit-scrollbar-thumb:hover {
	animation-duration: 1s;
	background: linear-gradient(
		180deg,
		rgba(3, 105, 161, 1) 0%,
		rgba(59, 130, 246, 0.95) 25%,
		rgba(147, 197, 253, 1) 50%,
		rgba(59, 130, 246, 0.95) 75%,
		rgba(3, 105, 161, 1) 100%
	);
}
