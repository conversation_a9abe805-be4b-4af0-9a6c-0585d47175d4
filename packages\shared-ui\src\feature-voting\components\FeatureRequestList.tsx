"use client";

import { useFeatureVoting } from "../hooks";
import type { FeatureRequestListProps } from "../types";
import { FeatureRequestItem } from "./FeatureRequestItem";

/**
 * 特性请求列表组件
 */
export function FeatureRequestList({
	productId,
	showVoteCounts = true,
	allowVoting = true,
	allowComments = true,
	className,
	ui,
}: FeatureRequestListProps) {
	const {
		Alert,
		AlertDescription,
		Button,
		AlertCircle,
		Loader2,
		RefreshCw,
		cn,
	} = ui;

	const { featureRequests, isLoading, error, vote, unvote, refresh } =
		useFeatureVoting({
			productId,
			showVoteCounts,
			autoRefresh: true,
			refreshInterval: 60000, // 1分钟自动刷新
		});

	const handleVote = async (featureRequestId: string) => {
		try {
			await vote(featureRequestId);
		} catch (error) {
			console.error("投票失败:", error);
		}
	};

	const handleUnvote = async (featureRequestId: string) => {
		try {
			await unvote(featureRequestId);
		} catch (error) {
			console.error("取消投票失败:", error);
		}
	};

	const handleRefresh = async () => {
		try {
			await refresh();
		} catch (error) {
			console.error("刷新失败:", error);
		}
	};

	if (isLoading && featureRequests.length === 0) {
		return (
			<div
				className={cn(
					"flex items-center justify-center py-12",
					className,
				)}
			>
				<div className="flex items-center gap-2 text-muted-foreground">
					<Loader2 className="h-5 w-5 animate-spin" />
					<span>加载中...</span>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className={cn("space-y-4", className)}>
				<Alert variant="error">
					<AlertCircle className="h-4 w-4" />
					<AlertDescription>{error}</AlertDescription>
				</Alert>
				<div className="flex justify-center">
					<Button
						variant="outline"
						onClick={handleRefresh}
						disabled={isLoading}
					>
						<RefreshCw className="h-4 w-4 mr-2" />
						重试
					</Button>
				</div>
			</div>
		);
	}

	if (featureRequests.length === 0) {
		return (
			<div className={cn("text-center py-12", className)}>
				<div className="text-muted-foreground">
					<p className="text-lg font-medium mb-2">暂无特性请求</p>
					<p className="text-sm">成为第一个提交特性请求的用户吧！</p>
				</div>
			</div>
		);
	}

	return (
		<div className={cn("space-y-4", className)}>
			{/* 刷新按钮 */}
			<div className="flex justify-between items-center">
				<p className="text-sm text-muted-foreground">
					共 {featureRequests.length} 个特性请求
				</p>
				<Button
					variant="ghost"
					size="sm"
					onClick={handleRefresh}
					disabled={isLoading}
				>
					<RefreshCw
						className={cn(
							"h-4 w-4 mr-2",
							isLoading && "animate-spin",
						)}
					/>
					刷新
				</Button>
			</div>

			{/* 特性请求列表 */}
			<div className="space-y-4">
				{featureRequests.map((featureRequest) => (
					<FeatureRequestItem
						key={featureRequest.id}
						featureRequest={featureRequest}
						showVoteCount={showVoteCounts}
						allowVoting={allowVoting}
						allowComments={allowComments}
						onVote={handleVote}
						onUnvote={handleUnvote}
						ui={ui}
					/>
				))}
			</div>

			{/* 加载更多指示器 */}
			{isLoading && featureRequests.length > 0 && (
				<div className="flex justify-center py-4">
					<div className="flex items-center gap-2 text-muted-foreground">
						<Loader2 className="h-4 w-4 animate-spin" />
						<span className="text-sm">更新中...</span>
					</div>
				</div>
			)}
		</div>
	);
}
