"use client";

import { useTranslations } from "next-intl";

/**
 * 旅行统计工具多语言Hook
 * 提供类型安全的翻译功能
 */
export function useTravelStatTranslations() {
	const t = useTranslations("travelStat");

	return {
		meta: {
			title: () => t("meta.title"),
			description: () => t("meta.description"),
		},
		navigation: {
			editMode: () => t("navigation.editMode"),
			cardGeneration: () => t("navigation.cardGeneration"),
			switchToEdit: () => t("navigation.switchToEdit"),
			switchToCard: () => t("navigation.switchToCard"),
			addFootprintsFirst: () => t("navigation.addFootprintsFirst"),
			addPointsToGenerate: () => t("navigation.addPointsToGenerate"),
			generateCard: () => t("navigation.generateCard"),
		},
		addPointPanel: {
			selectedLocation: () => t("addPointPanel.selectedLocation"),
			addToMap: () => t("addPointPanel.addToMap"),
			adding: () => t("addPointPanel.adding"),
			addDetailedInfo: () => t("addPointPanel.addDetailedInfo"),
			date: () => t("addPointPanel.date"),
			photo: () => t("addPointPanel.photo"),
			description: () => t("addPointPanel.description"),
			clickToUpload: () => t("addPointPanel.clickToUpload"),
			uploading: () => t("addPointPanel.uploading"),
			jpgPngMax20mb: () => t("addPointPanel.jpgPngMax20mb"),
			shareYourFeelings: () => t("addPointPanel.shareYourFeelings"),
			selectImageFile: () => t("addPointPanel.selectImageFile"),
			imageTooLarge: () => t("addPointPanel.imageTooLarge"),
			imageProcessFailed: () => t("addPointPanel.imageProcessFailed"),
			addSuccess: () => t("addPointPanel.addSuccess"),
			addFailed: (error: string) =>
				t("addPointPanel.addFailed", { error }),
			preview: () => t("addPointPanel.preview"),
		},
		search: {
			title: () => t("search.title"),
			placeholder: () => t("search.placeholder"),
			noResults: () => t("search.noResults"),
			suggestions: () => t("search.suggestions"),
			recent: () => t("search.recent"),
			popular: () => t("search.popular"),
		},
		stats: {
			title: () => t("stats.title"),
			countries: {
				title: () => t("stats.countries.title"),
				count: (count: number) => t("stats.countries.count", { count }),
			},
			cities: {
				title: () => t("stats.cities.title"),
				count: (count: number) => t("stats.cities.count", { count }),
			},
			points: {
				title: () => t("stats.points.title"),
				count: (count: number) => t("stats.points.count", { count }),
			},
			continents: {
				title: () => t("stats.continents.title"),
				count: (count: number) =>
					t("stats.continents.count", { count }),
			},
			distance: {
				title: () => t("stats.distance.title"),
				km: (distance: number) => t("stats.distance.km", { distance }),
				miles: (distance: number) =>
					t("stats.distance.miles", { distance }),
			},
		},
		countryList: {
			title: () => t("countryList.title"),
			emptyState: () => t("countryList.emptyState"),
			colorGuideTitle: () => t("countryList.colorGuideTitle"),
			colorGuideHelpTooltip: () => t("countryList.colorGuideHelpTooltip"),
			colorGuide: {
				currentTheme: () => t("countryList.colorGuide.currentTheme"),
				mapColorDescription: () =>
					t("countryList.colorGuide.mapColorDescription"),
				visitCount: {
					one: () => t("countryList.colorGuide.visitCount.one"),
					two: () => t("countryList.colorGuide.visitCount.two"),
					three: () => t("countryList.colorGuide.visitCount.three"),
					four: () => t("countryList.colorGuide.visitCount.four"),
					five: () => t("countryList.colorGuide.visitCount.five"),
					sixToTen: () =>
						t("countryList.colorGuide.visitCount.sixToTen"),
					tenPlus: () =>
						t("countryList.colorGuide.visitCount.tenPlus"),
				},
				tip: () => t("countryList.colorGuide.tip"),
			},
		},
		operationPanel: {
			title: () => t("operationPanel.title"),
			importData: () => t("operationPanel.importData"),
			exportData: () => t("operationPanel.exportData"),
			clearData: () => t("operationPanel.clearData"),
			confirmClear: {
				title: () => t("operationPanel.confirmClear.title"),
				description: () => t("operationPanel.confirmClear.description"),
				warningText: () => t("operationPanel.confirmClear.warningText"),
				items: {
					travelPoints: () =>
						t("operationPanel.confirmClear.items.travelPoints"),
					countryStats: () =>
						t("operationPanel.confirmClear.items.countryStats"),
					mapData: () =>
						t("operationPanel.confirmClear.items.mapData"),
				},
				confirmText: () => t("operationPanel.confirmClear.confirmText"),
				cancelButton: () =>
					t("operationPanel.confirmClear.cancelButton"),
				confirmButton: () =>
					t("operationPanel.confirmClear.confirmButton"),
			},
		},
		clearDataDialog: {
			title: () => t("clearDataDialog.title"),
			message: () => t("clearDataDialog.message"),
			clearImagesLabel: () => t("clearDataDialog.clearImagesLabel"),
			clearImagesDescription: () =>
				t("clearDataDialog.clearImagesDescription"),
			confirm: () => t("clearDataDialog.confirm"),
			cancel: () => t("clearDataDialog.cancel"),
		},
		searchBox: {
			placeholder: () => t("searchBox.placeholder"),
		},
		resizablePanel: {
			dragHint: () => t("resizablePanel.dragHint"),
			draggingHint: () => t("resizablePanel.draggingHint"),
		},
		templateSelector: {
			noTemplatesForPlatform: (platform: string) =>
				t("templateSelector.noTemplatesForPlatform", { platform }),
			noTemplatesAvailable: () =>
				t("templateSelector.noTemplatesAvailable"),
			selectTemplate: (templateName: string) =>
				t("templateSelector.selectTemplate", { templateName }),
			selected: () => t("templateSelector.selected"),
		},
		templates: {
			names: {
				minimal: () => t("templates.names.minimal"),
				vibrant: () => t("templates.names.vibrant"),
				elegant: () => t("templates.names.elegant"),
				retro: () => t("templates.names.retro"),
				handDrawn: () => t("templates.names.handDrawn"),
			},
			descriptions: {
				minimal: () => t("templates.descriptions.minimal"),
				vibrant: () => t("templates.descriptions.vibrant"),
				elegant: () => t("templates.descriptions.elegant"),
				retro: () => t("templates.descriptions.retro"),
				handDrawn: () => t("templates.descriptions.handDrawn"),
			},
			categories: {
				minimal: () => t("templates.categories.minimal"),
				vibrant: () => t("templates.categories.vibrant"),
				elegant: () => t("templates.categories.elegant"),
				retro: () => t("templates.categories.retro"),
				handDrawn: () => t("templates.categories.handDrawn"),
			},
			categoryDescriptions: {
				minimal: () => t("templates.categoryDescriptions.minimal"),
				vibrant: () => t("templates.categoryDescriptions.vibrant"),
				elegant: () => t("templates.categoryDescriptions.elegant"),
				retro: () => t("templates.categoryDescriptions.retro"),
				handDrawn: () => t("templates.categoryDescriptions.handDrawn"),
			},
		},
		colorThemeSelector: {
			current: () => t("colorThemeSelector.current"),
			recommendedThemes: () => t("colorThemeSelector.recommendedThemes"),
			recommendedSubtitle: () =>
				t("colorThemeSelector.recommendedSubtitle"),
			allThemes: () => t("colorThemeSelector.allThemes"),
			recommended: () => t("colorThemeSelector.recommended"),
			categories: {
				classic: () => t("colorThemeSelector.categories.classic"),
				modern: () => t("colorThemeSelector.categories.modern"),
				nature: () => t("colorThemeSelector.categories.nature"),
				vibrant: () => t("colorThemeSelector.categories.vibrant"),
				minimal: () => t("colorThemeSelector.categories.minimal"),
			},
		},
		colorThemes: {
			names: {
				"classic-blue-green": () =>
					t("colorThemes.names.classic-blue-green"),
				"warm-sunset": () => t("colorThemes.names.warm-sunset"),
				"cool-ocean": () => t("colorThemes.names.cool-ocean"),
				"vibrant-rainbow": () => t("colorThemes.names.vibrant-rainbow"),
				"earth-tones": () => t("colorThemes.names.earth-tones"),
				"purple-pink": () => t("colorThemes.names.purple-pink"),
				monochrome: () => t("colorThemes.names.monochrome"),
				"high-contrast": () => t("colorThemes.names.high-contrast"),
				"pastel-soft": () => t("colorThemes.names.pastel-soft"),
				"neon-bright": () => t("colorThemes.names.neon-bright"),
			},
			descriptions: {
				"classic-blue-green": () =>
					t("colorThemes.descriptions.classic-blue-green"),
				"warm-sunset": () => t("colorThemes.descriptions.warm-sunset"),
				"cool-ocean": () => t("colorThemes.descriptions.cool-ocean"),
				"vibrant-rainbow": () =>
					t("colorThemes.descriptions.vibrant-rainbow"),
				"earth-tones": () => t("colorThemes.descriptions.earth-tones"),
				"purple-pink": () => t("colorThemes.descriptions.purple-pink"),
				monochrome: () => t("colorThemes.descriptions.monochrome"),
				"high-contrast": () =>
					t("colorThemes.descriptions.high-contrast"),
				"pastel-soft": () => t("colorThemes.descriptions.pastel-soft"),
				"neon-bright": () => t("colorThemes.descriptions.neon-bright"),
			},
			levelDescriptions: {
				unvisited: () => t("colorThemes.levelDescriptions.unvisited"),
				firstVisit: () => t("colorThemes.levelDescriptions.firstVisit"),
				secondVisit: () =>
					t("colorThemes.levelDescriptions.secondVisit"),
				multipleVisits: () =>
					t("colorThemes.levelDescriptions.multipleVisits"),
				frequent: () => t("colorThemes.levelDescriptions.frequent"),
				veryFrequent: () =>
					t("colorThemes.levelDescriptions.veryFrequent"),
				extremelyFrequent: () =>
					t("colorThemes.levelDescriptions.extremelyFrequent"),
				superFrequent: () =>
					t("colorThemes.levelDescriptions.superFrequent"),
			},
			getName: (themeId: string) => {
				const key = `colorThemes.names.${themeId}` as any;
				return t(key);
			},
			getDescription: (themeId: string) => {
				const key = `colorThemes.descriptions.${themeId}` as any;
				return t(key);
			},
		},
		mapControls: {
			title: () => t("mapControls.title"),
			export: () => t("mapControls.export"),
			exportHighQuality: () => t("mapControls.exportHighQuality"),
			exporting: () => t("mapControls.exporting"),
			styles: {
				title: () => t("mapControls.styles.title"),
				markers: () => t("mapControls.styles.markers"),
				atmosphere: () => t("mapControls.styles.atmosphere"),
				animation: () => t("mapControls.styles.animation"),
				colors: () => t("mapControls.styles.colors"),
				projection: () => t("mapControls.styles.projection"),
			},
			display: {
				title: () => t("mapControls.display.title"),
				current: () => t("mapControls.display.current"),
				custom: () => t("mapControls.display.custom"),
				detailedSettings: () =>
					t("mapControls.display.detailedSettings"),
				showMarkers: () => t("mapControls.display.showMarkers"),
				showTooltips: () => t("mapControls.display.showTooltips"),
				tooltipDisabledHint: () =>
					t("mapControls.display.tooltipDisabledHint"),
				presets: {
					all: {
						name: () => t("mapControls.display.presets.all.name"),
						description: () =>
							t("mapControls.display.presets.all.description"),
					},
					markersOnly: {
						name: () =>
							t("mapControls.display.presets.markersOnly.name"),
						description: () =>
							t(
								"mapControls.display.presets.markersOnly.description",
							),
					},
					clean: {
						name: () => t("mapControls.display.presets.clean.name"),
						description: () =>
							t("mapControls.display.presets.clean.description"),
					},
				},
			},
		},
		cardGenerator: {
			platforms: {
				title: () => t("cardGenerator.platforms.title"),
				instagram: () => t("cardGenerator.platforms.instagram"),
				wechat: () => t("cardGenerator.platforms.wechat"),
				weibo: () => t("cardGenerator.platforms.weibo"),
				twitter: () => t("cardGenerator.platforms.twitter"),
				facebook: () => t("cardGenerator.platforms.facebook"),
			},
			templates: {
				title: () => t("cardGenerator.templates.title"),
			},
			customization: {
				title: () => t("cardGenerator.customization.title"),
				cardTitle: () => t("cardGenerator.customization.cardTitle"),
				cardTitlePlaceholder: () =>
					t("cardGenerator.customization.cardTitlePlaceholder"),
				footer: () => t("cardGenerator.customization.footer"),
				footerPlaceholder: () =>
					t("cardGenerator.customization.footerPlaceholder"),
				showDate: () => t("cardGenerator.customization.showDate"),
				customDate: () => t("cardGenerator.customization.customDate"),
				customDatePlaceholder: () =>
					t("cardGenerator.customization.customDatePlaceholder"),
			},
			preview: {
				calculating: () => t("cardGenerator.preview.calculating"),
				mapNotLoaded: () => t("cardGenerator.preview.mapNotLoaded"),
				mapExportSuccess: () =>
					t("cardGenerator.preview.mapExportSuccess"),
				mapExportFailed: () =>
					t("cardGenerator.preview.mapExportFailed"),
				cardNotReady: () => t("cardGenerator.preview.cardNotReady"),
				cardExportSuccess: (filename: string) =>
					t("cardGenerator.preview.cardExportSuccess", { filename }),
				cardExportFailed: () =>
					t("cardGenerator.preview.cardExportFailed"),
				exportMapOnly: () => t("cardGenerator.preview.exportMapOnly"),
				exporting: () => t("cardGenerator.preview.exporting"),
				exportCard: () => t("cardGenerator.preview.exportCard"),
			},
			stats: {
				destinations: () => t("cardGenerator.stats.destinations"),
				places: () => t("cardGenerator.stats.places"),
				cities: () => t("cardGenerator.stats.cities"),
				countries: () => t("cardGenerator.stats.countries"),
			},
			defaults: {
				title: () => t("cardGenerator.defaults.title"),
				footer: () => t("cardGenerator.defaults.footer"),
			},
			export: {
				messages: {
					completed: (quality: string) =>
						t("cardGenerator.export.messages.completed", {
							quality,
						}),
				},
			},
		},
		mapStyles: {
			names: {
				streets: () => t("mapStyles.names.streets"),
				outdoors: () => t("mapStyles.names.outdoors"),
				light: () => t("mapStyles.names.light"),
				dark: () => t("mapStyles.names.dark"),
				satellite: () => t("mapStyles.names.satellite"),
				"satellite-streets": () =>
					t("mapStyles.names.satellite-streets"),
				"navigation-day": () => t("mapStyles.names.navigation-day"),
				"navigation-night": () => t("mapStyles.names.navigation-night"),
			},
			descriptions: {
				streets: () => t("mapStyles.descriptions.streets"),
				outdoors: () => t("mapStyles.descriptions.outdoors"),
				light: () => t("mapStyles.descriptions.light"),
				dark: () => t("mapStyles.descriptions.dark"),
				satellite: () => t("mapStyles.descriptions.satellite"),
				"satellite-streets": () =>
					t("mapStyles.descriptions.satellite-streets"),
				"navigation-day": () =>
					t("mapStyles.descriptions.navigation-day"),
				"navigation-night": () =>
					t("mapStyles.descriptions.navigation-night"),
			},
			categories: {
				all: () => t("mapStyles.categories.all"),
				basic: () => t("mapStyles.categories.basic"),
				satellite: () => t("mapStyles.categories.satellite"),
				navigation: () => t("mapStyles.categories.navigation"),
				artistic: () => t("mapStyles.categories.artistic"),
			},
			popover: {
				title: () => t("mapStyles.popover.title"),
				recommended: () => t("mapStyles.popover.recommended"),
				currentStyle: (styleName: string) =>
					t("mapStyles.popover.currentStyle", { styleName }),
				goodWithEffects: () => t("mapStyles.popover.goodWithEffects"),
			},
		},
		mapProjections: {
			names: {
				globe: () => t("mapProjections.names.globe"),
				mercator: () => t("mapProjections.names.mercator"),
				equalEarth: () => t("mapProjections.names.equalEarth"),
				naturalEarth: () => t("mapProjections.names.naturalEarth"),
				winkelTripel: () => t("mapProjections.names.winkelTripel"),
				albers: () => t("mapProjections.names.albers"),
				lambertConformalConic: () =>
					t("mapProjections.names.lambertConformalConic"),
				equirectangular: () =>
					t("mapProjections.names.equirectangular"),
			},
			descriptions: {
				globe: () => t("mapProjections.descriptions.globe"),
				mercator: () => t("mapProjections.descriptions.mercator"),
				equalEarth: () => t("mapProjections.descriptions.equalEarth"),
				naturalEarth: () =>
					t("mapProjections.descriptions.naturalEarth"),
				winkelTripel: () =>
					t("mapProjections.descriptions.winkelTripel"),
				albers: () => t("mapProjections.descriptions.albers"),
				lambertConformalConic: () =>
					t("mapProjections.descriptions.lambertConformalConic"),
				equirectangular: () =>
					t("mapProjections.descriptions.equirectangular"),
			},
			shortDescs: {
				globe: () => t("mapProjections.shortDescs.globe"),
				mercator: () => t("mapProjections.shortDescs.mercator"),
				equalEarth: () => t("mapProjections.shortDescs.equalEarth"),
				naturalEarth: () => t("mapProjections.shortDescs.naturalEarth"),
				winkelTripel: () => t("mapProjections.shortDescs.winkelTripel"),
				albers: () => t("mapProjections.shortDescs.albers"),
				lambertConformalConic: () =>
					t("mapProjections.shortDescs.lambertConformalConic"),
				equirectangular: () =>
					t("mapProjections.shortDescs.equirectangular"),
			},
			badges: {
				globe: () => t("mapProjections.badges.globe"),
				mercator: () => t("mapProjections.badges.mercator"),
				equalEarth: () => t("mapProjections.badges.equalEarth"),
				naturalEarth: () => t("mapProjections.badges.naturalEarth"),
				winkelTripel: () => t("mapProjections.badges.winkelTripel"),
				albers: () => t("mapProjections.badges.albers"),
				lambertConformalConic: () =>
					t("mapProjections.badges.lambertConformalConic"),
				equirectangular: () =>
					t("mapProjections.badges.equirectangular"),
			},
			categories: {
				common: () => t("mapProjections.categories.common"),
				professional: () => t("mapProjections.categories.professional"),
			},
		},
		atmosphere: {
			names: {
				day: () => t("atmosphere.names.day"),
				night: () => t("atmosphere.names.night"),
				sunset: () => t("atmosphere.names.sunset"),
				dawn: () => t("atmosphere.names.dawn"),
				aurora: () => t("atmosphere.names.aurora"),
				"deep-space": () => t("atmosphere.names.deep-space"),
				ocean: () => t("atmosphere.names.ocean"),
				minimal: () => t("atmosphere.names.minimal"),
			},
			descriptions: {
				day: () => t("atmosphere.descriptions.day"),
				night: () => t("atmosphere.descriptions.night"),
				sunset: () => t("atmosphere.descriptions.sunset"),
				dawn: () => t("atmosphere.descriptions.dawn"),
				aurora: () => t("atmosphere.descriptions.aurora"),
				"deep-space": () => t("atmosphere.descriptions.deep-space"),
				ocean: () => t("atmosphere.descriptions.ocean"),
				minimal: () => t("atmosphere.descriptions.minimal"),
			},
			popover: {
				title: () => t("atmosphere.popover.title"),
				currentAtmosphere: (atmosphereName: string) =>
					t("atmosphere.popover.currentAtmosphere", {
						atmosphereName,
					}),
				starIntensity: () => t("atmosphere.popover.starIntensity"),
				horizonBlend: () => t("atmosphere.popover.horizonBlend"),
				starfield: () => t("atmosphere.popover.starfield"),
			},
		},
		animation: {
			names: {
				"shooting-stars": () => t("animation.names.shooting-stars"),
				"floating-particles": () =>
					t("animation.names.floating-particles"),
				aurora: () => t("animation.names.aurora"),
				minimal: () => t("animation.names.minimal"),
				galaxy: () => t("animation.names.galaxy"),
				none: () => t("animation.names.none"),
			},
			descriptions: {
				"shooting-stars": () =>
					t("animation.descriptions.shooting-stars"),
				"floating-particles": () =>
					t("animation.descriptions.floating-particles"),
				aurora: () => t("animation.descriptions.aurora"),
				minimal: () => t("animation.descriptions.minimal"),
				galaxy: () => t("animation.descriptions.galaxy"),
				none: () => t("animation.descriptions.none"),
			},
			popover: {
				title: () => t("animation.popover.title"),
				currentTheme: () => t("animation.popover.currentTheme"),
				currentAnimation: (animationName: string) =>
					t("animation.popover.currentAnimation", { animationName }),
			},
		},
		seo: {
			mainTitle: () => t("seo.mainTitle"),
			mainDescription: () => t("seo.mainDescription"),
			features: {
				smartSearch: {
					title: () => t("seo.features.smartSearch.title"),
					description: () =>
						t("seo.features.smartSearch.description"),
				},
				detailedStats: {
					title: () => t("seo.features.detailedStats.title"),
					description: () =>
						t("seo.features.detailedStats.description"),
				},
				socialCards: {
					title: () => t("seo.features.socialCards.title"),
					description: () =>
						t("seo.features.socialCards.description"),
				},
				mapStyles: {
					title: () => t("seo.features.mapStyles.title"),
					description: () => t("seo.features.mapStyles.description"),
				},
				dataManagement: {
					title: () => t("seo.features.dataManagement.title"),
					description: () =>
						t("seo.features.dataManagement.description"),
				},
				responsiveDesign: {
					title: () => t("seo.features.responsiveDesign.title"),
					description: () =>
						t("seo.features.responsiveDesign.description"),
				},
			},
			howToUse: {
				title: () => t("seo.howToUse.title"),
				steps: {
					searchAndAdd: {
						title: () => t("seo.howToUse.steps.searchAndAdd.title"),
						description: () =>
							t("seo.howToUse.steps.searchAndAdd.description"),
					},
					viewStatistics: {
						title: () =>
							t("seo.howToUse.steps.viewStatistics.title"),
						description: () =>
							t("seo.howToUse.steps.viewStatistics.description"),
					},
					generateCards: {
						title: () =>
							t("seo.howToUse.steps.generateCards.title"),
						description: () =>
							t("seo.howToUse.steps.generateCards.description"),
					},
					customizeMapStyle: {
						title: () =>
							t("seo.howToUse.steps.customizeMapStyle.title"),
						description: () =>
							t(
								"seo.howToUse.steps.customizeMapStyle.description",
							),
					},
					exportBackupData: {
						title: () =>
							t("seo.howToUse.steps.exportBackupData.title"),
						description: () =>
							t(
								"seo.howToUse.steps.exportBackupData.description",
							),
					},
					shareToSocial: {
						title: () =>
							t("seo.howToUse.steps.shareToSocial.title"),
						description: () =>
							t("seo.howToUse.steps.shareToSocial.description"),
					},
				},
			},
			faq: {
				title: () => t("seo.faq.title"),
				items: {
					isItFree: {
						question: () => t("seo.faq.items.isItFree.question"),
						answer: () => t("seo.faq.items.isItFree.answer"),
					},
					dataStorage: {
						question: () => t("seo.faq.items.dataStorage.question"),
						answer: () => t("seo.faq.items.dataStorage.answer"),
					},
					deviceSupport: {
						question: () =>
							t("seo.faq.items.deviceSupport.question"),
						answer: () => t("seo.faq.items.deviceSupport.answer"),
					},
					socialPlatforms: {
						question: () =>
							t("seo.faq.items.socialPlatforms.question"),
						answer: () => t("seo.faq.items.socialPlatforms.answer"),
					},
				},
			},
			keywords: {
				tags: () =>
					t("seo.keywords.tags")
						.split(",")
						.map((tag) => tag.trim()),
				description: () => t("seo.keywords.description"),
			},
		},
	};
}

export type TravelStatTranslations = ReturnType<
	typeof useTravelStatTranslations
>;
