import type { CardCustomization } from "../types/cardTypes";

/**
 * 获取自定义日期文本或计算的日期范围
 * @param customization 卡片自定义配置
 * @param travelPoints 旅行点数据
 * @param fallbackText 回退文本
 * @returns 日期文本
 */
export function getDateDisplay(
	customization: CardCustomization,
	travelPoints?: Array<{ date: string; timestamp?: number | string }>,
	fallbackText?: string,
): string {
	// 优先使用自定义日期文本
	if (customization.content?.customDate?.trim()) {
		return customization.content.customDate;
	}

	// 回退到计算的日期范围
	if (travelPoints && travelPoints.length > 0) {
		// 统一转换为Date对象进行比较
		const dates = travelPoints
			.map((point) => {
				if (point.timestamp) {
					return new Date(point.timestamp);
				}
				return new Date(point.date);
			})
			.filter((date) => !Number.isNaN(date.getTime())); // 过滤无效日期

		if (dates.length > 0) {
			const earliestDate = new Date(
				Math.min(...dates.map((d) => d.getTime())),
			);
			const latestDate = new Date(
				Math.max(...dates.map((d) => d.getTime())),
			);

			const startYear = earliestDate.getFullYear();
			const endYear = latestDate.getFullYear();

			return startYear === endYear
				? `${startYear}`
				: `${startYear} - ${endYear}`;
		}
	}

	// 最终回退文本
	return fallbackText || "";
}

/**
 * 计算旅行统计数据
 * @param travelPoints 旅行点数据
 * @returns 统计数据对象
 */
export function calculateTravelStats(
	travelPoints: Array<{ date: string; timestamp?: number | string }>,
) {
	if (!travelPoints || travelPoints.length === 0) {
		return {
			earliestDate: null,
			latestDate: null,
		};
	}

	// 统一转换为Date对象进行比较
	const dates = travelPoints
		.map((point) => {
			if (point.timestamp) {
				return new Date(point.timestamp);
			}
			return new Date(point.date);
		})
		.filter((date) => !Number.isNaN(date.getTime())); // 过滤无效日期

	if (dates.length === 0) {
		return {
			earliestDate: null,
			latestDate: null,
		};
	}

	const earliestDate = new Date(Math.min(...dates.map((d) => d.getTime())));
	const latestDate = new Date(Math.max(...dates.map((d) => d.getTime())));

	return {
		earliestDate: earliestDate.toISOString(),
		latestDate: latestDate.toISOString(),
	};
}
