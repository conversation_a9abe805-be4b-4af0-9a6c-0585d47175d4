"use client";

import { Button, type ButtonProps } from "@ui/components/button";
import { Map as MapIcon } from "lucide-react";
import { useRouter } from "next/navigation";

interface ViewStoryButtonProps extends Omit<ButtonProps, "onClick"> {
	diaryId: string;
	showIcon?: boolean;
	label?: string;
}

export function ViewStoryButton({
	diaryId,
	variant = "primary",
	size = "md",
	showIcon = true,
	label = "查看地图故事",
	className,
	...props
}: ViewStoryButtonProps) {
	const router = useRouter();

	const handleClick = () => {
		router.push(`/app/mapbox/${diaryId}`);
	};

	return (
		<Button
			variant={variant}
			size={size}
			onClick={handleClick}
			className={className}
			{...props}
		>
			{showIcon && <MapIcon className="w-4 h-4 mr-2" />}
			{label}
		</Button>
	);
}
