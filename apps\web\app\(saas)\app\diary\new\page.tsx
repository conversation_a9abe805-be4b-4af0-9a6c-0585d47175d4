"use client";

import { Loader2 } from "lucide-react";
import { useTranslations } from "next-intl"; // 添加导入
import { useRouter } from "next/navigation"; // 导入 useRouter
import { useEffect, useState } from "react"; // 导入 useEffect 和 useState
import { toast } from "sonner"; // 用于显示错误信息
import { createEmptyDiary } from "../../services/diary-service"; // 导入服务函数

// 定义加载状态组件 (可选, 可以用 Shadcn Spinner 或其他)
function LoadingIndicator() {
	const t = useTranslations("newDiaryPage.loadingIndicator");
	return (
		<div className="flex flex-col items-center justify-center h-screen bg-gradient-to-b from-gray-100 to-gray-300">
			<Loader2 className="h-12 w-12 animate-spin text-primary" />
			<p className="mt-4 text-lg text-muted-foreground">
				{t("creating")}
			</p>
		</div>
	);
}

// 定义错误状态组件 (可选)
function ErrorDisplay({ message }: { message: string }) {
	const t = useTranslations("newDiaryPage.errorDisplay");
	return (
		<div className="flex flex-col items-center justify-center h-screen bg-gradient-to-b from-red-100 to-red-300">
			<h2 className="text-2xl font-bold text-destructive mb-2">
				{t("title")}
			</h2>
			<p className="text-destructive-foreground">{message}</p>
			{/* 可以添加重试按钮 */}
		</div>
	);
}

const NewDiaryPage = () => {
	const router = useRouter();
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const errorT = useTranslations("newDiaryPage.errorDisplay");
	const toastT = useTranslations("newDiaryPage.toast");

	useEffect(() => {
		let isMounted = true; // 防止在组件卸载后更新状态

		async function handleCreateEmptyDiary() {
			setIsLoading(true);
			setError(null);
			try {
				console.log("开始创建空日记...");
				const newDiary = await createEmptyDiary();
				console.log("空日记创建成功，ID:", newDiary.id);

				if (isMounted) {
					// 使用 replace 跳转到新日记的编辑页面
					// 假设你的编辑页面路由是 /app/diary/[id]
					router.replace(`/app/diary/${newDiary.id}`);
					// 不需要 setIsLoading(false) 因为页面会跳转
				}
			} catch (err) {
				console.error("处理创建空日记时出错:", err);
				const errorMessage =
					err instanceof Error ? err.message : errorT("unknownError");
				toast.error(`${toastT("createFailedPrefix")}${errorMessage}`);
				if (isMounted) {
					setError(errorMessage);
					setIsLoading(false);
				}
			}
		}

		void handleCreateEmptyDiary();

		// 清理函数
		return () => {
			isMounted = false;
			console.log("NewDiaryPage卸载，清理...");
		};
	}, [router, errorT, toastT]); // 依赖 router 和翻译函数

	if (isLoading) {
		return <LoadingIndicator />;
	}

	if (error) {
		return <ErrorDisplay message={error} />;
	}

	// 理论上，成功后会跳转，不会渲染这个 return
	// 但为了以防万一和明确性，可以保留一个空的 return 或 null
	return null;
};

export default NewDiaryPage;
