"use client";

import { useEffect, useState } from "react";

export function WordsFromHeart() {
	const [currentTestimonial, setCurrentTestimonial] = useState(0);
	const [isVisible, setIsVisible] = useState(false);

	const testimonials = [
		{
			name: "林小雨",
			role: "自由摄影师",
			content:
				"这个应用让我重新爱上了旅行记录。每一次按下快门的瞬间，都能在地图上找到对应的故事。那些散落在世界各地的回忆，现在都有了最温暖的归宿。",
			location: "北京",
			avatar: "🌸",
			color: "from-pink-400 to-rose-500",
		},
		{
			name: "陈文杰",
			role: "背包客",
			content:
				"两年来，我用这个应用记录了30个国家的足迹。看着地图上那些密密麻麻的标记点，就像看到了自己成长的轨迹。每一个点都是一段故事，每一条路线都是一次蜕变。",
			location: "广州",
			avatar: "🎒",
			color: "from-blue-400 to-indigo-500",
		},
		{
			name: "王晓晴",
			role: "文学爱好者",
			content:
				"最喜欢在夜深人静的时候打开这个应用，重读那些旅途中写下的文字。文字配上地图，回忆配上足迹，那种感觉就像是在阅读自己人生的诗集。",
			location: "杭州",
			avatar: "📖",
			color: "from-purple-400 to-violet-500",
		},
		{
			name: "张思远",
			role: "设计师",
			content:
				"作为一个完美主义者，我对应用的设计和用户体验要求很高。这个应用不仅功能强大，界面设计也很有艺术感，让记录旅行变成了一种美的享受。",
			location: "上海",
			avatar: "🎨",
			color: "from-emerald-400 to-teal-500",
		},
	];

	useEffect(() => {
		const timer = setTimeout(() => setIsVisible(true), 500);
		return () => clearTimeout(timer);
	}, []);

	useEffect(() => {
		const interval = setInterval(() => {
			setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
		}, 6000);
		return () => clearInterval(interval);
	}, [testimonials.length]);

	const nextTestimonial = () => {
		setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
	};

	const prevTestimonial = () => {
		setCurrentTestimonial(
			(prev) => (prev - 1 + testimonials.length) % testimonials.length,
		);
	};

	return (
		<section className="py-20 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-slate-900 dark:via-blue-900 dark:to-purple-900">
			<div className="container mx-auto px-4">
				{/* 标题 */}
				<div
					className={`text-center mb-16 transition-all duration-1000 ${isVisible ? "opacity-100 transform translate-y-0" : "opacity-0 transform translate-y-8"}`}
				>
					<h2 className="text-4xl md:text-5xl font-bold text-gray-800 dark:text-white mb-6">
						来自旅行者的
						<span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
							真心话
						</span>
					</h2>
					<p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto leading-relaxed">
						听听那些用心记录生活的人们，分享他们与旅行足迹的美好故事
					</p>
				</div>

				{/* 主要内容区域 */}
				<div className="max-w-5xl mx-auto">
					<div
						className={`transition-all duration-1000 delay-300 ${isVisible ? "opacity-100 transform translate-y-0" : "opacity-0 transform translate-y-8"}`}
					>
						{/* 用户故事卡片 */}
						<div className="relative overflow-hidden">
							<div
								className="flex transition-transform duration-700 ease-in-out"
								style={{
									transform: `translateX(-${currentTestimonial * 100}%)`,
								}}
							>
								{testimonials.map((testimonial, index) => (
									<div
										key={index}
										className="w-full flex-shrink-0 px-4"
									>
										<div className="bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm rounded-3xl p-12 shadow-2xl border border-white/50 dark:border-slate-700/50">
											{/* 引号装饰 */}
											<div className="text-6xl text-gray-200 dark:text-slate-600 mb-6">
												"
											</div>

											{/* 用户故事 */}
											<blockquote className="text-xl md:text-2xl text-gray-700 dark:text-gray-300 leading-relaxed mb-8 italic">
												{testimonial.content}
											</blockquote>

											{/* 用户信息 */}
											<div className="flex items-center gap-6">
												<div
													className={`w-16 h-16 rounded-full bg-gradient-to-br ${testimonial.color} flex items-center justify-center text-2xl shadow-lg`}
												>
													{testimonial.avatar}
												</div>
												<div>
													<h4 className="text-xl font-bold text-gray-800 dark:text-white mb-1">
														{testimonial.name}
													</h4>
													<p className="text-gray-600 dark:text-gray-400">
														{testimonial.role} ·{" "}
														{testimonial.location}
													</p>
												</div>
											</div>
										</div>
									</div>
								))}
							</div>
						</div>

						{/* 导航控制 */}
						<div className="flex items-center justify-center mt-8 gap-8">
							{/* 上一个按钮 */}
							<button
								type="button"
								onClick={prevTestimonial}
								className="w-12 h-12 rounded-full bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm shadow-lg hover:shadow-xl flex items-center justify-center transition-all duration-300 hover:scale-110"
								aria-label="上一个用户故事"
							>
								<svg
									className="w-6 h-6 text-gray-600 dark:text-gray-300"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<title>左箭头</title>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M15 19l-7-7 7-7"
									/>
								</svg>
							</button>

							{/* 指示器 */}
							<div className="flex gap-2">
								{testimonials.map((_, index) => (
									<button
										type="button"
										key={index}
										onClick={() =>
											setCurrentTestimonial(index)
										}
										className={`w-3 h-3 rounded-full transition-all duration-300 ${
											index === currentTestimonial
												? "bg-blue-500 scale-125"
												: "bg-gray-300 dark:bg-slate-600 hover:bg-gray-400 dark:hover:bg-slate-500"
										}`}
										aria-label={`切换到第${index + 1}个用户故事`}
									/>
								))}
							</div>

							{/* 下一个按钮 */}
							<button
								type="button"
								onClick={nextTestimonial}
								className="w-12 h-12 rounded-full bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm shadow-lg hover:shadow-xl flex items-center justify-center transition-all duration-300 hover:scale-110"
								aria-label="下一个用户故事"
							>
								<svg
									className="w-6 h-6 text-gray-600 dark:text-gray-300"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<title>右箭头</title>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M9 5l7 7-7 7"
									/>
								</svg>
							</button>
						</div>
					</div>
				</div>

				{/* 社区统计 */}
				<div
					className={`mt-20 transition-all duration-1000 delay-700 ${isVisible ? "opacity-100 transform translate-y-0" : "opacity-0 transform translate-y-8"}`}
				>
					<div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
						{[
							{ number: "50K+", label: "活跃用户", icon: "👥" },
							{ number: "200K+", label: "足迹记录", icon: "📍" },
							{ number: "1M+", label: "美好回忆", icon: "💝" },
							{ number: "150+", label: "国家地区", icon: "🌍" },
						].map((stat, index) => (
							<div key={index} className="text-center">
								<div className="text-3xl mb-2">{stat.icon}</div>
								<div className="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-2">
									{stat.number}
								</div>
								<div className="text-gray-600 dark:text-gray-400">
									{stat.label}
								</div>
							</div>
						))}
					</div>
				</div>
			</div>
		</section>
	);
}
