import { cn } from "@ui/lib";
import { format } from "date-fns";
import { CalendarIcon, Check, X } from "lucide-react";
import { useEffect, useState } from "react";

interface EditableDateProps {
	value: Date;
	isEditing: boolean;
	onEnterEdit: () => void;
	onExitEdit: (save: boolean) => void;
	onValueChange: (value: Date) => void;
	showActions?: boolean;
	autoOpenCalendar?: boolean;
	useLastSelectedDate?: boolean;
	className?: string;
}

// 全局变量，用于记录上次选择的日期
let globalLastSelectedDate: Date | null = null;

// 安全地转换日期，确保返回有效的Date对象
function ensureValidDate(date: Date | undefined | null): Date {
	if (!date || !(date instanceof Date) || Number.isNaN(date.getTime())) {
		console.warn("EditableDate收到无效日期，使用当前日期:", date);
		return new Date();
	}
	return date;
}

// 获取UTC日期以确保时区一致性
const getUTCDate = (date?: Date) => {
	if (!date) {
		return new Date(
			Date.UTC(
				new Date().getUTCFullYear(),
				new Date().getUTCMonth(),
				new Date().getUTCDate(),
			),
		);
	}
	return new Date(
		Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate()),
	);
};

// 格式化日期显示
const formatDetailDate = (date: Date): string => {
	try {
		const validDate = ensureValidDate(date);
		const localDate = new Date(
			validDate.getTime() - validDate.getTimezoneOffset() * 60000,
		);
		return format(localDate, "PPP");
	} catch (error) {
		console.error("日期格式化错误:", error);
		return "日期格式错误";
	}
};

// 将Date对象转换为HTML date input所需的格式 (YYYY-MM-DD)
const formatDateForInput = (date: Date): string => {
	try {
		const validDate = ensureValidDate(date);
		return format(validDate, "yyyy-MM-dd");
	} catch (error) {
		console.error("日期格式化错误:", error);
		return format(new Date(), "yyyy-MM-dd");
	}
};

// 从HTML date input的值解析Date对象
const parseDateFromInput = (dateString: string): Date => {
	const date = new Date(`${dateString}T12:00:00.000Z`); // 设置为UTC中午避免时区问题
	return getUTCDate(date);
};

export function EditableDate({
	value,
	isEditing,
	onEnterEdit,
	onExitEdit,
	onValueChange,
	showActions = true,
	autoOpenCalendar = true,
	useLastSelectedDate = false,
	className,
}: EditableDateProps) {
	// 初始化本地值：使用上次选择的日期（如果启用且存在）或传入的值
	const getInitialDate = (): Date => {
		if (useLastSelectedDate && globalLastSelectedDate) {
			return globalLastSelectedDate;
		}
		return ensureValidDate(value);
	};

	const [localValue, setLocalValue] = useState(getInitialDate());

	// 当编辑状态改变时，同步本地值
	useEffect(() => {
		if (isEditing) {
			const initialDate = getInitialDate();
			setLocalValue(initialDate);
		}
	}, [isEditing, value, useLastSelectedDate]);

	const handleSave = () => {
		onValueChange(localValue);

		// 保存为上次选择的日期
		globalLastSelectedDate = localValue;
		console.log("保存日期并记录为上次选择:", localValue);

		onExitEdit(true);
	};

	const handleCancel = () => {
		setLocalValue(ensureValidDate(value));
		onExitEdit(false);
	};

	const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const newDate = parseDateFromInput(e.target.value);
		setLocalValue(newDate);
		console.log("日期变更:", newDate);
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		e.stopPropagation(); // 防止触发父组件的键盘事件

		if (e.key === "Enter") {
			e.preventDefault();
			handleSave();
		} else if (e.key === "Escape") {
			e.preventDefault();
			handleCancel();
		}
	};

	if (isEditing) {
		return (
			<div className={cn("flex items-center gap-2", className)}>
				<div className="flex items-center gap-2 flex-1">
					<CalendarIcon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
					<input
						type="date"
						value={formatDateForInput(localValue)}
						onChange={handleDateChange}
						onKeyDown={handleKeyDown}
						className="h-8 px-2 text-sm border border-input rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent flex-1"
						style={{ minWidth: "140px" }}
					/>
				</div>
				{showActions && (
					<div className="edit-actions flex items-center gap-1">
						<button
							type="button"
							onClick={handleSave}
							className="p-1 rounded hover:bg-green-100 text-green-600 transition-colors"
							title="保存 (Enter)"
						>
							<Check className="h-4 w-4" />
						</button>
						<button
							type="button"
							onClick={handleCancel}
							className="p-1 rounded hover:bg-red-100 text-red-600 transition-colors"
							title="取消 (Esc)"
						>
							<X className="h-4 w-4" />
						</button>
					</div>
				)}
			</div>
		);
	}

	return (
		<button
			type="button"
			className={cn(
				"text-sm text-muted-foreground cursor-pointer hover:bg-muted/50 rounded px-1 py-0.5 transition-colors text-left inline-flex items-center",
				className,
			)}
			onClick={onEnterEdit}
			onKeyDown={(e) => {
				if (e.key === "Enter" || e.key === " ") {
					e.preventDefault();
					onEnterEdit();
				}
			}}
			title="点击编辑日期"
		>
			<CalendarIcon className="mr-1 h-3 w-3 text-muted-foreground" />
			{formatDetailDate(ensureValidDate(value))}
		</button>
	);
}
