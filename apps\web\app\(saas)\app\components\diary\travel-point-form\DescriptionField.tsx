import { Textarea } from "@ui/components/textarea";
import { AlignLeft } from "lucide-react";
import { useTranslations } from "next-intl";
import { useId } from "react";
import type { FormFieldProps } from "./types";

export function DescriptionField({ formData, setFormData }: FormFieldProps) {
	const t = useTranslations();
	const descriptionId = useId();

	return (
		<div>
			<label
				htmlFor={descriptionId}
				className="block text-sm font-medium mb-1"
			>
				{t("travelMemo.travelPointForm.labels.description")}
			</label>
			<div className="flex items-start">
				<AlignLeft className="w-5 h-5 mr-2 text-travel-dark/60 flex-shrink-0 mt-2" />
				<Textarea
					id={descriptionId}
					placeholder={t(
						"travelMemo.travelPointForm.placeholders.describeExperience",
					)}
					value={formData.description || ""}
					onChange={(e) =>
						setFormData({
							...formData,
							description: e.target.value,
						})
					}
					className="min-h-[100px]"
				/>
			</div>
		</div>
	);
}
