---
description: 
globs: *.tsx,*.ts
alwaysApply: false
---
# Performance Optimization Guide

## Server Components
- Use React Server Components (RSC) by default
- Minimize 'use client' directives
- Keep client components small and focused
- Use streaming and Suspense boundaries

## Data Fetching
- Implement proper caching strategies
- Use React Suspense for loading states
- Handle errors gracefully
- Optimize API routes

## Code Splitting
- Use dynamic imports for large components
- Implement proper code splitting boundaries
- Lazy load non-critical components
- Use route groups for organization

## Image Optimization
- Use Next.js Image component
- Implement proper image sizing
- Use WebP format when possible
- Implement lazy loading
- Include width and height attributes

## State Management
- Minimize client-side state
- Use URL state with `nuqs` when appropriate
- Implement proper loading indicators
- Handle error states consistently

## Web Vitals
- Optimize for Core Web Vitals
- Monitor LCP (Largest Contentful Paint)
- Minimize CLS (Cumulative Layout Shift)
- Improve FID (First Input Delay)
- Use proper caching strategies

## Build Optimization
- Minimize bundle size
- Remove unused code
- Optimize dependencies
- Use proper tree shaking
- Implement proper caching headers
