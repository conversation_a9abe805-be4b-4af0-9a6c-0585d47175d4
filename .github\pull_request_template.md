## 📝 Description

<!-- 简要描述此 PR 的变更内容 -->

## 🔄 Type of Change

- [ ] 🐛 Bug fix (non-breaking change which fixes an issue)
- [ ] ✨ New feature (non-breaking change which adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] 📚 Documentation update
- [ ] 🎨 Style changes (formatting, renaming, etc.)
- [ ] ♻️ Code refactoring (no functional changes)
- [ ] ⚡ Performance improvements
- [ ] 🧪 Adding tests
- [ ] 🔧 Build/CI changes

## 🧪 Testing

<!-- 描述你如何测试了这些变更 -->

- [ ] 测试通过本地开发环境
- [ ] 添加了单元测试
- [ ] 添加了集成测试
- [ ] 手动测试了相关功能

## 📋 Checklist

- [ ] 我的代码遵循项目的代码规范
- [ ] 我已经对我的代码进行了自我审查
- [ ] 我已经为我的代码添加了注释，特别是在难以理解的地方
- [ ] 我已经更新了相关文档
- [ ] 我的更改不会产生新的警告
- [ ] 我已经添加了测试来证明我的修复有效或我的功能正常工作
- [ ] 新的和现有的单元测试都通过了本地测试

## 📸 Screenshots (if applicable)

<!-- 如果有 UI 变更，请添加截图 -->

## 🔗 Related Issues

<!-- 引用相关的 issue，例如：Fixes #123, Closes #456 -->

## 📱 Mobile Testing

- [ ] 已在移动设备上测试
- [ ] 响应式设计正常工作

## 🌐 Browser Testing

- [ ] Chrome
- [ ] Firefox  
- [ ] Safari
- [ ] Edge

## 🚨 Breaking Changes

<!-- 如果有破坏性变更，请详细说明 -->

## 📋 Additional Notes

<!-- 任何其他相关信息 -->