"use client";

import { useEffect, useState } from "react";
import { fetchTravelDiary } from "../../../../mock-data";

export function useDiaryData(initialDiaryId?: string) {
	const [diary, setDiary] = useState<any>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<Error | null>(null);

	useEffect(() => {
		const loadDiary = async () => {
			try {
				setIsLoading(true);
				setError(null);
				// 使用URL中的diaryId或传入的diaryId或默认使用关西旅行
				let diaryId = initialDiaryId;
				if (!diaryId && typeof window !== "undefined") {
					diaryId =
						window.location.pathname.split("/").pop() ||
						"kansai-trip";
				}

				const data = await fetchTravelDiary(diaryId || "kansai-trip");
				if (data) {
					setDiary(data);
				} else {
					throw new Error("未找到旅行日记数据");
				}
			} catch (error) {
				console.error("加载旅行日记失败", error);
				setError(
					error instanceof Error
						? error
						: new Error("加载旅行日记失败"),
				);
			} finally {
				setIsLoading(false);
			}
		};

		loadDiary();
	}, [initialDiaryId]);

	return { diary, isLoading, error };
}
