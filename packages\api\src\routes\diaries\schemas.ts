// 从 database 包导入共享的 schema
import {
	travelDiaryContentSchema,
	travelPointImageSchema,
} from "@repo/database/src/schemas/travel-diary";
import type { TravelPointImage } from "@repo/database/src/types/travel-diary";
import { z } from "zod";
import { TiptapContentSchema } from "./tiptap-schema"; // Assuming a shared schema for Tiptap content

// ID参数的schema
export const diaryIdParamSchema = z.object({
	id: z.string().min(1, "日记ID不能为空"),
});

// 添加视频导出任务参数的schema
export const exportVideoTaskParamSchema = z.object({
	id: z.string().min(1, "日记ID不能为空"),
	taskId: z.string().min(1, "任务ID不能为空"),
});

// 创建日记的输入schema
export const createDiarySchema = z.object({
	title: z.string().min(1, "标题不能为空"),
	subtitle: z.string().nullish(),
	coverImage: z.string().url("封面图片必须是有效的URL").nullish(),
	// 使用共享的 schema
	content: travelDiaryContentSchema, // 创建时 content 是必需的
	isPublic: z.boolean().optional().default(false),
});

// 更新日记的输入schema
export const updateDiarySchema = z.object({
	title: z.string().min(1, "标题不能为空").optional(),
	subtitle: z.string().nullish(),
	coverImage: z.string().url("封面图片必须是有效的URL").nullish(),
	// 使用共享的 schema，并设为可选
	content: travelDiaryContentSchema.optional(),
	richTextDraftContent: TiptapContentSchema.nullish(), // 新增: 富文本草稿，可选
	isPublic: z.boolean().optional(),
});

// 日记列表响应schema - 简化版本，不包含完整的content
export const diaryListResponseSchema = z.object({
	diaries: z.array(
		z.object({
			id: z.string(),
			title: z.string(),
			subtitle: z.string().nullable(),
			coverImage: z.string().nullable(),
			isPublic: z.boolean(),
			createdAt: z.string(),
			updatedAt: z.string(),
			// 注意：列表响应不包含完整的content和userId，以减少数据传输
		}),
	),
});
export type DiaryListResponseOutput = z.infer<typeof diaryListResponseSchema>;

// 单个日记响应schema
export const diaryResponseSchema = z.object({
	diary: z.object({
		id: z.string(),
		userId: z.string(),
		title: z.string(),
		subtitle: z.string().nullable(),
		coverImage: z.string().nullable(),
		isPublic: z.boolean(),
		content: travelDiaryContentSchema, // Keep existing content schema
		richTextDraftContent: TiptapContentSchema.nullable(), // 新增: 返回富文本草稿内容，允许为null
		createdAt: z.string(),
		updatedAt: z.string(),
	}),
});
export type DiaryResponseOutput = z.infer<typeof diaryResponseSchema>;

// 操作结果响应schema
export const operationResultSchema = z.object({
	success: z.boolean(),
	message: z.string().optional(),
});
export type OperationResult = z.infer<typeof operationResultSchema>;

// Schema for footprint data needed by the map
export const footprintSchema = z.object({
	id: z.string(),
	latitude: z.number(),
	longitude: z.number(),
	location: z.string(),
	diaryId: z.string(), // 修改为必填字段，确保每个足迹点都关联到一个日记
	date: z.string().optional(), // 添加日期字段
	description: z.string().optional(), // 添加描述字段
	images: z.array(travelPointImageSchema).optional(), // 修改：使用新的图片对象schema
	iconType: z.string().optional(), // 添加图标类型
	country: z.string().nullish(), // 添加国家
	city: z.string().nullish(), // 添加城市
	title: z.string().optional(), // 添加标题
	timelineId: z.string().optional(), // 添加时间线ID
	timelineTitle: z.string().optional(), // 添加时间线标题
	diaryTitle: z.string().optional(), // 添加日记标题
});
export type Footprint = z.infer<typeof footprintSchema>;

// Schema for the /footprints response
export const footprintListResponseSchema = z.object({
	footprints: z.array(footprintSchema),
});
export type FootprintListResponse = z.infer<typeof footprintListResponseSchema>;

// Schema for the /stats response
export const diaryStatsSchema = z.object({
	countriesVisited: z.number().int().nonnegative(),
	citiesExplored: z.number().int().nonnegative(),
	footprintsLeft: z.number().int().nonnegative(),
	photosTaken: z.number().int().nonnegative(),
});
export type DiaryStats = z.infer<typeof diaryStatsSchema>;

export const diaryStatsResponseSchema = z.object({
	stats: diaryStatsSchema,
});
export type DiaryStatsResponse = z.infer<typeof diaryStatsResponseSchema>;

// 导出图片类型供其他模块使用
export type { TravelPointImage };
