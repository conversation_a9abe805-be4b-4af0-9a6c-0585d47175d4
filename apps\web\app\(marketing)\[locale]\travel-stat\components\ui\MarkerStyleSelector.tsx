"use client";

import { Badge } from "@ui/components/badge";
import { But<PERSON> } from "@ui/components/button";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { Crown, Paintbrush, Palette, Sparkles } from "lucide-react";
import { useState } from "react";
import type { MarkerStyleType } from "../../types/markerTypes";
import {
	GRADIENT_PULSE_THEMES,
	MARKER_STYLE_CONFIGS,
} from "../../types/markerTypes";

// 粒子特效主题配置（内联定义避免导入问题）
const PARTICLE_EFFECT_THEMES = {
	fire: {
		particleColor: "rgb(251, 146, 60)", // orange-400
		particleCount: 8,
		animationSpeed: 1.2,
		orbitRadius: 35,
		name: "火焰",
		description: "热情奔放",
	},
	electric: {
		particleColor: "rgb(59, 130, 246)", // blue-500
		particleCount: 10,
		animationSpeed: 1.5,
		orbitRadius: 40,
		name: "电光",
		description: "动感炫酷",
	},
	magic: {
		particleColor: "rgb(168, 85, 247)", // purple-500
		particleCount: 12,
		animationSpeed: 0.8,
		orbitRadius: 45,
		name: "魔法",
		description: "神秘优雅",
	},
	nature: {
		particleColor: "rgb(34, 197, 94)", // green-500
		particleCount: 6,
		animationSpeed: 1.0,
		orbitRadius: 30,
		name: "自然",
		description: "清新宁静",
	},
};

interface MarkerStyleSelectorProps {
	currentStyle: MarkerStyleType;
	currentTheme?: string;
	onStyleChange: (style: MarkerStyleType) => void;
	onThemeChange: (theme: string) => void;
}

// 注意：MarkerStyleContent 逻辑已迁移到 MarkerStylePopover.tsx

export function MarkerStyleSelector({
	currentStyle,
	currentTheme = "ocean",
	onStyleChange,
	onThemeChange,
}: MarkerStyleSelectorProps) {
	const [isOpen, setIsOpen] = useState(false);

	const currentConfig = MARKER_STYLE_CONFIGS[currentStyle];

	// 按分类分组风格
	const stylesByCategory = Object.values(MARKER_STYLE_CONFIGS).reduce(
		(acc, config) => {
			if (!acc[config.category]) {
				acc[config.category] = [];
			}
			acc[config.category].push(config);
			return acc;
		},
		{} as Record<string, (typeof MARKER_STYLE_CONFIGS)[MarkerStyleType][]>,
	);

	const categoryNames = {
		classic: "经典风格",
		animated: "动画效果",
		artistic: "艺术风格",
		interactive: "交互式",
	};

	const categoryIcons = {
		classic: "📍",
		animated: "✨",
		artistic: "🎨",
		interactive: "🎮",
	};

	return (
		<Popover open={isOpen} onOpenChange={setIsOpen}>
			<PopoverTrigger asChild>
				<Button
					variant="outline"
					size="sm"
					className="flex items-center gap-2 bg-white/90 backdrop-blur-sm border-white/50 hover:bg-white/95"
				>
					<Palette className="w-4 h-4" />
					<span className="text-lg">{currentConfig.preview}</span>
					<span className="hidden sm:inline">
						{currentConfig.name}
					</span>
				</Button>
			</PopoverTrigger>

			<PopoverContent
				className="w-[800px] max-w-[90vw] max-h-[80vh] p-0 bg-white/95 backdrop-blur-sm border-white/50 shadow-xl overflow-y-auto"
				align="center"
				side="bottom"
				sideOffset={20}
				avoidCollisions={true}
				collisionPadding={24}
				sticky="always"
			>
				<div className="p-6">
					<div className="flex items-center justify-between mb-4">
						<div className="flex items-center gap-2">
							<Sparkles className="w-4 h-4 text-purple-600" />
							<h3 className="font-semibold text-gray-900">
								点位风格
							</h3>
						</div>
					</div>

					{/* 风格分类展示 */}
					<div className="space-y-4">
						{Object.entries(stylesByCategory).map(
							([category, styles]) => (
								<div key={category}>
									<div className="flex items-center gap-2 mb-2">
										<span className="text-lg">
											{
												categoryIcons[
													category as keyof typeof categoryIcons
												]
											}
										</span>
										<h4 className="text-sm font-medium text-gray-700">
											{
												categoryNames[
													category as keyof typeof categoryNames
												]
											}
										</h4>
									</div>

									<div className="grid grid-cols-3 gap-3">
										{styles.map((style) => (
											<button
												key={style.id}
												type="button"
												className={`w-full p-3 rounded-lg border cursor-pointer transition-all duration-200 hover:scale-[1.02] hover:shadow-md text-left ${
													currentStyle === style.id
														? "ring-2 ring-purple-500 bg-purple-50 border-purple-200"
														: "hover:bg-gray-50 border-gray-200"
												}`}
												onClick={() => {
													onStyleChange(style.id);
													setIsOpen(false);
												}}
											>
												<div className="flex items-center gap-2 mb-1">
													<span className="text-lg">
														{style.preview}
													</span>
													<span className="text-sm font-medium text-gray-900">
														{style.name}
													</span>
													{style.isPremium && (
														<Crown className="w-3 h-3 text-yellow-500" />
													)}
												</div>
												<p className="text-xs text-gray-600 leading-tight">
													{style.description}
												</p>
												{style.isPremium && (
													<Badge
														status="info"
														className="mt-1 text-xs"
													>
														高级
													</Badge>
												)}
											</button>
										))}
									</div>
								</div>
							),
						)}
					</div>

					{/* 渐变光晕主题选择器 */}
					{currentStyle === "gradient-pulse" && (
						<div className="mt-6 pt-4 border-t border-gray-200">
							<div className="flex items-center gap-2 mb-3">
								<Paintbrush className="w-4 h-4 text-purple-600" />
								<h4 className="text-sm font-medium text-gray-700">
									光晕主题
								</h4>
							</div>

							<div className="grid grid-cols-3 gap-2">
								{Object.entries(GRADIENT_PULSE_THEMES).map(
									([themeKey, themeConfig]) => {
										const isActive =
											themeKey === currentTheme;
										const themeName =
											themeKey === "ocean"
												? "海洋"
												: themeKey === "sunset"
													? "日落"
													: themeKey === "forest"
														? "森林"
														: themeKey === "aurora"
															? "极光"
															: themeKey ===
																	"fire"
																? "火焰"
																: themeKey;

										return (
											<button
												key={themeKey}
												type="button"
												onClick={() => {
													onThemeChange(themeKey);
												}}
												className={`relative p-3 rounded-lg border-2 transition-all duration-200 text-left ${
													isActive
														? "border-purple-400 bg-purple-50 shadow-md"
														: "border-gray-200 hover:border-purple-300 hover:bg-purple-25"
												}`}
											>
												{/* 激活指示器 */}
												{isActive && (
													<div className="absolute top-1 right-1 w-2 h-2 bg-purple-500 rounded-full" />
												)}

												{/* 渐变预览 */}
												<div
													className="w-full h-8 rounded-md mb-2 border border-white/50"
													style={{
														background: `linear-gradient(135deg, ${themeConfig.primaryColor}, ${themeConfig.secondaryColor})`,
													}}
												/>

												<div className="text-center">
													<span
														className={`text-xs font-medium block ${isActive ? "text-purple-800" : "text-gray-700"}`}
													>
														{themeName}
													</span>
													<div
														className={`text-xs mt-1 ${isActive ? "text-purple-600" : "text-gray-500"}`}
													>
														{themeKey === "ocean"
															? "宁静"
															: themeKey ===
																	"sunset"
																? "浪漫"
																: themeKey ===
																		"forest"
																	? "清新"
																	: themeKey ===
																			"aurora"
																		? "绚烂"
																		: themeKey ===
																				"fire"
																			? "奔放"
																			: "经典"}
													</div>
												</div>
											</button>
										);
									},
								)}
							</div>
						</div>
					)}

					{/* 粒子特效主题选择器 */}
					{currentStyle === "particle-effect" && (
						<div className="mt-6 pt-4 border-t border-gray-200">
							<div className="flex items-center gap-2 mb-3">
								<Paintbrush className="w-4 h-4 text-orange-600" />
								<h4 className="text-sm font-medium text-gray-700">
									粒子主题
								</h4>
							</div>

							<div className="grid grid-cols-2 gap-3">
								{Object.entries(PARTICLE_EFFECT_THEMES).map(
									([themeKey, themeConfig]) => {
										const isActive =
											themeKey === currentTheme;

										return (
											<button
												key={themeKey}
												type="button"
												onClick={() => {
													onThemeChange(themeKey);
												}}
												className={`relative p-3 rounded-lg border-2 transition-all duration-200 text-left ${
													isActive
														? "border-orange-400 bg-orange-50 shadow-md"
														: "border-gray-200 hover:border-orange-300 hover:bg-orange-25"
												}`}
											>
												{/* 激活指示器 */}
												{isActive && (
													<div className="absolute top-1 right-1 w-2 h-2 bg-orange-500 rounded-full" />
												)}

												{/* 粒子效果预览 */}
												<div className="flex items-center justify-center h-8 mb-2 relative">
													{/* 中心点 */}
													<div
														className="w-3 h-3 rounded-full"
														style={{
															backgroundColor:
																themeConfig.particleColor,
														}}
													/>
													{/* 周围的小粒子 */}
													{Array.from(
														{ length: 4 },
														(_, i) => (
															<div
																key={i}
																className="absolute w-1 h-1 rounded-full opacity-70"
																style={{
																	backgroundColor:
																		themeConfig.particleColor,
																	transform: `rotate(${i * 90}deg) translateX(12px)`,
																}}
															/>
														),
													)}
												</div>

												<div className="text-center">
													<span
														className={`text-xs font-medium block ${isActive ? "text-orange-800" : "text-gray-700"}`}
													>
														{themeConfig.name}
													</span>
													<div
														className={`text-xs mt-1 ${isActive ? "text-orange-600" : "text-gray-500"}`}
													>
														{
															themeConfig.description
														}
													</div>
													<div
														className={`text-xs mt-1 ${isActive ? "text-orange-500" : "text-gray-400"}`}
													>
														{
															themeConfig.particleCount
														}
														个粒子
													</div>
												</div>
											</button>
										);
									},
								)}
							</div>
						</div>
					)}

					{/* 底部提示 */}
					<div className="mt-4 pt-3 border-t border-gray-200">
						<p className="text-xs text-gray-500 text-center">
							💡 更多风格和主题正在开发中，敬请期待
						</p>
					</div>
				</div>
			</PopoverContent>
		</Popover>
	);
}
