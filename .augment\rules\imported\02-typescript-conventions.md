---
type: "agent_requested"
---

# TypeScript and Coding Conventions

## TypeScript Usage
- Use TypeScript for all code
- Prefer interfaces over types for object definitions
- Avoid enums; use const maps instead
- Use strict type checking
- Always define return types for functions

## Component Structure
```typescript
// Types at the top
interface Props {
  title: string;
  children: React.ReactNode;
}

// Exported component
export function MyComponent({ title, children }: Props) {
  return (...)
}
```

## Function Patterns
- Use named function declarations
- Prefer pure functions
- Use TypeScript generics when appropriate
- Implement proper error handling

## State Management
- Use React Server Components (RSC) by default
- Minimize client-side state
- Use `nuqs` for URL search params
- Implement proper loading and error states

## Code Organization
- One component per file
- Export named components
- Group related utilities
- Keep files focused and small

## Naming Conventions
- Use PascalCase for components
- Use camelCase for functions and variables
- Use descriptive names with auxiliary verbs:
  - `isLoading`
  - `hasError`
  - `shouldUpdate`
  - `onSubmit`
