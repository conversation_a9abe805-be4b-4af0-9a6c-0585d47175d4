"use client";
// 使用共享的AI模块
// import { generateImage } from '@repo/ai';
// 使用共享的认证模块
// import { useAuth } from '@repo/auth';
// 使用共享的存储模块
// import { uploadFile } from '@repo/storage';

export default function HomePage() {
	return (
		<div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">
			{/* 导航栏 */}
			<nav className="bg-white shadow-sm">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="flex justify-between h-16">
						<div className="flex items-center">
							<h1 className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
								AI图片生成器
							</h1>
						</div>
						<div className="flex items-center space-x-4">
							<a
								href="/login"
								className="text-gray-600 hover:text-purple-600"
							>
								登录
							</a>
							<a
								href="/signup"
								className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700"
							>
								注册
							</a>
						</div>
					</div>
				</div>
			</nav>

			{/* 主要内容 */}
			<main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
				{/* Hero 区域 */}
				<div className="text-center mb-16">
					<h2 className="text-4xl md:text-6xl font-bold mb-6">
						<span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
							将想象变为现实
						</span>
					</h2>
					<p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
						使用最先进的AI技术，只需输入文字描述，即可生成精美的艺术作品。
						无论是概念设计、插画创作还是艺术探索，AI都能帮您实现创意愿景。
					</p>
					<div className="flex flex-col sm:flex-row gap-4 justify-center">
						<a
							href="/signup"
							className="bg-purple-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-purple-700 transition-colors"
						>
							开始创作
						</a>
						<a
							href="/choose-plan"
							className="border border-purple-600 text-purple-600 px-8 py-3 rounded-lg text-lg font-semibold hover:bg-purple-50 transition-colors"
						>
							查看套餐
						</a>
					</div>
				</div>

				{/* 特色功能 */}
				<div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
					<div className="text-center p-6">
						<div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
							<svg
								className="w-8 h-8 text-purple-600"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<title>快速生成图标</title>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M13 10V3L4 14h7v7l9-11h-7z"
								/>
							</svg>
						</div>
						<h3 className="text-xl font-semibold mb-2">快速生成</h3>
						<p className="text-gray-600">
							秒级生成高质量图片，让创意不再等待
						</p>
					</div>

					<div className="text-center p-6">
						<div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
							<svg
								className="w-8 h-8 text-blue-600"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<title>多种风格图标</title>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"
								/>
							</svg>
						</div>
						<h3 className="text-xl font-semibold mb-2">多种风格</h3>
						<p className="text-gray-600">
							支持写实、卡通、抽象等多种艺术风格
						</p>
					</div>

					<div className="text-center p-6">
						<div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
							<svg
								className="w-8 h-8 text-indigo-600"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<title>高清质量图标</title>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
								/>
							</svg>
						</div>
						<h3 className="text-xl font-semibold mb-2">高清质量</h3>
						<p className="text-gray-600">
							支持4K分辨率输出，满足专业需求
						</p>
					</div>
				</div>

				{/* 示例图片展示 */}
				<div className="text-center mb-16">
					<h3 className="text-3xl font-bold mb-8">AI创作示例</h3>
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
						{[1, 2, 3, 4].map((i) => (
							<div
								key={i}
								className="bg-white rounded-lg shadow-md overflow-hidden"
							>
								<div className="h-48 bg-gradient-to-br from-purple-200 to-blue-200 flex items-center justify-center">
									<span className="text-gray-500">
										AI生成示例 {i}
									</span>
								</div>
								<div className="p-4">
									<p className="text-sm text-gray-600">
										"一只在星空下的猫咪，梵高风格"
									</p>
								</div>
							</div>
						))}
					</div>
				</div>

				{/* CTA 区域 */}
				<div className="text-center bg-white rounded-2xl p-12 shadow-lg">
					<h3 className="text-3xl font-bold mb-4">
						准备开始您的AI创作之旅吗？
					</h3>
					<p className="text-xl text-gray-600 mb-8">
						加入数万名创作者，体验AI艺术的无限可能
					</p>
					<a
						href="/signup"
						className="bg-purple-600 text-white px-8 py-4 rounded-lg text-xl font-semibold hover:bg-purple-700 transition-colors"
					>
						立即开始创作
					</a>
				</div>
			</main>

			{/* 页脚 */}
			<footer className="bg-gray-900 text-white py-12 mt-16">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="text-center">
						<h4 className="text-xl font-bold mb-4">AI图片生成器</h4>
						<p className="text-gray-400 mb-4">
							让AI成为您的创作伙伴
						</p>
						<div className="flex justify-center space-x-6">
							<a
								href="/about"
								className="text-gray-400 hover:text-white"
							>
								关于我们
							</a>
							<a
								href="/contact"
								className="text-gray-400 hover:text-white"
							>
								联系我们
							</a>
							<a
								href="/privacy"
								className="text-gray-400 hover:text-white"
							>
								隐私政策
							</a>
						</div>
					</div>
				</div>
			</footer>
		</div>
	);
}
