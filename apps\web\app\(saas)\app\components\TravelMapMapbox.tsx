"use client";

import type { Footprint } from "@repo/api/src/routes/diaries/schemas";
import type { TravelPointImage } from "@repo/database/src/types/travel-diary";
import { cn } from "@ui/lib";
import { Loader2, Moon, RotateCc<PERSON>, Sun } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import MapGL, {
	Marker,
	NavigationControl,
	FullscreenControl,
	ScaleControl,
	GeolocateControl,
} from "react-map-gl";
import "mapbox-gl/dist/mapbox-gl.css";
import { motion } from "framer-motion";
import mapboxgl from "mapbox-gl";
import { useRouter } from "next/navigation";
import {
	FootprintPopover,
	type FootprintWithDiaryIndex,
} from "./FootprintPopover";
import { ImageLightbox } from "./ImageLightbox";

// 使用环境变量或默认Token
const MAPBOX_TOKEN = process.env.NEXT_PUBLIC_MAPBOX_TOKEN || "";

// 默认地图中心坐标和缩放级别
const DEFAULT_CENTER = { lat: 20, lng: 0 };
const DEFAULT_ZOOM = 2;

// 地图样式URLs
const MAP_STYLES = {
	day: "mapbox://styles/mapbox/streets-v12", // 日间模式 - 标准地图样式
	night: "mapbox://styles/mapbox/dark-v11", // 夜间模式 - 导航夜间样式
};

// 缩放级别限制
const MIN_ZOOM = 0.5; // 最小缩放级别
const MAX_ZOOM = 22; // 最大缩放级别

// 每项格式为: [背景颜色, 文字颜色]
const PASTEL_COLORS: [string, string][] = [
	["#A0C4FF", "#000000"], // 淡蓝色，黑色文字
	["#BDB2FF", "#000000"], // 淡紫色，黑色文字
	["#CAFFBF", "#000000"], // 淡绿色，黑色文字
	["#FFADAD", "#000000"], // 淡红色，黑色文字
	["#FFD6A5", "#000000"], // 淡橙色，黑色文字
	["#FDFFB6", "#000000"], // 淡黄色，黑色文字
	["#9BF6FF", "#000000"], // 淡青色，黑色文字
	["#FFC6FF", "#000000"], // 淡粉色，黑色文字
	["#E2E2E2", "#000000"], // 淡灰色，黑色文字
	["#B9E6D3", "#000000"], // 淡薄荷色，黑色文字
];

// 夜间模式的Marker颜色
const NIGHT_MODE_COLORS: [string, string][] = [
	["#5D8BF4", "#FFFFFF"], // 蓝色，白色文字
	["#9376E0", "#FFFFFF"], // 紫色，白色文字
	["#55D8C1", "#000000"], // 青绿色，黑色文字
	["#FF8787", "#FFFFFF"], // 红色，白色文字
	["#FFB562", "#000000"], // 橙色，黑色文字
	["#F8FF95", "#000000"], // 黄色，黑色文字
	["#00D7FF", "#000000"], // 亮青色，黑色文字
	["#E893CF", "#FFFFFF"], // 粉色，白色文字
	["#E0E0E0", "#000000"], // 灰色，黑色文字
	["#90C8AC", "#000000"], // 薄荷色，黑色文字
];

interface TravelMapProps {
	footprints: Footprint[];
	className?: string;
	height?: string | number;
	onMarkerClick?: (footprintId: string) => void;
}

/**
 * Mapbox版本的旅行地图组件
 */
export default function TravelMapMapbox({
	footprints = [],
	className = "",
	height = "500px",
	onMarkerClick,
}: TravelMapProps) {
	// 状态管理
	const [mapLoaded, setMapLoaded] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [selectedFootprintId, setSelectedFootprintId] = useState<
		string | null
	>(null);
	// 存储带有日记索引的足迹点
	const [processedFootprints, setProcessedFootprints] = useState<
		FootprintWithDiaryIndex[]
	>([]);
	// 日夜模式状态
	const [isNightMode, setIsNightMode] = useState(false);
	// 弹出框位置
	const [popoverOpen, setPopoverOpen] = useState<{
		id: string;
		open: boolean;
	} | null>(null);
	// 调试标志
	const [debugLogs, setDebugLogs] = useState<string[]>([]);
	// 添加 Lightbox 相关状态
	const [lightboxOpen, setLightboxOpen] = useState(false);
	const [selectedImageIndex, setSelectedImageIndex] = useState(0);
	const [selectedFootprintImages, setSelectedFootprintImages] = useState<
		TravelPointImage[]
	>([]);
	// 添加 Ctrl 键状态和滚动提示状态
	const [isCtrlKeyPressed, setIsCtrlKeyPressed] = useState(false);
	const [showScrollHint, setShowScrollHint] = useState(false);
	const scrollHintTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const autoHideHintTimeoutRef = useRef<NodeJS.Timeout | null>(null); // 新增: 用于自动隐藏提示的timeout
	const [hasHintBeenShown, setHasHintBeenShown] = useState(false); // 新增: 跟踪提示是否已显示

	// 添加路由器用于导航
	const router = useRouter();

	// 添加调试日志的帮助函数
	const addDebugLog = useCallback((message: string) => {
		console.log(`🔍 DEBUG: ${message}`);
		setDebugLogs((prev) => [
			...prev,
			`${new Date().toISOString().slice(11, 19)} - ${message}`,
		]);
	}, []);

	// 地图引用
	const mapRef = useRef<any>(null);
	const popoverRef = useRef<boolean>(false);

	// 处理足迹点数据，为它们添加日记索引
	useEffect(() => {
		if (!footprints.length) return;

		// 根据diaryId分组足迹点
		const diaryGroups = new Map<string, FootprintWithDiaryIndex[]>();

		// 首先对足迹点进行检查
		footprints.forEach((fp) => {
			// 检查是否有diaryId，确保每个点都关联到一个有效的日记
			if (!fp.diaryId) {
				console.warn("发现缺少diaryId的足迹点:", fp);
			}

			// 使用 diaryId，如果不存在才回退到ID前缀
			const diaryId = fp.diaryId || `unknown_${fp.id.substring(0, 8)}`;

			if (!diaryGroups.has(diaryId)) {
				diaryGroups.set(diaryId, []);
			}

			diaryGroups.get(diaryId)?.push({
				...fp,
				// 使用后端提供的 diaryTitle，如果不存在则显示未命名
				diaryTitle: fp.diaryTitle || "未命名日记",
			});
		});

		// 为每个分组分配一个索引（即颜色索引）
		const processed: FootprintWithDiaryIndex[] = [];
		let diaryIndex = 0;

		// 输出分组调试信息
		if (process.env.NODE_ENV === "development") {
			console.log(`🗺️ 足迹点分组情况: ${diaryGroups.size}个不同日记`);
			diaryGroups.forEach((group, id) => {
				console.log(
					`  - 日记 ${id}: ${group.length}个点位，标题: ${group[0]?.diaryTitle || "未知"}`,
				);
			});
		}

		// 将分组按日记ID排序，确保相同的日记ID每次都获得相同的颜色索引
		const sortedDiaryIds = Array.from(diaryGroups.keys()).sort();

		sortedDiaryIds.forEach((diaryId) => {
			const group = diaryGroups.get(diaryId);
			if (!group) return;

			// 获取该日记的总点位数
			const totalFootprints = group.length;

			// 为这个分组中的所有点分配相同的日记索引
			group.forEach((fp, localIndex) => {
				processed.push({
					...fp,
					diaryIndex,
					// 确保使用正确的diaryId
					diaryId: diaryId,
					// 使用后端提供的 diaryTitle
					diaryTitle: fp.diaryTitle || "未命名日记",
					// 添加该日记的总点位数
					totalFootprints: totalFootprints,
					// 添加当前足迹在日记中的索引
					footprintIndexInDiary: localIndex,
				});
			});

			// 确保颜色循环
			diaryIndex = (diaryIndex + 1) % PASTEL_COLORS.length;
		});

		setProcessedFootprints(processed);
	}, [footprints]);

	// 辅助函数：调整颜色亮度
	function adjustColorBrightness(hex: string, percent: number): string {
		// 从十六进制转为RGB
		let r = Number.parseInt(hex.slice(1, 3), 16);
		let g = Number.parseInt(hex.slice(3, 5), 16);
		let b = Number.parseInt(hex.slice(5, 7), 16);

		// 调整亮度
		r = Math.max(0, Math.min(255, r + percent * 2.55));
		g = Math.max(0, Math.min(255, g + percent * 2.55));
		b = Math.max(0, Math.min(255, b + percent * 2.55));

		// 转回十六进制
		return `#${Math.round(r).toString(16).padStart(2, "0")}${Math.round(g).toString(16).padStart(2, "0")}${Math.round(b).toString(16).padStart(2, "0")}`;
	}

	// 监听 Ctrl 键的按下和释放
	useEffect(() => {
		const handleKeyDown = (event: KeyboardEvent) => {
			if (event.key === "Control") {
				setIsCtrlKeyPressed(true);
				// 当 Ctrl 按下时，立即隐藏提示
				setShowScrollHint(false);
				if (scrollHintTimeoutRef.current) {
					clearTimeout(scrollHintTimeoutRef.current);
				}
			}
		};

		const handleKeyUp = (event: KeyboardEvent) => {
			if (event.key === "Control") {
				setIsCtrlKeyPressed(false);
			}
		};

		window.addEventListener("keydown", handleKeyDown);
		window.addEventListener("keyup", handleKeyUp);

		return () => {
			window.removeEventListener("keydown", handleKeyDown);
			window.removeEventListener("keyup", handleKeyUp);
			if (scrollHintTimeoutRef.current) {
				clearTimeout(scrollHintTimeoutRef.current);
			}
			if (autoHideHintTimeoutRef.current) {
				// 清理新增的timeout
				clearTimeout(autoHideHintTimeoutRef.current);
			}
		};
	}, []);

	// 容器样式
	const containerStyle = {
		height: typeof height === "number" ? `${height}px` : height,
		width: "100%",
	};

	// 处理标记点击
	const handleMarkerClick = useCallback(
		(footprintId: string, event?: React.MouseEvent) => {
			// 阻止事件冒泡
			if (event) {
				event.stopPropagation();
				event.preventDefault();
			}

			// 设置选中的标记ID
			setSelectedFootprintId(footprintId);

			// 标记 popover 正在打开
			popoverRef.current = true;

			// 打开弹出框，但不触发其他点击操作
			setPopoverOpen({ id: footprintId, open: true });

			// 如果有外部点击处理函数，则调用
			if (onMarkerClick) {
				onMarkerClick(footprintId);
			}
		},
		[onMarkerClick],
	);

	// 处理地图加载完成
	const handleLoad = useCallback(() => {
		setMapLoaded(true);

		if (!mapRef.current) return;

		const map = mapRef.current.getMap();

		// 设置统一的缩放限制
		map.setMinZoom(MIN_ZOOM);
		map.setMaxZoom(MAX_ZOOM);

		// 添加点击事件监听，防止点击地图关闭 popover
		map.on("click", () => {
			// 如果 popover 刚打开，忽略此次点击
			if (popoverRef.current) {
				popoverRef.current = false;
				return;
			}
		});
	}, []);

	// 处理日夜模式切换
	const toggleMapMode = useCallback(() => {
		if (!mapRef.current) return;

		const newNightMode = !isNightMode;
		setIsNightMode(newNightMode);

		const map = mapRef.current.getMap();

		// 切换地图样式
		map.setStyle(newNightMode ? MAP_STYLES.night : MAP_STYLES.day);
	}, [isNightMode]);

	// 重置视图到默认状态
	const resetMapView = useCallback(() => {
		if (!mapRef.current) return;

		const map = mapRef.current.getMap();

		// 如果有足迹点，则显示所有点；否则显示默认视图
		if (footprints.length > 0) {
			try {
				const bounds = new mapboxgl.LngLatBounds();
				footprints.forEach((fp) => {
					bounds.extend([fp.longitude, fp.latitude]);
				});

				map.fitBounds(bounds, {
					padding: 100,
					duration: 1000,
					maxZoom: 14,
				});
			} catch (err) {
				console.warn("重置视图失败", err);
				// 回退到默认视图
				map.flyTo({
					center: [DEFAULT_CENTER.lng, DEFAULT_CENTER.lat],
					zoom: DEFAULT_ZOOM,
					duration: 1000,
				});
			}
		} else {
			// 没有足迹点，显示默认视图
			map.flyTo({
				center: [DEFAULT_CENTER.lng, DEFAULT_CENTER.lat],
				zoom: DEFAULT_ZOOM,
				duration: 1000,
			});
		}
	}, [footprints]);

	// 地图基本校验
	useEffect(() => {
		if (!MAPBOX_TOKEN) {
			setError(
				"未配置Mapbox访问令牌，请添加NEXT_PUBLIC_MAPBOX_TOKEN环境变量",
			);
			return;
		}
	}, []);

	// 调整地图边界，使所有标记点可见
	useEffect(() => {
		if (!mapRef.current || !mapLoaded || footprints.length === 0) return;

		try {
			const map = mapRef.current.getMap();

			// 创建边界对象
			const bounds = new mapboxgl.LngLatBounds();

			// 将所有点位添加到边界对象
			footprints.forEach((fp) => {
				bounds.extend([fp.longitude, fp.latitude]);
			});

			// 调整地图以显示所有点位
			map.fitBounds(bounds, {
				padding: 100,
				duration: 1000,
				maxZoom: 14, // 避免过度缩放
			});
		} catch (err) {
			console.error("调整地图视图失败", err);
		}
	}, [mapLoaded, footprints]);

	// 根据图标类型选择图标
	const getIconForType = (iconType?: string) => {
		const iconMap: Record<string, string> = {
			landmark: "🏛️",
			food: "🍽️",
			park: "🌳",
			hotel: "🏨",
			transportation: "🚆",
			shopping: "🛍️",
			entertainment: "🎭",
			default: "📍",
		};

		return iconType
			? iconMap[iconType] || iconMap.default
			: iconMap.default;
	};

	// 监听 popoverOpen 状态变化的调试
	useEffect(() => {
		if (popoverOpen) {
			// 更新点位颜色和视图状态
			if (mapRef.current && popoverOpen.open) {
				const selectedFp = processedFootprints.find(
					(fp) => fp.id === popoverOpen.id,
				);
				if (selectedFp) {
					try {
						const map = mapRef.current.getMap();
						// 平滑地移动到选中的点位
						map.flyTo({
							center: [selectedFp.longitude, selectedFp.latitude],
							zoom: 12,
							duration: 1000,
						});
					} catch (error) {
						console.error("无法移动到选中点位:", error);
					}
				}
			}
		}
	}, [popoverOpen, processedFootprints]);

	// 处理查看详情按钮点击
	const handleViewDetails = useCallback(
		(footprintId: string, diaryId: string) => {
			// 打印调试信息
			console.log(
				`准备跳转到日记详情: footprintId=${footprintId}, diaryId=${diaryId}`,
			);

			// 关闭弹出框
			setPopoverOpen(null);

			// 如果有外部点击处理函数，先调用它
			if (onMarkerClick) {
				onMarkerClick(footprintId);
			}

			// 检查diaryId是否有效
			if (!diaryId) {
				console.error("无法跳转：diaryId无效", {
					footprintId,
					diaryId,
				});
				return;
			}

			// 跳转到日记详情页面
			const detailPath = `/app/diary/${diaryId}`;
			console.log(`正在跳转到: ${detailPath}`);
			router.push(detailPath);
		},
		[onMarkerClick, router],
	);

	// 处理图片点击，打开 Lightbox
	const handleImageClick = useCallback(
		(footprint: FootprintWithDiaryIndex, imageIndex: number) => {
			if (!footprint.images || footprint.images.length === 0) return;

			console.log("打开图片查看器", {
				imageCount: footprint.images.length,
				currentIndex: imageIndex,
				location: footprint.location,
			});

			// 设置图片数组和起始索引
			setSelectedFootprintImages(footprint.images);
			setSelectedImageIndex(imageIndex);

			// 打开 Lightbox
			setLightboxOpen(true);
		},
		[],
	);

	// 处理上一张图片
	const handlePrevImage = useCallback(() => {
		setSelectedImageIndex(
			(prev) =>
				(prev - 1 + selectedFootprintImages.length) %
				selectedFootprintImages.length,
		);
	}, [selectedFootprintImages]);

	// 处理下一张图片
	const handleNextImage = useCallback(() => {
		setSelectedImageIndex(
			(prev) => (prev + 1) % selectedFootprintImages.length,
		);
	}, [selectedFootprintImages]);

	// 导航到同一日记的上一个点位
	const navigateToPrevLocation = useCallback(
		(currentFp: FootprintWithDiaryIndex) => {
			if (!currentFp.diaryId) return;

			// 找出当前日记的所有点位
			const sameDiaryFootprints = processedFootprints.filter(
				(fp) => fp.diaryId === currentFp.diaryId,
			);

			if (sameDiaryFootprints.length <= 1) return; // 只有一个点位时不做任何操作

			// 找出当前点位的索引
			const currentIndex = sameDiaryFootprints.findIndex(
				(fp) => fp.id === currentFp.id,
			);

			// 计算上一个点位的索引（循环导航）
			const prevIndex =
				(currentIndex - 1 + sameDiaryFootprints.length) %
				sameDiaryFootprints.length;

			// 获取上一个点位
			const prevFootprint = sameDiaryFootprints[prevIndex];

			// 关闭当前 popover，打开新的点位 popover
			setPopoverOpen(null);
			// 短暂延迟后打开新的点位，让前一个有时间关闭
			setTimeout(() => {
				setSelectedFootprintId(prevFootprint.id);
				setPopoverOpen({ id: prevFootprint.id, open: true });
			}, 50);
		},
		[processedFootprints],
	);

	// 导航到同一日记的下一个点位
	const navigateToNextLocation = useCallback(
		(currentFp: FootprintWithDiaryIndex) => {
			if (!currentFp.diaryId) return;

			// 找出当前日记的所有点位
			const sameDiaryFootprints = processedFootprints.filter(
				(fp) => fp.diaryId === currentFp.diaryId,
			);

			if (sameDiaryFootprints.length <= 1) return; // 只有一个点位时不做任何操作

			// 找出当前点位的索引
			const currentIndex = sameDiaryFootprints.findIndex(
				(fp) => fp.id === currentFp.id,
			);

			// 计算下一个点位的索引（循环导航）
			const nextIndex = (currentIndex + 1) % sameDiaryFootprints.length;

			// 获取下一个点位
			const nextFootprint = sameDiaryFootprints[nextIndex];

			// 关闭当前 popover，打开新的点位 popover
			setPopoverOpen(null);
			// 短暂延迟后打开新的点位，让前一个有时间关闭
			setTimeout(() => {
				setSelectedFootprintId(nextFootprint.id);
				setPopoverOpen({ id: nextFootprint.id, open: true });
			}, 50);
		},
		[processedFootprints],
	);

	// 显示错误
	if (error) {
		return (
			<div
				style={containerStyle}
				className={cn(
					"flex items-center justify-center bg-destructive/10 border border-destructive/50 text-destructive p-4 rounded-md",
					className,
				)}
			>
				{error}
			</div>
		);
	}

	// Mapbox Token未配置
	if (!MAPBOX_TOKEN) {
		return (
			<div
				style={containerStyle}
				className={cn(
					"flex items-center justify-center bg-amber-50 border border-amber-200 text-amber-700 p-4 rounded-md",
					className,
				)}
			>
				地图配置不完整，请配置Mapbox Token
			</div>
		);
	}

	// 渲染地图
	return (
		<div
			className={cn("relative rounded-md overflow-hidden", className)}
			style={containerStyle}
			onMouseEnter={() => {
				if (!isCtrlKeyPressed && !hasHintBeenShown) {
					// 仅当Ctrl未按下且提示未显示过时
					if (scrollHintTimeoutRef.current) {
						clearTimeout(scrollHintTimeoutRef.current);
					}
					if (autoHideHintTimeoutRef.current) {
						// 清除可能存在的自动隐藏计时器
						clearTimeout(autoHideHintTimeoutRef.current);
					}
					scrollHintTimeoutRef.current = setTimeout(() => {
						setShowScrollHint(true);
						setHasHintBeenShown(true); // 标记提示已显示
						// 设置2秒后自动隐藏
						autoHideHintTimeoutRef.current = setTimeout(() => {
							setShowScrollHint(false);
						}, 2000);
					}, 300); // 延迟300毫秒显示
				}
			}}
			onMouseLeave={() => {
				if (scrollHintTimeoutRef.current) {
					clearTimeout(scrollHintTimeoutRef.current);
				}
				// 当鼠标移开时，如果提示正在显示，并且不是通过自动隐藏计时器隐藏的，则立即隐藏
				// 但我们希望它能通过 autoHideHintTimeoutRef 自然消失，除非Ctrl键被按下
				// 因此，这里主要清除 scrollHintTimeoutRef 即可，让自动隐藏逻辑处理其余部分
				// 如果不想让它在移开后立即消失（若已触发显示），可以注释掉下面的setShowScrollHint(false)
				// setShowScrollHint(false);
			}}
		>
			{/* Lightbox 组件 */}
			<ImageLightbox
				images={selectedFootprintImages.map((img) => img.url)}
				initialIndex={selectedImageIndex}
				isOpen={lightboxOpen}
				onClose={() => setLightboxOpen(false)}
				onPrev={handlePrevImage}
				onNext={handleNextImage}
			/>

			<MapGL
				ref={mapRef}
				mapboxAccessToken={MAPBOX_TOKEN}
				initialViewState={{
					latitude: DEFAULT_CENTER.lat,
					longitude: DEFAULT_CENTER.lng,
					zoom: DEFAULT_ZOOM,
					pitch: 0,
					bearing: 0,
				}}
				style={{ width: "100%", height: "100%" }}
				mapStyle={isNightMode ? MAP_STYLES.night : MAP_STYLES.day}
				onLoad={handleLoad}
				minZoom={MIN_ZOOM}
				maxZoom={MAX_ZOOM}
				scrollZoom={isCtrlKeyPressed} // 仅在 Ctrl 按下时允许缩放
				dragPan={isCtrlKeyPressed} // 仅在 Ctrl 按下时允许拖动
				onClick={(e) => {
					// 防止点击地图时关闭 popover
					// 如果 popover 刚打开，忽略此次点击
					if (popoverRef.current) {
						popoverRef.current = false;
						e.preventDefault();
					}
				}}
			>
				{/* 地图控件 */}
				<GeolocateControl position="top-right" />
				<FullscreenControl position="top-right" />
				<NavigationControl position="top-right" />
				<ScaleControl />

				{/* 日夜模式切换按钮和重置视图按钮 */}
				<div className="absolute top-3 left-3 z-10 flex flex-col gap-2">
					<button
						type="button"
						onClick={toggleMapMode}
						className={cn(
							"rounded-md p-2 shadow-md transition-colors",
							isNightMode
								? "bg-slate-800 text-yellow-300 hover:bg-slate-700"
								: "bg-white text-slate-800 hover:bg-gray-100",
						)}
						title={
							isNightMode ? "切换到日间模式" : "切换到夜间模式"
						}
					>
						{isNightMode ? <Sun size={20} /> : <Moon size={20} />}
					</button>

					<button
						type="button"
						onClick={resetMapView}
						className={cn(
							"rounded-md p-2 shadow-md transition-colors",
							isNightMode
								? "bg-slate-800 text-blue-300 hover:bg-slate-700"
								: "bg-white text-blue-600 hover:bg-gray-100",
						)}
						title="重置视图"
					>
						<RotateCcw size={20} />
					</button>
				</div>

				{/* 标记点 */}
				{processedFootprints.map((fp) => {
					// 确定此点的颜色
					const colorIndex =
						fp.diaryIndex !== undefined ? fp.diaryIndex : 0;
					// 根据当前模式选择颜色
					const [bgColor, textColor] = isNightMode
						? NIGHT_MODE_COLORS[colorIndex]
						: PASTEL_COLORS[colorIndex];
					const isSelected = selectedFootprintId === fp.id;

					// 选中状态使用更暗的颜色变体
					const selectedBgColor = isSelected
						? adjustColorBrightness(bgColor, isNightMode ? 10 : -15) // 夜间模式增亮，日间模式变暗
						: bgColor;

					return (
						<Marker
							key={fp.id}
							latitude={fp.latitude}
							longitude={fp.longitude}
							anchor="bottom"
							onClick={(e) => {
								// 阻止事件冒泡到地图
								e.originalEvent.stopPropagation();
								handleMarkerClick(
									fp.id,
									e.originalEvent as unknown as React.MouseEvent,
								);
							}}
						>
							<FootprintPopover
								footprint={fp}
								isSelected={selectedFootprintId === fp.id}
								isNightMode={isNightMode}
								popoverOpen={
									popoverOpen?.id === fp.id &&
									popoverOpen.open
								}
								onOpenChange={(open) => {
									if (open) {
										setPopoverOpen({ id: fp.id, open });
									} else if (popoverOpen?.id === fp.id) {
										// 只有当关闭的是当前打开的 popover 时才关闭
										setPopoverOpen(null);
									}
								}}
								onViewDetails={handleViewDetails}
								onImageClick={handleImageClick}
								navigateToPrevLocation={navigateToPrevLocation}
								navigateToNextLocation={navigateToNextLocation}
								getIconForType={getIconForType}
							>
								<button
									type="button"
									className={cn(
										"flex flex-col items-center cursor-pointer border-none bg-transparent p-0 m-0",
										isSelected && "z-10",
									)}
									onClick={(e) => {
										// 阻止事件冒泡
										e.stopPropagation();
									}}
									aria-label={`查看 ${fp.location} 的详细信息`}
								>
									<motion.div
										className="relative"
										whileHover={{ scale: 1.1 }}
										animate={
											isSelected
												? {
														scale: [1, 1.2, 1],
														rotate: [0, 10, -10, 0],
													}
												: {}
										}
										transition={
											isSelected
												? {
														duration: 0.5,
														ease: "easeInOut",
														times: [0, 0.3, 0.6, 1],
													}
												: {}
										}
									>
										<div
											className={cn(
												"rounded-full shadow-md flex items-center justify-center transition-all duration-200",
												isSelected
													? "w-8 h-8 border-[2.5px]"
													: "w-6 h-6 border-2",
											)}
											style={{
												backgroundColor:
													selectedBgColor,
												borderColor: isSelected
													? isNightMode
														? "rgba(255,255,255,0.9)"
														: "white"
													: isNightMode
														? "rgba(255,255,255,0.6)"
														: "rgba(255,255,255,0.8)",
											}}
										>
											<span
												className={cn(
													"font-bold transition-all",
													isSelected
														? "text-sm"
														: "text-xs",
												)}
												style={{ color: textColor }}
											>
												{/* 显示日记索引+1（从1开始而不是0） */}
												{(fp.diaryIndex ?? 0) + 1}
											</span>
										</div>
										<div
											className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent"
											style={{
												height: "10px",
												borderTopColor: selectedBgColor,
											}}
										/>
									</motion.div>
								</button>
							</FootprintPopover>
						</Marker>
					);
				})}
			</MapGL>

			{/* 滚动缩放提示 */}
			{showScrollHint && !isCtrlKeyPressed && (
				<div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-black/70 text-white p-3 rounded-md text-sm z-20 pointer-events-none">
					按住 Ctrl 可缩放和平移地图
				</div>
			)}

			{/* 加载中状态 */}
			{!mapLoaded && (
				<div className="absolute inset-0 flex flex-col items-center justify-center bg-muted/80 backdrop-blur-sm">
					<Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
					<span className="mt-2 text-sm text-muted-foreground">
						正在加载地图...
					</span>
				</div>
			)}

			{/* 调试信息面板，仅在开发环境显示 */}
			{/* 注释掉调试面板
			{process.env.NODE_ENV === "development" && (
				<div className="absolute bottom-10 left-0 right-0 mx-auto max-w-md bg-black/80 text-white text-xs p-2 z-50 max-h-40 overflow-auto">
					<strong>调试日志:</strong>
					<ul className="mt-1">
						{debugLogs.slice(-10).map((log, index) => (
							<li key={index}>{log}</li>
						))}
					</ul>
				</div>
			)}
			*/}
		</div>
	);
}
