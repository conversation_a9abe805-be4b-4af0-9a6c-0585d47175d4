/**
 * 匿名用户身份识别工具
 * 用于Feature Voting模块支持匿名用户投票和评论
 */

const ANONYMOUS_ID_KEY = "feature_voting_anonymous_id";
const ANONYMOUS_USER_KEY = "feature_voting_anonymous_user";

export interface AnonymousUser {
	id: string;
	name?: string;
	email?: string;
	createdAt: string;
}

export interface BrowserFingerprint {
	userAgent: string;
	language: string;
	timezone: string;
	screenResolution: string;
	colorDepth: number;
	platform: string;
}

/**
 * 生成唯一的匿名用户ID
 */
export function generateAnonymousId(): string {
	if (typeof crypto !== "undefined" && crypto.randomUUID) {
		return crypto.randomUUID();
	}

	// 降级方案：使用时间戳和随机数
	return `anon_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 获取浏览器指纹信息
 */
export function getBrowserFingerprint(): BrowserFingerprint {
	if (typeof window === "undefined") {
		// 服务端渲染时的默认值
		return {
			userAgent: "",
			language: "",
			timezone: "",
			screenResolution: "",
			colorDepth: 0,
			platform: "",
		};
	}

	return {
		userAgent: navigator.userAgent,
		language: navigator.language,
		timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
		screenResolution: `${screen.width}x${screen.height}`,
		colorDepth: screen.colorDepth,
		platform: navigator.platform,
	};
}

/**
 * 生成基于浏览器指纹的增强ID
 */
export function generateEnhancedAnonymousId(): string {
	const fingerprint = getBrowserFingerprint();
	const fingerprintString = JSON.stringify(fingerprint);

	// 简单的哈希函数
	let hash = 0;
	for (let i = 0; i < fingerprintString.length; i++) {
		const char = fingerprintString.charCodeAt(i);
		hash = (hash << 5) - hash + char;
		hash = hash & hash; // 转换为32位整数
	}

	const baseId = generateAnonymousId();
	return `${baseId}_${Math.abs(hash).toString(36)}`;
}

/**
 * 获取或创建匿名用户ID
 */
export function getOrCreateAnonymousId(): string {
	if (typeof window === "undefined") {
		return generateAnonymousId();
	}

	try {
		let anonymousId = localStorage.getItem(ANONYMOUS_ID_KEY);

		if (!anonymousId) {
			anonymousId = generateEnhancedAnonymousId();
			localStorage.setItem(ANONYMOUS_ID_KEY, anonymousId);
		}

		return anonymousId;
	} catch (error) {
		// localStorage 不可用时的降级方案
		console.warn(
			"localStorage not available, using session-only anonymous ID",
		);
		return generateAnonymousId();
	}
}

/**
 * 获取匿名用户信息
 */
export function getAnonymousUser(): AnonymousUser | null {
	if (typeof window === "undefined") {
		return null;
	}

	try {
		const userData = localStorage.getItem(ANONYMOUS_USER_KEY);
		if (userData) {
			return JSON.parse(userData);
		}

		// 如果没有用户数据，但有ID，创建基础用户信息
		const anonymousId = getOrCreateAnonymousId();
		const user: AnonymousUser = {
			id: anonymousId,
			createdAt: new Date().toISOString(),
		};

		localStorage.setItem(ANONYMOUS_USER_KEY, JSON.stringify(user));
		return user;
	} catch (error) {
		console.warn("Failed to get anonymous user from localStorage:", error);
		return {
			id: getOrCreateAnonymousId(),
			createdAt: new Date().toISOString(),
		};
	}
}

/**
 * 更新匿名用户信息
 */
export function updateAnonymousUser(
	updates: Partial<Pick<AnonymousUser, "name" | "email">>,
): AnonymousUser {
	const currentUser = getAnonymousUser();
	if (!currentUser) {
		throw new Error("Failed to get current anonymous user");
	}

	const updatedUser: AnonymousUser = {
		...currentUser,
		...updates,
	};

	if (typeof window !== "undefined") {
		try {
			localStorage.setItem(
				ANONYMOUS_USER_KEY,
				JSON.stringify(updatedUser),
			);
		} catch (error) {
			console.warn(
				"Failed to save anonymous user to localStorage:",
				error,
			);
		}
	}

	return updatedUser;
}

/**
 * 清除匿名用户数据（用户登录后可选择清除）
 */
export function clearAnonymousUser(): void {
	if (typeof window === "undefined") {
		return;
	}

	try {
		localStorage.removeItem(ANONYMOUS_ID_KEY);
		localStorage.removeItem(ANONYMOUS_USER_KEY);
	} catch (error) {
		console.warn("Failed to clear anonymous user data:", error);
	}
}

/**
 * 检查是否为有效的匿名ID格式
 */
export function isValidAnonymousId(id: string): boolean {
	if (!id || typeof id !== "string") {
		return false;
	}

	// 检查是否符合我们的ID格式
	return (
		id.startsWith("anon_") ||
		/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/.test(id)
	);
}

/**
 * 获取用户地理位置信息（可选）
 */
export interface GeoLocation {
	latitude: number;
	longitude: number;
	accuracy: number;
}

export function getUserLocation(): Promise<GeoLocation | null> {
	return new Promise((resolve) => {
		if (typeof window === "undefined" || !navigator.geolocation) {
			resolve(null);
			return;
		}

		navigator.geolocation.getCurrentPosition(
			(position) => {
				resolve({
					latitude: position.coords.latitude,
					longitude: position.coords.longitude,
					accuracy: position.coords.accuracy,
				});
			},
			() => {
				resolve(null);
			},
			{
				timeout: 10000,
				maximumAge: 300000, // 5 minutes
			},
		);
	});
}

/**
 * 获取用户IP地址（通过第三方API）
 */
export async function getUserIP(): Promise<string | null> {
	try {
		const response = await fetch("https://api.ipify.org?format=json");
		const data = await response.json();
		return data.ip || null;
	} catch (error) {
		console.warn("Failed to get user IP:", error);
		return null;
	}
}

/**
 * 简单的频率限制检查
 */
export function checkRateLimit(
	action: string,
	maxAttempts = 5,
	timeWindow = 60000,
): boolean {
	if (typeof window === "undefined") {
		return true;
	}

	const key = `rate_limit_${action}`;
	const now = Date.now();

	try {
		const data = localStorage.getItem(key);
		if (!data) {
			localStorage.setItem(
				key,
				JSON.stringify({ count: 1, firstAttempt: now }),
			);
			return true;
		}

		const { count, firstAttempt } = JSON.parse(data);

		// 如果超过时间窗口，重置计数
		if (now - firstAttempt > timeWindow) {
			localStorage.setItem(
				key,
				JSON.stringify({ count: 1, firstAttempt: now }),
			);
			return true;
		}

		// 检查是否超过限制
		if (count >= maxAttempts) {
			return false;
		}

		// 增加计数
		localStorage.setItem(
			key,
			JSON.stringify({ count: count + 1, firstAttempt }),
		);
		return true;
	} catch (error) {
		console.warn("Rate limit check failed:", error);
		return true; // 出错时允许操作
	}
}

/**
 * 清除频率限制记录
 */
export function clearRateLimit(action: string): void {
	if (typeof window === "undefined") {
		return;
	}

	try {
		localStorage.removeItem(`rate_limit_${action}`);
	} catch (error) {
		console.warn("Failed to clear rate limit:", error);
	}
}

/**
 * 创建匿名用户对象（用于API调用）
 */
export interface AnonymousUserData {
	anonymousId: string;
	voterName?: string;
	voterEmail?: string;
	browserFingerprint?: BrowserFingerprint;
}

export function createAnonymousUser(
	name?: string,
	email?: string,
): AnonymousUserData {
	const anonymousId = getOrCreateAnonymousId();
	const browserFingerprint = getBrowserFingerprint();

	const anonymousUser: AnonymousUserData = {
		anonymousId,
		browserFingerprint,
	};

	if (name) {
		anonymousUser.voterName = name;
	}

	if (email) {
		anonymousUser.voterEmail = email;
	}

	// 更新本地存储的用户信息
	updateAnonymousUser({ name, email });

	return anonymousUser;
}

/**
 * 获取匿名用户数据（用于API调用）
 */
export function getAnonymousUserData(): AnonymousUserData {
	const user = getAnonymousUser();
	if (!user) {
		return createAnonymousUser();
	}

	return createAnonymousUser(user.name, user.email);
}
