"use client";

interface LoadingSpinnerProps {
	message?: string;
}

export function LoadingSpinner({ message = "加载中..." }: LoadingSpinnerProps) {
	return (
		<div className="flex flex-col items-center justify-center h-screen">
			<div className="animate-spin h-12 w-12 border-t-2 border-b-2 border-primary rounded-full mb-4" />
			<p className="text-muted-foreground text-lg">{message}</p>
		</div>
	);
}
