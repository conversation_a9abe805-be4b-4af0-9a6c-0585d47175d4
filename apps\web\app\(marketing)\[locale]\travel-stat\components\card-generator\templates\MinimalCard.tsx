"use client";

import { Building2, Flag, MapPin } from "lucide-react";
import React from "react";
import type {
	CardCustomization,
	CardTemplateProps,
	CountryData,
	TravelPoint,
} from "../types/cardTypes";

interface MinimalCardProps extends CardTemplateProps {
	mapImageData?: {
		dataURL: string;
		dimensions: { width: number; height: number };
	};
	mapComponent?: React.ReactNode;
	travelPoints: TravelPoint[];
	visitedCountries: CountryData[];
	customization: CardCustomization;
	platform?: string;
	isPreview?: boolean;
}

export function MinimalCard({
	mapImageData,
	mapComponent,
	travelPoints,
	visitedCountries,
	customization,
	platform = "instagram",
	isPreview = false,
}: MinimalCardProps) {
	// 极简风格的默认配置
	const colors = customization.colors || {
		primary: "#2c3e50",
		secondary: "#7f8c8d",
		accent: "#3498db",
		background: "#ffffff",
		text: "#2c3e50",
	};

	const typography = customization.typography || {
		fontFamily:
			"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
		headerSize: 28,
		bodySize: 16,
		titleWeight: 600,
	};

	const layout = customization.layout || {
		padding: 40,
		spacing: 24,
		borderRadius: 0,
		showShadow: false,
	};

	const content = customization.content || {
		showUserInfo: true,
		showDetailedStats: true,
		customTitle: "Travel Journal",
		customFooter: "Every journey tells a story",
	};

	// Ensure showUserInfo and showDetailedStats have proper defaults
	const safeContent = {
		showUserInfo: content.showUserInfo ?? true,
		showDetailedStats: content.showDetailedStats ?? true,
		customTitle: content.customTitle || "Travel Journal",
		customFooter: content.customFooter || "Every journey tells a story",
	};

	// 计算统计数据
	const stats = {
		totalPoints: travelPoints.length,
		totalCountries: visitedCountries.length,
		totalCities: new Set(travelPoints.map((p) => p.city)).size,
		earliestDate:
			travelPoints.length > 0
				? travelPoints.reduce((earliest, point) =>
						new Date(point.date) < new Date(earliest.date)
							? point
							: earliest,
					).date
				: null,
		latestDate:
			travelPoints.length > 0
				? travelPoints.reduce((latest, point) =>
						new Date(point.date) > new Date(latest.date)
							? point
							: latest,
					).date
				: null,
	};

	// 平台尺寸配置
	const platformDimensions = {
		instagram: { width: 1080, height: 1080, aspectRatio: 1 },
		wechat: { width: 1200, height: 900, aspectRatio: 4 / 3 },
		weibo: { width: 1080, height: 1350, aspectRatio: 4 / 5 },
		twitter: { width: 1200, height: 675, aspectRatio: 16 / 9 },
		facebook: { width: 1200, height: 630, aspectRatio: 1.91 },
	};

	const dimensions =
		platformDimensions[platform as keyof typeof platformDimensions] ||
		platformDimensions.instagram;

	// 根据宽高比决定布局策略
	const isWideFormat = dimensions.aspectRatio > 1.5; // Twitter, Facebook
	const isTallFormat = dimensions.aspectRatio < 0.9; // Weibo
	const isSquareFormat =
		dimensions.aspectRatio >= 0.9 && dimensions.aspectRatio <= 1.3; // Instagram, WeChat

	// 动态字体尺寸
	const scale = Math.min(dimensions.width / 1080, 1.5);
	const adjustedTypography = {
		titleSize: Math.round(isWideFormat ? 28 * scale : 36 * scale),
		statsSize: Math.round(isWideFormat ? 36 * scale : 48 * scale),
		headerSize: Math.round((typography.headerSize || 28) * scale),
		bodySize: Math.round((typography.bodySize || 16) * scale),
		smallSize: Math.round(14 * scale),
		titleWeight: typography.titleWeight || 600,
		fontFamily: typography.fontFamily || "'Inter', sans-serif",
	};

	const getLayoutValue = (
		key: "padding" | "spacing" | "borderRadius",
		defaultValue: number,
	): number => {
		const value = layout[key];
		return typeof value === "number" ? value : defaultValue;
	};

	const getDateRange = () => {
		// 优先使用自定义日期文本
		if (content.customDate?.trim()) {
			return content.customDate;
		}
		// 回退到计算的日期范围
		if (!stats.earliestDate || !stats.latestDate) return "";
		const start = new Date(stats.earliestDate).getFullYear();
		const end = new Date(stats.latestDate).getFullYear();
		return start === end ? `${start}` : `${start} – ${end}`;
	};

	// 渲染宽屏布局 (Twitter, Facebook)
	if (isWideFormat) {
		return (
			<div
				data-card-element
				className="relative overflow-hidden"
				style={{
					width: dimensions.width,
					height: dimensions.height,
					fontFamily: adjustedTypography.fontFamily,
					backgroundColor: colors.background,
					color: colors.text,
					padding: getLayoutValue("padding", 40),
					display: "flex",
					flexDirection: "row",
					alignItems: "stretch",
				}}
			>
				{/* 背景装饰 */}
				<div className="absolute inset-0">
					<div
						style={{
							position: "absolute",
							top: 0,
							left: "20%",
							right: "20%",
							height: "2px",
							background: `linear-gradient(90deg, transparent, ${colors.accent}, transparent)`,
						}}
					/>
					<div
						style={{
							position: "absolute",
							bottom: 0,
							left: "30%",
							right: "30%",
							height: "1px",
							background: colors.secondary,
							opacity: 0.3,
						}}
					/>
				</div>

				{/* 左侧内容区域 */}
				<div
					className="flex flex-col justify-between relative z-10"
					style={{
						width: "40%",
						paddingRight: getLayoutValue("spacing", 24),
					}}
				>
					{/* 标题 */}
					{safeContent.showUserInfo && (
						<div className="space-y-3">
							<h1
								style={{
									fontSize: adjustedTypography.titleSize,
									fontWeight: adjustedTypography.titleWeight,
									color: colors.primary,
									margin: 0,
									lineHeight: 1.2,
									letterSpacing: "0.5px",
								}}
							>
								{safeContent.customTitle}
							</h1>
							{getDateRange() && (
								<div
									style={{
										color: colors.secondary,
										fontSize: adjustedTypography.bodySize,
										fontWeight: 400,
										letterSpacing: "1px",
									}}
								>
									{getDateRange()}
								</div>
							)}
						</div>
					)}

					{/* 统计数据 - 垂直排列 */}
					{safeContent.showDetailedStats && (
						<div className="space-y-6">
							{/* 足迹点 */}
							<div className="text-center">
								<div
									style={{
										fontSize: adjustedTypography.statsSize,
										fontWeight: 300,
										color: colors.primary,
										lineHeight: 1,
										marginBottom: "8px",
										fontFeatureSettings: "'tnum'",
									}}
								>
									{stats.totalPoints}
								</div>
								<div
									style={{
										fontSize: adjustedTypography.bodySize,
										color: colors.secondary,
										fontWeight: 500,
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										gap: "8px",
										textTransform: "uppercase",
										letterSpacing: "0.5px",
									}}
								>
									<MapPin
										size={adjustedTypography.bodySize}
									/>
									Places
								</div>
							</div>

							{/* 城市 */}
							<div className="text-center">
								<div
									style={{
										fontSize: adjustedTypography.statsSize,
										fontWeight: 300,
										color: colors.primary,
										lineHeight: 1,
										marginBottom: "8px",
										fontFeatureSettings: "'tnum'",
									}}
								>
									{stats.totalCities}
								</div>
								<div
									style={{
										fontSize: adjustedTypography.bodySize,
										color: colors.secondary,
										fontWeight: 500,
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										gap: "8px",
										textTransform: "uppercase",
										letterSpacing: "0.5px",
									}}
								>
									<Building2
										size={adjustedTypography.bodySize}
									/>
									Cities
								</div>
							</div>

							{/* 国家 */}
							<div className="text-center">
								<div
									style={{
										fontSize: adjustedTypography.statsSize,
										fontWeight: 300,
										color: colors.primary,
										lineHeight: 1,
										marginBottom: "8px",
										fontFeatureSettings: "'tnum'",
									}}
								>
									{stats.totalCountries}
								</div>
								<div
									style={{
										fontSize: adjustedTypography.bodySize,
										color: colors.secondary,
										fontWeight: 500,
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										gap: "8px",
										textTransform: "uppercase",
										letterSpacing: "0.5px",
									}}
								>
									<Flag size={adjustedTypography.bodySize} />
									Countries
								</div>
							</div>
						</div>
					)}

					{/* 页脚 */}
					{safeContent.customFooter && (
						<div
							style={{
								paddingTop: getLayoutValue("spacing", 24),
								borderTop: `1px solid ${colors.secondary}20`,
							}}
						>
							<p
								style={{
									fontSize: adjustedTypography.bodySize,
									color: colors.secondary,
									margin: 0,
									fontWeight: 400,
									fontStyle: "italic",
									letterSpacing: "0.3px",
									textAlign: "center",
								}}
							>
								{safeContent.customFooter}
							</p>
						</div>
					)}
				</div>

				{/* 右侧地图区域 */}
				<div
					className="relative"
					style={{
						width: "60%",
						border: `1px solid ${colors.secondary}20`,
						overflow: "hidden",
					}}
				>
					<div
						style={{
							width: "100%",
							height: "100%",
							position: "relative",
						}}
					>
						{mapComponent ? (
							<div style={{ width: "100%", height: "100%" }}>
								{mapComponent}
							</div>
						) : (
							mapImageData && (
								<img
									src={mapImageData.dataURL}
									alt="Travel Map"
									style={{
										width: "100%",
										height: "100%",
										objectFit: "cover",
									}}
								/>
							)
						)}

						<div
							className="absolute inset-0"
							style={{
								background: `linear-gradient(135deg, ${colors.accent}05, transparent, ${colors.primary}03)`,
							}}
						/>
					</div>
				</div>
			</div>
		);
	}

	// 正方形/竖屏布局 (Instagram, WeChat, Weibo)
	return (
		<div
			data-card-element
			className="relative overflow-hidden"
			style={{
				width: dimensions.width,
				height: dimensions.height,
				fontFamily: adjustedTypography.fontFamily,
				backgroundColor: colors.background,
				color: colors.text,
				padding: getLayoutValue("padding", 40),
				display: "flex",
				flexDirection: "column",
			}}
		>
			{/* 极简背景设计 */}
			<div className="absolute inset-0">
				<div
					style={{
						position: "absolute",
						top: 0,
						left: "20%",
						right: "20%",
						height: "2px",
						background: `linear-gradient(90deg, transparent, ${colors.accent}, transparent)`,
					}}
				/>
				<div
					style={{
						position: "absolute",
						bottom: 0,
						left: "30%",
						right: "30%",
						height: "1px",
						background: colors.secondary,
						opacity: 0.3,
					}}
				/>
			</div>

			{/* Header Section */}
			{safeContent.showUserInfo && (
				<div
					className="text-center relative"
					style={{
						marginBottom: getLayoutValue("spacing", 24) * 2,
						paddingTop: getLayoutValue("spacing", 24),
					}}
				>
					<h1
						style={{
							fontSize: adjustedTypography.titleSize,
							fontWeight: adjustedTypography.titleWeight,
							color: colors.primary,
							margin: 0,
							lineHeight: 1.2,
							textAlign: "center",
							letterSpacing: "0.5px",
							marginBottom: getLayoutValue("spacing", 24) / 2,
						}}
					>
						{safeContent.customTitle}
					</h1>

					{getDateRange() && (
						<div
							style={{
								color: colors.secondary,
								fontSize: adjustedTypography.bodySize,
								fontWeight: 400,
								letterSpacing: "1px",
								textAlign: "center",
							}}
						>
							{getDateRange()}
						</div>
					)}
				</div>
			)}

			{/* Stats Section */}
			{safeContent.showDetailedStats && (
				<div
					style={{
						display: "grid",
						gridTemplateColumns: isTallFormat
							? "1fr"
							: "1fr 1fr 1fr",
						gap:
							getLayoutValue("spacing", 24) *
							(isTallFormat ? 1.5 : 2),
						marginBottom:
							getLayoutValue("spacing", 24) *
							(isTallFormat ? 1 : 2),
						padding: `${getLayoutValue("spacing", 24)}px 0`,
					}}
				>
					{/* 足迹点 */}
					<div className="text-center">
						<div
							style={{
								fontSize: adjustedTypography.statsSize,
								fontWeight: 300,
								color: colors.primary,
								lineHeight: 1,
								marginBottom: "12px",
								fontFeatureSettings: "'tnum'",
							}}
						>
							{stats.totalPoints}
						</div>
						<div
							style={{
								fontSize: adjustedTypography.bodySize,
								color: colors.secondary,
								fontWeight: 500,
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								gap: "8px",
								textTransform: "uppercase",
								letterSpacing: "0.5px",
							}}
						>
							<MapPin size={adjustedTypography.bodySize} />
							Places
						</div>
					</div>

					{/* 城市 */}
					<div className="text-center">
						<div
							style={{
								fontSize: adjustedTypography.statsSize,
								fontWeight: 300,
								color: colors.primary,
								lineHeight: 1,
								marginBottom: "12px",
								fontFeatureSettings: "'tnum'",
							}}
						>
							{stats.totalCities}
						</div>
						<div
							style={{
								fontSize: adjustedTypography.bodySize,
								color: colors.secondary,
								fontWeight: 500,
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								gap: "8px",
								textTransform: "uppercase",
								letterSpacing: "0.5px",
							}}
						>
							<Building2 size={adjustedTypography.bodySize} />
							Cities
						</div>
					</div>

					{/* 国家 */}
					<div className="text-center">
						<div
							style={{
								fontSize: adjustedTypography.statsSize,
								fontWeight: 300,
								color: colors.primary,
								lineHeight: 1,
								marginBottom: "12px",
								fontFeatureSettings: "'tnum'",
							}}
						>
							{stats.totalCountries}
						</div>
						<div
							style={{
								fontSize: adjustedTypography.bodySize,
								color: colors.secondary,
								fontWeight: 500,
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
								gap: "8px",
								textTransform: "uppercase",
								letterSpacing: "0.5px",
							}}
						>
							<Flag size={adjustedTypography.bodySize} />
							Countries
						</div>
					</div>
				</div>
			)}

			{/* Map Section */}
			<div
				className="relative flex-1"
				style={{
					marginBottom: getLayoutValue("spacing", 24) * 2,
					border: `1px solid ${colors.secondary}20`,
					overflow: "hidden",
				}}
			>
				<div
					style={{
						width: "100%",
						height: "100%",
						position: "relative",
					}}
				>
					{mapComponent ? (
						<div style={{ width: "100%", height: "100%" }}>
							{mapComponent}
						</div>
					) : (
						mapImageData && (
							<img
								src={mapImageData.dataURL}
								alt="Travel Map"
								style={{
									width: "100%",
									height: "100%",
									objectFit: "cover",
								}}
							/>
						)
					)}

					<div
						className="absolute inset-0"
						style={{
							background: `linear-gradient(135deg, ${colors.accent}05, transparent, ${colors.primary}03)`,
						}}
					/>
				</div>
			</div>

			{/* Footer Section */}
			{safeContent.customFooter && (
				<div
					className="text-center relative"
					style={{
						paddingTop: getLayoutValue("spacing", 24),
						borderTop: `1px solid ${colors.secondary}20`,
						marginTop: "auto",
					}}
				>
					<p
						style={{
							fontSize: adjustedTypography.bodySize,
							color: colors.secondary,
							margin: 0,
							fontWeight: 400,
							textAlign: "center",
							fontStyle: "italic",
							letterSpacing: "0.3px",
						}}
					>
						{safeContent.customFooter}
					</p>
				</div>
			)}
		</div>
	);
}
