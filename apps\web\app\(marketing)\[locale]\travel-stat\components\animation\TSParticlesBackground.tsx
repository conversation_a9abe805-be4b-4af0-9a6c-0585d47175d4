"use client";

import { use<PERSON><PERSON>back, useMemo, useState } from "react";
import Particles from "react-tsparticles";
import type { Engine } from "tsparticles-engine";
import { loadSlim } from "tsparticles-slim";

// tsParticles 主题类型定义
export type TSParticlesTheme =
	| "meteor-shower"
	| "floating-cities"
	| "travel-routes"
	| "orbital-paths"
	| "aurora-waves"
	| "cosmic-web"
	| "particle-rain"
	| "galaxy-spiral";

interface TSParticlesBackgroundProps {
	theme?: TSParticlesTheme;
	className?: string;
	density?: number;
}

// 配置类型定义
interface ParticleConfig {
	background?: {
		color?: string;
	};
	fpsLimit?: number;
	particles?: {
		number?: {
			value?: number;
			density?: {
				enable?: boolean;
				value_area?: number;
			};
		};
		color?: {
			value?: string | string[];
		};
		shape?: {
			type?: string | string[];
			polygon?: {
				sides?: number;
			};
		};
		opacity?: {
			value?: number;
			random?: boolean;
			animation?: {
				enable?: boolean;
				speed?: number;
				minimumValue?: number;
				sync?: boolean;
			};
		};
		size?: {
			value?: number | { min: number; max: number };
			random?: boolean;
			animation?: {
				enable?: boolean;
				speed?: number;
				minimumValue?: number;
				sync?: boolean;
			};
		};
		links?: {
			enable?: boolean;
			distance?: number;
			color?: string;
			opacity?: number;
			width?: number;
			triangles?: {
				enable?: boolean;
				color?: string;
				opacity?: number;
			};
		};
		move?: {
			enable?: boolean;
			speed?: number | { min: number; max: number };
			direction?: string;
			random?: boolean;
			straight?: boolean;
			outModes?: {
				default?: string;
			};
			attract?: {
				enable?: boolean;
				rotateX?: number;
				rotateY?: number;
			};
			trail?: {
				enable?: boolean;
				length?: number;
				fillColor?: string;
			};
			warp?: boolean;
		};
		life?: {
			duration?: {
				sync?: boolean;
				value?: number;
			};
			count?: number;
			delay?: {
				random?: {
					enable?: boolean;
					minimumValue?: number;
				};
				value?: number;
			};
		};
		wobble?: {
			distance?: number;
			enable?: boolean;
			speed?: {
				angle?: number;
				move?: number;
			};
		};
	};
	interactivity?: {
		detectsOn?: "canvas" | "parent" | "window";
		events?: {
			onHover?: {
				enable?: boolean;
				mode?: string;
			};
			resize?: boolean;
		};
		modes?: {
			grab?: {
				distance?: number;
				links?: {
					opacity?: number;
				};
			};
			connect?: {
				distance?: number;
				links?: {
					opacity?: number;
				};
				radius?: number;
			};
		};
	};
	detectRetina?: boolean;
	emitters?: any;
	responsive?: Array<{
		maxWidth?: number;
		options?: any;
	}>;
}

// 流星雨配置
const meteorShowerConfig: ParticleConfig = {
	background: {
		color: "transparent",
	},
	fpsLimit: 120,
	particles: {
		number: {
			value: 50,
			density: {
				enable: true,
				value_area: 800,
			},
		},
		color: {
			value: ["#ffffff", "#87ceeb", "#ffd700", "#ff6347"],
		},
		shape: {
			type: "circle",
		},
		opacity: {
			value: 0.8,
			random: true,
			animation: {
				enable: true,
				speed: 1,
				minimumValue: 0.1,
				sync: false,
			},
		},
		size: {
			value: { min: 1, max: 4 },
			random: true,
			animation: {
				enable: true,
				speed: 2,
				minimumValue: 0.5,
				sync: false,
			},
		},
		links: {
			enable: false,
		},
		move: {
			enable: true,
			speed: { min: 2, max: 8 },
			direction: "bottomRight",
			random: false,
			straight: false,
			outModes: {
				default: "out",
			},
			attract: {
				enable: false,
			},
		},
		life: {
			duration: {
				sync: false,
				value: 3,
			},
			count: 0,
			delay: {
				random: {
					enable: true,
					minimumValue: 0.5,
				},
				value: 1,
			},
		},
	},
	detectRetina: true,
	emitters: {
		direction: "bottom-right",
		life: {
			count: 0,
			duration: 0.1,
			delay: 2,
		},
		rate: {
			delay: 0.1,
			quantity: 1,
		},
		size: {
			width: 0,
			height: 0,
		},
		position: {
			x: 0,
			y: 0,
		},
	},
};

// 浮动城市配置
const floatingCitiesConfig: ParticleConfig = {
	background: {
		color: "transparent",
	},
	fpsLimit: 60,
	particles: {
		number: {
			value: 80,
			density: {
				enable: true,
				value_area: 800,
			},
		},
		color: {
			value: ["#4a90e2", "#50c878", "#ffa500", "#ff69b4"],
		},
		shape: {
			type: ["circle", "triangle", "polygon"],
			polygon: {
				sides: 6,
			},
		},
		opacity: {
			value: 0.6,
			random: true,
			animation: {
				enable: true,
				speed: 0.5,
				minimumValue: 0.3,
				sync: false,
			},
		},
		size: {
			value: { min: 3, max: 12 },
			random: true,
			animation: {
				enable: true,
				speed: 1,
				minimumValue: 2,
				sync: false,
			},
		},
		links: {
			enable: true,
			distance: 150,
			color: "#ffffff",
			opacity: 0.2,
			width: 1,
			triangles: {
				enable: true,
				color: "#ffffff",
				opacity: 0.05,
			},
		},
		move: {
			enable: true,
			speed: 1,
			direction: "none",
			random: false,
			straight: false,
			outModes: {
				default: "bounce",
			},
			attract: {
				enable: true,
				rotateX: 600,
				rotateY: 1200,
			},
		},
	},
	interactivity: {
		detectsOn: "canvas",
		events: {
			onHover: {
				enable: true,
				mode: "grab",
			},
			resize: true,
		},
		modes: {
			grab: {
				distance: 200,
				links: {
					opacity: 0.5,
				},
			},
		},
	},
	detectRetina: true,
};

// 旅行路线配置
const travelRoutesConfig: ParticleConfig = {
	background: {
		color: "transparent",
	},
	fpsLimit: 60,
	particles: {
		number: {
			value: 30,
			density: {
				enable: true,
				value_area: 400,
			},
		},
		color: {
			value: "#ffd700",
		},
		shape: {
			type: "circle",
		},
		opacity: {
			value: 0.8,
			random: false,
			animation: {
				enable: true,
				speed: 2,
				minimumValue: 0.3,
				sync: false,
			},
		},
		size: {
			value: { min: 2, max: 6 },
			random: true,
		},
		links: {
			enable: true,
			distance: 250,
			color: "#ffd700",
			opacity: 0.4,
			width: 2,
			triangles: {
				enable: false,
			},
		},
		move: {
			enable: true,
			speed: 0.5,
			direction: "none",
			random: true,
			straight: false,
			outModes: {
				default: "bounce",
			},
			trail: {
				enable: true,
				length: 10,
				fillColor: "#000000",
			},
		},
	},
	detectRetina: true,
};

// 轨道路径配置
const orbitalPathsConfig: ParticleConfig = {
	background: {
		color: "transparent",
	},
	fpsLimit: 60,
	particles: {
		number: {
			value: 60,
		},
		color: {
			value: ["#ff6b6b", "#4ecdc4", "#45b7d1", "#96ceb4", "#feca57"],
		},
		shape: {
			type: "circle",
		},
		opacity: {
			value: 0.7,
			random: true,
		},
		size: {
			value: { min: 1, max: 3 },
			random: true,
		},
		links: {
			enable: false,
		},
		move: {
			enable: true,
			speed: 2,
			direction: "none",
			random: false,
			straight: false,
			outModes: {
				default: "bounce",
			},
		},
	},
	detectRetina: true,
};

// 极光波浪配置
const auroraWavesConfig: ParticleConfig = {
	background: {
		color: "transparent",
	},
	fpsLimit: 60,
	particles: {
		number: {
			value: 100,
			density: {
				enable: true,
				value_area: 800,
			},
		},
		color: {
			value: ["#00ff88", "#0088ff", "#8800ff", "#ff0088"],
		},
		shape: {
			type: "circle",
		},
		opacity: {
			value: 0.5,
			random: true,
			animation: {
				enable: true,
				speed: 1,
				minimumValue: 0.1,
				sync: false,
			},
		},
		size: {
			value: { min: 1, max: 5 },
			random: true,
			animation: {
				enable: true,
				speed: 3,
				minimumValue: 0.5,
				sync: false,
			},
		},
		links: {
			enable: false,
		},
		move: {
			enable: true,
			speed: { min: 1, max: 3 },
			direction: "top",
			random: true,
			straight: false,
			outModes: {
				default: "out",
			},
			warp: true,
		},
		wobble: {
			distance: 20,
			enable: true,
			speed: {
				angle: 50,
				move: 10,
			},
		},
	},
	detectRetina: true,
};

// 宇宙网络配置
const cosmicWebConfig: ParticleConfig = {
	background: {
		color: "transparent",
	},
	fpsLimit: 60,
	particles: {
		number: {
			value: 120,
			density: {
				enable: true,
				value_area: 800,
			},
		},
		color: {
			value: "#ffffff",
		},
		shape: {
			type: "circle",
		},
		opacity: {
			value: 0.4,
			random: true,
			animation: {
				enable: true,
				speed: 0.5,
				minimumValue: 0.1,
				sync: false,
			},
		},
		size: {
			value: { min: 1, max: 3 },
			random: true,
		},
		links: {
			enable: true,
			distance: 200,
			color: "#ffffff",
			opacity: 0.15,
			width: 1,
			triangles: {
				enable: true,
				color: "#ffffff",
				opacity: 0.02,
			},
		},
		move: {
			enable: true,
			speed: 0.3,
			direction: "none",
			random: true,
			straight: false,
			outModes: {
				default: "bounce",
			},
		},
	},
	interactivity: {
		detectsOn: "canvas",
		events: {
			onHover: {
				enable: true,
				mode: "connect",
			},
			resize: true,
		},
		modes: {
			connect: {
				distance: 80,
				links: {
					opacity: 0.3,
				},
				radius: 60,
			},
		},
	},
	detectRetina: true,
};

// 粒子雨配置
const particleRainConfig: ParticleConfig = {
	background: {
		color: "transparent",
	},
	fpsLimit: 120,
	particles: {
		number: {
			value: 150,
			density: {
				enable: true,
				value_area: 800,
			},
		},
		color: {
			value: ["#87ceeb", "#4169e1", "#1e90ff", "#00bfff"],
		},
		shape: {
			type: ["circle", "edge"],
		},
		opacity: {
			value: 0.6,
			random: true,
			animation: {
				enable: true,
				speed: 1,
				minimumValue: 0.1,
				sync: false,
			},
		},
		size: {
			value: { min: 0.5, max: 3 },
			random: true,
		},
		links: {
			enable: false,
		},
		move: {
			enable: true,
			speed: { min: 1, max: 6 },
			direction: "bottom",
			random: false,
			straight: true,
			outModes: {
				default: "out",
			},
			attract: {
				enable: false,
			},
		},
	},
	detectRetina: true,
};

// 银河螺旋配置
const galaxySpiralConfig: ParticleConfig = {
	background: {
		color: "transparent",
	},
	fpsLimit: 60,
	particles: {
		number: {
			value: 200,
			density: {
				enable: true,
				value_area: 800,
			},
		},
		color: {
			value: ["#ffd700", "#ff6347", "#87ceeb", "#dda0dd"],
		},
		shape: {
			type: "circle",
		},
		opacity: {
			value: 0.8,
			random: true,
			animation: {
				enable: true,
				speed: 0.5,
				minimumValue: 0.3,
				sync: false,
			},
		},
		size: {
			value: { min: 1, max: 4 },
			random: true,
			animation: {
				enable: true,
				speed: 1,
				minimumValue: 0.5,
				sync: false,
			},
		},
		links: {
			enable: false,
		},
		move: {
			enable: true,
			speed: 1,
			direction: "none",
			random: false,
			straight: false,
			outModes: {
				default: "out",
			},
		},
	},
	detectRetina: true,
};

// 配置映射
const PARTICLE_CONFIGS: Record<TSParticlesTheme, ParticleConfig> = {
	"meteor-shower": meteorShowerConfig,
	"floating-cities": floatingCitiesConfig,
	"travel-routes": travelRoutesConfig,
	"orbital-paths": orbitalPathsConfig,
	"aurora-waves": auroraWavesConfig,
	"cosmic-web": cosmicWebConfig,
	"particle-rain": particleRainConfig,
	"galaxy-spiral": galaxySpiralConfig,
};

export function TSParticlesBackground({
	theme = "meteor-shower",
	className = "",
	density = 1,
}: TSParticlesBackgroundProps) {
	const [mounted, setMounted] = useState(false);

	// 确保只在客户端渲染
	useState(() => {
		setMounted(true);
	});

	// 引擎初始化
	const particlesInit = useCallback(
		async (engine: Engine) => {
			console.log("🎆 TSParticles 引擎初始化:", theme);
			await loadSlim(engine);
		},
		[theme],
	);

	// 粒子加载完成回调
	const particlesLoaded = useCallback(async (container: any) => {
		console.log("🌟 TSParticles 容器加载完成:", container);
	}, []);

	// 根据密度调整配置
	const adjustedConfig = useMemo(() => {
		const baseConfig = { ...PARTICLE_CONFIGS[theme] };

		if (baseConfig.particles?.number?.value) {
			baseConfig.particles.number.value = Math.floor(
				baseConfig.particles.number.value * density,
			);
		}

		// 添加响应式配置
		baseConfig.responsive = [
			{
				maxWidth: 768,
				options: {
					particles: {
						number: {
							value: Math.floor(
								(baseConfig.particles?.number?.value || 50) *
									0.5,
							),
						},
					},
				},
			},
			{
				maxWidth: 480,
				options: {
					particles: {
						number: {
							value: Math.floor(
								(baseConfig.particles?.number?.value || 50) *
									0.3,
							),
						},
					},
				},
			},
		];

		return baseConfig;
	}, [theme, density]);

	if (!mounted) return null;

	return (
		<div className={`fixed inset-0 -z-10 ${className}`}>
			<Particles
				id={`tsparticles-${theme}`}
				init={particlesInit}
				loaded={particlesLoaded}
				options={adjustedConfig as any}
				style={{
					position: "absolute",
					top: 0,
					left: 0,
					width: "100%",
					height: "100%",
					pointerEvents: "none",
				}}
			/>
		</div>
	);
}

// 主题预设
export const TSPARTICLES_THEMES: Record<TSParticlesTheme, string> = {
	"meteor-shower": "流星雨",
	"floating-cities": "浮动城市",
	"travel-routes": "旅行路线",
	"orbital-paths": "轨道路径",
	"aurora-waves": "极光波浪",
	"cosmic-web": "宇宙网络",
	"particle-rain": "粒子雨",
	"galaxy-spiral": "银河螺旋",
};
