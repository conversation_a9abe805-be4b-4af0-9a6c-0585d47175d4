"use client";

import { Button } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { X } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";

interface PopoverPortalProps {
	isOpen: boolean;
	position: {
		top: number;
		left: number;
		width?: number;
		height?: number;
		right?: number;
		bottom?: number;
	};
	title: string;
	children: React.ReactNode;
	onClose: () => void;
	onContentClick: (e: React.MouseEvent) => void;
}

interface AdjustedPosition {
	top: number;
	left: number;
	width?: number;
	height?: number;
	transformOrigin: string;
	placement: "top" | "bottom" | "left" | "right" | "center";
	isMobile: boolean;
}

export function PopoverPortal({
	isOpen,
	position,
	title,
	children,
	onClose,
	onContentClick,
}: PopoverPortalProps) {
	const t = useTranslations("travelStat.common");
	const contentRef = useRef<HTMLDialogElement>(null);
	const [adjustedPosition, setAdjustedPosition] = useState<AdjustedPosition>({
		top: position.top,
		left: position.left,
		transformOrigin: "top left",
		placement: "bottom",
		isMobile: false,
	});

	// 计算智能定位
	useEffect(() => {
		if (!isOpen) {
			return;
		}

		const calculatePosition = () => {
			// 检测是否为移动设备
			const isMobileDevice = window.innerWidth < 768;
			const viewportWidth = window.innerWidth;
			const viewportHeight = window.innerHeight;
			const margin = isMobileDevice ? 8 : 16;

			// 移动设备特殊处理 - 使用底部弹窗模式
			if (isMobileDevice) {
				const mobileHeight = Math.min(viewportHeight * 0.8, 600);
				setAdjustedPosition({
					top: viewportHeight - mobileHeight,
					left: margin,
					width: viewportWidth - margin * 2,
					height: mobileHeight,
					transformOrigin: "bottom center",
					placement: "bottom",
					isMobile: true,
				});
				return;
			}

			// 桌面端智能定位
			const content = contentRef.current;
			if (!content) {
				// 使用默认尺寸进行初始计算
				const defaultWidth = 384; // w-96 = 384px
				const defaultHeight = 400;
				calculateDesktopPosition(defaultWidth, defaultHeight);
				return;
			}

			// 使用实际尺寸
			const rect = content.getBoundingClientRect();
			// 确保最小宽度，避免宽度过小
			const actualWidth = Math.max(rect.width, 320); // w-80 = 320px
			calculateDesktopPosition(actualWidth, rect.height);
		};

		const calculateDesktopPosition = (
			contentWidth: number,
			contentHeight: number,
		) => {
			const viewportWidth = window.innerWidth;
			const viewportHeight = window.innerHeight;
			const margin = 16;
			const {
				top: triggerTop,
				left: triggerLeft,
				width: triggerWidth = 0,
				height: triggerHeight = 0,
			} = position;

			let finalTop = triggerTop;
			let finalLeft = triggerLeft;
			let transformOrigin = "top right";
			let placement: "top" | "bottom" | "left" | "right" | "center" =
				"left";

			// 优先在左侧显示 popover
			const leftSpace = triggerLeft;
			const rightSpace = viewportWidth - triggerLeft;

			if (leftSpace >= contentWidth + margin) {
				// 左侧空间充足，在左侧显示
				finalLeft = triggerLeft - contentWidth - 8; // 8px 间距
				transformOrigin = "top right";
				placement = "left";
			} else if (rightSpace >= contentWidth + margin) {
				// 左侧空间不足，但右侧空间充足，在右侧显示
				finalLeft = triggerLeft + triggerWidth + 8; // 触发器右侧 + 8px 间距
				transformOrigin = "top left";
				placement = "right";
			} else {
				// 两侧空间都不足，居中显示
				finalLeft = Math.max(
					margin,
					(viewportWidth - contentWidth) / 2,
				);
				transformOrigin = "top center";
				placement = "center";
			}

			// 垂直定位逻辑 - 与触发器顶部对齐
			const bottomSpace = viewportHeight - triggerTop;
			const topSpace = triggerTop;

			if (bottomSpace >= contentHeight + margin) {
				// 下方空间充足，与触发器顶部对齐
				finalTop = triggerTop;
			} else if (topSpace >= contentHeight + margin) {
				// 上方空间充足，向上显示
				finalTop = triggerTop - contentHeight + 40; // 40px 是触发器的大概高度
				transformOrigin = transformOrigin.replace("top", "bottom");
			} else {
				// 上下空间都不足，垂直居中
				finalTop = Math.max(
					margin,
					(viewportHeight - contentHeight) / 2,
				);
				transformOrigin = transformOrigin.replace("top", "center");
			}

			// 最终边界检查
			finalLeft = Math.max(
				margin,
				Math.min(finalLeft, viewportWidth - contentWidth - margin),
			);
			finalTop = Math.max(
				margin,
				Math.min(finalTop, viewportHeight - contentHeight - margin),
			);

			setAdjustedPosition({
				top: finalTop,
				left: finalLeft,
				transformOrigin,
				placement,
				isMobile: false,
			});
		};

		// 使用 setTimeout 确保 DOM 更新完成
		const timer = setTimeout(calculatePosition, 0);

		// 监听窗口大小变化
		const handleResize = () => {
			calculatePosition();
		};

		window.addEventListener("resize", handleResize);
		return () => {
			clearTimeout(timer);
			window.removeEventListener("resize", handleResize);
		};
	}, [isOpen, position]);

	if (!isOpen || typeof document === "undefined") {
		return null;
	}

	const containerStyle = adjustedPosition.isMobile
		? {
				top: adjustedPosition.top,
				left: adjustedPosition.left,
				width: adjustedPosition.width,
				height: adjustedPosition.height,
				zIndex: 99999,
			}
		: {
				top: adjustedPosition.top,
				left: adjustedPosition.left,
				zIndex: 99999,
				transformOrigin: adjustedPosition.transformOrigin,
			};

	return createPortal(
		<div
			className={`fixed pointer-events-none transition-all duration-200 ease-out ${
				adjustedPosition.isMobile ? "inset-x-0" : ""
			}`}
			style={containerStyle}
		>
			<dialog
				ref={contentRef}
				className={`pointer-events-auto ${
					adjustedPosition.isMobile
						? "animate-in slide-in-from-bottom duration-300 h-full"
						: "animate-in fade-in-0 zoom-in-95 duration-200"
				}`}
				data-popover-content
				onClick={onContentClick}
				onKeyDown={(e) => {
					if (e.key === "Escape") {
						onClose();
					}
				}}
				aria-labelledby="popover-title"
				open={isOpen}
			>
				<Card
					className={`bg-white border-sky-200 shadow-xl overflow-hidden ${
						adjustedPosition.isMobile
							? "w-full h-full rounded-t-xl rounded-b-none"
							: "w-80 sm:w-96 max-h-[calc(100vh-32px)] rounded-xl"
					}`}
				>
					<div className="flex items-center justify-between p-3 border-b border-sky-100 flex-shrink-0">
						{adjustedPosition.isMobile && (
							<div className="w-8 h-1 bg-gray-300 rounded-full mx-auto absolute left-1/2 -translate-x-1/2 -top-2" />
						)}
						<span
							id="popover-title"
							className="text-sm font-medium text-gray-700"
						>
							{title}
						</span>
						<Button
							onClick={(e) => {
								e.stopPropagation();
								onClose();
							}}
							size="sm"
							variant="ghost"
							className="p-1 hover:bg-gray-100 flex-shrink-0 rounded-md"
							aria-label={t("closePopover")}
						>
							<X className="w-4 h-4" />
						</Button>
					</div>
					<div className="overflow-y-auto flex-1">{children}</div>
				</Card>
			</dialog>
		</div>,
		document.body,
	);
}
