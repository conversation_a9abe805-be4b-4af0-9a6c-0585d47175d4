import { Building2, Flag, MapPin } from "lucide-react";
import React from "react";
import type {
	CardCustomization,
	CardProps,
	CountryData,
	TravelPoint,
} from "../types/cardTypes";

interface HandDrawnCardProps extends CardProps {
	mapImageData?: {
		dataURL: string;
		dimensions: { width: number; height: number };
	};
	mapComponent?: React.ReactNode;
	travelPoints: TravelPoint[];
	visitedCountries: CountryData[];
	customization: CardCustomization;
	platform?: "instagram" | "wechat" | "weibo" | "twitter" | "facebook";
	isPreview?: boolean;
	className?: string;
}

export function HandDrawnCard({
	mapImageData,
	mapComponent,
	travelPoints,
	visitedCountries,
	customization,
	platform = "instagram",
	isPreview = false,
	className = "",
}: HandDrawnCardProps) {
	// 计算统计数据
	const stats = {
		footsteps: travelPoints.length,
		cities: new Set(travelPoints.map((p) => p.city)).size,
		countries: visitedCountries.length,
	};

	// 获取自定义日期文本
	const getCustomDate = () => {
		// 使用自定义日期文本
		if (customization.content?.customDate?.trim()) {
			return customization.content.customDate;
		}
		// 回退到计算的日期范围
		if (travelPoints.length > 0) {
			const earliestDate = travelPoints.reduce((earliest, point) =>
				new Date(point.date) < new Date(earliest.date)
					? point
					: earliest,
			).date;
			const latestDate = travelPoints.reduce((latest, point) =>
				new Date(point.date) > new Date(latest.date) ? point : latest,
			).date;
			const start = new Date(earliestDate).getFullYear();
			const end = new Date(latestDate).getFullYear();
			return start === end ? `${start}` : `${start} - ${end}`;
		}
		return "";
	};

	// 平台尺寸配置
	const platformDimensions = {
		instagram: { width: 1080, height: 1080, aspectRatio: 1 },
		wechat: { width: 1200, height: 900, aspectRatio: 4 / 3 },
		weibo: { width: 1080, height: 1350, aspectRatio: 4 / 5 },
		twitter: { width: 1200, height: 675, aspectRatio: 16 / 9 },
		facebook: { width: 1200, height: 630, aspectRatio: 1.91 },
	};

	const dimensions = platformDimensions[platform];
	const scaleFactor = dimensions.width / 1080;

	// 根据宽高比决定布局策略
	const isWideFormat = dimensions.aspectRatio > 1.5; // Twitter, Facebook
	const isTallFormat = dimensions.aspectRatio < 0.9; // Weibo
	const isSquareFormat =
		dimensions.aspectRatio >= 0.9 && dimensions.aspectRatio <= 1.3; // Instagram, WeChat

	// 调整文字大小 - 更大更易读
	const fontSize = {
		title: isWideFormat ? 48 : isTallFormat ? 52 : 50,
		subtitle: isWideFormat ? 22 : isTallFormat ? 24 : 23,
		stats: isWideFormat ? 44 : isTallFormat ? 50 : 48,
		labels: isWideFormat ? 20 : isTallFormat ? 26 : 25, // 横屏模式标签字体调小
		bigNumber: isWideFormat ? 45 : isTallFormat ? 70 : 65, // 横屏模式数字字体调小
	};

	return (
		<div
			className={`relative overflow-hidden ${className}`}
			style={{
				width: `${dimensions.width}px`,
				height: `${dimensions.height}px`,
				backgroundColor: "#FFF8E7", // 更温暖的奶油色背景
				fontFamily: "'Kalam', 'Comic Neue', cursive",
				position: "relative",
			}}
		>
			{/* 手绘纸张纹理背景 */}
			<div
				className="absolute inset-0"
				style={{
					backgroundImage: `
						radial-gradient(circle at 20% 50%, rgba(255, 182, 193, 0.1) 0%, transparent 50%),
						radial-gradient(circle at 80% 20%, rgba(255, 218, 185, 0.1) 0%, transparent 50%),
						radial-gradient(circle at 40% 80%, rgba(255, 239, 213, 0.1) 0%, transparent 50%)
					`,
				}}
			/>

			{/* 手绘涂鸦装饰 */}
			<svg
				className="absolute inset-0 pointer-events-none"
				width={dimensions.width}
				height={dimensions.height}
				style={{ opacity: 0.6 }}
				aria-hidden="true"
			>
				{/* 飘动的心形 */}
				<g>
					<path
						d="M100 80 Q90 70 85 80 Q80 90 85 95 Q90 100 100 105 Q110 100 115 95 Q120 90 115 80 Q110 70 100 80Z"
						fill="#FF69B4"
						opacity="0.4"
						transform="rotate(15)"
					/>
					<path
						d="M200 120 Q190 110 185 120 Q180 130 185 135 Q190 140 200 145 Q210 140 215 135 Q220 130 215 120 Q210 110 200 120Z"
						fill="#FFB6C1"
						opacity="0.4"
						transform="rotate(-10)"
					/>
				</g>

				{/* 手绘星星 */}
				<g>
					<path
						d="M150 50 L155 65 L170 65 L158 75 L163 90 L150 80 L137 90 L142 75 L130 65 L145 65 Z"
						fill="#FFD700"
						opacity="0.5"
						transform="rotate(25)"
					/>
					<path
						d="M${dimensions.width - 80} 60 L${dimensions.width - 75} 75 L${dimensions.width - 60} 75 L${dimensions.width - 72} 85 L${dimensions.width - 67} 100 L${dimensions.width - 80} 90 L${dimensions.width - 93} 100 L${dimensions.width - 88} 85 L${dimensions.width - 100} 75 L${dimensions.width - 85} 75 Z"
						fill="#FFA500"
						opacity="0.5"
						transform="rotate(-15)"
					/>
				</g>

				{/* 手绘云朵 */}
				<g>
					<path
						d="M${dimensions.width - 150} 100 Q${dimensions.width - 140} 90 ${dimensions.width - 120} 95 Q${dimensions.width - 100} 100 ${dimensions.width - 105} 115 Q${dimensions.width - 110} 125 ${dimensions.width - 130} 120 Q${dimensions.width - 150} 115 ${dimensions.width - 150} 100Z"
						fill="#87CEEB"
						opacity="0.3"
						transform="rotate(8)"
					/>
				</g>

				{/* 手绘箭头 */}
				<g>
					<path
						d="M50 ${dimensions.height - 150} Q60 ${dimensions.height - 140} 70 ${dimensions.height - 145} L75 ${dimensions.height - 140} L70 ${dimensions.height - 135} L65 ${dimensions.height - 138} Q55 ${dimensions.height - 133} 45 ${dimensions.height - 142} Z"
						fill="#32CD32"
						opacity="0.6"
						transform="rotate(20)"
					/>
				</g>
			</svg>

			{isWideFormat ? (
				// 横屏布局 - 左右分布
				<div
					className="flex h-full p-8"
					style={{ gap: `${40 * scaleFactor}px` }}
				>
					{/* 左侧内容区域 - 填充整个高度 */}
					<div
						className="flex flex-col justify-between"
						style={{
							flex: "0 0 45%",
							height: "100%",
							position: "relative",
						}}
					>
						{/* 标题区域 - 手绘风格 */}
						<div
							className="relative"
							style={{
								transform: "rotate(-1.5deg)",
								marginBottom: `${30 * scaleFactor}px`,
							}}
						>
							{/* 手绘边框 */}
							<svg
								className="absolute inset-0"
								style={{
									width: "100%",
									height: "100%",
									transform: "scale(1.1)",
								}}
								aria-hidden="true"
							>
								<path
									d="M10 20 Q15 10 30 15 L90% 12 Q95% 8 98% 18 L95% 80% Q98% 85% 90% 88% L20 85% Q8 88% 12 78% Z"
									fill="#FFFAF0"
									stroke="#FF6B6B"
									strokeWidth="3"
									strokeDasharray="5,3"
									opacity="0.9"
									style={{
										filter: "drop-shadow(3px 3px 6px rgba(0,0,0,0.15))",
									}}
								/>
							</svg>

							<div
								className="relative z-10 text-center"
								style={{
									padding: `${30 * scaleFactor}px ${25 * scaleFactor}px`,
								}}
							>
								<h1
									style={{
										fontSize: `${fontSize.title * scaleFactor}px`,
										fontWeight: "bold",
										color: "#FF6B6B",
										margin: 0,
										lineHeight: 1.2,
										textShadow:
											"2px 2px 0px rgba(255,255,255,0.8)",
										transform: "rotate(0.5deg)",
									}}
								>
									🌟 我的旅行足迹
								</h1>
								<p
									style={{
										fontSize: `${fontSize.subtitle * scaleFactor}px`,
										color: "#666",
										margin: `${15 * scaleFactor}px 0 0 0`,
										transform: "rotate(-0.3deg)",
										fontStyle: "italic",
									}}
								>
									每一步都是故事 ✈️
								</p>
							</div>
						</div>

						{/* 统计卡片们 - 横屏三个一行紧凑排列 */}
						<div
							className="flex-1 flex justify-center items-center"
							style={{ gap: `${15 * scaleFactor}px` }}
						>
							{/* 足迹点数 */}
							<div
								className="relative flex-1"
								style={{
									transform: "rotate(1deg)",
									maxWidth: "30%",
								}}
							>
								<div
									style={{
										backgroundColor: "#FFE4E1",
										padding: `${18 * scaleFactor}px ${12 * scaleFactor}px`,
										borderRadius: "15px",
										border: "2px dashed #FF69B4",
										boxShadow:
											"0 3px 10px rgba(255,105,180,0.2)",
										textAlign: "center",
									}}
								>
									<MapPin
										size={35 * scaleFactor}
										color="#FF1493"
										style={{
											transform: "rotate(-10deg)",
											marginBottom: `${8 * scaleFactor}px`,
										}}
									/>
									<div
										style={{
											fontSize: `${fontSize.bigNumber * scaleFactor}px`,
											fontWeight: "900",
											color: "#FF1493",
											lineHeight: 1,
										}}
									>
										{stats.footsteps}
									</div>
									<div
										style={{
											fontSize: `${fontSize.labels * scaleFactor}px`,
											color: "#666",
											fontWeight: "600",
											marginTop: `${5 * scaleFactor}px`,
										}}
									>
										个足迹点
									</div>
								</div>
								{/* 手绘装饰 */}
								<div
									style={{
										position: "absolute",
										top: `-${8 * scaleFactor}px`,
										right: `-${8 * scaleFactor}px`,
										fontSize: `${20 * scaleFactor}px`,
										transform: "rotate(20deg)",
									}}
								>
									🎈
								</div>
							</div>

							{/* 城市数 */}
							<div
								className="relative flex-1"
								style={{
									transform: "rotate(-1deg)",
									maxWidth: "30%",
								}}
							>
								<div
									style={{
										backgroundColor: "#E0F6FF",
										padding: `${18 * scaleFactor}px ${12 * scaleFactor}px`,
										borderRadius: "15px",
										border: "2px dashed #4169E1",
										boxShadow:
											"0 3px 10px rgba(65,105,225,0.2)",
										textAlign: "center",
									}}
								>
									<Building2
										size={35 * scaleFactor}
										color="#4169E1"
										style={{
											transform: "rotate(8deg)",
											marginBottom: `${8 * scaleFactor}px`,
										}}
									/>
									<div
										style={{
											fontSize: `${fontSize.bigNumber * scaleFactor}px`,
											fontWeight: "900",
											color: "#4169E1",
											lineHeight: 1,
										}}
									>
										{stats.cities}
									</div>
									<div
										style={{
											fontSize: `${fontSize.labels * scaleFactor}px`,
											color: "#666",
											fontWeight: "600",
											marginTop: `${5 * scaleFactor}px`,
										}}
									>
										座城市
									</div>
								</div>
								{/* 手绘装饰 */}
								<div
									style={{
										position: "absolute",
										bottom: `-${8 * scaleFactor}px`,
										left: `-${10 * scaleFactor}px`,
										fontSize: `${18 * scaleFactor}px`,
										transform: "rotate(-15deg)",
									}}
								>
									🏢
								</div>
							</div>

							{/* 国家数 */}
							<div
								className="relative flex-1"
								style={{
									transform: "rotate(0.5deg)",
									maxWidth: "30%",
								}}
							>
								<div
									style={{
										backgroundColor: "#F0FFF0",
										padding: `${18 * scaleFactor}px ${12 * scaleFactor}px`,
										borderRadius: "15px",
										border: "2px dashed #32CD32",
										boxShadow:
											"0 3px 10px rgba(50,205,50,0.2)",
										textAlign: "center",
									}}
								>
									<Flag
										size={35 * scaleFactor}
										color="#228B22"
										style={{
											transform: "rotate(-5deg)",
											marginBottom: `${8 * scaleFactor}px`,
										}}
									/>
									<div
										style={{
											fontSize: `${fontSize.bigNumber * scaleFactor}px`,
											fontWeight: "900",
											color: "#228B22",
											lineHeight: 1,
										}}
									>
										{stats.countries}
									</div>
									<div
										style={{
											fontSize: `${fontSize.labels * scaleFactor}px`,
											color: "#666",
											fontWeight: "600",
											marginTop: `${5 * scaleFactor}px`,
										}}
									>
										个国家
									</div>
								</div>
								{/* 手绘装饰 */}
								<div
									style={{
										position: "absolute",
										top: `-${10 * scaleFactor}px`,
										left: `-${8 * scaleFactor}px`,
										fontSize: `${16 * scaleFactor}px`,
										transform: "rotate(12deg)",
									}}
								>
									🌍
								</div>
							</div>
						</div>

						{/* 底部装饰 */}
						<div
							className="text-center"
							style={{
								marginTop: `${20 * scaleFactor}px`,
								transform: "rotate(-0.5deg)",
							}}
						>
							<div
								style={{
									fontSize: `${18 * scaleFactor}px`,
									color: "#999",
									fontStyle: "italic",
								}}
							>
								{customization.content?.customFooter ||
									"✨ 用心记录每一段旅程 ✨"}
							</div>
						</div>
					</div>

					{/* 右侧地图区域 */}
					<div className="flex-1 relative" style={{ height: "100%" }}>
						{/* 地图容器 - 手绘相框风格 */}
						<div
							className="relative h-full"
							style={{
								transform: "rotate(0.8deg)",
								padding: `${15 * scaleFactor}px`,
							}}
						>
							{/* 手绘相框 */}
							<div
								style={{
									position: "absolute",
									inset: 0,
									background: "#FFFFFF",
									borderRadius: "15px",
									boxShadow: "0 10px 30px rgba(0,0,0,0.15)",
									border: "4px solid #DDD",
									transform: "rotate(-0.5deg)",
								}}
							/>
							<div
								style={{
									position: "absolute",
									inset: `${8 * scaleFactor}px`,
									background: "#FFFAF0",
									borderRadius: "10px",
									border: "2px dashed #FFB347",
								}}
							/>

							{/* 实际地图 */}
							<div
								style={{
									position: "relative",
									zIndex: 2,
									height: "100%",
									margin: `${20 * scaleFactor}px`,
									borderRadius: "8px",
									overflow: "hidden",
								}}
							>
								{mapComponent ? (
									<div
										style={{
											width: "100%",
											height: "100%",
										}}
									>
										{mapComponent}
									</div>
								) : mapImageData ? (
									<img
										src={mapImageData.dataURL}
										alt="旅行地图"
										style={{
											width: "100%",
											height: "100%",
											objectFit: "cover",
										}}
									/>
								) : null}

								{/* 胶带效果 */}
								<div
									style={{
										position: "absolute",
										top: `-${10 * scaleFactor}px`,
										left: "20%",
										width: `${80 * scaleFactor}px`,
										height: `${20 * scaleFactor}px`,
										background: "rgba(255,255,0,0.7)",
										transform: "rotate(-5deg)",
										borderRadius: "2px",
									}}
								/>
								<div
									style={{
										position: "absolute",
										bottom: `-${10 * scaleFactor}px`,
										right: "25%",
										width: `${80 * scaleFactor}px`,
										height: `${20 * scaleFactor}px`,
										background: "rgba(255,255,0,0.7)",
										transform: "rotate(8deg)",
										borderRadius: "2px",
									}}
								/>
							</div>
						</div>
					</div>
				</div>
			) : (
				// 竖屏布局 - 上下分布，更宽松
				<div className="flex flex-col h-full p-6">
					{/* 顶部标题 */}
					<div
						className="relative mb-8"
						style={{
							transform: "rotate(-0.8deg)",
						}}
					>
						{/* 手绘横幅背景 */}
						<svg
							className="absolute inset-0"
							style={{
								width: "100%",
								height: "120%",
								top: "-10%",
							}}
							aria-hidden="true"
						>
							<path
								d="M5% 30% Q10% 10% 25% 20% L75% 15% Q90% 8% 95% 25% L92% 70% Q95% 85% 80% 82% L25% 85% Q8% 88% 8% 70% Z"
								fill="#FFF0F5"
								stroke="#FF69B4"
								strokeWidth="4"
								strokeDasharray="8,4"
								opacity="0.95"
								style={{
									filter: "drop-shadow(4px 4px 8px rgba(0,0,0,0.1))",
								}}
							/>
						</svg>

						<div
							className="relative z-10 text-center"
							style={{
								padding: `${35 * scaleFactor}px ${30 * scaleFactor}px`,
							}}
						>
							<h1
								style={{
									fontSize: `${fontSize.title * scaleFactor}px`,
									fontWeight: "bold",
									color: "#FF1493",
									margin: 0,
									lineHeight: 1.2,
									textShadow:
										"3px 3px 0px rgba(255,255,255,0.8)",
									transform: "rotate(0.3deg)",
								}}
							>
								🌟 我的旅行足迹 🌟
							</h1>
							<p
								style={{
									fontSize: `${fontSize.subtitle * scaleFactor}px`,
									color: "#666",
									margin: `${18 * scaleFactor}px 0 0 0`,
									transform: "rotate(-0.2deg)",
									fontStyle: "italic",
								}}
							>
								每一步都值得纪念 ✈️💕
							</p>
						</div>
					</div>

					{/* 统计信息 - 三个卡片同一行 */}
					<div
						className="mb-8 flex"
						style={{
							gap: `${15 * scaleFactor}px`,
							justifyContent: "space-between",
						}}
					>
						{/* 足迹点数 */}
						<div
							className="relative flex-1"
							style={{
								transform: "rotate(1.2deg)",
							}}
						>
							<div
								style={{
									backgroundColor: "#FFE4E1",
									padding: `${20 * scaleFactor}px ${15 * scaleFactor}px`,
									borderRadius: "20px",
									border: "3px dashed #FF69B4",
									boxShadow:
										"0 6px 15px rgba(255,105,180,0.25)",
									textAlign: "center",
								}}
							>
								<MapPin
									size={40 * scaleFactor}
									color="#FF1493"
									style={{
										transform: "rotate(-10deg)",
										marginBottom: `${8 * scaleFactor}px`,
									}}
								/>
								<div
									style={{
										fontSize: `${(fontSize.bigNumber - 5) * scaleFactor}px`,
										fontWeight: "900",
										color: "#FF1493",
										lineHeight: 1,
									}}
								>
									{stats.footsteps}
								</div>
								<div
									style={{
										fontSize: `${(fontSize.labels - 1) * scaleFactor}px`,
										color: "#666",
										fontWeight: "600",
										marginTop: `${5 * scaleFactor}px`,
									}}
								>
									个足迹点
								</div>
								<div
									style={{
										position: "absolute",
										top: `-${8 * scaleFactor}px`,
										right: `-${8 * scaleFactor}px`,
										fontSize: `${25 * scaleFactor}px`,
										transform: "rotate(15deg)",
									}}
								>
									🎈
								</div>
							</div>
						</div>

						{/* 城市数 */}
						<div
							className="relative flex-1"
							style={{
								transform: "rotate(-0.8deg)",
							}}
						>
							<div
								style={{
									backgroundColor: "#E0F6FF",
									padding: `${20 * scaleFactor}px ${15 * scaleFactor}px`,
									borderRadius: "20px",
									border: "3px dashed #4169E1",
									boxShadow:
										"0 6px 15px rgba(65,105,225,0.25)",
									textAlign: "center",
								}}
							>
								<Building2
									size={40 * scaleFactor}
									color="#4169E1"
									style={{
										transform: "rotate(8deg)",
										marginBottom: `${8 * scaleFactor}px`,
									}}
								/>
								<div
									style={{
										fontSize: `${(fontSize.bigNumber - 5) * scaleFactor}px`,
										fontWeight: "900",
										color: "#4169E1",
										lineHeight: 1,
									}}
								>
									{stats.cities}
								</div>
								<div
									style={{
										fontSize: `${(fontSize.labels - 1) * scaleFactor}px`,
										color: "#666",
										fontWeight: "600",
										marginTop: `${5 * scaleFactor}px`,
									}}
								>
									座城市
								</div>
								<div
									style={{
										position: "absolute",
										bottom: `-${8 * scaleFactor}px`,
										right: `-${8 * scaleFactor}px`,
										fontSize: `${25 * scaleFactor}px`,
										transform: "rotate(-15deg)",
									}}
								>
									🏢
								</div>
							</div>
						</div>

						{/* 国家数 */}
						<div
							className="relative flex-1"
							style={{
								transform: "rotate(1.2deg)",
							}}
						>
							<div
								style={{
									backgroundColor: "#F0FFF0",
									padding: `${20 * scaleFactor}px ${15 * scaleFactor}px`,
									borderRadius: "20px",
									border: "3px dashed #32CD32",
									boxShadow:
										"0 6px 15px rgba(50,205,50,0.25)",
									textAlign: "center",
								}}
							>
								<Flag
									size={40 * scaleFactor}
									color="#228B22"
									style={{
										transform: "rotate(-12deg)",
										marginBottom: `${8 * scaleFactor}px`,
									}}
								/>
								<div
									style={{
										fontSize: `${(fontSize.bigNumber - 5) * scaleFactor}px`,
										fontWeight: "900",
										color: "#228B22",
										lineHeight: 1,
									}}
								>
									{stats.countries}
								</div>
								<div
									style={{
										fontSize: `${(fontSize.labels - 1) * scaleFactor}px`,
										color: "#666",
										fontWeight: "600",
										marginTop: `${5 * scaleFactor}px`,
									}}
								>
									个国家
								</div>
								<div
									style={{
										position: "absolute",
										top: `-${8 * scaleFactor}px`,
										left: `-${8 * scaleFactor}px`,
										fontSize: `${20 * scaleFactor}px`,
										transform: "rotate(20deg)",
									}}
								>
									🌍
								</div>
							</div>
						</div>
					</div>

					{/* 地图区域 */}
					<div className="flex-1 relative">
						{/* 地图容器 - 手绘相框风格 */}
						<div
							className="relative h-full"
							style={{
								transform: "rotate(-0.3deg)",
								padding: `${12 * scaleFactor}px`,
							}}
						>
							{/* 手绘相框 */}
							<div
								style={{
									position: "absolute",
									inset: 0,
									background: "#FFFFFF",
									borderRadius: "20px",
									boxShadow: "0 12px 25px rgba(0,0,0,0.15)",
									border: "5px solid #DDD",
									transform: "rotate(0.4deg)",
								}}
							/>
							<div
								style={{
									position: "absolute",
									inset: `${10 * scaleFactor}px`,
									background: "#FFFAF0",
									borderRadius: "15px",
									border: "3px dashed #FFB347",
								}}
							/>

							{/* 实际地图 */}
							<div
								style={{
									position: "relative",
									zIndex: 2,
									height: "100%",
									margin: `${25 * scaleFactor}px`,
									borderRadius: "12px",
									overflow: "hidden",
								}}
							>
								{mapComponent ? (
									<div
										style={{
											width: "100%",
											height: "100%",
										}}
									>
										{mapComponent}
									</div>
								) : mapImageData ? (
									<img
										src={mapImageData.dataURL}
										alt="旅行地图"
										style={{
											width: "100%",
											height: "100%",
											objectFit: "cover",
										}}
									/>
								) : null}

								{/* 胶带效果 */}
								<div
									style={{
										position: "absolute",
										top: `-${12 * scaleFactor}px`,
										left: "15%",
										width: `${100 * scaleFactor}px`,
										height: `${25 * scaleFactor}px`,
										background: "rgba(255,255,0,0.7)",
										transform: "rotate(-8deg)",
										borderRadius: "3px",
									}}
								/>
								<div
									style={{
										position: "absolute",
										bottom: `-${12 * scaleFactor}px`,
										right: "20%",
										width: `${100 * scaleFactor}px`,
										height: `${25 * scaleFactor}px`,
										background: "rgba(255,255,0,0.7)",
										transform: "rotate(12deg)",
										borderRadius: "3px",
									}}
								/>
								<div
									style={{
										position: "absolute",
										top: "25%",
										right: `-${12 * scaleFactor}px`,
										width: `${25 * scaleFactor}px`,
										height: `${100 * scaleFactor}px`,
										background: "rgba(255,182,193,0.7)",
										transform: "rotate(5deg)",
										borderRadius: "3px",
									}}
								/>
							</div>

							{/* 底部水印 */}
							<div
								style={{
									position: "absolute",
									bottom: `${8 * scaleFactor}px`,
									right: `${15 * scaleFactor}px`,
									fontSize: `${16 * scaleFactor}px`,
									color: "rgba(139, 69, 19, 0.4)",
									fontStyle: "italic",
									transform: "rotate(-2deg)",
									zIndex: 3,
								}}
							>
								{customization.content?.customFooter ||
									"✨ 旅行足迹生成器 ✨"}
							</div>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
