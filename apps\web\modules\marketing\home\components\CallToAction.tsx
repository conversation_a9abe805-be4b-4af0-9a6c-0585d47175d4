"use client";

import { But<PERSON> } from "@ui/components/button";
import Link from "next/link";
import { useEffect, useState } from "react";

export function CallToAction() {
	const [isVisible, setIsVisible] = useState(false);
	const [sparkles, setSparkles] = useState<
		Array<{ id: number; x: number; y: number; delay: number }>
	>([]);

	useEffect(() => {
		const timer = setTimeout(() => setIsVisible(true), 300);

		// 生成随机闪烁星星
		const newSparkles = Array.from({ length: 12 }, (_, i) => ({
			id: i,
			x: Math.random() * 100,
			y: Math.random() * 100,
			delay: Math.random() * 3,
		}));
		setSparkles(newSparkles);

		return () => clearTimeout(timer);
	}, []);

	return (
		<section className="py-24 bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 relative overflow-hidden">
			{/* 背景装饰 */}
			<div className="absolute inset-0">
				{/* 渐变光晕 */}
				<div className="absolute top-0 left-1/4 w-96 h-96 bg-white/10 rounded-full blur-3xl animate-pulse" />
				<div
					className="absolute bottom-0 right-1/4 w-80 h-80 bg-pink-300/20 rounded-full blur-3xl animate-pulse"
					style={{ animationDelay: "1s" }}
				/>

				{/* 闪烁星星 */}
				{sparkles.map((sparkle) => (
					<div
						key={sparkle.id}
						className="absolute w-2 h-2 bg-white rounded-full animate-ping"
						style={{
							left: `${sparkle.x}%`,
							top: `${sparkle.y}%`,
							animationDelay: `${sparkle.delay}s`,
							animationDuration: "3s",
						}}
					/>
				))}

				{/* 几何装饰 */}
				<div className="absolute top-20 left-10 w-4 h-4 bg-white/30 rotate-45 animate-pulse" />
				<div
					className="absolute top-40 right-20 w-6 h-6 border-2 border-white/30 rounded-full animate-pulse"
					style={{ animationDelay: "1.5s" }}
				/>
				<div
					className="absolute bottom-32 left-1/3 w-3 h-3 bg-pink-300/50 rounded-full animate-pulse"
					style={{ animationDelay: "2s" }}
				/>
			</div>

			{/* 主要内容 */}
			<div className="container mx-auto px-4 relative z-10">
				<div
					className={`text-center transition-all duration-1000 ${isVisible ? "opacity-100 transform translate-y-0" : "opacity-0 transform translate-y-8"}`}
				>
					{/* 标题 */}
					<h2 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-8 leading-tight">
						准备好了吗？
					</h2>

					<div
						className={`transition-all duration-1000 delay-300 ${isVisible ? "opacity-100 transform translate-y-0" : "opacity-0 transform translate-y-8"}`}
					>
						<p className="text-xl md:text-2xl text-white/90 max-w-3xl mx-auto leading-relaxed mb-4">
							让我们一起踏上这段记录美好的旅程
						</p>
						<p className="text-lg md:text-xl text-white/80 max-w-2xl mx-auto leading-relaxed mb-12">
							每一次出发都是新的开始，每一个足迹都值得被珍藏
						</p>
					</div>

					{/* 行动按钮 */}
					<div
						className={`transition-all duration-1000 delay-500 ${isVisible ? "opacity-100 transform translate-y-0" : "opacity-0 transform translate-y-8"}`}
					>
						<div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
							<Button
								size="lg"
								className="px-12 py-6 text-xl bg-white text-purple-600 hover:bg-gray-50 shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-500 font-bold"
							>
								<Link
									href="/auth/login"
									className="flex items-center gap-3"
								>
									<span>开始我的旅行记录</span>
									<span className="text-2xl">✨</span>
								</Link>
							</Button>

							<Button
								variant="outline"
								size="lg"
								className="px-10 py-6 text-xl border-2 border-white/50 text-white hover:bg-white/10 backdrop-blur-sm transition-all duration-500 hover:scale-105"
							>
								<Link
									href="#story"
									className="flex items-center gap-3"
								>
									<span>再看看功能</span>
									<svg
										className="w-6 h-6"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<title>查看图标</title>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
										/>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
										/>
									</svg>
								</Link>
							</Button>
						</div>
					</div>

					{/* 小贴士 */}
					<div
						className={`transition-all duration-1000 delay-700 ${isVisible ? "opacity-100 transform translate-y-0" : "opacity-0 transform translate-y-8"}`}
					>
						<div className="inline-flex items-center gap-3 px-8 py-4 bg-white/10 backdrop-blur-sm rounded-full border border-white/30">
							<span className="text-yellow-300 text-xl">💝</span>
							<p className="text-white/90 font-medium">
								完全免费开始，随时可以升级
							</p>
						</div>
					</div>
				</div>

				{/* 底部装饰文字 */}
				<div
					className={`mt-20 text-center transition-all duration-1000 delay-900 ${isVisible ? "opacity-100 transform translate-y-0" : "opacity-0 transform translate-y-8"}`}
				>
					<div className="max-w-4xl mx-auto">
						<p className="text-lg text-white/70 leading-relaxed italic">
							"旅行的意义不在于到达目的地，而在于沿途的风景和内心的成长。
							<br className="hidden md:block" />
							让我们用心记录每一个美好瞬间，让回忆成为最珍贵的财富。"
						</p>
					</div>
				</div>
			</div>

			{/* 底部波浪装饰 */}
			<div className="absolute bottom-0 left-0 right-0">
				<svg
					className="w-full h-24 fill-white dark:fill-slate-900"
					viewBox="0 0 1200 120"
					preserveAspectRatio="none"
				>
					<title>波浪装饰</title>
					<path
						d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z"
						opacity=".25"
					/>
					<path
						d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z"
						opacity=".5"
					/>
					<path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" />
				</svg>
			</div>
		</section>
	);
}
