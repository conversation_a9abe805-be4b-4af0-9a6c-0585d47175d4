// 导入新的类型
import type {
	DiaryStats,
	Footprint,
} from "@repo/api/src/routes/diaries/schemas";
import type {
	FrontendTravelDiary,
	FrontendTravelTimeline,
	TravelDiary,
	TravelDiaryContent,
	UpdateTravelDiaryInput,
} from "@repo/database/src/types/travel-diary";
import type { JSONContent } from "@tiptap/core";
import { fetchWithExport } from "./api-utils";
// 导入我们新创建的转换函数
import { toDatabaseModel, toFrontendModel } from "./model-converters";
import { convertTiptapDocToDiary } from "./tiptap-conversion";

/**
 * 删除指定ID的日记
 */
export async function deleteDiary(id: string): Promise<void> {
	try {
		const response = await fetchWithExport(`/api/diaries/diary/${id}`, {
			method: "DELETE",
			headers: { "Content-Type": "application/json" },
		});

		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.error || "删除日记失败");
		}
	} catch (error) {
		console.error("删除日记时出错:", error);
		throw error;
	}
}

/**
 * 获取指定ID的日记详情
 */
export async function getDiary(id: string): Promise<TravelDiary> {
	try {
		const response = await fetchWithExport(`/api/diaries/diary/${id}`, {
			method: "GET",
			headers: { "Content-Type": "application/json" },
		});

		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.error || "获取日记失败");
		}

		const { diary } = await response.json();
		return diary;
	} catch (error) {
		console.error("获取日记失败", error);
		throw error;
	}
}

/**
 * 创建一个空的日记草稿
 */
export async function createEmptyDiary(): Promise<TravelDiary> {
	try {
		const response = await fetchWithExport("/api/diaries/create-empty", {
			method: "POST",
			headers: { "Content-Type": "application/json" },
			// 不需要 body
		});

		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.error || "创建空日记失败");
		}

		const { diary } = await response.json();
		if (!diary || !diary.id) {
			throw new Error("创建空日记后未能获取有效的日记ID");
		}
		return diary;
	} catch (error) {
		console.error("创建空日记失败:", error);
		throw error;
	}
}

/**
 * 保存完整日记（包括内容和元数据）
 */
export async function saveDiary(
	diaryId: string,
	diary: FrontendTravelDiary,
): Promise<TravelDiary> {
	try {
		console.log("准备保存完整日记，日记ID:", diaryId);

		// 将前端日记模型转换为数据库格式
		const dbModel = toDatabaseModel(diary);

		// 构建更新数据对象，处理可能为null的字段
		const updateData: UpdateTravelDiaryInput = {
			title: diary.title,
			subtitle: diary.subtitle ?? undefined, // 使用空值合并运算符，将null或undefined转换为undefined
			coverImage: diary.coverImage ?? undefined, // 使用空值合并运算符，将null或undefined转换为undefined
			content: dbModel.content,
			isPublic: diary.isPublic,
		};

		console.log("发送的完整数据:", JSON.stringify(updateData, null, 2));

		const response = await fetchWithExport(
			`/api/diaries/diary/${diaryId}`,
			{
				method: "PUT",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify(updateData),
			},
		);

		console.log("保存响应状态:", response.status);

		if (!response.ok) {
			const errorText = await response.text();
			console.error("保存失败 - 状态码:", response.status);
			console.error("响应内容:", errorText);

			// 尝试解析错误响应为JSON
			try {
				const errorData = JSON.parse(errorText);
				console.error("错误详情:", errorData);
				throw new Error(
					errorData.error || errorData.message || "保存日记失败",
				);
			} catch (e) {
				// 如果无法解析为JSON，则返回原始文本
				throw new Error(
					`保存日记失败 (${response.status}): ${errorText}`,
				);
			}
		}

		const data = await response.json();
		console.log("保存成功响应:", data);
		return data.diary || data;
	} catch (error) {
		console.error("保存日记失败:", error);
		throw error;
	}
}

/**
 * 保存日记内容（仅内容部分，不包括元数据）
 * @deprecated 建议使用 saveDiary 代替，以保存完整的日记数据
 */
export async function saveDiaryContent(
	diaryId: string,
	content: TravelDiaryContent,
): Promise<TravelDiary> {
	try {
		console.log("准备保存日记内容，日记ID:", diaryId);
		console.log("发送的内容:", JSON.stringify(content, null, 2));

		const response = await fetchWithExport(
			`/api/diaries/diary/${diaryId}`,
			{
				method: "PUT",
				headers: { "Content-Type": "application/json" },
				body: JSON.stringify({ content }),
			},
		);

		console.log("保存响应状态:", response.status);

		if (!response.ok) {
			const errorText = await response.text();
			console.error("保存失败 - 状态码:", response.status);
			console.error("响应内容:", errorText);

			// 尝试解析错误响应为JSON
			try {
				const errorData = JSON.parse(errorText);
				console.error("错误详情:", errorData);
				throw new Error(
					errorData.error || errorData.message || "保存日记失败",
				);
			} catch (e) {
				// 如果无法解析为JSON，则返回原始文本
				throw new Error(
					`保存日记失败 (${response.status}): ${errorText}`,
				);
			}
		}

		const data = await response.json();
		console.log("保存成功响应:", data);
		return data.diary || data;
	} catch (error) {
		console.error("保存日记失败:", error);
		throw error;
	}
}

/**
 * 获取用户的所有日记
 */
export async function getUserDiaries(): Promise<TravelDiary[]> {
	try {
		const response = await fetchWithExport("/api/diaries/diary", {
			method: "GET",
			headers: { "Content-Type": "application/json" },
		});

		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.error || "获取日记列表失败");
		}

		const { diaries } = await response.json();
		return diaries;
	} catch (error) {
		console.error("获取日记列表失败", error);
		throw error;
	}
}

/**
 * 转换数据库日记数据为前端使用的格式
 */
export function transformDiaryContent(diary: TravelDiary): FrontendTravelDiary {
	// 使用通用转换函数
	return toFrontendModel(diary);
}

/**
 * 将前端日记数据格式转换为数据库存储格式
 */
export function transformToDbFormat(
	timelines: FrontendTravelTimeline[],
): TravelDiaryContent {
	try {
		// 创建一个临时的前端日记对象
		const tempFrontendDiary: FrontendTravelDiary = {
			id: "",
			title: "",
			subtitle: "",
			timelines,
			isPublic: false, // 添加默认值
		};

		console.log(
			"转换前端数据为数据库格式 - 输入:",
			JSON.stringify(timelines, null, 2),
		);

		// 使用通用转换函数获取内容部分
		const dbModel = toDatabaseModel(tempFrontendDiary);
		const result = dbModel.content as TravelDiaryContent;

		console.log("转换后的数据库格式:", JSON.stringify(result, null, 2));
		return result;
	} catch (error) {
		console.error("数据格式转换失败:", error);
		throw new Error(
			`数据格式转换失败: ${error instanceof Error ? error.message : String(error)}`,
		);
	}
}

/**
 * 获取用户日记的统计信息
 */
export async function getDiaryStats(): Promise<DiaryStats> {
	try {
		const response = await fetchWithExport("/api/diaries/stats", {
			method: "GET",
			headers: { "Content-Type": "application/json" },
		});

		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.error || "获取统计信息失败");
		}

		const { stats } = await response.json();
		return stats;
	} catch (error) {
		console.error("获取统计信息失败", error);
		throw error;
	}
}

/**
 * 获取用户的所有足迹（去过的地点）
 */
export async function getFootprints(): Promise<Footprint[]> {
	try {
		const response = await fetchWithExport("/api/diaries/footprints", {
			method: "GET",
			headers: { "Content-Type": "application/json" },
		});

		if (!response.ok) {
			const error = await response.json();
			throw new Error(error.error || "获取足迹信息失败");
		}

		const { footprints } = await response.json();
		return footprints;
	} catch (error) {
		console.error("获取足迹信息失败", error);
		throw error;
	}
}

/**
 * 格式化富文本内容为结构化日记
 */
export async function formatRichTextToDiary(
	diaryId: string,
	richTextContent: JSONContent,
): Promise<FrontendTravelDiary | null> {
	try {
		// 获取原始日记数据，用于保留一些属性
		const originalDiary = await getDiary(diaryId);

		// 使用转换工具将富文本内容转换为日记格式
		const formattedDiary = convertTiptapDocToDiary(
			diaryId,
			richTextContent,
			originalDiary ? transformDiaryContent(originalDiary) : undefined,
		);

		// 保存转换后的日记
		await saveDiary(diaryId, formattedDiary);

		return formattedDiary;
	} catch (error) {
		console.error("格式化富文本内容失败:", error);
		return null;
	}
}

/**
 * 获取日记富文本草稿内容
 */
export async function getDiaryRichTextDraft(
	diaryId: string,
): Promise<JSONContent | null> {
	try {
		// 调用API获取草稿内容
		const response = await fetch(
			`/api/diaries/diary/${diaryId}/richtext-draft`,
			{
				method: "GET",
				headers: {
					"Content-Type": "application/json",
				},
			},
		);

		if (!response.ok) {
			throw new Error(`获取草稿失败: ${response.statusText}`);
		}

		const data = await response.json();
		return data.content || null;
	} catch (error) {
		console.error("获取日记富文本草稿失败:", error);
		return null;
	}
}

/**
 * 保存日记富文本草稿内容
 */
export async function saveDiaryRichTextDraft(
	diaryId: string,
	content: JSONContent,
): Promise<boolean> {
	try {
		// 调用API保存草稿内容
		const response = await fetch(
			`/api/diaries/diary/${diaryId}/richtext-draft`,
			{
				method: "PUT",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({ content }),
			},
		);

		if (!response.ok) {
			throw new Error(`保存草稿失败: ${response.statusText}`);
		}

		return true;
	} catch (error) {
		console.error("保存日记富文本草稿失败:", error);
		return false;
	}
}
