"use client";
import { config } from "@repo/config";
import { NavBar, useSidebarStore } from "@saas/shared/components/NavBar";
import { cn } from "@ui/lib";
import type { PropsWithChildren } from "react";

export function AppWrapper({ children }: PropsWithChildren) {
	const { isCollapsed } = useSidebarStore();

	return (
		<div
			className={cn(
				"bg-[radial-gradient(farthest-corner_at_0%_0%,color-mix(in_oklch,var(--color-primary),transparent_95%)_0%,var(--color-background)_50%)] dark:bg-[radial-gradient(farthest-corner_at_0%_0%,color-mix(in_oklch,var(--color-primary),transparent_90%)_0%,var(--color-background)_50%)]",
				[config.ui.saas.useSidebarLayout ? "" : ""],
			)}
		>
			<NavBar />
			<div
				className={cn("px-0 transition-all duration-300", [
					config.ui.saas.useSidebarLayout
						? "min-h-[calc(100vh-1rem)]"
						: "",
					config.ui.saas.useSidebarLayout && !isCollapsed
						? "md:ml-[280px]"
						: "",
					config.ui.saas.useSidebarLayout && isCollapsed
						? "md:ml-[72px]"
						: "",
				])}
			>
				<main
					className={cn("w-full p-6", [
						config.ui.saas.useSidebarLayout
							? "md:p-6"
							: "container max-w-6xl",
					])}
				>
					{children}
				</main>
			</div>
		</div>
	);
}
