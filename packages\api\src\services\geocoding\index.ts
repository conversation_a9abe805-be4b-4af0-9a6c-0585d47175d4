/**
 * 统一地理编码服务
 * 集成多个提供商，支持负载均衡和故障转移
 */

import { logger } from "@repo/logs";
import type { BaseGeocodingProvider } from "./base-provider";
import {
	GeocodingLoadBalancer,
	type LoadBalanceStrategy,
} from "./load-balancer";
import { AmapProvider } from "./providers/amap-provider";
import { BaiduProvider } from "./providers/baidu-provider";
import { BingProvider } from "./providers/bing-provider";
import { GoogleProvider } from "./providers/google-provider";
import { MapboxProvider } from "./providers/mapbox-provider";
import type {
	BatchGeocodingResult,
	GeocodingConfig,
	GeocodingOptions,
	GeocodingProvider,
	GeocodingStats,
	UnifiedGeocodingResult,
} from "./types";

/**
 * 地理编码服务配置
 */
export interface GeocodingServiceConfig {
	/** 提供商配置列表 */
	providers: GeocodingConfig[];
	/** 负载均衡策略 */
	strategy?: LoadBalanceStrategy;
	/** 默认选项 */
	defaultOptions?: GeocodingOptions;
	/** 是否启用缓存 */
	enableCache?: boolean;
	/** 缓存TTL（秒） */
	cacheTTL?: number;
}

/**
 * 统一地理编码服务
 */
export class UnifiedGeocodingService {
	private loadBalancer: GeocodingLoadBalancer;
	private cache = new Map<
		string,
		{ result: UnifiedGeocodingResult; expiry: number }
	>();
	private config: GeocodingServiceConfig;

	constructor(config: GeocodingServiceConfig) {
		this.config = config;

		// 创建提供商实例
		const providers = this.createProviders(config.providers);

		// 创建负载均衡器
		this.loadBalancer = new GeocodingLoadBalancer(
			providers,
			config.strategy || "smart",
		);

		logger.info("地理编码服务初始化完成", {
			providersCount: providers.length,
			strategy: config.strategy || "smart",
			enableCache: config.enableCache || false,
		});
	}

	/**
	 * 地理编码（地址转坐标）
	 */
	async geocode(
		address: string,
		options: GeocodingOptions = {},
	): Promise<UnifiedGeocodingResult | null> {
		const mergedOptions = { ...this.config.defaultOptions, ...options };
		const cacheKey = this.getCacheKey("geocode", address, mergedOptions);

		// 检查缓存
		if (this.config.enableCache) {
			const cached = this.getFromCache(cacheKey);
			if (cached) {
				logger.info("从缓存返回地理编码结果", { address });
				return cached;
			}
		}

		// 执行地理编码
		const result = await this.loadBalancer.geocode(address, mergedOptions);

		// 缓存结果
		if (result && this.config.enableCache) {
			this.setCache(cacheKey, result);
		}

		return result;
	}

	/**
	 * 反向地理编码（坐标转地址）
	 */
	async reverseGeocode(
		longitude: number,
		latitude: number,
		options: GeocodingOptions = {},
	): Promise<UnifiedGeocodingResult | null> {
		const mergedOptions = { ...this.config.defaultOptions, ...options };
		const cacheKey = this.getCacheKey(
			"reverse",
			`${longitude},${latitude}`,
			mergedOptions,
		);

		// 检查缓存
		if (this.config.enableCache) {
			const cached = this.getFromCache(cacheKey);
			if (cached) {
				logger.info("从缓存返回反向地理编码结果", {
					longitude,
					latitude,
				});
				return cached;
			}
		}

		// 执行反向地理编码
		const result = await this.loadBalancer.reverseGeocode(
			longitude,
			latitude,
			mergedOptions,
		);

		// 缓存结果
		if (result && this.config.enableCache) {
			this.setCache(cacheKey, result);
		}

		return result;
	}

	/**
	 * 批量地理编码
	 */
	async batchGeocode(
		addresses: string[],
		options: GeocodingOptions = {},
	): Promise<BatchGeocodingResult[]> {
		const results: BatchGeocodingResult[] = [];

		for (const address of addresses) {
			try {
				const result = await this.geocode(address, options);
				results.push({
					address,
					result,
					provider: result?.provider,
				});

				// 添加延迟避免API限制
				await this.delay(50);
			} catch (error) {
				results.push({
					address,
					result: null,
					error:
						error instanceof Error ? error.message : String(error),
				});
			}
		}

		return results;
	}

	/**
	 * 智能地理编码（尝试多种策略）
	 */
	async smartGeocode(
		address: string,
		options: GeocodingOptions = {},
	): Promise<UnifiedGeocodingResult | null> {
		// 策略1：直接查询
		let result = await this.geocode(address, options);
		if (result && result.confidence === "high") {
			return result;
		}

		// 策略2：如果包含中文，添加中国地区偏好
		if (/[\u4e00-\u9fa5]/.test(address)) {
			logger.info("检测到中文地址，添加中国地区偏好", { address });

			result = await this.geocode(address, {
				...options,
				region: "CN",
				language: "zh-CN",
			});

			if (result) {
				return result;
			}
		}

		// 策略3：尝试简化地址
		const simplifiedAddress = this.simplifyAddress(address);
		if (simplifiedAddress !== address) {
			logger.info("尝试简化地址重新查询", {
				original: address,
				simplified: simplifiedAddress,
			});

			result = await this.geocode(simplifiedAddress, options);
		}

		return result;
	}

	/**
	 * 获取服务统计信息
	 */
	getStats(): GeocodingStats {
		const loadBalancerStats = this.loadBalancer.getStats();

		// 计算总体统计
		let totalRequests = 0;
		let successfulRequests = 0;
		let failedRequests = 0;
		let totalResponseTime = 0;

		const providerStats: Record<GeocodingProvider, any> = {} as any;

		for (const providerStat of loadBalancerStats.providerStats) {
			const stats = providerStat.stats;
			totalRequests += stats.requests;
			successfulRequests += stats.successes;
			failedRequests += stats.failures;
			totalResponseTime += stats.totalResponseTime;

			providerStats[providerStat.provider] = {
				requests: stats.requests,
				successes: stats.successes,
				failures: stats.failures,
				averageResponseTime: stats.averageResponseTime,
				lastUsed: stats.lastUsed,
			};
		}

		return {
			totalRequests,
			successfulRequests,
			failedRequests,
			averageResponseTime:
				successfulRequests > 0
					? totalResponseTime / successfulRequests
					: 0,
			providerStats,
		};
	}

	/**
	 * 重置统计信息
	 */
	resetStats() {
		this.loadBalancer.resetStats();
	}

	/**
	 * 清除缓存
	 */
	clearCache() {
		this.cache.clear();
		logger.info("地理编码缓存已清除");
	}

	/**
	 * 设置负载均衡策略
	 */
	setStrategy(strategy: LoadBalanceStrategy) {
		this.loadBalancer.setStrategy(strategy);
		logger.info("负载均衡策略已更新", { strategy });
	}

	/**
	 * 添加提供商
	 */
	addProvider(config: GeocodingConfig) {
		const provider = this.createProvider(config);
		if (provider) {
			this.loadBalancer.addProvider(provider);
			logger.info("提供商已添加", { provider: config.provider });
		}
	}

	/**
	 * 移除提供商
	 */
	removeProvider(providerType: GeocodingProvider) {
		this.loadBalancer.removeProvider(providerType);
		logger.info("提供商已移除", { provider: providerType });
	}

	/**
	 * 创建提供商实例
	 */
	private createProviders(
		configs: GeocodingConfig[],
	): BaseGeocodingProvider[] {
		const providers: BaseGeocodingProvider[] = [];

		for (const config of configs) {
			const provider = this.createProvider(config);
			if (provider) {
				providers.push(provider);
			}
		}

		return providers;
	}

	/**
	 * 创建单个提供商实例
	 */
	private createProvider(
		config: GeocodingConfig,
	): BaseGeocodingProvider | null {
		try {
			switch (config.provider) {
				case "google":
					return new GoogleProvider(config);
				case "bing":
					return new BingProvider(config);
				case "mapbox":
					return new MapboxProvider(config);
				case "baidu":
					return new BaiduProvider(config);
				case "amap":
					return new AmapProvider(config);
				default:
					logger.warn("未知的提供商类型", {
						provider: config.provider,
					});
					return null;
			}
		} catch (error) {
			logger.error("创建提供商失败", {
				provider: config.provider,
				error: error instanceof Error ? error.message : String(error),
			});
			return null;
		}
	}

	/**
	 * 生成缓存键
	 */
	private getCacheKey(
		type: string,
		query: string,
		options: GeocodingOptions,
	): string {
		const optionsStr = JSON.stringify(options);
		return `${type}:${query}:${optionsStr}`;
	}

	/**
	 * 从缓存获取结果
	 */
	private getFromCache(key: string): UnifiedGeocodingResult | null {
		const cached = this.cache.get(key);
		if (cached && cached.expiry > Date.now()) {
			return cached.result;
		}

		// 清除过期缓存
		if (cached) {
			this.cache.delete(key);
		}

		return null;
	}

	/**
	 * 设置缓存
	 */
	private setCache(key: string, result: UnifiedGeocodingResult) {
		const ttl = (this.config.cacheTTL || 3600) * 1000; // 默认1小时
		const expiry = Date.now() + ttl;

		this.cache.set(key, { result, expiry });

		// 定期清理过期缓存
		if (this.cache.size % 100 === 0) {
			this.cleanExpiredCache();
		}
	}

	/**
	 * 清理过期缓存
	 */
	private cleanExpiredCache() {
		const now = Date.now();
		for (const [key, value] of Array.from(this.cache.entries())) {
			if (value.expiry <= now) {
				this.cache.delete(key);
			}
		}
	}

	/**
	 * 简化地址
	 */
	private simplifyAddress(address: string): string {
		return address
			.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, " ") // 移除特殊字符
			.replace(/\s+/g, " ") // 合并多个空格
			.trim();
	}

	/**
	 * 延迟函数
	 */
	private delay(ms: number): Promise<void> {
		return new Promise((resolve) => setTimeout(resolve, ms));
	}

	/**
	 * 计算两点间距离（公里）
	 */
	static calculateDistance(
		lon1: number,
		lat1: number,
		lon2: number,
		lat2: number,
	): number {
		const R = 6371; // 地球半径（公里）
		const dLat = UnifiedGeocodingService.toRadians(lat2 - lat1);
		const dLon = UnifiedGeocodingService.toRadians(lon2 - lon1);
		const a =
			Math.sin(dLat / 2) * Math.sin(dLat / 2) +
			Math.cos(UnifiedGeocodingService.toRadians(lat1)) *
				Math.cos(UnifiedGeocodingService.toRadians(lat2)) *
				Math.sin(dLon / 2) *
				Math.sin(dLon / 2);
		const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
		return R * c;
	}

	/**
	 * 角度转弧度
	 */
	private static toRadians(degrees: number): number {
		return degrees * (Math.PI / 180);
	}

	/**
	 * 验证坐标是否有效
	 */
	static isValidCoordinates(longitude: number, latitude: number): boolean {
		return (
			typeof longitude === "number" &&
			typeof latitude === "number" &&
			longitude >= -180 &&
			longitude <= 180 &&
			latitude >= -90 &&
			latitude <= 90 &&
			!Number.isNaN(longitude) &&
			!Number.isNaN(latitude)
		);
	}

	/**
	 * 获取成本分析报告
	 */
	getCostAnalysis(): {
		totalRequests: number;
		estimatedCost: number;
		dailyUsage: Record<string, number>;
		costByProvider: Record<
			string,
			{ requests: number; estimatedCost: number }
		>;
		recommendations: string[];
	} {
		const stats = this.getStats();
		const costByProvider: Record<
			string,
			{ requests: number; estimatedCost: number }
		> = {};
		let totalEstimatedCost = 0;
		const recommendations: string[] = [];

		// 各提供商的大概成本（每1000次请求的美元成本）
		const providerCosts = {
			google: 5.0, // Google Maps: $5/1000 requests
			bing: 1.0, // Bing Maps: $1/1000 requests
			mapbox: 0.5, // Mapbox: $0.5/1000 requests
			baidu: 0.1, // 百度地图: 约$0.1/1000 requests
			amap: 0.05, // 高德地图: 约$0.05/1000 requests
		};

		for (const [provider, providerStats] of Object.entries(
			stats.providerStats,
		)) {
			const cost =
				(providerStats.requests / 1000) *
				(providerCosts[provider as keyof typeof providerCosts] || 1.0);
			costByProvider[provider] = {
				requests: providerStats.requests,
				estimatedCost: cost,
			};
			totalEstimatedCost += cost;
		}

		// 生成优化建议
		if (stats.totalRequests > 0) {
			const successRate = stats.successfulRequests / stats.totalRequests;

			if (successRate < 0.8) {
				recommendations.push(
					"成功率较低，建议检查API密钥配置或网络连接",
				);
			}

			if (stats.averageResponseTime > 2000) {
				recommendations.push(
					"平均响应时间较长，建议优化超时设置或调整提供商优先级",
				);
			}

			// 成本优化建议
			const highCostProviders = Object.entries(costByProvider)
				.filter(
					([_, data]) =>
						data.requests > 10 &&
						providerCosts[_ as keyof typeof providerCosts] > 2.0,
				)
				.map(([provider]) => provider);

			if (highCostProviders.length > 0) {
				recommendations.push(
					`考虑减少高成本提供商的使用: ${highCostProviders.join(", ")}`,
				);
			}
		}

		return {
			totalRequests: stats.totalRequests,
			estimatedCost: totalEstimatedCost,
			dailyUsage: this.getDailyUsage(),
			costByProvider,
			recommendations,
		};
	}

	/**
	 * 获取每日使用统计
	 */
	private getDailyUsage(): Record<string, number> {
		// 简化实现，实际应该从持久化存储中获取
		const today = new Date().toISOString().split("T")[0];
		const stats = this.getStats();

		return {
			[today]: stats.totalRequests,
		};
	}
}

// 导出所有类型和类
export * from "./types";
export * from "./base-provider";
export * from "./load-balancer";
export * from "./providers/amap-provider";
export * from "./providers/baidu-provider";
export * from "./providers/bing-provider";
export * from "./providers/google-provider";
export * from "./providers/mapbox-provider";

/**
 * 创建地理编码服务的便捷工厂函数
 */
export function createGeocodingService(
	config: GeocodingServiceConfig,
): UnifiedGeocodingService {
	return new UnifiedGeocodingService(config);
}

/**
 * 为中国用户优化的配置
 */
export function createChinaOptimizedConfig(apiKeys: {
	amap?: string;
	baidu?: string;
	google?: string;
	bing?: string;
	mapbox?: string;
}): GeocodingServiceConfig {
	const providers: GeocodingConfig[] = [];

	// 优先配置国内提供商
	if (apiKeys.amap) {
		providers.push({
			provider: "amap",
			apiKey: apiKeys.amap,
			weight: 3,
			priority: 1,
			enabled: true,
		});
	}

	if (apiKeys.baidu) {
		providers.push({
			provider: "baidu",
			apiKey: apiKeys.baidu,
			weight: 2,
			priority: 2,
			enabled: true,
		});
	}

	// 配置国外提供商作为备选
	if (apiKeys.google) {
		providers.push({
			provider: "google",
			apiKey: apiKeys.google,
			weight: 2,
			priority: 3,
			enabled: true,
		});
	}

	if (apiKeys.bing) {
		providers.push({
			provider: "bing",
			apiKey: apiKeys.bing,
			weight: 1,
			priority: 4,
			enabled: true,
		});
	}

	if (apiKeys.mapbox) {
		providers.push({
			provider: "mapbox",
			apiKey: apiKeys.mapbox,
			weight: 1,
			priority: 5,
			enabled: true,
		});
	}

	return {
		providers,
		strategy: "smart",
		enableCache: true,
		cacheTTL: 3600, // 1小时
		defaultOptions: {
			language: "zh-CN",
			region: "CN",
		},
	};
}
