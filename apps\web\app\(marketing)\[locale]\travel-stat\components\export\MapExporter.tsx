"use client";

import { useAnalytics } from "@modules/analytics";
import { useCallback, useState } from "react";
import { HIGH_QUALITY_EXPORT } from "./presets";
import type { ExportContext, ExportOptions, ExportResult } from "./types";

/**
 * 地图导出 Hook
 */
export function useMapExporter() {
	const { trackEvent } = useAnalytics();
	const [isExporting, setIsExporting] = useState(false);
	const [currentMethod, setCurrentMethod] = useState<string>("");
	const [exportResult, setExportResult] = useState<ExportResult | null>(null);

	const exportMap = useCallback(
		async (
			mapRef: any,
			travelPoints: any[] = [],
			options: Partial<ExportOptions> = {},
		): Promise<ExportResult> => {
			const startTime = Date.now();
			setIsExporting(true);
			setCurrentMethod("正在准备导出...");
			setExportResult(null);

			// 追踪导出开始事件
			trackEvent("travel_stat_export_started", {
				points_count: travelPoints.length,
				format: options.format || HIGH_QUALITY_EXPORT.format,
				quality: options.quality || HIGH_QUALITY_EXPORT.quality,
				scale: options.scale || HIGH_QUALITY_EXPORT.scale,
			});

			try {
				// 使用默认高质量配置，允许覆盖部分选项
				const finalOptions: ExportOptions = {
					...HIGH_QUALITY_EXPORT,
					...options,
					filename:
						options.filename ||
						`地图导出_${new Date()
							.toLocaleString("zh-CN", {
								year: "numeric",
								month: "2-digit",
								day: "2-digit",
								hour: "2-digit",
								minute: "2-digit",
								second: "2-digit",
							})
							.replace(
								/[/:]/g,
								"-",
							)}.${options.format || HIGH_QUALITY_EXPORT.format}`,
				};

				const context: ExportContext = {
					mapRef,
					travelPoints,
					setCurrentMethod,
				};

				// 动态导入导出器
				const { exportMap: doExport } = await import(
					"./exporters/html2canvas-exporter"
				);
				const canvas = await doExport(finalOptions, context);

				const result: ExportResult = {
					canvas,
					duration: Date.now() - startTime,
					success: true,
				};

				// 追踪导出成功事件
				trackEvent("travel_stat_export_completed", {
					points_count: travelPoints.length,
					format: finalOptions.format,
					duration: result.duration,
					quality: finalOptions.quality,
					scale: finalOptions.scale,
				});

				setExportResult(result);
				return result;
			} catch (error) {
				const errorMessage =
					error instanceof Error ? error.message : String(error);
				console.error("地图导出失败:", error);

				// 追踪导出失败事件
				trackEvent("travel_stat_export_failed", {
					points_count: travelPoints.length,
					format: options.format || HIGH_QUALITY_EXPORT.format,
					duration: Date.now() - startTime,
					error_message: errorMessage,
				});

				const result: ExportResult = {
					canvas: document.createElement("canvas"), // 空画布
					duration: Date.now() - startTime,
					success: false,
					error: errorMessage,
				};

				setExportResult(result);
				throw error;
			} finally {
				setIsExporting(false);
				setCurrentMethod("");
			}
		},
		[trackEvent],
	);

	return {
		exportMap,
		isExporting,
		currentMethod,
		exportResult,
	};
}
