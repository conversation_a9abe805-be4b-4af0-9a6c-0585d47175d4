import type {
	FrontendTravelDiary,
	FrontendTravelPoint,
	FrontendTravelTimeline,
	GeoCoordinates,
	TravelPointImage,
} from "@repo/database/src/types/travel-diary";

/**
 * 旅行点位接口
 */
export interface TravelPoint {
	id: string;
	date: Date | string;
	location: string;
	description: string;
	images?: TravelPointImage[];
	iconType: string;
	coordinates: {
		lat: number;
		lng: number;
	};
}

/**
 * MapBox地图点位接口
 */
export interface MapPoint {
	id: string;
	date: string;
	location: string;
	description: string;
	images?: TravelPointImage[];
	iconType: string;
	coordinates: {
		lat: number;
		lng: number;
	};
}

// 直接导出数据库定义的类型
export type {
	FrontendTravelDiary,
	FrontendTravelPoint,
	FrontendTravelTimeline,
	GeoCoordinates,
};

// 为了保持向后兼容性，定义类型别名
export type TravelDiary = FrontendTravelDiary;

// 添加requestIdleCallback的类型声明（如果不存在的话）
interface RequestIdleCallbackOptions {
	timeout?: number;
}

interface RequestIdleCallbackDeadline {
	readonly didTimeout: boolean;
	timeRemaining: () => number;
}

declare global {
	interface Window {
		requestIdleCallback: (
			callback: (deadline: RequestIdleCallbackDeadline) => void,
			opts?: RequestIdleCallbackOptions,
		) => number;
		cancelIdleCallback: (handle: number) => void;
		// 已有的storyExportCompleted属性
		storyExportCompleted?: boolean;
	}
}
