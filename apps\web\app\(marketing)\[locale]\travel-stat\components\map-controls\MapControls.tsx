"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { Camera, ChevronUp, Loader2, Settings } from "lucide-react";
import { useMapExporter } from "../export";
import type { MapStyleType } from "../types";
import { MAP_STYLE_CONFIGS } from "../types";

import { useTranslatedAnimationThemes } from "../../hooks/useTranslatedAnimationThemes";
import { useTranslatedAtmosphereThemes } from "../../hooks/useTranslatedAtmosphereThemes";
import { useTranslatedColorThemes } from "../../hooks/useTranslatedColorThemes";
import { useTranslatedMapStyles } from "../../hooks/useTranslatedMapStyles";
import { useTranslatedMarkerStyles } from "../../hooks/useTranslatedMarkerStyles";
import { useTranslatedProjections } from "../../hooks/useTranslatedProjections";
import { useTravelStatTranslations } from "../../hooks/useTravelStatTranslations";
import { useClickOutside } from "./hooks/useClickOutside";
import { useMapControlsState } from "./hooks/useMapControlsState";
import { usePopoverPosition } from "./hooks/usePopoverPosition";
import {
	AnimationPopover,
	AtmospherePopover,
	ColorThemePopover,
	DisplayPopover,
	MapStylePopover,
	MarkerStylePopover,
	PopoverPortal,
	ProjectionPopover,
} from "./popovers";
import type { MapControlsProps } from "./types";
import { createControlItems } from "./utils/controlItemsFactory";

export function MapControls(props: MapControlsProps) {
	const {
		mapLoaded,
		atmosphereTheme,
		mapStyle,
		animationTheme,
		mapProjection,
		currentColorTheme,
		markerStyle,
		markerTheme,
		currentEmoji,
		currentEmojiColor,
		hideOutline,
		mapRef,
		travelPoints,
		visitedCountries,
		onAtmosphereChange,
		onMapStyleChange,
		onAnimationChange,
		onProjectionChange,
		onColorThemeChange,
		onMarkerStyleChange,
		onMarkerThemeChange,
		onEmojiChange,
		onEmojiColorChange,
		onHideOutlineChange,
		showTooltips,
		showMarkers,
		onShowTooltipsChange,
		onShowMarkersChange,
	} = props;

	const { getTheme } = useTranslatedColorThemes();
	const { getTranslatedMapStyle } = useTranslatedMapStyles();
	const { getTranslatedProjection } = useTranslatedProjections();
	const { getAnimationTheme } = useTranslatedAnimationThemes();
	const { getAtmosphereThemeName } = useTranslatedAtmosphereThemes();
	const { getMarkerStyleName } = useTranslatedMarkerStyles();

	const currentColorThemeName = getTheme(currentColorTheme).name;
	const currentMapStyleName = getTranslatedMapStyle(mapStyle)?.name ?? "";
	const currentProjectionName =
		getTranslatedProjection(mapProjection)?.name ?? "";
	const currentAnimationThemeName = getAnimationTheme(animationTheme).name;
	const currentAtmosphereThemeName = getAtmosphereThemeName(atmosphereTheme);
	const currentMarkerStyleName = getMarkerStyleName(markerStyle);

	const translations = useTravelStatTranslations();

	// 计算当前显示模式名称
	const displayModeName =
		showMarkers && showTooltips
			? translations.mapControls.display.presets.all.name()
			: showMarkers && !showTooltips
				? translations.mapControls.display.presets.markersOnly.name()
				: !showMarkers && !showTooltips
					? translations.mapControls.display.presets.clean.name()
					: translations.mapControls.display.custom();

	const {
		isExpanded,
		setIsExpanded,
		activePopover,
		setActivePopover,
		selectedStyleCategory,
		setSelectedStyleCategory,
		popoverPosition,
		setPopoverPosition,
		controlsRef,
		buttonRefs,
	} = useMapControlsState();

	const { calculatePopoverPosition } = usePopoverPosition();

	// 地图导出功能
	const { exportMap, isExporting } = useMapExporter();

	// 直接导出高质量图片
	const handleQuickExport = async () => {
		try {
			await exportMap(mapRef, travelPoints, {
				format: "png",
				quality: 1.0,
				scale: 2,
				filename: `travel_map_${Date.now()}.png`,
			});
		} catch (error) {
			console.error("导出失败:", error);
		}
	};

	// 点击外部区域收起控制栏
	useClickOutside({
		isExpanded,
		activePopover,
		controlsRef,
		onClose: () => {
			setIsExpanded(false);
			setActivePopover(null);
		},
	});

	// 阻止弹窗内部点击事件冒泡
	const handlePopoverClick = (e: React.MouseEvent) => {
		e.stopPropagation();
	};

	// 检查地图样式是否推荐用于当前大气层/动画
	const isStyleRecommended = (style: MapStyleType): boolean => {
		const config = MAP_STYLE_CONFIGS[style];
		const atmosphereMatch = config.bestAtmosphere.includes(atmosphereTheme);
		const animationMatch = config.bestAnimation.includes(animationTheme);
		return atmosphereMatch || animationMatch;
	};

	const controlItems = createControlItems({
		mapLoaded,
		atmosphereTheme,
		mapStyle,
		animationTheme,
		mapProjection,
		currentColorTheme,
		markerStyle,
		colorThemeName: currentColorThemeName,
		mapStyleName: currentMapStyleName,
		projectionName: currentProjectionName,
		animationThemeName: currentAnimationThemeName,
		atmosphereThemeName: currentAtmosphereThemeName,
		markerStyleName: currentMarkerStyleName,
		showTooltips,
		showMarkers,
		displayModeName,
		translations,
	});

	// 渲染弹窗内容
	const renderPopoverContent = (popoverId: string) => {
		switch (popoverId) {
			case "style":
				return (
					<MapStylePopover
						mapStyle={mapStyle}
						selectedStyleCategory={selectedStyleCategory}
						onMapStyleChange={onMapStyleChange}
						onCategoryChange={setSelectedStyleCategory}
						isStyleRecommended={isStyleRecommended}
					/>
				);
			case "markers":
				return (
					<MarkerStylePopover
						currentStyle={markerStyle}
						currentTheme={markerTheme}
						currentEmoji={currentEmoji}
						currentEmojiColor={currentEmojiColor}
						hideOutline={hideOutline}
						onStyleChange={onMarkerStyleChange}
						onThemeChange={onMarkerThemeChange}
						onEmojiChange={onEmojiChange}
						onEmojiColorChange={onEmojiColorChange}
						onHideOutlineChange={onHideOutlineChange}
					/>
				);
			case "display":
				return (
					<DisplayPopover
						showTooltips={showTooltips}
						showMarkers={showMarkers}
						onShowTooltipsChange={onShowTooltipsChange}
						onShowMarkersChange={onShowMarkersChange}
					/>
				);
			case "atmosphere":
				return (
					<AtmospherePopover
						atmosphereTheme={atmosphereTheme}
						onAtmosphereChange={onAtmosphereChange}
					/>
				);
			case "animation":
				return (
					<AnimationPopover
						animationTheme={animationTheme}
						onAnimationChange={onAnimationChange}
					/>
				);
			case "colors":
				return (
					<ColorThemePopover
						currentColorTheme={currentColorTheme}
						mapStyle={mapStyle}
						onColorThemeChange={onColorThemeChange}
					/>
				);
			case "projection":
				return (
					<ProjectionPopover
						mapProjection={mapProjection}
						onProjectionChange={onProjectionChange}
					/>
				);
			default:
				return null;
		}
	};

	if (!isExpanded) {
		// 收缩状态：显示主控制按钮和导出按钮
		return (
			<div
				ref={controlsRef}
				className="absolute top-2 left-2 z-40 space-y-2"
				data-export-hidden="true"
				data-map-control="settings-export"
				style={{
					// 在卡片预览缩放环境下确保控制按钮可点击
					zIndex: 100, // 提高z-index确保在缩放容器中仍能交互
					isolation: "isolate", // 创建独立的堆叠上下文
				}}
			>
				<Button
					onClick={() => setIsExpanded(true)}
					size="sm"
					variant="outline"
					className="p-0 bg-white/90 backdrop-blur-sm border-sky-200 hover:bg-sky-50 shadow-lg transition-all duration-200"
					title={translations.mapControls.title()}
					style={{
						width: "38px",
						height: "38px",
						borderRadius: "9px",
						display: "flex",
						alignItems: "center",
						justifyContent: "center",
						minWidth: "unset",
						minHeight: "unset",
					}}
				>
					<Settings
						className="w-4 h-4"
						style={{ marginRight: "0px" }}
					/>
				</Button>

				{/* 直接导出按钮 */}
				<Button
					onClick={handleQuickExport}
					size="sm"
					variant="outline"
					className="p-0 bg-white/90 backdrop-blur-sm border-sky-200 hover:bg-sky-50 shadow-lg transition-all duration-200"
					title={
						isExporting
							? translations.mapControls.exporting()
							: translations.mapControls.exportHighQuality()
					}
					disabled={!mapLoaded || isExporting}
					style={{
						width: "38px",
						height: "38px",
						borderRadius: "9px",
						display: "flex",
						alignItems: "center",
						justifyContent: "center",
						minWidth: "unset",
						minHeight: "unset",
					}}
				>
					{isExporting ? (
						<Loader2
							className="w-4 h-4 animate-spin"
							style={{ marginRight: "0px" }}
						/>
					) : (
						<Camera
							className="w-4 h-4"
							style={{ marginRight: "0px" }}
						/>
					)}
				</Button>
			</div>
		);
	}

	// 展开状态：显示所有控制选项
	return (
		<div
			ref={controlsRef}
			className="absolute top-3 left-3 z-40"
			data-export-hidden="true"
			data-map-control="settings-panel"
			style={{
				// 在卡片预览缩放环境下确保控制面板可交互
				zIndex: 100, // 提高z-index确保在缩放容器中仍能交互
				isolation: "isolate", // 创建独立的堆叠上下文
			}}
		>
			<Card className="bg-white/95 backdrop-blur-sm border-sky-200 shadow-lg min-w-[280px]">
				{/* 标题栏 */}
				<div className="flex items-center justify-between p-3 border-b border-sky-100">
					<div className="flex items-center gap-2">
						<Settings className="w-4 h-4 text-sky-600" />
						<span className="font-medium text-sm text-gray-800">
							{translations.mapControls.title()}
						</span>
					</div>
					<Button
						onClick={() => setIsExpanded(false)}
						size="sm"
						variant="ghost"
						className="p-0 hover:bg-sky-50"
						style={{
							width: "28px",
							height: "28px",
							borderRadius: "6px",
							display: "flex",
							alignItems: "center",
							justifyContent: "center",
							minWidth: "unset",
							minHeight: "unset",
						}}
					>
						<ChevronUp className="w-4 h-4" />
					</Button>
				</div>

				{/* 控制选项 */}
				<div className="p-3 space-y-2">
					{/* 其他控制选项 */}
					{controlItems.map((item) => (
						<div key={item.id} className="relative">
							{/* 标准单行控制项 */}
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-2">
									{item.icon}
									<span className="text-sm font-medium text-gray-700">
										{item.title}
									</span>
								</div>
								<Button
									ref={(el) => {
										if (item.hasPopover) {
											buttonRefs.current[item.id] = el;
										}
									}}
									onClick={() => {
										if (item.hasPopover) {
											const isOpening =
												activePopover !== item.id;
											setActivePopover(
												isOpening ? item.id : null,
											);

											if (
												isOpening &&
												buttonRefs.current[item.id]
											) {
												const position =
													calculatePopoverPosition(
														buttonRefs.current[
															item.id
														]!,
														item.id,
													);
												setPopoverPosition(position);
											}
										} else if (item.onClick) {
											item.onClick();
										}
									}}
									disabled={item.disabled}
									size="sm"
									variant="outline"
									className="h-7 px-3 text-xs bg-sky-50 border-sky-200 hover:bg-sky-100"
								>
									{item.buttonText}
								</Button>
							</div>
						</div>
					))}
				</div>
			</Card>

			{/* Portal 弹窗 */}
			<PopoverPortal
				isOpen={!!activePopover}
				position={popoverPosition}
				title={
					controlItems.find((item) => item.id === activePopover)
						?.title || ""
				}
				onClose={() => setActivePopover(null)}
				onContentClick={handlePopoverClick}
			>
				{activePopover && renderPopoverContent(activePopover)}
			</PopoverPortal>
		</div>
	);
}
