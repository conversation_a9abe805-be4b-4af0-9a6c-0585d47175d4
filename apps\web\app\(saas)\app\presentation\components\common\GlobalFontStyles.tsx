"use client";

export function GlobalFontStyles() {
	return (
		<style jsx global>{`
      /* 应用全局字体设置 */
      .travel-memo-content {
        font-family: "LXGW WenKai", "Quicksand", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      }
      
      /* 页面标题和小标题 */
      .travel-memo-title {
        font-family: "LXGW WenKai", "Quicksand", serif;
        font-weight: 700;
      }
      
      /* 副标题 */
      .travel-memo-subtitle {
        font-family: "LXGW WenKai", "Quicksand", sans-serif;
        font-weight: 500;
      }
      
      /* 正文文本 */
      .travel-memo-text {
        font-family: "LXGW WenKai", "Quicksand", sans-serif;
        font-weight: 400;
      }
      
      /* 数字和统计数据 */
      .travel-memo-number {
        font-family: "Quicksand", "LXGW WenKai", sans-serif;
        font-weight: 700;
      }
      
      /* 标签和描述文字 */
      .travel-memo-label {
        font-family: "LXG<PERSON> WenKai", "Quicksand", sans-serif;
        font-weight: 400;
      }
      
      /* 强调文本 */
      .travel-memo-emphasis {
        font-family: "LXGW WenKai", "Quicksand", serif;
        font-weight: 500;
        font-style: italic;
      }
      
      /* 按钮文本 */
      .travel-memo-button {
        font-family: "Quicksand", "LXGW WenKai", sans-serif;
        font-weight: 500;
      }
    `}</style>
	);
}
