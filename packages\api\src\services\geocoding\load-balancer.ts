/**
 * 地理编码负载均衡器
 * 实现多提供商的负载均衡和故障转移
 */

import { logger } from "@repo/logs";
import type { BaseGeocodingProvider } from "./base-provider";
import type {
	GeocodingOptions,
	GeocodingProvider,
	UnifiedGeocodingResult,
} from "./types";

/**
 * 负载均衡策略
 */
export type LoadBalanceStrategy =
	| "random" // 随机选择
	| "round_robin" // 轮询
	| "weighted" // 加权轮询
	| "priority" // 优先级
	| "performance" // 性能优先
	| "smart"; // 智能选择

/**
 * 地理编码负载均衡器
 */
export class GeocodingLoadBalancer {
	private providers: BaseGeocodingProvider[] = [];
	private currentIndex = 0;
	private strategy: LoadBalanceStrategy = "smart";

	constructor(
		providers: BaseGeocodingProvider[],
		strategy: LoadBalanceStrategy = "smart",
	) {
		this.providers = providers;
		this.strategy = strategy;
	}

	/**
	 * 添加提供商
	 */
	addProvider(provider: BaseGeocodingProvider) {
		this.providers.push(provider);
	}

	/**
	 * 移除提供商
	 */
	removeProvider(providerType: GeocodingProvider) {
		this.providers = this.providers.filter(
			(p) => p.getProviderType() !== providerType,
		);
	}

	/**
	 * 获取可用的提供商列表
	 */
	getAvailableProviders(): BaseGeocodingProvider[] {
		return this.providers.filter((p) => p.isAvailable());
	}

	/**
	 * 设置负载均衡策略
	 */
	setStrategy(strategy: LoadBalanceStrategy) {
		this.strategy = strategy;
	}

	/**
	 * 地理编码（带负载均衡和故障转移）
	 */
	async geocode(
		address: string,
		options: GeocodingOptions = {},
	): Promise<UnifiedGeocodingResult | null> {
		const availableProviders = this.getAvailableProviders();

		if (availableProviders.length === 0) {
			logger.warn("没有可用的地理编码提供商", { address });
			return null;
		}

		// 根据策略选择提供商顺序
		const orderedProviders = this.selectProviders(
			availableProviders,
			address,
		);

		// 尝试每个提供商
		for (const provider of orderedProviders) {
			try {
				logger.info("尝试地理编码", {
					provider: provider.getProviderType(),
					address,
					strategy: this.strategy,
				});

				const result = await provider.geocode(address, options);

				if (result) {
					logger.info("地理编码成功", {
						provider: provider.getProviderType(),
						address,
						responseTime: result.responseTime,
					});

					// 更新配额使用
					provider.updateQuotaUsage();

					return result;
				}

				logger.warn("地理编码无结果，尝试下一个提供商", {
					provider: provider.getProviderType(),
					address,
				});
			} catch (error) {
				logger.error("地理编码失败，尝试下一个提供商", {
					provider: provider.getProviderType(),
					address,
					error:
						error instanceof Error ? error.message : String(error),
				});
			}

			// 添加延迟避免过快切换
			await this.delay(100);
		}

		logger.error("所有提供商都失败", {
			address,
			providersCount: orderedProviders.length,
		});
		return null;
	}

	/**
	 * 反向地理编码（带负载均衡和故障转移）
	 */
	async reverseGeocode(
		longitude: number,
		latitude: number,
		options: GeocodingOptions = {},
	): Promise<UnifiedGeocodingResult | null> {
		const availableProviders = this.getAvailableProviders();

		if (availableProviders.length === 0) {
			logger.warn("没有可用的地理编码提供商", { longitude, latitude });
			return null;
		}

		// 根据策略选择提供商顺序
		const orderedProviders = this.selectProviders(availableProviders);

		// 尝试每个提供商
		for (const provider of orderedProviders) {
			try {
				logger.info("尝试反向地理编码", {
					provider: provider.getProviderType(),
					longitude,
					latitude,
					strategy: this.strategy,
				});

				const result = await provider.reverseGeocode(
					longitude,
					latitude,
					options,
				);

				if (result) {
					logger.info("反向地理编码成功", {
						provider: provider.getProviderType(),
						longitude,
						latitude,
						responseTime: result.responseTime,
					});

					// 更新配额使用
					provider.updateQuotaUsage();

					return result;
				}

				logger.warn("反向地理编码无结果，尝试下一个提供商", {
					provider: provider.getProviderType(),
					longitude,
					latitude,
				});
			} catch (error) {
				logger.error("反向地理编码失败，尝试下一个提供商", {
					provider: provider.getProviderType(),
					longitude,
					latitude,
					error:
						error instanceof Error ? error.message : String(error),
				});
			}

			// 添加延迟避免过快切换
			await this.delay(100);
		}

		logger.error("所有提供商都失败", {
			longitude,
			latitude,
			providersCount: orderedProviders.length,
		});
		return null;
	}

	/**
	 * 根据策略选择提供商顺序
	 */
	private selectProviders(
		availableProviders: BaseGeocodingProvider[],
		address?: string,
	): BaseGeocodingProvider[] {
		switch (this.strategy) {
			case "random":
				return this.randomSelection(availableProviders);

			case "round_robin":
				return this.roundRobinSelection(availableProviders);

			case "weighted":
				return this.weightedSelection(availableProviders);

			case "priority":
				return this.prioritySelection(availableProviders);

			case "performance":
				return this.performanceSelection(availableProviders);

			case "smart":
				return this.smartSelection(availableProviders, address);

			default:
				return availableProviders;
		}
	}

	/**
	 * 随机选择
	 */
	private randomSelection(
		providers: BaseGeocodingProvider[],
	): BaseGeocodingProvider[] {
		const shuffled = [...providers];
		for (let i = shuffled.length - 1; i > 0; i--) {
			const j = Math.floor(Math.random() * (i + 1));
			[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
		}
		return shuffled;
	}

	/**
	 * 轮询选择
	 */
	private roundRobinSelection(
		providers: BaseGeocodingProvider[],
	): BaseGeocodingProvider[] {
		const result = [];
		const startIndex = this.currentIndex % providers.length;

		for (let i = 0; i < providers.length; i++) {
			const index = (startIndex + i) % providers.length;
			result.push(providers[index]);
		}

		this.currentIndex++;
		return result;
	}

	/**
	 * 加权选择
	 */
	private weightedSelection(
		providers: BaseGeocodingProvider[],
	): BaseGeocodingProvider[] {
		return providers.sort((a, b) => b.getWeight() - a.getWeight());
	}

	/**
	 * 优先级选择
	 */
	private prioritySelection(
		providers: BaseGeocodingProvider[],
	): BaseGeocodingProvider[] {
		return providers.sort((a, b) => a.getPriority() - b.getPriority());
	}

	/**
	 * 性能选择
	 */
	private performanceSelection(
		providers: BaseGeocodingProvider[],
	): BaseGeocodingProvider[] {
		return providers.sort((a, b) => {
			const aStats = a.getStats();
			const bStats = b.getStats();

			// 综合考虑成功率和响应时间
			const aScore =
				aStats.successRate * 1000 - aStats.averageResponseTime;
			const bScore =
				bStats.successRate * 1000 - bStats.averageResponseTime;

			return bScore - aScore;
		});
	}

	/**
	 * 智能选择（综合多种因素）
	 */
	private smartSelection(
		providers: BaseGeocodingProvider[],
		address?: string,
	): BaseGeocodingProvider[] {
		const scored = providers.map((provider) => {
			const stats = provider.getStats();
			const weight = provider.getWeight();
			const priority = provider.getPriority();

			// 计算综合得分
			let score = 0;

			// 成功率权重 (40%)
			score += stats.successRate * 40;

			// 响应时间权重 (20%) - 响应时间越短得分越高
			const responseTimeScore = Math.max(
				0,
				100 - stats.averageResponseTime / 10,
			);
			score += responseTimeScore * 0.2;

			// 权重 (20%)
			score += weight * 20;

			// 优先级 (20%) - 优先级越高（数字越小）得分越高
			const priorityScore = Math.max(0, 100 - priority);
			score += priorityScore * 0.2;

			// 地址特征匹配（如果是中文地址，优先国内提供商）
			if (address && /[\u4e00-\u9fa5]/.test(address)) {
				const providerType = provider.getProviderType();
				if (providerType === "amap" || providerType === "baidu") {
					score += 20; // 中文地址加分
				}
			}

			return { provider, score };
		});

		// 按得分排序
		scored.sort((a, b) => b.score - a.score);

		return scored.map((item) => item.provider);
	}

	/**
	 * 获取负载均衡统计信息
	 */
	getStats() {
		const providerStats = this.providers.map((provider) => ({
			provider: provider.getProviderType(),
			available: provider.isAvailable(),
			weight: provider.getWeight(),
			priority: provider.getPriority(),
			stats: provider.getStats(),
		}));

		return {
			strategy: this.strategy,
			totalProviders: this.providers.length,
			availableProviders: this.getAvailableProviders().length,
			providerStats,
		};
	}

	/**
	 * 重置所有提供商的统计信息
	 */
	resetStats() {
		for (const provider of this.providers) {
			provider.resetStats();
		}
		this.currentIndex = 0;
	}

	/**
	 * 延迟函数
	 */
	private delay(ms: number): Promise<void> {
		return new Promise((resolve) => setTimeout(resolve, ms));
	}
}
