"use client";

import { Star } from "lucide-react";
import { useTravelStatTranslations } from "../../../hooks/useTravelStatTranslations";
import type { AnimationTheme } from "../../types";
import { ANIMATION_THEME_ICONS } from "../constants";
import { PopoverContainer } from "./PopoverContainer";

interface AnimationPopoverProps {
	animationTheme: AnimationTheme;
	onAnimationChange: (theme: AnimationTheme) => void;
}

const animationThemes: AnimationTheme[] = [
	"shooting-stars",
	"floating-particles",
	"aurora",
	"minimal",
	"galaxy",
	"none",
];

export function AnimationPopover({
	animationTheme,
	onAnimationChange,
}: AnimationPopoverProps) {
	const t = useTravelStatTranslations();

	return (
		<PopoverContainer>
			{/* 动画主题选择 */}
			<div className="grid grid-cols-2 gap-2 mb-4">
				{animationThemes.map((themeId) => {
					const Icon = ANIMATION_THEME_ICONS[themeId];
					const isActive = themeId === animationTheme;
					const themeName = t.animation.names[themeId]();
					const themeDescription =
						t.animation.descriptions[themeId]();

					return (
						<button
							key={themeId}
							type="button"
							onClick={(e) => {
								e.stopPropagation();
								onAnimationChange(themeId);
							}}
							className={`relative p-3 rounded-lg border-2 transition-all duration-200 text-left ${
								isActive
									? "border-sky-400 bg-sky-50 shadow-md"
									: "border-gray-200 hover:border-sky-300 hover:bg-sky-25"
							}`}
						>
							{/* 激活指示器 */}
							{isActive && (
								<div className="absolute top-1 right-1 w-2 h-2 bg-sky-500 rounded-full" />
							)}

							<div className="flex items-center gap-2 mb-1">
								<Icon
									className={`w-4 h-4 ${
										isActive
											? "text-sky-600"
											: "text-gray-500"
									}`}
								/>
								<span
									className={`text-sm font-medium ${
										isActive
											? "text-sky-800"
											: "text-gray-700"
									}`}
								>
									{themeName}
								</span>
							</div>

							<p
								className={`text-xs ${
									isActive ? "text-sky-600" : "text-gray-500"
								}`}
							>
								{themeDescription}
							</p>
						</button>
					);
				})}
			</div>

			{/* 当前动画主题预览 */}
			<div className="p-3 bg-gradient-to-r from-sky-50 to-blue-50 rounded-lg border border-sky-200">
				<div className="flex items-center gap-2 mb-1">
					<Star className="w-4 h-4 text-sky-500" />
					<span className="text-sm font-medium text-sky-800">
						{t.animation.popover.currentTheme()}
					</span>
				</div>
				<p className="text-xs text-sky-600">
					{t.animation.names[animationTheme]()} -{" "}
					{t.animation.descriptions[animationTheme]()}
				</p>
			</div>
		</PopoverContainer>
	);
}
