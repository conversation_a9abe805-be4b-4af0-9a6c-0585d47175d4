/**
 * 图片缓存管理器
 * 负责智能管理图片资源的加载、缓存和释放
 */

// 缓存配置
const CACHE_CONFIG = {
	// 最大缓存图片数量
	MAX_CACHE_SIZE: 100,
	// 图片过期时间（毫秒）
	CACHE_EXPIRY: 5 * 60 * 1000, // 5分钟
	// 🔧 优化：大幅降低marker图片尺寸
	// 低质量图片尺寸（用于快速预览和远距离显示）
	LOW_QUALITY_SIZE: 250, // 从150降到80
	// 中等质量图片尺寸（用于正常显示）
	MEDIUM_QUALITY_SIZE: 800, // 新增中等质量
	// 高质量图片尺寸（用于详细查看，但仍然控制在合理范围）
	HIGH_QUALITY_SIZE: 1500, // 从800降到250，足够清晰且不会卡顿
	// 超高质量图片尺寸（仅用于全屏查看）
	ULTRA_QUALITY_SIZE: 3000, // 新增超高质量，仅特殊情况使用
	// 压缩质量
	JPEG_QUALITY: 0.75, // 从0.85降到0.75，进一步减小文件大小
	// WebP质量（优先使用WebP格式）
	WEBP_QUALITY: 0.7, // WebP在相同质量下文件更小
};

interface CacheEntry {
	url: string;
	blob: Blob;
	timestamp: number;
	accessCount: number;
	lastAccess: number;
	size: number;
	priority: "high" | "medium" | "low";
}

interface ImageProcessOptions {
	maxWidth?: number;
	maxHeight?: number;
	quality?: number;
	format?: "webp" | "jpeg" | "png";
}

class ImageCacheManager {
	private cache = new Map<string, CacheEntry>();
	private pendingRequests = new Map<string, Promise<string | null>>();
	private totalCacheSize = 0;

	/**
	 * 获取缓存的图片URL
	 */
	getCachedImage(originalUrl: string): string | null {
		const entry = this.cache.get(originalUrl);
		if (!entry) return null;

		// 检查是否过期
		if (Date.now() - entry.timestamp > CACHE_CONFIG.CACHE_EXPIRY) {
			this.removeCacheEntry(originalUrl);
			return null;
		}

		// 更新访问信息
		entry.accessCount++;
		entry.lastAccess = Date.now();

		return URL.createObjectURL(entry.blob);
	}

	/**
	 * 处理图片并缓存
	 */
	async processAndCacheImage(
		originalUrl: string,
		options: ImageProcessOptions = {},
		priority: "high" | "medium" | "low" = "medium",
	): Promise<string | null> {
		// 如果已有缓存，直接返回
		const cached = this.getCachedImage(originalUrl);
		if (cached) return cached;

		// 如果正在处理中，返回现有的Promise
		if (this.pendingRequests.has(originalUrl)) {
			return this.pendingRequests.get(originalUrl)!;
		}

		// 创建处理Promise
		const processPromise = this.processImageInternal(
			originalUrl,
			options,
			priority,
		);
		this.pendingRequests.set(originalUrl, processPromise);

		try {
			const result = await processPromise;
			return result;
		} finally {
			this.pendingRequests.delete(originalUrl);
		}
	}

	/**
	 * 内部图片处理逻辑
	 */
	private async processImageInternal(
		originalUrl: string,
		options: ImageProcessOptions,
		priority: "high" | "medium" | "low",
	): Promise<string | null> {
		try {
			console.log("🔄 ImageCache: 处理图片:", {
				originalUrl: originalUrl.slice(0, 50),
				priority,
			});

			// 根据URL类型选择不同的处理方式
			let imageData: Blob;

			if (originalUrl.startsWith("data:")) {
				// Base64 图片
				imageData = await this.dataUrlToBlob(originalUrl);
			} else if (originalUrl.startsWith("blob:")) {
				// Blob URL
				imageData = await fetch(originalUrl).then((r) => r.blob());
			} else if (originalUrl.startsWith("indexeddb:")) {
				// IndexedDB 存储的图片 - 委托给ImageStorage处理
				console.log(
					"🔄 ImageCache: 检测到indexeddb URL，委托给ImageStorage处理",
				);
				const { imageStorage } = await import("./imageStorage");
				const processedUrl = await imageStorage.processImageUrl(
					originalUrl,
					{
						useCache: false, // 避免循环调用
					},
				);
				if (!processedUrl) {
					throw new Error(
						`ImageStorage无法处理IndexedDB URL: ${originalUrl}`,
					);
				}
				// 重新处理返回的URL
				if (processedUrl.startsWith("blob:")) {
					imageData = await fetch(processedUrl).then((r) => r.blob());
				} else if (processedUrl.startsWith("data:")) {
					imageData = await this.dataUrlToBlob(processedUrl);
				} else {
					throw new Error(
						`ImageStorage返回了无法处理的URL类型: ${processedUrl}`,
					);
				}
			} else {
				// HTTP URL - 增强错误处理和日志
				console.log("🔄 ImageCache: 开始处理HTTP URL:", {
					url: originalUrl.slice(0, 80),
					urlProtocol: new URL(originalUrl).protocol,
				});

				try {
					const response = await fetch(originalUrl);

					console.log("📥 ImageCache: HTTP响应:", {
						status: response.status,
						statusText: response.statusText,
						contentType: response.headers.get("content-type"),
						contentLength: response.headers.get("content-length"),
						url: originalUrl.slice(0, 80),
					});

					if (!response.ok) {
						throw new Error(
							`HTTP ${response.status}: ${response.statusText}`,
						);
					}

					imageData = await response.blob();

					console.log("✅ ImageCache: HTTP图片获取成功:", {
						size: this.formatBytes(imageData.size),
						type: imageData.type,
						url: originalUrl.slice(0, 80),
					});
				} catch (fetchError) {
					console.error("❌ ImageCache: HTTP图片获取失败:", {
						error:
							fetchError instanceof Error
								? fetchError.message
								: String(fetchError),
						url: originalUrl.slice(0, 80),
						errorType:
							fetchError instanceof TypeError
								? "CORS/Network"
								: "HTTP",
					});
					throw fetchError;
				}
			}

			// 处理和压缩图片
			const processedBlob = await this.processImageBlob(
				imageData,
				options,
			);

			// 添加到缓存
			const cacheKey = this.generateCacheKey(originalUrl, options);
			this.addToCache(cacheKey, processedBlob, priority);

			// 返回处理后的URL
			const resultUrl = URL.createObjectURL(processedBlob);
			console.log("✅ ImageCache: 图片处理成功:", {
				originalSize: imageData.size,
				processedSize: processedBlob.size,
				originalUrl: originalUrl.slice(0, 50),
			});

			return resultUrl;
		} catch (error) {
			console.error("❌ ImageCache: 图片处理失败:", error, {
				originalUrl,
			});
			return null;
		}
	}

	/**
	 * 处理图片Blob（调整尺寸、压缩等）
	 * 🔧 优化：添加WebP支持、更好的压缩策略、防止过大图片
	 */
	private async processImageBlob(
		blob: Blob,
		options: ImageProcessOptions,
	): Promise<Blob> {
		return new Promise((resolve, reject) => {
			const img = new Image();
			const canvas = document.createElement("canvas");
			const ctx = canvas.getContext("2d");

			if (!ctx) {
				reject(new Error("无法获取Canvas上下文"));
				return;
			}

			// 🔧 性能优化：检查原始图片大小，如果超过5MB则强制使用低质量
			const isLargeImage = blob.size > 5 * 1024 * 1024; // 5MB
			const adjustedOptions = { ...options };

			if (isLargeImage) {
				console.warn("⚠️ ImageCache: 检测到大图片，强制使用低质量:", {
					originalSize: this.formatBytes(blob.size),
					adjustedMaxWidth: CACHE_CONFIG.LOW_QUALITY_SIZE,
				});
				adjustedOptions.maxWidth = Math.min(
					adjustedOptions.maxWidth || CACHE_CONFIG.HIGH_QUALITY_SIZE,
					CACHE_CONFIG.LOW_QUALITY_SIZE,
				);
				adjustedOptions.maxHeight = Math.min(
					adjustedOptions.maxHeight || CACHE_CONFIG.HIGH_QUALITY_SIZE,
					CACHE_CONFIG.LOW_QUALITY_SIZE,
				);
			}

			img.onload = () => {
				try {
					// 🔧 性能优化：对于超大原始图片，进一步限制尺寸
					const originalPixels = img.width * img.height;
					const isVeryLargeImage = originalPixels > 4000000; // 4MP

					// 计算新尺寸
					const { width, height } = this.calculateNewSize(
						img.width,
						img.height,
						adjustedOptions.maxWidth ||
							CACHE_CONFIG.HIGH_QUALITY_SIZE,
						adjustedOptions.maxHeight ||
							CACHE_CONFIG.HIGH_QUALITY_SIZE,
					);

					canvas.width = width;
					canvas.height = height;

					// 🔧 渲染优化：使用更好的图像平滑算法
					ctx.imageSmoothingEnabled = true;
					ctx.imageSmoothingQuality = "high";

					// 绘制图片
					ctx.drawImage(img, 0, 0, width, height);

					// 🔧 格式优化：优先尝试WebP格式
					const preferredFormat = this.getOptimalFormat(
						adjustedOptions.format,
						blob.type,
					);
					const quality = this.getOptimalQuality(
						preferredFormat,
						isLargeImage,
						isVeryLargeImage,
					);

					console.log("🎨 ImageCache: 压缩参数:", {
						originalSize: `${img.width}x${img.height}`,
						newSize: `${width}x${height}`,
						format: preferredFormat,
						quality: quality,
						isLargeImage,
						isVeryLargeImage,
					});

					// 转换为Blob
					canvas.toBlob(
						(processedBlob) => {
							if (processedBlob) {
								console.log("✅ ImageCache: 压缩完成:", {
									originalSize: this.formatBytes(blob.size),
									compressedSize: this.formatBytes(
										processedBlob.size,
									),
									compressionRatio: `${Math.round((1 - processedBlob.size / blob.size) * 100)}%`,
								});
								resolve(processedBlob);
							} else {
								reject(new Error("图片转换失败"));
							}
						},
						preferredFormat,
						quality,
					);
				} catch (error) {
					reject(error);
				}
			};

			img.onerror = () => reject(new Error("图片加载失败"));
			img.src = URL.createObjectURL(blob);
		});
	}

	/**
	 * 🔧 新增：获取最优图片格式
	 */
	private getOptimalFormat(
		requestedFormat?: string,
		originalType?: string,
	): string {
		// 如果明确指定了格式，使用指定格式
		if (requestedFormat === "png") return "image/png";
		if (requestedFormat === "jpeg") return "image/jpeg";

		// 优先使用WebP（如果浏览器支持）
		if (this.supportsWebP()) {
			return "image/webp";
		}

		// 如果原始是PNG且需要透明度，保持PNG
		if (originalType === "image/png") {
			return "image/png";
		}

		// 默认使用JPEG
		return "image/jpeg";
	}

	/**
	 * 🔧 新增：获取最优压缩质量
	 */
	private getOptimalQuality(
		format: string,
		isLargeImage: boolean,
		isVeryLargeImage: boolean,
	): number {
		if (format === "image/webp") {
			if (isVeryLargeImage) return 0.6; // 超大图片更激进压缩
			if (isLargeImage) return CACHE_CONFIG.WEBP_QUALITY;
			return 0.8; // 普通图片稍高质量
		}

		if (format === "image/jpeg") {
			if (isVeryLargeImage) return 0.6; // 超大图片更激进压缩
			if (isLargeImage) return CACHE_CONFIG.JPEG_QUALITY;
			return 0.8; // 普通图片稍高质量
		}

		return 1.0; // PNG不压缩
	}

	/**
	 * 🔧 新增：检查WebP支持
	 */
	private supportsWebP(): boolean {
		const canvas = document.createElement("canvas");
		canvas.width = 1;
		canvas.height = 1;
		return canvas.toDataURL("image/webp").indexOf("data:image/webp") === 0;
	}

	/**
	 * 计算新图片尺寸（保持比例）
	 */
	private calculateNewSize(
		originalWidth: number,
		originalHeight: number,
		maxWidth: number,
		maxHeight: number,
	): { width: number; height: number } {
		if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
			return { width: originalWidth, height: originalHeight };
		}

		const widthRatio = maxWidth / originalWidth;
		const heightRatio = maxHeight / originalHeight;
		const ratio = Math.min(widthRatio, heightRatio);

		return {
			width: Math.round(originalWidth * ratio),
			height: Math.round(originalHeight * ratio),
		};
	}

	/**
	 * 生成缓存键
	 */
	private generateCacheKey(
		url: string,
		options: ImageProcessOptions,
	): string {
		const optionsStr = JSON.stringify(options);
		return `${url}:${optionsStr}`;
	}

	/**
	 * 添加到缓存
	 */
	private addToCache(
		key: string,
		blob: Blob,
		priority: "high" | "medium" | "low",
	) {
		// 检查缓存大小限制
		if (this.cache.size >= CACHE_CONFIG.MAX_CACHE_SIZE) {
			this.evictLeastRecentlyUsed();
		}

		const entry: CacheEntry = {
			url: key,
			blob,
			timestamp: Date.now(),
			accessCount: 1,
			lastAccess: Date.now(),
			size: blob.size,
			priority,
		};

		this.cache.set(key, entry);
		this.totalCacheSize += blob.size;

		console.log("💾 ImageCache: 图片已缓存:", {
			key: key.slice(0, 50),
			size: blob.size,
			totalEntries: this.cache.size,
			totalSize: this.formatBytes(this.totalCacheSize),
		});
	}

	/**
	 * 移除缓存条目
	 */
	private removeCacheEntry(key: string) {
		const entry = this.cache.get(key);
		if (entry) {
			this.cache.delete(key);
			this.totalCacheSize -= entry.size;
			// 释放Blob URL
			URL.revokeObjectURL(URL.createObjectURL(entry.blob));
		}
	}

	/**
	 * 驱逐最近最少使用的条目
	 */
	private evictLeastRecentlyUsed() {
		let oldestEntry: CacheEntry | null = null;
		let oldestKey: string | null = null;

		// 🔧 修复TypeScript错误：使用forEach替代for...of
		this.cache.forEach((entry, key) => {
			// 优先驱逐低优先级的条目
			if (entry.priority === "low") {
				if (!oldestEntry || entry.lastAccess < oldestEntry.lastAccess) {
					oldestEntry = entry;
					oldestKey = key;
				}
			}
		});

		// 如果没有低优先级的，驱逐最老的条目
		if (!oldestKey) {
			this.cache.forEach((entry, key) => {
				if (!oldestEntry || entry.lastAccess < oldestEntry.lastAccess) {
					oldestEntry = entry;
					oldestKey = key;
				}
			});
		}

		if (oldestKey) {
			console.log("🗑️ ImageCache: 驱逐缓存条目:", {
				key: oldestKey.slice(0, 50),
				priority: oldestEntry?.priority,
				lastAccess: oldestEntry?.lastAccess,
			});
			this.removeCacheEntry(oldestKey);
		}
	}

	/**
	 * Data URL 转 Blob
	 */
	private async dataUrlToBlob(dataUrl: string): Promise<Blob> {
		const response = await fetch(dataUrl);
		return response.blob();
	}

	/**
	 * 初始化 IndexedDB
	 */
	private async initIndexedDB(): Promise<IDBDatabase> {
		return new Promise((resolve, reject) => {
			const request = indexedDB.open("ImageCache", 1);

			request.onerror = () => {
				console.error("❌ IndexedDB: 打开数据库失败", request.error);
				reject(request.error);
			};

			request.onupgradeneeded = () => {
				const db = request.result;
				console.log("🔧 IndexedDB: 正在创建对象存储...");

				// 如果对象存储不存在，创建它
				if (!db.objectStoreNames.contains("images")) {
					const store = db.createObjectStore("images", {
						keyPath: "key",
					});
					store.createIndex("timestamp", "timestamp", {
						unique: false,
					});
					console.log("✅ IndexedDB: 对象存储 'images' 已创建");
				}
			};

			request.onsuccess = () => {
				const db = request.result;
				console.log("✅ IndexedDB: 数据库已打开");
				resolve(db);
			};
		});
	}

	/**
	 * 从 IndexedDB 获取图片
	 */
	private async getFromIndexedDB(key: string): Promise<Blob> {
		try {
			const db = await this.initIndexedDB();

			return new Promise((resolve, reject) => {
				const transaction = db.transaction(["images"], "readonly");
				const store = transaction.objectStore("images");
				const getRequest = store.get(key);

				getRequest.onsuccess = () => {
					if (getRequest.result?.data) {
						console.log("📥 IndexedDB: 图片获取成功", {
							key: key.slice(0, 50),
						});
						resolve(new Blob([getRequest.result.data]));
					} else {
						console.warn("⚠️ IndexedDB: 图片未找到", {
							key: key.slice(0, 50),
						});
						reject(new Error(`图片未找到: ${key}`));
					}
				};

				getRequest.onerror = () => {
					console.error(
						"❌ IndexedDB: 获取图片失败",
						getRequest.error,
					);
					reject(getRequest.error);
				};

				transaction.onerror = () => {
					console.error("❌ IndexedDB: 事务失败", transaction.error);
					reject(transaction.error);
				};
			});
		} catch (error) {
			console.error("❌ IndexedDB: 初始化失败", error);
			throw error;
		}
	}

	/**
	 * 保存图片到 IndexedDB
	 */
	private async saveToIndexedDB(key: string, blob: Blob): Promise<void> {
		try {
			const db = await this.initIndexedDB();

			return new Promise((resolve, reject) => {
				const transaction = db.transaction(["images"], "readwrite");
				const store = transaction.objectStore("images");

				const data = {
					key,
					data: blob,
					timestamp: Date.now(),
					size: blob.size,
				};

				const putRequest = store.put(data);

				putRequest.onsuccess = () => {
					console.log("💾 IndexedDB: 图片保存成功", {
						key: key.slice(0, 50),
						size: blob.size,
					});
					resolve();
				};

				putRequest.onerror = () => {
					console.error(
						"❌ IndexedDB: 保存图片失败",
						putRequest.error,
					);
					reject(putRequest.error);
				};

				transaction.onerror = () => {
					console.error(
						"❌ IndexedDB: 保存事务失败",
						transaction.error,
					);
					reject(transaction.error);
				};
			});
		} catch (error) {
			console.error("❌ IndexedDB: 保存失败", error);
			throw error;
		}
	}

	/**
	 * 格式化字节数
	 */
	private formatBytes(bytes: number): string {
		if (bytes === 0) return "0 B";
		const k = 1024;
		const sizes = ["B", "KB", "MB", "GB"];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
	}

	/**
	 * 清理缓存
	 */
	clearCache() {
		// 🔧 修复TypeScript错误：使用forEach替代for...of
		this.cache.forEach((entry) => {
			URL.revokeObjectURL(URL.createObjectURL(entry.blob));
		});
		this.cache.clear();
		this.totalCacheSize = 0;
		console.log("🧹 ImageCache: 缓存已清理");
	}

	/**
	 * 获取缓存统计信息
	 */
	getCacheStats() {
		return {
			totalEntries: this.cache.size,
			totalSize: this.totalCacheSize,
			formattedSize: this.formatBytes(this.totalCacheSize),
			entries: Array.from(this.cache.values()).map((entry) => ({
				url: entry.url.slice(0, 50),
				size: entry.size,
				priority: entry.priority,
				accessCount: entry.accessCount,
				age: Date.now() - entry.timestamp,
			})),
		};
	}
}

// 全局实例
export const imageCache = new ImageCacheManager();

// 导出配置和类型
export { CACHE_CONFIG, type ImageProcessOptions };
