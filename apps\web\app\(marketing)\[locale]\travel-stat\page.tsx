import type { Metadata } from "next";
import { Suspense } from "react";
import { TravelFootprintTool } from "./TravelFootprintTool";

export const metadata: Metadata = {
	title: "旅行足迹记录工具 - 在互动地图上记录你的旅行足迹",
	description:
		"专业的旅行足迹记录工具。在互动世界地图上标记你去过的地方，支持多种地图投影，统计访问的城市和国家数量，导出和导入你的旅行数据。",
	keywords:
		"旅行足迹,足迹记录,地图标记,旅行统计,世界地图,旅行工具,地图投影,互动地图,旅行日记,旅行地图,世界足迹,旅行记录,地图工具",
	authors: [{ name: "Travel Memo" }],
	creator: "Travel Memo",
	publisher: "Travel Memo",
	robots: {
		index: true,
		follow: true,
		googleBot: {
			index: true,
			follow: true,
			"max-video-preview": -1,
			"max-image-preview": "large",
			"max-snippet": -1,
		},
	},
	openGraph: {
		title: "旅行足迹记录工具 - 在互动地图上记录你的旅行足迹",
		description:
			"专业的旅行足迹记录工具。在互动世界地图上标记你去过的地方，支持多种地图投影和样式。",
		type: "website",
		locale: "zh_CN",
		siteName: "Travel Memo",
		images: [
			{
				url: "/api/og?title=旅行足迹记录工具",
				width: 1200,
				height: 630,
				alt: "旅行足迹记录工具截图",
			},
		],
	},
	twitter: {
		card: "summary_large_image",
		title: "旅行足迹记录工具 - 记录你的世界足迹",
		description: "在互动地图上标记旅行足迹，支持多种地图样式和数据导出。",
		images: ["/api/og?title=旅行足迹记录工具"],
	},
	alternates: {
		canonical: "/travel-stat",
	},
	// Google Search Console 验证标签
	// 请替换为您的实际验证码
	verification: {
		google: "your-google-verification-code",
	},
};

function TravelStatPageContent() {
	return (
		<>
			<div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
				<TravelFootprintTool />
			</div>
		</>
	);
}

export default function TravelStatPage() {
	return (
		<Suspense fallback={<div>Loading...</div>}>
			<TravelStatPageContent />
		</Suspense>
	);
}
