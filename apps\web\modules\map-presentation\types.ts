import type { TravelPointImage } from "@repo/database/src/types/travel-diary";

/**
 * 经纬度坐标类型
 */
export interface LatLng {
	lat: number;
	lng: number;
}

/**
 * 地图点位类型
 */
export interface MapPoint {
	coordinates: LatLng;
	location: string;
	description: string;
	date: string;
	iconType?: string;
	images?: TravelPointImage[];
}

// 使用类型声明而不是直接引用
export interface MarkerRef {
	setMap: (map: any | null) => void;
	addListener: (event: string, handler: () => void) => void;
	getPosition?: () => { lat: () => number; lng: () => number };
	position?: { lat: number; lng: number }; // 支持高级标记的 position 属性
}

// 活动标记相关类型
export interface PulseEffectRef {
	setMap: (map: any | null) => void;
	setRadius: (radius: number) => void;
	setOptions: (options: any) => void;
}

export interface ActiveMarkerState {
	marker: MarkerRef;
	point: MapPoint;
	pulseEffect: PulseEffectRef | null;
}

export interface PolylineRef {
	setMap: (map: any | null) => void;
	setPath: (path: Array<{ lat: number; lng: number }>) => void;
}

/**
 * 地图动画配置
 */
export interface MapAnimationConfig {
	/** 路线动画持续时间(秒) */
	routeAnimationDuration?: number;
	/** 路线颜色 */
	routeColor?: string;
	/** 路线宽度 */
	routeWeight?: number;
	/** 点标记掉落动画 */
	markerAnimation?: string | number;
	/** 过渡间隔时间(毫秒) */
	transitionDelay?: number;
	/** 路线动画样式: 'arrow' | 'dashed' | 'gradient' */
	routeAnimationStyle?: "arrow" | "dashed" | "gradient";
	/** 是否使用平滑路径插值 */
	useSmoothPath?: boolean;
}

/**
 * 动画阶段回调
 */
export interface AnimationCallbacks {
	/** 中点移动完成后回调 */
	onMidPointReached?: () => void;
	/** 路线动画完成后回调 */
	onRouteAnimationComplete?: () => void;
	/** 目标点聚焦完成后回调 */
	onTargetFocusComplete?: () => void;
}
