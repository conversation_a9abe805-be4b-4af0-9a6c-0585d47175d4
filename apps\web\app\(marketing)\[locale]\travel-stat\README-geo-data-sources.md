# 地理数据源配置指南

## 🎯 问题解决

解决了从 GitHub raw 链接下载世界国家 GeoJSON 数据经常失败的问题，以及美国等国家颜色不显示的问题。

## 📊 数据源优先级

系统按以下顺序尝试加载世界国家边界数据：

### 1. 本地文件（最高优先级）
- **路径**: `/public/data/countries.geo.json`
- **格式**: GeoJSON
- **可靠性**: ⭐⭐⭐⭐⭐
- **说明**: 已预下载到本地，加载最快最稳定

### 2. CDN 备份
- **unpkg**: `https://unpkg.com/world-atlas@1/countries-110m.json`
- **jsDelivr**: `https://cdn.jsdelivr.net/npm/world-atlas@1/countries-110m.json`
- **格式**: TopoJSON（暂时跳过，需要转换）
- **可靠性**: ⭐⭐⭐⭐

### 3. GitHub 备份
- **原始**: `https://raw.githubusercontent.com/johan/world.geo.json/master/countries.geo.json`
- **D3 Gallery**: `https://raw.githubusercontent.com/holtzy/D3-graph-gallery/master/DATA/world.geojson`
- **格式**: GeoJSON
- **可靠性**: ⭐⭐

## 🔧 国家名称映射

### 根本问题
- **Mapbox Geocoding API** 返回: `"United States"`
- **GeoJSON 数据源** 中的名称: `"United States of America"`
- **结果**: 名称不匹配，导致颜色不显示

### 解决方案
实现了国家名称标准化映射表：

```typescript
const COUNTRY_NAME_MAPPING = {
  // 美国的各种变体 -> GeoJSON 标准名称
  "United States": "United States of America",
  "USA": "United States of America", 
  "US": "United States of America",
  "America": "United States of America",
  
  // 其他常见变体...
};
```

## 🛠️ 技术实现

### 数据源配置文件
- **位置**: `constants/geoDataSources.ts`
- **功能**: 
  - 统一管理所有数据源
  - 提供国家名称标准化
  - 支持多种数据格式

### 加载逻辑
```typescript
// 按优先级尝试每个数据源
for (const source of dataSources) {
  try {
    console.log(`🌍 尝试从 ${source.name} 加载...`);
    const response = await fetch(source.url);
    
    if (response.ok) {
      const data = await response.json();
      // 验证数据格式并使用
      if (data.type === "FeatureCollection") {
        console.log(`✅ 成功加载 ${data.features.length} 个国家`);
        setWorldCountriesData(data);
        return;
      }
    }
  } catch (error) {
    console.warn(`❌ 从 ${source.name} 加载失败`);
    // 继续尝试下一个数据源
  }
}
```

## 🐛 调试功能

在开发模式下提供了详细的调试信息：

### 控制台输出
- `🌍 世界国家数据已加载` - 数据加载完成
- `🇺🇸 找到的美国相关特征` - 美国数据结构分析
- `🗺️ 已访问的国家` - 当前访问国家列表
- `🎨 颜色表达式长度` - 地图颜色匹配规则数量

### 地图点击调试
点击地图上的任何国家，查看该国家在数据中的实际属性：
```javascript
onClick={(e) => {
  if (e.features && e.features.length > 0) {
    const feature = e.features[0];
    console.log("🌍 点击的国家数据:", {
      properties: feature.properties,
      allKeys: Object.keys(feature.properties || {}),
    });
  }
}}
```

### 调试面板
在开发模式下，地图左上角显示实时调试信息：
- 已访问国家数量
- 前3个国家及访问次数
- 提示信息

## 📈 性能优化

### 本地缓存
- 世界国家数据缓存在 `/public/data/`
- 避免每次加载时网络请求
- 减少加载时间和失败率

### 备用策略
- 多个数据源确保高可用性
- 自动降级到下一个可用源
- 详细的错误日志便于问题排查

## 🔄 维护说明

### 更新地理数据
如需更新世界国家边界数据：

```bash
# 重新下载最新数据
curl -o public/data/countries.geo.json "https://raw.githubusercontent.com/johan/world.geo.json/master/countries.geo.json"
```

### 添加新的国家映射
在 `geoDataSources.ts` 中的 `COUNTRY_NAME_MAPPING` 添加新的映射关系：

```typescript
export const COUNTRY_NAME_MAPPING = {
  // 现有映射...
  
  // 新增映射
  "别名": "标准名称",
} as const;
```

### 添加新的数据源
在 `GEO_DATA_SOURCES` 中添加新的备用源：

```typescript
fallback: [
  // 现有源...
  {
    url: "新的数据源URL",
    name: "数据源名称",
    type: "geojson",
    reliable: true/false,
  },
],
```

## ✅ 验证测试

要验证修复是否生效：

1. **打开开发者控制台**
2. **搜索美国地点** (如 "New York", "Los Angeles")
3. **查看控制台日志**:
   - 应显示 `🌍 世界国家数据已加载`
   - 应显示 `🗺️ 已访问的国家: [...]` 包含美国
4. **观察地图**:
   - 美国区域应显示颜色
   - 点击美国查看属性数据
5. **检查调试面板**:
   - 左上角应显示已访问国家包含美国

如果仍有问题，控制台会显示详细的错误信息和数据结构，便于进一步排查。 