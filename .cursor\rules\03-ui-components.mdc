---
description: 
globs: *.tsx
alwaysApply: false
---
# UI Components and Styling Guide

## Component Libraries
- Use Shadcn UI as the primary component library
- Use Radix UI for complex interactive components
- Implement consistent styling with Tailwind CSS

## Tailwind Usage
- Follow mobile-first responsive design
- Use the `cn` utility for class merging
- Maintain consistent spacing and sizing
- Use CSS variables for theming

## Component Structure
```tsx
import { cn } from "@lib/utils"

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "default" | "outline"
}

export function Button({ 
  className, 
  variant = "default",
  ...props 
}: ButtonProps) {
  return (
    <button
      className={cn(
        "rounded-md px-4 py-2",
        variant === "outline" && "border border-gray-200",
        className
      )}
      {...props}
    />
  )
}
```

## Best Practices
- Keep components small and focused
- Use composition over inheritance
- Implement proper loading states
- Handle error states gracefully
- Use semantic HTML elements
- Ensure keyboard accessibility
- Support dark mode

## Layout Guidelines
- Use CSS Grid for page layouts
- Use Flexbox for component layouts
- Implement proper spacing system
- Ensure responsive behavior
- Consider mobile interactions
