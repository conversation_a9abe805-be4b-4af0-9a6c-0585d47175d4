"use client";
import { Card } from "@ui/components/card";
import { MapPin, RotateCcw } from "lucide-react";
import "mapbox-gl/dist/mapbox-gl.css";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import MapGL, {
	FullscreenControl,
	GeolocateControl,
	Layer,
	NavigationControl,
	ScaleControl,
	Source,
} from "react-map-gl";
import {
	GEOJSON_COUNTRY_NAME_FIELDS,
	getCountryNameVariants,
} from "../../constants/countryNameMapping";
import { MAPBOX_TOKEN } from "../../constants/mapConfig";
import { useMap } from "../../contexts/MapContext";
import { useMapboxControlStyling } from "../../hooks/useMapboxControlStyling";
import type { TravelPoint } from "../../types";
import {
	getStrategyManager,
	updateStrategyTheme,
} from "../../utils/colorUtils";
import {
	BackgroundAnimations,
	useGlobePosition,
} from "../animation/BackgroundAnimations";
import { ImageLoadingDebug } from "../debug/ImageLoadingDebug";
import { ImagePerformanceMonitor } from "../debug/ImagePerformanceMonitor";
import { MapControls } from "../map-controls";
import { MAP_STYLE_CONFIGS } from "../map-style/MapStyleSwitcher";
import { ClusterManager } from "./ClusterManager";

// Props are now handled by the context, so this interface is empty.
interface MapContainerProps {
	// Optional props for future extensibility
	className?: string;
}

export function MapContainer(_props: MapContainerProps) {
	// 🔍 开发环境渲染计数器
	const renderCountRef = useRef(0);
	renderCountRef.current += 1;
	if (process.env.NODE_ENV === "development") {
		console.log(`🔄 MapContainer 渲染次数: ${renderCountRef.current}`);
	}

	// 地图容器引用，用于监听尺寸变化
	const mapContainerRef = useRef<HTMLDivElement>(null);

	// 地图缩放级别状态
	const [mapZoom, setMapZoom] = useState(4); // 默认缩放级别

	// Consume the central map context
	const {
		travelPoints,
		visitedCountries,
		worldCountriesData,
		viewState,
		mapLoaded,
		atmosphereTheme,
		mapStyle,
		animationTheme,
		mapProjection,
		currentTheme: currentColorTheme, // alias for local usage
		markerStyle,
		markerTheme,
		currentEmoji,
		currentEmojiColor,
		hideOutline,
		mapRef: externalMapRef,
		handleMapLoad: onMapLoad, // alias for local usage
		resetMapView,
		removeTravelPoint: onRemovePoint, // alias
		setAtmosphereTheme: onAtmosphereChange, // alias
		setMapStyle: onMapStyleChange, // alias
		setAnimationTheme: onAnimationChange, // alias
		setMapProjection: onProjectionChange, // alias
		changeTheme: onColorThemeChange, // alias
		changeMarkerStyle: onMarkerStyleChange, // alias
		changeMarkerTheme: onMarkerThemeChange, // alias
		changeEmoji: onEmojiChange, // alias
		changeEmojiColor: onEmojiColorChange, // alias
		changeHideOutline: onHideOutlineChange, // alias
		// tooltip相关功能
		showTooltips,
		showMarkers,
		setShowTooltips,
		setShowMarkers,
		polaroidTooltipState, // 全局tooltip状态
		showPolaroidTooltip, // marker点击处理函数
	} = useMap();

	// The onResetView handler is now created locally using values from context
	const onResetView = useCallback(() => {
		resetMapView(travelPoints);
	}, [resetMapView, travelPoints]);

	// 使用来自 context 的 mapRef，不需要创建新的引用
	const mapRef = externalMapRef;

	// 动态跟踪地球位置 - 用于背景动画遮罩
	const globePosition = useGlobePosition(mapRef, true);

	// 应用Mapbox控件自定义样式
	useMapboxControlStyling();

	// 地图加载回调 - 同时设置 ref 和调用原始回调
	const handleMapLoad = useCallback(
		(event: any) => {
			// 调用原始的地图加载回调
			onMapLoad(event);

			// 获取初始缩放级别
			const map = event.target;
			if (map) {
				const currentZoom = map.getZoom();
				setMapZoom(currentZoom);

				// 监听缩放变化事件
				map.on("zoom", () => {
					const newZoom = map.getZoom();
					setMapZoom(newZoom);
				});
			}
		},
		[onMapLoad],
	);

	// 地图尺寸调整函数
	const resizeMap = useCallback(() => {
		if (mapRef?.current && mapLoaded) {
			try {
				const map = mapRef.current;
				if (map && typeof map.resize === "function") {
					// 立即调用一次
					map.resize();

					// 延迟再调用一次，确保容器尺寸更新完成
					setTimeout(() => {
						map.resize();
						console.log("🗺️ 地图尺寸已调整");
					}, 100);

					// 如果是在卡片模式下，可能需要更长的延迟
					setTimeout(() => {
						map.resize();
						console.log("🗺️ 地图尺寸二次调整完成");
					}, 300);
				}
			} catch (error) {
				console.warn("⚠️ 地图尺寸调整失败:", error);
			}
		}
	}, [mapRef, mapLoaded]);

	// 监听容器尺寸变化
	useEffect(() => {
		if (!mapContainerRef.current) {
			return;
		}

		// 使用 ResizeObserver 监听容器尺寸变化
		const resizeObserver = new ResizeObserver((entries) => {
			for (const entry of entries) {
				const { width, height } = entry.contentRect;
				console.log("📏 地图容器尺寸变化:", { width, height });

				// 当容器尺寸变化时，调整地图尺寸
				resizeMap();
			}
		});

		resizeObserver.observe(mapContainerRef.current);

		// 监听 window resize 作为备用方案
		const handleWindowResize = () => {
			console.log("🪟 窗口尺寸变化，调整地图");
			resizeMap();
		};

		window.addEventListener("resize", handleWindowResize);

		// 清理函数
		return () => {
			resizeObserver.disconnect();
			window.removeEventListener("resize", handleWindowResize);
		};
	}, [resizeMap]);

	// 当地图加载完成时，确保尺寸正确
	useEffect(() => {
		if (mapLoaded) {
			// 地图加载完成后立即调整一次尺寸
			setTimeout(() => {
				resizeMap();
			}, 100);
		}
	}, [mapLoaded, resizeMap]);

	// 监听数据变化，在数据加载后调整地图尺寸
	useEffect(() => {
		if (
			mapLoaded &&
			(travelPoints.length > 0 || visitedCountries.length > 0)
		) {
			// 数据加载完成后，延迟调整地图尺寸以适应可能的布局变化
			const timer = setTimeout(() => {
				resizeMap();
				console.log("📊 数据加载完成：调整地图尺寸", {
					points: travelPoints.length,
					countries: visitedCountries.length,
				});
			}, 500); // 稍长的延迟确保所有布局更新完成

			return () => clearTimeout(timer);
		}
	}, [mapLoaded, travelPoints.length, visitedCountries.length, resizeMap]);

	// 更新策略管理器的主题（当主题变化时）
	useEffect(() => {
		updateStrategyTheme(currentColorTheme);
	}, [currentColorTheme]);

	// 根据旅行点计算标记颜色（使用策略系统）
	const getMarkerColor = useCallback(
		(point: TravelPoint) => {
			// 根据该城市/国家的访问次数确定颜色
			const countryData = visitedCountries.find(
				(c) => c.name === point.country,
			);
			const visitCount = countryData?.visitCount || 1;

			// 使用策略管理器计算颜色
			return getStrategyManager().getHexColor(visitCount);
		},
		[visitedCountries],
	);

	// 国家颜色表达式状态 - 确保图层重新渲染
	const countryColorExpression = useMemo(() => {
		// 初始化颜色表达式
		const strategyManager = getStrategyManager();
		let newExpression: any;
		if (visitedCountries.length === 0) {
			newExpression = strategyManager.getRgbaColor(0);
		} else {
			const caseExpression: any[] = ["case"];

			visitedCountries.forEach((country) => {
				// 使用策略管理器计算颜色
				const color = strategyManager.getRgbaColor(country.visitCount);

				// 使用统一的国家名称映射系统
				const countryNameVariants = getCountryNameVariants(
					country.name,
				);

				// 为每个名称变体和属性字段添加匹配条件
				countryNameVariants.forEach((variant) => {
					GEOJSON_COUNTRY_NAME_FIELDS.forEach((field) => {
						caseExpression.push(
							["==", ["get", field], variant],
							color,
						);
					});
				});
			});

			// 默认颜色
			caseExpression.push(strategyManager.getRgbaColor(0));

			newExpression = caseExpression as any;
		}

		return newExpression;
	}, [visitedCountries, currentColorTheme]);

	// 调试：当世界国家数据加载完成时，分析数据结构
	useEffect(() => {
		if (worldCountriesData && process.env.NODE_ENV === "development") {
			console.log("🌍 世界国家数据已加载，开始分析结构...");

			// 查找美国的数据
			const usaFeatures = worldCountriesData.features?.filter(
				(feature: any) => {
					const props = feature.properties || {};
					const propValues = Object.values(props).map((v) =>
						String(v).toLowerCase(),
					);
					return propValues.some(
						(val: string) =>
							val.includes("united states") ||
							val.includes("usa") ||
							val.includes("america") ||
							val === "us",
					);
				},
			);

			console.log("🇺🇸 找到的美国相关特征:", usaFeatures?.length);

			usaFeatures?.forEach((feature: any, index: number) => {
				console.log(`🗺️ 美国特征 #${index + 1}:`, {
					properties: feature.properties,
					allPropertyKeys: Object.keys(feature.properties || {}),
				});
			});

			// 显示前3个国家的属性结构作为参考
			console.log("📋 前3个国家的属性结构:");
			worldCountriesData.features
				?.slice(0, 3)
				.forEach((feature: any, index: number) => {
					console.log(`国家 #${index + 1}:`, {
						properties: feature.properties,
						keys: Object.keys(feature.properties || {}),
					});
				});
		}
	}, [worldCountriesData]);

	return (
		<Card className="overflow-hidden bg-white/80 backdrop-blur-sm border-sky-200 relative h-full">
			<div ref={mapContainerRef} className="h-full relative">
				{MAPBOX_TOKEN ? (
					<div className="relative w-full h-full">
						<MapGL
							ref={(ref) => {
								if (ref) {
									// 保存 react-map-gl 的 ref 实例（用于导出功能）
									(mapRef as any).reactMapGLRef = ref;
									// 保存 mapboxgl.Map 实例（用于其他功能）
									mapRef.current = ref.getMap();
								} else {
									(mapRef as any).reactMapGLRef = null;
									mapRef.current = null;
								}
							}}
							mapboxAccessToken={MAPBOX_TOKEN}
							initialViewState={viewState}
							style={{
								width: "100%",
								height: "100%",
							}}
							mapStyle={MAP_STYLE_CONFIGS[mapStyle].url}
							projection={mapProjection as any}
							onLoad={handleMapLoad}
							interactiveLayerIds={["country-fills"]}
							onClick={(e) => {
								// 调试：点击地图时显示国家信息
								if (e.features && e.features.length > 0) {
									const feature = e.features[0];
									if (feature.source === "world-countries") {
										console.log("🌍 点击的国家数据:", {
											properties: feature.properties,
										});
									}
								}
							}}
							// 🔑 关键配置：启用导出功能
							preserveDrawingBuffer={true}
						>
							{/* 自定义重置视图按钮 - 放在右上角第一个位置 */}
							<div
								className="absolute top-0.5 right-0.5 z-30"
								data-export-hidden="true"
								data-map-control="reset-view"
								style={{
									// 确保在卡片缩放环境下重置按钮可交互
									zIndex: 102,
									isolation: "isolate",
								}}
							>
								<div
									className="mapboxgl-ctrl-group"
									style={{
										background: "rgba(255, 255, 255, 0.9)",
										backdropFilter: "blur(8px)",
										border: "1px solid rgb(224, 242, 254)",
										borderRadius: "10px",
										boxShadow:
											"0 1px 2px 0 rgba(0, 0, 0, 0.05)",
										padding: "3px",
										margin: "6px",
										display: "flex",
										flexDirection: "column",
									}}
								>
									<button
										type="button"
										onClick={onResetView}
										disabled={!mapLoaded}
										title={
											travelPoints.length === 0
												? "重置到默认视图"
												: `重置到${travelPoints.length}个点位视图`
										}
										style={{
											background: "transparent",
											border: "1px solid transparent",
											borderRadius: "6px",
											width: "28px",
											height: "28px",
											margin: "1px",
											display: "flex",
											alignItems: "center",
											justifyContent: "center",
											transition:
												"all 150ms cubic-bezier(0.4, 0, 0.2, 1)",
											color: "rgb(71, 85, 105)",
											outline: "none",
											fontSize: "12px",
											fontWeight: "500",
											padding: "0",
											flexShrink: "0",
											cursor: mapLoaded
												? "pointer"
												: "not-allowed",
											opacity: mapLoaded ? "1" : "0.5",
											// 确保按钮在缩放环境下可点击
											zIndex: 103,
											pointerEvents: "auto",
										}}
										onMouseEnter={(e) => {
											if (mapLoaded) {
												e.currentTarget.style.background =
													"rgb(248, 250, 252)";
												e.currentTarget.style.borderColor =
													"rgb(226, 232, 240)";
												e.currentTarget.style.color =
													"rgb(51, 65, 85)";
											}
										}}
										onMouseLeave={(e) => {
											if (mapLoaded) {
												e.currentTarget.style.background =
													"transparent";
												e.currentTarget.style.borderColor =
													"transparent";
												e.currentTarget.style.color =
													"rgb(71, 85, 105)";
											}
										}}
										onMouseDown={(e) => {
											if (mapLoaded) {
												e.currentTarget.style.background =
													"rgb(241, 245, 249)";
											}
										}}
										onMouseUp={(e) => {
											if (mapLoaded) {
												e.currentTarget.style.background =
													"rgb(248, 250, 252)";
											}
										}}
									>
										<RotateCcw
											className="w-[18px] h-[18px]"
											style={{ opacity: "0.8" }}
										/>
									</button>
								</div>
							</div>

							{/* 地图控件 - 放在重置按钮下方 */}
							<GeolocateControl position="top-right" />
							<FullscreenControl position="top-right" />
							<NavigationControl position="top-right" />
							<ScaleControl />

							{/* 世界国家边界图层 */}
							{worldCountriesData && (
								<Source
									id="world-countries"
									type="geojson"
									data={worldCountriesData}
								>
									<Layer
										id="country-fills"
										type="fill"
										paint={{
											"fill-color":
												countryColorExpression,
											"fill-opacity": 0.7,
										}}
									/>
									<Layer
										id="country-borders"
										type="line"
										paint={{
											"line-color": "#ffffff",
											"line-width": 1,
											"line-opacity": 0.8,
										}}
									/>
								</Source>
							)}

							{/* 智能聚合标记管理器 - 自动处理性能优化 */}
							<ClusterManager
								travelPoints={travelPoints}
								mapRef={mapRef}
								mapZoom={mapZoom}
								showMarkers={showMarkers}
								showTooltips={showTooltips}
								markerStyle={markerStyle}
								markerTheme={markerTheme}
								currentEmoji={currentEmoji}
								currentEmojiColor={currentEmojiColor}
								hideOutline={hideOutline}
								getMarkerColor={getMarkerColor}
								onRemovePoint={onRemovePoint}
								onMarkerClick={showPolaroidTooltip}
								polaroidTooltipState={polaroidTooltipState}
							/>
						</MapGL>

						{/* 新的动画控制器 - 背景层，控制按钮可交互 */}
						{/* <AnimationController
							className="z-0"
							onAnimationChange={(type, theme) => {
								console.log("🎨 动画切换:", type, theme);
							}}
						/> */}

						{/* 背景动画效果 - 在地图下方，带圆形遮罩 */}
						<BackgroundAnimations
							theme={animationTheme}
							foreground={false}
							maskGlobe={true}
							globeCenter={{ x: 50, y: 50 }} // 固定在容器中心
							globeRadius={33} // 覆盖三分之二，所以遮罩半径为33%
							className="absolute inset-0 pointer-events-none"
							style={{
								zIndex: 0, // 确保在地图下方
							}}
						/>

						{/* 地图控制器 - 直接定位，确保可以点击 */}
						<MapControls
							mapLoaded={mapLoaded}
							atmosphereTheme={atmosphereTheme}
							mapStyle={mapStyle}
							animationTheme={animationTheme}
							mapProjection={mapProjection}
							currentColorTheme={currentColorTheme}
							markerStyle={markerStyle}
							markerTheme={markerTheme}
							currentEmoji={currentEmoji}
							currentEmojiColor={currentEmojiColor}
							hideOutline={hideOutline}
							mapRef={mapRef}
							travelPoints={travelPoints}
							visitedCountries={visitedCountries}
							showTooltips={showTooltips}
							showMarkers={showMarkers}
							onAtmosphereChange={onAtmosphereChange}
							onMapStyleChange={onMapStyleChange}
							onAnimationChange={onAnimationChange}
							onProjectionChange={onProjectionChange}
							onColorThemeChange={onColorThemeChange}
							onMarkerStyleChange={onMarkerStyleChange}
							onMarkerThemeChange={onMarkerThemeChange}
							onEmojiChange={onEmojiChange}
							onEmojiColorChange={onEmojiColorChange}
							onHideOutlineChange={onHideOutlineChange}
							onShowTooltipsChange={setShowTooltips}
							onShowMarkersChange={setShowMarkers}
						/>
					</div>
				) : (
					<div className="flex items-center justify-center h-full">
						<div className="text-center">
							<MapPin className="mx-auto h-12 w-12 text-gray-400" />
							<h3 className="mt-2 text-sm font-medium text-gray-900">
								缺少 Mapbox Token
							</h3>
							<p className="mt-1 text-sm text-gray-500">
								请在配置文件中提供 Mapbox Token才能显示地图。
							</p>
						</div>
					</div>
				)}
			</div>

			{/* 调试组件 - 仅在开发环境显示 */}
			{process.env.NODE_ENV === "development" && (
				<>
					<ImageLoadingDebug isVisible={false} />
					<ImagePerformanceMonitor isVisible={false} />
				</>
			)}
		</Card>
	);
}
