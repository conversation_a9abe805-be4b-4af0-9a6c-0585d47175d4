"use client";

import { type FormEvent, useCallback, useState } from "react";
import { createAnonymousUser, getAnonymousUserData } from "../anonymous-user";
import type { CreateFeatureRequestData } from "../types";

interface UseFeatureRequestFormOptions {
	onSubmit?: (data: CreateFeatureRequestData) => Promise<void>;
	validateTitle?: (title: string) => string | null;
	validateDescription?: (description: string) => string | null;
	validateEmail?: (email: string) => string | null;
	requireAuthorInfo?: boolean;
}

interface FormData {
	title: string;
	description: string;
	productId: string;
	authorName: string;
	authorEmail: string;
}

interface FormErrors {
	title?: string;
	description?: string;
	productId?: string;
	authorName?: string;
	authorEmail?: string;
	general?: string;
}

interface UseFeatureRequestFormReturn {
	// 表单数据
	formData: FormData;
	setFormData: (data: Partial<FormData>) => void;

	// 错误状态
	errors: FormErrors;
	setErrors: (errors: Partial<FormErrors>) => void;

	// 提交状态
	isSubmitting: boolean;
	isSubmitted: boolean;

	// 操作
	handleSubmit: (e?: FormEvent) => Promise<void>;
	handleFieldChange: (field: keyof FormData, value: string) => void;
	resetForm: () => void;
	clearErrors: () => void;

	// 验证
	validateForm: () => boolean;
	validateField: (field: keyof FormData) => string | null;
}

const defaultFormData: FormData = {
	title: "",
	description: "",
	productId: "",
	authorName: "",
	authorEmail: "",
};

// 默认验证函数
const defaultValidations = {
	validateTitle: (title: string): string | null => {
		if (!title.trim()) return "标题不能为空";
		if (title.length > 200) return "标题不能超过200个字符";
		return null;
	},

	validateDescription: (description: string): string | null => {
		if (!description.trim()) return "描述不能为空";
		if (description.length > 2000) return "描述不能超过2000个字符";
		return null;
	},

	validateEmail: (email: string): string | null => {
		if (!email) return null; // 可选字段
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(email)) return "请输入有效的邮箱地址";
		return null;
	},
};

export function useFeatureRequestForm(
	options: UseFeatureRequestFormOptions = {},
): UseFeatureRequestFormReturn {
	const {
		onSubmit,
		validateTitle = defaultValidations.validateTitle,
		validateDescription = defaultValidations.validateDescription,
		validateEmail = defaultValidations.validateEmail,
		requireAuthorInfo = false,
	} = options;

	// 状态
	const [formData, setFormDataState] = useState<FormData>(defaultFormData);
	const [errors, setErrorsState] = useState<FormErrors>({});
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [isSubmitted, setIsSubmitted] = useState(false);

	// 设置表单数据
	const setFormData = useCallback((data: Partial<FormData>) => {
		setFormDataState((prev: FormData) => ({ ...prev, ...data }));
		// 清除相关字段的错误
		const clearedErrors: Partial<FormErrors> = {};
		Object.keys(data).forEach((key) => {
			clearedErrors[key as keyof FormErrors] = undefined;
		});
		setErrorsState((prev: FormErrors) => ({ ...prev, ...clearedErrors }));
	}, []);

	// 设置错误
	const setErrors = useCallback((newErrors: Partial<FormErrors>) => {
		setErrorsState((prev: FormErrors) => ({ ...prev, ...newErrors }));
	}, []);

	// 清除所有错误
	const clearErrors = useCallback(() => {
		setErrorsState({});
	}, []);

	// 验证单个字段
	const validateField = useCallback(
		(field: keyof FormData): string | null => {
			const value = formData[field];

			switch (field) {
				case "title":
					return validateTitle(value);
				case "description":
					return validateDescription(value);
				case "productId":
					return !value ? "请选择产品" : null;
				case "authorName":
					return requireAuthorInfo && !value
						? "请输入您的姓名"
						: null;
				case "authorEmail":
					return validateEmail(value);
				default:
					return null;
			}
		},
		[
			formData,
			validateTitle,
			validateDescription,
			validateEmail,
			requireAuthorInfo,
		],
	);

	// 验证整个表单
	const validateForm = useCallback((): boolean => {
		const newErrors: FormErrors = {};

		// 验证所有字段
		(Object.keys(formData) as Array<keyof FormData>).forEach((field) => {
			const error = validateField(field);
			if (error) {
				newErrors[field] = error;
			}
		});

		setErrorsState(newErrors);
		return Object.keys(newErrors).length === 0;
	}, [formData, validateField]);

	// 处理字段变化
	const handleFieldChange = useCallback(
		(field: keyof FormData, value: string) => {
			setFormData({ [field]: value });
		},
		[setFormData],
	);

	// 重置表单
	const resetForm = useCallback(() => {
		setFormDataState(defaultFormData);
		setErrorsState({});
		setIsSubmitted(false);
	}, []);

	// 处理表单提交
	const handleSubmit = useCallback(
		async (e?: FormEvent) => {
			if (e) {
				e.preventDefault();
			}

			// 验证表单
			if (!validateForm()) {
				return;
			}

			try {
				setIsSubmitting(true);
				setErrorsState((prev: FormErrors) => ({
					...prev,
					general: undefined,
				}));

				// 获取匿名用户数据
				let anonymousUser = getAnonymousUserData();
				if (!anonymousUser) {
					anonymousUser = createAnonymousUser();
				}

				// 准备提交数据
				const submitData: CreateFeatureRequestData = {
					title: formData.title.trim(),
					description: formData.description.trim(),
					productId: formData.productId,
					authorName:
						formData.authorName.trim() ||
						anonymousUser.voterName ||
						"",
					authorEmail:
						formData.authorEmail.trim() ||
						anonymousUser.voterEmail ||
						"",
					anonymousId: anonymousUser.anonymousId,
				};

				// 调用提交回调
				if (onSubmit) {
					await onSubmit(submitData);
				}

				setIsSubmitted(true);
			} catch (error) {
				setErrorsState((prev: FormErrors) => ({
					...prev,
					general:
						error instanceof Error
							? error.message
							: "提交失败，请重试",
				}));
			} finally {
				setIsSubmitting(false);
			}
		},
		[formData, validateForm, onSubmit],
	);

	return {
		// 表单数据
		formData,
		setFormData,

		// 错误状态
		errors,
		setErrors,

		// 提交状态
		isSubmitting,
		isSubmitted,

		// 操作
		handleSubmit,
		handleFieldChange,
		resetForm,
		clearErrors,

		// 验证
		validateForm,
		validateField,
	};
}
