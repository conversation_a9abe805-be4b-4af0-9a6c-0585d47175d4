import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@ui/components/card";
import type { LucideIcon } from "lucide-react";

interface StatsCardProps {
	title: string;
	value: string | number;
	icon: LucideIcon;
	description?: string;
	className?: string;
}

const StatsCard = ({
	title,
	value,
	icon: Icon,
	description,
	className,
}: StatsCardProps) => {
	return (
		<Card className={`overflow-hidden ${className}`}>
			<CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
				<CardTitle className="text-sm font-medium">{title}</CardTitle>
				<Icon className="h-4 w-4 text-muted-foreground" />
			</CardHeader>
			<CardContent>
				<div className="text-2xl font-bold">{value}</div>
				{description && (
					<p className="text-xs text-muted-foreground mt-1">
						{description}
					</p>
				)}
			</CardContent>
		</Card>
	);
};

export default StatsCard;
