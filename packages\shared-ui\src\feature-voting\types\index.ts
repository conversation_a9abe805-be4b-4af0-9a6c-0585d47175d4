/**
 * Feature Voting 模块的类型定义
 */

import type { ComponentType } from "react";

export interface Product {
	id: string;
	name: string;
	description?: string;
	createdAt: string;
	updatedAt: string;
	_count?: {
		featureRequests: number;
	};
}

export interface FeatureRequest {
	id: string;
	title: string;
	description: string;
	status: FeatureStatus;
	voteCount: number;
	productId: string;
	product?: {
		id: string;
		name: string;
	};
	author?: {
		name: string;
	};
	hasVoted?: boolean;
	commentCount?: number;
	createdAt: string;
	updatedAt: string;
}

export interface FeatureComment {
	id: string;
	content: string;
	author: {
		name: string;
	};
	createdAt: string;
}

export interface FeatureVote {
	id: string;
	featureRequestId: string;
	userId?: string;
	anonymousId?: string;
	voterName?: string;
	voterEmail?: string;
	createdAt: string;
}

export type FeatureStatus =
	| "under_consideration" // 待考虑
	| "planned" // 计划中
	| "in_progress" // 开发中
	| "completed" // 已完成
	| "wont_do"; // 不考虑

export interface FeatureStatusConfig {
	value: FeatureStatus;
	label: string;
	color: string;
	bgColor: string;
	description: string;
}

export interface AnonymousUser {
	id: string;
	name?: string;
	email?: string;
	createdAt: string;
}

export interface VoteRequest {
	anonymousId?: string;
	voterName?: string;
	voterEmail?: string;
}

export interface SubmitFeatureRequest {
	title: string;
	description: string;
	productId: string;
	authorName?: string;
	authorEmail?: string;
	anonymousId?: string;
}

export interface CommentRequest {
	content: string;
	anonymousId?: string;
	authorName?: string;
	authorEmail?: string;
}

// UI Components Props Types
export interface UIComponents {
	// Basic components
	Button: ComponentType<any>;
	Card: ComponentType<any>;
	CardContent: ComponentType<any>;
	CardHeader: ComponentType<any>;
	CardTitle: ComponentType<any>;
	Input: ComponentType<any>;
	Label: ComponentType<any>;
	Textarea: ComponentType<any>;
	Alert: ComponentType<any>;
	AlertDescription: ComponentType<any>;

	// Select components
	Select: ComponentType<any>;
	SelectContent: ComponentType<any>;
	SelectItem: ComponentType<any>;
	SelectTrigger: ComponentType<any>;
	SelectValue: ComponentType<any>;

	// Tabs components
	Tabs: ComponentType<any>;
	TabsContent: ComponentType<any>;
	TabsList: ComponentType<any>;
	TabsTrigger: ComponentType<any>;

	// Icons
	ChevronUp: ComponentType<any>;
	MessageCircle: ComponentType<any>;
	Calendar: ComponentType<any>;
	AlertCircle: ComponentType<any>;
	Loader2: ComponentType<any>;
	RefreshCw: ComponentType<any>;
	Send: ComponentType<any>;
	Lightbulb: ComponentType<any>;
	TrendingUp: ComponentType<any>;
	Plus: ComponentType<any>;

	// Utility functions
	cn: (...classes: any[]) => string;
}

export interface FeatureRequestListProps {
	productId?: string;
	showVoteCounts?: boolean;
	allowVoting?: boolean;
	allowComments?: boolean;
	className?: string;
	ui: UIComponents;
}

export interface FeatureRequestItemProps {
	featureRequest: FeatureRequest;
	showVoteCount?: boolean;
	allowVoting?: boolean;
	allowComments?: boolean;
	onVote?: (featureRequestId: string) => void;
	onUnvote?: (featureRequestId: string) => void;
	onComment?: (featureRequestId: string, comment: string) => void;
	className?: string;
	ui: UIComponents;
}

export interface SubmitFeatureFormProps {
	products: Product[];
	onSubmit: (data: SubmitFeatureRequest) => void;
	isLoading?: boolean;
	className?: string;
	ui: UIComponents;
}

export interface ProductSelectorProps {
	products: Product[];
	selectedProductId?: string;
	onProductChange: (productId: string) => void;
	className?: string;
	ui: UIComponents;
}

export interface FeatureStatusBadgeProps {
	status: FeatureStatus;
	className?: string;
	ui: UIComponents;
}

export interface VoteButtonProps {
	featureRequestId: string;
	hasVoted: boolean;
	voteCount?: number;
	showCount?: boolean;
	onVote: (featureRequestId: string) => void;
	onUnvote: (featureRequestId: string) => void;
	isLoading?: boolean;
	className?: string;
	ui: UIComponents;
}

export interface CommentSectionProps {
	featureRequestId: string;
	comments?: FeatureComment[];
	onAddComment: (featureRequestId: string, comment: string) => void;
	isLoading?: boolean;
	className?: string;
	ui: UIComponents;
}

export interface FeatureVotingPageProps {
	className?: string;
	defaultProductId?: string;
	showSubmitForm?: boolean;
	allowVoting?: boolean;
	allowComments?: boolean;
	ui: UIComponents;
}

// API Response Types
export interface ApiResponse<T> {
	success: boolean;
	data?: T;
	error?: string;
	message?: string;
}

export interface PaginatedResponse<T> {
	success: boolean;
	data: {
		items: T[];
		pagination: {
			page: number;
			limit: number;
			total: number;
			totalPages: number;
		};
	};
}

export interface FeatureRequestFilters {
	productId?: string;
	status?: FeatureStatus;
	search?: string;
	sortBy?: "createdAt" | "voteCount" | "title";
	sortOrder?: "asc" | "desc";
	page?: number;
	limit?: number;
}

// Hook Types
export interface UseFeatureVotingOptions {
	productId?: string;
	showVoteCounts?: boolean;
	autoRefresh?: boolean;
	refreshInterval?: number;
}

export interface UseFeatureVotingReturn {
	featureRequests: FeatureRequest[];
	products: Product[];
	isLoading: boolean;
	error: string | null;
	vote: (featureRequestId: string, voteData?: VoteRequest) => Promise<void>;
	unvote: (featureRequestId: string) => Promise<void>;
	submitFeature: (data: SubmitFeatureRequest) => Promise<void>;
	addComment: (
		featureRequestId: string,
		comment: CommentRequest,
	) => Promise<void>;
	refresh: () => Promise<void>;
}

// Constants
export const FEATURE_STATUS_CONFIG: Record<FeatureStatus, FeatureStatusConfig> =
	{
		under_consideration: {
			value: "under_consideration",
			label: "待考虑",
			color: "text-gray-600",
			bgColor: "bg-gray-100",
			description: "我们正在评估这个功能的可行性和优先级",
		},
		planned: {
			value: "planned",
			label: "计划中",
			color: "text-blue-600",
			bgColor: "bg-blue-100",
			description: "这个功能已被纳入开发计划",
		},
		in_progress: {
			value: "in_progress",
			label: "开发中",
			color: "text-orange-600",
			bgColor: "bg-orange-100",
			description: "我们正在积极开发这个功能",
		},
		completed: {
			value: "completed",
			label: "已完成",
			color: "text-green-600",
			bgColor: "bg-green-100",
			description: "这个功能已经完成并发布",
		},
		wont_do: {
			value: "wont_do",
			label: "不考虑",
			color: "text-red-600",
			bgColor: "bg-red-100",
			description: "经过评估，我们决定不实现这个功能",
		},
	};
