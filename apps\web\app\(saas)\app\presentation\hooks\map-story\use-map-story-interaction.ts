"use client";

import mapboxgl from "mapbox-gl";
import { useCallback } from "react";
import { StoryState } from "../../constants";
import type { FrontendTravelPoint } from "../../types";
import type { MapStoryInteraction, MapStoryNavigationContext } from "./types";
import { createLogger } from "./utils";

// 创建交互模块专用的日志记录器
const logger = createLogger("MapStoryInteraction");

export function useMapStoryInteraction(
	context: MapStoryNavigationContext,
): MapStoryInteraction {
	const { props, state, setters, timers, navigation } = context;
	const { currentPointIndex, storyState } = state;
	const { points } = props;
	const {
		setCurrentPointIndex,
		setStoryState,
		setIsPlaying,
		setSelectedImageIndex,
		setLightboxOpen,
		setIsMapReady,
		setMapInstance,
	} = setters;
	const { clearPlayTimer } = timers;
	const {
		startJourney,
		viewMemories,
		goToNextPoint,
		goToPrevPoint,
		resetPresentation,
	} = navigation;

	// 开始/暂停播放或查看回忆
	const togglePlay = useCallback(async () => {
		// 根据当前状态执行不同操作
		logger.debug("togglePlay: 切换播放状态", {
			currentState: storyState,
			currentPointIndex,
		});

		// 如果在概览状态，开始旅程
		if (storyState === StoryState.OVERVIEW) {
			logger.info("togglePlay: 从概览状态开始旅程");
			await startJourney();
			return;
		}

		// 如果在完成状态，查看回忆
		if (storyState === StoryState.COMPLETED) {
			logger.info("togglePlay: 在完成状态下查看回忆");
			viewMemories();
			return;
		}

		// 如果动画正在进行中，不做任何操作
		if (context.refs.isAnimatingRef.current) {
			logger.debug("togglePlay: 动画进行中，忽略操作");
			return;
		}

		// 切换播放状态
		setIsPlaying((prevIsPlaying) => {
			const newIsPlaying = !prevIsPlaying;

			logger.info(
				`togglePlay: 播放状态从 ${prevIsPlaying} 切换为 ${newIsPlaying}`,
				{
					currentPointIndex,
					storyState,
				},
			);

			// 如果开始播放
			if (newIsPlaying) {
				// 判断当前位置
				if (
					currentPointIndex <= 0 ||
					currentPointIndex > points.length
				) {
					// 如果在概览状态或超出点位范围，开始或重新开始
					if (currentPointIndex === 0) {
						// 如果在概览状态，开始旅程（这部分已在前面状态检查中处理）
						logger.info("togglePlay: 从概览状态开始旅程");
						startJourney();
					} else {
						// 如果超出点位范围，重置为第一个点位
						logger.info(
							"togglePlay: 超出点位范围，重置到第一个点位",
						);
						setTimeout(() => {
							context.animation.advanceToPoint(0);
						}, 0);
					}
				} else if (currentPointIndex === points.length) {
					// 如果在最后一个点位，即将完成
					logger.info("togglePlay: 在最后一个点位，开始结束动画");
					context.animation.finishPresentation();
				} else {
					// 正常点位范围内，前进到下一个点位
					logger.info(
						`togglePlay: 前进到下一个点位 (当前索引: ${currentPointIndex})`,
					);
					setTimeout(() => {
						context.animation.advanceToPoint(currentPointIndex);
					}, 0);
				}
			} else {
				// 如果暂停播放，清除计时器并设置状态为暂停
				logger.info("togglePlay: 暂停播放，清除计时器");
				clearPlayTimer();
				setStoryState(StoryState.PAUSED_ON_POINT);
			}

			return newIsPlaying;
		});
	}, [
		storyState,
		startJourney,
		viewMemories,
		currentPointIndex,
		points,
		context.refs.isAnimatingRef,
		context.animation,
		clearPlayTimer,
		setIsPlaying,
		setStoryState,
	]);

	// 处理标记点点击
	const handleMarkerClick = useCallback(
		(point: FrontendTravelPoint, index: number) => {
			logger.info("handleMarkerClick: 点击标记点", {
				pointId: point.id,
				pointTitle: point.title,
				index,
			});

			setIsPlaying(false);
			clearPlayTimer();
			setCurrentPointIndex(index + 1);
			setStoryState(StoryState.PLAYING);
		},
		[clearPlayTimer, setIsPlaying, setCurrentPointIndex, setStoryState],
	);

	// 处理图片点击，打开图片查看器
	const handleImageClick = useCallback(
		(imageSrc: string, index: number) => {
			logger.debug("handleImageClick: 打开图片查看器", { index });
			setSelectedImageIndex(index);
			setLightboxOpen(true);
		},
		[setSelectedImageIndex, setLightboxOpen],
	);

	// 图片导航
	const handlePrevImage = useCallback(() => {
		const pointIndex = currentPointIndex - 1;
		if (pointIndex < 0 || !points[pointIndex]?.images) return;

		const images = points[pointIndex].images;
		const totalImages = images.length;
		logger.debug("handlePrevImage: 切换到上一张图片", { totalImages });
		setSelectedImageIndex((prev) => (prev - 1 + totalImages) % totalImages);
	}, [currentPointIndex, points, setSelectedImageIndex]);

	const handleNextImage = useCallback(() => {
		const pointIndex = currentPointIndex - 1;
		if (pointIndex < 0 || !points[pointIndex]?.images) return;

		const images = points[pointIndex].images;
		const totalImages = images.length;
		logger.debug("handleNextImage: 切换到下一张图片", { totalImages });
		setSelectedImageIndex((prev) => (prev + 1) % totalImages);
	}, [currentPointIndex, points, setSelectedImageIndex]);

	// 处理进度条变化
	const handleSliderChange = useCallback(
		(values: number[]) => {
			logger.info("handleSliderChange: 进度条位置变化", {
				newValue: values[0],
			});
			setIsPlaying(false);
			clearPlayTimer();
			setCurrentPointIndex(values[0]);
			if (values[0] > 0) {
				setStoryState(StoryState.PLAYING);
			} else {
				setStoryState(StoryState.OVERVIEW);
			}
		},
		[clearPlayTimer, setIsPlaying, setCurrentPointIndex, setStoryState],
	);

	// 处理地图加载完成
	const handleMapLoad = useCallback(
		(map: mapboxgl.Map) => {
			logger.info("handleMapLoad: 地图加载完成", {
				pointsCount: points.length,
			});
			setMapInstance(map);
			setIsMapReady(true);

			// 在地图加载完成后设置俯视视角
			if (points.length > 0) {
				// 计算所有点位的中心
				const bounds = new mapboxgl.LngLatBounds();

				// 过滤有效坐标的点位
				const validPoints = points.filter(
					(point) =>
						point.coordinates &&
						typeof point.coordinates.lng === "number" &&
						typeof point.coordinates.lat === "number",
				);

				if (validPoints.length === 0) {
					logger.error("handleMapLoad: 没有有效坐标的点位");
					return;
				}

				validPoints.forEach((point) => {
					bounds.extend([
						point.coordinates.lng,
						point.coordinates.lat,
					]);
				});

				logger.debug("handleMapLoad: 调整地图视角以显示所有点位", {
					pointsCount: validPoints.length,
					bounds: bounds.toString(),
				});

				// 设置地图视角为俯视整个旅行路线
				map.fitBounds(bounds, {
					padding: 100,
					pitch: 0, // 平面视角
					bearing: 0, // 正北方向
					duration: 1000,
				});
			}
		},
		[points, setMapInstance, setIsMapReady],
	);

	return {
		togglePlay,
		handleMarkerClick,
		handleImageClick,
		handlePrevImage,
		handleNextImage,
		handleSliderChange,
		handleMapLoad,
	};
}
