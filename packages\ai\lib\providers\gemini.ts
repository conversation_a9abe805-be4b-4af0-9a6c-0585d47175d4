import type { <PERSON><PERSON><PERSON>ider } from "../types";

export class GeminiProvider implements AIProvider {
	private apiKey: string;
	private apiEndpoint: string;

	name = "Google Gemini";
	supportedFeatures = {
		text: true,
		image: true,
		imageAnalysis: true,
		audio: false, // 目前Gemini不提供标准的音频转录API
	};

	constructor(
		apiKey: string,
		apiEndpoint = "https://generativelanguage.googleapis.com",
	) {
		this.apiKey = apiKey;
		this.apiEndpoint = apiEndpoint;
	}

	async generateText(options: {
		systemPrompt?: string;
		userPrompt: string;
		maxTokens?: number;
		temperature?: number;
	}): Promise<string> {
		const url = `${this.apiEndpoint}/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${this.apiKey}`;

		const response = await fetch(url, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
			},
			body: JSON.stringify({
				contents: [
					{
						parts: [
							{
								text: options.systemPrompt
									? `${options.systemPrompt}\n\n${options.userPrompt}`
									: options.userPrompt,
							},
						],
					},
				],
				generationConfig: {
					temperature: options.temperature || 0.7,
					maxOutputTokens: options.maxTokens || 1000,
				},
			}),
		});

		if (!response.ok) {
			const error = await response.text();
			throw new Error(`Gemini API 错误: ${error}`);
		}

		const data = await response.json();
		return data.candidates[0]?.content?.parts[0]?.text || "";
	}

	async generateImage(prompt: string, options?: any): Promise<string> {
		const model = options?.model || "gemini-2.0-flash";
		const url = `${this.apiEndpoint}/v1/models/${model}:generateContent?key=${this.apiKey}`;

		// Gemini 目前不直接支持像DALL-E那样的图像生成
		// 我们可以使用vision模型创建图像描述，然后用其他服务生成
		// 这是一个模拟实现
		throw new Error("Image generation not directly supported by Gemini");
	}

	/**
	 * 分析图片内容并回答问题
	 * @param imageUrl 图片URL
	 * @param prompt 提示词/问题
	 * @param options 可选配置项
	 * @returns 分析结果
	 */
	async analyzeImage(
		imageUrl: string,
		prompt: string,
		options?: any,
	): Promise<string> {
		// 使用 Gemini Pro Vision 模型进行图像分析
		const model = options?.visionModel || "gemini-2.0-flash";
		const url = `${this.apiEndpoint}/v1beta/models/${model}:generateContent?key=${this.apiKey}`;

		try {
			// 从图片URL获取图片数据
			const imageResponse = await fetch(imageUrl);
			if (!imageResponse.ok) {
				throw new Error(`获取图片失败: ${imageResponse.statusText}`);
			}

			// 获取图片的MIME类型
			const contentType =
				imageResponse.headers.get("content-type") || "image/jpeg";

			// 获取图片的arrayBuffer并转换为Base64
			const arrayBuffer = await imageResponse.arrayBuffer();
			const base64Image = this.arrayBufferToBase64(arrayBuffer);

			// 构建请求体
			const requestBody = {
				contents: [
					{
						parts: [
							{
								text: options?.systemPrompt
									? `${options.systemPrompt}\n\n${prompt}`
									: prompt,
							},
							{
								inline_data: {
									mime_type: contentType,
									data: base64Image,
								},
							},
						],
					},
				],
				generationConfig: {
					temperature: options?.temperature || 0.7,
					maxOutputTokens: options?.maxTokens || 1000,
					topP: options?.topP || 0.95,
					topK: options?.topK || 40,
				},
				safetySettings: options?.safetySettings || [],
			};

			const response = await fetch(url, {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(requestBody),
			});

			if (!response.ok) {
				const error = await response.text();
				throw new Error(`Gemini 图片分析 API 错误: ${error}`);
			}

			const data = await response.json();
			return data.candidates[0].content.parts[0].text;
		} catch (error) {
			console.error("Gemini 图片分析失败:", error);
			throw error;
		}
	}

	/**
	 * 辅助方法：将ArrayBuffer转换为Base64字符串
	 * 这个方法在浏览器和Node.js环境中都可以工作
	 */
	private arrayBufferToBase64(buffer: ArrayBuffer): string {
		// 检查是否在Node.js环境中
		if (typeof Buffer !== "undefined") {
			// Node.js环境
			return Buffer.from(buffer).toString("base64");
		}

		// 浏览器环境（作为后备方案）
		let binary = "";
		const bytes = new Uint8Array(buffer);
		const len = bytes.byteLength;
		for (let i = 0; i < len; i++) {
			binary += String.fromCharCode(bytes[i]);
		}
		return btoa(binary);
	}
}
