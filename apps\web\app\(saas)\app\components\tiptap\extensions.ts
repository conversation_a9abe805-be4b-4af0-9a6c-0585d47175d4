import { Node, mergeAttributes } from "@tiptap/core";
import { ReactNodeViewRenderer } from "@tiptap/react";
import { TravelPointNodeView } from "./TravelPointNodeView";

// 自定义节点属性接口
export interface TravelPointAttributes {
	pointId: string | null;
	location: string;
	pointDate: string;
	images: string[];
}

// 创建自定义旅行点位节点
export const CustomTravelPointNode = Node.create({
	name: "travelPoint",
	group: "block",
	content: "block+",

	addOptions() {
		return {
			HTMLAttributes: {},
		};
	},

	addAttributes() {
		return {
			pointId: { default: null },
			location: {
				default: "",
			},
			pointDate: {
				default: new Date().toISOString(),
			},
			images: {
				default: [],
			},
		};
	},

	parseHTML() {
		return [
			{
				tag: "div[data-type='travel-point']",
			},
		];
	},

	renderHTML({ HTMLAttributes }) {
		return [
			"div",
			mergeAttributes(
				{ "data-type": "travel-point", class: "travel-point" },
				HTMLAttributes,
			),
			0,
		];
	},

	addNodeView() {
		return ReactNodeViewRenderer(TravelPointNodeView);
	},
});

// Placeholder for CustomTimelineNode if you plan to implement it
// export const CustomTimelineNode = Node.create({ /* ... */ });

// 导出所有自定义扩展
export * from "./extensions/TravelPointNode";
export * from "./extensions/TravelTimelineNode";
