import type { TravelPointImage } from "@repo/database/src/types/travel-diary";

// 定义旅行点位的接口
export interface TravelPoint {
	id: string;
	date: Date;
	location: string;
	description: string;
	images: TravelPointImage[];
	iconType: string;
	coordinates: { lat: number; lng: number };
}

// 地理编码结果类型
export interface GeocodingResult {
	lat: number;
	lng: number;
	formattedAddress?: string;
	placeDetails?: {
		name: string;
		vicinity?: string;
		photos?: google.maps.places.PlacePhoto[];
		types?: string[];
	};
}

// 常用的表单组件属性
export interface FormFieldProps {
	formData: Partial<TravelPoint>;
	setFormData: React.Dispatch<React.SetStateAction<Partial<TravelPoint>>>;
}

// 图标选项类型
export interface IconOption {
	id: string;
	label: string;
}

// 扩展全局window对象类型 (扩展现有的google maps声明)
declare global {
	interface Window {
		initGoogleMapsAutocomplete?: () => void;
		_pacContainerObserver?: MutationObserver;
	}
}

// 扩展HTMLInputElement以支持自动完成实例存储
declare global {
	interface HTMLInputElement {
		_autocomplete?: google.maps.places.Autocomplete;
	}
}

// 扩展Google Maps相关类型
declare global {
	namespace google {
		namespace maps {
			namespace places {
				interface Autocomplete {
					_listeners?: google.maps.MapsEventListener[];
				}
				class PlacePhoto {
					// 根据Google Maps API定义添加必要的方法
					getUrl(opts?: any): string;
				}
			}
		}
	}
}
