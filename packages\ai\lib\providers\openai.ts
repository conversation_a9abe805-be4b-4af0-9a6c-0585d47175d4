import { openai } from "@ai-sdk/openai";
import { experimental_generateImage as generateImage, generateText } from "ai";
import type { AIProvider } from "../types";

export class OpenAIProvider implements AIProvider {
	private apiKey?: string;
	private textModel: string;
	private imageModel: string;
	private audioModel: string;

	name = "OpenAI";
	supportedFeatures = {
		text: true,
		image: true,
		imageAnalysis: true,
		audio: true,
	};

	constructor(
		textModelName = "gpt-4o-mini",
		imageModelName = "dall-e-3",
		audioModelName = "whisper-1",
		apiKey?: string,
	) {
		this.apiKey = apiKey || process.env.OPENAI_API_KEY;
		this.textModel = textModelName;
		this.imageModel = imageModelName;
		this.audioModel = audioModelName;
	}

	async generateText(options: {
		systemPrompt?: string;
		userPrompt: string;
		maxTokens?: number;
		temperature?: number;
	}): Promise<string> {
		try {
			const openaiModel = openai(this.textModel);

			const { text } = await generateText({
				model: openaiModel,
				system: options.systemPrompt,
				prompt: options.userPrompt,
				maxTokens: options.maxTokens || 1000,
				temperature: options.temperature || 0.7,
			});

			return text;
		} catch (error) {
			console.error("OpenAI generateText 失败:", error);
			throw error;
		}
	}

	async generateImage(prompt: string, options?: any): Promise<string> {
		try {
			const openaiModel = openai.image(this.imageModel);

			const { image } = await generateImage({
				model: openaiModel,
				prompt,
				providerOptions: {
					openai: {
						size: options?.size || "1024x1024",
						style: options?.style || "vivid",
						quality: options?.quality || "standard",
						...options,
					},
				},
			});

			// 返回 base64 数据 URL 格式
			return `data:image/png;base64,${image.base64}`;
		} catch (error) {
			console.error("OpenAI generateImage 失败:", error);
			throw error;
		}
	}

	async transcribeAudio(
		audioData: Blob | Buffer,
		options?: any,
	): Promise<string> {
		try {
			// 使用原生OpenAI API进行音频转录
			const formData = new FormData();

			// 处理 Buffer 类型的音频数据，统一转换为 Blob
			const audioBlob: Blob =
				audioData instanceof Buffer
					? new Blob([audioData], { type: "audio/mpeg" })
					: (audioData as Blob);

			formData.append("file", audioBlob, "audio.wav");
			formData.append("model", this.audioModel);
			if (options?.language) {
				formData.append("language", options.language);
			}
			if (options?.prompt) {
				formData.append("prompt", options.prompt);
			}

			const response = await fetch(
				"https://api.openai.com/v1/audio/transcriptions",
				{
					method: "POST",
					headers: {
						Authorization: `Bearer ${this.apiKey}`,
					},
					body: formData,
				},
			);

			if (!response.ok) {
				const error = await response.text();
				throw new Error(`OpenAI 音频转录 API 错误: ${error}`);
			}

			const data = await response.json();
			return data.text || "";
		} catch (error) {
			console.error("OpenAI 音频转录失败:", error);
			throw error;
		}
	}

	/**
	 * 分析图片内容并回答问题
	 * @param imageUrl 图片URL
	 * @param prompt 提示词/问题
	 * @param options 可选配置项
	 * @returns 分析结果
	 */
	async analyzeImage(
		imageUrl: string,
		prompt: string,
		options?: any,
	): Promise<string> {
		try {
			const openaiModel = openai(options?.visionModel || "gpt-4o");

			const { text } = await generateText({
				model: openaiModel,
				system:
					options?.systemPrompt ||
					"你是一位擅长分析图像的AI助手。请基于图像内容回答问题。",
				messages: [
					{
						role: "user",
						content: [
							{
								type: "text",
								text: prompt,
							},
							{
								type: "image",
								image: imageUrl,
							},
						],
					},
				],
				maxTokens: options?.maxTokens || 1000,
				temperature: options?.temperature || 0.7,
			});

			return text;
		} catch (error) {
			console.error("OpenAI 图片分析失败:", error);
			throw error;
		}
	}
}
