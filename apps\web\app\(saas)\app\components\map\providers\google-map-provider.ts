"use client";

import { loadGoogleMapsApi } from "../../../utils/googleMaps";
import type {
	LatLng,
	MapBounds,
	MapEventHandler,
	MapEventType,
	MapService,
	MapServiceProvider,
	MapViewOptions,
	MarkerOptions,
} from "../types";

/**
 * Google地图服务实现类
 */
class GoogleMapService implements MapService {
	private map: google.maps.Map | null = null;
	private markers: Map<string, google.maps.Marker> = new Map();
	private eventListeners: Map<
		string,
		{
			event: MapEventType;
			handler: MapEventHandler;
			listener: google.maps.MapsEventListener;
		}
	> = new Map();
	private markerId = 0;

	async init(element: HTMLElement, options: MapViewOptions): Promise<void> {
		// 确保Google Maps API已加载
		await GoogleMapProvider.getInstance().load();

		// 创建地图实例
		this.map = new google.maps.Map(element, {
			center: options.center,
			zoom: options.zoom,
			minZoom: options.minZoom,
			maxZoom: options.maxZoom,
			mapTypeControl: false,
			fullscreenControl: false,
			streetViewControl: false,
			zoomControl: true,
		});
	}

	destroy(): void {
		// 清理所有标记
		this.markers.forEach((marker) => {
			marker.setMap(null);
		});
		this.markers.clear();

		// 清理所有事件监听器
		this.eventListeners.forEach(({ listener }) => {
			google.maps.event.removeListener(listener);
		});
		this.eventListeners.clear();

		// 清理地图实例
		this.map = null;
	}

	addMarker(options: MarkerOptions): string {
		if (!this.map) throw new Error("地图未初始化");

		const id = `marker_${this.markerId++}`;
		const marker = new google.maps.Marker({
			position: options.position,
			map: this.map,
			title: options.title,
			icon: options.icon,
			draggable: options.draggable,
			animation: google.maps.Animation.DROP,
		});

		// 添加点击事件
		if (options.onClick) {
			marker.addListener("click", options.onClick);
		}

		// 保存标记引用
		this.markers.set(id, marker);

		return id;
	}

	removeMarker(markerId: string): void {
		const marker = this.markers.get(markerId);
		if (marker) {
			marker.setMap(null);
			google.maps.event.clearInstanceListeners(marker);
			this.markers.delete(markerId);
		}
	}

	updateMarkerPosition(markerId: string, position: LatLng): void {
		const marker = this.markers.get(markerId);
		if (marker) {
			marker.setPosition(position);
		}
	}

	setCenter(center: LatLng): void {
		if (!this.map) throw new Error("地图未初始化");
		this.map.setCenter(center);
	}

	setZoom(zoom: number): void {
		if (!this.map) throw new Error("地图未初始化");
		this.map.setZoom(zoom);
	}

	getBounds(): MapBounds {
		if (!this.map) throw new Error("地图未初始化");

		const bounds = this.map.getBounds();
		if (!bounds) {
			// 如果边界不可用，返回默认值
			return { north: 0, south: 0, east: 0, west: 0 };
		}

		return {
			north: bounds.getNorthEast().lat(),
			south: bounds.getSouthWest().lat(),
			east: bounds.getNorthEast().lng(),
			west: bounds.getSouthWest().lng(),
		};
	}

	getCenter(): LatLng {
		if (!this.map) throw new Error("地图未初始化");
		const center = this.map.getCenter();
		if (!center) {
			throw new Error("无法获取地图中心点");
		}
		return { lat: center.lat(), lng: center.lng() };
	}

	getZoom(): number {
		if (!this.map) throw new Error("地图未初始化");
		return this.map.getZoom() || 0;
	}

	fitBounds(points: LatLng[]): void {
		if (!this.map) throw new Error("地图未初始化");
		if (points.length === 0) return;

		const bounds = new google.maps.LatLngBounds();
		points.forEach((point) => {
			bounds.extend(new google.maps.LatLng(point.lat, point.lng));
		});

		this.map.fitBounds(bounds);
	}

	addEventListener(type: MapEventType, handler: MapEventHandler): void {
		if (!this.map) throw new Error("地图未初始化");

		// 创建唯一的监听器ID
		const listenerId = `${type}_${Date.now()}`;

		// 映射事件类型到Google Maps事件
		const googleEventType = type;

		// 添加事件监听器
		const listener = this.map.addListener(googleEventType, (e: any) => {
			handler(e);
		});

		// 保存监听器引用
		this.eventListeners.set(listenerId, { event: type, handler, listener });
	}

	removeEventListener(type: MapEventType, handler: MapEventHandler): void {
		if (!this.map) return;

		// 查找并移除匹配的事件监听器
		this.eventListeners.forEach((value, key) => {
			if (value.event === type && value.handler === handler) {
				google.maps.event.removeListener(value.listener);
				this.eventListeners.delete(key);
			}
		});
	}

	getNativeMapInstance(): google.maps.Map | null {
		return this.map;
	}
}

/**
 * Google地图服务提供商 (单例模式)
 */
export class GoogleMapProvider implements MapServiceProvider {
	private static instance: GoogleMapProvider;
	private isApiLoaded = false;

	private constructor() {}

	// 获取单例实例
	static getInstance(): GoogleMapProvider {
		if (!GoogleMapProvider.instance) {
			GoogleMapProvider.instance = new GoogleMapProvider();
		}
		return GoogleMapProvider.instance;
	}

	// 加载Google Maps API
	async load(): Promise<void> {
		if (this.isApiLoaded) {
			return;
		}

		try {
			await loadGoogleMapsApi();
			this.isApiLoaded = true;
		} catch (error) {
			console.error("加载Google地图API失败:", error);
			throw error;
		}
	}

	// 创建地图服务实例
	createMapService(): MapService {
		return new GoogleMapService();
	}

	// 检查地图API是否已加载
	isLoaded(): boolean {
		return (
			this.isApiLoaded ||
			(typeof window !== "undefined" && !!window.google?.maps?.places)
		);
	}

	// 获取提供商名称
	getProviderName(): string {
		return "Google Maps";
	}
}

// 默认导出Google地图提供商实例
export default GoogleMapProvider.getInstance();
