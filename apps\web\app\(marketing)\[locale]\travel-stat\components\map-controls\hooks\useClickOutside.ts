import { useEffect } from "react";

interface UseClickOutsideProps {
	isExpanded: boolean;
	activePopover: string | null;
	controlsRef: React.RefObject<HTMLDivElement | null>;
	onClose: () => void;
}

export function useClickOutside({
	isExpanded,
	activePopover,
	controlsRef,
	onClose,
}: UseClickOutsideProps) {
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			// 检查点击的元素是否在弹窗内
			const target = event.target as Element;
			const popoverElement = document.querySelector(
				"[data-popover-content]",
			);

			if (
				controlsRef.current &&
				!controlsRef.current.contains(target) &&
				(!popoverElement || !popoverElement.contains(target))
			) {
				onClose();
			}
		};

		if (isExpanded || activePopover) {
			document.addEventListener("mousedown", handleClickOutside);
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [isExpanded, activePopover, controlsRef, onClose]);
}
