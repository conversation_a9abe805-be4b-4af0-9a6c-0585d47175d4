-- CreateTable
CREATE TABLE "video_export_tasks" (
    "id" TEXT NOT NULL,
    "diaryId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "status" TEXT NOT NULL,
    "options" JSONB NOT NULL,
    "videoUrl" TEXT,
    "errorMessage" TEXT,
    "progress" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "video_export_tasks_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "video_export_tasks_userId_idx" ON "video_export_tasks"("userId");

-- CreateIndex
CREATE INDEX "video_export_tasks_diaryId_idx" ON "video_export_tasks"("diaryId");
