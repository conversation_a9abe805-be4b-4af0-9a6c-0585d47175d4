"use client";

import { getAnonymousUser } from "@repo/utils";
import { useEffect, useState } from "react";
import type { SubmitFeatureFormProps, SubmitFeatureRequest } from "../types";
import { ProductSelector } from "./ProductSelector";

/**
 * 提交特性请求表单组件
 */
export function SubmitFeatureForm({
	products,
	onSubmit,
	isLoading = false,
	className,
	ui,
}: SubmitFeatureFormProps) {
	const {
		<PERSON>ton,
		Card,
		CardContent,
		CardHeader,
		CardTitle,
		Input,
		Label,
		Textarea,
		Send,
		cn,
	} = ui;

	const [formData, setFormData] = useState<SubmitFeatureRequest>({
		title: "",
		description: "",
		productId: "",
		authorName: "",
		authorEmail: "",
	});

	// 从匿名用户信息中预填表单
	useEffect(() => {
		if (typeof window !== "undefined") {
			const anonymousUser = getAnonymousUser();
			if (anonymousUser) {
				setFormData((prev) => ({
					...prev,
					authorName: anonymousUser.name || "",
					authorEmail: anonymousUser.email || "",
				}));
			}
		}
	}, []);

	const handleInputChange = (
		field: keyof SubmitFeatureRequest,
		value: string,
	) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (
			!formData.title.trim() ||
			!formData.description.trim() ||
			!formData.productId
		) {
			return;
		}

		try {
			await onSubmit(formData);
			// 重置表单（保留用户信息）
			setFormData((prev) => ({
				title: "",
				description: "",
				productId: "",
				authorName: prev.authorName,
				authorEmail: prev.authorEmail,
			}));
		} catch (error) {
			console.error("提交失败:", error);
		}
	};

	const isFormValid =
		formData.title.trim() &&
		formData.description.trim() &&
		formData.productId;

	return (
		<Card className={cn("w-full", className)}>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Send className="h-5 w-5" />
					提交新特性请求
				</CardTitle>
			</CardHeader>
			<CardContent>
				<form onSubmit={handleSubmit} className="space-y-4">
					{/* 产品选择 */}
					<ProductSelector
						products={products}
						selectedProductId={formData.productId}
						onProductChange={(productId) =>
							handleInputChange("productId", productId)
						}
						ui={ui}
					/>

					{/* 标题 */}
					<div className="space-y-2">
						<Label htmlFor="title">特性标题 *</Label>
						<Input
							id="title"
							type="text"
							placeholder="简洁描述您希望的新特性"
							value={formData.title}
							onChange={(e) =>
								handleInputChange("title", e.target.value)
							}
							required
						/>
					</div>

					{/* 描述 */}
					<div className="space-y-2">
						<Label htmlFor="description">详细描述 *</Label>
						<Textarea
							id="description"
							placeholder="详细描述这个特性的功能、使用场景和预期效果"
							value={formData.description}
							onChange={(e) =>
								handleInputChange("description", e.target.value)
							}
							rows={4}
							required
						/>
					</div>

					{/* 用户信息 */}
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="space-y-2">
							<Label htmlFor="authorName">您的姓名</Label>
							<Input
								id="authorName"
								type="text"
								placeholder="可选，用于显示提交者"
								value={formData.authorName}
								onChange={(e) =>
									handleInputChange(
										"authorName",
										e.target.value,
									)
								}
							/>
						</div>
						<div className="space-y-2">
							<Label htmlFor="authorEmail">您的邮箱</Label>
							<Input
								id="authorEmail"
								type="email"
								placeholder="可选，用于接收更新通知"
								value={formData.authorEmail}
								onChange={(e) =>
									handleInputChange(
										"authorEmail",
										e.target.value,
									)
								}
							/>
						</div>
					</div>

					{/* 提交按钮 */}
					<Button
						type="submit"
						disabled={!isFormValid || isLoading}
						className="w-full"
					>
						{isLoading ? (
							<>
								<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
								提交中...
							</>
						) : (
							<>
								<Send className="h-4 w-4 mr-2" />
								提交特性请求
							</>
						)}
					</Button>

					<p className="text-xs text-muted-foreground">
						*
						必填项。提交后您的请求将被公开显示，其他用户可以为其投票。
					</p>
				</form>
			</CardContent>
		</Card>
	);
}
