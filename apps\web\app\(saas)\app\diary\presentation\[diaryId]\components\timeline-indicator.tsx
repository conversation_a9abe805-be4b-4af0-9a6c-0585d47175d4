"use client";

import type { FrontendTravelDiary } from "@repo/database/src/types/travel-diary";
import { cn } from "@ui/lib/utils";
import { useEffect, useRef } from "react";

interface TimelineIndicatorProps {
	diary: FrontendTravelDiary;
	currentTimelineIndex: number;
	currentPointIndex: number;
}

export function TimelineIndicator({
	diary,
	currentTimelineIndex,
	currentPointIndex,
}: TimelineIndicatorProps) {
	const timelineRef = useRef<HTMLDivElement>(null);
	const activePointRef = useRef<HTMLButtonElement>(null);

	// 获取所有足迹点的扁平数组
	// const allFootprints = diary.timelines.flatMap(
	// 	(timeline) => timeline.points,
	// ); // 不再需要用于悬停提示

	// 当激活的点位变化时，确保它在视图中可见
	useEffect(() => {
		if (activePointRef.current && timelineRef.current) {
			// 调整滚动行为，使其更平滑地居中
			activePointRef.current.scrollIntoView({
				behavior: "smooth",
				block: "center",
				inline: "nearest",
			});
		}
	}, [currentPointIndex]);

	// 构建时间线数据
	const timelineData = diary.timelines.reduce<
		Array<{
			timelineIndex: number;
			pointIndex: number;
			title: string;
			isActive: boolean;
		}>
	>((acc, timeline, index) => {
		timeline.points.forEach((point, pointIndex) => {
			acc.push({
				timelineIndex: index,
				pointIndex,
				title: point.title,
				isActive:
					index === currentTimelineIndex &&
					pointIndex === currentPointIndex,
			});
		});
		return acc;
	}, []);

	return (
		<div
			ref={timelineRef}
			// 调整定位和样式：移除背景、模糊、固定高度和滚动条，增加左边距
			className="fixed left-10 top-1/2 transform -translate-y-1/2 z-20 flex flex-col py-4"
		>
			<div className="relative h-full flex flex-col items-center px-4">
				{/* 时间线轨道 - 调整颜色和宽度 */}
				<div className="absolute top-0 bottom-0 left-1/2 transform -translate-x-1/2 w-[1px] bg-neutral-400"></div>

				{/* 时间线标记 */}
				{timelineData.map((timeline, timelineIndex) => (
					<div
						key={`${timeline.timelineIndex}-${timeline.pointIndex}`}
						className={cn(
							"w-3 h-3 rounded-full border-2 transition-all duration-300",
							timeline.isActive
								? "bg-white border-white scale-110"
								: "bg-transparent border-white/50 hover:border-white/80",
						)}
					/>
				))}
			</div>
		</div>
	);
}
