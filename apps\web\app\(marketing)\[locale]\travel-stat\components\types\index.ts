// =============================================================================
// 统一类型导出文件
// 解决导入路径层级过深的问题，提供简洁的类型导入方式
// =============================================================================

// 大气层相关类型
export type {
	AtmosphereTheme,
	AtmosphereConfig,
} from "../atmosphere/AtmosphereSwitcher";
export {
	ATMOSPHERE_CONFIGS,
	ATMOSPHERE_NAMES,
} from "../atmosphere/AtmosphereSwitcher";

// 动画相关类型
export type {
	AnimationTheme,
	AnimationConfig,
} from "../animation/BackgroundAnimations";
export { THEME_NAMES as ANIMATION_THEME_NAMES } from "../animation/BackgroundAnimations";

// 地图样式相关类型
export type { MapStyleType } from "../map-style/MapStyleSwitcher";
export {
	MAP_STYLE_CONFIGS,
	STYLE_CATEGORIES,
} from "../map-style/MapStyleSwitcher";

// 地图投影相关类型
export type { MapProjectionType } from "../map-style/ProjectionSwitcher";
export { MAP_PROJECTION_CONFIGS } from "../map-style/ProjectionSwitcher";
export { MAP_PROJECTION_CONFIGS as PROJECTION_CONFIGS } from "../map-style/ProjectionSwitcher";

// 业务数据类型
export type { CountryData, TravelPoint } from "../../types";
export type { ColorThemeType } from "../../types/colorTypes";
export type { MarkerStyleType } from "../../types/markerTypes";
export { MARKER_STYLE_CONFIGS } from "../../types/markerTypes";

// 颜色主题相关
export { COLOR_THEMES } from "../../constants/colorThemes";

// Re-export 常用的 React 类型
export type { ReactNode, RefObject } from "react";

// Mapbox 相关类型 (如果需要的话)
declare global {
	namespace mapboxgl {
		interface Map {}
	}
}
