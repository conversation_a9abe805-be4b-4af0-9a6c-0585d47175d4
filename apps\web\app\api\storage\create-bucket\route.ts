import { createStorageProviderFromEnv } from "@repo/storage";
import type { StorageProviderType } from "@repo/storage";
import { type NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
	try {
		const requestData = await request.json();
		const { provider, bucket, isPublic, corsConfig, region } = requestData;

		if (!bucket) {
			return NextResponse.json(
				{ error: "缺少必要参数 bucket" },
				{ status: 400 },
			);
		}

		// 创建存储提供商实例
		let storageProvider: any;

		try {
			// 如果指定了provider，使用指定的provider
			if (provider) {
				storageProvider = createStorageProviderFromEnv(
					provider as StorageProviderType,
				);
			} else {
				// 否则尝试从环境变量中检测默认提供商
				storageProvider = createStorageProviderFromEnv();
			}
		} catch (error) {
			console.error("创建存储提供商失败:", error);
			return NextResponse.json(
				{
					error:
						error instanceof Error
							? error.message
							: "无法创建存储提供商",
				},
				{ status: 500 },
			);
		}

		// 创建存储桶
		await storageProvider.createBucket(bucket, {
			public: isPublic,
		});

		return NextResponse.json({ success: true, bucket });
	} catch (error) {
		console.error("创建存储桶错误:", error);
		return NextResponse.json(
			{
				error:
					error instanceof Error ? error.message : "创建存储桶失败",
			},
			{ status: 500 },
		);
	}
}
