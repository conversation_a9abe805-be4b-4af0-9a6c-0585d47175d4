"use client";

import { useMap } from "../../contexts/MapContext";
import { ClassicMarker } from "./ClassicMarker";
import { EmojiMarker } from "./EmojiMarker";
import { GradientPulseMarker } from "./GradientPulseMarker";
import { HandDrawnMarker } from "./HandDrawnMarker";
import { ParticleEffectMarker } from "./ParticleEffectMarker";
import { PolaroidMarker } from "./PolaroidMarker";
import type {
	BaseMarkerProps,
	GradientPulseTheme,
	HandDrawnTheme,
	MarkerStyleType,
	PolaroidTheme,
} from "./types";
import { MARKER_STYLES } from "./types";

interface MarkerFactoryProps extends BaseMarkerProps {
	style: MarkerStyleType;
	theme?: string;
	customProps?: Record<string, any>;
	mapZoom?: number;
}

export function MarkerFactory({
	style,
	theme,
	customProps = {},
	mapZoom,
	...baseProps
}: MarkerFactoryProps) {
	// 获取统一tooltip控制函数
	const { showPolaroidTooltip } = useMap();
	switch (style) {
		case MARKER_STYLES.CLASSIC:
			return (
				<ClassicMarker
					{...baseProps}
					color={customProps.color}
					hideOutline={customProps.hideOutline}
				/>
			);

		case MARKER_STYLES.GRADIENT_PULSE:
			return (
				<GradientPulseMarker
					{...baseProps}
					theme={theme as GradientPulseTheme}
					customConfig={customProps.customConfig}
					hideOutline={customProps.hideOutline}
				/>
			);

		// 预留其他风格的实现位置
		case MARKER_STYLES.NEON_GLOW:
			// TODO: 实现霓虹发光效果
			return (
				<ClassicMarker
					{...baseProps}
					color="rgb(147, 51, 234)" // purple-600
				/>
			);

		case MARKER_STYLES.PARTICLE_EFFECT:
			return (
				<ParticleEffectMarker
					{...baseProps}
					theme={theme || "fire"}
					particleColor={customProps.particleColor}
					particleCount={customProps.particleCount}
					hideOutline={customProps.hideOutline}
				/>
			);

		case MARKER_STYLES.HAND_DRAWN:
			return (
				<HandDrawnMarker
					{...baseProps}
					theme={theme as HandDrawnTheme}
					customRoughness={customProps.customRoughness}
					hideOutline={customProps.hideOutline}
				/>
			);

		case MARKER_STYLES.EMOJI:
			return (
				<EmojiMarker
					{...baseProps}
					emoji={customProps.emoji || "📍"}
					backgroundColor={
						customProps.backgroundColor || "transparent"
					}
					size={customProps.size || "medium"}
					hideOutline={customProps.hideOutline}
				/>
			);

		case MARKER_STYLES.POLAROID:
			return (
				<PolaroidMarker
					{...baseProps}
					theme={theme as PolaroidTheme}
					customConfig={customProps.customConfig}
					hideOutline={customProps.hideOutline}
					size={customProps.size || "medium"}
					showDescription={customProps.showDescription !== false}
					maxDescriptionLength={
						customProps.maxDescriptionLength || 20
					}
					onPhotoClick={customProps.onPhotoClick}
					onMarkerClick={
						customProps.onMarkerClick || showPolaroidTooltip
					}
					mapZoom={mapZoom}
					isVisible={customProps.isVisible}
					priority={customProps.priority || "medium"}
				/>
			);

		case MARKER_STYLES.CONSTELLATION:
			// TODO: 实现星座连线
			return (
				<ClassicMarker
					{...baseProps}
					color="rgb(234, 179, 8)" // yellow-500
				/>
			);

		case MARKER_STYLES.SEASONAL:
			// TODO: 实现季节主题
			return (
				<ClassicMarker
					{...baseProps}
					color="rgb(34, 197, 94)" // green-500
				/>
			);

		case MARKER_STYLES.ARTISTIC:
			// TODO: 实现艺术风格
			return (
				<ClassicMarker
					{...baseProps}
					color="rgb(168, 85, 247)" // purple-500
				/>
			);

		case MARKER_STYLES.BADGE:
			// TODO: 实现成就徽章
			return (
				<ClassicMarker
					{...baseProps}
					color="rgb(239, 68, 68)" // red-500
				/>
			);

		default:
			// 默认使用经典风格
			return <ClassicMarker {...baseProps} />;
	}
}
