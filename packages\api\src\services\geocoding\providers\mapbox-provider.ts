/**
 * Mapbox地理编码提供商
 */

import { BaseGeocodingProvider } from "../base-provider";
import type {
	GeocodingOptions,
	GeocodingProvider,
	UnifiedGeocodingResult,
} from "../types";

/**
 * Mapbox地理编码提供商
 */
export class MapboxProvider extends BaseGeocodingProvider {
	private readonly baseUrl =
		"https://api.mapbox.com/geocoding/v5/mapbox.places";

	getProviderType(): GeocodingProvider {
		return "mapbox";
	}

	protected async performGeocode(
		address: string,
		options: GeocodingOptions,
	): Promise<UnifiedGeocodingResult | null> {
		const encodedAddress = encodeURIComponent(address);
		const url = `${this.baseUrl}/${encodedAddress}.json`;

		const params = new URLSearchParams({
			access_token: this.config.apiKey,
		});

		// 添加可选参数
		if (options.limit) {
			params.set("limit", options.limit.toString());
		}
		if (options.country) {
			params.set("country", options.country);
		}
		if (options.language) {
			params.set("language", options.language);
		}
		if (options.types) {
			params.set("types", options.types.join(","));
		}

		const response = await this.makeRequest(`${url}?${params}`);

		if (!response.success || !response.rawData) {
			return null;
		}

		const data = response.rawData;

		if (data.features && data.features.length > 0) {
			const feature = data.features[0];
			const geometry = feature.geometry;
			const properties = feature.properties;
			const context = feature.context || [];

			return {
				longitude: geometry.coordinates[0],
				latitude: geometry.coordinates[1],
				formattedAddress: feature.place_name,
				addressComponents: this.parseMapboxContext(context, properties),
				confidence: this.mapMapboxRelevance(feature.relevance),
				placeTypes: feature.place_type || [],
				viewport: feature.bbox
					? {
							southwest: {
								lat: feature.bbox[1],
								lng: feature.bbox[0],
							},
							northeast: {
								lat: feature.bbox[3],
								lng: feature.bbox[2],
							},
						}
					: undefined,
				placeId: feature.id,
				provider: "mapbox",
				responseTime: response.responseTime,
			};
		}

		return null;
	}

	protected async performReverseGeocode(
		longitude: number,
		latitude: number,
		options: GeocodingOptions,
	): Promise<UnifiedGeocodingResult | null> {
		const url = `${this.baseUrl}/${longitude},${latitude}.json`;

		const params = new URLSearchParams({
			access_token: this.config.apiKey,
		});

		// 添加可选参数
		if (options.language) {
			params.set("language", options.language);
		}
		if (options.types) {
			params.set("types", options.types.join(","));
		}

		const response = await this.makeRequest(`${url}?${params}`);

		if (!response.success || !response.rawData) {
			return null;
		}

		const data = response.rawData;

		if (data.features && data.features.length > 0) {
			const feature = data.features[0];
			const properties = feature.properties;
			const context = feature.context || [];

			return {
				longitude,
				latitude,
				formattedAddress: feature.place_name,
				addressComponents: this.parseMapboxContext(context, properties),
				confidence: this.mapMapboxRelevance(feature.relevance),
				placeTypes: feature.place_type || [],
				placeId: feature.id,
				provider: "mapbox",
				responseTime: response.responseTime,
			};
		}

		return null;
	}

	/**
	 * 解析Mapbox的上下文信息
	 */
	private parseMapboxContext(
		context: any[],
		properties: any,
	): {
		country?: string;
		countryCode?: string;
		province?: string;
		city?: string;
		district?: string;
		street?: string;
		streetNumber?: string;
		postalCode?: string;
	} {
		const result: any = {};

		// 从properties中获取地址信息
		if (properties.address) {
			result.streetNumber = properties.address;
		}

		// 从context中解析地址组件
		for (const item of context) {
			const id = item.id;

			if (id.startsWith("country")) {
				result.country = item.text;
				result.countryCode = item.short_code;
			} else if (id.startsWith("region")) {
				result.province = item.text;
			} else if (id.startsWith("place")) {
				result.city = item.text;
			} else if (id.startsWith("district")) {
				result.district = item.text;
			} else if (id.startsWith("postcode")) {
				result.postalCode = item.text;
			}
		}

		return result;
	}

	/**
	 * 映射Mapbox的相关性到置信度
	 */
	private mapMapboxRelevance(relevance?: number): "high" | "medium" | "low" {
		if (!relevance) return "medium";

		if (relevance >= 0.8) return "high";
		if (relevance >= 0.5) return "medium";
		return "low";
	}
}
