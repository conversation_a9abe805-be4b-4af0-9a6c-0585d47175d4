"use client";

import { But<PERSON> } from "@ui/components/button";
import Link from "next/link";

interface ErrorPageProps {
	message: string;
}

export function ErrorPage({ message }: ErrorPageProps) {
	return (
		<div className="flex flex-col items-center justify-center h-screen p-4">
			<div className="text-destructive text-5xl mb-4">😕</div>
			<h1 className="text-2xl font-semibold mb-2">出错了</h1>
			<p className="text-muted-foreground mb-6 text-center">{message}</p>

			<div className="flex space-x-2">
				<Button asChild variant="outline">
					<Link href="/app">返回旅行记忆</Link>
				</Button>

				<Button>
					<Link href="/app/diary">浏览日记</Link>
				</Button>
			</div>
		</div>
	);
}
