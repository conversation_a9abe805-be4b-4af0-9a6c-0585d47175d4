"use client";

import React from "react";
import type { FeatureRequest } from "../types";
import { FEATURE_STATUS_COLORS, FEATURE_STATUS_LABELS } from "../types";
import { VoteButton } from "./VoteButton";

interface FeatureRequestCardProps {
	featureRequest: FeatureRequest;
	onVote: (featureRequestId: string) => Promise<void>;
	onUnvote: (featureRequestId: string) => Promise<void>;
	isVoting?: boolean;
	showVoteCount?: boolean;
	showProduct?: boolean;
	showAuthor?: boolean;
	showDescription?: boolean;
	onClick?: (featureRequest: FeatureRequest) => void;
	className?: string;
}

export function FeatureRequestCard({
	featureRequest,
	onVote,
	onUnvote,
	isVoting = false,
	showVoteCount = true,
	showProduct = true,
	showAuthor = true,
	showDescription = true,
	onClick,
	className = "",
}: FeatureRequestCardProps) {
	const {
		title,
		description,
		status,
		createdAt,
		product,
		user,
		authorName,
		commentCount,
	} = featureRequest;

	// 状态颜色映射
	const getStatusColorClasses = (status: string) => {
		const colorMap: Record<string, string> = {
			yellow: "bg-yellow-100 text-yellow-800 border-yellow-200",
			blue: "bg-blue-100 text-blue-800 border-blue-200",
			orange: "bg-orange-100 text-orange-800 border-orange-200",
			green: "bg-green-100 text-green-800 border-green-200",
			gray: "bg-gray-100 text-gray-800 border-gray-200",
		};

		const color =
			FEATURE_STATUS_COLORS[
				status as keyof typeof FEATURE_STATUS_COLORS
			] || "gray";
		return colorMap[color] || colorMap.gray;
	};

	const authorDisplayName = user?.name || authorName || "匿名用户";
	const formatDate = (date: Date) => {
		return new Date(date).toLocaleDateString("zh-CN", {
			year: "numeric",
			month: "short",
			day: "numeric",
		});
	};

	const handleCardClick = () => {
		if (onClick) {
			onClick(featureRequest);
		}
	};

	const handleCardKeyDown = (e: React.KeyboardEvent) => {
		if ((e.key === "Enter" || e.key === " ") && onClick) {
			e.preventDefault();
			onClick(featureRequest);
		}
	};

	const handleVoteAreaKeyDown = (e: React.KeyboardEvent) => {
		// 阻止事件冒泡到卡片
		e.stopPropagation();
	};

	const cardClasses = [
		"bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200",
		onClick ? "cursor-pointer hover:border-gray-300" : "",
		className,
	].join(" ");

	return (
		<div
			className={cardClasses}
			onClick={handleCardClick}
			onKeyDown={handleCardKeyDown}
			role={onClick ? "button" : undefined}
			tabIndex={onClick ? 0 : undefined}
		>
			{/* 卡片头部 */}
			<div className="flex items-start justify-between mb-3">
				<div className="flex-1 min-w-0">
					<h3 className="text-lg font-semibold text-gray-900 truncate mb-1">
						{title}
					</h3>

					<div className="flex flex-wrap items-center gap-2 text-sm text-gray-500">
						{/* 状态标签 */}
						<span
							className={`inline-flex px-2 py-1 rounded-full text-xs font-medium border ${getStatusColorClasses(status)}`}
						>
							{FEATURE_STATUS_LABELS[
								status as keyof typeof FEATURE_STATUS_LABELS
							] || status}
						</span>

						{/* 产品信息 */}
						{showProduct && product && (
							<>
								<span>•</span>
								<span className="font-medium text-gray-700">
									{product.name}
								</span>
							</>
						)}

						{/* 创建时间 */}
						<span>•</span>
						<time dateTime={createdAt.toISOString()}>
							{formatDate(createdAt)}
						</time>
					</div>
				</div>

				{/* 投票按钮 */}
				<div
					className="ml-4 flex-shrink-0"
					onClick={(e) => e.stopPropagation()}
					onKeyDown={handleVoteAreaKeyDown}
				>
					<VoteButton
						featureRequest={featureRequest}
						onVote={onVote}
						onUnvote={onUnvote}
						isVoting={isVoting}
						showVoteCount={showVoteCount}
						size="sm"
						variant="outline"
					/>
				</div>
			</div>

			{/* 描述内容 */}
			{showDescription && description && (
				<div className="mb-4">
					<p className="text-gray-700 leading-relaxed line-clamp-3">
						{description}
					</p>
				</div>
			)}

			{/* 卡片底部 */}
			<div className="flex items-center justify-between text-sm text-gray-500">
				{/* 作者信息 */}
				{showAuthor && (
					<div className="flex items-center gap-2">
						<div className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center">
							<span className="text-xs font-medium text-gray-600">
								{authorDisplayName.charAt(0).toUpperCase()}
							</span>
						</div>
						<span>由 {authorDisplayName} 提交</span>
					</div>
				)}

				{/* 评论数量 */}
				{commentCount !== undefined && commentCount > 0 && (
					<div className="flex items-center gap-1">
						<svg
							className="w-4 h-4"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
							xmlns="http://www.w3.org/2000/svg"
							aria-label="评论图标"
						>
							<title>评论</title>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
							/>
						</svg>
						<span>{commentCount} 条评论</span>
					</div>
				)}
			</div>
		</div>
	);
}
