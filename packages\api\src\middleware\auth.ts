import { type Session, auth } from "@repo/auth";
import { createMiddleware } from "hono/factory";

// 长且复杂的导出令牌，用于视频导出时绕过认证
// 在生产环境中，建议将此值存储在环境变量中而不是硬编码
export const EXPORT_AUTH_TOKEN = "j8Kp4Xz2Q9vYfTs7B3e5W6gA1hR0dNmVlCuMoIpLxZyE";

export const authMiddleware = createMiddleware<{
	Variables: {
		session: Session["session"];
		user: Session["user"];
	};
}>(async (c, next) => {
	// 检查是否是视频导出请求
	const isExportMode = c.req.query("export") === "video";
	const exportToken = c.req.query("exportToken");

	// 如果是导出模式且提供了正确的令牌，则跳过认证
	if (isExportMode && exportToken === EXPORT_AUTH_TOKEN) {
		// 创建一个模拟的用户会话，权限足够访问所需资源
		// 通常这个用户应该是系统管理员或特殊服务账户
		// c.set("session", {
		// 	expires: new Date(Date.now() + 3600000).toISOString(),
		// 	fresh: false,
		// });

		// 设置一个系统用户
		// c.set("user", {
		// 	id: "system-video-export",
		// 	email: "<EMAIL>",
		// 	emailVerified: true,
		// 	name: "System Video Export",
		// 	role: "system",
		// });

		// 记录日志，便于审计
		console.log("视频导出认证绕过已激活", { path: c.req.path });

		await next();
		return;
	}

	// 常规认证流程
	const session = await auth.api.getSession({
		headers: c.req.raw.headers,
	});

	if (!session) {
		return c.json({ error: "Unauthorized" }, 401);
	}

	c.set("session", session.session);
	c.set("user", session.user);

	await next();
});
