"use client";

import type mapboxgl from "mapbox-gl";
import { LngLatBounds } from "mapbox-gl";
import {
	type RefObject,
	useCallback,
	useEffect,
	useMemo,
	useRef,
	useState,
} from "react"; // 添加RefObject
import {
	FullscreenControl,
	GeolocateControl,
	Layer,
	type MapLayerMouseEvent,
	type MapboxEvent,
	Marker,
	NavigationControl,
	ScaleControl,
	Source,
	type ViewState,
	type ViewStateChangeEvent,
} from "react-map-gl";
// TODO: Move THEME_COLORS to a shared constants file or pass via props/context
import {
	ANIMATION_DURATION,
	CAMERA_SETTINGS,
	THEME_COLORS,
} from "../constants"; // Import from MapStoryPage
import type { FrontendTravelPoint } from "../types";
import "mapbox-gl/dist/mapbox-gl.css";
import MapGL from "react-map-gl"; // Rename to avoid conflict with global Map
import { createLogger } from "../hooks/map-story/utils";

// 创建地图组件专用的日志记录器
const logger = createLogger("MapComponent");

// Marker Component
function CustomMarker({
	point,
	index,
	isActive,
	isVisited,
	isBlinking,
	isTarget,
	onClick,
	markerRef,
	isOverviewMode,
	isCompletedMode,
}: {
	point: FrontendTravelPoint;
	index: number;
	isActive: boolean;
	isVisited?: boolean;
	isBlinking?: boolean;
	isTarget?: boolean; // 新增：是否为目标点位（正在飞向的点位）
	onClick?: () => void;
	markerRef?: RefObject<HTMLDivElement | null>;
	isOverviewMode?: boolean;
	isCompletedMode?: boolean;
}) {
	// 根据状态获取大头针颜色和大小
	const getPinStyle = () => {
		// 完成状态 - 所有点位都显示为绿色
		if (isCompletedMode) {
			return {
				color: THEME_COLORS.secondary, // 绿色
				pinSize: isActive ? "w-9 h-9" : "w-7 h-7", // 当前点位略大
				labelSize: isActive ? "text-sm" : "text-xs",
				animation: "",
				zIndex: isActive ? 10 : 5,
			};
		}

		// 目标点位 - 红色 + 脉冲动画（正在飞向的点位）
		if (isTarget) {
			return {
				color: "#EF4444", // 红色
				pinSize: "w-10 h-10", // 更大，比普通active点位更大
				labelSize: "text-base", // 更大的文字
				animation: "pulse-animation",
				zIndex: 20, // 更高的优先级，确保在最前面
			};
		}

		// 活动/当前点位 - 使用绿色
		if (isActive) {
			return {
				color: THEME_COLORS.secondary, // 绿色
				pinSize: "w-9 h-9", // 较大
				labelSize: "text-sm",
				animation: "pulse-animation",
				zIndex: 10,
			};
		}

		// 已访问点位 - 绿色（已播放过的点位）
		if (isVisited) {
			return {
				color: THEME_COLORS.secondary, // 绿色 - 已播放过的点位
				pinSize: "w-7 h-7", // 中等
				labelSize: "text-xs",
				animation: "",
				zIndex: 5,
			};
		}

		// 闪烁点位 - 使用蓝色
		if (isBlinking) {
			return {
				color: THEME_COLORS.primary, // 蓝色
				pinSize: "w-8 h-8", // 中等
				labelSize: "text-xs",
				animation: "blink-animation",
				zIndex: 7,
			};
		}

		// 默认/未访问点位 - 浅灰色
		return {
			color: "#9CA3AF", // 浅灰色
			pinSize: "w-6 h-6", // 较小
			labelSize: "text-xs",
			animation: "",
			zIndex: 1,
		};
	};

	const style = getPinStyle();

	// 在概览模式或完成模式下显示所有点位名称
	const shouldShowLabel = isOverviewMode || isCompletedMode || isActive;

	// 获取位置显示文本 - 适应FrontendTravelPoint的字段
	const locationText = point.address || point.title;

	return (
		<Marker
			longitude={point.coordinates.lng}
			latitude={point.coordinates.lat}
			anchor="bottom"
			onClick={onClick}
			style={{ zIndex: style.zIndex }}
		>
			<div
				className="flex flex-col items-center relative"
				style={{
					zIndex: style.zIndex,
					position: "relative",
				}}
			>
				{/* 位置名称 - 仅在特定情况下显示 */}
				{shouldShowLabel && (
					<div className="bg-white/80 backdrop-blur-sm px-2 py-1 rounded-md shadow-md mb-1 text-xs font-medium text-slate-800 whitespace-nowrap max-w-[150px] truncate">
						{locationText}
					</div>
				)}

				{/* 大头针样式 */}
				<div className="relative">
					{/* 目标点位的光环效果 */}
					{isTarget && (
						<div
							className="absolute inset-0 rounded-full animate-ping"
							style={{
								backgroundColor: style.color,
								opacity: 0.3,
								transform: "scale(1.5)",
								zIndex: style.zIndex - 1,
							}}
						/>
					)}
					<div
						ref={isActive && markerRef ? markerRef : undefined}
						className={`${style.pinSize} rounded-full border-2 border-white ${isTarget ? "shadow-2xl" : "shadow-md"} flex items-center justify-center ${style.animation}`}
						style={{
							backgroundColor: style.color,
							position: "relative",
							zIndex: style.zIndex,
							...(isTarget && {
								boxShadow: `0 0 20px ${style.color}40, 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)`,
							}),
						}}
					>
						<span
							className={`${style.labelSize} font-bold text-white`}
						>
							{index + 1}
						</span>
					</div>
					<div
						className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent"
						style={{
							height: "10px",
							borderTopColor: style.color,
							zIndex: style.zIndex,
						}}
					/>
				</div>
			</div>
		</Marker>
	);
}

// Props for the MapComponent
interface MapComponentProps {
	mapboxToken: string;
	initialViewState?: Partial<ViewState>; // Make optional if fitting to points
	mapStyle?: string;
	points: FrontendTravelPoint[];
	currentPointIndex: number; // Index of the currently active point
	// routeGeoJson?: any; // GeoJSON for the route line (optional, can be generated internally)
	interactiveLayerIds?: string[];
	// Callbacks
	onMapLoad?: (map: mapboxgl.Map) => void;
	onMapMove?: (evt: ViewStateChangeEvent) => void;
	onMarkerClick?: (point: FrontendTravelPoint, index: number) => void;
	onClick?: (event: MapLayerMouseEvent) => void;
	onPointArrival?: (point: FrontendTravelPoint) => void; // Callback when camera arrives at a point
	// Animation control
	flyToPointIndex?: number | null; // Prop to trigger flying to a specific point index
	fitBoundsToPoints?: boolean; // Prop to trigger fitting bounds
	// 标记点引用 - 修改为接受可为null的引用
	activeMarkerRef?: RefObject<HTMLDivElement | null>;
	// 闪烁点位索引数组
	blinkingPoints?: number[];
	// 模式控制
	showOverviewMode?: boolean;
	showCompletedMode?: boolean;
	// 控件控制
	hideControls?: boolean; // 新增：是否隐藏地图控件
}

// Helper: Easing function
const easeInOutCubic = (t: number) => {
	return t < 0.5 ? 4 * t * t * t : 1 - (-2 * t + 2) ** 3 / 2;
};

// 新增：计算两个地理坐标点之间的方位角
function calculateBearing(
	startLat: number,
	startLng: number,
	endLat: number,
	endLng: number,
): number {
	const toRadians = (degrees: number) => (degrees * Math.PI) / 180;
	const toDegrees = (radians: number) => (radians * 180) / Math.PI;

	const lat1 = toRadians(startLat);
	const lon1 = toRadians(startLng);
	const lat2 = toRadians(endLat);
	const lon2 = toRadians(endLng);

	const deltaLon = lon2 - lon1;

	const y = Math.sin(deltaLon) * Math.cos(lat2);
	const x =
		Math.cos(lat1) * Math.sin(lat2) -
		Math.sin(lat1) * Math.cos(lat2) * Math.cos(deltaLon);

	let bearing = Math.atan2(y, x);
	bearing = toDegrees(bearing);
	return (bearing + 360) % 360; // 归一化到 0-360 度
}

// 将地图flyTo操作转换为Promise
function flyToPromise(
	map: mapboxgl.Map,
	options: mapboxgl.FlyToOptions,
): Promise<void> {
	return new Promise((resolve) => {
		map.flyTo(options);
		map.once("moveend", () => {
			// 在moveend之后，再等待idle事件
			map.once("idle", () => resolve());
		});
	});
}

// 将fitBounds操作转换为Promise
function fitBoundsPromise(
	map: mapboxgl.Map,
	bounds: mapboxgl.LngLatBounds,
	options?: mapboxgl.FitBoundsOptions,
): Promise<void> {
	return new Promise((resolve) => {
		map.fitBounds(bounds, options);
		map.once("moveend", () => {
			// 在moveend之后，再等待idle事件
			map.once("idle", () => resolve());
		});
	});
}

export function MapComponent({
	mapboxToken,
	initialViewState,
	mapStyle = "mapbox://styles/mapbox/streets-v12", // Default style
	points,
	currentPointIndex,
	// routeGeoJson, // Removed, will generate internally if needed
	interactiveLayerIds,
	onMapLoad,
	onMapMove,
	onMarkerClick,
	onClick,
	onPointArrival,
	flyToPointIndex,
	fitBoundsToPoints,
	activeMarkerRef,
	blinkingPoints,
	showOverviewMode,
	showCompletedMode,
	hideControls,
}: MapComponentProps) {
	const mapRef = useRef<mapboxgl.Map | null>(null);
	const [isMapReady, setIsMapReady] = useState(false);
	// 添加一个ref来追踪已处理过的flyToPointIndex值，避免重复处理
	const processedFlyToIndexRef = useRef<number | null>(null);
	const [routeProgress, setRouteProgress] = useState(0); // 路线绘制进度 (0-1)
	const [forceRouteUpdate, setForceRouteUpdate] = useState(0); // 强制路径更新的计数器

	// 为目标点位添加CSS动画样式
	useEffect(() => {
		const style = document.createElement("style");
		style.textContent = `
			.pulse-animation {
				animation: pulse 4s ease-in-out infinite;
			}
			.blink-animation {
				animation: blink 1s ease-in-out infinite alternate;
			}
			@keyframes pulse {
				0%, 100% {
					opacity: 1;
					transform: scale(1);
				}
				50% {
					opacity: .9;
					transform: scale(1.03);
				}
			}
			@keyframes blink {
				0% {
					opacity: 0.4;
				}
				100% {
					opacity: 1;
				}
			}
		`;
		document.head.appendChild(style);

		return () => {
			document.head.removeChild(style);
		};
	}, []);

	// --- Map Interaction Logic from MapStoryPage ---

	// Fit map to points - 修改为返回Promise
	const fitMapToPoints = useCallback(
		async (
			map: mapboxgl.Map | null,
			pointsToFit: FrontendTravelPoint[],
		): Promise<void> => {
			if (!map || pointsToFit.length === 0) return Promise.resolve();

			// 过滤有效坐标的点位并统一按时间排序
			const validPoints = pointsToFit
				.filter(
					(p) =>
						p.coordinates &&
						typeof p.coordinates.lng === "number" &&
						typeof p.coordinates.lat === "number",
				)
				.sort((a, b) => {
					// 统一按时间戳排序，确保所有场景下路径连接正确
					const timeA = a.timestamp
						? new Date(a.timestamp).getTime()
						: 0;
					const timeB = b.timestamp
						? new Date(b.timestamp).getTime()
						: 0;
					return timeA - timeB;
				});

			// 如果没有有效点位，则返回
			if (validPoints.length === 0) {
				console.error("没有有效坐标的点位，无法调整地图视角");
				return Promise.resolve();
			}

			const bounds = new LngLatBounds();
			validPoints.forEach((point) => {
				bounds.extend([point.coordinates.lng, point.coordinates.lat]);
			});

			// 根据模式调整 padding
			const paddingConfig = showCompletedMode
				? {
						// 回顾页面模式：使用较小的 padding 确保所有点位可见
						padding: {
							top: 80,
							bottom: 120,
							left: 60,
							right: 60,
						},
						maxZoom: 16, // 限制最大缩放
					}
				: {
						// 普通模式：使用统一的 padding
						padding: 100,
					};

			return fitBoundsPromise(map, bounds, {
				...paddingConfig,
				duration: ANIMATION_DURATION.overviewFit,
				essential: true,
			}).then(() => {
				// 在fitBounds完成后强制更新路径
				setForceRouteUpdate((prev) => prev + 1);
			});
		},
		[showCompletedMode],
	);

	// Animate camera along route using flyTo - 修改为返回Promise
	const animateCameraToPoint = useCallback(
		async (
			map: mapboxgl.Map | null,
			fromPoint: FrontendTravelPoint,
			toPoint: FrontendTravelPoint,
		): Promise<void> => {
			if (!map || !fromPoint?.coordinates || !toPoint?.coordinates) {
				logger.warn("animateCameraToPoint: 无效的参数", {
					mapExists: !!map,
					fromPoint,
					toPoint,
				});
				return Promise.resolve();
			}

			const fromCoords = fromPoint.coordinates;
			const toCoords = toPoint.coordinates;

			// 在准备阶段开始时重置路径进度，防止路径在准备阶段意外显示
			setRouteProgress(0);

			// --- 1. 准备阶段 (Setup Flight) ---
			// 获取当前相机中心，用于计算包含当前视角、起点和终点的概览区域
			const currentCenter = map.getCenter();
			const pathBounds = new LngLatBounds();

			// 添加当前相机中心到边界计算中，确保过渡平滑
			pathBounds.extend([currentCenter.lng, currentCenter.lat]);
			pathBounds.extend([fromCoords.lng, fromCoords.lat]);
			pathBounds.extend([toCoords.lng, toCoords.lat]);

			const cameraForPath = map.cameraForBounds(pathBounds, {
				padding: map.getCanvas().width > 768 ? 100 : 50, // 增加padding确保两个点都清晰可见
			});

			// cameraForPath.zoom 应该是可用的
			const fixedZoomForPathAnimation =
				cameraForPath?.zoom !== undefined
					? Math.max(
							cameraForPath.zoom,
							CAMERA_SETTINGS.pathAnimationMinZoom,
						)
					: CAMERA_SETTINGS.pathAnimationMinZoom;

			// pitch 不直接从 cameraForBounds 获取，使用预设值
			const fixedPitchForPathAnimation =
				CAMERA_SETTINGS.pathAnimationDefaultPitch;

			// 计算从起点到终点的方位角，用于路径动画
			const initialPathBearing = calculateBearing(
				fromCoords.lat,
				fromCoords.lng,
				toCoords.lat,
				toCoords.lng,
			);

			logger.info("准备阶段: 调整视角以展示路径概览", {
				currentCenter: [currentCenter.lng, currentCenter.lat],
				fromPoint: [fromCoords.lng, fromCoords.lat],
				toPoint: [toCoords.lng, toCoords.lat],
				targetZoom: fixedZoomForPathAnimation,
				targetPitch: fixedPitchForPathAnimation,
				pathBearing: initialPathBearing,
			});

			// 使用 fitBounds 而不是 flyTo，这样可以平滑地从当前视角调整到概览视角
			await fitBoundsPromise(map, pathBounds, {
				padding: map.getCanvas().width > 768 ? 100 : 50,
				pitch: fixedPitchForPathAnimation, // 设置目标俯仰角
				duration: ANIMATION_DURATION.setupFlightToPathStart,
				essential: true,
			});
			logger.info("准备阶段完成，概览视角已设置，可以看到起点和终点。");

			// --- 2. 路径动画阶段 (Path Animation) ---
			logger.info(
				`路径动画: 从 "${fromPoint.address || fromPoint.title}" 到 "${toPoint.address || toPoint.title}"`,
			);

			const targetBearingForPath = initialPathBearing;

			const duration = ANIMATION_DURATION.pointTransition;
			const steps = 60;
			const interval = duration / steps;

			await new Promise<void>((resolveAnimation) => {
				setRouteProgress(0);
				let currentStep = 0;
				const animatePath = () => {
					if (currentStep >= steps || !map) {
						setRouteProgress(1);
						map?.once("idle", () => resolveAnimation());
						return;
					}

					const progress = easeInOutCubic(currentStep / steps);
					setRouteProgress(progress);

					// 路径动画阶段：只更新进度，不调整任何相机参数
					// 路线会通过 routeGeoJson 根据 routeProgress 自动绘制

					currentStep++;
					requestAnimationFrame(() =>
						setTimeout(animatePath, interval),
					);
				};
				animatePath();
			});
			logger.info("路径动画完成。");

			// --- 3. 抵达与聚焦阶段 (Final Arrival Adjustment) ---
			logger.info(
				`抵达聚焦: 平移并聚焦到 "${toPoint.address || toPoint.title}"`,
			);

			// 获取当前相机状态
			const currentCameraCenter = map.getCenter();
			const currentZoom = map.getZoom();
			const currentPitch = map.getPitch();
			const currentBearing = map.getBearing();

			// 目标相机状态
			const targetCenter: [number, number] = [toCoords.lng, toCoords.lat];
			const targetZoom = CAMERA_SETTINGS.arrivalZoom;
			const targetPitch = CAMERA_SETTINGS.arrivalPitch;
			const targetBearing = CAMERA_SETTINGS.arrivalBearing;

			// 第一步：平移到目标点（保持当前缩放和视角）
			logger.info("第一步：平移目标点到地图中心");
			const panDuration = ANIMATION_DURATION.arrivalFocusFlight * 0.4; // 40%时间用于平移
			const panSteps = 20;
			const panInterval = panDuration / panSteps;

			await new Promise<void>((resolvePan) => {
				let panStep = 0;
				const animatePan = () => {
					if (panStep >= panSteps || !map) {
						// 确保平移到精确位置
						map?.jumpTo({
							center: targetCenter,
							zoom: currentZoom, // 保持当前缩放
							pitch: currentPitch, // 保持当前俯仰
							bearing: currentBearing, // 保持当前朝向
						});
						map?.once("idle", () => resolvePan());
						return;
					}

					const panProgress = easeInOutCubic(panStep / panSteps);

					// 只调整中心位置，其他参数保持不变
					const currentPanLng =
						currentCameraCenter.lng +
						(toCoords.lng - currentCameraCenter.lng) * panProgress;
					const currentPanLat =
						currentCameraCenter.lat +
						(toCoords.lat - currentCameraCenter.lat) * panProgress;

					map.jumpTo({
						center: [currentPanLng, currentPanLat],
						zoom: currentZoom, // 保持不变
						pitch: currentPitch, // 保持不变
						bearing: currentBearing, // 保持不变
					});

					panStep++;
					requestAnimationFrame(() =>
						setTimeout(animatePan, panInterval),
					);
				};
				animatePan();
			});

			// 第二步：缩放和视角调整（聚光灯效果）
			logger.info("第二步：缩放聚焦，形成聚光灯效果");
			const zoomDuration = ANIMATION_DURATION.arrivalFocusFlight * 0.6; // 60%时间用于缩放聚焦
			const zoomSteps = 25;
			const zoomInterval = zoomDuration / zoomSteps;

			await new Promise<void>((resolveZoom) => {
				let zoomStep = 0;
				const animateZoom = () => {
					if (zoomStep >= zoomSteps || !map) {
						// 确保最终状态精确 - 聚光灯效果
						map?.jumpTo({
							center: targetCenter,
							zoom: targetZoom,
							pitch: targetPitch,
							bearing: targetBearing,
						});
						map?.once("idle", () => resolveZoom());
						return;
					}

					const zoomProgress = easeInOutCubic(zoomStep / zoomSteps);

					// 只调整缩放、俯仰和朝向，中心位置保持在目标点
					const currentFocusZoom =
						currentZoom + (targetZoom - currentZoom) * zoomProgress;
					const currentFocusPitch =
						currentPitch +
						(targetPitch - currentPitch) * zoomProgress;
					const currentFocusBearing =
						currentBearing +
						(targetBearing - currentBearing) * zoomProgress;

					map.jumpTo({
						center: targetCenter, // 保持在目标点中心
						zoom: currentFocusZoom,
						pitch: currentFocusPitch,
						bearing: currentFocusBearing,
					});

					zoomStep++;
					requestAnimationFrame(() =>
						setTimeout(animateZoom, zoomInterval),
					);
				};
				animateZoom();
			});

			logger.info("聚焦完成，聚光灯效果已形成。");
		},
		[setRouteProgress],
	);

	// --- Effects ---

	// Handle map load
	const handleLoad = useCallback(
		(evt: MapboxEvent<undefined> & { target: mapboxgl.Map }) => {
			const map = evt.target;
			mapRef.current = map;
			setIsMapReady(true);
			onMapLoad?.(map);

			// 添加星空和大气层效果
			map.setFog({
				color: "rgb(186, 210, 235)", // 近地大气颜色
				"high-color": "rgb(36, 92, 223)", // 高空大气颜色
				"horizon-blend": 0.02, // 地平线混合厚度
				"space-color": "rgb(11, 11, 25)", // 星空背景色
				"star-intensity": 0.8, // 星星亮度，0~1
			});

			// Add 3D buildings layer
			try {
				if (!map.getLayer("3d-buildings")) {
					map.addLayer({
						id: "3d-buildings",
						source: "composite",
						"source-layer": "building",
						filter: ["==", "extrude", "true"],
						type: "fill-extrusion",
						minzoom: 15,
						paint: {
							"fill-extrusion-color": "#aaa",
							"fill-extrusion-height": [
								"interpolate",
								["linear"],
								["zoom"],
								15,
								0,
								15.05,
								["get", "height"],
							],
							"fill-extrusion-base": [
								"interpolate",
								["linear"],
								["zoom"],
								15,
								0,
								15.05,
								["get", "min_height"],
							],
							"fill-extrusion-opacity": 0.6,
						},
					});
				}
			} catch (error) {
				console.error("Failed to add 3D buildings layer:", error);
			}

			// Initial fit bounds if requested and points available
			if (fitBoundsToPoints && points.length > 0) {
				fitMapToPoints(map, points).catch((err) =>
					console.error("Initial fitMapToPoints failed:", err),
				);
			} else {
				// 即使不需要fitBounds，也触发一次路径更新
				setTimeout(() => {
					setForceRouteUpdate((prev) => prev + 1);
				}, 100);
			}
		},
		[onMapLoad, points, fitBoundsToPoints, fitMapToPoints],
	);

	// Effect to handle flying to a specific point when flyToPointIndex changes
	useEffect(() => {
		logger.debug("useEffect[flyToPointIndex] triggered", {
			flyToPointIndex,
			isMapReady,
			showCompletedMode,
			processedIndex: processedFlyToIndexRef.current,
		});

		// 如果flyToPointIndex与上一次处理的值相同，则不重复处理
		if (
			flyToPointIndex === undefined ||
			flyToPointIndex === null ||
			flyToPointIndex < 0 ||
			flyToPointIndex >= points.length ||
			!mapRef.current ||
			!isMapReady ||
			showCompletedMode || // 不在完成状态下执行飞行动画
			flyToPointIndex === processedFlyToIndexRef.current // 添加条件:避免重复处理同一个flyToPointIndex
		) {
			logger.debug("跳过飞行动画，不满足执行条件", {
				flyToPointIndex,
				isMapReady,
				showCompletedMode,
				processedIndex: processedFlyToIndexRef.current,
			});
			return;
		}

		// 更新已处理的索引
		processedFlyToIndexRef.current = flyToPointIndex;

		const performFlightSequence = async () => {
			const map = mapRef.current!;
			const targetPoint = points[flyToPointIndex];

			if (!targetPoint?.coordinates) {
				console.error("目标点位坐标无效，无法执行飞行动画");
				return;
			}

			try {
				logger.info(
					`开始处理飞行序列到: ${targetPoint.address || targetPoint.title}`,
					{
						coordinates: targetPoint.coordinates,
						flyToPointIndex,
					},
				);

				if (flyToPointIndex === 0) {
					// 第一个点位：直接飞到并聚焦
					logger.info("飞向第一个点 (直接聚焦)");
					await flyToPromise(map, {
						center: [
							targetPoint.coordinates.lng,
							targetPoint.coordinates.lat,
						],
						zoom: CAMERA_SETTINGS.arrivalZoom,
						pitch: CAMERA_SETTINGS.arrivalPitch,
						bearing: CAMERA_SETTINGS.arrivalBearing,
						duration: ANIMATION_DURATION.arrivalFocusFlight, // 使用聚焦动画时长
						essential: true,
					});
				} else {
					// 后续点位：执行三阶段动画
					const previousPoint = points[flyToPointIndex - 1];
					if (previousPoint) {
						await animateCameraToPoint(
							map,
							previousPoint,
							targetPoint,
						);
					} else {
						// Fallback (理论上不应发生)
						logger.error(
							"animateCameraToPoint 调用错误: previousPoint 为 null，但 flyToPointIndex > 0",
							{ flyToPointIndex },
						);
						// Fallback: 直接飞到目标点聚焦 (同 flyToPointIndex === 0)
						await flyToPromise(map, {
							center: [
								targetPoint.coordinates.lng,
								targetPoint.coordinates.lat,
							],
							zoom: CAMERA_SETTINGS.arrivalZoom,
							pitch: CAMERA_SETTINGS.arrivalPitch,
							bearing: CAMERA_SETTINGS.arrivalBearing,
							duration: ANIMATION_DURATION.arrivalFocusFlight,
							essential: true,
						});
					}
				}

				logger.info(
					`完整飞行序列完成，已到达: ${targetPoint.address || targetPoint.title}`,
					{
						pointId: targetPoint.id,
						flyToPointIndex,
						zoomLevel: map.getZoom(),
						bearing: map.getBearing(),
					},
				);
				onPointArrival?.(targetPoint); // 在所有动画完成后调用
			} catch (error) {
				logger.error("飞行序列执行失败", error);
			}
		};

		performFlightSequence();
	}, [
		flyToPointIndex,
		points,
		isMapReady,
		showCompletedMode,
		animateCameraToPoint,
		onPointArrival,
	]);

	// Effect to fit bounds when fitBoundsToPoints prop changes to true
	useEffect(() => {
		if (
			fitBoundsToPoints &&
			mapRef.current &&
			isMapReady &&
			points.length > 0
		) {
			logger.info("执行fitBounds操作", { pointsCount: points.length });
			// 重置已处理的flyToPointIndex，确保下次能重新触发飞行动画
			processedFlyToIndexRef.current = null;
			fitMapToPoints(mapRef.current, points).catch((err) =>
				logger.error("fitMapToPoints failed", err),
			);
		}
	}, [fitBoundsToPoints, points, isMapReady, fitMapToPoints]);

	// Generate GeoJSON for the route line dynamically
	const routeGeoJson = useMemo(() => {
		logger.debug("生成路线GeoJSON", {
			pointsLength: points.length,
			showCompletedMode,
			currentPointIndex,
			flyToPointIndex,
			routeProgress,
			forceRouteUpdate,
		});

		if (points.length <= 1) {
			logger.debug("点位数量不足，无法生成路线");
			return null;
		}

		// 过滤有效坐标的点位并统一按时间排序
		const validPoints = points
			.filter(
				(p) =>
					p.coordinates &&
					typeof p.coordinates.lng === "number" &&
					typeof p.coordinates.lat === "number",
			)
			.sort((a, b) => {
				// 统一按时间戳排序，确保所有场景下路径连接正确
				const timeA = a.timestamp ? new Date(a.timestamp).getTime() : 0;
				const timeB = b.timestamp ? new Date(b.timestamp).getTime() : 0;
				return timeA - timeB;
			});

		logger.debug("有效点位数量", { validPointsLength: validPoints.length });

		if (validPoints.length <= 1) {
			logger.debug("有效点位数量不足，无法生成路线");
			return null;
		}

		// 完成模式：显示完整路径
		if (showCompletedMode) {
			const fullRoute = {
				type: "Feature" as const,
				properties: {},
				geometry: {
					type: "LineString" as const,
					coordinates: validPoints.map((p) => [
						p.coordinates.lng,
						p.coordinates.lat,
					]),
				},
			};
			logger.debug("完成模式：生成完整路线", {
				coordinatesCount: fullRoute.geometry.coordinates.length,
				validPointsCount: validPoints.length,
				firstCoord: fullRoute.geometry.coordinates[0],
				lastCoord:
					fullRoute.geometry.coordinates[
						fullRoute.geometry.coordinates.length - 1
					],
			});
			return fullRoute;
		}

		// 根据当前进度生成路线
		if (validPoints.length > 1) {
			// 如果正在飞行到某个点，则根据routeProgress显示部分路线
			if (
				typeof flyToPointIndex === "number" &&
				flyToPointIndex > 0 &&
				routeProgress < 1
			) {
				const fromIndex = flyToPointIndex - 1;
				const toIndex = flyToPointIndex;

				// 显示完整的已经走过的路径，加上当前正在飞行路径的一部分
				if (
					fromIndex < validPoints.length &&
					toIndex < validPoints.length
				) {
					// 创建部分路线
					const coordinates = [];

					// 添加所有已经完成的路线段
					for (let i = 0; i <= fromIndex; i++) {
						coordinates.push([
							validPoints[i].coordinates.lng,
							validPoints[i].coordinates.lat,
						]);
					}

					// 添加当前正在进行的路线段的部分
					if (routeProgress > 0) {
						const fromPoint = validPoints[fromIndex];
						const toPoint = validPoints[toIndex];
						const partialLng =
							fromPoint.coordinates.lng +
							(toPoint.coordinates.lng -
								fromPoint.coordinates.lng) *
								routeProgress;
						const partialLat =
							fromPoint.coordinates.lat +
							(toPoint.coordinates.lat -
								fromPoint.coordinates.lat) *
								routeProgress;

						coordinates.push([partialLng, partialLat]);
					}

					return {
						type: "Feature" as const,
						properties: {},
						geometry: {
							type: "LineString" as const,
							coordinates: coordinates,
						},
					};
				}
			}

			// 确定显示到哪个点：
			// 如果路径动画已完成(routeProgress === 1)且正在飞行到某个点，显示到flyToPointIndex
			// 否则显示到currentPointIndex
			let endIndex = currentPointIndex;
			if (
				typeof flyToPointIndex === "number" &&
				flyToPointIndex > 0 &&
				routeProgress === 1
			) {
				endIndex = flyToPointIndex;
			}

			// 确保endIndex不超出范围，并且至少显示到第一个点
			endIndex = Math.max(0, Math.min(endIndex, validPoints.length - 1));

			// 显示完整路线到确定的终点
			return {
				type: "Feature" as const,
				properties: {},
				geometry: {
					type: "LineString" as const,
					coordinates: validPoints
						.slice(0, endIndex + 1)
						.map((p) => [p.coordinates.lng, p.coordinates.lat]),
				},
			};
		}

		return null;
	}, [
		points,
		currentPointIndex,
		flyToPointIndex,
		routeProgress,
		showCompletedMode,
		forceRouteUpdate,
	]);

	return (
		<div className="relative w-full h-full overflow-hidden">
			<MapGL
				{...initialViewState}
				mapboxAccessToken={mapboxToken}
				style={{ width: "100%", height: "100%" }}
				mapStyle={mapStyle}
				projection={"globe" as any} // 使用类型断言解决类型问题
				onLoad={handleLoad}
				interactiveLayerIds={interactiveLayerIds}
				onClick={onClick}
				onMove={(evt) => onMapMove?.(evt)}
				scrollZoom={{ speed: 0.8, smooth: true } as any}
			>
				{/* 地图控件 - 根据hideControls prop条件渲染 */}
				{!hideControls && (
					<>
						<NavigationControl
							position="top-right"
							showCompass
							showZoom
						/>
						<ScaleControl position="bottom-right" />
						<GeolocateControl position="top-right" />
						<FullscreenControl position="top-right" />
					</>
				)}

				{/* 动态路线图层 */}
				{routeGeoJson && (
					<Source
						id="route-source"
						type="geojson"
						data={routeGeoJson}
					>
						<Layer
							id="route-layer"
							type="line"
							paint={{
								"line-color": showCompletedMode
									? "#60A5FA"
									: THEME_COLORS.primary, // 完成模式使用更亮的蓝色
								"line-width": showCompletedMode ? 4 : 3, // 完成模式使用更粗的线条
								"line-opacity": showCompletedMode ? 1.0 : 0.8, // 完成模式使用完全不透明
								...(showCompletedMode
									? {}
									: { "line-dasharray": [1, 1] }), // 完成模式不使用虚线，其他模式使用虚线
							}}
						/>
						<Layer
							id="route-glow"
							type="line"
							paint={{
								"line-color": showCompletedMode
									? "#60A5FA"
									: THEME_COLORS.primary,
								"line-width": showCompletedMode ? 8 : 6, // 完成模式使用更粗的光晕
								"line-opacity": showCompletedMode ? 0.6 : 0.4, // 完成模式使用更明显的光晕
								"line-blur": 3,
							}}
						/>
					</Source>
				)}
				{/* 调试信息：路线状态 */}
				{process.env.NODE_ENV === "development" && (
					<div className="absolute top-4 left-4 bg-black/80 text-white p-2 rounded text-xs z-50 max-w-xs overflow-auto max-h-60">
						<div>
							路线状态: {routeGeoJson ? "已生成" : "未生成"}
						</div>
						<div>完成模式: {showCompletedMode ? "是" : "否"}</div>
						<div>点位数量: {points.length}</div>
						<div>当前点位: {currentPointIndex}</div>
						<div>强制更新: {forceRouteUpdate}</div>
						{routeGeoJson && (
							<div className="mt-2 text-xs">
								<div>
									路径坐标数:{" "}
									{routeGeoJson.geometry.coordinates.length}
								</div>
								<div>
									起点: [
									{routeGeoJson.geometry.coordinates[0]?.[0]?.toFixed(
										3,
									)}
									,{" "}
									{routeGeoJson.geometry.coordinates[0]?.[1]?.toFixed(
										3,
									)}
									]
								</div>
								<div>
									终点: [
									{routeGeoJson.geometry.coordinates[
										routeGeoJson.geometry.coordinates
											.length - 1
									]?.[0]?.toFixed(3)}
									,{" "}
									{routeGeoJson.geometry.coordinates[
										routeGeoJson.geometry.coordinates
											.length - 1
									]?.[1]?.toFixed(3)}
									]
								</div>
							</div>
						)}
						<div className="mt-2 text-xs">
							点位时间序列:
							{points.slice(0, 3).map((p, i) => (
								<div key={i} className="text-xs">
									{i}:{" "}
									{p.timestamp
										? new Date(
												p.timestamp,
											).toLocaleTimeString()
										: "N/A"}
								</div>
							))}
							{points.length > 3 && <div>...</div>}
						</div>
					</div>
				)}

				{/* 显示所有点位的标记 */}
				{points.map((point, index) => {
					if (
						!point.coordinates ||
						typeof point.coordinates.lng !== "number" ||
						typeof point.coordinates.lat !== "number"
					) {
						return null;
					}

					// 计算点位状态
					// currentPointIndex prop is -1 for overview/initial, 0 to (points.length-1) for active point, points.length for completed view
					const isCurrentPoint = currentPointIndex === index;

					const isPointVisited =
						showCompletedMode ||
						(currentPointIndex !== -1 && index < currentPointIndex);

					const isBlinking =
						Array.isArray(blinkingPoints) &&
						blinkingPoints.includes(index);

					// 计算是否为目标点位（正在飞向的点位）
					const isTargetPoint =
						typeof flyToPointIndex === "number" &&
						flyToPointIndex === index &&
						!showCompletedMode; // 完成模式下不显示目标点位

					// 计算点位样式以获取z-index
					const getMarkerZIndex = () => {
						// 完成状态 - 所有点位都显示为绿色
						if (showCompletedMode) {
							return isCurrentPoint ? 10 : 5;
						}
						// 目标点位 - 红色 + 脉冲动画（正在飞向的点位）
						if (isTargetPoint) {
							return 20; // 最高优先级，确保在最前面
						}
						// 活动/当前点位 - 使用绿色
						if (isCurrentPoint) {
							return 10;
						}
						// 已访问点位 - 绿色（已播放过的点位）
						if (isPointVisited) {
							return 5;
						}
						// 闪烁点位 - 使用蓝色
						if (isBlinking) {
							return 7;
						}
						// 默认/未访问点位 - 浅灰色
						return 1;
					};

					const markerZIndex = getMarkerZIndex();

					return (
						<Marker
							key={`marker-${point.id || index}`}
							longitude={point.coordinates.lng}
							latitude={point.coordinates.lat}
							onClick={() => onMarkerClick?.(point, index)}
							anchor="bottom"
							style={{ zIndex: markerZIndex }}
						>
							<CustomMarker
								point={point}
								index={index}
								isActive={isCurrentPoint}
								isVisited={isPointVisited}
								isBlinking={isBlinking}
								isTarget={isTargetPoint}
								onClick={() => onMarkerClick?.(point, index)}
								markerRef={
									isCurrentPoint ? activeMarkerRef : undefined
								}
								isOverviewMode={showOverviewMode}
								isCompletedMode={showCompletedMode}
							/>
						</Marker>
					);
				})}
			</MapGL>
		</div>
	);
}
