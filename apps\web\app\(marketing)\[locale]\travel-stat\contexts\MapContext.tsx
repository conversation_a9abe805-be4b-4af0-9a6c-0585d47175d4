"use client";

import { type ReactNode, createContext, useContext, useMemo } from "react";
import {
	type UseColorThemeReturn,
	useColorTheme,
} from "../hooks/useColorTheme";
import {
	type UseMapControlReturn,
	useMapControl,
} from "../hooks/useMapControl";
import {
	type UseMarkerStyleReturn,
	useMarkerStyle,
} from "../hooks/useMarkerStyle";
import {
	type UseTravelDataReturn,
	useTravelData,
} from "../hooks/useTravelData";

// 1. 合并所有 Hooks 的返回类型来定义 Context 的类型
// 这样可以确保 Context 提供的 value 是类型安全的
type MapContextType = UseTravelDataReturn &
	UseMapControlReturn &
	UseColorThemeReturn &
	UseMarkerStyleReturn;

// 2. 创建 Context
// 初始值为 null，但在 Provider 外部使用时会抛出错误
const MapContext = createContext<MapContextType | null>(null);

// 3. 创建 Provider 组件
// 这个组件将包裹所有需要访问地图状态的子组件
export function MapProvider({ children }: { children: ReactNode }) {
	// 在 Provider 内部调用所有的 hooks
	const travelData = useTravelData();
	const mapControl = useMapControl();
	const colorTheme = useColorTheme();
	const markerStyle = useMarkerStyle();

	// 🔑 关键修复：使用 useMemo 来确保 value 对象引用的稳定性
	// 只有当实际的数据发生变化时，value 对象才会重新创建
	const value: MapContextType = useMemo(() => {
		return {
			...travelData,
			...mapControl,
			...colorTheme,
			...markerStyle,
		};
	}, [
		// 只监听核心数据的变化，避免函数引用变化导致的重新渲染
		travelData.travelPoints,
		travelData.visitedCountries,
		mapControl.mapLoaded,
		mapControl.viewState,
		mapControl.worldCountriesData,
		mapControl.atmosphereTheme,
		mapControl.mapStyle,
		mapControl.animationTheme,
		mapControl.mapProjection,
		mapControl.showTooltips,
		mapControl.showMarkers,
		mapControl.polaroidTooltipState,
		colorTheme.currentTheme,
		markerStyle.markerStyle,
		markerStyle.markerTheme,
		markerStyle.currentEmoji,
		markerStyle.currentEmojiColor,
		markerStyle.hideOutline,
		// 包含所有的函数引用，确保它们也被缓存
		travelData.addTravelPoint,
		travelData.addTravelPointWithDetails,
		travelData.removeTravelPoint,
		travelData.clearAllData,
		travelData.setData,
		mapControl.handleMapLoad,
		mapControl.resetMapView,
		mapControl.flyToPoint,
		mapControl.flyToCountry,
		mapControl.setAtmosphereTheme,
		mapControl.setMapStyle,
		mapControl.setAnimationTheme,
		mapControl.setMapProjection,
		mapControl.setShowTooltips,
		mapControl.setShowMarkers,
		mapControl.setPolaroidTooltipState,
		colorTheme.changeTheme,
		markerStyle.changeMarkerStyle,
		markerStyle.changeMarkerTheme,
		markerStyle.changeEmoji,
		markerStyle.changeEmojiColor,
		markerStyle.changeHideOutline,
	]);

	return <MapContext.Provider value={value}>{children}</MapContext.Provider>;
}

// 4. 创建自定义 Hook
// 这是消费 Context 的标准方式，可以提供更好的类型推断和错误处理
export function useMap() {
	const context = useContext(MapContext);
	if (!context) {
		// 如果在 Provider 外部使用此 hook，将抛出错误，这是一种保护机制
		throw new Error("useMap must be used within a MapProvider");
	}
	return context;
}
