"use client";

import { Marker<PERSON>lusterer } from "@googlemaps/markerclusterer";
import { GoogleMap, useJsApiLoader } from "@react-google-maps/api";
import type { Footprint } from "@repo/api/src/routes/diaries/schemas";
import { cn } from "@ui/lib";
import { Loader2 } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";

// 使用Google Maps测试API密钥（仅用于开发环境）
const DEV_API_KEY = "AIzaSyB41DRUbKWJHPxaFjMAwdrzWzbVKartNGg";
const GOOGLE_MAPS_API_KEY =
	process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || DEV_API_KEY;

// 默认地图中心坐标和缩放级别
const DEFAULT_CENTER = { lat: 20, lng: 0 };
const DEFAULT_ZOOM = 2;

interface TravelMapProps {
	footprints: Footprint[];
	className?: string;
	height?: string | number;
	onMarkerClick?: (footprintId: string) => void;
}

/**
 * 优化版地图组件 - 使用直接集成的Google Maps API和MarkerClusterer
 */
export default function TravelMap({
	footprints = [],
	className = "",
	height = "500px",
	onMarkerClick,
}: TravelMapProps) {
	// 状态管理
	const [mapInstance, setMapInstance] = useState<google.maps.Map | null>(
		null,
	);
	const [clusterer, setClusterer] = useState<MarkerClusterer | null>(null);
	const [loadingState, setLoadingState] = useState<string>("初始化中");
	const [error, setError] = useState<string | null>(null);
	const [selectedFootprintId, setSelectedFootprintId] = useState<
		string | null
	>(null);

	// 引用存储
	const markersRef = useRef<Map<string, google.maps.Marker>>(new Map());
	const mountCountRef = useRef(0);

	// 加载Google Maps API
	const { isLoaded, loadError } = useJsApiLoader({
		googleMapsApiKey: GOOGLE_MAPS_API_KEY,
		libraries: ["places", "geometry"] as any,
	});

	// 容器样式
	const containerStyle = {
		height: typeof height === "number" ? `${height}px` : height,
		width: "100%",
	};

	// 地图加载完成回调
	const handleMapLoad = useCallback((map: google.maps.Map) => {
		console.log("🌍 [TravelMap] 地图实例已加载");
		setMapInstance(map);
		setLoadingState("地图已加载");

		// 初始化聚合器 - 修复了配置问题
		try {
			// 简化配置，只传入必要参数，避免类型错误
			const newClusterer = new MarkerClusterer({ map });

			setClusterer(newClusterer);
			console.log("🌍 [TravelMap] 标记聚合器已初始化");
		} catch (err) {
			console.error("🌍 [TravelMap] 聚合器初始化失败:", err);
			setError(
				`聚合器初始化失败: ${err instanceof Error ? err.message : String(err)}`,
			);
		}
	}, []);

	// 处理标记 - 在地图和聚合器都加载完成后
	useEffect(() => {
		// 递增挂载计数
		mountCountRef.current++;
		console.log(`🌍 [TravelMap] 组件挂载 #${mountCountRef.current}`);

		return () => {
			console.log(`🌍 [TravelMap] 组件卸载 #${mountCountRef.current}`);
		};
	}, []);

	// 处理足迹标记
	useEffect(() => {
		if (!mapInstance || !clusterer) {
			return;
		}

		setLoadingState("处理标记中");
		console.log(`🌍 [TravelMap] 处理${footprints.length}个足迹点`);

		try {
			// 清除现有标记
			clusterer.clearMarkers();
			markersRef.current.forEach((marker) => {
				google.maps.event.clearInstanceListeners(marker);
				marker.setMap(null);
			});
			markersRef.current.clear();

			// 如果没有足迹点，重置视图
			if (footprints.length === 0) {
				mapInstance.setCenter(DEFAULT_CENTER);
				mapInstance.setZoom(DEFAULT_ZOOM);
				setLoadingState("无足迹数据");
				return;
			}

			// 创建边界对象和标记数组
			const bounds = new google.maps.LatLngBounds();
			const markers: google.maps.Marker[] = [];

			// 为每个足迹点创建标记
			footprints.forEach((fp) => {
				const position = { lat: fp.latitude, lng: fp.longitude };
				bounds.extend(position);

				const marker = new google.maps.Marker({
					position,
					title: fp.location,
					animation: google.maps.Animation.DROP,
				});

				// 添加点击事件
				marker.addListener("click", () => {
					console.log(`🌍 [TravelMap] 标记点击: ${fp.id}`);

					// 设置选中状态
					setSelectedFootprintId(fp.id);

					// 平滑移动到标记位置
					mapInstance.panTo(position);

					// 传递点击事件给父组件
					if (onMarkerClick) {
						onMarkerClick(fp.id);
					}
				});

				markers.push(marker);
				markersRef.current.set(fp.id, marker);
			});

			// 添加到聚合器
			clusterer.addMarkers(markers);

			// 自动调整地图视图以显示所有标记
			if (footprints.length > 0) {
				mapInstance.fitBounds(bounds);

				// 避免过度缩放
				google.maps.event.addListenerOnce(mapInstance, "idle", () => {
					const currentZoom = mapInstance.getZoom();
					if (currentZoom !== undefined && currentZoom > 14) {
						mapInstance.setZoom(14);
					}
				});
			}

			setLoadingState(`已显示${markers.length}个地点`);
			console.log(`🌍 [TravelMap] 已添加${markers.length}个标记`);
		} catch (err) {
			console.error("🌍 [TravelMap] 处理标记错误:", err);
			setError(
				`处理标记失败: ${err instanceof Error ? err.message : String(err)}`,
			);
		}
	}, [mapInstance, clusterer, footprints, onMarkerClick]);

	// 处理选中标记状态变化
	useEffect(() => {
		if (!mapInstance || !selectedFootprintId) return;

		// 高亮显示选中的标记
		const selectedMarker = markersRef.current.get(selectedFootprintId);
		if (selectedMarker) {
			// 确保标记在地图上可见
			const position = selectedMarker.getPosition();
			if (position) {
				mapInstance.panTo(position);
			}

			// 可以添加更多高亮效果，如弹跳动画
			selectedMarker.setAnimation(google.maps.Animation.BOUNCE);

			// 2秒后停止动画
			setTimeout(() => {
				selectedMarker.setAnimation(null);
			}, 2000);
		}
	}, [mapInstance, selectedFootprintId]);

	// 加载错误
	if (loadError) {
		return (
			<div
				style={containerStyle}
				className={cn(
					"flex items-center justify-center bg-destructive/10 border border-destructive/50 text-destructive p-4 rounded-md",
					className,
				)}
			>
				Google Maps API加载失败: {loadError.message}
			</div>
		);
	}

	// 自定义错误
	if (error) {
		return (
			<div
				style={containerStyle}
				className={cn(
					"flex items-center justify-center bg-destructive/10 border border-destructive/50 text-destructive p-4 rounded-md",
					className,
				)}
			>
				{error}
			</div>
		);
	}

	// API仍在加载中
	if (!isLoaded) {
		return (
			<div
				style={containerStyle}
				className={cn(
					"flex flex-col items-center justify-center bg-muted rounded-md",
					className,
				)}
			>
				<Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
				<span className="mt-2 text-sm text-muted-foreground">
					正在加载地图...
				</span>
			</div>
		);
	}

	// 渲染地图
	return (
		<div
			className={cn("relative rounded-md overflow-hidden", className)}
			style={containerStyle}
		>
			<GoogleMap
				mapContainerStyle={{ width: "100%", height: "100%" }}
				center={DEFAULT_CENTER}
				zoom={DEFAULT_ZOOM}
				onLoad={handleMapLoad}
				options={{
					gestureHandling: "greedy",
					zoomControl: true,
					fullscreenControl: true,
					mapTypeControl: false,
					streetViewControl: false,
					clickableIcons: false, // 禁用POI点击以提高性能
					maxZoom: 18,
					minZoom: 2,
					styles: [
						{
							featureType: "poi",
							elementType: "labels",
							stylers: [{ visibility: "off" }], // 隐藏POI标签以提高性能
						},
					],
				}}
			/>

			{/* 状态信息条 */}
			{process.env.NODE_ENV === "development" && (
				<div className="absolute bottom-0 left-0 bg-black/70 text-white text-xs p-1 z-10">
					状态: {loadingState} | 足迹: {footprints.length} | 标记:{" "}
					{markersRef.current.size}
				</div>
			)}
		</div>
	);
}
