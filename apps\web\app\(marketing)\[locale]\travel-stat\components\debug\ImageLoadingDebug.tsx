"use client";

import { useEffect, useState } from "react";
import { imageCache } from "../../utils/imageCache";
import { imageStorage } from "../../utils/imageStorage";

interface ImageLoadingDebugProps {
	isVisible?: boolean;
}

export function ImageLoadingDebug({
	isVisible = false,
}: ImageLoadingDebugProps) {
	const [debugInfo, setDebugInfo] = useState<any>({});
	const [testResults, setTestResults] = useState<any>({});

	// 更新调试信息
	const updateDebugInfo = async () => {
		try {
			const cacheStats = imageCache.getCacheStats();
			const storageStats = await imageStorage.getStorageStats();

			// 检查两个IndexedDB数据库的状态
			const checkIndexedDBStatus = async () => {
				try {
					const dbs = await indexedDB.databases();
					const imageCacheDB = dbs.find(
						(db) => db.name === "ImageCache",
					);
					const imageStorageDB = dbs.find(
						(db) => db.name === "TravelPhotos",
					);

					return {
						imageCacheExists: !!imageCacheDB,
						imageStorageExists: !!imageStorageDB,
						totalDatabases: dbs.length,
						allDatabases: dbs.map((db) => ({
							name: db.name,
							version: db.version,
						})),
					};
				} catch (error) {
					return { error: error.message };
				}
			};

			const dbStatus = await checkIndexedDBStatus();

			setDebugInfo({
				cache: cacheStats,
				storage: storageStats,
				indexeddbStatus: dbStatus,
				timestamp: new Date().toLocaleTimeString(),
			});
		} catch (error) {
			console.error("获取调试信息失败:", error);
		}
	};

	// 测试图片处理流程
	const runImageTests = async () => {
		const results: any = {};

		try {
			// 测试1: 测试空URL
			results.emptyUrl = await imageStorage.processImageUrl("");

			// 测试2: 测试data URL
			const testDataUrl =
				"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";
			results.dataUrl = await imageStorage.processImageUrl(testDataUrl);

			// 测试3: 测试IndexedDB
			results.indexeddbSupport = "indexedDB" in window;

			// 测试4: 测试缓存
			if (results.dataUrl) {
				results.cacheTest = await imageCache.processAndCacheImage(
					testDataUrl,
					{},
					"low",
				);
			}

			// 测试5: 测试indexeddb URL (如果有的话)
			try {
				const storageStats = await imageStorage.getStorageStats();
				if (storageStats.files.length > 0) {
					const testIndexedDbUrl = `indexeddb:${storageStats.files[0].id}`;
					results.indexeddbUrlTest =
						await imageStorage.processImageUrl(testIndexedDbUrl);
				} else {
					results.indexeddbUrlTest = "无IndexedDB文件可测试";
				}
			} catch (error) {
				results.indexeddbUrlTest = `测试失败: ${error.message}`;
			}

			setTestResults(results);
		} catch (error) {
			setTestResults({ error: error.message });
		}
	};

	// 清理缓存
	const clearAllCaches = async () => {
		try {
			// 清理内存缓存
			imageCache.clearCache();
			console.log("🧹 ImageCache内存缓存已清理");

			// 清理ImageStorage的IndexedDB
			await imageStorage.clearAll();
			console.log("🧹 ImageStorage IndexedDB已清理");

			// 清理ImageCache的IndexedDB
			try {
				const deleteRequest = indexedDB.deleteDatabase("ImageCache");
				await new Promise((resolve, reject) => {
					deleteRequest.onsuccess = () => resolve(undefined);
					deleteRequest.onerror = () => reject(deleteRequest.error);
					deleteRequest.onblocked = () => {
						console.warn(
							"⚠️ ImageCache数据库删除被阻止，可能有其他连接",
						);
						resolve(undefined);
					};
				});
				console.log("🧹 ImageCache IndexedDB已清理");
			} catch (error) {
				console.warn("⚠️ 清理ImageCache IndexedDB失败:", error);
			}

			console.log("🧹 所有缓存已清理");
			setTimeout(updateDebugInfo, 500); // 延迟更新，确保数据库清理完成
		} catch (error) {
			console.error("清理缓存失败:", error);
		}
	};

	// 定期更新调试信息
	useEffect(() => {
		if (isVisible) {
			updateDebugInfo();
			const interval = setInterval(updateDebugInfo, 2000);
			return () => clearInterval(interval);
		}
	}, [isVisible]);

	if (!isVisible) return null;

	return (
		<div className="fixed bottom-4 left-4 bg-black/80 text-white text-xs p-4 rounded-lg max-w-md z-50 max-h-96 overflow-y-auto">
			<div className="flex justify-between items-center mb-2">
				<h4 className="font-bold text-yellow-400">🐛 图片加载调试</h4>
				<div className="flex gap-2">
					<button
						type="button"
						onClick={updateDebugInfo}
						className="px-2 py-1 bg-blue-600 rounded text-xs hover:bg-blue-700"
					>
						刷新
					</button>
					<button
						type="button"
						onClick={runImageTests}
						className="px-2 py-1 bg-green-600 rounded text-xs hover:bg-green-700"
					>
						测试
					</button>
					<button
						type="button"
						onClick={clearAllCaches}
						className="px-2 py-1 bg-red-600 rounded text-xs hover:bg-red-700"
					>
						清理
					</button>
				</div>
			</div>

			{/* 缓存状态 */}
			<div className="mb-3">
				<div className="text-cyan-400 font-semibold">
					📦 图片缓存状态
				</div>
				<div>条目: {debugInfo.cache?.totalEntries || 0}</div>
				<div>大小: {debugInfo.cache?.formattedSize || "0 B"}</div>
				<div>更新: {debugInfo.timestamp}</div>
			</div>

			{/* 存储状态 */}
			<div className="mb-3">
				<div className="text-green-400 font-semibold">
					💾 ImageStorage状态
				</div>
				<div>文件: {debugInfo.storage?.fileCount || 0}</div>
				<div>
					总大小:{" "}
					{Math.round((debugInfo.storage?.totalSize || 0) / 1024)} KB
				</div>
			</div>

			{/* IndexedDB数据库状态 */}
			<div className="mb-3">
				<div className="text-blue-400 font-semibold">
					🗄️ IndexedDB数据库
				</div>
				{debugInfo.indexeddbStatus ? (
					<>
						<div>
							ImageCache DB:{" "}
							{debugInfo.indexeddbStatus.imageCacheExists
								? "✅"
								: "❌"}
						</div>
						<div>
							TravelPhotos DB:{" "}
							{debugInfo.indexeddbStatus.imageStorageExists
								? "✅"
								: "❌"}
						</div>
						<div>
							总数据库:{" "}
							{debugInfo.indexeddbStatus.totalDatabases || 0}
						</div>
						{debugInfo.indexeddbStatus.error && (
							<div className="text-red-400 text-xs">
								错误: {debugInfo.indexeddbStatus.error}
							</div>
						)}
					</>
				) : (
					<div>检查中...</div>
				)}
			</div>

			{/* 测试结果 */}
			{Object.keys(testResults).length > 0 && (
				<div className="mb-3">
					<div className="text-orange-400 font-semibold">
						🧪 测试结果
					</div>
					{Object.entries(testResults).map(([key, value]) => (
						<div key={key} className="text-xs">
							{key}:{" "}
							{typeof value === "boolean"
								? value
									? "✅"
									: "❌"
								: value === null
									? "❌ null"
									: typeof value === "string"
										? "✅ OK"
										: String(value)}
						</div>
					))}
				</div>
			)}

			{/* 近期缓存条目 */}
			{debugInfo.cache?.entries?.length > 0 && (
				<div>
					<div className="text-purple-400 font-semibold">
						📋 近期缓存
					</div>
					{debugInfo.cache.entries
						.slice(0, 3)
						.map((entry: any, index: number) => (
							<div
								key={index}
								className="text-xs border-l-2 border-gray-600 pl-2 mb-1"
							>
								<div>URL: {entry.url}</div>
								<div>
									优先级: {entry.priority} | 访问:{" "}
									{entry.accessCount}
								</div>
								<div>
									年龄: {Math.round(entry.age / 1000)}s |
									大小: {Math.round(entry.size / 1024)}KB
								</div>
							</div>
						))}
				</div>
			)}
		</div>
	);
}
