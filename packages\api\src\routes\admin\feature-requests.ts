import { z<PERSON>alida<PERSON> } from "@hono/zod-validator";
import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { adminMiddleware } from "../../middleware/admin";

// 特性状态枚举
const FEATURE_STATUSES = [
	"under_consideration", // 待考虑
	"planned", // 计划中
	"in_progress", // 开发中
	"completed", // 已完成
	"wont_do", // 不考虑
] as const;

// 特性请求创建和更新的验证schema
const createFeatureRequestSchema = z.object({
	title: z.string().min(1, "标题不能为空").max(200, "标题不能超过200个字符"),
	description: z.string().min(1, "描述不能为空"),
	productId: z.string().min(1, "必须选择产品"),
	status: z.enum(FEATURE_STATUSES).default("under_consideration"),
});

const updateFeatureRequestSchema = createFeatureRequestSchema.partial();

// 状态更新schema
const updateStatusSchema = z.object({
	status: z.enum(FEATURE_STATUSES),
});

// 查询参数验证
const querySchema = z.object({
	page: z
		.string()
		.optional()
		.transform((val) => (val ? Number.parseInt(val, 10) : 1)),
	limit: z
		.string()
		.optional()
		.transform((val) => (val ? Number.parseInt(val, 10) : 10)),
	search: z.string().optional(),
	productId: z.string().optional(),
	status: z.enum(FEATURE_STATUSES).optional(),
	sortBy: z
		.enum(["createdAt", "updatedAt", "voteCount", "title"])
		.default("createdAt"),
	sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

export const featureRequestRouter = new Hono()
	.use(adminMiddleware)
	// 获取特性请求列表
	.get("/feature-requests", zValidator("query", querySchema), async (c) => {
		try {
			const {
				page,
				limit,
				search,
				productId,
				status,
				sortBy,
				sortOrder,
			} = c.req.valid("query");
			const offset = (page - 1) * limit;

			// 构建查询条件
			const where: any = {};

			if (search) {
				where.OR = [
					{ title: { contains: search, mode: "insensitive" } },
					{ description: { contains: search, mode: "insensitive" } },
				];
			}

			if (productId) {
				where.productId = productId;
			}

			if (status) {
				where.status = status;
			}

			// 构建排序条件
			const orderBy: any = {};
			orderBy[sortBy] = sortOrder;

			// 获取总数和数据
			const [total, featureRequests] = await Promise.all([
				db.featureRequest.count({ where }),
				db.featureRequest.findMany({
					where,
					skip: offset,
					take: limit,
					orderBy,
					include: {
						product: {
							select: {
								id: true,
								name: true,
							},
						},
						user: {
							select: {
								id: true,
								name: true,
								email: true,
							},
						},
						_count: {
							select: {
								votes: true,
								comments: true,
								subscriptions: true,
							},
						},
					},
				}),
			]);

			return c.json({
				success: true,
				data: {
					featureRequests,
					pagination: {
						page,
						limit,
						total,
						totalPages: Math.ceil(total / limit),
					},
				},
			});
		} catch (error) {
			console.error("获取特性请求列表失败:", error);
			return c.json(
				{
					success: false,
					error: "获取特性请求列表失败",
				},
				500,
			);
		}
	})

	// 获取单个特性请求详情
	.get("/feature-requests/:id", async (c) => {
		try {
			const id = c.req.param("id");

			const featureRequest = await db.featureRequest.findUnique({
				where: { id },
				include: {
					product: {
						select: {
							id: true,
							name: true,
							description: true,
						},
					},
					user: {
						select: {
							id: true,
							name: true,
							email: true,
						},
					},
					votes: {
						include: {
							user: {
								select: {
									id: true,
									name: true,
									email: true,
								},
							},
						},
						orderBy: { createdAt: "desc" },
					},
					comments: {
						include: {
							user: {
								select: {
									id: true,
									name: true,
									email: true,
								},
							},
						},
						orderBy: { createdAt: "desc" },
					},
					subscriptions: {
						include: {
							user: {
								select: {
									id: true,
									name: true,
									email: true,
								},
							},
						},
					},
					_count: {
						select: {
							votes: true,
							comments: true,
							subscriptions: true,
						},
					},
				},
			});

			if (!featureRequest) {
				return c.json(
					{
						success: false,
						error: "特性请求不存在",
					},
					404,
				);
			}

			return c.json({
				success: true,
				data: featureRequest,
			});
		} catch (error) {
			console.error("获取特性请求详情失败:", error);
			return c.json(
				{
					success: false,
					error: "获取特性请求详情失败",
				},
				500,
			);
		}
	})

	// 创建特性请求
	.post(
		"/feature-requests",
		zValidator("json", createFeatureRequestSchema),
		async (c) => {
			try {
				const data = c.req.valid("json");

				// 验证产品是否存在
				const product = await db.product.findUnique({
					where: { id: data.productId },
				});

				if (!product) {
					return c.json(
						{
							success: false,
							error: "指定的产品不存在",
						},
						400,
					);
				}

				const featureRequest = await db.featureRequest.create({
					data,
					include: {
						product: {
							select: {
								id: true,
								name: true,
							},
						},
						_count: {
							select: {
								votes: true,
								comments: true,
							},
						},
					},
				});

				return c.json(
					{
						success: true,
						data: featureRequest,
						message: "特性请求创建成功",
					},
					201,
				);
			} catch (error) {
				console.error("创建特性请求失败:", error);
				return c.json(
					{
						success: false,
						error: "创建特性请求失败",
					},
					500,
				);
			}
		},
	)

	// 更新特性请求
	.put(
		"/feature-requests/:id",
		zValidator("json", updateFeatureRequestSchema),
		async (c) => {
			try {
				const id = c.req.param("id");
				const data = c.req.valid("json");

				// 检查特性请求是否存在
				const existingFeatureRequest =
					await db.featureRequest.findUnique({
						where: { id },
					});

				if (!existingFeatureRequest) {
					return c.json(
						{
							success: false,
							error: "特性请求不存在",
						},
						404,
					);
				}

				// 如果更新产品ID，验证产品是否存在
				if (data.productId) {
					const product = await db.product.findUnique({
						where: { id: data.productId },
					});

					if (!product) {
						return c.json(
							{
								success: false,
								error: "指定的产品不存在",
							},
							400,
						);
					}
				}

				const featureRequest = await db.featureRequest.update({
					where: { id },
					data,
					include: {
						product: {
							select: {
								id: true,
								name: true,
							},
						},
						_count: {
							select: {
								votes: true,
								comments: true,
							},
						},
					},
				});

				return c.json({
					success: true,
					data: featureRequest,
					message: "特性请求更新成功",
				});
			} catch (error) {
				console.error("更新特性请求失败:", error);
				return c.json(
					{
						success: false,
						error: "更新特性请求失败",
					},
					500,
				);
			}
		},
	)

	// 更新特性请求状态
	.patch(
		"/feature-requests/:id/status",
		zValidator("json", updateStatusSchema),
		async (c) => {
			try {
				const id = c.req.param("id");
				const { status } = c.req.valid("json");

				// 检查特性请求是否存在
				const existingFeatureRequest =
					await db.featureRequest.findUnique({
						where: { id },
					});

				if (!existingFeatureRequest) {
					return c.json(
						{
							success: false,
							error: "特性请求不存在",
						},
						404,
					);
				}

				const featureRequest = await db.featureRequest.update({
					where: { id },
					data: { status },
					include: {
						product: {
							select: {
								id: true,
								name: true,
							},
						},
						_count: {
							select: {
								votes: true,
								comments: true,
							},
						},
					},
				});

				// TODO: 这里可以添加通知逻辑，通知订阅用户状态变更

				return c.json({
					success: true,
					data: featureRequest,
					message: "特性状态更新成功",
				});
			} catch (error) {
				console.error("更新特性状态失败:", error);
				return c.json(
					{
						success: false,
						error: "更新特性状态失败",
					},
					500,
				);
			}
		},
	)

	// 删除特性请求
	.delete("/feature-requests/:id", async (c) => {
		try {
			const id = c.req.param("id");

			// 检查特性请求是否存在
			const existingFeatureRequest = await db.featureRequest.findUnique({
				where: { id },
				include: {
					_count: {
						select: {
							votes: true,
							comments: true,
							subscriptions: true,
						},
					},
				},
			});

			if (!existingFeatureRequest) {
				return c.json(
					{
						success: false,
						error: "特性请求不存在",
					},
					404,
				);
			}

			// 删除特性请求（级联删除相关的投票、评论、订阅）
			await db.featureRequest.delete({
				where: { id },
			});

			return c.json({
				success: true,
				message: "特性请求删除成功",
			});
		} catch (error) {
			console.error("删除特性请求失败:", error);
			return c.json(
				{
					success: false,
					error: "删除特性请求失败",
				},
				500,
			);
		}
	})

	// 获取特性状态统计
	.get("/feature-requests/stats/status", async (c) => {
		try {
			const stats = await db.featureRequest.groupBy({
				by: ["status"],
				_count: {
					id: true,
				},
				_sum: {
					voteCount: true,
				},
			});

			const formattedStats = stats.map((stat) => ({
				status: stat.status,
				count: stat._count.id,
				totalVotes: stat._sum.voteCount || 0,
			}));

			return c.json({
				success: true,
				data: formattedStats,
			});
		} catch (error) {
			console.error("获取状态统计失败:", error);
			return c.json(
				{
					success: false,
					error: "获取状态统计失败",
				},
				500,
			);
		}
	})

	// 获取产品特性统计
	.get("/feature-requests/stats/products", async (c) => {
		try {
			const stats = await db.product.findMany({
				select: {
					id: true,
					name: true,
					_count: {
						select: {
							featureRequests: true,
						},
					},
					featureRequests: {
						select: {
							voteCount: true,
						},
					},
				},
			});

			const formattedStats = stats.map((product) => ({
				productId: product.id,
				productName: product.name,
				featureCount: product._count.featureRequests,
				totalVotes: product.featureRequests.reduce(
					(sum, fr) => sum + fr.voteCount,
					0,
				),
			}));

			return c.json({
				success: true,
				data: formattedStats,
			});
		} catch (error) {
			console.error("获取产品统计失败:", error);
			return c.json(
				{
					success: false,
					error: "获取产品统计失败",
				},
				500,
			);
		}
	});
