import { spawn } from "node:child_process";
import fs from "node:fs";
import path from "node:path";
import { getTravelDiary } from "@repo/database/src/models/travel-diary";
import { logger } from "@repo/logs";

// 视频导出选项
export interface VideoExportOptions {
	resolution: string;
	fps: number;
	pointDuration: number;
}

// 导出服务参数
export interface ExportVideoParams {
	diaryId: string;
	userId: string;
	taskId: string;
	options: VideoExportOptions;
}

// 设置视频分辨率
const RESOLUTIONS = {
	"720p": { width: 1280, height: 720 },
	"1080p": { width: 1920, height: 1080 },
};

// 动态导入
const getChromium = async () => {
	const { chromium } = await import("playwright-core/lib/server");
	return chromium;
};

// 导出视频服务
export async function exportVideoService({
	diaryId,
	userId,
	taskId,
	options,
}: ExportVideoParams): Promise<string> {
	// 创建输出目录
	const outputDir = path.join(process.cwd(), "temp", "videos", taskId);
	fs.mkdirSync(outputDir, { recursive: true });

	const videoPath = path.join(outputDir, `${diaryId}.mp4`);
	logger.info(`开始为日记 ${diaryId} 导出视频，任务ID: ${taskId}`);

	try {
		// 获取日记详情
		const diary = await getTravelDiary(diaryId, userId);
		if (!diary) {
			throw new Error("日记不存在");
		}

		// 获取分辨率设置
		const resolution =
			RESOLUTIONS[options.resolution as keyof typeof RESOLUTIONS] ||
			RESOLUTIONS["720p"];

		// 1. 启动 Playwright
		const chromium = await getChromium();
		const browser = await chromium.launch();
		const context = await browser.newContext({
			viewport: resolution,
			recordVideo: {
				dir: outputDir,
				size: resolution,
			},
		});

		// 创建页面
		const page = await context.newPage();

		try {
			// 构建要访问的URL，包含导出模式参数
			// 注意：这里假设前端应用在同一域名下或有代理
			// 实际部署时需要配置正确的应用URL
			const appBaseUrl =
				process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";
			const url = `${appBaseUrl}/app/mapbox/${diaryId}?export=video&autoPlay=true&pointDuration=${options.pointDuration}`;

			logger.info(`访问页面: ${url}`, { taskId });
			await page.goto(url);

			// 等待页面加载完成
			await page.waitForLoadState("networkidle");

			// 等待地图加载完成 - 查找特定元素或等待自定义事件
			await page.waitForSelector(".travel-memo-content", {
				state: "visible",
			});

			// 2. 识别当前页面类型并处理
			// 如果是 CoverPage，点击继续按钮进入 MapStoryPage
			const isCoverPage = await page.evaluate(() => {
				return (
					document.querySelector(
						".travel-memo-content button[aria-label='开始旅行']",
					) !== null
				);
			});

			if (isCoverPage) {
				logger.info("当前在封面页，点击进入地图故事页", { taskId });
				await page.click("button[aria-label='开始旅行']");
				// 等待过渡动画完成
				await page.waitForTimeout(1000);
			}

			// 3. 现在应该在 MapStoryPage，等待故事完成
			logger.info("等待地图故事播放完成", { taskId });

			// 我们将通过页面上的一个全局变量来检测故事是否播放完成
			// 需要在 MapStoryPage 组件中添加支持，稍后会实现
			await page.waitForFunction("window.storyExportCompleted === true", {
				timeout: 600000,
			}); // 最长等待10分钟

			logger.info("地图故事播放完成，关闭浏览器", { taskId });

			// 4. 故事播放完成，关闭浏览器以保存视频
			await context.close();
			await browser.close();

			// 5. 处理生成的视频文件
			// Playwright 录制的视频文件名可能是随机的，需要找到并重命名
			const files = fs
				.readdirSync(outputDir)
				.filter((f) => f.endsWith(".webm"));
			if (files.length === 0) {
				throw new Error("未找到录制的视频文件");
			}

			// 找到生成的视频文件
			const recordedVideo = path.join(outputDir, files[0]);

			// 6. 使用ffmpeg转换为MP4（如果需要）
			const mp4Path = await convertToMp4(
				recordedVideo,
				videoPath,
				options.fps,
			);

			logger.info(`视频导出完成: ${mp4Path}`, { taskId });

			// 7. 清理临时文件
			if (fs.existsSync(recordedVideo)) {
				fs.unlinkSync(recordedVideo);
			}

			// 8. 返回视频文件路径
			return mp4Path;
		} catch (error) {
			logger.error(
				`视频录制过程中出错: ${error instanceof Error ? error.message : String(error)}`,
				{ taskId },
			);
			await browser.close();
			throw error;
		}
	} catch (error) {
		logger.error(
			`视频导出失败: ${error instanceof Error ? error.message : String(error)}`,
			{ taskId },
		);
		throw error;
	}
}

// 将WebM转换为MP4
async function convertToMp4(
	inputPath: string,
	outputPath: string,
	fps: number,
): Promise<string> {
	return new Promise((resolve, reject) => {
		// 检查ffmpeg是否可用
		try {
			// 使用ffmpeg转换为mp4
			const ffmpeg = spawn("ffmpeg", [
				"-i",
				inputPath,
				"-c:v",
				"libx264",
				"-preset",
				"fast",
				"-r",
				fps.toString(), // 设置帧率
				"-pix_fmt",
				"yuv420p", // 确保兼容性
				"-movflags",
				"+faststart", // 优化网络播放
				"-y", // 覆盖输出文件
				outputPath,
			]);

			ffmpeg.stderr.on("data", (data) => {
				// ffmpeg 输出日志到 stderr
				logger.debug(`ffmpeg: ${data}`);
			});

			ffmpeg.on("close", (code) => {
				if (code === 0) {
					resolve(outputPath);
				} else {
					reject(new Error(`ffmpeg 进程退出，状态码 ${code}`));
				}
			});

			ffmpeg.on("error", (err) => {
				reject(new Error(`ffmpeg 错误: ${err.message}`));
			});
		} catch (error) {
			reject(
				new Error(
					`启动 ffmpeg 失败: ${error instanceof Error ? error.message : String(error)}`,
				),
			);
		}
	});
}
