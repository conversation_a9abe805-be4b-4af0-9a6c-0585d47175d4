"use client";

import { useCallback, useEffect, useState } from "react";
import { createAnonymousUser, getAnonymousUserData } from "../anonymous-user";
import { FeatureVotingApiClient } from "../api-client";
import type {
	CreateFeatureRequestData,
	FeatureRequest,
	FeatureRequestQuery,
	PaginationInfo,
	Product,
	UseFeatureVotingReturn,
	VoteData,
} from "../types";

interface UseFeatureVotingOptions {
	apiBaseUrl?: string;
	autoLoadProducts?: boolean;
	autoLoadFeatureRequests?: boolean;
	defaultQuery?: FeatureRequestQuery;
}

interface VotingStates {
	[key: string]: boolean;
}

interface SubmissionState {
	isSubmitting: boolean;
	success: boolean;
	error: string | null;
}

export function useFeatureVoting(
	options: UseFeatureVotingOptions = {},
): UseFeatureVotingReturn {
	const {
		apiBaseUrl = "/api",
		autoLoadProducts = true,
		autoLoadFeatureRequests = true,
		defaultQuery = {},
	} = options;

	// API 客户端
	const [apiClient] = useState(() => new FeatureVotingApiClient(apiBaseUrl));

	// 数据状态
	const [featureRequests, setFeatureRequests] = useState<FeatureRequest[]>(
		[],
	);
	const [products, setProducts] = useState<Product[]>([]);
	const [pagination, setPagination] = useState<PaginationInfo | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// 操作状态
	const [votingStates, setVotingStates] = useState<VotingStates>({});
	const [submissionState, setSubmissionState] = useState<SubmissionState>({
		isSubmitting: false,
		success: false,
		error: null,
	});

	// 初始化匿名用户
	useEffect(() => {
		const initAnonymousUser = () => {
			try {
				const anonymousUser = getAnonymousUserData();
				if (!anonymousUser) {
					createAnonymousUser();
				}
				apiClient.setAnonymousId(anonymousUser?.anonymousId || "");
			} catch (error) {
				console.warn("Failed to initialize anonymous user:", error);
			}
		};

		initAnonymousUser();
	}, [apiClient]);

	// 加载产品列表
	const loadProducts = useCallback(async () => {
		try {
			setError(null);
			const response = await apiClient.getProducts();

			if (response.success && response.data) {
				setProducts(response.data);
			} else {
				setError(response.error || "加载产品列表失败");
			}
		} catch (error) {
			setError(
				error instanceof Error ? error.message : "加载产品列表失败",
			);
		}
	}, [apiClient]);

	// 加载特性请求列表
	const loadFeatureRequests = useCallback(
		async (query: FeatureRequestQuery = {}) => {
			try {
				setIsLoading(true);
				setError(null);

				const mergedQuery = { ...defaultQuery, ...query };
				const response =
					await apiClient.getFeatureRequests(mergedQuery);

				if (response.success && response.data) {
					setFeatureRequests(response.data);
					// 假设API返回的数据包含分页信息
					// 这里需要根据实际API响应格式调整
					if ("pagination" in response && response.pagination) {
						setPagination(response.pagination as PaginationInfo);
					}
				} else {
					setError(response.error || "加载特性请求失败");
				}
			} catch (error) {
				setError(
					error instanceof Error ? error.message : "加载特性请求失败",
				);
			} finally {
				setIsLoading(false);
			}
		},
		[apiClient, defaultQuery],
	);

	// 投票功能
	const vote = useCallback(
		async (featureRequestId: string, voteData: VoteData = {}) => {
			try {
				setVotingStates((prev: VotingStates) => ({
					...prev,
					[featureRequestId]: true,
				}));
				setError(null);

				// 获取匿名用户数据
				const anonymousUser = getAnonymousUserData();
				const mergedVoteData = {
					...voteData,
					anonymousId: anonymousUser?.anonymousId,
					voterName: voteData.voterName || anonymousUser?.voterName,
					voterEmail:
						voteData.voterEmail || anonymousUser?.voterEmail,
				};

				const response = await apiClient.voteForFeature(
					featureRequestId,
					mergedVoteData,
				);

				if (response.success) {
					// 更新本地状态
					setFeatureRequests((prev: FeatureRequest[]) =>
						prev.map((fr: FeatureRequest) =>
							fr.id === featureRequestId
								? {
										...fr,
										voteCount: fr.voteCount + 1,
										hasVoted: true,
									}
								: fr,
						),
					);
				} else {
					setError(response.error || "投票失败");
				}
			} catch (error) {
				setError(error instanceof Error ? error.message : "投票失败");
			} finally {
				setVotingStates((prev: VotingStates) => ({
					...prev,
					[featureRequestId]: false,
				}));
			}
		},
		[apiClient],
	);

	// 取消投票功能
	const unvote = useCallback(
		async (featureRequestId: string) => {
			try {
				setVotingStates((prev: VotingStates) => ({
					...prev,
					[featureRequestId]: true,
				}));
				setError(null);

				const response =
					await apiClient.unvoteFeature(featureRequestId);

				if (response.success) {
					// 更新本地状态
					setFeatureRequests((prev: FeatureRequest[]) =>
						prev.map((fr: FeatureRequest) =>
							fr.id === featureRequestId
								? {
										...fr,
										voteCount: Math.max(
											0,
											fr.voteCount - 1,
										),
										hasVoted: false,
									}
								: fr,
						),
					);
				} else {
					setError(response.error || "取消投票失败");
				}
			} catch (error) {
				setError(
					error instanceof Error ? error.message : "取消投票失败",
				);
			} finally {
				setVotingStates((prev: VotingStates) => ({
					...prev,
					[featureRequestId]: false,
				}));
			}
		},
		[apiClient],
	);

	// 提交特性请求
	const submitFeatureRequest = useCallback(
		async (data: CreateFeatureRequestData) => {
			try {
				setSubmissionState((prev: SubmissionState) => ({
					...prev,
					isSubmitting: true,
					error: null,
				}));

				// 获取匿名用户数据
				const anonymousUser = getAnonymousUserData();
				const submitData = {
					...data,
					anonymousId: anonymousUser?.anonymousId,
					authorName: data.authorName || anonymousUser?.voterName,
					authorEmail: data.authorEmail || anonymousUser?.voterEmail,
				};

				const response =
					await apiClient.submitFeatureRequest(submitData);

				if (response.success && response.data) {
					setSubmissionState((prev: SubmissionState) => ({
						...prev,
						success: true,
					}));

					// 添加到本地列表
					setFeatureRequests((prev: FeatureRequest[]) => [
						response.data!,
						...prev,
					]);
				} else {
					setSubmissionState((prev: SubmissionState) => ({
						...prev,
						error: response.error || "提交失败",
					}));
				}
			} catch (error) {
				setSubmissionState((prev: SubmissionState) => ({
					...prev,
					error: error instanceof Error ? error.message : "提交失败",
				}));
			} finally {
				setSubmissionState((prev: SubmissionState) => ({
					...prev,
					isSubmitting: false,
				}));
			}
		},
		[apiClient],
	);

	// 自动加载数据
	useEffect(() => {
		if (autoLoadProducts) {
			loadProducts();
		}
	}, [autoLoadProducts, loadProducts]);

	useEffect(() => {
		if (autoLoadFeatureRequests) {
			loadFeatureRequests(defaultQuery);
		}
	}, [autoLoadFeatureRequests, loadFeatureRequests, defaultQuery]);

	return {
		// 数据
		featureRequests,
		products,
		isLoading,
		error,

		// 分页
		pagination,

		// 操作
		vote,
		unvote,
		submitFeatureRequest,
		loadFeatureRequests,
		loadProducts,

		// 状态
		votingStates,
		submissionState,
	};
}
