import { type StorageProviderType, createStorageProvider } from "@repo/storage";
import { type NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
	try {
		const requestData = await request.json();
		const { provider, bucket, paths, region: clientRegion } = requestData;

		if (
			!provider ||
			!bucket ||
			!paths ||
			!Array.isArray(paths) ||
			paths.length === 0
		) {
			return NextResponse.json(
				{ error: "缺少必要参数 provider, bucket 或 paths 数组" },
				{ status: 400 },
			);
		}

		// 获取区域配置（针对腾讯云 COS）
		let region: string | undefined;
		if (provider === "tencent-cos") {
			region = process.env.TENCENT_COS_REGION || "ap-shanghai";
			console.log(`使用腾讯云 COS 区域: ${region}`);
		} else {
			// 如果前端传入了 region 且不是腾讯云 COS，保留这个值
			region = clientRegion;
		}

		// 创建存储提供商实例
		const storageProvider = createStorageProvider(
			provider as StorageProviderType,
			{
				accessKeyId: process.env.TENCENT_COS_SECRET_ID || "placeholder",
				secretAccessKey:
					process.env.TENCENT_COS_SECRET_KEY || "placeholder",
				region: process.env.TENCENT_COS_REGION || "ap-shanghai",
			},
		);

		// 执行批量删除
		const result = await storageProvider.batchDeleteObjects({
			bucket,
			paths,
			region, // 使用从环境变量获取的区域
		});

		return NextResponse.json(result);
	} catch (error) {
		console.error("批量删除文件错误:", error);
		return NextResponse.json(
			{
				error:
					error instanceof Error ? error.message : "批量删除文件失败",
			},
			{ status: 500 },
		);
	}
}
