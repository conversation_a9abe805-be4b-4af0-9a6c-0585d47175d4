import html2canvas from "html2canvas-pro";
import React from "react";
import { createRoot } from "react-dom/client";
import type {
	CardCustomization,
	CardExportOptions,
	CardExportResult,
	CardTemplate,
	CountryData,
	ExportQuality,
	SocialPlatform,
	TravelPoint,
} from "../types/cardTypes";

// 社交平台配置
const PLATFORM_CONFIGS = {
	instagram: { width: 1080, height: 1080, name: "Instagram" },
	wechat: { width: 1200, height: 900, name: "微信朋友圈" },
	weibo: { width: 1080, height: 1350, name: "微博" },
	twitter: { width: 1200, height: 675, name: "Twitter" },
	facebook: { width: 1200, height: 630, name: "Facebook" },
} as const;

// 质量配置
const QUALITY_CONFIGS = {
	low: { scale: 1, quality: 0.7 },
	medium: { scale: 1.5, quality: 0.85 },
	high: { scale: 2, quality: 0.95 },
} as const;

/**
 * 等待字体加载完成
 */
async function waitForFonts(): Promise<void> {
	if ("fonts" in document) {
		try {
			await document.fonts.ready;
			// 额外等待一小段时间确保字体渲染稳定
			await new Promise((resolve) => setTimeout(resolve, 100));
		} catch (error) {
			console.warn("字体加载等待失败:", error);
		}
	}
}

/**
 * 导出 HTML 元素为图片（基础版本）
 */
async function exportCardElement(
	element: HTMLElement,
	options: CardExportOptions,
): Promise<CardExportResult> {
	try {
		console.log("📸 开始导出HTML元素", {
			platform: options.platform,
			quality: options.quality,
			elementSize: {
				width: element.offsetWidth,
				height: element.offsetHeight,
			},
		});

		// 等待字体加载
		console.log("🔤 等待字体加载");
		await waitForFonts();

		const platformConfig = PLATFORM_CONFIGS[options.platform];
		const qualityConfig = QUALITY_CONFIGS[options.quality];

		console.log("⚙️ 配置信息:", {
			platformConfig,
			qualityConfig,
		});

		// html2canvas 配置
		console.log("🖼️ 开始 html2canvas 截图");
		const canvas = await html2canvas(element, {
			useCORS: true,
			allowTaint: false,
			backgroundColor: null,
			scale: qualityConfig.scale,
			width: element.offsetWidth,
			height: element.offsetHeight,
			logging: false,
			imageTimeout: 15000,
			removeContainer: true,
			foreignObjectRendering: false,
			onclone: (clonedDoc: Document) => {
				console.log("🔄 克隆文档处理");
				// 确保克隆文档中的样式正确
				const clonedElement = clonedDoc.querySelector(
					'[class*="card"]',
				) as HTMLElement;
				if (clonedElement) {
					clonedElement.style.transform = "none";
					clonedElement.style.animation = "none";
				}
			},
		});

		console.log("✅ html2canvas 截图完成", {
			canvasWidth: canvas.width,
			canvasHeight: canvas.height,
		});

		// 创建最终尺寸的画布
		const finalCanvas = document.createElement("canvas");
		const targetWidth = platformConfig.width;
		const targetHeight = platformConfig.height;

		finalCanvas.width = targetWidth;
		finalCanvas.height = targetHeight;

		const ctx = finalCanvas.getContext("2d")!;

		// 填充白色背景
		ctx.fillStyle = "#ffffff";
		ctx.fillRect(0, 0, targetWidth, targetHeight);

		// 计算缩放比例以适应目标尺寸，保持纵横比
		const scaleX = targetWidth / canvas.width;
		const scaleY = targetHeight / canvas.height;
		const scale = Math.min(scaleX, scaleY);

		const scaledWidth = canvas.width * scale;
		const scaledHeight = canvas.height * scale;
		const x = (targetWidth - scaledWidth) / 2;
		const y = (targetHeight - scaledHeight) / 2;

		// 绘制缩放后的图像
		ctx.drawImage(canvas, x, y, scaledWidth, scaledHeight);

		// 转换为 Blob
		const blob = await new Promise<Blob>((resolve, reject) => {
			finalCanvas.toBlob(
				(blob) => {
					if (blob) {
						resolve(blob);
					} else {
						reject(new Error("无法生成图片"));
					}
				},
				"image/png",
				qualityConfig.quality,
			);
		});

		// 创建下载链接
		const url = URL.createObjectURL(blob);
		const filename = `${options.filename || "travel-card"}.png`;

		// 触发下载
		const link = document.createElement("a");
		link.href = url;
		link.download = filename;
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);

		// 清理 URL
		setTimeout(() => URL.revokeObjectURL(url), 100);

		return {
			success: true,
			blob,
			dataURL: finalCanvas.toDataURL("image/png", qualityConfig.quality),
			filename,
			width: targetWidth,
			height: targetHeight,
		};
	} catch (error) {
		console.error("导出卡片失败:", error);
		return {
			success: false,
			error: error instanceof Error ? error.message : "导出失败",
		};
	}
}

/**
 * 复制图片到剪贴板（适配版本）
 */
export async function copyToClipboard(
	template: CardTemplate,
	mapImageData: {
		dataURL: string;
		dimensions: { width: number; height: number };
	},
	travelPoints: TravelPoint[],
	visitedCountries: CountryData[],
	customization: CardCustomization,
	options: { platform: SocialPlatform; quality: ExportQuality },
): Promise<void> {
	try {
		// 使用导出函数生成图片
		const exportResult = await exportCard(
			template,
			mapImageData,
			travelPoints,
			visitedCountries,
			customization,
			{
				platform: options.platform,
				quality: options.quality,
				filename: `temp-clipboard-${Date.now()}`,
			},
		);

		if (!exportResult.success || !exportResult.blob) {
			throw new Error(exportResult.error || "生成图片失败");
		}

		// 复制到剪贴板
		if (navigator.clipboard && window.ClipboardItem) {
			const clipboardItem = new ClipboardItem({
				"image/png": exportResult.blob,
			});
			await navigator.clipboard.write([clipboardItem]);
		} else {
			throw new Error("浏览器不支持剪贴板功能");
		}
	} catch (error) {
		console.error("复制到剪贴板失败:", error);
		throw error;
	}
}

/**
 * 分享到社交媒体（适配版本）
 */
export async function shareToSocialMedia(
	template: CardTemplate,
	mapImageData: {
		dataURL: string;
		dimensions: { width: number; height: number };
	},
	travelPoints: TravelPoint[],
	visitedCountries: CountryData[],
	customization: CardCustomization,
	options: { platform: SocialPlatform; quality: ExportQuality },
	title = "我的旅行足迹",
): Promise<void> {
	try {
		if (navigator.share) {
			// 使用 Web Share API
			const exportResult = await exportCard(
				template,
				mapImageData,
				travelPoints,
				visitedCountries,
				customization,
				{
					platform: options.platform,
					quality: options.quality,
					filename: `travel-card-${Date.now()}`,
				},
			);

			if (exportResult.success && exportResult.blob) {
				const file = new File(
					[exportResult.blob],
					`${exportResult.filename}`,
					{
						type: "image/png",
					},
				);

				await navigator.share({
					title,
					text: "我的旅行足迹卡片 - 使用 Map Moment 制作",
					files: [file],
				});
			}
		} else {
			// 降级方案：复制到剪贴板并打开平台链接
			await copyToClipboard(
				template,
				mapImageData,
				travelPoints,
				visitedCountries,
				customization,
				options,
			);

			const platformUrls: Record<SocialPlatform, string> = {
				instagram: "https://www.instagram.com/",
				wechat: "weixin://",
				weibo: "https://weibo.com/",
				twitter: "https://twitter.com/intent/tweet",
				facebook: "https://www.facebook.com/",
			};

			// 提示用户并打开平台
			const platformName = PLATFORM_CONFIGS[options.platform].name;
			const shouldOpen = confirm(
				`图片已复制到剪贴板！\n是否要打开 ${platformName} 进行分享？`,
			);

			if (shouldOpen && platformUrls[options.platform]) {
				window.open(platformUrls[options.platform], "_blank");
			}
		}
	} catch (error) {
		console.error("分享失败:", error);
		throw error;
	}
}

/**
 * 获取支持的导出质量选项
 */
export function getSupportedQualities(): ExportQuality[] {
	return ["low", "medium", "high"];
}

/**
 * 获取平台信息
 */
export function getPlatformInfo(platform: SocialPlatform) {
	return PLATFORM_CONFIGS[platform];
}

/**
 * 检查浏览器功能支持
 */
export function checkBrowserSupport() {
	return {
		html2canvas: true, // html2canvas 库已导入
		clipboard: !!navigator.clipboard,
		webShare: !!navigator.share,
		download: true, // 基本支持
	};
}

/**
 * 导出卡片模板（适配版本）
 */
export async function exportCard(
	template: CardTemplate,
	mapImageData: {
		dataURL: string;
		dimensions: { width: number; height: number };
	},
	travelPoints: TravelPoint[],
	visitedCountries: CountryData[],
	customization: CardCustomization,
	options: CardExportOptions,
): Promise<CardExportResult> {
	console.log("🎨 开始渲染卡片模板", {
		templateName: template.name,
		templateId: template.id,
		platform: options.platform,
		quality: options.quality,
	});

	return new Promise((resolve) => {
		// 创建临时容器
		console.log("📦 创建临时容器");
		const container = document.createElement("div");
		container.style.position = "fixed";
		container.style.top = "-9999px";
		container.style.left = "-9999px";
		container.style.zIndex = "-1000";
		document.body.appendChild(container);

		try {
			// 渲染模板组件
			console.log("🔧 准备渲染模板组件", {
				componentExists: !!template.component,
			});
			const TemplateComponent = template.component;
			const root = createRoot(container);

			const element = React.createElement(TemplateComponent, {
				mapImageData,
				travelPoints,
				visitedCountries,
				customization,
				platform: options.platform,
			});

			console.log("⚛️ 开始渲染 React 组件");
			root.render(element);

			// 等待渲染完成
			setTimeout(async () => {
				console.log("⏰ 渲染等待完成，查找卡片元素");
				try {
					const cardElement =
						container.querySelector("[data-card-element]") ||
						(container.firstElementChild as HTMLElement);

					console.log("🔍 查找结果:", {
						cardElementExists: !!cardElement,
						elementTagName: cardElement?.tagName,
						elementClassName: cardElement?.className,
						containerChildren: container.children.length,
					});

					if (!cardElement) {
						throw new Error("未找到卡片元素");
					}

					console.log("📐 卡片元素尺寸:", {
						width: (cardElement as HTMLElement).offsetWidth,
						height: (cardElement as HTMLElement).offsetHeight,
						scrollWidth: (cardElement as HTMLElement).scrollWidth,
						scrollHeight: (cardElement as HTMLElement).scrollHeight,
					});

					// 调用原始导出函数
					console.log("🖼️ 开始截图处理");
					const result = await exportCardElement(
						cardElement as HTMLElement,
						options,
					);

					console.log("🧹 清理容器");
					// 清理
					root.unmount();
					document.body.removeChild(container);

					resolve(result);
				} catch (error) {
					console.error("❌ 渲染或导出过程中出错:", error);
					// 清理
					root.unmount();
					document.body.removeChild(container);

					resolve({
						success: false,
						error:
							error instanceof Error ? error.message : "导出失败",
					});
				}
			}, 500); // 给足够时间让组件渲染完成
		} catch (error) {
			// 清理
			document.body.removeChild(container);
			resolve({
				success: false,
				error: error instanceof Error ? error.message : "导出失败",
			});
		}
	});
}
