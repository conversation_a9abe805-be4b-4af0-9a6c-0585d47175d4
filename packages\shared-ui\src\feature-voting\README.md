# Feature Voting 模块

一个完整的特性投票系统，支持匿名和登录用户投票、提交特性请求、评论等功能。

## 功能特性

- ✅ **匿名投票支持** - 无需注册即可投票和提交特性请求
- ✅ **实时更新** - 自动刷新投票数据
- ✅ **响应式设计** - 适配移动端和桌面端
- ✅ **状态管理** - 完整的特性状态流程管理
- ✅ **模块化组件** - 可独立使用的组件
- ✅ **TypeScript支持** - 完整的类型定义

## 快速开始

### 1. 基本使用

```tsx
import { FeatureVotingPage } from "@repo/shared-ui/feature-voting";

export default function VotingPage() {
  return (
    <FeatureVotingPage
      defaultProductId="product-1"
      showSubmitForm={true}
      allowVoting={true}
      allowComments={true}
    />
  );
}
```

### 2. 只显示特性列表

```tsx
import { FeatureRequestList } from "@repo/shared-ui/feature-voting";

export default function FeatureList() {
  return (
    <FeatureRequestList
      productId="product-1"
      showVoteCounts={true}
      allowVoting={true}
      allowComments={false}
    />
  );
}
```

### 3. 只显示提交表单

```tsx
import { SubmitFeatureForm, useFeatureVoting } from "@repo/shared-ui/feature-voting";

export default function SubmitForm() {
  const { products, submitFeature } = useFeatureVoting();

  const handleSubmit = async (data) => {
    await submitFeature(data);
    // 处理提交成功
  };

  return (
    <SubmitFeatureForm
      products={products}
      onSubmit={handleSubmit}
    />
  );
}
```

## 组件API

### FeatureVotingPage

完整的特性投票页面组件。

```tsx
interface FeatureVotingPageProps {
  className?: string;
  defaultProductId?: string;
  showSubmitForm?: boolean;
  allowVoting?: boolean;
  allowComments?: boolean;
}
```

### FeatureRequestList

特性请求列表组件。

```tsx
interface FeatureRequestListProps {
  productId?: string;
  showVoteCounts?: boolean;
  allowVoting?: boolean;
  allowComments?: boolean;
  className?: string;
}
```

### SubmitFeatureForm

提交特性请求表单组件。

```tsx
interface SubmitFeatureFormProps {
  products: Product[];
  onSubmit: (data: SubmitFeatureRequest) => void;
  isLoading?: boolean;
  className?: string;
}
```

### VoteButton

投票按钮组件。

```tsx
interface VoteButtonProps {
  featureRequestId: string;
  hasVoted: boolean;
  voteCount?: number;
  showCount?: boolean;
  onVote: (featureRequestId: string) => void;
  onUnvote: (featureRequestId: string) => void;
  isLoading?: boolean;
  className?: string;
}
```

## Hook API

### useFeatureVoting

主要的状态管理Hook。

```tsx
const {
  featureRequests,
  products,
  isLoading,
  error,
  vote,
  unvote,
  submitFeature,
  addComment,
  refresh,
} = useFeatureVoting({
  productId: "product-1",
  showVoteCounts: true,
  autoRefresh: true,
  refreshInterval: 60000,
});
```

## 类型定义

### FeatureRequest

```tsx
interface FeatureRequest {
  id: string;
  title: string;
  description: string;
  status: FeatureStatus;
  voteCount: number;
  productId: string;
  product?: {
    id: string;
    name: string;
  };
  author?: {
    name: string;
  };
  hasVoted?: boolean;
  commentCount?: number;
  createdAt: string;
  updatedAt: string;
}
```

### FeatureStatus

```tsx
type FeatureStatus = 
  | "under_consideration"  // 待考虑
  | "planned"             // 计划中
  | "in_progress"         // 开发中
  | "completed"           // 已完成
  | "wont_do";           // 不考虑
```

## 样式定制

所有组件都支持通过 `className` 属性进行样式定制，使用 Tailwind CSS 类名。

```tsx
<FeatureVotingPage 
  className="max-w-4xl mx-auto p-6"
/>

<FeatureRequestList 
  className="space-y-6"
/>
```

## 匿名用户支持

系统自动处理匿名用户身份：

- 自动生成唯一的匿名用户ID
- 使用localStorage保存用户偏好
- 支持浏览器指纹识别
- 自动防重复投票

## 注意事项

1. **API依赖**: 需要确保后端API路由已正确配置
2. **匿名工具**: 需要 `@repo/utils` 包中的匿名用户工具
3. **UI组件**: 依赖项目中的Shadcn UI组件
4. **状态管理**: 使用React hooks进行状态管理，无需额外的状态管理库

## 开发指南

### 添加新组件

1. 在 `components/` 目录下创建新组件
2. 在 `types/index.ts` 中添加类型定义
3. 在 `components/index.ts` 中导出组件
4. 更新文档

### 扩展功能

- 评论系统：可以扩展 `CommentSection` 组件
- 搜索过滤：可以添加搜索和过滤功能
- 通知系统：可以集成邮件通知
- 管理界面：可以添加管理员界面

## 故障排除

### 常见问题

1. **投票不生效**: 检查API路由和匿名用户ID生成
2. **样式问题**: 确保Tailwind CSS正确配置
3. **类型错误**: 检查TypeScript配置和类型导入

### 调试技巧

```tsx
// 启用调试模式
const { error } = useFeatureVoting();
console.log("Feature voting error:", error);

// 检查匿名用户状态
import { getAnonymousUser } from "@repo/utils";
console.log("Anonymous user:", getAnonymousUser());
``` 