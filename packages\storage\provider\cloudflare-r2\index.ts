import { Readable } from "stream";
import {
	CreateBucketCommand,
	DeleteObjectsCommand,
	GetObjectCommand,
	PutObjectCommand,
	S3Client,
} from "@aws-sdk/client-s3";
import { getSignedUrl as getR2SignedUrl } from "@aws-sdk/s3-request-presigner";
import { logger } from "@repo/logs";
import type {
	BatchDeleteOptions,
	BatchDeleteResult,
	BatchUploadOptions,
	BatchUploadResult,
	StorageProvider,
	StorageProviderConfig,
	UploadFileOptions,
	UploadFileResult,
} from "../../types";

// 将Buffer转换为Readable流
function bufferToStream(buffer: Buffer): Readable {
	const stream = new Readable();
	stream.push(buffer);
	stream.push(null);
	return stream;
}

/**
 * Cloudflare R2存储提供商实现
 * 注意: Cloudflare R2使用S3兼容API，但有一些细微差异
 */
export class CloudflareR2Provider implements StorageProvider {
	private client: S3Client;
	private config: StorageProviderConfig;

	constructor(config: StorageProviderConfig) {
		this.config = config;
		this.client = this.createClient();
	}

	/**
	 * 获取提供商名称
	 */
	getProviderName(): string {
		return "cloudflare-r2";
	}

	/**
	 * 创建Cloudflare R2客户端
	 */
	private createClient(): S3Client {
		const { endpoint, accessKeyId, secretAccessKey, options } = this.config;

		if (!endpoint) {
			throw new Error("Cloudflare R2提供商配置中缺少endpoint");
		}

		if (!accessKeyId) {
			throw new Error("Cloudflare R2提供商配置中缺少accessKeyId");
		}

		if (!secretAccessKey) {
			throw new Error("Cloudflare R2提供商配置中缺少secretAccessKey");
		}

		return new S3Client({
			region: "auto", // Cloudflare R2不使用区域，但需要提供一个值
			endpoint,
			credentials: {
				accessKeyId,
				secretAccessKey,
			},
			...options,
		});
	}

	/**
	 * 创建存储桶
	 */
	async createBucket(
		name: string,
		options?: { public?: boolean },
	): Promise<void> {
		try {
			await this.client.send(
				new CreateBucketCommand({
					Bucket: name,
				}),
			);

			// Cloudflare R2可以通过R2管理控制台设置公开访问，或通过R2 API设置
			if (options?.public) {
				logger.info(`请在Cloudflare控制台配置桶 ${name} 为公开访问`);
				// R2实际设置公开访问需要额外步骤，此处简化处理
			}
		} catch (e) {
			logger.error(e);
			throw new Error(`无法创建存储桶: ${name}`);
		}
	}

	/**
	 * 获取签名上传URL
	 */
	async getSignedUploadUrl(
		path: string,
		options: { bucket: string; contentType?: string },
	): Promise<string> {
		try {
			// 创建命令并设置必要的参数
			const command = new PutObjectCommand({
				Bucket: options.bucket,
				Key: path,
				// 如果提供了 Content-Type，则添加到命令中
				...(options.contentType
					? { ContentType: options.contentType }
					: {}),
			});

			// 获取签名 URL，将 signableHeaders 设置为所有头信息可签名
			return await getR2SignedUrl(this.client, command, {
				expiresIn: 3600, // 增加到 1 小时，避免过期问题
			});
		} catch (e) {
			logger.error(e);
			throw new Error("无法获取签名上传URL");
		}
	}

	/**
	 * 获取签名访问URL
	 */
	async getSignedUrl(
		path: string,
		options: { bucket: string; expiresIn?: number },
	): Promise<string> {
		try {
			return getR2SignedUrl(
				this.client,
				new GetObjectCommand({ Bucket: options.bucket, Key: path }),
				{ expiresIn: options.expiresIn || 3600 },
			);
		} catch (e) {
			logger.error(e);
			throw new Error("无法获取签名访问URL");
		}
	}

	/**
	 * 批量获取签名上传URL
	 */
	async getBatchSignedUploadUrls(
		options: BatchUploadOptions,
	): Promise<BatchUploadResult> {
		const { bucket, paths, contentType = "image/jpeg" } = options;
		const result: BatchUploadResult = {
			urls: {},
			failedPaths: [],
			errors: {},
		};

		// 使用 Promise.allSettled 并行处理所有路径
		const promises = paths.map(async (path) => {
			try {
				const url = await getR2SignedUrl(
					this.client,
					new PutObjectCommand({
						Bucket: bucket,
						Key: path,
					}),
					{
						expiresIn: 60,
					},
				);
				return { path, url, success: true };
			} catch (error) {
				logger.error(`无法为路径 ${path} 获取签名上传URL:`, error);
				return {
					path,
					success: false,
					error:
						error instanceof Error ? error.message : String(error),
				};
			}
		});

		const results = await Promise.allSettled(promises);

		// 处理结果
		results.forEach((promiseResult) => {
			if (promiseResult.status === "fulfilled") {
				const { path, url, success, error } = promiseResult.value;
				if (success) {
					result.urls[path] = url || "";
				} else {
					result.failedPaths!.push(path);
					result.errors![path] = error || "";
				}
			} else {
				// 处理 Promise.allSettled 中被拒绝的情况
				logger.error(
					"处理批量签名URL时发生Promise拒绝:",
					promiseResult.reason,
				);
			}
		});

		// 如果没有失败的路径，清除这些字段
		if (result.failedPaths?.length === 0) {
			delete result.failedPaths;
			delete result.errors;
		}

		return result;
	}

	/**
	 * 批量删除对象
	 */
	async batchDeleteObjects(
		options: BatchDeleteOptions,
	): Promise<BatchDeleteResult> {
		const { bucket, paths } = options;
		const result: BatchDeleteResult = {
			deletedPaths: [],
			failedPaths: [],
			errors: {},
		};

		if (paths.length === 0) {
			return result;
		}

		try {
			// Cloudflare R2 一次最多支持删除1000个对象
			const maxKeysPerRequest = 1000;

			// 将路径分成多个批次处理
			for (let i = 0; i < paths.length; i += maxKeysPerRequest) {
				const batchPaths = paths.slice(i, i + maxKeysPerRequest);
				try {
					// 使用R2/S3的批量删除API
					const deleteResponse = await this.client.send(
						new DeleteObjectsCommand({
							Bucket: bucket,
							Delete: {
								Objects: batchPaths.map((path) => ({
									Key: path,
								})),
								Quiet: false, // 返回详细信息
							},
						}),
					);

					// 处理成功删除的对象
					if (deleteResponse.Deleted) {
						deleteResponse.Deleted.forEach((deleted) => {
							if (deleted.Key) {
								result.deletedPaths.push(deleted.Key);
							}
						});
					}

					// 处理删除失败的对象
					if (
						deleteResponse.Errors &&
						deleteResponse.Errors.length > 0
					) {
						deleteResponse.Errors.forEach((error) => {
							if (error.Key) {
								result.failedPaths!.push(error.Key);
								result.errors![error.Key] =
									error.Message || "未知错误";
							}
						});
					}
				} catch (batchError) {
					// 如果某个批次操作失败，将该批次的所有路径标记为失败
					logger.error("批量删除对象批次失败:", batchError);
					const errorMessage =
						batchError instanceof Error
							? batchError.message
							: String(batchError);
					batchPaths.forEach((path) => {
						result.failedPaths!.push(path);
						result.errors![path] = errorMessage;
					});
				}
			}
		} catch (error) {
			// 如果整个批量删除操作失败
			logger.error("批量删除对象失败:", error);
			// 将所有路径标记为失败
			result.failedPaths = paths;
			const errorMessage =
				error instanceof Error ? error.message : String(error);
			paths.forEach((path) => {
				result.errors![path] = errorMessage;
			});
			result.deletedPaths = []; // 清空成功删除的列表
		}

		// 如果没有失败的路径，清除这些字段
		if (result.failedPaths?.length === 0) {
			delete result.failedPaths;
			delete result.errors;
		}

		return result;
	}

	/**
	 * 直接上传文件到R2
	 * @param options 上传选项
	 */
	async uploadFile(options: UploadFileOptions): Promise<UploadFileResult> {
		const {
			bucket,
			path,
			file,
			contentType = "application/octet-stream",
		} = options;

		try {
			// 准备上传命令
			const command = new PutObjectCommand({
				Bucket: bucket,
				Key: path,
				Body: file instanceof Buffer ? file : (file as any),
				ContentType: contentType,
			});

			// 执行上传
			const response = await this.client.send(command);

			return {
				success: true,
				path,
				etag: response.ETag
					? response.ETag.replace(/"/g, "")
					: undefined,
			};
		} catch (error) {
			logger.error(`上传文件到R2失败 (${bucket}/${path}):`, error);
			return {
				success: false,
				error: error instanceof Error ? error.message : String(error),
			};
		}
	}
}

/**
 * 创建Cloudflare R2存储提供商实例
 */
export function createCloudflareR2Provider(
	config: StorageProviderConfig,
): StorageProvider {
	return new CloudflareR2Provider(config);
}

/**
 * 使用环境变量创建Cloudflare R2存储提供商实例
 */
export function createCloudflareR2ProviderFromEnv(): StorageProvider {
	const r2AccountId = process.env.CLOUDFLARE_R2_ACCOUNT_ID;
	if (!r2AccountId) {
		throw new Error("缺少环境变量 CLOUDFLARE_R2_ACCOUNT_ID");
	}

	// Cloudflare R2端点格式: https://<account_id>.r2.cloudflarestorage.com
	const r2Endpoint =
		process.env.CLOUDFLARE_R2_ENDPOINT ||
		`https://${r2AccountId}.r2.cloudflarestorage.com`;

	const r2AccessKeyId = process.env.CLOUDFLARE_R2_ACCESS_KEY_ID as string;
	if (!r2AccessKeyId) {
		throw new Error("缺少环境变量 CLOUDFLARE_R2_ACCESS_KEY_ID");
	}

	const r2SecretAccessKey = process.env
		.CLOUDFLARE_R2_SECRET_ACCESS_KEY as string;
	if (!r2SecretAccessKey) {
		throw new Error("缺少环境变量 CLOUDFLARE_R2_SECRET_ACCESS_KEY");
	}

	return createCloudflareR2Provider({
		endpoint: r2Endpoint,
		accessKeyId: r2AccessKeyId,
		secretAccessKey: r2SecretAccessKey,
	});
}
