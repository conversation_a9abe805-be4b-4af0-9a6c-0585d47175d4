import { type ChangeEvent, useState } from "react";
import { toast } from "sonner";

interface BatchCOSUploaderProps {
	bucket: string;
	region?: string;
	folder?: string; // 可选的文件夹路径
	contentType?: string;
	maxFiles?: number;
	onSuccess?: (results: { path: string; url?: string }[]) => void;
	onError?: (error: string) => void;
}

export function BatchCOSUploader({
	bucket,
	region = "ap-shanghai",
	folder = "uploads",
	contentType,
	maxFiles = 10,
	onSuccess,
	onError,
}: BatchCOSUploaderProps) {
	const [isUploading, setIsUploading] = useState(false);
	const [progress, setProgress] = useState(0);
	const [uploadedCount, setUploadedCount] = useState(0);
	const [totalFiles, setTotalFiles] = useState(0);

	// 处理批量文件上传 - 服务端处理方式
	const handleServerUpload = async (files: FileList) => {
		try {
			setIsUploading(true);
			setProgress(5);

			// 限制文件数量
			const filesToUpload = Array.from(files).slice(0, maxFiles);
			setTotalFiles(filesToUpload.length);
			setUploadedCount(0);

			const uploadResults = [];

			// 使用服务端上传方式，避免签名问题
			for (let i = 0; i < filesToUpload.length; i++) {
				const file = filesToUpload[i];
				const filePath = `${folder}/${Date.now()}-${file.name}`;

				// 创建 FormData
				const formData = new FormData();
				formData.append("bucket", bucket);
				formData.append("path", filePath);
				formData.append("file", file);
				if (region) formData.append("region", region);
				formData.append("provider", "tencent-cos");

				// 上传到服务器端 API
				const response = await fetch("/api/storage/upload-file", {
					method: "POST",
					body: formData,
				});

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(
						`上传失败: ${errorData.error || response.statusText}`,
					);
				}

				const result = await response.json();
				if (result.success) {
					uploadResults.push({ path: filePath });
					setUploadedCount(i + 1);
					setProgress(
						Math.floor(((i + 1) / filesToUpload.length) * 100),
					);
				} else {
					console.error(`文件 ${file.name} 上传失败:`, result.error);
				}
			}

			// 全部上传完成
			if (uploadResults.length > 0) {
				toast.success(`成功上传 ${uploadResults.length} 个文件`);
				if (onSuccess) {
					onSuccess(uploadResults);
				}
			} else {
				throw new Error("没有文件成功上传");
			}
		} catch (error) {
			console.error("批量上传过程中出错:", error);
			const errorMessage =
				error instanceof Error
					? error.message
					: "批量上传文件时发生未知错误";

			toast.error(errorMessage);

			if (onError) {
				onError(errorMessage);
			}
		} finally {
			// 延迟清理状态，以便显示进度完成
			setTimeout(() => {
				setIsUploading(false);
				setProgress(0);
				setUploadedCount(0);
				setTotalFiles(0);
			}, 800);
		}
	};

	// 处理文件选择
	const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
		const files = e.target.files;
		if (!files || files.length === 0) return;

		// 使用服务端处理批量上传
		handleServerUpload(files);
	};

	return (
		<div className="flex flex-col gap-3">
			<div className="flex items-center gap-2">
				<label
					htmlFor="batch-cos-file-input"
					className={`px-3 py-2 rounded cursor-pointer transition-colors ${
						isUploading
							? "bg-blue-100 text-blue-700"
							: "bg-blue-500 text-white hover:bg-blue-600"
					}`}
				>
					{isUploading ? "上传中..." : "选择多个文件"}
				</label>
				<input
					id="batch-cos-file-input"
					type="file"
					onChange={handleFileChange}
					disabled={isUploading}
					multiple
					className="hidden"
				/>
			</div>

			{isUploading && (
				<>
					<div className="w-full bg-gray-200 rounded-full h-2">
						<div
							className="bg-blue-600 rounded-full h-2 transition-all duration-300"
							style={{ width: `${progress}%` }}
						></div>
					</div>

					<div className="text-sm text-gray-500">
						<p>
							进度: {uploadedCount}/{totalFiles} 文件 ({progress}
							%)
						</p>
						<p>存储桶: {bucket}</p>
					</div>
				</>
			)}
		</div>
	);
}
