import {
	DndContext,
	type DragEndEvent,
	DragOverlay,
	type DragStartEvent,
	KeyboardSensor,
	PointerSensor,
	closestCenter,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import {
	SortableContext,
	arrayMove,
	rectSortingStrategy,
	useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { cn } from "@ui/lib";
import {
	Check,
	GripVertical,
	Image as ImageIcon,
	Loader2,
	Plus,
	X,
} from "lucide-react";
import { useRef, useState } from "react";

interface EditableImagesProps {
	value: string[];
	isEditing: boolean;
	onEnterEdit: () => void;
	onExitEdit: (save: boolean) => void;
	onValueChange: (images: string[]) => void;
	onImagePreview?: (images: string[], index: number) => void;
	className?: string;
}

// 图片项的状态类型
interface ImageItem {
	url: string;
	isUploading?: boolean;
	uploadProgress?: number;
	isPreview?: boolean; // 是否是本地预览
	file?: File; // 原始文件对象
}

// 可排序的图片项组件
function SortableImageItem({
	imageItem,
	index,
	onRemove,
	isDragOverlay = false,
	isUploading = false,
}: {
	imageItem: ImageItem;
	index: number;
	onRemove: () => void;
	isDragOverlay?: boolean;
	isUploading?: boolean;
}) {
	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging,
	} = useSortable({ id: imageItem.url });

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
		opacity: isDragging ? 0.8 : 1,
	};

	return (
		<div
			ref={setNodeRef}
			style={style}
			className={cn(
				"relative group",
				isDragOverlay && "rotate-3 shadow-xl",
			)}
		>
			{/* 拖拽手柄 - 上传中时禁用 */}
			{!imageItem.isUploading && (
				<div
					className="absolute -top-1 -left-1 z-10 bg-black/20 hover:bg-black/30 backdrop-blur-sm rounded p-1 opacity-0 group-hover:opacity-100 transition-all duration-200 cursor-grab shadow-lg"
					{...attributes}
					{...listeners}
				>
					<GripVertical className="h-3 w-3 text-white drop-shadow-sm" />
				</div>
			)}

			<img
				src={imageItem.url}
				alt={`图片 ${index + 1}`}
				className={cn(
					"h-16 w-full object-cover rounded-md",
					imageItem.isUploading && "opacity-60",
				)}
			/>

			{/* 上传进度覆盖层 */}
			{imageItem.isUploading && (
				<div className="absolute inset-0 bg-black/40 rounded-md flex flex-col items-center justify-center">
					<Loader2 className="h-4 w-4 text-white animate-spin mb-1" />
					<div className="w-3/4 bg-white/20 rounded-full h-1">
						<div
							className="bg-white h-1 rounded-full transition-all duration-300"
							style={{
								width: `${imageItem.uploadProgress || 0}%`,
							}}
						/>
					</div>
					<span className="text-white text-xs mt-1">
						{Math.round(imageItem.uploadProgress || 0)}%
					</span>
				</div>
			)}

			{/* 预览标识 */}
			{imageItem.isPreview && !imageItem.isUploading && (
				<div className="absolute bottom-1 right-1 bg-blue-500 text-white text-xs px-1 rounded">
					预览
				</div>
			)}

			{/* 删除按钮 - 上传中时禁用 */}
			{!imageItem.isUploading && (
				<Button
					variant="error"
					size="icon"
					className="absolute -top-1 -right-1 h-5 w-5 opacity-0 group-hover:opacity-100 transition-opacity"
					onClick={onRemove}
				>
					<X className="h-3 w-3" />
				</Button>
			)}
		</div>
	);
}

export function EditableImages({
	value,
	isEditing,
	onEnterEdit,
	onExitEdit,
	onValueChange,
	onImagePreview,
	className,
}: EditableImagesProps) {
	const [localImageItems, setLocalImageItems] = useState<ImageItem[]>(
		value.map((url) => ({ url })),
	);
	const [newImageUrl, setNewImageUrl] = useState("");
	const [activeId, setActiveId] = useState<string | null>(null);
	const [isUploading, setIsUploading] = useState(false);
	const fileInputRef = useRef<HTMLInputElement>(null);

	// 创建传感器用于拖拽检测
	const sensors = useSensors(
		useSensor(PointerSensor, {
			activationConstraint: {
				distance: 8,
			},
		}),
		useSensor(KeyboardSensor),
	);

	const handleSave = () => {
		// 只保存已上传完成的图片
		const uploadedImages = localImageItems
			.filter((item) => !item.isUploading && !item.isPreview)
			.map((item) => item.url)
			.filter((url) => url.trim());

		onValueChange(uploadedImages);
		onExitEdit(true);
	};

	const handleCancel = () => {
		setLocalImageItems(value.map((url) => ({ url })));
		setNewImageUrl("");
		onExitEdit(false);
	};

	const handleAddImageUrl = () => {
		if (newImageUrl.trim()) {
			setLocalImageItems((prev) => [
				...prev,
				{ url: newImageUrl.trim() },
			]);
			setNewImageUrl("");
		}
	};

	const handleRemoveImage = (index: number) => {
		setLocalImageItems((prev) => prev.filter((_, i) => i !== index));
	};

	// 文件选择处理 - 立即预览
	const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
		const files = e.target.files;
		if (!files || files.length === 0) return;

		// 立即添加预览图片
		const newPreviewItems: ImageItem[] = Array.from(files).map((file) => ({
			url: URL.createObjectURL(file),
			isPreview: true,
			file: file,
		}));

		setLocalImageItems((prev) => [...prev, ...newPreviewItems]);

		// 重置文件输入
		e.target.value = "";
	};

	// 开始上传所有预览图片
	const handleStartUpload = async () => {
		const previewItems = localImageItems.filter((item) => item.isPreview);
		if (previewItems.length === 0) return;

		setIsUploading(true);

		for (let i = 0; i < previewItems.length; i++) {
			const item = previewItems[i];
			if (!item.file) continue;

			try {
				// 更新状态为上传中
				setLocalImageItems((prev) =>
					prev.map((prevItem) =>
						prevItem.url === item.url
							? {
									...prevItem,
									isUploading: true,
									uploadProgress: 0,
								}
							: prevItem,
					),
				);

				// 获取上传 URL
				const presignResponse = await fetch("/api/storage/upload", {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						filename: item.file.name,
						contentType: item.file.type,
					}),
				});

				if (!presignResponse.ok) {
					throw new Error("获取上传 URL 失败");
				}

				const { uploadUrl, permanentUrl } =
					await presignResponse.json();

				// 模拟上传进度
				const updateProgress = (progress: number) => {
					setLocalImageItems((prev) =>
						prev.map((prevItem) =>
							prevItem.url === item.url
								? { ...prevItem, uploadProgress: progress }
								: prevItem,
						),
					);
				};

				// 上传文件
				const uploadResponse = await fetch(uploadUrl, {
					method: "PUT",
					headers: {
						"Content-Type": item.file.type,
					},
					body: item.file,
				});

				if (!uploadResponse.ok) {
					throw new Error("文件上传失败");
				}

				// 上传完成，更新为最终URL
				setLocalImageItems((prev) =>
					prev.map((prevItem) =>
						prevItem.url === item.url
							? {
									url: permanentUrl,
									isUploading: false,
									isPreview: false,
									uploadProgress: 100,
								}
							: prevItem,
					),
				);

				// 清理预览URL
				URL.revokeObjectURL(item.url);
			} catch (error) {
				console.error("图片上传失败:", error);
				// 上传失败，移除该项
				setLocalImageItems((prev) =>
					prev.filter((prevItem) => prevItem.url !== item.url),
				);
				URL.revokeObjectURL(item.url);
			}
		}

		setIsUploading(false);
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		e.stopPropagation();

		if (e.key === "Escape") {
			e.preventDefault();
			handleCancel();
		} else if (e.key === "Enter" && e.target === e.currentTarget) {
			e.preventDefault();
			handleSave();
		}
	};

	// 拖拽开始
	const handleDragStart = (event: DragStartEvent) => {
		setActiveId(event.active.id as string);
	};

	// 拖拽结束
	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;

		if (over && active.id !== over.id) {
			const oldIndex = localImageItems.findIndex(
				(item) => item.url === active.id,
			);
			const newIndex = localImageItems.findIndex(
				(item) => item.url === over.id,
			);

			if (oldIndex !== -1 && newIndex !== -1) {
				const newItems = arrayMove(localImageItems, oldIndex, newIndex);
				setLocalImageItems(newItems);
			}
		}

		setActiveId(null);
	};

	// 检查是否有预览图片
	const hasPreviewImages = localImageItems.some((item) => item.isPreview);
	const hasUploadingImages = localImageItems.some((item) => item.isUploading);

	if (isEditing) {
		return (
			<div className={cn("flex flex-col gap-3 py-2", className)}>
				{/* 当前图片列表 - 支持拖拽排序 */}
				{localImageItems.length > 0 && (
					<DndContext
						sensors={sensors}
						collisionDetection={closestCenter}
						onDragStart={handleDragStart}
						onDragEnd={handleDragEnd}
					>
						<SortableContext
							items={localImageItems.map((item) => item.url)}
							strategy={rectSortingStrategy}
						>
							<div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
								{localImageItems.map((imageItem, index) => (
									<SortableImageItem
										key={imageItem.url}
										imageItem={imageItem}
										index={index}
										onRemove={() =>
											handleRemoveImage(index)
										}
										isUploading={imageItem.isUploading}
									/>
								))}
							</div>
						</SortableContext>

						{/* 拖拽覆盖层 */}
						<DragOverlay>
							{activeId ? (
								<SortableImageItem
									imageItem={
										localImageItems.find(
											(item) => item.url === activeId,
										)!
									}
									index={localImageItems.findIndex(
										(item) => item.url === activeId,
									)}
									onRemove={() => {}}
									isDragOverlay={true}
								/>
							) : null}
						</DragOverlay>
					</DndContext>
				)}

				{/* 添加图片区域 */}
				<div className="space-y-2">
					{/* URL 输入 */}
					<div className="flex items-center gap-2">
						<Input
							value={newImageUrl}
							onChange={(e) => setNewImageUrl(e.target.value)}
							placeholder="输入图片 URL..."
							className="text-sm h-8"
							disabled={isUploading}
							onKeyDown={(e) => {
								if (e.key === "Enter") {
									e.preventDefault();
									handleAddImageUrl();
								}
							}}
						/>
						<Button
							size="sm"
							variant="outline"
							onClick={handleAddImageUrl}
							disabled={!newImageUrl.trim() || isUploading}
							className="h-8 px-2"
						>
							<Plus className="h-3 w-3" />
						</Button>
					</div>

					{/* 文件选择和上传 */}
					<div className="flex items-center gap-2">
						<Button
							size="sm"
							variant="outline"
							onClick={() => fileInputRef.current?.click()}
							disabled={isUploading}
							className="h-8 px-3 text-xs"
						>
							<ImageIcon className="mr-1 h-3 w-3" />
							选择图片
						</Button>

						{hasPreviewImages && (
							<Button
								size="sm"
								onClick={handleStartUpload}
								disabled={isUploading}
								className="h-8 px-3 text-xs"
							>
								{isUploading ? (
									<>
										<Loader2 className="mr-1 h-3 w-3 animate-spin" />
										上传中...
									</>
								) : (
									<>
										<ImageIcon className="mr-1 h-3 w-3" />
										开始上传
									</>
								)}
							</Button>
						)}

						<input
							ref={fileInputRef}
							type="file"
							accept="image/*"
							multiple
							onChange={handleFileSelect}
							className="hidden"
						/>
					</div>
				</div>

				{/* 状态提示 */}
				{hasPreviewImages && !isUploading && (
					<div className="text-xs text-blue-600 bg-blue-50 border border-blue-200 rounded p-2">
						💡 已选择{" "}
						{
							localImageItems.filter((item) => item.isPreview)
								.length
						}{" "}
						张图片，点击"开始上传"按钮上传到服务器
					</div>
				)}

				{hasUploadingImages && (
					<div className="text-xs text-orange-600 bg-orange-50 border border-orange-200 rounded p-2">
						⏳ 正在上传图片，请稍候...
					</div>
				)}

				{/* 编辑提示 */}
				{localImageItems.length > 1 &&
					!hasPreviewImages &&
					!hasUploadingImages && (
						<div className="text-xs text-muted-foreground bg-blue-50 border border-blue-200 rounded p-2">
							💡 提示：悬停图片可看到拖拽手柄，拖拽图片可重新排序
						</div>
					)}

				{/* 操作按钮 */}
				<div className="flex items-center gap-2">
					<Button
						size="sm"
						onClick={handleSave}
						disabled={hasPreviewImages || hasUploadingImages}
						className="h-7 px-3 text-xs"
						onKeyDown={handleKeyDown}
					>
						<Check className="mr-1 h-3 w-3" />
						保存
					</Button>
					<Button
						size="sm"
						variant="outline"
						onClick={handleCancel}
						disabled={hasUploadingImages}
						className="h-7 px-3 text-xs"
						onKeyDown={handleKeyDown}
					>
						<X className="mr-1 h-3 w-3" />
						取消
					</Button>
				</div>
			</div>
		);
	}

	// 非编辑模式 - 显示图片网格
	if (value.length === 0) {
		return (
			<button
				type="button"
				className={cn(
					"flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground cursor-pointer hover:bg-muted/50 rounded px-2 py-1 transition-colors",
					className,
				)}
				onClick={onEnterEdit}
				title="点击添加图片"
			>
				<ImageIcon className="h-4 w-4" />
				添加图片
			</button>
		);
	}

	return (
		<div className={cn("mt-3", className)}>
			<div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
				{value.slice(0, 3).map((image, index) => (
					<button
						key={index}
						type="button"
						className="relative group overflow-hidden rounded-md"
						onClick={(e) => {
							e.stopPropagation();
							if (onImagePreview) {
								onImagePreview(value, index);
							}
						}}
						onContextMenu={(e) => {
							e.preventDefault();
							onEnterEdit();
						}}
						title="左键预览，右键编辑"
					>
						<img
							src={image}
							alt={`图片 ${index + 1}`}
							className="h-16 w-full object-cover rounded-md transition-transform group-hover:scale-105 cursor-pointer"
						/>
						<div className="absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100">
							<span className="text-white text-xs">预览</span>
						</div>
					</button>
				))}
				{value.length > 3 && (
					<button
						type="button"
						className="relative cursor-pointer"
						onClick={(e) => {
							e.stopPropagation();
							if (onImagePreview) {
								onImagePreview(value, 3);
							}
						}}
						onContextMenu={(e) => {
							e.preventDefault();
							onEnterEdit();
						}}
						title="查看所有图片"
					>
						<img
							src={value[3]}
							alt="更多图片"
							className="h-16 w-full object-cover rounded-md"
						/>
						<div className="absolute inset-0 flex items-center justify-center bg-black/40 rounded-md">
							<span className="text-white font-medium">
								+{value.length - 3}
							</span>
						</div>
					</button>
				)}
				{/* 添加图片按钮 */}
				<button
					type="button"
					className="h-16 border-2 border-dashed border-muted-foreground/30 rounded-md flex items-center justify-center hover:border-muted-foreground/60 hover:bg-muted/20 transition-colors"
					onClick={(e) => {
						e.stopPropagation();
						onEnterEdit();
					}}
					title="添加更多图片"
				>
					<Plus className="h-6 w-6 text-muted-foreground" />
				</button>
			</div>
		</div>
	);
}
