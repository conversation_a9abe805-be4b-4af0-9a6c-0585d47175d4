interface DebugInfoProps {
	isOpen: boolean;
}

export function DebugInfo({ isOpen }: DebugInfoProps) {
	// 仅在开发环境下显示调试信息
	if (process.env.NODE_ENV !== "development") {
		return null;
	}

	return (
		<div className="mt-4 pt-2 border-t border-border/40 text-xs text-muted-foreground">
			<details>
				<summary className="cursor-pointer hover:text-primary">
					调试信息
				</summary>
				<div className="mt-2 space-y-1">
					<p>
						Google API状态:{" "}
						{typeof window !== "undefined" && window.google
							? "✅"
							: "❌"}{" "}
						已加载
					</p>
					<p>
						Maps API状态:{" "}
						{typeof window !== "undefined" && window.google?.maps
							? "✅"
							: "❌"}{" "}
						已加载
					</p>
					<p>
						Places API状态:{" "}
						{typeof window !== "undefined" &&
						window.google?.maps?.places
							? "✅"
							: "❌"}{" "}
						已加载
					</p>
					<button
						className="mt-2 px-2 py-1 bg-primary/10 text-primary rounded-sm text-[10px]"
						onClick={() => {
							if (typeof window !== "undefined") {
								console.log("调试: 窗口状态", {
									isOpen,
									google: Boolean(window.google),
									maps: Boolean(window.google?.maps),
									places: Boolean(
										window.google?.maps?.places,
									),
								});
							}
						}}
					>
						打印状态到控制台
					</button>
				</div>
			</details>
		</div>
	);
}
