/**
 * 判断当前索引是否是时间线的第一个点位
 */
export function isNewTimelineFirstPoint(index: number, diary: any): boolean {
	let pointCount = 0;

	for (let i = 0; i < diary.timelines.length; i++) {
		if (pointCount === index && index > 0) {
			return true;
		}

		pointCount += diary.timelines[i].points.length;
	}

	return false;
}

/**
 * 根据当前足迹索引计算活动的时间线索引
 */
export function calculateActiveTimelineIndex(
	currentFootprintIndex: number,
	diary: any,
): number {
	let count = 0;
	let foundIndex = 0;

	for (let i = 0; i < diary.timelines.length; i++) {
		const pointsCount = diary.timelines[i].points.length;
		if (currentFootprintIndex < count + pointsCount) {
			foundIndex = i;
			break;
		}
		count += pointsCount;
	}

	return foundIndex;
}
