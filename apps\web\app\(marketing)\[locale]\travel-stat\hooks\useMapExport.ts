"use client";

import { useCallback, useState } from "react";
import type { CountryData, TravelPoint } from "../types";

interface UseMapExportOptions {
	mapRef: any;
	travelPoints: TravelPoint[];
	visitedCountries: CountryData[];
	mapLoaded: boolean;
}

interface ExportStatus {
	isExporting: boolean;
	status: "idle" | "success" | "error";
	message: string;
}

export function useMapExport({
	mapRef,
	travelPoints,
	visitedCountries,
	mapLoaded,
}: UseMapExportOptions) {
	const [exportStatus, setExportStatus] = useState<ExportStatus>({
		isExporting: false,
		status: "idle",
		message: "",
	});

	const exportMap = useCallback(
		async (
			format: "png" | "jpeg" = "png",
			options: {
				quality?: number;
				scale?: number;
				addWatermark?: boolean;
				addStats?: boolean;
				filename?: string;
			} = {},
		) => {
			if (!mapRef?.current || !mapLoaded) {
				setExportStatus({
					isExporting: false,
					status: "error",
					message: "地图未加载完成，请稍后再试",
				});
				return;
			}

			const {
				quality = 1.0,
				scale = 1,
				addWatermark = true,
				addStats = true,
				filename = `travel_map_${Date.now()}.${format}`,
			} = options;

			setExportStatus({
				isExporting: true,
				status: "idle",
				message: "正在导出地图...",
			});

			try {
				const map = mapRef.current.getMap();

				// 等待地图渲染完成
				await new Promise((resolve) => {
					if (map.loaded()) {
						resolve(true);
					} else {
						map.once("idle", resolve);
					}
				});

				const canvas = map.getCanvas();
				const dataURL = canvas.toDataURL(
					format === "png" ? "image/png" : "image/jpeg",
					quality,
				);

				// 如果需要高分辨率或覆盖层，创建新画布
				if (scale > 1 || addStats || addWatermark) {
					const exportCanvas = document.createElement("canvas");
					exportCanvas.width = canvas.width * scale;
					exportCanvas.height = canvas.height * scale;

					const ctx = exportCanvas.getContext("2d");
					if (!ctx) {
						throw new Error("无法创建画布上下文");
					}

					// 绘制地图
					ctx.scale(scale, scale);
					const img = new Image();
					img.onload = () => {
						ctx.drawImage(img, 0, 0);

						// 添加覆盖层（这里需要实现具体的绘制逻辑）
						// ...

						const finalDataURL = exportCanvas.toDataURL(
							format === "png" ? "image/png" : "image/jpeg",
							quality,
						);
						downloadImage(finalDataURL, filename);
					};
					img.src = dataURL;
				} else {
					// 直接下载原始图片
					downloadImage(dataURL, filename);
				}

				setExportStatus({
					isExporting: false,
					status: "success",
					message: `成功导出 ${filename}`,
				});
			} catch (error) {
				console.error("导出地图失败:", error);
				setExportStatus({
					isExporting: false,
					status: "error",
					message:
						error instanceof Error ? error.message : "导出失败",
				});
			}
		},
		[mapRef, mapLoaded, travelPoints, visitedCountries],
	);

	const clearStatus = useCallback(() => {
		setExportStatus({
			isExporting: false,
			status: "idle",
			message: "",
		});
	}, []);

	return {
		exportStatus,
		exportMap,
		clearStatus,
	};
}

// 辅助函数：下载图片
function downloadImage(dataURL: string, filename: string) {
	const link = document.createElement("a");
	link.download = filename;
	link.href = dataURL;
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link);
}
