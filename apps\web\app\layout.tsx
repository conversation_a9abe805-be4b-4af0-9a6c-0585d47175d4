import "cropperjs/dist/cropper.css";
import type { Metadata } from "next";
import type { PropsWithChildren } from "react";
import "../styles/artistic-scrollbar.css";
import "../styles/blog-gallery.css";
import "../styles/bubble-animations.css";
import "../styles/decorative-elements.css";
import "../styles/fonts.css";
import "../styles/ink-elements.css";
import "../styles/literary-animations.css";
import "../styles/literary-elements.css";
import "../styles/literary-typography.css";
import "./globals.css";

export const metadata: Metadata = {
	title: {
		absolute: "mapmpment.app - Application",
		default: "mapmpment.app- Application",
		template: "%s | mapmpment.app - Application",
	},
};

export default function RootLayout({ children }: PropsWithChildren) {
	return children;
}
