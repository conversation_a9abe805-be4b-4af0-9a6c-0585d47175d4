"use client";

import { useCallback, useEffect, useRef, useState } from "react";

interface AnimationStep {
	id: string;
	duration: number; // 毫秒
	onStart?: () => void;
	onComplete?: () => void;
}

interface AnimationSequencerProps {
	steps: AnimationStep[];
	autoStart?: boolean;
	loop?: boolean;
	onComplete?: () => void;
	children?: React.ReactNode;
}

export function AnimationSequencer({
	steps,
	autoStart = false,
	loop = false,
	onComplete,
	children,
}: AnimationSequencerProps) {
	const [currentStepIndex, setCurrentStepIndex] = useState(-1); // -1表示未开始
	const [isPlaying, setIsPlaying] = useState(false);
	const [isPaused, setIsPaused] = useState(false);
	const timerRef = useRef<NodeJS.Timeout | null>(null);
	const remainingTimeRef = useRef<number>(0);
	const startTimeRef = useRef<number>(0);

	// 清理当前计时器
	const clearTimer = useCallback(() => {
		if (timerRef.current) {
			clearTimeout(timerRef.current);
			timerRef.current = null;
		}
	}, []);

	// 启动序列
	const startSequence = useCallback(() => {
		if (steps.length === 0) return;

		clearTimer();
		setCurrentStepIndex(0);
		setIsPlaying(true);
		setIsPaused(false);
	}, [steps.length, clearTimer]);

	// 暂停序列
	const pauseSequence = useCallback(() => {
		if (!isPlaying || isPaused) return;

		// 计算剩余时间
		const elapsedTime = Date.now() - startTimeRef.current;
		remainingTimeRef.current = Math.max(
			0,
			steps[currentStepIndex]?.duration - elapsedTime,
		);

		clearTimer();
		setIsPaused(true);
	}, [isPlaying, isPaused, clearTimer, currentStepIndex, steps]);

	// 恢复序列
	const resumeSequence = useCallback(() => {
		if (!isPlaying || !isPaused) return;

		startTimeRef.current =
			Date.now() -
			(steps[currentStepIndex]?.duration - remainingTimeRef.current);
		scheduleNextStep(remainingTimeRef.current);
		setIsPaused(false);
	}, [isPlaying, isPaused, currentStepIndex, steps]);

	// 停止序列
	const stopSequence = useCallback(() => {
		clearTimer();
		setCurrentStepIndex(-1);
		setIsPlaying(false);
		setIsPaused(false);
	}, [clearTimer]);

	// 跳转到特定步骤
	const jumpToStep = useCallback(
		(stepIndex: number) => {
			if (stepIndex < 0 || stepIndex >= steps.length) return;

			clearTimer();
			setCurrentStepIndex(stepIndex);
			setIsPlaying(true);
			setIsPaused(false);
		},
		[steps.length, clearTimer],
	);

	// 调度下一个步骤
	const scheduleNextStep = useCallback(
		(delay: number) => {
			clearTimer();

			if (currentStepIndex < 0 || currentStepIndex >= steps.length)
				return;

			timerRef.current = setTimeout(() => {
				// 当前步骤结束，执行onComplete回调
				steps[currentStepIndex]?.onComplete?.();

				// 移动到下一个步骤
				const nextStepIndex = currentStepIndex + 1;

				if (nextStepIndex >= steps.length) {
					// 序列结束
					if (loop) {
						// 如果循环，回到第一步
						setCurrentStepIndex(0);
					} else {
						// 否则完成序列
						setCurrentStepIndex(-1);
						setIsPlaying(false);
						onComplete?.();
						return;
					}
				} else {
					// 继续下一步
					setCurrentStepIndex(nextStepIndex);
				}
			}, delay);
		},
		[currentStepIndex, steps, loop, onComplete, clearTimer],
	);

	// 监听步骤变化，开始下一个步骤
	useEffect(() => {
		if (
			!isPlaying ||
			isPaused ||
			currentStepIndex < 0 ||
			currentStepIndex >= steps.length
		)
			return;

		// 执行当前步骤的onStart回调
		steps[currentStepIndex].onStart?.();

		// 记录开始时间
		startTimeRef.current = Date.now();

		// 调度下一个步骤
		scheduleNextStep(steps[currentStepIndex].duration);

		return () => clearTimer();
	}, [
		currentStepIndex,
		isPlaying,
		isPaused,
		steps,
		scheduleNextStep,
		clearTimer,
	]);

	// 自动开始
	useEffect(() => {
		if (autoStart) {
			startSequence();
		}

		return () => clearTimer();
	}, [autoStart, startSequence, clearTimer]);

	return <div className="animation-sequencer">{children}</div>;
}
