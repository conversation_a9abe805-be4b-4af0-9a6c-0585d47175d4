"use client";

import {
	Float,
	MeshDistortMaterial,
	OrbitControls,
	Sphere,
	Stars,
} from "@react-three/drei";
import { Canvas, useFrame } from "@react-three/fiber";
import { useEffect, useMemo, useRef, useState } from "react";
import type * as THREE from "three";

// 粒子星空类型定义
export type ThreeJsTheme =
	| "starfield"
	| "galaxy"
	| "nebula"
	| "cosmos"
	| "aurora"
	| "wormhole"
	| "travel-constellation";

interface ThreeJsBackgroundProps {
	theme?: ThreeJsTheme;
	className?: string;
	interactive?: boolean;
}

// 浮动粒子云组件 - 简化版本
function ParticleCloud({
	count = 500,
	theme = "starfield",
}: { count?: number; theme?: ThreeJsTheme }) {
	const groupRef = useRef<THREE.Group>(null);

	// 使用简单的球体作为粒子，避免复杂的 bufferAttribute 配置
	const particles = useMemo(() => {
		return Array.from({ length: count }, (_, i) => ({
			id: i,
			x: (Math.random() - 0.5) * 100,
			y: (Math.random() - 0.5) * 100,
			z: (Math.random() - 0.5) * 100,
			scale: Math.random() * 0.5 + 0.2,
			speed: Math.random() * 0.02 + 0.01,
		}));
	}, [count]);

	// 根据主题选择颜色
	const getParticleColor = () => {
		switch (theme) {
			case "nebula":
				return ["#ff6b9d", "#c44569", "#f8b500"];
			case "aurora":
				return ["#00ff88", "#0088ff", "#ff0088"];
			case "galaxy":
				return ["#ffffff", "#87ceeb", "#4169e1"];
			case "cosmos":
				return ["#ff6b9d", "#4a69bd", "#ffffff"];
			default:
				return ["#ffffff", "#87ceeb"];
		}
	};

	const colors = getParticleColor();

	useFrame((state) => {
		if (groupRef.current) {
			groupRef.current.rotation.y = state.clock.elapsedTime * 0.02;
			groupRef.current.rotation.x =
				Math.sin(state.clock.elapsedTime * 0.05) * 0.1;
		}
	});

	return (
		<group ref={groupRef}>
			{particles.map((particle) => (
				<mesh
					key={particle.id}
					position={[particle.x, particle.y, particle.z]}
					scale={particle.scale}
				>
					<sphereGeometry args={[0.2, 8, 8]} />
					<meshBasicMaterial
						color={colors[particle.id % colors.length]}
						transparent
						opacity={0.6}
					/>
				</mesh>
			))}
		</group>
	);
}

// 旋转星环组件
function RotatingRings({ theme }: { theme: ThreeJsTheme }) {
	const ring1 = useRef<THREE.Mesh>(null);
	const ring2 = useRef<THREE.Mesh>(null);
	const ring3 = useRef<THREE.Mesh>(null);

	useFrame((state) => {
		if (ring1.current)
			ring1.current.rotation.z = state.clock.elapsedTime * 0.5;
		if (ring2.current)
			ring2.current.rotation.z = -state.clock.elapsedTime * 0.3;
		if (ring3.current)
			ring3.current.rotation.z = state.clock.elapsedTime * 0.7;
	});

	const ringColor =
		theme === "aurora"
			? "#00ff88"
			: theme === "nebula"
				? "#8844ff"
				: "#ffffff";

	return (
		<group>
			<mesh ref={ring1}>
				<torusGeometry args={[8, 0.1, 8, 100]} />
				<meshBasicMaterial
					color={ringColor}
					transparent
					opacity={0.3}
				/>
			</mesh>
			<mesh ref={ring2}>
				<torusGeometry args={[12, 0.05, 8, 100]} />
				<meshBasicMaterial
					color={ringColor}
					transparent
					opacity={0.2}
				/>
			</mesh>
			<mesh ref={ring3}>
				<torusGeometry args={[16, 0.03, 8, 100]} />
				<meshBasicMaterial
					color={ringColor}
					transparent
					opacity={0.1}
				/>
			</mesh>
		</group>
	);
}

// 流星组件
function ShootingStars() {
	const starsRef = useRef<THREE.Group>(null);

	const stars = useMemo(() => {
		return Array.from({ length: 20 }, (_, i) => ({
			id: i,
			x: (Math.random() - 0.5) * 100,
			y: (Math.random() - 0.5) * 100,
			z: (Math.random() - 0.5) * 100,
			speed: Math.random() * 0.5 + 0.1,
			length: Math.random() * 5 + 2,
		}));
	}, []);

	useFrame((state) => {
		if (starsRef.current) {
			starsRef.current.children.forEach((star, i) => {
				const starData = stars[i];
				star.position.x += starData.speed;
				star.position.y -= starData.speed * 0.5;

				// 重置位置当流星离开视野
				if (star.position.x > 50) {
					star.position.x = -50;
					star.position.y = Math.random() * 50 - 25;
					star.position.z = Math.random() * 50 - 25;
				}
			});
		}
	});

	return (
		<group ref={starsRef}>
			{stars.map((star) => (
				<mesh key={star.id} position={[star.x, star.y, star.z]}>
					<sphereGeometry args={[0.1]} />
					<meshBasicMaterial color="white" />
				</mesh>
			))}
		</group>
	);
}

// 虫洞效果组件
function Wormhole() {
	const meshRef = useRef<THREE.Mesh>(null);

	useFrame((state) => {
		if (meshRef.current) {
			meshRef.current.rotation.z = state.clock.elapsedTime * 0.5;
			meshRef.current.rotation.x = state.clock.elapsedTime * 0.1;
		}
	});

	return (
		<Float speed={2} rotationIntensity={1} floatIntensity={2}>
			<Sphere ref={meshRef} args={[2, 64, 64]}>
				<MeshDistortMaterial
					color="#8844ff"
					attach="material"
					distort={0.6}
					speed={2}
					roughness={0.1}
					transparent
					opacity={0.8}
				/>
			</Sphere>
		</Float>
	);
}

// 旅行星座组件 - 简化版本
function TravelConstellation() {
	const groupRef = useRef<THREE.Group>(null);

	const constellationPoints = useMemo(() => {
		return [
			{ x: -5, y: 3, z: 0 },
			{ x: -2, y: 5, z: 0 },
			{ x: 2, y: 4, z: 0 },
			{ x: 5, y: 2, z: 0 },
			{ x: 3, y: -2, z: 0 },
			{ x: 0, y: -4, z: 0 },
			{ x: -3, y: -3, z: 0 },
		];
	}, []);

	useFrame((state) => {
		if (groupRef.current) {
			groupRef.current.rotation.y = state.clock.elapsedTime * 0.2;
		}
	});

	return (
		<group ref={groupRef}>
			{/* 星座点 */}
			{constellationPoints.map((point, i) => (
				<Float
					key={i}
					speed={1 + i * 0.1}
					rotationIntensity={0.5}
					floatIntensity={0.5}
				>
					<mesh position={[point.x, point.y, point.z]}>
						<sphereGeometry args={[0.15, 16, 16]} />
						<meshBasicMaterial color="#ffaa00" />
					</mesh>
				</Float>
			))}

			{/* 连接线 - 使用简单的圆柱体代替复杂的lineSegments */}
			{constellationPoints.map((point, i) => {
				const nextPoint =
					constellationPoints[(i + 1) % constellationPoints.length];
				const distance = Math.sqrt(
					Math.pow(nextPoint.x - point.x, 2) +
						Math.pow(nextPoint.y - point.y, 2) +
						Math.pow(nextPoint.z - point.z, 2),
				);
				const midPoint: [number, number, number] = [
					(point.x + nextPoint.x) / 2,
					(point.y + nextPoint.y) / 2,
					(point.z + nextPoint.z) / 2,
				];

				return (
					<mesh
						key={`line-${i}`}
						position={midPoint}
						lookAt={[nextPoint.x, nextPoint.y, nextPoint.z]}
					>
						<cylinderGeometry args={[0.02, 0.02, distance, 8]} />
						<meshBasicMaterial
							color="#ffaa00"
							transparent
							opacity={0.6}
						/>
					</mesh>
				);
			})}
		</group>
	);
}

// 主场景组件
function Scene({
	theme,
	interactive,
}: { theme: ThreeJsTheme; interactive: boolean }) {
	return (
		<>
			{/* 环境光 */}
			<ambientLight intensity={0.2} />
			<pointLight position={[10, 10, 10]} intensity={0.5} />

			{/* 背景星空 */}
			<Stars
				radius={300}
				depth={60}
				count={theme === "cosmos" ? 8000 : 5000}
				factor={theme === "galaxy" ? 10 : 6}
				saturation={0}
				fade={true}
			/>

			{/* 粒子云 */}
			<ParticleCloud
				count={theme === "cosmos" ? 2000 : 1000}
				theme={theme}
			/>

			{/* 根据主题显示不同效果 */}
			{theme === "galaxy" && <RotatingRings theme={theme} />}
			{theme === "nebula" && <RotatingRings theme={theme} />}
			{theme === "aurora" && <RotatingRings theme={theme} />}
			{theme === "starfield" && <ShootingStars />}
			{theme === "wormhole" && <Wormhole />}
			{theme === "travel-constellation" && <TravelConstellation />}

			{/* 交互控制 */}
			{interactive && (
				<OrbitControls
					enableZoom={false}
					enablePan={false}
					autoRotate
					autoRotateSpeed={0.5}
				/>
			)}

			{/* 环境光照 - 移除有问题的 Environment 预设 */}
			{/* <Environment preset="night" /> */}
		</>
	);
}

export function ThreeJsBackground({
	theme = "starfield",
	className = "",
	interactive = false,
}: ThreeJsBackgroundProps) {
	const [mounted, setMounted] = useState(false);

	// 确保只在客户端渲染
	useEffect(() => {
		setMounted(true);
	}, []);

	if (!mounted) return null;

	return (
		<div className={`absolute inset-0 pointer-events-none ${className}`}>
			<Canvas
				camera={{
					position: [0, 0, 10],
					fov: 75,
					near: 0.1,
					far: 1000,
				}}
				style={{
					background: "transparent",
					pointerEvents: "none",
				}}
				gl={{
					antialias: true,
					alpha: true,
					premultipliedAlpha: false,
				}}
			>
				<Scene theme={theme} interactive={interactive} />
			</Canvas>
		</div>
	);
}

// 主题预设
export const THREEJS_THEMES: Record<ThreeJsTheme, string> = {
	starfield: "经典星空",
	galaxy: "银河系",
	nebula: "星云",
	cosmos: "宇宙深空",
	aurora: "极光",
	wormhole: "虫洞",
	"travel-constellation": "旅行星座",
};
