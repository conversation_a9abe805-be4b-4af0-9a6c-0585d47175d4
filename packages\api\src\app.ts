import { getBaseUrl } from "@repo/utils";
import { apiReference } from "@scalar/hono-api-reference";
import { Hono } from "hono";
import { openAPISpecs } from "hono-openapi";
import { corsMiddleware } from "./middleware/cors";
import { loggerMiddleware } from "./middleware/logger";
import { adminRouter } from "./routes/admin/router";
import { aiRouter } from "./routes/ai";
import { authRouter } from "./routes/auth";
import { contactRouter } from "./routes/contact/router";
import { diariesRouter } from "./routes/diaries/router";
import { featureRequestRouter } from "./routes/feature-requests";
import { healthRouter } from "./routes/health";
import { newsletterRouter } from "./routes/newsletter";
import { organizationsRouter } from "./routes/organizations/router";
import { paymentsRouter } from "./routes/payments/router";
import { storageRouter } from "./routes/storage"; // Import the new storage router
import { uploadsRouter } from "./routes/uploads";
import { webhooksRouter } from "./routes/webhooks";

export const app = new Hono().basePath("/api");

app.use(loggerMiddleware);
app.use(corsMiddleware);

// 使用明确的路径注册每个路由器，避免路由冲突
const appRouter = app
	.route("/auth", authRouter)
	.route("/webhooks", webhooksRouter)
	.route("/ai", aiRouter)
	.route("/uploads", uploadsRouter)
	.route("/payments", paymentsRouter)
	.route("/contact", contactRouter)
	.route("/newsletter", newsletterRouter)
	.route("/organizations", organizationsRouter)
	.route("/admin", adminRouter)
	.route("/diaries", diariesRouter)
	.route("/storage", storageRouter) // Register the storage router
	.route("/", featureRequestRouter) // Register the feature requests router
	.route("/health", healthRouter);

app.get(
	"/openapi",
	openAPISpecs(app, {
		documentation: {
			info: {
				title: "supastarter API",
				version: "1.0.0",
			},
			servers: [
				{
					url: getBaseUrl(),
					description: "API server",
				},
			],
		},
	}),
);

app.get(
	"/docs",
	apiReference({
		theme: "saturn",
		spec: {
			url: "/api/openapi",
		},
	}),
);

export type AppRouter = typeof appRouter;
