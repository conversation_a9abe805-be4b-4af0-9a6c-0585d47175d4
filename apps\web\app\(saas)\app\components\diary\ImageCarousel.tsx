"use client";

import { But<PERSON> } from "@ui/components/button";
import { Dialog, DialogContent } from "@ui/components/dialog";
import { cn } from "@ui/lib";
import { ChevronLeft, ChevronRight, X } from "lucide-react";
import { useEffect, useState } from "react";

interface ImageCarouselProps {
	images: string[];
	locationName: string;
	initialIndex?: number;
	isOpen: boolean;
	onOpenChange: (open: boolean) => void;
}

export function ImageCarousel({
	images,
	locationName,
	initialIndex = 0,
	isOpen,
	onOpenChange,
}: ImageCarouselProps) {
	const [currentIndex, setCurrentIndex] = useState(initialIndex);

	// 确保当打开时重置当前索引为初始索引
	useEffect(() => {
		if (isOpen && images.length > 0) {
			setCurrentIndex(initialIndex < images.length ? initialIndex : 0);
		}
	}, [isOpen, initialIndex, images]);

	// 确保有图片可显示
	if (!images || images.length === 0) {
		return null;
	}

	const handlePrevious = () => {
		setCurrentIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
	};

	const handleNext = () => {
		setCurrentIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
	};

	return (
		<Dialog open={isOpen} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-3xl p-0 bg-black/95 border-none overflow-hidden">
				<div className="relative">
					<div className="absolute right-2 top-2 z-10">
						<Button
							variant="ghost"
							size="icon"
							onClick={() => onOpenChange(false)}
							className="text-white hover:bg-white/20"
						>
							<X className="h-5 w-5" />
						</Button>
					</div>

					<div className="h-[70vh] flex items-center justify-center p-4">
						{/* 添加图片加载状态 */}
						<div className="relative w-full h-full flex items-center justify-center">
							<img
								src={images[currentIndex]}
								alt={`${locationName} 图片 ${currentIndex + 1}`}
								className="max-h-full max-w-full object-contain"
								onError={(e) => {
									// 图片加载错误的处理
									e.currentTarget.src =
										"https://placehold.co/600x400?text=加载失败";
								}}
							/>
						</div>
					</div>

					{/* 导航按钮 - 仅当有多张图片时显示 */}
					{images.length > 1 && (
						<>
							<div className="absolute inset-y-0 left-0 flex items-center">
								<Button
									variant="ghost"
									size="icon"
									onClick={handlePrevious}
									className="text-white hover:bg-white/20 h-12 w-12 rounded-full"
								>
									<ChevronLeft className="h-6 w-6" />
								</Button>
							</div>

							<div className="absolute inset-y-0 right-0 flex items-center">
								<Button
									variant="ghost"
									size="icon"
									onClick={handleNext}
									className="text-white hover:bg-white/20 h-12 w-12 rounded-full"
								>
									<ChevronRight className="h-6 w-6" />
								</Button>
							</div>

							{/* 图片计数器 */}
							<div className="absolute bottom-2 left-0 right-0 flex justify-center">
								<div className="bg-black/60 px-3 py-1 rounded-full text-white text-sm">
									{currentIndex + 1} / {images.length}
								</div>
							</div>

							{/* 缩略图导航 */}
							<div className="absolute bottom-12 left-0 right-0">
								<div className="flex justify-center gap-1 px-4 overflow-x-auto py-2 scrollbar-travel">
									{images.map((image, index) => (
										<button
											key={index}
											type="button"
											onClick={() =>
												setCurrentIndex(index)
											}
											className={cn(
												"h-12 w-16 flex-shrink-0 overflow-hidden rounded border-2 transition-all",
												currentIndex === index
													? "border-white opacity-100"
													: "border-transparent opacity-60 hover:opacity-100",
											)}
										>
											<img
												src={image}
												alt={`缩略图 ${index + 1}`}
												className="h-full w-full object-cover"
												onError={(e) => {
													e.currentTarget.src =
														"https://placehold.co/100x100?text=错误";
												}}
											/>
										</button>
									))}
								</div>
							</div>
						</>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}
