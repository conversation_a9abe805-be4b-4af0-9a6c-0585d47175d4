# MapMoment - 多服务旅行应用平台

基于 Monorepo 架构的旅行应用平台，包含旅行日记、AI图片生成和视频导出等多个服务。

## 🚀 服务架构

| 服务 | 描述 | 端口 | 状态 |
|------|------|------|------|
| **MapMoment** | 旅行日记主应用 | 3000 | ✅ 可用 |
| **AI Images** | AI图片生成器 | 3001 | ✅ 可用 |
| **Video Exporter** | 视频导出服务 | 后台 | ✅ 可用 |

## ✨ 特性

### 旅行日记 (MapMoment)
- 创建和编辑旅行日记
- 管理旅行时间线和地点
- 支持图片上传和地图集成
- 富文本编辑功能
  - 支持粗体、斜体、标题、列表等基本格式
  - 支持图片和链接插入
  - 自定义旅行点位节点
  - 在富文本和传统编辑模式之间无缝切换

### AI图片生成器
- 基于AI的图片生成
- 多种风格和尺寸选择
- 图片收藏和分享功能
- 模板管理系统

### 视频导出服务
- 旅行日记视频导出
- 后台队列处理
- 多种输出格式支持

## 🛠️ 快速开始

### 安装依赖

```bash
pnpm install
```

### 启动服务

```bash
# 启动单个服务
pnpm dev:mapmoment     # 旅行日记主应用 (http://localhost:3000)
pnpm dev:ai-images     # AI图片生成器 (http://localhost:3001)
pnpm dev:video-exporter # 视频导出服务 (后台)

# 启动所有服务
pnpm dev:all
```

### 数据库设置

```bash
# 生成数据库类型
pnpm db:generate

# 运行数据库迁移
pnpm db:migrate --name "init"

# 打开数据库管理界面
pnpm db:studio
```

## 📋 详细文档

- [🚀 服务管理指南](./SERVICES.md) - 完整的服务启动和管理说明
- [🗄️ 多数据库配置](./packages/database/README-multi-db.md) - 数据库架构和迁移指南
- [⚡ 快速参考](./packages/database/QUICK-REFERENCE.md) - 常用命令速查

## 🏗️ 项目结构

```
mapmoment/
├── apps/
│   ├── web/                    # MapMoment 主应用
│   └── ai-image-generator/     # AI图片生成器
├── packages/
│   ├── api/                    # API路由和业务逻辑
│   ├── auth/                   # 身份认证
│   ├── database/               # 数据库层
│   ├── ai/                     # AI功能
│   ├── storage/                # 文件存储
│   ├── payments/               # 支付处理
│   ├── mail/                   # 邮件服务
│   ├── video-exporter/         # 视频导出服务
│   └── utils/                  # 工具函数
└── tooling/                    # 构建工具配置
```

## 🔧 环境配置

复制环境变量模板：

```bash
cp .env.example .env.local
```

配置必要的环境变量：

```bash
# 数据库
DATABASE_URL="postgresql://localhost:5432/travel_memo"
AI_IMAGES_DATABASE_URL="postgresql://localhost:5432/ai_images"

# API密钥
OPENAI_API_KEY="your-openai-key"
NEXTAUTH_SECRET="your-secret"

# 存储配置
STORAGE_PROVIDER="s3"
AWS_ACCESS_KEY_ID="your-key"
AWS_SECRET_ACCESS_KEY="your-secret"
```

## 🚀 部署

### 构建所有服务

```bash
pnpm build:all
```

### 生产环境启动

```bash
pnpm start:all
```

### 单独部署

```bash
# 构建单个服务
pnpm build:mapmoment
pnpm build:ai-images
pnpm build:video-exporter

# 启动单个服务
pnpm start:mapmoment
pnpm start:ai-images
pnpm start:video-exporter
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

---

## 技术栈

- **前端**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **后端**: Hono.js, Prisma, PostgreSQL
- **AI**: OpenAI API
- **存储**: AWS S3 / Cloudflare R2 / 腾讯云COS
- **认证**: Better Auth
- **支付**: Stripe / LemonSqueezy
- **构建**: Turbo, pnpm workspace

## 相关链接

- [📘 Supastarter 文档](https://supastarter.dev/docs/nextjs)
- [🚀 在线演示](https://demo.supastarter.dev)
- [💬 Discord 社区](https://discord.gg/BZDNtf8hqt)
