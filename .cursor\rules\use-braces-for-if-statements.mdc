---
description: 
globs: *.ts,*.tsx
alwaysApply: false
---
# Always Use Braces for If Statements

## Rule

All `if` statements, even single-line ones, must use curly braces `{}` to enclose their statement body.

## Rationale

Using braces for all `if` statements improves code readability and maintainability. It prevents common errors that can arise when adding more lines to an initially single-line `if` body without also adding the braces. This practice leads to more consistent and less error-prone code.

## Examples

### Bad

```typescript
if (condition) doSomething();

if (condition)
  doSomething();
```

### Good

```typescript
if (condition) {
  doSomething();
}

if (otherCondition) {
  doSomethingElse();
  doAnotherThing();
}
```

## Enforcement

This rule should be followed in all TypeScript/JavaScript code within the project. Linters should ideally be configured to enforce this, but manual adherence is expected regardless.

