"use client";

import { useCallback, useEffect, useState } from "react";
import type { TravelPoint } from "../types";

interface TooltipState {
	point: TravelPoint | null;
	mousePosition: { x: number; y: number } | null;
	isVisible: boolean;
}

export function useTooltip(enabled = true) {
	const [tooltipState, setTooltipState] = useState<TooltipState>({
		point: null,
		mousePosition: null,
		isVisible: false,
	});

	// 当tooltip被禁用时，自动隐藏当前显示的tooltip
	useEffect(() => {
		if (!enabled && tooltipState.isVisible) {
			setTooltipState({
				point: null,
				mousePosition: null,
				isVisible: false,
			});
		}
	}, [enabled, tooltipState.isVisible]);

	// 显示tooltip - 只有在启用时才显示
	const showTooltip = useCallback(
		(point: TravelPoint, mousePosition: { x: number; y: number }) => {
			if (!enabled) {
				return;
			}

			setTooltipState({
				point,
				mousePosition,
				isVisible: true,
			});
		},
		[enabled],
	);

	// 隐藏tooltip
	const hideTooltip = useCallback(() => {
		setTooltipState({
			point: null,
			mousePosition: null,
			isVisible: false,
		});
	}, []);

	// 更新鼠标位置（用于智能定位）- 只有在启用时才更新
	const updateMousePosition = useCallback(
		(mousePosition: { x: number; y: number }) => {
			if (!enabled) return;

			setTooltipState((prev) =>
				prev.isVisible
					? {
							...prev,
							mousePosition,
						}
					: prev,
			);
		},
		[enabled],
	);

	return {
		tooltipState,
		showTooltip,
		hideTooltip,
		updateMousePosition,
	};
}

export type UseTooltipReturn = ReturnType<typeof useTooltip>;
