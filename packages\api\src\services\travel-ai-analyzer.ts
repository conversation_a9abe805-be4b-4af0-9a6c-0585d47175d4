import {
	type AIClient,
	createOpenAIClient,
	createVolcengineClient,
} from "@repo/ai";
import { logger } from "@repo/logs";
import { GeocodingService } from "./geocoding-service";

/**
 * AI分析的旅行信息
 */
export interface TravelInfo {
	/** 地点信息 */
	locations: LocationInfo[];
	/** 时间信息 */
	timeInfo: TimeInfo[];
	/** 活动描述 */
	activities: ActivityInfo[];
	/** 整体旅行描述 */
	summary: string;
}

/**
 * AI配置选项
 */
export interface AIAnalyzerConfig {
	/** AI提供商类型 */
	provider?: "openai" | "volcengine";
	/** API密钥 */
	apiKey?: string;
	/** 火山引擎秘钥（仅火山引擎需要） */
	secretKey?: string;
	/** API端点 */
	apiEndpoint?: string;
	/** 模型名称 */
	model?: string;
}

/**
 * 地点信息
 */
export interface LocationInfo {
	/** 地点名称 */
	name: string;
	/** 地点类型（城市、景点、餐厅等） */
	type:
		| "city"
		| "attraction"
		| "restaurant"
		| "hotel"
		| "transport"
		| "other";
	/** 置信度 (0-1) */
	confidence: number;
	/** 相关描述 */
	description?: string;
	/** 在原文中的位置 */
	textPosition?: number;
	/** 坐标 */
	coordinates?: {
		longitude: number;
		latitude: number;
	};
	/** 地址 */
	address?: string;
	/** 国家 */
	country?: string;
	/** 省份 */
	province?: string;
	/** 城市 */
	city?: string;
	/** 区县 */
	district?: string;
}

/**
 * 时间信息
 */
export interface TimeInfo {
	/** 时间描述 */
	description: string;
	/** 推断的日期（ISO格式） */
	inferredDate?: string;
	/** 时间类型 */
	type: "absolute" | "relative" | "duration";
	/** 置信度 (0-1) */
	confidence: number;
	/** 在原文中的位置 */
	textPosition?: number;
}

/**
 * 活动信息
 */
export interface ActivityInfo {
	/** 活动描述 */
	description: string;
	/** 活动类型 */
	type:
		| "sightseeing"
		| "dining"
		| "shopping"
		| "transport"
		| "accommodation"
		| "other";
	/** 相关地点 */
	location?: string;
	/** 相关时间 */
	time?: string;
	/** 在原文中的位置 */
	textPosition?: number;
}

/**
 * 旅行AI分析器
 */
export class TravelAIAnalyzer {
	private aiClient: AIClient;
	private geocodingService: GeocodingService;

	constructor(config?: AIAnalyzerConfig) {
		// 创建AI客户端
		this.aiClient = this.createAIClient(config);
		this.geocodingService = new GeocodingService();
	}

	/**
	 * 创建AI客户端
	 */
	private createAIClient(config?: AIAnalyzerConfig): AIClient {
		const provider = config?.provider || "openai";

		try {
			switch (provider) {
				case "volcengine": {
					const volcApiKey =
						config?.apiKey || process.env.VOLCENGINE_API_KEY;
					const volcSecretKey =
						config?.secretKey || process.env.VOLCENGINE_SECRET_KEY;

					if (!volcApiKey || !volcSecretKey) {
						logger.warn("火山引擎配置不完整，回退到OpenAI", {
							hasApiKey: !!volcApiKey,
							hasSecretKey: !!volcSecretKey,
						});
						return createOpenAIClient();
					}

					logger.info("使用火山引擎AI服务");
					return createVolcengineClient(
						volcApiKey,
						volcSecretKey,
						config?.apiEndpoint,
					);
				}

				default: {
					const openaiApiKey =
						config?.apiKey || process.env.OPENAI_API_KEY;
					logger.info("使用OpenAI服务", {
						hasApiKey: !!openaiApiKey,
					});
					return createOpenAIClient(config?.model, openaiApiKey);
				}
			}
		} catch (error) {
			logger.error("创建AI客户端失败，使用默认OpenAI客户端", {
				error: error instanceof Error ? error.message : String(error),
				provider,
			});
			return createOpenAIClient();
		}
	}

	/**
	 * 分析文本内容，提取旅行信息
	 */
	async analyzeText(textContent: string): Promise<TravelInfo> {
		const maxRetries = 2;
		let lastError: Error | null = null;

		for (let attempt = 1; attempt <= maxRetries; attempt++) {
			try {
				logger.info("开始AI文本分析", {
					textLength: textContent.length,
					provider: this.aiClient.getProvider().name,
					attempt,
					maxRetries,
				});

				const prompt = this.buildAnalysisPrompt(textContent);

				// 使用正确的AI客户端接口，限制响应长度
				const response = await this.aiClient.generateText(
					{
						systemPrompt:
							"你是一个专业的旅行内容分析助手，擅长从文本中提取地理位置、时间和活动信息。返回简洁的JSON格式结果。",
						userPrompt: prompt,
					},
					{
						maxTokens: attempt === 1 ? 20000 : 15000, // 第二次尝试使用更短的限制
						temperature: 0.3, // 降低随机性，提高一致性
					},
				);

				logger.info("AI分析完成", {
					responseLength: response.length,
					attempt,
				});

				const travelInfo = this.parseAIResponse(response);

				// 如果解析成功且有有效数据，继续处理
				if (
					travelInfo.locations.length > 0 ||
					travelInfo.timeInfo.length > 0 ||
					travelInfo.activities.length > 0
				) {
					// 增强地理位置信息
					const enhancedLocations = await this.enhanceLocationInfo(
						travelInfo.locations,
					);

					return {
						...travelInfo,
						locations: enhancedLocations,
					};
				}

				if (attempt < maxRetries) {
					// 如果没有解析到有效数据，且还有重试机会，继续重试
					logger.warn("AI分析结果为空，准备重试", { attempt });
				} else {
					// 最后一次尝试仍然没有有效数据，返回解析结果
					return travelInfo;
				}
			} catch (error) {
				lastError =
					error instanceof Error ? error : new Error(String(error));
				logger.warn("AI文本分析失败，准备重试", {
					error: lastError.message,
					textLength: textContent.length,
					attempt,
					maxRetries,
				});

				// 如果是最后一次尝试，不再重试
				if (attempt === maxRetries) {
					break;
				}

				// 等待一段时间后重试
				await this.delay(1000 * attempt);
			}
		}

		// 所有重试都失败了，记录错误并返回备用分析
		logger.error("AI文本分析完全失败，使用备用分析", {
			error: lastError?.message,
			textLength: textContent.length,
			attempts: maxRetries,
		});

		return this.createFallbackAnalysis(textContent);
	}

	/**
	 * 构建分析提示词
	 */
	private buildAnalysisPrompt(textContent: string): string {
		return `
分析旅行文本，提取地点、时间、活动信息。

文本：
"""
${textContent}
"""

返回JSON格式：
{
  "locations": [{"name": "地点名", "type": "city|attraction|restaurant|hotel|transport|other", "confidence": 0.9}],
  "timeInfo": [{"description": "时间描述", "type": "absolute|relative|duration", "confidence": 0.8}],
  "activities": [{"description": "活动描述", "type": "sightseeing|dining|shopping|transport|accommodation|other"}],
  "summary": "简短总结"
}

要求：
1. 识别地点（城市、景点、餐厅、酒店等）
2. 提取时间信息
3. 识别活动类型
4. 置信度0-1
5. 只返回有效JSON，无其他文字
6. 保持简洁，避免过长描述
		`.trim();
	}

	/**
	 * 解析AI响应
	 */
	private parseAIResponse(response: string): TravelInfo {
		try {
			// 清理响应文本，移除可能的markdown代码块标记
			let cleanedResponse = response
				.replace(/```json\s*/g, "")
				.replace(/```\s*/g, "")
				.trim();

			logger.info("AI响应的结果", {
				responseLength: response.length,
				cleanedLength: cleanedResponse.length,
				response: response.substring(0, 1000), // 只记录前1000字符
			});

			// 尝试修复不完整的JSON
			cleanedResponse = this.fixIncompleteJSON(cleanedResponse);

			const parsed = JSON.parse(cleanedResponse);

			// 验证和标准化数据
			return {
				locations: this.validateLocations(parsed.locations || []),
				timeInfo: this.validateTimeInfo(parsed.timeInfo || []),
				activities: this.validateActivities(parsed.activities || []),
				summary: parsed.summary || "无法生成旅行总结",
			};
		} catch (error) {
			logger.error("解析AI响应失败", {
				error: error instanceof Error ? error.message : String(error),
				responseLength: response.length,
				response: response.substring(0, 500),
			});

			// 尝试部分解析
			const partialResult = this.tryPartialParse(response);
			if (partialResult) {
				logger.info("部分解析成功", {
					locationsCount: partialResult.locations.length,
					timeInfoCount: partialResult.timeInfo.length,
					activitiesCount: partialResult.activities.length,
				});
				return partialResult;
			}

			// 返回空结果而不是抛出错误
			return {
				locations: [],
				timeInfo: [],
				activities: [],
				summary: "AI分析结果解析失败",
			};
		}
	}

	/**
	 * 修复不完整的JSON
	 */
	private fixIncompleteJSON(jsonString: string): string {
		// 如果JSON字符串不完整，尝试修复
		let fixed = jsonString.trim();

		// 检查是否以{开始
		if (!fixed.startsWith("{")) {
			const startIndex = fixed.indexOf("{");
			if (startIndex > 0) {
				fixed = fixed.substring(startIndex);
			}
		}

		// 检查JSON是否完整
		let braceCount = 0;
		let lastValidIndex = -1;

		for (let i = 0; i < fixed.length; i++) {
			const char = fixed[i];
			if (char === "{") {
				braceCount++;
			} else if (char === "}") {
				braceCount--;
				if (braceCount === 0) {
					lastValidIndex = i;
					break;
				}
			}
		}

		// 如果找到完整的JSON结构，截取到该位置
		if (lastValidIndex > 0) {
			fixed = fixed.substring(0, lastValidIndex + 1);
		} else if (braceCount > 0) {
			// 如果缺少结束括号，尝试修复
			// 先移除最后一个不完整的项
			const lastCommaIndex = fixed.lastIndexOf(",");
			const lastCompleteObjectEnd = fixed.lastIndexOf("}");

			// 如果最后一个逗号在最后一个完整对象之后，说明有不完整的项
			if (lastCommaIndex > lastCompleteObjectEnd) {
				fixed = fixed.substring(0, lastCommaIndex);
				// 重新计算括号数量
				braceCount = 0;
				for (let i = 0; i < fixed.length; i++) {
					const char = fixed[i];
					if (char === "{") {
						braceCount++;
					} else if (char === "}") {
						braceCount--;
					}
				}
			}

			// 补全缺少的括号
			while (braceCount > 0) {
				fixed += "}";
				braceCount--;
			}
		}

		return fixed;
	}

	/**
	 * 尝试部分解析JSON
	 */
	private tryPartialParse(response: string): TravelInfo | null {
		try {
			// 尝试提取各个字段，使用更健壮的正则表达式
			const locationsMatch = response.match(
				/"locations"\s*:\s*\[([\s\S]*?)\](?=\s*[,}])/,
			);
			const timeInfoMatch = response.match(
				/"timeInfo"\s*:\s*\[([\s\S]*?)\](?=\s*[,}])/,
			);
			const activitiesMatch = response.match(
				/"activities"\s*:\s*\[([\s\S]*?)\](?=\s*[,}])/,
			);
			const summaryMatch = response.match(
				/"summary"\s*:\s*"((?:[^"\\]|\\.)*)"/,
			);

			const partialJSON: any = {};

			if (locationsMatch) {
				try {
					partialJSON.locations = JSON.parse(
						`[${locationsMatch[1]}]`,
					);
				} catch {
					// 如果解析失败，尝试修复数组
					const fixedArray = this.fixIncompleteArray(
						locationsMatch[1],
					);
					try {
						partialJSON.locations = JSON.parse(`[${fixedArray}]`);
					} catch {
						partialJSON.locations = [];
					}
				}
			}

			if (timeInfoMatch) {
				try {
					partialJSON.timeInfo = JSON.parse(`[${timeInfoMatch[1]}]`);
				} catch {
					partialJSON.timeInfo = [];
				}
			}

			if (activitiesMatch) {
				try {
					partialJSON.activities = JSON.parse(
						`[${activitiesMatch[1]}]`,
					);
				} catch {
					partialJSON.activities = [];
				}
			}

			if (summaryMatch) {
				partialJSON.summary = summaryMatch[1];
			}

			// 如果至少解析出了一些数据，返回结果
			if (
				partialJSON.locations ||
				partialJSON.timeInfo ||
				partialJSON.activities
			) {
				return {
					locations: this.validateLocations(
						partialJSON.locations || [],
					),
					timeInfo: this.validateTimeInfo(partialJSON.timeInfo || []),
					activities: this.validateActivities(
						partialJSON.activities || [],
					),
					summary: partialJSON.summary || "部分解析的旅行总结",
				};
			}
		} catch (error) {
			logger.warn("部分解析也失败", {
				error: error instanceof Error ? error.message : String(error),
			});
		}

		return null;
	}

	/**
	 * 修复不完整的数组
	 */
	private fixIncompleteArray(arrayContent: string): string {
		let fixed = arrayContent.trim();

		// 移除最后一个不完整的对象
		const lastCompleteObjectEnd = fixed.lastIndexOf("}");
		if (lastCompleteObjectEnd > 0) {
			const afterLastObject = fixed.substring(lastCompleteObjectEnd + 1);
			// 如果最后一个对象后面还有内容但不是完整的对象，移除它
			if (afterLastObject.trim() && !afterLastObject.includes("{")) {
				fixed = fixed.substring(0, lastCompleteObjectEnd + 1);
			}
		} else {
			// 如果没有找到完整的对象结束符，尝试找到最后一个逗号并截断
			const lastCommaIndex = fixed.lastIndexOf(",");
			if (lastCommaIndex > 0) {
				// 检查逗号前是否有完整的对象
				const beforeComma = fixed.substring(0, lastCommaIndex);
				const lastObjectEnd = beforeComma.lastIndexOf("}");
				if (lastObjectEnd > 0) {
					fixed = beforeComma;
				}
			}
		}

		return fixed;
	}

	/**
	 * 验证地点信息
	 */
	private validateLocations(locations: any[]): LocationInfo[] {
		return locations
			.filter((loc) => loc && typeof loc.name === "string")
			.map((loc) => ({
				name: loc.name,
				type: this.validateLocationType(loc.type),
				confidence: Math.max(
					0,
					Math.min(1, Number(loc.confidence) || 0.5),
				),
				description: loc.description || undefined,
				textPosition: Number(loc.textPosition) || undefined,
			}));
	}

	/**
	 * 验证时间信息
	 */
	private validateTimeInfo(timeInfo: any[]): TimeInfo[] {
		return timeInfo
			.filter((time) => time && typeof time.description === "string")
			.map((time) => ({
				description: time.description,
				inferredDate: this.validateDate(time.inferredDate),
				type: this.validateTimeType(time.type),
				confidence: Math.max(
					0,
					Math.min(1, Number(time.confidence) || 0.5),
				),
				textPosition: Number(time.textPosition) || undefined,
			}));
	}

	/**
	 * 验证活动信息
	 */
	private validateActivities(activities: any[]): ActivityInfo[] {
		return activities
			.filter(
				(activity) =>
					activity && typeof activity.description === "string",
			)
			.map((activity) => ({
				description: activity.description,
				type: this.validateActivityType(activity.type),
				location: activity.location || undefined,
				time: activity.time || undefined,
				textPosition: Number(activity.textPosition) || undefined,
			}));
	}

	/**
	 * 验证地点类型
	 */
	private validateLocationType(type: string): LocationInfo["type"] {
		const validTypes: LocationInfo["type"][] = [
			"city",
			"attraction",
			"restaurant",
			"hotel",
			"transport",
			"other",
		];
		return validTypes.includes(type as LocationInfo["type"])
			? (type as LocationInfo["type"])
			: "other";
	}

	/**
	 * 验证时间类型
	 */
	private validateTimeType(type: string): TimeInfo["type"] {
		const validTypes: TimeInfo["type"][] = [
			"absolute",
			"relative",
			"duration",
		];
		return validTypes.includes(type as TimeInfo["type"])
			? (type as TimeInfo["type"])
			: "relative";
	}

	/**
	 * 验证活动类型
	 */
	private validateActivityType(type: string): ActivityInfo["type"] {
		const validTypes: ActivityInfo["type"][] = [
			"sightseeing",
			"dining",
			"shopping",
			"transport",
			"accommodation",
			"other",
		];
		return validTypes.includes(type as ActivityInfo["type"])
			? (type as ActivityInfo["type"])
			: "other";
	}

	/**
	 * 验证日期格式
	 */
	private validateDate(dateString: string): string | undefined {
		if (!dateString) return undefined;

		try {
			const date = new Date(dateString);
			if (Number.isNaN(date.getTime())) return undefined;
			return date.toISOString();
		} catch {
			return undefined;
		}
	}

	/**
	 * 延迟函数
	 */
	private delay(ms: number): Promise<void> {
		return new Promise((resolve) => setTimeout(resolve, ms));
	}

	/**
	 * 增强地理位置信息
	 */
	private async enhanceLocationInfo(
		locations: LocationInfo[],
	): Promise<LocationInfo[]> {
		const enhancedLocations: LocationInfo[] = [];

		for (const location of locations) {
			try {
				// 使用智能地理编码获取更好的结果
				const geocodeResult = await this.geocodingService.smartGeocode(
					location.name,
					{
						language: "zh-CN", // 优先使用中文
						region: "CN", // 中国地区偏好
					},
				);

				if (geocodeResult) {
					enhancedLocations.push({
						...location,
						coordinates: {
							longitude: geocodeResult.longitude,
							latitude: geocodeResult.latitude,
						},
						address: geocodeResult.address,
						country: geocodeResult.country,
						province: geocodeResult.province,
						city: geocodeResult.city,
						district: geocodeResult.district,
					});
				} else {
					// 如果地理编码失败，保留原始信息
					enhancedLocations.push(location);
				}

				// 添加延迟避免API限制
				await this.delay(200);
			} catch (error) {
				logger.warn("地理编码失败", { location: location.name, error });
				enhancedLocations.push(location);
			}
		}

		return enhancedLocations;
	}

	/**
	 * 创建备用分析结果（当AI分析失败时）
	 */
	private createFallbackAnalysis(textContent: string): TravelInfo {
		// 简单的关键词匹配来提取地点信息
		const locations: LocationInfo[] = [];
		const activities: ActivityInfo[] = [];

		// 常见的中国地点关键词
		const locationKeywords = [
			{ name: "北京", type: "city" as const },
			{ name: "天安门", type: "attraction" as const },
			{ name: "王府井", type: "attraction" as const },
			{ name: "长城", type: "attraction" as const },
			{ name: "上海", type: "city" as const },
			{ name: "外滩", type: "attraction" as const },
			{ name: "广州", type: "city" as const },
			{ name: "深圳", type: "city" as const },
		];

		// 活动关键词
		const activityKeywords = [
			{ keyword: "购物", type: "shopping" as const },
			{ keyword: "登上", type: "sightseeing" as const },
			{ keyword: "欣赏", type: "sightseeing" as const },
			{ keyword: "感受", type: "sightseeing" as const },
		];

		// 提取地点
		locationKeywords.forEach((location, index) => {
			if (textContent.includes(location.name)) {
				locations.push({
					name: location.name,
					type: location.type,
					confidence: 0.7,
					description: `从文本中识别的${location.name}`,
					textPosition: textContent.indexOf(location.name),
				});
			}
		});

		// 提取活动
		activityKeywords.forEach((activity, index) => {
			if (textContent.includes(activity.keyword)) {
				activities.push({
					description: `${activity.keyword}相关活动`,
					type: activity.type,
					textPosition: textContent.indexOf(activity.keyword),
				});
			}
		});

		return {
			locations,
			timeInfo: [
				{
					description: "今天",
					type: "relative" as const,
					confidence: 0.8,
					textPosition: textContent.indexOf("今天"),
				},
			].filter((time) => time.textPosition >= 0),
			activities,
			summary: `基于关键词分析的旅行总结：识别到${locations.length}个地点和${activities.length}个活动`,
		};
	}
}
