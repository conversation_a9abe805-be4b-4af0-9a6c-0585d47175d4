import { createOpenAIClient } from "@packages/ai";
import { promptSummarize, promptTravelPlan } from "@packages/ai/lib/prompts";

interface TravelPlanProps {
	destination: string;
	days: number;
	interests?: string[];
	customSystemPrompt?: string;
}

/**
 * 生成旅行计划示例
 */
export async function generateTravelPlan({
	destination,
	days,
	interests = [],
	customSystemPrompt,
}: TravelPlanProps) {
	// 创建 OpenAI 客户端
	const client = createOpenAIClient();

	try {
		// 方法 1: 使用提示词模板并传入自定义系统提示词
		const travelPlanPromptOptions = customSystemPrompt
			? { systemPrompt: customSystemPrompt }
			: undefined;

		const travelPlan = await client.generateFromTemplate(promptTravelPlan, [
			destination,
			days,
			interests,
			travelPlanPromptOptions,
		]);

		// 方法 2: 使用原始字符串提示词，在选项中传入系统提示词
		const rawPrompt = `请为我规划一个${days}天的${destination}旅行计划`;
		const travelPlanAlternative = await client.generateText(rawPrompt, {
			systemPrompt:
				customSystemPrompt ||
				"你是一位专业的旅行规划师，为用户提供精确的旅行建议。",
			temperature: 0.7,
		});

		// 方法 3: 直接传入对象，包含系统提示词和用户提示词
		const travelPlanObject = await client.generateText({
			systemPrompt:
				customSystemPrompt ||
				"你是一位热爱旅行的本地向导，了解各地隐藏的美食和景点。",
			userPrompt: `请为我规划一个${days}天的${destination}美食探索之旅。`,
		});

		return {
			usingTemplate: travelPlan,
			usingRawPrompt: travelPlanAlternative,
			usingPromptObject: travelPlanObject,
		};
	} catch (error) {
		console.error("生成旅行计划时出错:", error);
		throw error;
	}
}

/**
 * 生成内容摘要示例
 */
export async function generateContentSummary(
	content: string,
	role: "journalist" | "researcher" | "student" = "journalist",
) {
	const client = createOpenAIClient();

	// 根据不同角色设置不同的系统提示词
	const roleSystemPrompts = {
		journalist:
			"你是一位资深新闻编辑，善于提取新闻要点并生成新闻摘要。请使用客观、简洁的语言。",
		researcher:
			"你是一位学术研究员，擅长从学术文章中提取关键研究发现和方法。请保持学术严谨性。",
		student:
			"你是一位学习助手，擅长为学生总结学习材料的要点。请使用易于理解的语言。",
	};

	try {
		// 使用提示词模板并根据角色传入不同的系统提示词
		const summary = await client.generateFromTemplate(promptSummarize, [
			content,
			{ systemPrompt: roleSystemPrompts[role] },
		]);

		return summary;
	} catch (error) {
		console.error("生成内容摘要时出错:", error);
		throw error;
	}
}
