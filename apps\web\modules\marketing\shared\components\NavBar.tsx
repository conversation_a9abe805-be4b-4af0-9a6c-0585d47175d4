"use client";

import { LocaleLink, useLocalePathname } from "@i18n/routing";
import { config } from "@repo/config";
import { useSession } from "@saas/auth/hooks/use-session";
import { ColorModeToggle } from "@shared/components/ColorModeToggle";
import { LocaleSwitch } from "@shared/components/LocaleSwitch";
import { Logo } from "@shared/components/Logo";
import { Button } from "@ui/components/button";
import {
	Sheet,
	SheetContent,
	SheetTitle,
	SheetTrigger,
} from "@ui/components/sheet";
import { cn } from "@ui/lib";
import { MenuIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { Suspense, useEffect, useState } from "react";
import { useDebounceCallback } from "usehooks-ts";

export function NavBar() {
	const t = useTranslations();
	const { user } = useSession();
	const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
	const localePathname = useLocalePathname();
	const [isTop, setIsTop] = useState(true);

	const debouncedScrollHandler = useDebounceCallback(
		() => {
			setIsTop(window.scrollY <= 10);
		},
		150,
		{
			maxWait: 150,
		},
	);

	useEffect(() => {
		window.addEventListener("scroll", debouncedScrollHandler);
		debouncedScrollHandler();
		return () => {
			window.removeEventListener("scroll", debouncedScrollHandler);
		};
	}, [debouncedScrollHandler]);

	useEffect(() => {
		setMobileMenuOpen(false);
	}, [localePathname]);

	const isDocsPage = localePathname.startsWith("/docs");

	const menuItems: {
		label: string;
		href: string;
	}[] = [
		// {
		// 	label: t("common.menu.pricing"),
		// 	href: "/#pricing",
		// },
		// {
		// 	label: t("common.menu.faq"),
		// 	href: "/#faq",
		// },
		{
			label: t("common.menu.blog"),
			href: "/blog",
		},
		{
			label: t("common.menu.changelog"),
			href: "/changelog",
		},
		// {
		// 	label: t("common.menu.docs"),
		// 	href: "/docs",
		// },
	];

	const isMenuItemActive = (href: string) => localePathname.startsWith(href);

	return (
		<nav
			className={cn(
				"fixed top-0 left-0 z-50 w-full transition-all duration-300",
				!isTop || isDocsPage
					? "bg-white/80 dark:bg-gray-900/80 backdrop-blur-lg border-b border-white/20 dark:border-gray-700/50 shadow-lg"
					: "bg-transparent backdrop-blur-sm shadow-none",
			)}
			data-test="navigation"
		>
			{/* 背景装饰元素 */}
			<div className="absolute inset-0 overflow-hidden pointer-events-none">
				<div className="absolute -top-10 -left-10 w-32 h-32 bg-gradient-to-r from-blue-200/20 to-purple-200/20 dark:from-blue-800/15 dark:to-purple-800/15 rounded-full blur-2xl animate-pulse" />
				<div className="absolute -top-5 right-1/4 w-24 h-24 bg-gradient-to-r from-pink-200/15 to-blue-200/15 dark:from-pink-800/10 dark:to-blue-800/10 rounded-full blur-2xl animate-pulse delay-1000" />
			</div>
			<div className="relative container">
				<div
					className={cn(
						"flex items-center justify-stretch gap-6 transition-all duration-300",
						!isTop || isDocsPage ? "py-4" : "py-6",
					)}
				>
					<div className="flex flex-1 justify-start">
						<LocaleLink
							href="/"
							className="group block hover:no-underline active:no-underline relative"
						>
							{/* Logo背景装饰 */}
							<div className="absolute -inset-2 bg-gradient-to-r from-blue-400/20 via-purple-400/20 to-pink-400/20 dark:from-blue-600/30 dark:via-purple-600/30 dark:to-pink-600/30 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm -z-10" />
							<div className="relative transform transition-transform duration-300 group-hover:scale-105">
								<Logo />
							</div>
						</LocaleLink>
					</div>

					<div className="flex flex-1 items-center justify-end gap-3">
						<div className="hidden items-center lg:flex gap-2">
							{menuItems.map((menuItem, index) => (
								<div
									key={menuItem.href}
									className="group relative"
								>
									{/* 菜单项装饰 */}
									<div className="absolute -inset-1 bg-gradient-to-r from-blue-200/30 via-purple-200/30 to-pink-200/30 dark:from-blue-800/20 dark:via-purple-800/20 dark:to-pink-800/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm -z-10" />

									<LocaleLink
										href={menuItem.href}
										className={cn(
											"relative block px-4 py-2 font-medium text-sm rounded-lg transition-all duration-300 group-hover:scale-105",
											isMenuItemActive(menuItem.href)
												? "bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent font-bold"
												: "text-foreground/80 hover:text-foreground",
										)}
									>
										{/* 装饰小点 */}
										{!isMenuItemActive(menuItem.href) && (
											<div className="absolute left-2 top-1/2 w-1 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform -translate-y-1/2" />
										)}
										<span
											className={
												isMenuItemActive(menuItem.href)
													? "ml-0"
													: "ml-2"
											}
										>
											{menuItem.label}
										</span>
									</LocaleLink>
								</div>
							))}
						</div>

						{/* 右侧按钮组 */}
						<div className="flex items-center gap-2">
							{/* 颜色模式切换 */}
							<div className="group relative">
								<div className="absolute -inset-1 bg-gradient-to-r from-orange-200/30 via-yellow-200/30 to-orange-200/30 dark:from-orange-800/20 dark:via-yellow-800/20 dark:to-orange-800/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm -z-10" />
								<div className="relative transform transition-transform duration-300 group-hover:scale-105">
									<ColorModeToggle />
								</div>
							</div>

							{/* 语言切换 */}
							{config.i18n.enabled && (
								<div className="group relative">
									<div className="absolute -inset-1 bg-gradient-to-r from-green-200/30 via-emerald-200/30 to-green-200/30 dark:from-green-800/20 dark:via-emerald-800/20 dark:to-green-800/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm -z-10" />
									<div className="relative transform transition-transform duration-300 group-hover:scale-105">
										<Suspense>
											<LocaleSwitch />
										</Suspense>
									</div>
								</div>
							)}
						</div>

						{/* 移动端菜单 */}
						<div className="group relative lg:hidden">
							<div className="absolute -inset-1 bg-gradient-to-r from-purple-200/30 via-pink-200/30 to-purple-200/30 dark:from-purple-800/20 dark:via-pink-800/20 dark:to-purple-800/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm -z-10" />
							<Sheet
								open={mobileMenuOpen}
								onOpenChange={(open) => setMobileMenuOpen(open)}
							>
								<SheetTrigger asChild>
									<Button
										className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-white/20 dark:border-gray-700/50 hover:bg-white/90 dark:hover:bg-gray-800/90 transform transition-all duration-300 group-hover:scale-105"
										size="icon"
										variant="outline"
										aria-label="Menu"
									>
										<MenuIcon className="size-4" />
									</Button>
								</SheetTrigger>
								<SheetContent
									className="w-[280px] bg-white/95 dark:bg-gray-900/95 backdrop-blur-lg border-l border-white/20 dark:border-gray-700/50"
									side="right"
								>
									<SheetTitle />
									{/* 移动端菜单背景装饰 */}
									<div className="absolute inset-0 overflow-hidden pointer-events-none">
										<div className="absolute top-10 right-5 w-20 h-20 bg-gradient-to-r from-blue-200/20 to-purple-200/20 dark:from-blue-800/15 dark:to-purple-800/15 rounded-full blur-xl animate-pulse" />
										<div className="absolute bottom-20 left-5 w-16 h-16 bg-gradient-to-r from-pink-200/15 to-blue-200/15 dark:from-pink-800/10 dark:to-blue-800/10 rounded-full blur-xl animate-pulse delay-1000" />
									</div>

									<div className="relative flex flex-col items-start justify-center mt-8 space-y-2">
										{menuItems.map((menuItem) => (
											<div
												key={menuItem.href}
												className="group relative w-full"
											>
												{/* 移动端菜单项装饰 */}
												<div className="absolute -inset-1 bg-gradient-to-r from-blue-200/30 via-purple-200/30 to-pink-200/30 dark:from-blue-800/20 dark:via-purple-800/20 dark:to-pink-800/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm -z-10" />

												<LocaleLink
													href={menuItem.href}
													className={cn(
														"relative block w-full px-4 py-3 font-medium text-base rounded-lg transition-all duration-300 group-hover:scale-105",
														isMenuItemActive(
															menuItem.href,
														)
															? "bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent font-bold"
															: "text-foreground/80 hover:text-foreground",
													)}
												>
													{/* 移动端装饰小点 */}
													{!isMenuItemActive(
														menuItem.href,
													) && (
														<div className="absolute left-3 top-1/2 w-1.5 h-1.5 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform -translate-y-1/2" />
													)}
													<span
														className={
															isMenuItemActive(
																menuItem.href,
															)
																? "ml-0"
																: "ml-4"
														}
													>
														{menuItem.label}
													</span>
												</LocaleLink>
											</div>
										))}

										{/* MVP: 隐藏登录和dashboard链接 */}
										{/* <NextLink
										key={user ? "start" : "login"}
										href={user ? "/app" : "/auth/login"}
										className="block px-3 py-2 text-base"
										prefetch={!user}
									>
										{user
											? t("common.menu.dashboard")
											: t("common.menu.login")}
									</NextLink> */}
									</div>
								</SheetContent>
							</Sheet>
						</div>

						{/* MVP: 隐藏登录和dashboard按钮 */}
						{/* {config.ui.saas.enabled &&
							(user ? (
								<Button
									key="dashboard"
									className="hidden lg:flex"
									asChild
									variant="secondary"
								>
									<NextLink href="/app">
										{t("common.menu.dashboard")}
									</NextLink>
								</Button>
							) : (
								<Button
									key="login"
									className="hidden lg:flex"
									asChild
									variant="secondary"
								>
									<NextLink href="/auth/login">
										{t("common.menu.login")}
									</NextLink>
								</Button>
							))} */}
					</div>
				</div>
			</div>
		</nav>
	);
}
