# 富文本格式化服务

这个模块实现了将用户在富文本编辑器中输入的内容转换为结构化旅行日记数据的功能。

## 功能概述

### 核心流程
1. **富文本内容解析** - 从Tiptap JSONContent中提取文本和图片信息
2. **AI文本分析** - 使用大模型识别地点、时间和活动信息
3. **地理编码** - 通过Mapbox API获取精确的地理位置信息
4. **数据结构生成** - 将分析结果转换为旅行日记的数据结构

## 服务模块

### 1. RichTextAnalyzer (富文本分析器)
- **文件**: `richtext-analyzer.ts`
- **功能**: 解析Tiptap JSONContent，提取文本、图片等信息
- **主要方法**:
  - `analyze(content)` - 分析富文本内容
  - `isValidImageUrl(url)` - 验证图片URL
  - `filterValidImageUrls(urls)` - 过滤有效图片URL

### 2. TravelAIAnalyzer (旅行AI分析器)
- **文件**: `travel-ai-analyzer.ts`
- **功能**: 使用AI分析文本，提取旅行相关信息
- **支持的AI提供商**: OpenAI, Gemini, Volcengine
- **主要方法**:
  - `analyzeText(textContent)` - 分析文本内容
- **输出信息**:
  - 地点信息（名称、类型、置信度）
  - 时间信息（描述、推断日期、类型）
  - 活动信息（描述、类型、相关地点和时间）
  - 整体旅行总结

### 3. GeocodingService (地理编码服务)
- **文件**: `geocoding-service.ts`
- **功能**: 使用Mapbox API进行地理位置查询
- **主要方法**:
  - `geocode(query, options)` - 正向地理编码
  - `reverseGeocode(lng, lat, options)` - 反向地理编码
  - `smartGeocode(query, options)` - 智能地理编码（多策略）
  - `batchGeocode(queries, options)` - 批量地理编码

### 4. TravelDiaryGenerator (旅行日记生成器)
- **文件**: `travel-diary-generator.ts`
- **功能**: 将AI分析结果转换为旅行日记数据结构
- **主要方法**:
  - `generateDiaryContent(travelInfo, locationMatches, options)` - 生成日记内容
- **支持的分组策略**:
  - `daily` - 按日期分组
  - `activity` - 按活动类型分组
  - `location` - 按地点分组

## API接口

### POST /api/diaries/format-richtext
将富文本内容格式化为结构化的旅行日记数据。

**请求参数**:
```json
{
  "content": {}, // Tiptap JSONContent
  "diaryId": "string" // 日记ID
}
```

**响应数据**:
```json
{
  "formattedDiary": {
    "id": "string",
    "title": "string",
    "content": {
      "timelines": [
        {
          "id": "string",
          "title": "string",
          "date": "string",
          "points": [
            {
              "id": "string",
              "location": "string",
              "description": "string",
              "date": "string",
              "images": [],
              "iconType": "PIN|HOTEL|FOOD|LANDMARK|...",
              "latitude": 0,
              "longitude": 0,
              "order": 0,
              "country": "string",
              "city": "string"
            }
          ]
        }
      ]
    }
  },
  "analysisResult": {
    "textAnalysis": {
      "textLength": 0,
      "imageCount": 0,
      "blockCount": 0
    },
    "aiAnalysis": {
      "locationsFound": 0,
      "timeInfoFound": 0,
      "activitiesFound": 0,
      "summary": "string"
    },
    "geocoding": {
      "totalLocations": 0,
      "successfulMatches": 0,
      "failedMatches": 0
    }
  }
}
```

## 环境配置

### 必需的环境变量
```bash
# AI服务配置（选择一个）
VOLCENGINE_API_KEY=your_volcengine_api_key
OPENAI_API_KEY=your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key

# 地理编码服务
MAPBOX_ACCESS_TOKEN=your_mapbox_access_token
```

## 使用示例

### 基本使用
```typescript
import { RichTextAnalyzer } from './services/richtext-analyzer';
import { TravelAIAnalyzer } from './services/travel-ai-analyzer';
import { GeocodingService } from './services/geocoding-service';
import { TravelDiaryGenerator } from './services/travel-diary-generator';

// 1. 分析富文本内容
const analysisResult = RichTextAnalyzer.analyze(tiptapContent);

// 2. AI分析
const aiAnalyzer = new TravelAIAnalyzer();
const travelInfo = await aiAnalyzer.analyzeText(analysisResult.textContent);

// 3. 地理编码
const geocodingService = new GeocodingService();
const locationMatches = [];
for (const location of travelInfo.locations) {
  const results = await geocodingService.smartGeocode(location.name);
  locationMatches.push({
    locationInfo: location,
    geocodingResult: results[0] || null,
    confidence: location.confidence * (results[0]?.relevance || 0)
  });
}

// 4. 生成日记内容
const diaryContent = await TravelDiaryGenerator.generateDiaryContent(
  travelInfo,
  locationMatches,
  { timelineGrouping: 'daily' }
);
```

### 测试
运行测试脚本验证功能：
```bash
cd packages/api
npm run test:format-richtext
```

## 错误处理

### AI分析失败
- 当AI服务不可用时，返回500错误
- 错误信息包含具体的失败原因

### 地理编码失败
- 单个地点编码失败不会影响整体流程
- 失败的地点会被标记但仍保留在结果中
- 支持降级策略（粗略精度 -> 精确精度）

### 数据验证
- 所有输入数据都会进行验证
- 无效的图标类型会被替换为默认值
- 无效的坐标会被过滤

## 扩展性

### 添加新的AI提供商
1. 在 `packages/ai` 中实现新的提供商
2. 更新 `TravelAIAnalyzer` 构造函数
3. 添加相应的环境变量

### 添加新的地理编码服务
1. 实现 `GeocodingService` 接口
2. 更新配置和环境变量

### 自定义数据生成策略
1. 扩展 `GenerationOptions` 接口
2. 在 `TravelDiaryGenerator` 中实现新的分组策略

## 性能优化

### API调用限制
- 地理编码请求之间有200ms延迟
- 支持批量处理以减少API调用次数
- 实现了智能重试机制

### 缓存策略
- 可以添加Redis缓存来存储地理编码结果
- AI分析结果可以基于文本内容哈希进行缓存

### 并发处理
- 地理编码支持并发处理（需注意API限制）
- AI分析可以并行处理多个文本段落

## 后续开发计划

### 第二阶段：图片处理
- [ ] 集成图片识别AI
- [ ] 从图片中提取地理位置信息
- [ ] 图片内容分析和标签生成

### 第三阶段：用户交互
- [ ] 生成结果确认界面
- [ ] 手动编辑和调整功能
- [ ] 批量处理多个日记

### 第四阶段：高级功能
- [ ] 路线规划和优化
- [ ] 相似地点合并
- [ ] 智能时间推断
- [ ] 多语言支持 