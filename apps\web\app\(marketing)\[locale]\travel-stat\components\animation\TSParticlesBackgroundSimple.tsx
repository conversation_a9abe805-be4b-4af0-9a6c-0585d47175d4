"use client";

import { useCallback, useState } from "react";
import Particles from "react-tsparticles";
import { loadSlim } from "tsparticles-slim";

// tsParticles 主题类型定义
export type TSParticlesTheme =
	| "meteor-shower"
	| "floating-cities"
	| "travel-routes"
	| "aurora-waves"
	| "cosmic-web"
	| "particle-rain";

interface TSParticlesBackgroundProps {
	theme?: TSParticlesTheme;
	className?: string;
	density?: number;
}

export function TSParticlesBackground({
	theme = "meteor-shower",
	className = "",
	density = 1,
}: TSParticlesBackgroundProps) {
	const [mounted, setMounted] = useState(false);

	// 确保只在客户端渲染
	useState(() => {
		setMounted(true);
	});

	// 引擎初始化
	const particlesInit = useCallback(
		async (engine: any) => {
			console.log("🎆 TSParticles 引擎初始化:", theme);
			try {
				await loadSlim(engine);
			} catch (error) {
				console.warn("TSParticles 加载失败:", error);
			}
		},
		[theme],
	);

	// 粒子加载完成回调
	const particlesLoaded = useCallback(async (container: any) => {
		console.log("🌟 TSParticles 容器加载完成");
	}, []);

	// 根据主题获取配置
	const getConfig = () => {
		const baseConfig = {
			background: {
				color: "transparent",
			},
			fpsLimit: 60,
			detectRetina: true,
			particles: {
				number: {
					value: Math.floor(80 * density),
					density: {
						enable: true,
						value_area: 800,
					},
				},
				color: {
					value: "#ffffff",
				},
				shape: {
					type: "circle",
				},
				opacity: {
					value: 0.6,
					random: true,
					animation: {
						enable: true,
						speed: 1,
						minimumValue: 0.1,
						sync: false,
					},
				},
				size: {
					value: 3,
					random: true,
					animation: {
						enable: true,
						speed: 2,
						minimumValue: 0.5,
						sync: false,
					},
				},
				links: {
					enable: false,
				},
				move: {
					enable: true,
					speed: 2,
					direction: "none",
					random: false,
					straight: false,
					outModes: {
						default: "out",
					},
				},
			},
		};

		// 根据主题调整配置
		switch (theme) {
			case "meteor-shower":
				return {
					...baseConfig,
					particles: {
						...baseConfig.particles,
						color: {
							value: ["#ffffff", "#87ceeb", "#ffd700", "#ff6347"],
						},
						move: {
							...baseConfig.particles.move,
							speed: 6,
							direction: "bottom-right",
						},
						life: {
							duration: {
								sync: false,
								value: 3,
							},
							count: 0,
						},
					},
				};

			case "floating-cities":
				return {
					...baseConfig,
					particles: {
						...baseConfig.particles,
						color: {
							value: ["#4a90e2", "#50c878", "#ffa500", "#ff69b4"],
						},
						shape: {
							type: ["circle", "triangle"],
						},
						links: {
							enable: true,
							distance: 150,
							color: "#ffffff",
							opacity: 0.2,
							width: 1,
						},
						move: {
							...baseConfig.particles.move,
							speed: 1,
						},
					},
					interactivity: {
						detectsOn: "canvas",
						events: {
							onHover: {
								enable: true,
								mode: "grab",
							},
							resize: true,
						},
						modes: {
							grab: {
								distance: 200,
								links: {
									opacity: 0.5,
								},
							},
						},
					},
				};

			case "travel-routes":
				return {
					...baseConfig,
					particles: {
						...baseConfig.particles,
						number: {
							value: Math.floor(30 * density),
						},
						color: {
							value: "#ffd700",
						},
						links: {
							enable: true,
							distance: 250,
							color: "#ffd700",
							opacity: 0.4,
							width: 2,
						},
						move: {
							...baseConfig.particles.move,
							speed: 0.5,
							random: true,
						},
					},
				};

			case "aurora-waves":
				return {
					...baseConfig,
					particles: {
						...baseConfig.particles,
						number: {
							value: Math.floor(100 * density),
						},
						color: {
							value: ["#00ff88", "#0088ff", "#8800ff", "#ff0088"],
						},
						move: {
							...baseConfig.particles.move,
							speed: 3,
							direction: "top",
							random: true,
						},
					},
				};

			case "cosmic-web":
				return {
					...baseConfig,
					particles: {
						...baseConfig.particles,
						number: {
							value: Math.floor(120 * density),
						},
						links: {
							enable: true,
							distance: 200,
							color: "#ffffff",
							opacity: 0.15,
							width: 1,
						},
						move: {
							...baseConfig.particles.move,
							speed: 0.3,
							random: true,
						},
					},
					interactivity: {
						detectsOn: "canvas",
						events: {
							onHover: {
								enable: true,
								mode: "connect",
							},
							resize: true,
						},
						modes: {
							connect: {
								distance: 80,
								links: {
									opacity: 0.3,
								},
								radius: 60,
							},
						},
					},
				};

			case "particle-rain":
				return {
					...baseConfig,
					particles: {
						...baseConfig.particles,
						number: {
							value: Math.floor(150 * density),
						},
						color: {
							value: ["#87ceeb", "#4169e1", "#1e90ff", "#00bfff"],
						},
						size: {
							value: 2,
							random: true,
						},
						move: {
							...baseConfig.particles.move,
							speed: 4,
							direction: "bottom",
							straight: true,
						},
					},
				};

			default:
				return baseConfig;
		}
	};

	if (!mounted) return null;

	return (
		<div className={`absolute inset-0 pointer-events-none ${className}`}>
			<Particles
				id={`tsparticles-${theme}`}
				init={particlesInit}
				loaded={particlesLoaded}
				options={getConfig() as any}
				style={{
					position: "absolute",
					top: 0,
					left: 0,
					width: "100%",
					height: "100%",
					pointerEvents: "none",
				}}
			/>
		</div>
	);
}

// 主题预设
export const TSPARTICLES_THEMES: Record<TSParticlesTheme, string> = {
	"meteor-shower": "流星雨",
	"floating-cities": "浮动城市",
	"travel-routes": "旅行路线",
	"aurora-waves": "极光波浪",
	"cosmic-web": "宇宙网络",
	"particle-rain": "粒子雨",
};
