{"extends": "@repo/tsconfig/nextjs.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./app/*"], "@saas/*": ["../web/modules/saas/*"], "@shared/*": ["../web/modules/shared/*"], "@ui/*": ["../web/modules/ui/*"], "@marketing/*": ["../web/modules/marketing/*"], "@repo/shared-ui/*": ["../../packages/shared-ui/src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}