"use client";

import { formatDate, formatDistance, isValid, parseISO } from "date-fns";
import type { ChangelogItem } from "../types";

function formatChangelogDate(dateString: string): {
	formattedDate: string;
	relativeDate: string;
	displayDate: string;
} {
	if (!dateString) {
		return {
			formattedDate: "Invalid Date",
			relativeDate: "Unknown time",
			displayDate: "Unknown",
		};
	}

	try {
		let parsedDate: Date;

		if (typeof dateString === "string") {
			parsedDate = new Date(dateString);

			if (!isValid(parsedDate)) {
				parsedDate = parseISO(dateString);
			}

			if (!isValid(parsedDate) && /^\d+$/.test(dateString)) {
				parsedDate = new Date(Number(dateString));
			}
		} else {
			parsedDate = new Date(dateString);
		}

		if (!isValid(parsedDate)) {
			console.warn("All parsing attempts failed for date:", dateString);
			return {
				formattedDate: `Invalid: ${dateString}`,
				relativeDate: `Unknown: ${dateString}`,
				displayDate: `Unknown: ${dateString}`,
			};
		}

		return {
			formattedDate: formatDate(parsedDate, "yyyy-MM-dd"),
			relativeDate: formatDistance(parsedDate, new Date(), {
				addSuffix: true,
			}),
			displayDate: formatDate(parsedDate, "MMM dd"),
		};
	} catch (error) {
		console.warn("Error parsing date:", dateString, error);
		return {
			formattedDate: `Error: ${dateString}`,
			relativeDate: `Error: ${dateString}`,
			displayDate: `Error: ${dateString}`,
		};
	}
}

export function ChangelogSection({ items }: { items: ChangelogItem[] }) {
	return (
		<section id="changelog" className="relative">
			{/* 背景装饰 */}
			<div className="absolute inset-0 overflow-hidden pointer-events-none">
				<div className="absolute top-20 left-1/4 w-64 h-64 bg-blue-100/20 rounded-full blur-3xl animate-pulse" />
				<div className="absolute bottom-20 right-1/4 w-48 h-48 bg-purple-100/20 rounded-full blur-3xl animate-pulse delay-1000" />
			</div>

			<div className="relative mx-auto max-w-4xl">
				{/* 时间线容器 */}
				<div className="relative">
					{/* 主时间线 */}
					<div className="absolute left-8 md:left-16 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-200 via-purple-200 to-pink-200" />

					{items?.map((item, i) => {
						const { formattedDate, relativeDate, displayDate } =
							formatChangelogDate(item.date);

						return (
							<div
								key={i}
								className="relative flex items-start mb-12 md:mb-16 group"
							>
								{/* 时间线节点 */}
								<div className="absolute left-8 md:left-16 w-4 h-4 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full border-4 border-white shadow-lg transform -translate-x-2 group-hover:scale-125 transition-transform duration-300 z-10" />

								{/* 内容区域 */}
								<div className="w-full ml-16 md:ml-28">
									{/* 时间标签 */}
									<div className="flex items-center gap-3 mb-4">
										<div className="relative">
											<span
												className="inline-block bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent font-bold text-lg"
												title={formattedDate}
											>
												{displayDate}
											</span>
											<div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-60" />
										</div>
										<span className="text-sm text-muted-foreground bg-muted px-2 py-1 rounded-full">
											{relativeDate}
										</span>
									</div>

									{/* 变更内容卡片 */}
									<div className="relative bg-white/80 backdrop-blur-sm border border-white/20 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:translate-y-[-2px] before:absolute before:inset-0 before:bg-gradient-to-br before:from-blue-50/50 before:to-purple-50/50 before:rounded-2xl before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300">
										{/* 装饰性边框 */}
										<div className="absolute inset-0 bg-gradient-to-r from-blue-200/30 via-purple-200/30 to-pink-200/30 rounded-2xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />

										{/* 内容列表 */}
										<ul className="relative space-y-3">
											{item.changes.map((change, j) => (
												<li
													key={j}
													className="flex items-start gap-3 group/item"
												>
													{/* 美化的列表标记 */}
													<div className="flex-shrink-0 w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full mt-2 group-hover/item:scale-125 transition-transform duration-200" />
													<span className="text-gray-700 leading-relaxed group-hover/item:text-gray-900 transition-colors duration-200">
														{change}
													</span>
												</li>
											))}
										</ul>

										{/* 连接线装饰 */}
										<div className="absolute top-6 -left-6 w-6 h-0.5 bg-gradient-to-r from-blue-300 to-purple-300 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 hidden md:block" />
									</div>
								</div>
							</div>
						);
					})}
				</div>

				{/* 底部装饰 */}
				<div className="flex justify-center mt-16">
					<div className="flex items-center gap-2 text-muted-foreground">
						<div className="w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-pulse" />
						<span className="text-sm font-medium">
							Journey continues...
						</span>
						<div className="w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse delay-500" />
					</div>
				</div>
			</div>
		</section>
	);
}
