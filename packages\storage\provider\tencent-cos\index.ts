import { logger } from "@repo/logs";
import COS from "cos-nodejs-sdk-v5";
import type {
	BatchDeleteOptions,
	BatchDeleteResult,
	BatchUploadOptions,
	BatchUploadResult,
	StorageProvider,
	StorageProviderConfig,
	UploadFileOptions,
	UploadFileResult,
} from "../../types";

/**
 * 腾讯云COS存储提供商实现
 */
export function createTencentCOSProvider(
	config: StorageProviderConfig,
): StorageProvider & { createClient: () => any } {
	return {
		/**
		 * 获取提供商名称
		 */
		getProviderName: () => "tencent-cos",

		/**
		 * 创建COS客户端
		 */
		createClient: () => {
			const { accessKeyId, secretAccessKey, region } = config;

			if (!accessKeyId) {
				throw new Error("腾讯云COS提供商配置中缺少accessKeyId");
			}

			if (!secretAccessKey) {
				throw new Error("腾讯云COS提供商配置中缺少secretAccessKey");
			}

			return new COS({
				SecretId: accessKeyId,
				SecretKey: secretAccessKey,
				Region: region,
			} as any);
		},

		/**
		 * 创建存储桶(腾讯云称为Bucket)
		 */
		async createBucket(
			name: string,
			options?: { public?: boolean; corsConfig?: any; region?: string },
		): Promise<void> {
			const client = (this as any).createClient();
			const region = options?.region || config.region;

			if (!region) {
				throw new Error("创建存储桶时需要指定region");
			}

			try {
				// 创建存储桶
				await new Promise<void>((resolve, reject) => {
					client.putBucket(
						{
							Bucket: name,
							Region: region,
						},
						(err: any, data: any) => {
							if (err) {
								reject(err);
								return;
							}
							resolve();
						},
					);
				});

				// 配置CORS规则（默认允许常见的操作和localhost来源）
				const defaultCorsRules = [
					{
						AllowedOrigins: ["*"], // 或者指定 ['http://localhost:3000', 'https://yourdomain.com']
						AllowedMethods: [
							"GET",
							"PUT",
							"POST",
							"DELETE",
							"HEAD",
						],
						AllowedHeaders: ["*"],
						ExposeHeaders: [
							"ETag",
							"Content-Length",
							"x-cos-request-id",
						],
						MaxAgeSeconds: 86400, // 1天
					},
				];

				await new Promise<void>((resolve, reject) => {
					client.putBucketCors(
						{
							Bucket: name,
							Region: region,
							CORSRules: options?.corsConfig || defaultCorsRules,
						},
						(err: any, data: any) => {
							if (err) {
								logger.error("设置存储桶CORS规则失败", err);
								// 不要因为CORS设置失败而中断整个流程
								resolve();
								return;
							}
							logger.info(`成功设置存储桶 ${name} 的CORS规则`);
							resolve();
						},
					);
				});

				// 如果需要设置为公开访问
				if (options?.public) {
					await new Promise<void>((resolve, reject) => {
						client.putBucketAcl(
							{
								Bucket: name,
								Region: region,
								ACL: "public-read",
							},
							(err: any, data: any) => {
								if (err) {
									reject(err);
									return;
								}
								logger.info(`设置桶 ${name} 为公开访问`);
								resolve();
							},
						);
					});
				}
			} catch (e) {
				logger.error("创建存储桶失败:", e);
				throw new Error(
					`无法创建存储桶: ${name} - ${e instanceof Error ? e.message : String(e)}`,
				);
			}
		},

		/**
		 * 获取签名上传URL
		 */
		async getSignedUploadUrl(
			path: string,
			options: { bucket: string; contentType?: string; region?: string },
		): Promise<string> {
			const client = (this as any).createClient();
			const region = options.region || config.region;

			if (!region) {
				throw new Error("获取签名URL时需要指定region");
			}

			try {
				return await new Promise<string>((resolve, reject) => {
					client.getObjectUrl(
						{
							Bucket: options.bucket,
							Region: region,
							Key: path,
							Method: "PUT",
							Sign: true,
							Expires: 900, // 增加到15分钟
							// 确保设置正确的 Content-Type
							Headers: options.contentType
								? { "Content-Type": options.contentType }
								: undefined,
						},
						(err: any, data: any) => {
							if (err) {
								logger.error("获取腾讯云COS签名URL失败:", err);
								reject(err);
								return;
							}
							logger.info(`生成COS签名上传URL成功: ${path}`);
							resolve(data.Url);
						},
					);
				});
			} catch (e) {
				logger.error("获取腾讯云COS签名URL异常:", e);
				throw new Error(
					`无法获取签名上传URL: ${e instanceof Error ? e.message : String(e)}`,
				);
			}
		},

		/**
		 * 获取签名访问URL
		 */
		async getSignedUrl(
			path: string,
			options: { bucket: string; expiresIn?: number; region?: string },
		): Promise<string> {
			const client = (this as any).createClient();
			const region = options.region || config.region;

			if (!region) {
				throw new Error("获取签名URL时需要指定region");
			}

			try {
				return await new Promise<string>((resolve, reject) => {
					client.getObjectUrl(
						{
							Bucket: options.bucket,
							Region: region,
							Key: path,
							Sign: true,
							Expires: options.expiresIn || 3600, // 默认1小时过期
						},
						(err: any, data: any) => {
							if (err) {
								reject(err);
								return;
							}
							resolve(data.Url);
						},
					);
				});
			} catch (e) {
				logger.error("获取腾讯云COS签名URL异常:", e);
				throw new Error(
					`无法获取签名URL: ${e instanceof Error ? e.message : String(e)}`,
				);
			}
		},

		/**
		 * 批量获取签名上传URL
		 */
		async getBatchSignedUploadUrls(
			options: BatchUploadOptions,
		): Promise<BatchUploadResult> {
			const client = (this as any).createClient();
			const { bucket, paths: urls, region: clientRegion } = options;
			const region = clientRegion || config.region;

			if (!region) {
				throw new Error("批量获取签名URL时需要指定region");
			}

			const result: BatchUploadResult = {
				urls: {},
				failedPaths: [],
				errors: {},
			};

			if (urls.length === 0) {
				return result;
			}

			try {
				// 并发获取签名URL，腾讯云COS没有单次批量上传限制
				const urlPromises = urls.map(async (path) => {
					try {
						const signedUrl = await new Promise<string>(
							(resolve, reject) => {
								client.getObjectUrl(
									{
										Bucket: bucket,
										Region: region,
										Key: path,
										Method: "PUT",
										Sign: true,
										Expires: 900,
										Headers: {
											"Content-Type":
												"application/octet-stream",
										},
									},
									(err: any, data: any) => {
										if (err) {
											reject(err);
											return;
										}
										resolve(data.Url);
									},
								);
							},
						);

						result.urls[path] = signedUrl;
					} catch (error) {
						const errorMessage =
							error instanceof Error
								? error.message
								: String(error);
						result.failedPaths!.push(path);
						result.errors![path] = errorMessage;
						logger.error(`获取签名URL失败 (${path}):`, error);
					}
				});

				await Promise.all(urlPromises);
			} catch (error) {
				logger.error("批量获取签名URL失败:", error);
				throw new Error(
					`批量获取签名URL失败: ${error instanceof Error ? error.message : String(error)}`,
				);
			}

			// 如果没有失败的路径，清除这些字段
			if (result.failedPaths?.length === 0) {
				delete result.failedPaths;
				delete result.errors;
			}

			return result;
		},

		/**
		 * 批量删除对象
		 */
		async batchDeleteObjects(
			options: BatchDeleteOptions,
		): Promise<BatchDeleteResult> {
			const client = (this as any).createClient();
			const region = options.region || config.region;

			if (!region) {
				throw new Error("批量删除对象时需要指定region");
			}

			try {
				const result = await new Promise<any>((resolve, reject) => {
					client.deleteMultipleObject(
						{
							Bucket: options.bucket,
							Region: region,
							Objects: options.paths.map((path) => ({
								Key: path,
							})),
						},
						(err: any, data: any) => {
							if (err) {
								reject(err);
								return;
							}
							resolve(data);
						},
					);
				});

				const deletedPaths: string[] = [];
				const errors: Record<string, string> = {};

				if (result.Deleted) {
					result.Deleted.forEach((deleted: any) => {
						deletedPaths.push(deleted.Key);
					});
				}

				if (result.Error) {
					result.Error.forEach((error: any) => {
						errors[error.Key] = error.Message || "删除失败";
					});
				}

				logger.info(
					`批量删除完成: 成功删除 ${deletedPaths.length} 个对象，失败 ${Object.keys(errors).length} 个`,
				);

				return {
					deletedPaths,
					errors,
				};
			} catch (e) {
				logger.error("批量删除对象异常:", e);
				throw new Error(
					`批量删除失败: ${e instanceof Error ? e.message : String(e)}`,
				);
			}
		},

		/**
		 * 上传文件
		 */
		async uploadFile(
			options: UploadFileOptions,
		): Promise<UploadFileResult> {
			const client = (this as any).createClient();
			const region = options.region || config.region;

			if (!region) {
				throw new Error("上传文件时需要指定region");
			}

			try {
				const result = await new Promise<any>((resolve, reject) => {
					client.putObject(
						{
							Bucket: options.bucket,
							Region: region,
							Key: options.path,
							Body: options.file,
							ContentType: options.contentType,
							ACL: options.isPublic ? "public-read" : "private",
						},
						(err: any, data: any) => {
							if (err) {
								reject(err);
								return;
							}
							resolve(data);
						},
					);
				});

				logger.info(`文件上传成功: ${options.path}`);

				return {
					success: true,
					path: options.path,
					etag: result.ETag,
				};
			} catch (e) {
				logger.error("上传文件异常:", e);
				return {
					success: false,
					error: e instanceof Error ? e.message : String(e),
				};
			}
		},
	};
}

/**
 * 使用环境变量创建腾讯云COS存储提供商实例
 */
export function createTencentCOSProviderFromEnv(): StorageProvider & {
	createClient: () => any;
} {
	const cosRegion = process.env.TENCENT_COS_REGION as string;
	if (!cosRegion) {
		throw new Error("缺少环境变量 TENCENT_COS_REGION");
	}

	const cosAccessKeyId = process.env.TENCENT_COS_SECRET_ID as string;
	if (!cosAccessKeyId) {
		throw new Error("缺少环境变量 TENCENT_COS_SECRET_ID");
	}

	const cosSecretAccessKey = process.env.TENCENT_COS_SECRET_KEY as string;
	if (!cosSecretAccessKey) {
		throw new Error("缺少环境变量 TENCENT_COS_SECRET_KEY");
	}

	return createTencentCOSProvider({
		region: cosRegion,
		accessKeyId: cosAccessKeyId,
		secretAccessKey: cosSecretAccessKey,
	});
}
