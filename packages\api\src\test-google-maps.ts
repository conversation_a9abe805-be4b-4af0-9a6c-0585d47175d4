/**
 * 测试 Google Maps API 连接
 * 用于验证 API 密钥和网络连接
 */

import { resolve } from "node:path";
import { config } from "dotenv";

// 加载环境变量
const envFiles = [".env.local", ".env"];
for (const envFile of envFiles) {
	try {
		const envPath = resolve(process.cwd(), "../../", envFile);
		config({ path: envPath });
		console.log(`✅ 已加载环境变量文件: ${envFile}`);
		break;
	} catch (error) {
		// 继续尝试下一个文件
	}
}

async function testGoogleMapsAPI() {
	console.log("🔍 测试 Google Maps API 连接...\n");

	// 检查 API 密钥
	const apiKey = process.env.GOOGLE_MAPS_API_KEY;
	if (!apiKey) {
		console.error("❌ 错误: 未找到 GOOGLE_MAPS_API_KEY 环境变量");
		console.log("请在 .env.local 文件中添加:");
		console.log("GOOGLE_MAPS_API_KEY=your_api_key_here");
		return;
	}

	console.log(`✅ API 密钥已配置: ${apiKey.substring(0, 10)}...`);

	// 测试基本连接
	try {
		console.log("\n🌐 测试网络连接...");

		const testUrl = "https://maps.googleapis.com/maps/api/geocode/json";
		const params = new URLSearchParams({
			address: "Beijing",
			key: apiKey,
		});

		const fullUrl = `${testUrl}?${params}`;
		console.log(`📡 请求URL: ${fullUrl.replace(apiKey, "***")}`);

		const response = await fetch(fullUrl);

		console.log(`📊 HTTP状态: ${response.status} ${response.statusText}`);

		if (!response.ok) {
			console.error(
				`❌ HTTP请求失败: ${response.status} ${response.statusText}`,
			);
			return;
		}

		const data = await response.json();
		console.log(`📋 API响应状态: ${data.status}`);

		if (data.error_message) {
			console.error(`❌ API错误: ${data.error_message}`);

			// 提供常见错误的解决方案
			if (data.status === "REQUEST_DENIED") {
				console.log("\n💡 解决方案:");
				console.log("1. 检查 API 密钥是否正确");
				console.log("2. 确认已启用 Geocoding API");
				console.log("3. 检查 API 密钥的限制设置");
				console.log(
					"4. 访问 https://console.cloud.google.com/apis/credentials",
				);
			}
			return;
		}

		if (data.status === "OK" && data.results && data.results.length > 0) {
			const result = data.results[0];
			console.log("✅ 地理编码测试成功!");
			console.log(`📍 地址: ${result.formatted_address}`);
			console.log(
				`🌍 坐标: ${result.geometry.location.lat}, ${result.geometry.location.lng}`,
			);

			// 测试中文地址
			console.log("\n🇨🇳 测试中文地址...");
			await testChineseAddress(apiKey);
		} else {
			console.log(`⚠️ 未找到结果: ${data.status}`);
		}
	} catch (error) {
		console.error("❌ 网络请求失败:", error);

		if (error instanceof Error) {
			if (error.message.includes("fetch failed")) {
				console.log("\n💡 可能的原因:");
				console.log("1. 网络连接问题");
				console.log("2. 防火墙阻止了请求");
				console.log("3. 代理设置问题");
				console.log("4. DNS 解析问题");
			}
		}
	}
}

async function testChineseAddress(apiKey: string) {
	try {
		const testUrl = "https://maps.googleapis.com/maps/api/geocode/json";
		const params = new URLSearchParams({
			address: "北京天安门广场",
			key: apiKey,
			language: "zh-CN",
			region: "CN",
		});

		const response = await fetch(`${testUrl}?${params}`);
		const data = await response.json();

		if (data.status === "OK" && data.results && data.results.length > 0) {
			const result = data.results[0];
			console.log("✅ 中文地址测试成功!");
			console.log(`📍 地址: ${result.formatted_address}`);
			console.log(
				`🌍 坐标: ${result.geometry.location.lat}, ${result.geometry.location.lng}`,
			);
		} else {
			console.log(`⚠️ 中文地址测试失败: ${data.status}`);
			if (data.error_message) {
				console.log(`错误信息: ${data.error_message}`);
			}
		}
	} catch (error) {
		console.error("❌ 中文地址测试失败:", error);
	}
}

// 运行测试
testGoogleMapsAPI()
	.then(() => {
		console.log("\n🎉 测试完成!");
	})
	.catch((error) => {
		console.error("❌ 测试失败:", error);
		process.exit(1);
	});
