export function ScrollbarStyles() {
	return (
		<style jsx global>{`
      .custom-scrollbar::-webkit-scrollbar {
        width: 1px;
      }

      .custom-scrollbar::-webkit-scrollbar-track {
        background: rgba(240, 249, 255, 0.6);
        border-radius: 1px;
      }

      .custom-scrollbar::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, 
          rgba(56, 189, 248, 0.7) 0%, 
          rgba(14, 165, 233, 0.8) 50%, 
          rgba(2, 132, 199, 0.7) 100%
        );
        border-radius: 1px;
        border: none;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, 
          rgba(14, 165, 233, 0.8) 0%, 
          rgba(2, 132, 199, 0.9) 50%, 
          rgba(3, 105, 161, 0.8) 100%
        );
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
      }

      /* 火狐浏览器的滚动条样式 */
      .custom-scrollbar {
        scrollbar-width: thin;
        scrollbar-color: rgba(56, 189, 248, 0.7) rgba(240, 249, 255, 0.6);
      }

      /* 确保滚动条在暗色模式下可见 */
      .dark .custom-scrollbar::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, 
          rgba(56, 189, 248, 0.6) 0%, 
          rgba(14, 165, 233, 0.7) 50%, 
          rgba(2, 132, 199, 0.6) 100%
        );
        border: none;
      }

      .dark .custom-scrollbar::-webkit-scrollbar-track {
        background: rgba(30, 58, 138, 0.3);
      }

      .dark .custom-scrollbar {
        scrollbar-color: rgba(56, 189, 248, 0.6) rgba(30, 58, 138, 0.3);
      }

      /* 旅行主题特定颜色 - 更清新的配色 */
      .custom-scrollbar.travel-theme::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, 
          rgba(56, 189, 248, 0.7) 0%, 
          rgba(14, 165, 233, 0.8) 50%, 
          rgba(2, 132, 199, 0.7) 100%
        );
        border: none;
        opacity: 0.9;
      }

      .custom-scrollbar.travel-theme::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, 
          rgba(14, 165, 233, 0.8) 0%, 
          rgba(2, 132, 199, 0.9) 50%, 
          rgba(3, 105, 161, 0.8) 100%
        );
        opacity: 1;
        box-shadow: 0 2px 8px rgba(2, 132, 199, 0.3);
      }

      .custom-scrollbar.travel-theme {
        scrollbar-color: rgba(56, 189, 248, 0.7) rgba(240, 249, 255, 0.6);
      }

      .custom-scrollbar.travel-theme::-webkit-scrollbar-track {
        background: rgba(240, 249, 255, 0.6);
      }
    `}</style>
	);
}
