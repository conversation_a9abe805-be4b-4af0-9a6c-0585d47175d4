"use client";

// 延迟函数 - 将setTimeout转换为Promise
export function delay(ms: number): Promise<void> {
	return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * 日志工具 - 用于在控制台输出带时间戳的日志
 * @param module 模块名称
 * @param message 日志消息
 * @param data 可选的附加数据
 * @param type 日志类型 ('log'|'info'|'warn'|'error'|'debug')
 */
export function logWithTime(
	module: string,
	message: string,
	data?: any,
	type: "log" | "info" | "warn" | "error" | "debug" = "log",
) {
	// 在生产环境下可以禁用某些类型的日志
	if (process.env.NODE_ENV === "production" && type === "debug") {
		return;
	}

	const timestamp = new Date().toISOString().slice(11, 23); // 时:分:秒.毫秒
	const formattedMessage = `[${timestamp}][${module}] ${message}`;

	switch (type) {
		case "info":
			console.info(formattedMessage, data !== undefined ? data : "");
			break;
		case "warn":
			console.warn(formattedMessage, data !== undefined ? data : "");
			break;
		case "error":
			console.error(formattedMessage, data !== undefined ? data : "");
			break;
		case "debug":
			console.debug(formattedMessage, data !== undefined ? data : "");
			break;
		default:
			console.log(formattedMessage, data !== undefined ? data : "");
	}
}

/**
 * 创建特定模块的日志记录器
 * @param moduleName 模块名称
 * @returns 日志记录器函数集合
 */
export function createLogger(moduleName: string) {
	return {
		log: (message: string, data?: any) =>
			logWithTime(moduleName, message, data, "log"),
		info: (message: string, data?: any) =>
			logWithTime(moduleName, message, data, "info"),
		warn: (message: string, data?: any) =>
			logWithTime(moduleName, message, data, "warn"),
		error: (message: string, data?: any) =>
			logWithTime(moduleName, message, data, "error"),
		debug: (message: string, data?: any) =>
			logWithTime(moduleName, message, data, "debug"),
	};
}
