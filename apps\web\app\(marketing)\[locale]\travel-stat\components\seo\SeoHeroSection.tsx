import type { TravelStatTranslations } from "../../hooks/useTravelStatTranslations";

interface SeoHeroSectionProps {
	translations: TravelStatTranslations;
}

export function SeoHeroSection({ translations }: SeoHeroSectionProps) {
	return (
		<div className="relative text-center space-y-8">
			<div className="relative inline-block">
				<div className="absolute -inset-3 bg-gradient-to-r from-blue-400/20 via-purple-400/20 to-pink-400/20 dark:from-blue-600/30 dark:via-purple-600/30 dark:to-pink-600/30 rounded-xl blur-lg opacity-60 animate-pulse" />
				<h2 className="relative text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-800 via-blue-600 to-purple-600 dark:from-slate-100 dark:via-blue-400 dark:to-purple-400 bg-clip-text text-transparent">
					{translations.seo?.mainTitle() ||
						"Capture Your Travel Memories"}
				</h2>
			</div>
			<p className="text-xl text-slate-600 dark:text-slate-300 leading-relaxed font-light max-w-3xl mx-auto">
				{translations.seo?.mainDescription() ||
					"Map Moment helps you preserve precious travel moments on beautiful interactive maps. Mark your visited places, create stunning travel cards, and turn every journey into lasting digital memories worth cherishing."}
			</p>
			{/* 装饰线 */}
			<div className="flex justify-center">
				<div className="w-32 h-1 bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 dark:from-blue-300 dark:via-purple-300 dark:to-pink-300 rounded-full opacity-60" />
			</div>
		</div>
	);
}
