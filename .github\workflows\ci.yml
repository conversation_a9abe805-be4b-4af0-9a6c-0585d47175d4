name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '20'
  PNPM_VERSION: '9.3.0'

jobs:
  changes:
    name: Detect changes
    runs-on: ubuntu-latest
    outputs:
      packages: ${{ steps.changes.outputs.packages }}
      apps: ${{ steps.changes.outputs.apps }}
      has-changes: ${{ steps.changes.outputs.has-changes }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Detect changed files
        id: changes
        run: |
          # 检测改变的文件
          if [ "${{ github.event_name }}" == "pull_request" ]; then
            BASE_SHA="${{ github.event.pull_request.base.sha }}"
          else
            BASE_SHA="${{ github.event.before }}"
          fi
          
          # 如果是第一次推送，使用前一个提交
          if [ "$BASE_SHA" == "0000000000000000000000000000000000000000" ]; then
            BASE_SHA="HEAD~1"
          fi
          
          CHANGED_FILES=$(git diff --name-only $BASE_SHA HEAD)
          
          # 检查是否有包变更
          PACKAGES_CHANGED=$(echo "$CHANGED_FILES" | grep -E '^packages/' | wc -l)
          APPS_CHANGED=$(echo "$CHANGED_FILES" | grep -E '^apps/' | wc -l)
          
          echo "packages=$PACKAGES_CHANGED" >> $GITHUB_OUTPUT
          echo "apps=$APPS_CHANGED" >> $GITHUB_OUTPUT
          echo "has-changes=$((PACKAGES_CHANGED + APPS_CHANGED > 0))" >> $GITHUB_OUTPUT
          
          echo "Changed files:"
          echo "$CHANGED_FILES"

  install:
    name: Install dependencies
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}
          
      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV
          
      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-
            
      - name: Install dependencies
        run: pnpm install --frozen-lockfile

  lint:
    name: Lint & Format
    runs-on: ubuntu-latest
    needs: [install]
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}
          
      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV
          
      - name: Restore pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Run Biome (lint)
        run: pnpm lint
        
      - name: Run Biome (format check)
        run: pnpm prettier

  type-check:
    name: Type Check
    runs-on: ubuntu-latest
    needs: [install]
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}
          
      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV
          
      - name: Restore pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Generate database client
        run: pnpm db:generate
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          
      - name: Type check
        run: pnpm type-check

  build:
    name: Build
    runs-on: ubuntu-latest
    needs: [install, changes]
    if: needs.changes.outputs.has-changes == 'true'
    strategy:
      matrix:
        app: [mapmoment, ai-images]
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}
          
      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV
          
      - name: Restore pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Generate database client
        run: pnpm db:generate
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          
      - name: Build ${{ matrix.app }}
        run: pnpm build:${{ matrix.app }}
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-${{ matrix.app }}
          path: |
            apps/*/dist
            apps/*/.next
          retention-days: 1

  security:
    name: Security Scan
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
          
      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v3
        with:
          sarif_file: 'trivy-results.sarif'

  summary:
    name: CI Summary
    runs-on: ubuntu-latest
    needs: [lint, type-check, build]
    if: always()
    steps:
      - name: Check job statuses
        run: |
          echo "## CI Summary" >> $GITHUB_STEP_SUMMARY
          echo "| Job | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|-----|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| Lint | ${{ needs.lint.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Type Check | ${{ needs.type-check.result }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Build | ${{ needs.build.result }} |" >> $GITHUB_STEP_SUMMARY