"use client";

import {} from "@ui/components/alert";
import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { Textarea } from "@ui/components/textarea";
import { cn } from "@ui/lib";
import {
	Calendar,
	CheckCircle,
	ChevronUp,
	Clock,
	Lightbulb,
	Plus,
	RefreshCw,
	Send,
	TrendingUp,
	X,
	Zap,
} from "lucide-react";
import { useState } from "react";

/**
 * Feature Voting 演示页面
 * 展示Feature Voting系统的设计和功能概念
 */
export default function FeatureVotingDemoPage() {
	const [selectedProduct, setSelectedProduct] = useState("travel-memo");
	const [selectedStatus, setSelectedStatus] = useState("all");

	// 模拟数据
	const mockProducts = [
		{
			id: "travel-memo",
			name: "旅行足迹",
			description: "记录和分享旅行回忆",
		},
		{
			id: "ai-generator",
			name: "AI图片生成器",
			description: "AI驱动的图片创作工具",
		},
		{
			id: "feature-voting",
			name: "特性投票系统",
			description: "收集用户反馈的投票平台",
		},
	];

	const mockFeatures = [
		{
			id: "1",
			title: "支持离线地图功能",
			description:
				"希望能够下载离线地图，在没有网络的情况下也能查看和编辑旅行记录。这对于去偏远地区旅行的用户非常重要。",
			status: "under_consideration",
			voteCount: 24,
			hasVoted: false,
			authorName: "旅行爱好者",
			createdAt: "2024-01-15",
			productId: "travel-memo",
		},
		{
			id: "2",
			title: "添加旅行统计功能",
			description:
				"希望能看到自己的旅行统计，比如去过多少个国家、城市，总旅行距离等。可以生成年度旅行报告。",
			status: "planned",
			voteCount: 18,
			hasVoted: true,
			authorName: "数据控",
			createdAt: "2024-01-12",
			productId: "travel-memo",
		},
		{
			id: "3",
			title: "支持更多AI绘画风格",
			description:
				"希望能够支持更多的绘画风格，比如水彩、油画、素描等。现在的风格选择还比较有限。",
			status: "in_progress",
			voteCount: 31,
			hasVoted: false,
			authorName: "艺术家",
			createdAt: "2024-01-10",
			productId: "ai-generator",
		},
		{
			id: "4",
			title: "批量生成图片功能",
			description:
				"希望能够一次性生成多张图片，而不是每次只能生成一张。这样可以提高工作效率。",
			status: "completed",
			voteCount: 42,
			hasVoted: true,
			authorName: "设计师",
			createdAt: "2024-01-08",
			productId: "ai-generator",
		},
		{
			id: "5",
			title: "添加评论回复功能",
			description:
				"希望在特性投票系统中能够回复其他用户的评论，形成讨论串。这样可以更好地交流想法。",
			status: "under_consideration",
			voteCount: 15,
			hasVoted: false,
			authorName: "产品经理",
			createdAt: "2024-01-05",
			productId: "feature-voting",
		},
	];

	// 状态配置
	const statusConfig = {
		under_consideration: {
			label: "考虑中",
			color: "bg-yellow-100 text-yellow-800 border-yellow-200",
			icon: Clock,
		},
		planned: {
			label: "已计划",
			color: "bg-blue-100 text-blue-800 border-blue-200",
			icon: Calendar,
		},
		in_progress: {
			label: "开发中",
			color: "bg-purple-100 text-purple-800 border-purple-200",
			icon: Zap,
		},
		completed: {
			label: "已完成",
			color: "bg-green-100 text-green-800 border-green-200",
			icon: CheckCircle,
		},
		wont_do: {
			label: "不会做",
			color: "bg-gray-100 text-gray-800 border-gray-200",
			icon: X,
		},
	};

	// 过滤特性请求
	const filteredFeatures = mockFeatures.filter((feature) => {
		const productMatch =
			selectedProduct === "all" || feature.productId === selectedProduct;
		const statusMatch =
			selectedStatus === "all" || feature.status === selectedStatus;
		return productMatch && statusMatch;
	});

	return (
		<div className="min-h-screen bg-background">
			{/* 页面头部 */}
			<div className="border-b bg-white dark:bg-gray-950">
				<div className="container mx-auto py-8 px-4">
					<div className="text-center">
						<h1 className="text-4xl font-bold text-foreground mb-4">
							Feature Voting 功能演示
						</h1>
						<p className="text-xl text-muted-foreground mb-6 max-w-3xl mx-auto">
							体验我们的特性投票系统，为产品功能投票，提交新的特性请求，参与产品发展方向的决策。
						</p>
						<div className="flex justify-center gap-4">
							<Button asChild variant="outline">
								<a href="/zh">返回首页</a>
							</Button>
							<Button asChild>
								<a href="/auth/login">登录使用完整功能</a>
							</Button>
						</div>
					</div>
				</div>
			</div>

			{/* 功能介绍 */}
			<div className="container mx-auto py-8 px-4">
				<div className="grid md:grid-cols-3 gap-6 mb-12">
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<ChevronUp className="h-5 w-5 text-primary" />
								匿名投票
							</CardTitle>
						</CardHeader>
						<CardContent>
							<p className="text-sm text-muted-foreground">
								无需注册登录，即可为喜欢的功能特性投票，影响产品发展方向。
							</p>
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Lightbulb className="h-5 w-5 text-primary" />
								提交想法
							</CardTitle>
						</CardHeader>
						<CardContent>
							<p className="text-sm text-muted-foreground">
								有好的想法？提交新的特性请求，让其他用户为你的想法投票。
							</p>
						</CardContent>
					</Card>

					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<TrendingUp className="h-5 w-5 text-primary" />
								实时更新
							</CardTitle>
						</CardHeader>
						<CardContent>
							<p className="text-sm text-muted-foreground">
								投票结果实时更新，查看最受欢迎的功能特性和发展趋势。
							</p>
						</CardContent>
					</Card>
				</div>

				{/* 演示说明 */}
				<div className="mb-8 p-6 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
					<h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3">
						🎯 演示说明
					</h2>
					<div className="grid md:grid-cols-2 gap-4 text-sm text-blue-800 dark:text-blue-200">
						<div>
							<h3 className="font-medium mb-2">功能特性</h3>
							<ul className="space-y-1">
								<li>• 支持匿名投票，无需登录</li>
								<li>• 多产品特性管理</li>
								<li>• 5种特性状态流转</li>
								<li>• 实时投票计数更新</li>
							</ul>
						</div>
						<div>
							<h3 className="font-medium mb-2">技术亮点</h3>
							<ul className="space-y-1">
								<li>• 依赖注入架构，高度解耦</li>
								<li>• 响应式设计，移动端友好</li>
								<li>• TypeScript全类型支持</li>
								<li>• 暗色模式支持</li>
							</ul>
						</div>
					</div>
				</div>

				{/* 主要功能演示 */}
				<div className="max-w-6xl mx-auto">
					<Tabs defaultValue="voting" className="w-full">
						<TabsList className="grid w-full grid-cols-2">
							<TabsTrigger value="voting">特性投票</TabsTrigger>
							<TabsTrigger value="submit">提交特性</TabsTrigger>
						</TabsList>

						{/* 投票页面 */}
						<TabsContent value="voting" className="space-y-6">
							{/* 筛选器 */}
							<div className="flex flex-wrap gap-4 p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
								<div className="flex-1 min-w-[200px]">
									<Label htmlFor="product-select">
										选择产品
									</Label>
									<Select
										value={selectedProduct}
										onValueChange={setSelectedProduct}
									>
										<SelectTrigger>
											<SelectValue placeholder="选择产品" />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="all">
												所有产品
											</SelectItem>
											{mockProducts.map((product) => (
												<SelectItem
													key={product.id}
													value={product.id}
												>
													{product.name}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
								</div>

								<div className="flex-1 min-w-[200px]">
									<Label htmlFor="status-select">
										筛选状态
									</Label>
									<Select
										value={selectedStatus}
										onValueChange={setSelectedStatus}
									>
										<SelectTrigger>
											<SelectValue placeholder="选择状态" />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="all">
												所有状态
											</SelectItem>
											{Object.entries(statusConfig).map(
												([key, config]) => (
													<SelectItem
														key={key}
														value={key}
													>
														{config.label}
													</SelectItem>
												),
											)}
										</SelectContent>
									</Select>
								</div>

								<div className="flex items-end">
									<Button variant="outline" size="sm">
										<RefreshCw className="h-4 w-4 mr-2" />
										刷新
									</Button>
								</div>
							</div>

							{/* 特性列表 */}
							<div className="space-y-4">
								{filteredFeatures.map((feature) => {
									const StatusIcon =
										statusConfig[
											feature.status as keyof typeof statusConfig
										].icon;
									return (
										<Card
											key={feature.id}
											className="hover:shadow-md transition-shadow"
										>
											<CardContent className="p-6">
												<div className="flex gap-4">
													{/* 投票按钮 */}
													<div className="flex flex-col items-center gap-1">
														<Button
															variant={
																feature.hasVoted
																	? "secondary"
																	: "outline"
															}
															size="sm"
															className="flex flex-col h-auto py-2 px-3 min-w-[60px]"
														>
															<ChevronUp
																className={cn(
																	"h-4 w-4 transition-transform",
																	feature.hasVoted &&
																		"scale-110",
																)}
															/>
															<span className="text-xs font-medium">
																{
																	feature.voteCount
																}
															</span>
														</Button>
													</div>

													{/* 内容 */}
													<div className="flex-1 space-y-3">
														<div className="flex items-start justify-between gap-4">
															<h3 className="text-lg font-semibold text-foreground">
																{feature.title}
															</h3>
															<div
																className={cn(
																	"flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border",
																	statusConfig[
																		feature.status as keyof typeof statusConfig
																	].color,
																)}
															>
																<StatusIcon className="h-3 w-3" />
																{
																	statusConfig[
																		feature.status as keyof typeof statusConfig
																	].label
																}
															</div>
														</div>

														<p className="text-muted-foreground">
															{
																feature.description
															}
														</p>

														<div className="flex items-center gap-4 text-sm text-muted-foreground">
															<span>
																由{" "}
																{
																	feature.authorName
																}{" "}
																提交
															</span>
															<span>•</span>
															<span>
																{
																	feature.createdAt
																}
															</span>
															<span>•</span>
															<span>
																{
																	mockProducts.find(
																		(p) =>
																			p.id ===
																			feature.productId,
																	)?.name
																}
															</span>
														</div>
													</div>
												</div>
											</CardContent>
										</Card>
									);
								})}
							</div>
						</TabsContent>

						{/* 提交页面 */}
						<TabsContent value="submit" className="space-y-6">
							<Card>
								<CardHeader>
									<CardTitle className="flex items-center gap-2">
										<Plus className="h-5 w-5" />
										提交新的特性请求
									</CardTitle>
								</CardHeader>
								<CardContent className="space-y-4">
									<div>
										<Label htmlFor="product">
											选择产品
										</Label>
										<Select>
											<SelectTrigger>
												<SelectValue placeholder="选择产品" />
											</SelectTrigger>
											<SelectContent>
												{mockProducts.map((product) => (
													<SelectItem
														key={product.id}
														value={product.id}
													>
														{product.name}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
									</div>

									<div>
										<Label htmlFor="title">特性标题</Label>
										<Input
											id="title"
											placeholder="简洁描述你想要的功能特性"
										/>
									</div>

									<div>
										<Label htmlFor="description">
											详细描述
										</Label>
										<Textarea
											id="description"
											placeholder="详细描述这个功能特性，包括使用场景、预期效果等"
											rows={4}
										/>
									</div>

									<div>
										<Label htmlFor="author">
											你的名字（可选）
										</Label>
										<Input
											id="author"
											placeholder="留下你的名字，让其他人知道是谁提出的好想法"
										/>
									</div>

									<div>
										<Label htmlFor="email">
											邮箱（可选）
										</Label>
										<Input
											id="email"
											type="email"
											placeholder="留下邮箱，我们会在功能上线时通知你"
										/>
									</div>

									<Button className="w-full">
										<Send className="h-4 w-4 mr-2" />
										提交特性请求
									</Button>
								</CardContent>
							</Card>
						</TabsContent>
					</Tabs>
				</div>

				{/* 技术信息 */}
				<div className="mt-12 p-6 bg-gray-50 dark:bg-gray-900/50 rounded-lg border">
					<h3 className="text-lg font-semibold mb-4">🔧 技术架构</h3>
					<div className="grid md:grid-cols-2 gap-6 text-sm">
						<div>
							<h4 className="font-medium mb-2">前端技术栈</h4>
							<ul className="space-y-1 text-muted-foreground">
								<li>• Next.js 15 + React 18</li>
								<li>• TypeScript + Tailwind CSS</li>
								<li>• Shadcn UI + Radix UI</li>
								<li>• 依赖注入模式</li>
							</ul>
						</div>
						<div>
							<h4 className="font-medium mb-2">后端技术栈</h4>
							<ul className="space-y-1 text-muted-foreground">
								<li>• Node.js + Prisma ORM</li>
								<li>• PostgreSQL 数据库</li>
								<li>• RESTful API 设计</li>
								<li>• 匿名用户管理</li>
							</ul>
						</div>
					</div>
				</div>

				{/* 联系信息 */}
				<div className="mt-8 text-center">
					<p className="text-muted-foreground mb-4">
						想了解更多技术细节或有合作意向？
					</p>
					<div className="flex justify-center gap-4">
						<Button variant="outline" asChild>
							<a href="mailto:<EMAIL>">联系我们</a>
						</Button>
						<Button variant="outline" asChild>
							<a
								href="https://github.com/your-repo"
								target="_blank"
								rel="noopener noreferrer"
							>
								查看源码
							</a>
						</Button>
					</div>
				</div>
			</div>
		</div>
	);
}
