"use client";

import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import {
	ChevronDown,
	ChevronRight,
	Globe,
	HelpCircle,
	MapPin,
	X,
} from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";
import { useTranslatedColorThemes } from "../../hooks/useTranslatedColorThemes";
import { useTravelStatTranslations } from "../../hooks/useTravelStatTranslations";
import type { TravelPoint } from "../../types";
import type { ColorThemeType } from "../../types/colorTypes";
import { getColorHex } from "../../utils/colorUtils";
import type { HierarchicalCountryData } from "../../utils/dataUtils";

interface CountryListProps {
	visitedCountries: HierarchicalCountryData[];
	travelPoints: TravelPoint[];
	currentColorTheme: ColorThemeType;
	onLocationClick?: (
		coordinates: [number, number],
		name: string,
		level: "country" | "region" | "city",
	) => void;
}

interface ExpandedState {
	[countryName: string]: {
		expanded: boolean;
		regions: {
			[regionName: string]: boolean;
		};
	};
}

export function CountryList({
	visitedCountries,
	travelPoints,
	currentColorTheme,
	onLocationClick,
}: CountryListProps) {
	const travelStatT = useTravelStatTranslations();
	const { getTheme } = useTranslatedColorThemes();
	const [showColorGuide, setShowColorGuide] = useState(false);
	const [guidePosition, setGuidePosition] = useState({ top: 0, left: 0 });
	const [expandedState, setExpandedState] = useState<ExpandedState>({});
	const [scrollState, setScrollState] = useState({
		canScrollUp: false,
		canScrollDown: false,
		showScrollHint: true,
	});
	const helpIconRef = useRef<HTMLButtonElement>(null);
	const scrollContainerRef = useRef<HTMLDivElement>(null);

	// 使用传入的分层数据
	const hierarchicalData = visitedCountries;

	// 检测滚动状态
	const checkScrollState = useCallback(() => {
		const container = scrollContainerRef.current;
		if (!container) return;

		const { scrollTop, scrollHeight, clientHeight } = container;
		const canScrollUp = scrollTop > 0;
		const canScrollDown = scrollTop < scrollHeight - clientHeight - 1;

		setScrollState((prev) => ({
			...prev,
			canScrollUp,
			canScrollDown,
		}));
	}, []);

	// 监听滚动事件
	useEffect(() => {
		const container = scrollContainerRef.current;
		if (!container) return;

		// 初始检查
		checkScrollState();

		// 添加滚动监听
		container.addEventListener("scroll", checkScrollState);

		// 监听内容变化
		const resizeObserver = new ResizeObserver(checkScrollState);
		resizeObserver.observe(container);

		return () => {
			container.removeEventListener("scroll", checkScrollState);
			resizeObserver.disconnect();
		};
	}, [checkScrollState, hierarchicalData]);

	// 5秒后隐藏滚动提示
	useEffect(() => {
		const timer = setTimeout(() => {
			setScrollState((prev) => ({ ...prev, showScrollHint: false }));
		}, 5000);

		return () => clearTimeout(timer);
	}, []);

	// 添加隐藏滚动条的样式
	useEffect(() => {
		const style = document.createElement("style");
		style.textContent = `
			.scrollbar-hide::-webkit-scrollbar {
				display: none;
			}
			.scrollbar-hide {
				-ms-overflow-style: none;
				scrollbar-width: none;
			}
		`;
		document.head.appendChild(style);

		return () => {
			if (document.head.contains(style)) {
				document.head.removeChild(style);
			}
		};
	}, []);

	// 计算popover位置
	const calculateGuidePosition = (buttonElement: HTMLButtonElement) => {
		const rect = buttonElement.getBoundingClientRect();
		return {
			top: rect.bottom + 8,
			left: rect.left,
		};
	};

	// 点击外部区域关闭popover
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				helpIconRef.current &&
				!helpIconRef.current.contains(event.target as Node)
			) {
				setShowColorGuide(false);
			}
		};

		if (showColorGuide) {
			document.addEventListener("mousedown", handleClickOutside);
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [showColorGuide]);

	// 处理帮助图标点击
	const handleHelpClick = () => {
		if (!showColorGuide && helpIconRef.current) {
			const position = calculateGuidePosition(helpIconRef.current);
			setGuidePosition(position);
		}
		setShowColorGuide(!showColorGuide);
	};

	// 切换国家展开状态
	const toggleCountryExpanded = useCallback((countryName: string) => {
		setExpandedState((prev) => ({
			...prev,
			[countryName]: {
				...prev[countryName],
				expanded: !prev[countryName]?.expanded,
				regions: prev[countryName]?.regions || {},
			},
		}));
	}, []);

	// 切换区域展开状态
	const toggleRegionExpanded = useCallback(
		(countryName: string, regionName: string) => {
			setExpandedState((prev) => ({
				...prev,
				[countryName]: {
					...prev[countryName],
					expanded: prev[countryName]?.expanded ?? false,
					regions: {
						...prev[countryName]?.regions,
						[regionName]: !prev[countryName]?.regions?.[regionName],
					},
				},
			}));
		},
		[],
	);

	// 处理位置点击
	const handleLocationClick = useCallback(
		(
			coordinates: [number, number],
			name: string,
			level: "country" | "region" | "city",
		) => {
			onLocationClick?.(coordinates, name, level);
		},
		[onLocationClick],
	);

	// 渲染城市
	const renderCity = (
		city: any,
		countryName: string,
		regionName?: string,
	) => (
		<div
			key={`${countryName}-${regionName || "direct"}-${city.name}`}
			className="flex justify-between items-center text-sm pl-8 py-1 hover:bg-sky-50 rounded-md cursor-pointer transition-colors"
			onClick={() =>
				handleLocationClick(city.coordinates, city.name, "city")
			}
			onKeyDown={(e) => {
				if (e.key === "Enter" || e.key === " ") {
					handleLocationClick(city.coordinates, city.name, "city");
				}
			}}
			tabIndex={0}
			role="button"
		>
			<div className="flex items-center gap-2">
				<MapPin className="w-3 h-3 text-gray-400" />
				<div
					className="w-2 h-2 rounded-full border border-white shadow-sm"
					style={{
						backgroundColor: getColorHex(city.visitCount),
					}}
				/>
				<span className="text-gray-600">{city.name}</span>
			</div>
			<Badge
				className="text-xs border ml-auto"
				style={{
					backgroundColor: `${getColorHex(city.visitCount)}20`,
					borderColor: getColorHex(city.visitCount),
					color: getColorHex(city.visitCount),
				}}
			>
				{city.visitCount}
			</Badge>
		</div>
	);

	// 渲染区域
	const renderRegion = (region: any, countryName: string) => {
		const isExpanded =
			expandedState[countryName]?.regions?.[region.name] ?? false;

		return (
			<div key={`${countryName}-${region.name}`} className="ml-4">
				<div
					className="flex justify-between items-center text-sm py-1 hover:bg-sky-50 rounded-md cursor-pointer transition-colors"
					onClick={() =>
						handleLocationClick(
							region.coordinates || [0, 0],
							region.name,
							"region",
						)
					}
					onKeyDown={(e) => {
						if (e.key === "Enter" || e.key === " ") {
							handleLocationClick(
								region.coordinates || [0, 0],
								region.name,
								"region",
							);
						}
					}}
					tabIndex={0}
					role="button"
				>
					<div className="flex items-center gap-2">
						<Button
							size="sm"
							variant="ghost"
							className="w-4 h-4 p-0 hover:bg-sky-100"
							onClick={(e) => {
								e.stopPropagation();
								toggleRegionExpanded(countryName, region.name);
							}}
						>
							{isExpanded ? (
								<ChevronDown className="w-3 h-3 text-gray-500" />
							) : (
								<ChevronRight className="w-3 h-3 text-gray-500" />
							)}
						</Button>
						<div
							className="w-2.5 h-2.5 rounded-full border border-white shadow-sm"
							style={{
								backgroundColor: getColorHex(region.visitCount),
							}}
						/>
						<span className="text-gray-700 font-medium">
							{region.name}
						</span>
					</div>
					<Badge
						className="text-xs border ml-auto"
						style={{
							backgroundColor: `${getColorHex(region.visitCount)}20`,
							borderColor: getColorHex(region.visitCount),
							color: getColorHex(region.visitCount),
						}}
					>
						{region.visitCount}
					</Badge>
				</div>

				{/* 区域下的城市 */}
				{isExpanded && (
					<div className="ml-2 space-y-1">
						{region.cities.map((city: any) =>
							renderCity(city, countryName, region.name),
						)}
					</div>
				)}
			</div>
		);
	};

	// 渲染国家
	const renderCountry = (country: HierarchicalCountryData) => {
		const isExpanded = expandedState[country.name]?.expanded ?? false;
		const hasSubItems =
			country.regions.length > 0 || country.cities.length > 0;

		return (
			<div key={country.name} className="space-y-1">
				<div
					className="flex justify-between items-center text-sm py-1 hover:bg-sky-50 rounded-md cursor-pointer transition-colors"
					onClick={() =>
						handleLocationClick(
							country.coordinates || [0, 0],
							country.name,
							"country",
						)
					}
					onKeyDown={(e) => {
						if (e.key === "Enter" || e.key === " ") {
							handleLocationClick(
								country.coordinates || [0, 0],
								country.name,
								"country",
							);
						}
					}}
					tabIndex={0}
					role="button"
				>
					<div className="flex items-center gap-2">
						{hasSubItems && (
							<Button
								size="sm"
								variant="ghost"
								className="w-4 h-4 p-0 hover:bg-sky-100"
								onClick={(e) => {
									e.stopPropagation();
									toggleCountryExpanded(country.name);
								}}
							>
								{isExpanded ? (
									<ChevronDown className="w-3 h-3 text-gray-500" />
								) : (
									<ChevronRight className="w-3 h-3 text-gray-500" />
								)}
							</Button>
						)}
						{!hasSubItems && <div className="w-4" />}
						<div
							className="w-3 h-3 rounded-full border border-white shadow-sm"
							style={{
								backgroundColor: getColorHex(
									country.visitCount,
								),
							}}
						/>
						<span className="text-gray-700 font-semibold">
							{country.name}
						</span>
					</div>
					<Badge
						className="text-xs border ml-auto"
						style={{
							backgroundColor: `${getColorHex(country.visitCount)}20`,
							borderColor: getColorHex(country.visitCount),
							color: getColorHex(country.visitCount),
						}}
					>
						{country.visitCount}
					</Badge>
				</div>

				{/* 展开的子项 */}
				{isExpanded && hasSubItems && (
					<div className="space-y-1">
						{/* 渲染区域 */}
						{country.regions.map((region) =>
							renderRegion(region, country.name),
						)}

						{/* 渲染直属城市（没有区域的城市） */}
						{country.cities.map((city) =>
							renderCity(city, country.name),
						)}
					</div>
				)}
			</div>
		);
	};

	return (
		<>
			<Card className="bg-white/90 backdrop-blur-sm border-sky-200">
				<CardHeader className="px-3 py-2 pb-1 flex flex-row items-center justify-between">
					<CardTitle className="text-sm font-medium flex items-center gap-2">
						<Globe className="w-4 h-4 text-sky-500" />
						{travelStatT.countryList.title()}
					</CardTitle>
					<Button
						ref={helpIconRef}
						onClick={handleHelpClick}
						onMouseEnter={() => {
							if (!showColorGuide && helpIconRef.current) {
								const position = calculateGuidePosition(
									helpIconRef.current,
								);
								setGuidePosition(position);
								setShowColorGuide(true);
							}
						}}
						size="sm"
						variant="ghost"
						className="w-6 h-6 p-0 hover:bg-sky-50"
						title={travelStatT.countryList.colorGuideHelpTooltip()}
					>
						<HelpCircle className="w-4 h-4 text-gray-400 hover:text-sky-500" />
					</Button>
				</CardHeader>
				<CardContent className="px-3 pb-3 pt-3">
					<div className="relative">
						{/* 顶部渐变遮罩 */}
						{scrollState.canScrollUp && (
							<div className="absolute top-0 left-0 right-0 h-6 bg-gradient-to-b from-white/90 via-white/60 to-transparent dark:from-gray-800/90 dark:via-gray-800/60 dark:to-transparent z-10 pointer-events-none" />
						)}

						{/* 滚动容器 */}
						<div
							ref={scrollContainerRef}
							className="space-y-2 max-h-80 overflow-y-auto scrollbar-hide"
							style={{
								scrollbarWidth: "none",
								msOverflowStyle: "none",
							}}
							onScroll={() => {
								if (scrollState.showScrollHint) {
									setScrollState((prev) => ({
										...prev,
										showScrollHint: false,
									}));
								}
							}}
						>
							{hierarchicalData.length > 0 ? (
								hierarchicalData.map((country) =>
									renderCountry(country),
								)
							) : (
								<p className="text-gray-500 text-sm">
									{travelStatT.countryList.emptyState()}
								</p>
							)}
						</div>

						{/* 底部渐变遮罩 */}
						{scrollState.canScrollDown && (
							<div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white/90 via-white/60 to-transparent dark:from-gray-800/90 dark:via-gray-800/60 dark:to-transparent z-10 pointer-events-none" />
						)}

						{/* 底部滚动提示 */}
						{scrollState.canScrollDown &&
							scrollState.showScrollHint && (
								<div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 z-20 pointer-events-none">
									<div className="flex flex-col items-center space-y-1 animate-bounce">
										<div className="text-xs text-sky-500 font-medium bg-white/90 dark:bg-gray-800/90 px-2 py-1 rounded-full shadow-sm border border-sky-200 dark:border-sky-700">
											向下滑动查看更多
										</div>
										<div className="flex space-x-0.5">
											<div className="w-1 h-1 bg-sky-400 rounded-full animate-pulse" />
											<div className="w-1 h-1 bg-sky-400 rounded-full animate-pulse delay-75" />
											<div className="w-1 h-1 bg-sky-400 rounded-full animate-pulse delay-150" />
										</div>
									</div>
								</div>
							)}
					</div>
				</CardContent>
			</Card>

			{/* 颜色系统说明 Popover */}
			{showColorGuide &&
				typeof document !== "undefined" &&
				createPortal(
					<div
						className="fixed z-[9999] pointer-events-none"
						style={{
							top: guidePosition.top,
							left: guidePosition.left,
						}}
					>
						<div className="pointer-events-auto">
							<Card className="bg-white border-sky-200 shadow-xl w-80">
								<div className="flex items-center justify-between p-3 border-b border-sky-100">
									<span className="text-sm font-medium text-gray-800">
										{travelStatT.countryList.colorGuideTitle()}
									</span>
									<Button
										onClick={() => setShowColorGuide(false)}
										size="sm"
										variant="ghost"
										className="w-5 h-5 p-0"
									>
										<X className="w-3 h-3" />
									</Button>
								</div>
								<div className="p-4">
									{(() => {
										const theme =
											getTheme(currentColorTheme);
										return (
											<>
												<div className="mb-3">
													<p className="text-xs text-gray-600 mb-2">
														{travelStatT.countryList.colorGuide.currentTheme()}
														：
														<span className="font-medium">
															{theme.name}
														</span>
													</p>
													<p className="text-xs text-gray-500 mb-3">
														{theme.description}
													</p>
													<p className="text-xs text-gray-600">
														{travelStatT.countryList.colorGuide.mapColorDescription()}
														：
													</p>
												</div>
												<div className="space-y-2">
													{/* 动态显示当前主题的颜色说明 */}
													<div className="flex items-center gap-2 text-xs">
														<div
															className="w-3 h-3 rounded-sm"
															style={{
																backgroundColor:
																	theme.colors
																		.level1
																		.hex,
															}}
														/>
														<span className="text-gray-700">
															{
																theme.colors
																	.level1
																	.description
															}{" "}
															(
															{travelStatT.countryList.colorGuide.visitCount.one()}
															)
														</span>
													</div>
													<div className="flex items-center gap-2 text-xs">
														<div
															className="w-3 h-3 rounded-sm"
															style={{
																backgroundColor:
																	theme.colors
																		.level2
																		.hex,
															}}
														/>
														<span className="text-gray-700">
															{
																theme.colors
																	.level2
																	.description
															}{" "}
															(
															{travelStatT.countryList.colorGuide.visitCount.two()}
															)
														</span>
													</div>
													<div className="flex items-center gap-2 text-xs">
														<div
															className="w-3 h-3 rounded-sm"
															style={{
																backgroundColor:
																	theme.colors
																		.level3
																		.hex,
															}}
														/>
														<span className="text-gray-700">
															{
																theme.colors
																	.level3
																	.description
															}{" "}
															(
															{travelStatT.countryList.colorGuide.visitCount.three()}
															)
														</span>
													</div>
													<div className="flex items-center gap-2 text-xs">
														<div
															className="w-3 h-3 rounded-sm"
															style={{
																backgroundColor:
																	theme.colors
																		.level4
																		.hex,
															}}
														/>
														<span className="text-gray-700">
															{
																theme.colors
																	.level4
																	.description
															}{" "}
															(
															{travelStatT.countryList.colorGuide.visitCount.four()}
															)
														</span>
													</div>
													<div className="flex items-center gap-2 text-xs">
														<div
															className="w-3 h-3 rounded-sm"
															style={{
																backgroundColor:
																	theme.colors
																		.level5
																		.hex,
															}}
														/>
														<span className="text-gray-700">
															{
																theme.colors
																	.level5
																	.description
															}{" "}
															(
															{travelStatT.countryList.colorGuide.visitCount.five()}
															)
														</span>
													</div>
													<div className="flex items-center gap-2 text-xs">
														<div
															className="w-3 h-3 rounded-sm"
															style={{
																backgroundColor:
																	theme.colors
																		.level6to10
																		.hex,
															}}
														/>
														<span className="text-gray-700">
															{
																theme.colors
																	.level6to10
																	.description
															}{" "}
															(
															{travelStatT.countryList.colorGuide.visitCount.sixToTen()}
															)
														</span>
													</div>
													<div className="flex items-center gap-2 text-xs">
														<div
															className="w-3 h-3 rounded-sm"
															style={{
																backgroundColor:
																	theme.colors
																		.level10plus
																		.hex,
															}}
														/>
														<span className="text-gray-700">
															{
																theme.colors
																	.level10plus
																	.description
															}{" "}
															(
															{travelStatT.countryList.colorGuide.visitCount.tenPlus()}
															)
														</span>
													</div>
													<div className="flex items-center gap-2 text-xs">
														<div
															className="w-3 h-3 rounded-sm"
															style={{
																backgroundColor:
																	theme.colors
																		.unvisited
																		.hex,
															}}
														/>
														<span className="text-gray-700">
															{
																theme.colors
																	.unvisited
																	.description
															}
														</span>
													</div>
												</div>
												<div className="mt-3 pt-3 border-t border-gray-100">
													<p className="text-xs text-gray-500">
														{travelStatT.countryList.colorGuide.tip()}
													</p>
												</div>
											</>
										);
									})()}
								</div>
							</Card>
						</div>
					</div>,
					document.body,
				)}
		</>
	);
}
