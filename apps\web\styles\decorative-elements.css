/* 装饰性元素 */

/* 文艺角落装饰 */
.corner-decoration {
	position: relative;
}

.corner-decoration::before,
.corner-decoration::after {
	content: "";
	position: absolute;
	width: 8px;
	height: 8px;
	border-style: solid;
	border-color: rgba(147, 197, 253, 0.4);
}

.corner-decoration::before {
	top: 8px;
	left: 8px;
	border-width: 1.5px 0 0 1.5px;
	border-radius: 2px 0 0 0;
}

.corner-decoration::after {
	bottom: 8px;
	right: 8px;
	border-width: 0 1.5px 1.5px 0;
	border-radius: 0 0 2px 0;
}

/* 日记标签装饰 */
.label-decoration {
	position: relative;
	padding-left: 12px;
}

.label-decoration::before {
	content: "";
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 4px;
	height: 80%;
	border-radius: 2px;
	background: linear-gradient(to bottom, #93c5fd, rgba(147, 197, 253, 0.3));
}

/* 引言装饰样式 */
.literary-quote {
	font-family: var(--font-serif);
	font-style: italic;
	line-height: 1.6;
	color: rgba(107, 114, 128, 0.95);
	border-left: 2px solid rgba(147, 197, 253, 0.5);
	padding-left: 1rem;
	margin-left: 0.5rem;
	position: relative;
}

.literary-quote::before {
	content: '"';
	position: absolute;
	left: -0.5rem;
	top: -0.5rem;
	font-size: 1.5rem;
	color: rgba(147, 197, 253, 0.6);
	font-family: var(--font-serif);
}

.literary-quote::after {
	content: '"';
	font-size: 1.5rem;
	color: rgba(147, 197, 253, 0.6);
	font-family: var(--font-serif);
	margin-left: 0.2rem;
}

/* 装饰性分割线 */
.decorative-divider {
	position: relative;
	height: 2px;
	background: linear-gradient(
		to right,
		transparent,
		rgba(147, 197, 253, 0.3),
		transparent
	);
	margin: 1.5rem 0;
}

.decorative-divider::before {
	content: "✿";
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	background-color: white;
	padding: 0 0.75rem;
	color: rgba(147, 197, 253, 0.7);
	font-size: 0.75rem;
}

/* 中式图纸背景 */
.rice-paper-bg {
	background-color: #fdfcf9;
	background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5z' fill='%2393c5fd' fill-opacity='0.08' fill-rule='evenodd'/%3E%3C/svg%3E");
	background-size: 60px 60px;
}

/* 拟物化纸张效果 */
.paper-effect {
	position: relative;
	background-color: white;
	border-radius: 0.375rem;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), 0 5px 15px -5px
		rgba(0, 0, 0, 0.05);
}

.paper-effect::before {
	content: "";
	position: absolute;
	top: 1px;
	left: 1px;
	right: 1px;
	bottom: 1px;
	border-radius: 0.375rem;
	box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.15);
	pointer-events: none;
}

/* 小标签样式 */
.delicate-tag {
	display: inline-flex;
	align-items: center;
	padding: 0.25rem 0.5rem;
	background-color: rgba(219, 234, 254, 0.5);
	border: 1px solid rgba(147, 197, 253, 0.3);
	border-radius: 0.25rem;
	font-size: 0.75rem;
	color: rgba(59, 130, 246, 0.9);
	font-weight: 500;
	transition: all 0.2s ease;
}

.delicate-tag:hover {
	background-color: rgba(219, 234, 254, 0.7);
	transform: translateY(-1px);
}

/* 清新卡片内容 */
.fresh-card-content {
	background: linear-gradient(
		120deg,
		rgba(255, 255, 255, 0.8),
		rgba(249, 250, 251, 0.8)
	);
	border-radius: 0.375rem;
	padding: 1rem;
	border: 1px solid rgba(147, 197, 253, 0.15);
}

/* 突出显示内容 */
.highlight-content {
	position: relative;
	padding: 1rem;
	border-radius: 0.375rem;
	background-color: rgba(219, 234, 254, 0.3);
	border-left: 3px solid rgba(96, 165, 250, 0.5);
}
