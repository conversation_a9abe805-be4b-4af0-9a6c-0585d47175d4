"use client";

import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@ui/components/tabs";
import { useState } from "react";

// 预设的图片示例，方便用户测试
const sampleImages = [
	{
		url: "https://images.unsplash.com/photo-1519046904884-53103b34b206",
		description: "海滩风景",
	},
	{
		url: "https://images.unsplash.com/photo-**********-e1885e51cdc5",
		description: "城市街景",
	},
	{
		url: "https://images.unsplash.com/photo-1504674900247-0877df9cc836",
		description: "美食图片",
	},
];

interface AIResult {
	text?: string;
	imageAnalysis?: string;
}

export default function AIDemo() {
	// 文本生成状态
	const [prompt, setPrompt] = useState("");
	const [systemPrompt, setSystemPrompt] = useState("");
	const [provider, setProvider] = useState<string>("openai");

	// 图片识别状态
	const [imageUrl, setImageUrl] = useState("");
	const [imageQuestion, setImageQuestion] = useState("");
	const [imageSystemPrompt, setImageSystemPrompt] = useState("");

	// 共享状态
	const [result, setResult] = useState<AIResult>({});
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState("");
	const [activeTab, setActiveTab] = useState("text");

	// 生成文本
	const generateText = async () => {
		if (!prompt.trim()) return;

		setIsLoading(true);
		setError("");
		setResult({});

		try {
			const response = await fetch("/api/ai-example", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					text: prompt,
					provider,
					systemPrompt: systemPrompt.trim() || undefined,
				}),
			});

			const data = await response.json();

			if (!response.ok) {
				throw new Error(data.error || "请求失败");
			}

			setResult({ text: data.result });
		} catch (err: any) {
			setError(err.message || "生成文本时发生错误");
			console.error("AI请求错误:", err);
		} finally {
			setIsLoading(false);
		}
	};

	// 分析图片
	const analyzeImage = async () => {
		if (!imageUrl.trim() || !imageQuestion.trim()) return;

		setIsLoading(true);
		setError("");
		setResult({});

		try {
			const response = await fetch("/api/ai-image-analysis", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					imageUrl,
					question: imageQuestion,
					provider,
					systemPrompt: imageSystemPrompt.trim() || undefined,
				}),
			});

			const data = await response.json();

			if (!response.ok) {
				throw new Error(data.error || "请求失败");
			}

			setResult({ imageAnalysis: data.result });
		} catch (err: any) {
			setError(err.message || "图片分析时发生错误");
			console.error("AI图片分析错误:", err);
		} finally {
			setIsLoading(false);
		}
	};

	// 使用示例图片
	const useSampleImage = (url: string) => {
		setImageUrl(url);
	};

	return (
		<div className="container mx-auto p-6 max-w-3xl">
			<h1 className="text-2xl font-bold mb-6">多平台 AI 演示</h1>

			<div className="mb-6">
				<label className="block text-sm font-medium mb-2">
					选择 AI 提供商
				</label>
				<select
					value={provider}
					onChange={(e) => setProvider(e.target.value)}
					className="w-full p-2 border rounded-md"
				>
					<option value="openai">OpenAI</option>
					<option value="gemini">Google Gemini</option>
					<option value="volcengine">火山引擎</option>
				</select>
			</div>

			<Tabs
				value={activeTab}
				onValueChange={setActiveTab}
				className="w-full"
			>
				<TabsList className="grid w-full grid-cols-2 mb-6">
					<TabsTrigger value="text">文本生成</TabsTrigger>
					<TabsTrigger value="image">图片识别</TabsTrigger>
				</TabsList>

				{/* 文本生成选项卡 */}
				<TabsContent value="text">
					<div className="space-y-4">
						<div>
							<label className="block text-sm font-medium mb-2">
								系统提示词{" "}
								<span className="text-gray-500 text-xs">
									(可选)
								</span>
							</label>
							<textarea
								value={systemPrompt}
								onChange={(e) =>
									setSystemPrompt(e.target.value)
								}
								className="w-full p-3 border rounded-md"
								placeholder="定义AI助手的角色和行为，例如：'你是一位专业的旅行顾问，擅长提供旅行建议'"
								rows={2}
							/>
						</div>

						<div>
							<label className="block text-sm font-medium mb-2">
								用户提示词
							</label>
							<textarea
								value={prompt}
								onChange={(e) => setPrompt(e.target.value)}
								className="w-full p-3 border rounded-md min-h-[120px]"
								placeholder="输入你想要生成的内容..."
								rows={4}
							/>
						</div>

						<button
							onClick={generateText}
							disabled={isLoading || !prompt.trim()}
							className={`w-full py-2 px-4 rounded-md text-white ${
								isLoading || !prompt.trim()
									? "bg-gray-400 cursor-not-allowed"
									: "bg-blue-600 hover:bg-blue-700"
							}`}
						>
							{isLoading ? "生成中..." : "生成文本"}
						</button>
					</div>
				</TabsContent>

				{/* 图片识别选项卡 */}
				<TabsContent value="image">
					<div className="space-y-4">
						<div>
							<label className="block text-sm font-medium mb-2">
								系统提示词{" "}
								<span className="text-gray-500 text-xs">
									(可选)
								</span>
							</label>
							<textarea
								value={imageSystemPrompt}
								onChange={(e) =>
									setImageSystemPrompt(e.target.value)
								}
								className="w-full p-3 border rounded-md"
								placeholder="定义AI助手的角色，例如：'你是一位图像分析专家，擅长分析图片内容'"
								rows={2}
							/>
						</div>

						<div>
							<label className="block text-sm font-medium mb-2">
								图片 URL
							</label>
							<input
								type="text"
								value={imageUrl}
								onChange={(e) => setImageUrl(e.target.value)}
								className="w-full p-3 border rounded-md"
								placeholder="输入图片URL地址..."
							/>
						</div>

						<div className="grid grid-cols-3 gap-2">
							{sampleImages.map((image, index) => (
								<button
									key={index}
									onClick={() => useSampleImage(image.url)}
									className="text-xs p-2 bg-gray-100 rounded hover:bg-gray-200 truncate"
									title={image.url}
								>
									{image.description}
								</button>
							))}
						</div>

						{imageUrl && (
							<div className="mt-2">
								<img
									src={imageUrl}
									alt="预览图片"
									className="max-h-48 rounded-md mx-auto object-contain"
								/>
							</div>
						)}

						<div>
							<label className="block text-sm font-medium mb-2">
								问题
							</label>
							<textarea
								value={imageQuestion}
								onChange={(e) =>
									setImageQuestion(e.target.value)
								}
								className="w-full p-3 border rounded-md"
								placeholder="询问关于图片的问题，例如：'这张图片展示了什么内容？'"
								rows={2}
							/>
						</div>

						<button
							onClick={analyzeImage}
							disabled={
								isLoading ||
								!imageUrl.trim() ||
								!imageQuestion.trim()
							}
							className={`w-full py-2 px-4 rounded-md text-white ${
								isLoading ||
								!imageUrl.trim() ||
								!imageQuestion.trim()
									? "bg-gray-400 cursor-not-allowed"
									: "bg-blue-600 hover:bg-blue-700"
							}`}
						>
							{isLoading ? "分析中..." : "分析图片"}
						</button>
					</div>
				</TabsContent>
			</Tabs>

			{error && (
				<div className="mt-6 p-3 bg-red-100 text-red-700 rounded-md">
					{error}
				</div>
			)}

			{(result.text || result.imageAnalysis) && (
				<div className="mt-6">
					<h2 className="text-xl font-semibold mb-2">生成结果:</h2>
					<div className="p-4 bg-gray-50 rounded-md whitespace-pre-wrap">
						{result.text || result.imageAnalysis}
					</div>
				</div>
			)}
		</div>
	);
}
