{"name": "mapmoment", "private": true, "scripts": {"build": "dotenv -c -- turbo build", "dev": "dotenv -c -- turbo dev --concurrency 15", "start": "dotenv -c -- turbo start", "lint": "biome lint .", "lint:fix": "biome lint --write .", "prettier": "biome format .", "prettier:fix": "biome format --write .", "type-check": "turbo run type-check", "google-play-crawler": "cd tooling/scripts && pnpm run google-play-scraper", "clean": "turbo clean", "format": "biome format . --write", "ci-check": "bash scripts/ci-check.sh", "ci-check-quick": "pnpm type-check && pnpm build:mapmoment", "// === 服务启动脚本 ===": "", "dev:mapmoment": "dotenv -c -- pnpm --filter @repo/web dev", "dev:ai-images": "dotenv -c -- pnpm --filter @repo/ai-image-generator dev", "dev:video-exporter": "dotenv -c -- pnpm --filter video-exporter dev", "dev:all": "dotenv -c -- turbo dev --concurrency 15", "// === 生产启动脚本 ===": "", "start:mapmoment": "dotenv -c -- pnpm --filter @repo/web start", "start:ai-images": "dotenv -c -- pnpm --filter @repo/ai-image-generator start", "start:video-exporter": "dotenv -c -- pnpm --filter video-exporter start", "start:all": "dotenv -c -- turbo start", "// === 构建脚本 ===": "", "build:mapmoment": "dotenv -c -- pnpm --filter @repo/web build", "build:ai-images": "dotenv -c -- pnpm --filter @repo/ai-image-generator build", "build:video-exporter": "dotenv -c -- pnpm --filter video-exporter build", "build:all": "dotenv -c -- turbo build", "// === 数据库脚本 ===": "", "db:generate": "dotenv -c -- pnpm --filter database generate:all", "db:migrate": "dotenv -c -- pnpm --filter database migrate", "db:migrate:ai": "dotenv -c -- pnpm --filter database tsx scripts/migrate-ai-images.ts migrate", "db:studio": "dotenv -c -- pnpm --filter database studio", "db:studio:ai": "dotenv -c -- pnpm --filter database tsx scripts/migrate-ai-images.ts studio", "// === 测试脚本 ===": "", "test:format-richtext": "dotenv -c -- tsx packages/api/src/test-format-richtext.ts", "test:google-maps": "dotenv -c -- tsx packages/api/src/test-google-maps.ts", "test:unified-geocoding": "dotenv -c -- tsx packages/api/src/test-unified-geocoding.ts", "// === 兼容性脚本（保持向后兼容）===": "", "video-exporter": "pnpm run dev:video-exporter"}, "engines": {"node": ">=20"}, "packageManager": "pnpm@9.3.0", "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/lodash.debounce": "^4.0.9", "@types/node": "^22.14.0", "dotenv-cli": "^8.0.0", "turbo": "^2.5.0", "typescript": "5.8.3"}, "pnpm": {"overrides": {"@types/react": "19.0.0", "@types/react-dom": "19.0.0", "prettier": "^3.4.2"}}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hono/zod-validator": "^0.4.3", "@react-three/drei": "^10.0.7", "@react-three/fiber": "^9.1.2", "@react-three/postprocessing": "^3.0.4", "@types/three": "^0.175.0", "bullmq": "^5.1.1", "lodash.debounce": "^4.0.8", "maath": "^0.10.8", "react-tsparticles": "^2.12.2", "simplex-noise": "^4.0.3", "three": "^0.175.0", "tsparticles": "^3.8.1", "tsparticles-engine": "^2.12.0", "tsparticles-slim": "^2.12.0", "zustand": "^5.0.3"}}