/* 拖拽相关样式 */
.travel-point-item {
	position: relative;
	transition: transform 300ms ease, margin 300ms ease, opacity 200ms ease;
}

.travel-point-item.dragging {
	z-index: 50;
	opacity: 0.85;
	transform: scale(1.02);
	box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px
		rgba(0, 0, 0, 0.1);
	outline: 2px solid var(--travel-primary, #3b82f6);
	border-radius: 0.5rem;
	pointer-events: none;
}

/* 拖拽手柄样式 */
.drag-handle {
	touch-action: none;
	cursor: grab !important;
	opacity: 0.6;
	transition: opacity 0.2s, background-color 0.2s;
}

.drag-handle:hover,
.drag-handle:focus {
	opacity: 1;
	background-color: rgba(156, 163, 175, 0.4);
}

.drag-handle:active {
	cursor: grabbing !important;
	opacity: 1;
}

/* 高亮动画 */
@keyframes highlight-pulse {
	0%,
	100% {
		box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
	}
	50% {
		box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5);
	}
}

.highlight-animation {
	animation: highlight-pulse 1.5s ease;
}

/* DnD动画 */
.sortable-item {
	transition: transform 200ms ease, opacity 200ms ease;
}

.sortable-item.dragging {
	z-index: 1000;
	opacity: 0.8;
	position: relative;
	transform: scale(1.02);
}

/* 滚动条样式 */
.scrollbar-travel::-webkit-scrollbar {
	width: 8px;
}

.scrollbar-travel::-webkit-scrollbar-track {
	background: rgba(241, 245, 249, 0.5);
	border-radius: 9999px;
}

.scrollbar-travel::-webkit-scrollbar-thumb {
	background: rgba(148, 163, 184, 0.4);
	border-radius: 9999px;
}

.scrollbar-travel::-webkit-scrollbar-thumb:hover {
	background: rgba(148, 163, 184, 0.6);
}

/* 拖拽时全局样式 */
body[data-dragging="true"] * {
	cursor: grabbing !important;
}
