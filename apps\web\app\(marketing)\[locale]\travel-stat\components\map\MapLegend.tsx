"use client";

import { Card } from "@ui/components/card";
import type { CountryData } from "../../types";
import { calculateColor, getColorHex } from "../../utils/colorUtils";

interface MapLegendProps {
	visitedCountries: CountryData[];
}

export function MapLegend({ visitedCountries }: MapLegendProps) {
	return (
		<Card className="p-4 bg-white/80 backdrop-blur-sm border-sky-200">
			<h4 className="text-sm font-semibold text-gray-800 mb-3">
				地图图例
			</h4>
			<div className="space-y-2 text-xs">
				{/* 动态生成图例 */}
				{visitedCountries.length === 0 ? (
					<div className="flex items-center gap-2">
						<div
							className="w-3 h-3 rounded-sm"
							style={{
								backgroundColor: getColorHex(0),
							}}
						/>
						<span className="text-gray-600">未访问</span>
					</div>
				) : (
					<>
						{/* 显示当前数据中存在的访问级别 */}
						{Array.from(
							new Set(visitedCountries.map((c) => c.visitCount)),
						)
							.sort((a, b) => a - b)
							.map((count) => (
								<div
									key={count}
									className="flex items-center gap-2"
								>
									<div
										className="w-3 h-3 rounded-sm"
										style={{
											backgroundColor: getColorHex(count),
										}}
									/>
									<span className="text-gray-600">
										{calculateColor(count).description} (
										{count}次)
									</span>
								</div>
							))}
						{/* 总是显示未访问 */}
						<div className="flex items-center gap-2">
							<div
								className="w-3 h-3 rounded-sm"
								style={{
									backgroundColor: getColorHex(0),
								}}
							/>
							<span className="text-gray-600">未访问</span>
						</div>
					</>
				)}
			</div>

			{/* 颜色系统说明 */}
			<div className="mt-3 pt-3 border-t border-gray-200">
				<p className="text-xs text-gray-500 mb-2">颜色系统：</p>
				<div className="grid grid-cols-2 gap-1 text-xs">
					<div className="flex items-center gap-1">
						<div className="w-2 h-2 rounded-sm bg-blue-300" />
						<span className="text-gray-500">1-3次</span>
					</div>
					<div className="flex items-center gap-1">
						<div className="w-2 h-2 rounded-sm bg-green-500" />
						<span className="text-gray-500">4-10次</span>
					</div>
					<div className="flex items-center gap-1">
						<div className="w-2 h-2 rounded-sm bg-yellow-500" />
						<span className="text-gray-500">10+次</span>
					</div>
					<div className="flex items-center gap-1">
						<div className="w-2 h-2 rounded-sm bg-gray-300" />
						<span className="text-gray-500">未访问</span>
					</div>
				</div>
			</div>
		</Card>
	);
}
