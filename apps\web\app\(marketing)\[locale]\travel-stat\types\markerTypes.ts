import type { TravelPoint } from "./index";

// 点位风格类型枚举
export const MARKER_STYLES = {
	CLASSIC: "classic",
	GRADIENT_PULSE: "gradient-pulse",
	NEON_GLOW: "neon-glow",
	PARTICLE_EFFECT: "particle-effect",
	HAND_DRAWN: "hand-drawn", // 新增手绘风格
	EMOJI: "emoji", // 新增表情符号风格
	POLAROID: "polaroid", // 新增拍立得照片风格
	CONSTELLATION: "constellation",
	SEASONAL: "seasonal",
	ARTISTIC: "artistic",
	BADGE: "badge",
} as const;

export type MarkerStyleType =
	(typeof MARKER_STYLES)[keyof typeof MARKER_STYLES];

// 点位风格配置接口
export interface MarkerStyleConfig {
	id: MarkerStyleType;
	name: string;
	description: string;
	preview: string; // 预览图标或emoji
	category: "classic" | "animated" | "artistic" | "interactive" | "emoji";
	isPremium?: boolean;
}

// 点位组件的通用 Props 接口
export interface BaseMarkerProps {
	point: TravelPoint;
	onRemovePoint: (pointId: string) => void;
	isSelected?: boolean;
	scale?: number;
	animationSpeed?: "slow" | "normal" | "fast";
	colorTheme?: string;
}

// 渐变光晕配置接口
export interface GradientPulseConfig {
	primaryColor: string;
	secondaryColor: string;
	pulseSpeed: number;
	glowSize: number;
	intensity: number;
}

// 点位风格配置映射
export const MARKER_STYLE_CONFIGS: Record<MarkerStyleType, MarkerStyleConfig> =
	{
		[MARKER_STYLES.CLASSIC]: {
			id: MARKER_STYLES.CLASSIC,
			name: "经典圆点",
			description: "简洁的圆形标记点",
			preview: "📍",
			category: "classic",
		},
		[MARKER_STYLES.GRADIENT_PULSE]: {
			id: MARKER_STYLES.GRADIENT_PULSE,
			name: "渐变光晕",
			description: "脉动的渐变光晕效果",
			preview: "✨",
			category: "animated",
		},
		[MARKER_STYLES.NEON_GLOW]: {
			id: MARKER_STYLES.NEON_GLOW,
			name: "霓虹发光",
			description: "赛博朋克霓虹发光效果",
			preview: "🌟",
			category: "artistic",
			isPremium: true,
		},
		[MARKER_STYLES.PARTICLE_EFFECT]: {
			id: MARKER_STYLES.PARTICLE_EFFECT,
			name: "粒子特效",
			description: "环绕的粒子动画效果",
			preview: "🎆",
			category: "animated",
			isPremium: true,
		},
		[MARKER_STYLES.HAND_DRAWN]: {
			id: MARKER_STYLES.HAND_DRAWN,
			name: "手绘素描",
			description: "文艺手工绘制风格",
			preview: "✏️",
			category: "artistic",
			isPremium: false,
		},
		[MARKER_STYLES.CONSTELLATION]: {
			id: MARKER_STYLES.CONSTELLATION,
			name: "星座连线",
			description: "点位之间的连线效果",
			preview: "⭐",
			category: "interactive",
			isPremium: true,
		},
		[MARKER_STYLES.SEASONAL]: {
			id: MARKER_STYLES.SEASONAL,
			name: "季节主题",
			description: "根据季节变化的主题样式",
			preview: "🌸",
			category: "artistic",
		},
		[MARKER_STYLES.ARTISTIC]: {
			id: MARKER_STYLES.ARTISTIC,
			name: "艺术风格",
			description: "手绘水彩风格",
			preview: "🎨",
			category: "artistic",
			isPremium: true,
		},
		[MARKER_STYLES.EMOJI]: {
			id: MARKER_STYLES.EMOJI,
			name: "表情符号",
			description: "用表情符号个性化标记",
			preview: "😊",
			category: "emoji",
			isPremium: false,
		},
		[MARKER_STYLES.POLAROID]: {
			id: MARKER_STYLES.POLAROID,
			name: "拍立得照片",
			description: "复古拍立得照片风格，显示图片和描述",
			preview: "📸",
			category: "artistic",
			isPremium: false,
		},
		[MARKER_STYLES.BADGE]: {
			id: MARKER_STYLES.BADGE,
			name: "成就徽章",
			description: "游戏化徽章样式",
			preview: "🏆",
			category: "interactive",
		},
	};

// 预设的渐变光晕主题配置
export const GRADIENT_PULSE_THEMES = {
	ocean: {
		primaryColor: "rgb(14, 165, 233)", // sky-500 - 更亮的蓝色
		secondaryColor: "rgb(56, 189, 248)", // sky-400
		pulseSpeed: 1.8,
		glowSize: 32,
		intensity: 0.9,
	},
	sunset: {
		primaryColor: "rgb(251, 146, 60)", // orange-400
		secondaryColor: "rgb(252, 211, 77)", // yellow-300 - 更亮的黄色
		pulseSpeed: 2.2,
		glowSize: 36,
		intensity: 1.0,
	},
	forest: {
		primaryColor: "rgb(34, 197, 94)", // green-500
		secondaryColor: "rgb(74, 222, 128)", // green-400 - 更亮的绿色
		pulseSpeed: 2.0,
		glowSize: 34,
		intensity: 0.85,
	},
	aurora: {
		primaryColor: "rgb(168, 85, 247)", // purple-500
		secondaryColor: "rgb(14, 165, 233)", // sky-500 - 蓝紫渐变
		pulseSpeed: 1.5,
		glowSize: 40,
		intensity: 1.0,
	},
	fire: {
		primaryColor: "rgb(239, 68, 68)", // red-500
		secondaryColor: "rgb(251, 146, 60)", // orange-400 - 红橙渐变
		pulseSpeed: 2.5,
		glowSize: 38,
		intensity: 0.95,
	},
} as const;

export type GradientPulseTheme = keyof typeof GRADIENT_PULSE_THEMES;

// 粒子特效配置接口
export interface ParticleEffectConfig {
	particleColor: string;
	particleCount: number;
	animationSpeed: number;
	orbitRadius: number;
}

// 预设的粒子特效主题配置
export const PARTICLE_EFFECT_THEMES = {
	fire: {
		particleColor: "rgb(251, 146, 60)", // orange-400
		particleCount: 8,
		animationSpeed: 1.2,
		orbitRadius: 35,
	},
	electric: {
		particleColor: "rgb(59, 130, 246)", // blue-500
		particleCount: 10,
		animationSpeed: 1.5,
		orbitRadius: 40,
	},
	magic: {
		particleColor: "rgb(168, 85, 247)", // purple-500
		particleCount: 12,
		animationSpeed: 0.8,
		orbitRadius: 45,
	},
	nature: {
		particleColor: "rgb(34, 197, 94)", // green-500
		particleCount: 6,
		animationSpeed: 1.0,
		orbitRadius: 30,
	},
} as const;

export type ParticleEffectTheme = keyof typeof PARTICLE_EFFECT_THEMES;

// 手绘笔触配置接口
export interface HandDrawnConfig {
	name: string;
	color: string;
	strokeWidth: number;
	opacity: number;
	roughness: number;
	description: string;
}

// 预设的手绘笔触主题配置
export const HAND_DRAWN_THEMES = {
	pencil: {
		name: "铅笔",
		color: "rgb(75, 85, 99)", // gray-600
		strokeWidth: 1.5,
		opacity: 0.8,
		roughness: 0.6,
		description: "轻柔细腻",
	},
	pen: {
		name: "钢笔",
		color: "rgb(30, 41, 59)", // slate-800
		strokeWidth: 2,
		opacity: 0.9,
		roughness: 0.4,
		description: "清晰锐利",
	},
	charcoal: {
		name: "炭笔",
		color: "rgb(15, 23, 42)", // slate-900
		strokeWidth: 2.5,
		opacity: 0.7,
		roughness: 0.8,
		description: "粗犷厚重",
	},
	marker: {
		name: "马克笔",
		color: "rgb(59, 130, 246)", // blue-500
		strokeWidth: 3,
		opacity: 0.6,
		roughness: 0.3,
		description: "饱满鲜艳",
	},
} as const;

export type HandDrawnTheme = keyof typeof HAND_DRAWN_THEMES;

// 拍立得照片风格配置接口
export interface PolaroidConfig {
	name: string;
	frameColor: string;
	shadowColor: string;
	shadowIntensity: number;
	rotation: number; // 旋转角度范围
	borderWidth: number;
	bottomBorderRatio: number; // 底部边框比例
	description: string;
}

// 预设的拍立得主题配置
export const POLAROID_THEMES = {
	classic: {
		name: "经典白色",
		frameColor: "#ffffff",
		shadowColor: "rgba(0, 0, 0, 0.2)",
		shadowIntensity: 0.3,
		rotation: 5, // ±5度随机旋转
		borderWidth: 8,
		bottomBorderRatio: 2.5, // 底部边框是普通边框的2.5倍
		description: "经典的白色拍立得相框",
	},
	vintage: {
		name: "复古泛黄",
		frameColor: "#fef3c7", // yellow-100
		shadowColor: "rgba(92, 73, 38, 0.25)",
		shadowIntensity: 0.4,
		rotation: 8,
		borderWidth: 10,
		bottomBorderRatio: 2.8,
		description: "带有岁月痕迹的泛黄相框",
	},
	pastel: {
		name: "马卡龙粉",
		frameColor: "#fce7f3", // pink-100
		shadowColor: "rgba(157, 23, 77, 0.15)",
		shadowIntensity: 0.25,
		rotation: 6,
		borderWidth: 8,
		bottomBorderRatio: 2.2,
		description: "温柔的粉色马卡龙色调",
	},
	mint: {
		name: "薄荷绿",
		frameColor: "#d1fae5", // green-100
		shadowColor: "rgba(5, 150, 105, 0.2)",
		shadowIntensity: 0.3,
		rotation: 7,
		borderWidth: 9,
		bottomBorderRatio: 2.4,
		description: "清新的薄荷绿调",
	},
	sky: {
		name: "天空蓝",
		frameColor: "#dbeafe", // blue-100
		shadowColor: "rgba(30, 64, 175, 0.2)",
		shadowIntensity: 0.35,
		rotation: 4,
		borderWidth: 8,
		bottomBorderRatio: 2.6,
		description: "纯净的天空蓝调",
	},
} as const;

export type PolaroidTheme = keyof typeof POLAROID_THEMES;
