"use client";

interface ProgressBarProps {
	current: number;
	total: number;
}

export function ProgressBar({ current, total }: ProgressBarProps) {
	const progress = Math.min(Math.max((current / total) * 100, 0), 100);

	return (
		<div className="fixed top-0 left-0 right-0 h-1 bg-neutral-800 z-20">
			<div
				className="h-full bg-primary transition-all duration-300 ease-out"
				style={{ width: `${progress}%` }}
			/>
		</div>
	);
}
