import * as fs from "node:fs";
import * as path from "node:path";
import * as dotenv from "dotenv";

// 尝试加载项目根目录的.env文件
const rootDir = path.resolve(__dirname, "../../../");
const envPaths = [path.join(rootDir, ".env.local"), path.join(rootDir, ".env")];

// 按优先级依次尝试加载环境变量文件
for (const envPath of envPaths) {
	if (fs.existsSync(envPath)) {
		console.log(`Loading environment from: ${envPath}`);
		dotenv.config({ path: envPath });
	}
}

import { db } from "@repo/database/src/client";
import { logger } from "@repo/logs";
import { Queue, QueueEvents, Worker } from "bullmq";
import { videoExportProcessor } from "./processors/video-export-processor";
import type { JobData } from "./types";
import { createRedisConnection } from "./utils/redis";

// 连接选项
const connection = createRedisConnection();

// 队列名称
const QUEUE_NAME = "video-export-queue";

// 创建队列
const videoExportQueue = new Queue<JobData>(QUEUE_NAME, {
	connection,
	defaultJobOptions: {
		attempts: 3,
		backoff: {
			type: "exponential",
			delay: 5000,
		},
		removeOnComplete: false,
		removeOnFail: false,
	},
});

// 队列事件
const queueEvents = new QueueEvents(QUEUE_NAME, { connection });

// 处理视频导出任务的 Worker
const worker = new Worker<JobData>(
	QUEUE_NAME,
	async (job) => {
		logger.info(`开始处理任务 ${job.id}`, {
			taskId: job.data.taskId,
			diaryId: job.data.diaryId,
		});

		try {
			// 更新任务状态为处理中
			await db.videoExportTask.update({
				where: { id: job.data.taskId },
				data: {
					status: "processing",
					progress: 10,
				},
			});

			// 调用处理器执行视频导出逻辑
			const result = await videoExportProcessor(job.data);

			// 更新任务状态为已完成
			await db.videoExportTask.update({
				where: { id: job.data.taskId },
				data: {
					status: "completed",
					videoUrl: result.videoUrl,
					progress: 100,
				},
			});

			return result;
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : String(error);
			logger.error(`任务处理失败: ${errorMessage}`, {
				taskId: job.data.taskId,
				diaryId: job.data.diaryId,
			});

			// 更新任务状态为失败
			await db.videoExportTask.update({
				where: { id: job.data.taskId },
				data: {
					status: "failed",
					errorMessage,
				},
			});

			throw error;
		}
	},
	{
		connection,
		concurrency: 1, // 一次处理一个任务
		limiter: {
			max: 2, // 最多2个任务/每分钟
			duration: 60000,
		},
	},
);

// 事件监听
queueEvents.on("completed", ({ jobId, returnvalue }) => {
	const result = JSON.parse(returnvalue);
	logger.info(`任务 ${jobId} 已完成`, { result });
});

queueEvents.on("failed", ({ jobId, failedReason }) => {
	logger.error(`任务 ${jobId} 失败: ${failedReason}`);
});

// 优雅关闭
async function shutdown() {
	logger.info("正在关闭服务...");

	await worker.close();
	await videoExportQueue.close();
	await connection.quit();

	logger.info("服务已关闭");
	process.exit(0);
}

// 监听进程终止信号
process.on("SIGTERM", shutdown);
process.on("SIGINT", shutdown);

// 启动消息
logger.info("视频导出服务已启动，正在监听队列...");

// 导出队列添加任务的函数
export async function addExportTask(jobData: JobData): Promise<string> {
	const job = await videoExportQueue.add("export-video", jobData, {
		removeOnComplete: false,
		removeOnFail: false,
	});

	return job.id as string;
}
