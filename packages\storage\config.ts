import { logger } from "@repo/logs";
import type { StorageProviderConfig, StorageProviderType } from "./index";

/**
 * 从环境变量中获取S3配置
 */
export function getS3ConfigFromEnv(): StorageProviderConfig {
	const s3Endpoint = process.env.S3_ENDPOINT as string;
	if (!s3Endpoint) {
		throw new Error("缺少环境变量 S3_ENDPOINT");
	}

	const s3Region = (process.env.S3_REGION as string) || "auto";

	const s3AccessKeyId = process.env.S3_ACCESS_KEY_ID as string;
	if (!s3AccessKeyId) {
		throw new Error("缺少环境变量 S3_ACCESS_KEY_ID");
	}

	const s3SecretAccessKey = process.env.S3_SECRET_ACCESS_KEY as string;
	if (!s3SecretAccessKey) {
		throw new Error("缺少环境变量 S3_SECRET_ACCESS_KEY");
	}

	return {
		endpoint: s3Endpoint,
		region: s3Region,
		accessKeyId: s3AccessKeyId,
		secretAccessKey: s3SecretAccessKey,
	};
}

/**
 * 从环境变量中获取Cloudflare R2配置
 */
export function getCloudflareR2ConfigFromEnv(): StorageProviderConfig {
	const r2AccountId = process.env.CLOUDFLARE_R2_ACCOUNT_ID;
	if (!r2AccountId) {
		throw new Error("缺少环境变量 CLOUDFLARE_R2_ACCOUNT_ID");
	}

	// Cloudflare R2端点格式: https://<account_id>.r2.cloudflarestorage.com
	const r2Endpoint =
		process.env.CLOUDFLARE_R2_ENDPOINT ||
		`https://${r2AccountId}.r2.cloudflarestorage.com`;

	const r2AccessKeyId = process.env.CLOUDFLARE_R2_ACCESS_KEY_ID as string;
	if (!r2AccessKeyId) {
		throw new Error("缺少环境变量 CLOUDFLARE_R2_ACCESS_KEY_ID");
	}

	const r2SecretAccessKey = process.env
		.CLOUDFLARE_R2_SECRET_ACCESS_KEY as string;
	if (!r2SecretAccessKey) {
		throw new Error("缺少环境变量 CLOUDFLARE_R2_SECRET_ACCESS_KEY");
	}

	return {
		endpoint: r2Endpoint,
		region: "auto", // Cloudflare R2不使用区域，但需要提供一个值
		accessKeyId: r2AccessKeyId,
		secretAccessKey: r2SecretAccessKey,
	};
}

/**
 * 从环境变量中获取腾讯云COS配置
 */
export function getTencentCOSConfigFromEnv(): StorageProviderConfig {
	const cosRegion = process.env.TENCENT_COS_REGION as string;
	if (!cosRegion) {
		throw new Error("缺少环境变量 TENCENT_COS_REGION");
	}

	const cosAccessKeyId = process.env.TENCENT_COS_SECRET_ID as string;
	if (!cosAccessKeyId) {
		throw new Error("缺少环境变量 TENCENT_COS_SECRET_ID");
	}

	const cosSecretAccessKey = process.env.TENCENT_COS_SECRET_KEY as string;
	if (!cosSecretAccessKey) {
		throw new Error("缺少环境变量 TENCENT_COS_SECRET_KEY");
	}

	return {
		region: cosRegion,
		accessKeyId: cosAccessKeyId,
		secretAccessKey: cosSecretAccessKey,
	};
}

/**
 * 从环境变量中获取阿里云OSS配置
 */
export function getAliyunOSSConfigFromEnv(): StorageProviderConfig {
	// 阿里云OSS端点优先使用 ALIYUN_OSS_ENDPOINT，如果没有则基于区域构建
	const ossRegion = process.env.ALIYUN_OSS_REGION as string;
	const ossEndpoint =
		(process.env.ALIYUN_OSS_ENDPOINT as string) ||
		(ossRegion ? `https://oss-${ossRegion}.aliyuncs.com` : undefined);

	if (!ossEndpoint && !ossRegion) {
		throw new Error(
			"缺少环境变量 ALIYUN_OSS_ENDPOINT 或 ALIYUN_OSS_REGION",
		);
	}

	const ossAccessKeyId = process.env.ALIYUN_OSS_ACCESS_KEY_ID as string;
	if (!ossAccessKeyId) {
		throw new Error("缺少环境变量 ALIYUN_OSS_ACCESS_KEY_ID");
	}

	const ossSecretAccessKey = process.env
		.ALIYUN_OSS_SECRET_ACCESS_KEY as string;
	if (!ossSecretAccessKey) {
		throw new Error("缺少环境变量 ALIYUN_OSS_SECRET_ACCESS_KEY");
	}

	return {
		endpoint: ossEndpoint,
		region: ossRegion,
		accessKeyId: ossAccessKeyId,
		secretAccessKey: ossSecretAccessKey,
	};
}

/**
 * 根据存储提供商类型获取对应的环境变量配置
 */
export function getProviderConfigFromEnv(
	type: StorageProviderType,
): StorageProviderConfig {
	try {
		switch (type) {
			case "s3":
				return getS3ConfigFromEnv();
			case "cloudflare-r2":
				return getCloudflareR2ConfigFromEnv();
			case "tencent-cos":
				return getTencentCOSConfigFromEnv();
			case "aliyun-oss":
				return getAliyunOSSConfigFromEnv();
			default:
				throw new Error(`不支持的存储提供商类型: ${type}`);
		}
	} catch (error) {
		logger.error(
			`从环境变量获取存储提供商配置失败: ${(error as Error).message}`,
		);
		throw error;
	}
}

/**
 * 从环境变量中检测存储提供商类型
 */
export function detectProviderTypeFromEnv(): StorageProviderType | undefined {
	// 检测存储提供商类型的环境变量
	const explicitType = process.env.STORAGE_PROVIDER_TYPE as
		| StorageProviderType
		| undefined;
	if (explicitType) {
		return explicitType;
	}

	// 通过检查特定的环境变量来推断提供商类型
	if (process.env.S3_ENDPOINT && process.env.S3_ACCESS_KEY_ID) {
		return "s3";
	}

	if (
		(process.env.ALIYUN_OSS_REGION || process.env.ALIYUN_OSS_ENDPOINT) &&
		process.env.ALIYUN_OSS_ACCESS_KEY_ID
	) {
		return "aliyun-oss";
	}

	if (
		(process.env.CLOUDFLARE_R2_ACCOUNT_ID ||
			process.env.CLOUDFLARE_R2_ENDPOINT) &&
		process.env.CLOUDFLARE_R2_ACCESS_KEY_ID
	) {
		return "cloudflare-r2";
	}

	if (process.env.TENCENT_COS_REGION && process.env.TENCENT_COS_SECRET_ID) {
		return "tencent-cos";
	}

	return undefined;
}
