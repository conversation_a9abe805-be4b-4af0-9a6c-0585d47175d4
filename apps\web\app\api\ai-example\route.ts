import {
	AIProviderType,
	createAIClient,
	createGeminiClient,
	createOpenAIClient,
	createVolcengineClient,
} from "@packages/ai";
import { NextResponse } from "next/server";

// 环境变量 (在实际使用中应该从 .env 文件中获取)
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const VOLCENGINE_API_KEY = process.env.VOLCENGINE_API_KEY;
const VOLCENGINE_SECRET_KEY = process.env.VOLCENGINE_SECRET_KEY;

/**
 * 创建AI客户端的示例
 */
export async function POST(req: Request) {
	try {
		const { text, provider, systemPrompt } = await req.json();

		if (!text) {
			return NextResponse.json(
				{ error: "缺少文本参数" },
				{ status: 400 },
			);
		}

		let aiClient;
		let result;

		// 选择使用的AI提供商
		switch (provider) {
			case "openai":
				// 使用便捷函数创建OpenAI客户端
				aiClient = createOpenAIClient("gpt-4o-mini", OPENAI_API_KEY);
				break;

			case "gemini":
				// 使用便捷函数创建Google Gemini客户端
				aiClient = createGeminiClient(GEMINI_API_KEY!);
				break;

			case "volcengine":
				// 使用便捷函数创建火山引擎客户端
				aiClient = createVolcengineClient(
					VOLCENGINE_API_KEY!,
					VOLCENGINE_SECRET_KEY!,
				);
				break;

			default:
				// 使用通用的创建方法 (更灵活的配置)
				aiClient = createAIClient({
					provider: AIProviderType.OPENAI,
					model: "gpt-4o-mini",
					apiKey: OPENAI_API_KEY,
				});
		}

		// 创建选项对象
		const options: Record<string, any> = {
			temperature: 0.7,
			maxTokens: 500,
		};

		// 如果提供了系统提示词，添加到选项中
		if (systemPrompt) {
			options.systemPrompt = systemPrompt;
		}

		// 生成文本响应
		result = await aiClient.generateText(text, options);

		return NextResponse.json({ result });
	} catch (error: any) {
		console.error("AI处理错误:", error);
		return NextResponse.json(
			{ error: error.message || "处理请求时发生错误" },
			{ status: 500 },
		);
	}
}
