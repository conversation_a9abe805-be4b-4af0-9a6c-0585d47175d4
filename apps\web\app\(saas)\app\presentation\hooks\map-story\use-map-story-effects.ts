"use client";

import { useEffect } from "react";
import { ANIMATION_DURATION, StoryState } from "../../constants";
import type { MapStoryNavigationContext } from "./types";
import { createLogger } from "./utils";

// 创建效果模块专用的日志记录器
const logger = createLogger("MapStoryEffects");

export function useMapStoryEffects(context: MapStoryNavigationContext): void {
	const { props, state, setters, refs, timers, animation, navigation } =
		context;
	const {
		storyState,
		currentPointIndex,
		isMapReady,
		isPlaying,
		showPointInfo,
		fitBoundsToPoints,
		mapInstance,
		isFinishing,
		manuallyReset,
	} = state;
	const { points } = props;
	const {
		setBlinkingPoints,
		setFlyToPointIndex,
		setShowPointInfo,
		setShowImages,
		setFitBoundsToPoints,
		setAutoPlayCountdown,
	} = setters;
	const { blinkTimerRef, autoPlayTimerRef, isAnimatingRef } = refs;
	const { clearBlinkTimer, clearPlayTimer } = timers;
	const { updateSpotlightPosition } = animation;
	const { startJourney } = navigation;

	// 点位闪烁效果
	useEffect(() => {
		if (storyState !== StoryState.OVERVIEW || points.length === 0) {
			clearBlinkTimer();
			return;
		}

		// 使用 ref 跟踪当前闪烁的点位索引
		let currentBlinkIndex = 0;

		// 按顺序一个个闪烁点位
		const updateBlinkingPoints = () => {
			// 每次只闪烁一个点位
			setBlinkingPoints([currentBlinkIndex]);

			// 更新索引，循环所有点位
			currentBlinkIndex = (currentBlinkIndex + 1) % points.length;

			logger.debug("闪烁点位更新", {
				currentIndex: currentBlinkIndex,
				totalPoints: points.length,
			});
		};

		// 初始设置
		updateBlinkingPoints();

		// 定期更新闪烁的点位
		blinkTimerRef.current = setInterval(() => {
			updateBlinkingPoints();
		}, ANIMATION_DURATION.blinkInterval);

		return () => {
			clearBlinkTimer();
		};
	}, [
		storyState,
		points.length,
		clearBlinkTimer,
		blinkTimerRef,
		setBlinkingPoints,
	]);

	// 监听地图移动，更新聚光灯位置
	useEffect(() => {
		if (!mapInstance || !showPointInfo) return;

		const handleMapMove = () => {
			updateSpotlightPosition();
		};

		mapInstance.on("move", handleMapMove);
		updateSpotlightPosition(); // 初始更新

		return () => {
			mapInstance.off("move", handleMapMove);
		};
	}, [mapInstance, showPointInfo, updateSpotlightPosition]);

	// 监听窗口大小变化，更新布局
	useEffect(() => {
		const handleResize = () => {
			// 强制更新聚光灯位置和信息面板位置
			if (showPointInfo) {
				updateSpotlightPosition();
			}
		};

		window.addEventListener("resize", handleResize);
		return () => {
			window.removeEventListener("resize", handleResize);
		};
	}, [showPointInfo, updateSpotlightPosition]);

	// 自动播放倒计时
	useEffect(() => {
		// 仅在概览状态、地图已加载、未播放且未手动重置过的情况下启动倒计时
		if (
			storyState === StoryState.OVERVIEW &&
			isMapReady &&
			!isPlaying &&
			!manuallyReset &&
			props.config?.autoPlay !== false
		) {
			logger.debug("启动自动播放倒计时", {
				storyState,
				isMapReady,
				manuallyReset,
			});

			// 创建一个标记，确保startJourney只会被调用一次
			let hasTriggeredStart = false;

			// 启动倒计时
			autoPlayTimerRef.current = setInterval(() => {
				setAutoPlayCountdown((prev) => {
					if (prev <= 1) {
						// 当倒计时结束，清除定时器并开始播放
						if (autoPlayTimerRef.current) {
							clearInterval(autoPlayTimerRef.current);
							autoPlayTimerRef.current = null;
						}

						// 只有在尚未触发开始的情况下才调用startJourney
						if (!hasTriggeredStart) {
							hasTriggeredStart = true;
							// 开始旅程
							logger.info("自动播放倒计时结束，启动旅程", {
								countdownFrom: 3,
								autoPlayEnabled:
									props.config?.autoPlay !== false,
								hasTriggeredBefore: false,
							});
							startJourney();
						} else {
							logger.debug("自动播放已触发，跳过重复触发", {
								hasTriggeredBefore: true,
							});
						}
						return 0;
					}
					return prev - 1;
				});
			}, 1000);

			// 清理函数
			return () => {
				if (autoPlayTimerRef.current) {
					clearInterval(autoPlayTimerRef.current);
					autoPlayTimerRef.current = null;
				}
			};
		}
	}, [
		storyState,
		isMapReady,
		isPlaying,
		manuallyReset,
		props.config?.autoPlay,
		autoPlayTimerRef,
		setAutoPlayCountdown,
		startJourney,
	]);

	// 组件卸载时清理动画资源
	useEffect(() => {
		return () => {
			logger.debug("组件卸载，清理动画资源");
			clearPlayTimer();
			// 清理自动播放计时器
			if (autoPlayTimerRef.current) {
				clearInterval(autoPlayTimerRef.current);
				autoPlayTimerRef.current = null;
			}
		};
	}, [clearPlayTimer, autoPlayTimerRef]);

	// 重置fitBoundsToPoints状态
	useEffect(() => {
		if (fitBoundsToPoints) {
			// 一旦应用，重置状态避免重复触发
			setTimeout(() => setFitBoundsToPoints(false), 100);
		}
	}, [fitBoundsToPoints, setFitBoundsToPoints]);
}
