// 简单测试CardPreviewArea.tsx的import语句
const fs = require('fs');
const path = require('path');

// 读取CardPreviewArea.tsx文件
const filePath = path.join(__dirname, 'apps/web/app/(marketing)/[locale]/travel-stat/components/card-generator/CardPreviewArea.tsx');

try {
  const content = fs.readFileSync(filePath, 'utf8');
  
  // 检查问题的模式
  const problemPatterns = [
    'exportMapImage',
    'useMapExporter({',
    'useMapExporter(mapRef'
  ];
  
  let hasProblems = false;
  
  problemPatterns.forEach(pattern => {
    if (content.includes(pattern)) {
      console.log(`❌ 发现问题模式: ${pattern}`);
      hasProblems = true;
    }
  });
  
  // 检查正确的模式
  const correctPatterns = [
    'const { exportMap, isExporting: mapExporting, currentMethod } = useMapExporter()',
    'await exportMap(mapRef.current, adaptedTravelPoints, {'
  ];
  
  let hasCorrectPatterns = true;
  
  correctPatterns.forEach(pattern => {
    if (!content.includes(pattern)) {
      console.log(`❌ 缺少正确模式: ${pattern}`);
      hasCorrectPatterns = false;
    } else {
      console.log(`✅ 发现正确模式: ${pattern}`);
    }
  });
  
  if (!hasProblems && hasCorrectPatterns) {
    console.log('\n🎉 CardPreviewArea.tsx 导出功能修复成功!');
    console.log('✅ 移除了错误的 exportMapImage 调用');
    console.log('✅ 修复了 useMapExporter 的使用方式');
    console.log('✅ 正确调用 exportMap 方法');
  } else {
    console.log('\n⚠️  可能还有问题需要修复');
  }
  
} catch (error) {
  console.error('读取文件失败:', error.message);
} 