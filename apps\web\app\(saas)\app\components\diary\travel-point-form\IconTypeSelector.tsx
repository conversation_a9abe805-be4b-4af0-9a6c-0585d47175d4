import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { useTranslations } from "next-intl";
import type { FormFieldProps, IconOption } from "./types";

interface IconTypeSelectorProps extends FormFieldProps {
	iconOptions: IconOption[];
}

export function IconTypeSelector({
	formData,
	setFormData,
	iconOptions,
}: IconTypeSelectorProps) {
	const t = useTranslations();
	return (
		<div>
			<label
				htmlFor="icon-type-group"
				className="block text-sm font-medium mb-1"
			>
				{t("travelMemo.travelPointForm.labels.iconType")}
			</label>
			<div
				id="icon-type-group"
				className="grid grid-cols-2 md:grid-cols-4 gap-2"
			>
				{iconOptions.map((icon) => (
					<Button
						key={icon.id}
						type="button"
						variant={
							formData.iconType === icon.id
								? "primary"
								: "outline"
						}
						className={cn(
							"justify-start transition-all",
							formData.iconType === icon.id
								? "bg-travel-primary text-travel-dark font-semibold ring-2 ring-travel-primary/30 ring-offset-1"
								: "hover:bg-travel-primary/10",
						)}
						onClick={() => {
							console.log(
								t(
									"travelMemo.travelPointForm.logs.iconSelected",
									{ iconId: icon.id },
								),
							);
							setFormData((prev) => ({
								...prev,
								iconType: icon.id,
							}));
						}}
					>
						{t(icon.label as any)}
					</Button>
				))}
			</div>
		</div>
	);
}
