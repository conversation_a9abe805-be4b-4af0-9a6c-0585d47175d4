"use client";

import { MapPin } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";
import { getIconTypeFromMapboxData } from "../utils/mapbox-icon-mapper";
import { StandaloneSearchBox } from "./StandaloneSearchBox";
import type { FormFieldProps } from "./types";

export function LocationField({ formData, setFormData }: FormFieldProps) {
	const t = useTranslations();
	const [location, setLocation] = useState(formData.location || "");
	const [isReady, setIsReady] = useState(false);
	const [searchBoxKey, setSearchBoxKey] = useState(0); // Key for resetting SearchBox

	// 添加调试日志 - 组件挂载和关键状态变化
	useEffect(() => {
		console.log("[LocationField] 组件挂载/更新", {
			formDataLocation: formData.location,
			localLocation: location,
			searchBoxKey,
			coordinates: formData.coordinates,
		});
	}, [formData.location, location, searchBoxKey, formData.coordinates]);

	// 简单检查环境变量加载状态
	useEffect(() => {
		// 验证Mapbox token是否存在
		if (process.env.NEXT_PUBLIC_MAPBOX_TOKEN) {
			setIsReady(true);
			console.log("[LocationField] Mapbox token 已加载，组件就绪");
		} else {
			console.error("Mapbox token未设置，请检查环境变量");
			toast.error(t("travelMemo.travelPointForm.errors.mapConfigError"));
		}
	}, [t]);

	// 使用 useCallback 包装地点选择处理函数，避免不必要的重渲染
	const handlePlaceSelect = useCallback(
		(place: any) => {
			console.log("[LocationField] handlePlaceSelect 开始", {
				place,
				currentLocation: location,
				currentFormData: formData,
			});

			if (!place.geometry?.location) {
				console.error("[LocationField] 地点缺少坐标信息", place);
				toast.error(
					t("travelMemo.travelPointForm.errors.coordinatesError"),
				);
				return;
			}

			// 获取位置名称
			const locationName = place.name || place.formatted_address || "";
			console.log("[LocationField] 解析得到位置名称:", locationName);

			// 获取国家和城市信息
			let country: string | undefined;
			let city: string | undefined;

			// 处理Mapbox特有的数据结构 (如果存在)
			if (place.mapbox_data) {
				console.log(
					"[LocationField] 使用 Mapbox 数据结构解析",
					place.mapbox_data,
				);
				// 从转换后的Mapbox数据中提取国家和城市
				if (place.mapbox_data.country) {
					country = place.mapbox_data.country.name;
				}

				if (place.mapbox_data.place) {
					city = place.mapbox_data.place.name;
				} else if (place.mapbox_data.region && !city) {
					// 如果没有城市但有地区，使用地区作为备选
					city = place.mapbox_data.region.name;
				}
			}
			// 兼容传统的address_components解析方式
			else if (
				place.address_components &&
				Array.isArray(place.address_components)
			) {
				console.log(
					"[LocationField] 使用 address_components 解析",
					place.address_components,
				);
				for (const component of place.address_components) {
					// 查找国家信息
					if (component.types.includes("country")) {
						country = component.long_name;
					}
					// 查找城市信息 - 首选 locality，这通常代表城市
					if (component.types.includes("locality")) {
						city = component.long_name;
					}
					// 对某些地点，可能需要使用 administrative_area_level_1 作为备选
					else if (
						!city &&
						component.types.includes("administrative_area_level_1")
					) {
						city = component.long_name;
					}
				}
			}

			console.log(
				`[LocationField] 解析地点结果: 国家=${country}, 城市=${city}`,
			);

			const coordinates = {
				lat: place.geometry!.location.lat(),
				lng: place.geometry!.location.lng(),
			};

			// 自动选择图标类型
			let autoSelectedIconType = "PIN"; // 默认图标
			if (place.mapbox_data) {
				autoSelectedIconType = getIconTypeFromMapboxData({
					poi_category: place.mapbox_data.poi_category,
					poi_category_ids: place.mapbox_data.poi_category_ids,
					feature_type: place.mapbox_data.feature_type,
					maki: place.mapbox_data.maki,
					brand: place.mapbox_data.brand,
				});

				// 将图标类型转换为应用中使用的格式
				const iconTypeMapping: Record<string, string> = {
					hotel: "HOTEL",
					food: "FOOD",
					landmark: "LANDMARK",
					beach: "LANDMARK", // 海滩归类为地标
					mountain: "LANDMARK", // 山地归类为地标
					museum: "LANDMARK", // 博物馆归类为地标
					park: "PARK",
					shopping: "SHOPPING",
					cafe: "FOOD", // 咖啡厅归类为餐饮
					activity: "OTHER", // 活动归类为其他
					transport: "TRANSPORT",
					airport: "TRANSPORT", // 机场归类为交通
					default: "PIN",
				};

				autoSelectedIconType =
					iconTypeMapping[autoSelectedIconType] || "PIN";
				console.log(
					`[LocationField] 自动选择图标类型: ${autoSelectedIconType}`,
				);
			}

			console.log("[LocationField] 准备更新状态", {
				locationName,
				coordinates,
				country,
				city,
				autoSelectedIconType,
			});

			// 更新本地状态
			setLocation(locationName);
			console.log(
				"[LocationField] 本地 location 状态已更新为:",
				locationName,
			);

			// 更新表单数据，包括位置名称、坐标、国家和城市，以及自动选择的图标类型
			setFormData((prevData) => {
				const newData = {
					...prevData,
					location: locationName,
					coordinates,
					country, // 添加国家信息
					city, // 添加城市信息
					iconType: autoSelectedIconType, // 自动选择的图标类型
				};
				console.log("[LocationField] formData 更新", {
					前: prevData,
					后: newData,
				});
				return newData;
			});

			setSearchBoxKey((prevKey) => {
				const newKey = prevKey + 1;
				console.log(
					`[LocationField] searchBoxKey 更新: ${prevKey} -> ${newKey}`,
				);
				return newKey;
			});
		},
		[
			location,
			formData,
			setFormData,
			toast,
			t,
			setSearchBoxKey,
			setLocation,
		],
	);

	// 当表单数据中的位置改变时，更新本地状态
	useEffect(() => {
		console.log("[LocationField] formData.location 变化检测", {
			formDataLocation: formData.location,
			currentLocation: location,
			shouldUpdate: formData.location !== location,
		});

		// 只有当外部传入的 formData.location 与当前 location 不同时才更新
		if (formData.location !== location) {
			console.log(
				`[LocationField] 同步外部 location: "${location}" -> "${formData.location}"`,
			);
			setLocation(formData.location || "");
		}
	}, [formData.location]); // 移除 location 依赖，避免循环更新

	// 监听输入值变化，同步更新formData
	const handleLocationInputChange = useCallback(
		(newValue: string) => {
			console.log(
				`[LocationField] 输入值变化: "${location}" -> "${newValue}"`,
			);
			setLocation(newValue);

			// 如果用户清空了输入，清除坐标信息
			setFormData((prev) => {
				const newData = {
					...prev,
					location: newValue,
					coordinates: undefined,
					country: undefined,
					city: undefined,
				};
				console.log("[LocationField] 输入变化导致 formData 更新", {
					前: prev,
					后: newData,
				});
				return newData;
			});
		},
		[setFormData, location],
	);

	return (
		<div>
			<label
				htmlFor={`location-input-${searchBoxKey}`}
				className="block text-sm font-medium mb-1"
			>
				{t("travelMemo.travelPointForm.labels.location")}
			</label>
			<div className="flex items-center">
				<MapPin className="w-5 h-5 mr-2 text-travel-dark/60 flex-shrink-0" />
				<div className="w-full">
					{!isReady ? (
						// 加载中的状态
						<div className="border border-input rounded-md h-10 px-3 py-2 text-sm relative">
							<div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground pl-6 pointer-events-none">
								{t(
									"travelMemo.travelPointForm.placeholders.loadingMapSearch",
								)}
							</div>
						</div>
					) : (
						// 使用Mapbox搜索框
						<StandaloneSearchBox
							key={searchBoxKey}
							onPlaceSelect={handlePlaceSelect}
							onInputChange={handleLocationInputChange}
							initialValue={location}
						/>
					)}
				</div>
			</div>
		</div>
	);
}
