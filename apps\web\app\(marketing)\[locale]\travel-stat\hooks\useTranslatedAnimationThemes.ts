import { useMemo } from "react";
import type { AnimationTheme } from "../types";
import { useTravelStatTranslations } from "./useTravelStatTranslations";

export interface TranslatedAnimationTheme {
	id: AnimationTheme;
	name: string;
	description: string;
}

export function useTranslatedAnimationThemes() {
	const translations = useTravelStatTranslations();

	const getAnimationTheme = useMemo(() => {
		return (themeId: AnimationTheme): TranslatedAnimationTheme => {
			return {
				id: themeId,
				name: translations.animation.names[themeId](),
				description: translations.animation.descriptions[themeId](),
			};
		};
	}, [translations.animation]);

	const getAllAnimationThemes = useMemo(() => {
		const themeIds: AnimationTheme[] = [
			"shooting-stars",
			"floating-particles",
			"aurora",
			"minimal",
			"galaxy",
			"none",
		];

		return themeIds.map((themeId) => getAnimationTheme(themeId));
	}, [getAnimationTheme]);

	return {
		themes: getAllAnimationThemes,
		getAnimationTheme,
		translations: translations.animation,
	};
}
