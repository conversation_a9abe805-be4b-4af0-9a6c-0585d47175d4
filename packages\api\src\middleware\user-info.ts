import { type Session, auth } from "@repo/auth";
import { createMiddleware } from "hono/factory";
import { EXPORT_AUTH_TOKEN } from "./auth";

export const userInfoMiddleware = createMiddleware<{
	Variables: {
		session: Session["session"];
		user: Session["user"];
	};
}>(async (c, next) => {
	// 检查是否是视频导出请求
	const isExportMode = c.req.query("export") === "video";
	const exportToken = c.req.query("exportToken");

	// 如果是导出模式且提供了正确的令牌，则设置系统用户
	if (isExportMode && exportToken === EXPORT_AUTH_TOKEN) {
		// 创建一个模拟的用户会话
		c.set("session", {
			id: "system-export-session",
			createdAt: new Date(),
			updatedAt: new Date(),
			userId: "system-video-export",
			expiresAt: new Date(Date.now() + 3600000),
			token: "system-export-token",
			ipAddress: null,
			userAgent: null,
		} as any);

		// 设置一个系统用户，与auth中间件保持一致
		c.set("user", {
			id: "system-video-export",
			email: "<EMAIL>",
			emailVerified: true,
			name: "System Video Export",
			createdAt: new Date(),
			updatedAt: new Date(),
			banned: false,
		} as any);

		await next();
		return;
	}

	// 常规用户信息获取
	const session = await auth.api.getSession({
		headers: c.req.raw.headers,
	});

	if (session) {
		c.set("session", {
			id: session.session.id,
			createdAt: session.session.createdAt,
			updatedAt: session.session.updatedAt,
			userId: session.session.userId,
			expiresAt: session.session.expiresAt,
			token: session.session.token,
			ipAddress: session.session.ipAddress,
			userAgent: session.session.userAgent,
		});
		c.set("user", session.user);
	}

	await next();
});
