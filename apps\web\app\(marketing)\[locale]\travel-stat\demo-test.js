// 🌍 旅行足迹国家名称映射测试脚本 (Node.js版本)
// 快速测试常见国家的映射功能，无需TypeScript或外部依赖

// ===== 内联的映射数据 =====

// 国家名称映射表 (简化版本，包含主要国家)
const COUNTRY_NAME_MAPPING = {
	// 美国变体
	"United States": "United States of America",
	USA: "United States of America",
	US: "United States of America",
	"United States of America": "United States of America",

	// 中国变体
	China: "China",
	中国: "China",
	"People's Republic of China": "China",
	PRC: "China",

	// 英国变体
	"United Kingdom": "United Kingdom",
	UK: "United Kingdom",
	"Great Britain": "United Kingdom",
	Britain: "United Kingdom",
	England: "United Kingdom",

	// 日本变体
	Japan: "Japan",
	日本: "Japan",

	// 德国变体
	Germany: "Germany",
	Deutschland: "Germany",
	德国: "Germany",

	// 法国变体
	France: "France",
	法国: "France",

	// 俄罗斯变体
	Russia: "Russia",
	"Russian Federation": "Russia",
	俄罗斯: "Russia",

	// 加拿大变体
	Canada: "Canada",
	加拿大: "Canada",

	// 澳大利亚变体
	Australia: "Australia",
	澳大利亚: "Australia",

	// 印度变体
	India: "India",
	印度: "India",

	// 巴西变体
	Brazil: "Brazil",
	Brasil: "Brazil",
	巴西: "Brazil",
};

// 标准化国家名称函数
function normalizeCountryName(countryName) {
	if (!countryName || typeof countryName !== "string") {
		return null;
	}

	const trimmed = countryName.trim();

	// 直接映射
	if (COUNTRY_NAME_MAPPING[trimmed]) {
		return COUNTRY_NAME_MAPPING[trimmed];
	}

	// 不区分大小写映射
	const lowerCaseMapping = Object.keys(COUNTRY_NAME_MAPPING).find(
		(key) => key.toLowerCase() === trimmed.toLowerCase(),
	);

	if (lowerCaseMapping) {
		return COUNTRY_NAME_MAPPING[lowerCaseMapping];
	}

	// 如果没有找到映射，返回原始名称
	return trimmed;
}

// 生成国家名称变体函数
function getCountryNameVariants(standardName) {
	const variants = new Set([standardName]);

	// 添加映射表中所有指向该标准名称的变体
	for (const [variant, mapped] of Object.entries(COUNTRY_NAME_MAPPING)) {
		if (mapped === standardName) {
			variants.add(variant);
		}
	}

	// 添加大小写变体
	const originalVariants = Array.from(variants);
	originalVariants.forEach((variant) => {
		variants.add(variant.toUpperCase());
		variants.add(variant.toLowerCase());

		// 添加首字母大写变体
		variants.add(
			variant.charAt(0).toUpperCase() + variant.slice(1).toLowerCase(),
		);
	});

	return Array.from(variants);
}

// ===== 测试函数 =====

// 测试映射功能
function testMapping() {
	console.log("🧪 开始测试国家名称映射功能...\n");
	console.log("=".repeat(50));

	// 测试用例
	const testCases = [
		// 美国测试
		{ input: "United States", expected: "United States of America" },
		{ input: "USA", expected: "United States of America" },
		{ input: "US", expected: "United States of America" },

		// 中国测试
		{ input: "China", expected: "China" },
		{ input: "中国", expected: "China" },
		{ input: "PRC", expected: "China" },

		// 英国测试
		{ input: "United Kingdom", expected: "United Kingdom" },
		{ input: "UK", expected: "United Kingdom" },
		{ input: "England", expected: "United Kingdom" },

		// 其他国家
		{ input: "Japan", expected: "Japan" },
		{ input: "Germany", expected: "Germany" },
		{ input: "France", expected: "France" },

		// 大小写测试
		{ input: "united states", expected: "United States of America" },
		{ input: "CHINA", expected: "China" },

		// 未映射的国家
		{ input: "Unknown Country", expected: "Unknown Country" },
	];

	let passed = 0;
	let failed = 0;

	testCases.forEach((testCase, index) => {
		const result = normalizeCountryName(testCase.input);
		const success = result === testCase.expected;

		if (success) {
			console.log(
				`✅ 测试 ${index + 1}: "${testCase.input}" -> "${result}"`,
			);
			passed++;
		} else {
			console.log(
				`❌ 测试 ${index + 1}: "${testCase.input}" -> "${result}" (期望: "${testCase.expected}")`,
			);
			failed++;
		}
	});

	console.log(`\n${"=".repeat(50)}`);
	console.log(`测试结果: ${passed} 通过, ${failed} 失败`);
	console.log(`成功率: ${((passed / testCases.length) * 100).toFixed(1)}%`);
}

// 测试变体生成功能
function testVariantGeneration() {
	console.log("\n\n🔄 测试国家名称变体生成功能...\n");
	console.log("=".repeat(50));

	const testCountries = [
		"United States of America",
		"China",
		"United Kingdom",
		"Japan",
	];

	testCountries.forEach((country) => {
		const variants = getCountryNameVariants(country);
		console.log(`\n🏷️  ${country} 的变体 (${variants.length}个):`);
		variants.slice(0, 8).forEach((variant, index) => {
			console.log(`   ${index + 1}. "${variant}"`);
		});
		if (variants.length > 8) {
			console.log(`   ... 还有 ${variants.length - 8} 个变体`);
		}
	});
}

// 演示完整流程
function demonstrateFullWorkflow() {
	console.log("\n\n🎯 演示完整的国家名称处理流程...\n");
	console.log("=".repeat(60));

	// 模拟从Mapbox API获取的国家名称
	const mapboxResponses = [
		"United States",
		"China",
		"United Kingdom",
		"unknown country",
	];

	mapboxResponses.forEach((apiResponse, index) => {
		console.log(`\n📍 处理第 ${index + 1} 个API响应: "${apiResponse}"`);

		// 1. 标准化名称
		const normalized = normalizeCountryName(apiResponse);
		console.log(`   📋 标准化结果: "${normalized}"`);

		// 2. 生成变体用于匹配GeoJSON
		const variants = getCountryNameVariants(normalized);
		console.log(`   🔄 生成 ${variants.length} 个变体用于GeoJSON匹配`);
		console.log(
			`   🎯 前5个变体: ${variants
				.slice(0, 5)
				.map((v) => `"${v}"`)
				.join(", ")}`,
		);

		// 3. 模拟在GeoJSON中查找
		console.log("   🗺️  在GeoJSON中搜索这些变体...");
		console.log("   ✅ 匹配成功 (模拟结果)");
	});
}

// 性能测试
function performanceTest() {
	console.log("\n\n⚡ 性能测试...\n");
	console.log("=".repeat(40));

	const testData = Array(1000)
		.fill(0)
		.map((_, i) => `Country ${i % 10}`);

	console.time("批量标准化1000个国家名称");
	testData.forEach((name) => normalizeCountryName(name));
	console.timeEnd("批量标准化1000个国家名称");

	console.time("批量生成1000个变体列表");
	testData.slice(0, 100).forEach((name) => getCountryNameVariants(name));
	console.timeEnd("批量生成1000个变体列表");
}

// ===== 主函数 =====

function main() {
	console.log("🌍 旅行足迹 - 国家名称映射系统测试\n");
	console.log("本脚本演示国家名称标准化和变体生成功能");
	console.log("用于验证Mapbox API结果与GeoJSON数据的匹配逻辑\n");

	try {
		// 运行所有测试
		testMapping();
		testVariantGeneration();
		demonstrateFullWorkflow();
		performanceTest();

		console.log("\n\n🎉 所有测试完成！");
		console.log("💡 如需测试完整的Mapbox API集成，请运行 test-mapping.js");
	} catch (error) {
		console.error("❌ 测试过程中发生错误:", error.message);
		process.exit(1);
	}
}

// 运行测试
if (require.main === module) {
	main();
}

module.exports = {
	normalizeCountryName,
	getCountryNameVariants,
	COUNTRY_NAME_MAPPING,
};
