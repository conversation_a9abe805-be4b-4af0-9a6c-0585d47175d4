import { type StorageProviderType, createStorageProvider } from "@repo/storage";
import { type NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
	try {
		// 检查请求是否包含multipart/form-data
		if (
			!request.headers
				.get("content-type")
				?.includes("multipart/form-data")
		) {
			return NextResponse.json(
				{ error: "请求必须是 multipart/form-data 格式" },
				{ status: 400 },
			);
		}

		// 解析表单数据
		const formData = await request.formData();
		const provider = formData.get("provider") as StorageProviderType;
		const bucket = formData.get("bucket") as string;
		const fileCountStr = formData.get("fileCount") as string;
		const fileCount = Number.parseInt(fileCountStr, 10);

		// 验证必要参数
		if (!provider || !bucket || isNaN(fileCount)) {
			return NextResponse.json(
				{ error: "缺少必要参数: provider, bucket, fileCount" },
				{ status: 400 },
			);
		}

		console.log(
			`准备批量上传 ${fileCount} 个文件到 ${provider} 的 ${bucket} 存储桶`,
		);

		// 收集所有文件
		const files: File[] = [];
		for (let i = 0; i < fileCount; i++) {
			const file = formData.get(`file_${i}`) as File;
			if (file && file instanceof File) {
				files.push(file);
			}
		}

		if (files.length === 0) {
			return NextResponse.json(
				{ error: "未提供有效文件" },
				{ status: 400 },
			);
		}

		console.log(`成功收集到 ${files.length} 个文件`);

		// 获取区域配置（针对腾讯云 COS）
		let region: string | undefined;
		if (provider === "tencent-cos") {
			region = process.env.TENCENT_COS_REGION || "ap-shanghai";
			console.log(`使用腾讯云 COS 区域: ${region}`);
		}

		// 创建存储提供商
		const storageProvider = createStorageProvider(provider, {
			accessKeyId: process.env.TENCENT_COS_SECRET_ID || "placeholder",
			secretAccessKey:
				process.env.TENCENT_COS_SECRET_KEY || "placeholder",
			region,
		});

		// 生成文件路径
		const filePaths = files.map((file) => {
			const timestamp = new Date().getTime();
			const fileName = file.name.replace(/[^a-zA-Z0-9.-]/g, "_");
			return `uploads/${timestamp}-${fileName}`;
		});

		// 获取批量上传URL
		const uploadUrlsResult = await storageProvider.getBatchSignedUploadUrls(
			{
				bucket,
				paths: filePaths,
				contentType: "auto",
			},
		);

		if (
			!uploadUrlsResult.urls ||
			Object.keys(uploadUrlsResult.urls).length === 0
		) {
			return NextResponse.json(
				{ error: "获取上传URL失败" },
				{ status: 500 },
			);
		}

		console.log(
			`成功获取 ${Object.keys(uploadUrlsResult.urls).length} 个上传URL`,
		);

		// 上传结果集合
		const results = [];
		let successCount = 0;
		let failCount = 0;

		// 开始批量上传
		for (let i = 0; i < files.length; i++) {
			const file = files[i];
			const path = filePaths[i];
			const uploadUrl = uploadUrlsResult.urls[path];

			if (!uploadUrl) {
				console.error(`未能获取 ${path} 的上传URL`);
				results.push({
					path,
					fileName: file.name,
					fileSize: file.size,
					fileType: file.type,
					success: false,
					error: "获取上传URL失败",
				});
				failCount++;
				continue;
			}

			try {
				console.log(`开始上传文件: ${file.name} 到路径: ${path}`);

				// 读取文件内容为 ArrayBuffer
				const fileBuffer = await file.arrayBuffer();
				const fileContent = Buffer.from(fileBuffer);

				// 使用 fetch 执行上传（在服务器端，没有CORS限制）
				const uploadResponse = await fetch(uploadUrl, {
					method: "PUT",
					body: fileContent,
					headers: {
						"Content-Type": file.type || "application/octet-stream",
					},
				});

				if (!uploadResponse.ok) {
					const errorText = await uploadResponse.text();
					console.error(
						`上传文件 ${file.name} 失败: ${uploadResponse.status} ${uploadResponse.statusText}`,
					);
					console.error(`错误详情: ${errorText}`);

					results.push({
						path,
						fileName: file.name,
						fileSize: file.size,
						fileType: file.type,
						success: false,
						error: `上传失败: ${uploadResponse.status} ${uploadResponse.statusText}`,
					});
					failCount++;
					continue;
				}

				// 获取文件访问URL
				const fileUrl = await storageProvider.getSignedUrl(path, {
					bucket,
					expiresIn: 3600, // 1小时有效期
				});

				console.log(`文件 ${file.name} 上传成功，访问URL: ${fileUrl}`);

				results.push({
					path,
					fileName: file.name,
					fileSize: file.size,
					fileType: file.type,
					success: true,
					url: fileUrl,
				});
				successCount++;
			} catch (error) {
				console.error(`处理文件 ${file.name} 时出错:`, error);

				results.push({
					path,
					fileName: file.name,
					fileSize: file.size,
					fileType: file.type,
					success: false,
					error:
						error instanceof Error
							? error.message
							: "上传过程中发生未知错误",
				});
				failCount++;
			}
		}

		return NextResponse.json({
			success: true,
			totalCount: files.length,
			successCount,
			failCount,
			files: results,
		});
	} catch (error) {
		console.error("批量文件上传处理错误:", error);
		return NextResponse.json(
			{
				error:
					error instanceof Error
						? error.message
						: "上传处理过程中发生未知错误",
			},
			{ status: 500 },
		);
	}
}
