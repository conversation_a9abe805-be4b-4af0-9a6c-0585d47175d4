import { LoginFormAdapter } from "../adapters/LoginFormAdapter";

interface LoginPageProps {
	t: (key: string) => string;
	getAuthErrorMessage?: (code?: string) => string;
	redirectAfterSignIn?: string;
	enablePasswordLogin?: boolean;
	enableMagicLink?: boolean;
}

export async function generateLoginMetadata(t: (key: string) => string) {
	return {
		title: t("auth.login.title"),
	};
}

export function LoginPage(props: LoginPageProps) {
	return <LoginFormAdapter {...props} />;
}
