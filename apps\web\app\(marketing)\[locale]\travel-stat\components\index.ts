// =============================================================================
// 统一组件导出 - Travel Stat 模块
// 提供所有组件的统一导入入口，简化组件使用
// =============================================================================

// ===== 地图相关组件 =====
export { MapContainer } from "./map/MapContainer";
export { MapLegend } from "./map/MapLegend";
export { SearchBox } from "./map/SearchBox";

// ===== 地图控制组件 =====
export { MapControls } from "./map-controls/MapControls";
export type { MapControlsProps } from "./map-controls/types";

// ===== 地图样式组件 =====
export { MapStyleSwitcher } from "./map-style/MapStyleSwitcher";
export { ProjectionSwitcher } from "./map-style/ProjectionSwitcher";
export { MapStyleDemo } from "./map-style/MapStyleDemo";

// ===== 大气层组件 =====
export { AtmosphereSwitcher } from "./atmosphere/AtmosphereSwitcher";
export { AtmosphereDemo } from "./atmosphere/AtmosphereDemo";

// ===== 动画相关组件 =====
export { BackgroundAnimations } from "./animation/BackgroundAnimations";
export { AnimationController } from "./animation/AnimationController";
export { AnimationThemeSwitcher } from "./animation/AnimationThemeSwitcher";
export { ThreeJsBackground } from "./animation/ThreeJsBackground";
export { TSParticlesBackground } from "./animation/TSParticlesBackgroundSimple";

// ===== 标记组件 =====
export { MarkerFactory } from "./markers/MarkerFactory";
export { ClassicMarker } from "./markers/ClassicMarker";
export { GradientPulseMarker } from "./markers/GradientPulseMarker";
export { ParticleEffectMarker } from "./markers/ParticleEffectMarker";

// ===== 统计组件 =====
export { TravelStats } from "./stats/TravelStats";
export { CountryList } from "./stats/CountryList";

// ===== 导出相关组件 =====
export { useMapExporter } from "./export/MapExporter";
export { ExportOptionsPopover } from "./export/ExportOptionsPopover";
export { exportPresets } from "./export/presets";

// ===== 卡片生成器组件 =====
export { CardGenerator } from "./card-generator/CardGenerator";
export { TemplateSelector } from "./card-generator/components/TemplateSelector";

// ===== UI 组件 =====
export { MarkerStyleSelector } from "./ui/MarkerStyleSelector";
export { OperationPanel } from "./ui/OperationPanel";

// ===== 组件类型导出 =====
export type {
	TravelPoint,
	CountryData,
	GeocodeFeature,
	MapStyleType,
	MapProjectionType,
	AtmosphereTheme,
	AnimationTheme,
	ColorThemeType,
	MarkerStyleType,
	BaseMarkerProps,
	GradientPulseTheme,
	SocialPlatform,
	ExportQuality,
} from "../types";

// ===== 常量配置导出 =====
export {
	MAP_CONFIG,
	ATMOSPHERE_CONFIGS,
	ANIMATION_THEMES,
	COLOR_THEMES,
	MARKER_STYLES,
	SOCIAL_PLATFORM_CONFIG,
	EXPORT_QUALITY_CONFIG,
	ANIMATION_CONFIG,
	SIZE_CONFIG,
	APP_META,
} from "../constants";

// ===== 额外组件导出 =====
// Export types
export type { ExportOptions } from "./export/types";
