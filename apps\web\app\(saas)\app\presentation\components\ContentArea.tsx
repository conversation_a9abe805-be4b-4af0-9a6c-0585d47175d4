"use client";

import {} from "react";
import { useCallback, useRef } from "react";
import { ANIMATION_DURATION } from "../constants";
import { createLogger } from "../hooks/map-story/utils";
import type { FrontendTravelPoint } from "../types";
import { ImageGallery } from "./ImageGallery";
import { PointInfoPanel } from "./PointInfoPanel";

// 创建日志记录器
const logger = createLogger("ContentArea");

interface ContentAreaProps {
	currentPoint: FrontendTravelPoint | null;
	images: any[];
	showPointInfo: boolean;
	showImages: boolean;
	onImageClick: (imageSrc: string, index: number) => void;
	onTypingComplete?: () => void;
}

export function ContentArea({
	currentPoint,
	images,
	showPointInfo,
	showImages,
	onImageClick,
	onTypingComplete,
}: ContentAreaProps) {
	// 组件实例ID用于追踪
	const componentIdRef = useRef(
		`content_${Math.random().toString(36).substr(2, 9)}`,
	);

	// 记录点位变化
	const prevPointIdRef = useRef<string | null>(null);
	const currentPointId = currentPoint?.id || null;

	// 跟踪渲染次数
	const renderCountRef = useRef(0);

	// 确保两个组件使用相同的动画时间，增加一点动画时间使其更流畅
	const animationDuration = ANIMATION_DURATION.contentFade * 4; // 增加动画时间让效果更明显

	// 由于两个组件需要同步出现和消失，我们传递相同的可见性状态
	const contentVisible = showPointInfo && !!currentPoint; // 使用双感叹号确保返回布尔值

	// 修改图片显示逻辑，允许在点信息显示前就预加载图片
	// 即使图片内容不可见，也传递images以便预加载
	const hasImages = images && images.length > 0;
	const imagesVisible = showImages && hasImages && contentVisible;

	// 记录点位变化
	if (currentPointId !== prevPointIdRef.current) {
		logger.info(`[${componentIdRef.current}] 点位变更`, {
			prevPointId: prevPointIdRef.current,
			currentPointId,
			showPointInfo,
			showImages,
		});
		prevPointIdRef.current = currentPointId;
	}

	// 包装打字完成回调
	const handleTypingComplete = useCallback(() => {
		logger.info(`[${componentIdRef.current}] 打字完成回调触发`, {
			pointId: currentPointId,
			showPointInfo,
			contentVisible,
		});

		if (onTypingComplete) {
			onTypingComplete();
		}
	}, [onTypingComplete, currentPointId, showPointInfo, contentVisible]);

	// 增加渲染计数
	renderCountRef.current += 1;

	logger.debug(
		`[${componentIdRef.current}] 渲染 ContentArea (第${renderCountRef.current}次)`,
		{
			currentPointId,
			showPointInfo,
			showImages,
			contentVisible,
			imagesVisible,
			imagesLength: images?.length || 0,
		},
	);

	return (
		<div
			className="absolute inset-x-0 mx-auto z-20 flex flex-col items-center justify-between px-4 pointer-events-none"
			style={{ top: "20px", bottom: "20px" }}
		>
			{/* 图片画廊 - 顶部 - 始终传递图片数据用于预加载 */}
			<div className="w-full pointer-events-auto">
				<ImageGallery
					images={images}
					isVisible={imagesVisible}
					animationDuration={animationDuration}
					onImageClick={onImageClick}
				/>
			</div>

			{/* 空白填充区域 */}
			<div className="flex-grow" />

			{/* 点位信息面板 - 底部 */}
			<div className="w-full pointer-events-auto">
				<PointInfoPanel
					point={currentPoint}
					isVisible={contentVisible}
					animationDuration={animationDuration}
					onTypingComplete={handleTypingComplete}
				/>
			</div>
		</div>
	);
}
