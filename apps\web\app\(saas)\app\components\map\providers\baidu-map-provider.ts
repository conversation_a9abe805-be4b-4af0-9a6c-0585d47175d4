"use client";

import type {
	LatLng,
	MapBounds,
	MapEventHandler,
	MapEventType,
	MapService,
	MapServiceProvider,
	MapViewOptions,
	MarkerOptions,
} from "../types";

/**
 * 百度地图服务实现类 (示例框架)
 * 注意：这是一个示例实现，实际使用需要加载百度地图SDK并使用实际API
 */
class BaiduMapService implements MapService {
	private map: any = null;
	private markers: Map<string, any> = new Map();
	private eventListeners: Map<
		string,
		{ event: MapEventType; handler: MapEventHandler; listener: any }
	> = new Map();
	private markerId = 0;

	async init(element: HTMLElement, options: MapViewOptions): Promise<void> {
		// 确保百度地图API已加载
		await BaiduMapProvider.getInstance().load();

		// 这里是百度地图API的初始化代码
		// 实际实现时需要替换为真实的百度地图API调用
		console.log("初始化百度地图", options);

		// 模拟百度地图对象
		this.map = {
			centerAndZoom: (center: any, zoom: number) => {
				console.log("设置中心点和缩放级别", center, zoom);
			},
			addOverlay: (overlay: any) => {
				console.log("添加覆盖物", overlay);
			},
			removeOverlay: (overlay: any) => {
				console.log("移除覆盖物", overlay);
			},
			addEventListener: (type: string, handler: Function) => {
				console.log("添加事件监听", type);
				return { remove: () => {} };
			},
			getCenter: () => ({ lat: () => 39.9042, lng: () => 116.4074 }),
			getZoom: () => options.zoom,
			getBounds: () => ({
				getNorthEast: () => ({ lat: () => 40.0, lng: () => 116.5 }),
				getSouthWest: () => ({ lat: () => 39.8, lng: () => 116.3 }),
			}),
		};

		// 初始化地图
		this.map.centerAndZoom(
			{ lat: options.center.lat, lng: options.center.lng },
			options.zoom,
		);
	}

	destroy(): void {
		// 清理所有标记
		this.markers.forEach((marker) => {
			if (this.map) {
				this.map.removeOverlay(marker);
			}
		});
		this.markers.clear();

		// 清理所有事件监听器
		this.eventListeners.forEach(({ listener }) => {
			if (listener && listener.remove) {
				listener.remove();
			}
		});
		this.eventListeners.clear();

		// 清理地图实例
		this.map = null;
	}

	addMarker(options: MarkerOptions): string {
		if (!this.map) throw new Error("地图未初始化");

		const id = `marker_${this.markerId++}`;

		// 模拟百度地图标记对象
		const marker = {
			position: options.position,
			title: options.title,
			setPosition: (pos: LatLng) => {
				console.log("设置标记位置", pos);
			},
		};

		// 将标记添加到地图
		this.map.addOverlay(marker);

		// 添加点击事件
		if (options.onClick) {
			const listener = this.map.addEventListener(
				"click",
				options.onClick,
			);
			this.eventListeners.set(`${id}_click`, {
				event: "click",
				handler: options.onClick,
				listener,
			});
		}

		// 保存标记引用
		this.markers.set(id, marker);

		return id;
	}

	removeMarker(markerId: string): void {
		const marker = this.markers.get(markerId);
		if (marker && this.map) {
			this.map.removeOverlay(marker);
			this.markers.delete(markerId);

			// 移除相关事件监听器
			const clickListenerId = `${markerId}_click`;
			if (this.eventListeners.has(clickListenerId)) {
				const { listener } = this.eventListeners.get(clickListenerId)!;
				if (listener && listener.remove) {
					listener.remove();
				}
				this.eventListeners.delete(clickListenerId);
			}
		}
	}

	updateMarkerPosition(markerId: string, position: LatLng): void {
		const marker = this.markers.get(markerId);
		if (marker) {
			marker.setPosition(position);
		}
	}

	setCenter(center: LatLng): void {
		if (!this.map) throw new Error("地图未初始化");
		this.map.centerAndZoom(center, this.map.getZoom());
	}

	setZoom(zoom: number): void {
		if (!this.map) throw new Error("地图未初始化");
		this.map.centerAndZoom(this.map.getCenter(), zoom);
	}

	getBounds(): MapBounds {
		if (!this.map) throw new Error("地图未初始化");

		const bounds = this.map.getBounds();
		return {
			north: bounds.getNorthEast().lat(),
			south: bounds.getSouthWest().lat(),
			east: bounds.getNorthEast().lng(),
			west: bounds.getSouthWest().lng(),
		};
	}

	getCenter(): LatLng {
		if (!this.map) throw new Error("地图未初始化");
		const center = this.map.getCenter();
		return { lat: center.lat(), lng: center.lng() };
	}

	getZoom(): number {
		if (!this.map) throw new Error("地图未初始化");
		return this.map.getZoom();
	}

	fitBounds(points: LatLng[]): void {
		if (!this.map) throw new Error("地图未初始化");
		if (points.length === 0) return;

		// 百度地图适配多个点的边界实现
		console.log("适配显示多个点", points);
	}

	addEventListener(type: MapEventType, handler: MapEventHandler): void {
		if (!this.map) throw new Error("地图未初始化");

		// 创建唯一的监听器ID
		const listenerId = `${type}_${Date.now()}`;

		// 添加事件监听器
		const listener = this.map.addEventListener(type, handler);

		// 保存监听器引用
		this.eventListeners.set(listenerId, { event: type, handler, listener });
	}

	removeEventListener(type: MapEventType, handler: MapEventHandler): void {
		// 查找并移除匹配的事件监听器
		this.eventListeners.forEach((value, key) => {
			if (value.event === type && value.handler === handler) {
				if (value.listener && value.listener.remove) {
					value.listener.remove();
				}
				this.eventListeners.delete(key);
			}
		});
	}

	getNativeMapInstance(): any {
		return this.map;
	}
}

/**
 * 百度地图服务提供商 (单例模式)
 * 注意：这是一个示例实现，实际使用需要配置百度地图API密钥
 */
export class BaiduMapProvider implements MapServiceProvider {
	private static instance: BaiduMapProvider;
	private isApiLoaded = false;

	private constructor() {}

	// 获取单例实例
	static getInstance(): BaiduMapProvider {
		if (!BaiduMapProvider.instance) {
			BaiduMapProvider.instance = new BaiduMapProvider();
		}
		return BaiduMapProvider.instance;
	}

	// 模拟加载百度地图API
	async load(): Promise<void> {
		if (this.isApiLoaded) {
			return;
		}

		console.log("加载百度地图API...");

		// 模拟API加载过程
		return new Promise<void>((resolve) => {
			setTimeout(() => {
				console.log("百度地图API加载完成");
				this.isApiLoaded = true;
				resolve();
			}, 500);
		});
	}

	// 创建地图服务实例
	createMapService(): MapService {
		return new BaiduMapService();
	}

	// 检查地图API是否已加载
	isLoaded(): boolean {
		return this.isApiLoaded;
	}

	// 获取提供商名称
	getProviderName(): string {
		return "百度地图";
	}
}

// 默认导出百度地图提供商实例
export default BaiduMapProvider.getInstance();
