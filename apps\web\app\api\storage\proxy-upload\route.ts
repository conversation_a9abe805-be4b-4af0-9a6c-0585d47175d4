import { Readable } from "node:stream";
import { logger } from "@repo/logs";
import { createStorageProvider } from "@repo/storage";
import type { StorageProviderType } from "@repo/storage";
import { type NextRequest, NextResponse } from "next/server";

// 禁用 Next.js 的默认 bodyParser，以便我们可以处理表单数据
export const config = {
	api: {
		bodyParser: false,
	},
};

// 将 IncomingMessage 转换为可读流
function bufferToStream(buffer: Buffer) {
	const stream = new Readable();
	stream.push(buffer);
	stream.push(null);
	return stream;
}

// 解析多部分表单数据
async function parseFormData(req: Request): Promise<{
	fields: Record<string, string>;
	file?: { name: string; type: string; buffer: Buffer };
}> {
	const contentType = req.headers.get("content-type") || "";

	if (!contentType.includes("multipart/form-data")) {
		throw new Error("请求必须是 multipart/form-data 类型");
	}

	const formData = await req.formData();
	const fields: Record<string, string> = {};
	let file: { name: string; type: string; buffer: Buffer } | undefined;

	// 处理表单数据
	for (const [key, value] of Array.from(formData.entries())) {
		if (key === "file" && value instanceof File) {
			const arrayBuffer = await value.arrayBuffer();
			file = {
				name: value.name,
				type: value.type,
				buffer: Buffer.from(arrayBuffer),
			};
		} else if (typeof value === "string") {
			fields[key] = value;
		}
	}

	return { fields, file };
}

// 生成文件路径
function generateFilePath(fileName: string): string {
	const timestamp = new Date().getTime();
	const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, "_");
	return `uploads/${timestamp}-${sanitizedFileName}`;
}

export async function POST(request: NextRequest) {
	try {
		// 解析表单数据
		const { fields, file } = await parseFormData(request);

		// 验证必要字段
		if (!file) {
			return NextResponse.json(
				{ error: "缺少必要参数: file" },
				{ status: 400 },
			);
		}

		const { provider, bucket } = fields;

		if (!provider || !bucket) {
			return NextResponse.json(
				{
					error: `缺少必要参数: ${!provider ? "provider" : ""} ${!bucket ? "bucket" : ""}`.trim(),
				},
				{ status: 400 },
			);
		}

		// 生成文件路径
		const filePath = fields.path || generateFilePath(file.name);

		// 获取区域配置（针对腾讯云 COS）
		let region: string | undefined;
		if (provider === "tencent-cos") {
			region = process.env.TENCENT_COS_REGION || "ap-shanghai";
			logger.info(`使用腾讯云 COS 区域: ${region}`);
		} else {
			// 如果前端传入了 region，保留这个值
			region = fields.region;
		}

		// 配置对象
		const config: Record<string, any> = {
			// 基本配置
			accessKeyId:
				provider === "tencent-cos"
					? process.env.TENCENT_COS_SECRET_ID || "placeholder"
					: process.env[
							`${provider.toUpperCase().replace("-", "_")}_ACCESS_KEY_ID`
						] || "placeholder",
			secretAccessKey:
				provider === "tencent-cos"
					? process.env.TENCENT_COS_SECRET_KEY || "placeholder"
					: process.env[
							`${provider.toUpperCase().replace("-", "_")}_SECRET_ACCESS_KEY`
						] || "placeholder",
			region: region || "auto",
		};

		// 特殊处理 Cloudflare R2 的 endpoint
		if (provider === "cloudflare-r2") {
			const r2AccountId = process.env.CLOUDFLARE_R2_ACCOUNT_ID;
			config.endpoint =
				process.env.CLOUDFLARE_R2_ENDPOINT ||
				(r2AccountId
					? `https://${r2AccountId}.r2.cloudflarestorage.com`
					: undefined);

			if (!config.endpoint) {
				return NextResponse.json(
					{
						error: "Cloudflare R2 需要设置 CLOUDFLARE_R2_ENDPOINT 或 CLOUDFLARE_R2_ACCOUNT_ID 环境变量",
					},
					{ status: 500 },
				);
			}
		}

		// 创建存储提供商实例
		const storageProvider = createStorageProvider(
			provider as StorageProviderType,
			config,
		);

		// 直接上传文件
		const result = await storageProvider.uploadFile({
			bucket,
			path: filePath,
			file: bufferToStream(file.buffer),
			contentType: file.type || "application/octet-stream",
			region,
		});

		if (!result.success) {
			return NextResponse.json(
				{
					error: result.error || "上传失败",
					success: false,
				},
				{ status: 500 },
			);
		}

		// 获取访问URL
		const url = await storageProvider.getSignedUrl(filePath, {
			bucket,
			expiresIn: 24 * 60 * 60, // 默认24小时过期
		});

		return NextResponse.json({
			success: true,
			filePath,
			url,
		});
	} catch (error) {
		logger.error("通过服务端代理上传错误:", error);
		return NextResponse.json(
			{
				success: false,
				error: error instanceof Error ? error.message : "上传失败",
			},
			{ status: 500 },
		);
	}
}
