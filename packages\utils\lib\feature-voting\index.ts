// Types
export type * from "./types";

// Utils
export * from "./api-client";

// Components
export { FeatureVotingBoard } from "./components/FeatureVotingBoard";

// Anonymous user utils (只导出非冲突的类型和函数)
export {
	generateAnonymousId,
	getBrowserFingerprint,
	getOrCreateAnonymousId,
	getAnonymousUser,
	updateAnonymousUser,
	clearAnonymousUser,
	isValidAnonymousId,
	getUserLocation,
	getUserIP,
	checkRateLimit,
	clearRateLimit,
	createAnonymousUser,
	getAnonymousUserData,
	type BrowserFingerprint,
	type GeoLocation,
} from "./anonymous-user";

// Hooks
export * from "./hooks/use-feature-voting";
export * from "./hooks/use-feature-request-form";
