"use client";
import { cn } from "@ui/lib";
import {
	CameraIcon,
	ClockIcon,
	GlobeIcon,
	HeartIcon,
	MapPinIcon,
	RouteIcon,
	ShareIcon,
	StarIcon,
	UsersIcon,
} from "lucide-react";
import Image, { type StaticImageData } from "next/image";
import { type JSXElementConstructor, type ReactNode, useState } from "react";
import heroImage from "../../../../public/images/hero.svg";

export const featureTabs: Array<{
	id: string;
	title: string;
	icon: JSXElementConstructor<any>;
	subtitle?: string;
	description?: ReactNode;
	image?: StaticImageData;
	imageBorder?: boolean;
	stack?: {
		title: string;
		href: string;
		icon: JSXElementConstructor<any>;
	}[];
	highlights?: {
		title: string;
		description: string;
		icon: JSXElementConstructor<any>;
		demoLink?: string;
		docsLink?: string;
	}[];
}> = [
	{
		id: "map-footprints",
		title: "地图足迹",
		icon: MapPinIcon,
		subtitle: "在地图上记录你的每一步旅程",
		description:
			"通过智能地图定位，自动记录你的旅行轨迹。每一个足迹点都承载着独特的故事和回忆，让你的旅行路线变成一张生动的回忆地图。",
		stack: [],
		image: heroImage,
		imageBorder: false,
		highlights: [
			{
				title: "智能定位记录",
				description:
					"自动捕捉GPS位置信息，精准记录每个旅行节点，无需手动输入地址。",
				icon: MapPinIcon,
			},
			{
				title: "轨迹可视化",
				description:
					"将你的旅行路线以美观的线条呈现在地图上，形成独特的足迹图案。",
				icon: RouteIcon,
			},
			{
				title: "离线地图支持",
				description:
					"即使在没有网络的偏远地区，也能准确记录位置信息，回到网络后自动同步。",
				icon: GlobeIcon,
			},
		],
	},
	{
		id: "rich-diary",
		title: "丰富日记",
		icon: CameraIcon,
		subtitle: "用多媒体记录旅行的精彩瞬间",
		description:
			"不仅仅是文字记录，支持照片、视频、语音等多种媒体形式，让每一篇日记都生动鲜活，完整保存旅行的美好时光。",
		stack: [],
		image: heroImage,
		imageBorder: false,
		highlights: [
			{
				title: "多媒体编辑器",
				description:
					"强大的编辑器支持文字、图片、视频混排，轻松创作精美的旅行日记。",
				icon: CameraIcon,
			},
			{
				title: "智能时间轴",
				description:
					"根据拍摄时间自动整理照片和内容，构建完整的时间线记录。",
				icon: ClockIcon,
			},
			{
				title: "情感标记",
				description:
					"为每个回忆点添加心情标签和评分，记录旅行中的情感变化。",
				icon: HeartIcon,
			},
		],
	},
	{
		id: "social-sharing",
		title: "分享回忆",
		icon: ShareIcon,
		subtitle: "与朋友分享你的足迹故事",
		description:
			"精美的分享卡片设计，一键生成包含地图路线和精彩瞬间的分享内容。让你的旅行故事激发更多人的旅行热情。",
		stack: [],
		image: heroImage,
		imageBorder: false,
		highlights: [
			{
				title: "精美分享卡片",
				description:
					"自动生成包含足迹地图、精选照片和关键信息的美观分享卡片。",
				icon: StarIcon,
			},
			{
				title: "隐私控制",
				description:
					"灵活的隐私设置，你可以选择公开分享或仅与特定好友分享旅行记录。",
				icon: UsersIcon,
			},
			{
				title: "旅行社区",
				description:
					"加入旅行者社区，发现热门目的地，获取旅行灵感和实用建议。",
				icon: GlobeIcon,
			},
		],
	},
];

export function Features() {
	const [selectedTab, setSelectedTab] = useState(featureTabs[0].id);
	return (
		<section id="features" className="scroll-my-20 pt-12 lg:pt-16">
			<div className="container max-w-5xl">
				<div className="mx-auto mb-6 lg:mb-0 lg:max-w-5xl lg:text-center">
					<h2 className="font-bold text-4xl lg:text-5xl">
						用地图记录足迹，让每次旅行都值得回味
					</h2>
					<p className="mt-6 text-balance text-lg opacity-50">
						基于智能地图定位的旅行日记应用，将你的足迹转化为珍贵的回忆宝藏。
						无论是城市漫步还是远方探险，每一步都值得被记录和分享。
					</p>
				</div>

				<div className="mt-8 mb-4 hidden justify-center lg:flex">
					{featureTabs.map((tab) => {
						return (
							<button
								type="button"
								key={tab.id}
								onClick={() => setSelectedTab(tab.id)}
								className={cn(
									"flex w-24 flex-col items-center gap-2 rounded-lg px-4 py-2 md:w-32",
									selectedTab === tab.id
										? "bg-primary/5 font-bold text-primary dark:bg-primary/10"
										: "font-medium text-foreground/80",
								)}
							>
								<tab.icon
									className={cn(
										"size-6 md:size-8",
										selectedTab === tab.id
											? "text-primary"
											: "text-foreground opacity-30",
									)}
								/>
								<span className="text-xs md:text-sm">
									{tab.title}
								</span>
							</button>
						);
					})}
				</div>
			</div>

			<div className="bg-card dark:bg-card">
				<div className="container max-w-5xl">
					{featureTabs.map((tab) => {
						const filteredStack = tab.stack || [];
						const filteredHighlights = tab.highlights || [];
						return (
							<div
								key={tab.id}
								className={cn(
									"border-t py-8 first:border-t-0 md:py-12 lg:border-t-0 lg:py-16",
									selectedTab === tab.id
										? "block"
										: "block lg:hidden",
								)}
							>
								<div className="grid grid-cols-1 items-center gap-8 md:grid-cols-2 lg:gap-12">
									<div>
										<h3 className="font-normal text-2xl text-foreground/60 leading-normal md:text-3xl">
											<strong className="text-secondary">
												{tab.title}.{" "}
											</strong>
											{tab.subtitle}
										</h3>

										{tab.description && (
											<p className="mt-4 text-foreground/60">
												{tab.description}
											</p>
										)}

										{filteredStack?.length > 0 && (
											<div className="mt-4 flex flex-wrap gap-6">
												{filteredStack.map(
													(tool, k) => (
														<a
															href={tool.href}
															target="_blank"
															key={`stack-tool-${k}`}
															className="flex items-center gap-2"
															rel="noreferrer"
														>
															<tool.icon className="size-6" />
															<strong className="block text-sm">
																{tool.title}
															</strong>
														</a>
													),
												)}
											</div>
										)}
									</div>
									<div>
										{tab.image && (
											<Image
												src={tab.image}
												alt={tab.title}
												className={cn(
													" h-auto w-full max-w-xl",
													{
														"rounded-2xl border-4 border-secondary/10":
															tab.imageBorder,
													},
												)}
											/>
										)}
									</div>
								</div>

								{filteredHighlights.length > 0 && (
									<div className="mt-8 grid gap-3 sm:grid-cols-2 lg:grid-cols-3">
										{filteredHighlights.map(
											(highlight, k) => (
												<div
													key={`highlight-${k}`}
													className="flex flex-col items-stretch justify-between rounded-lg border p-4"
												>
													<div>
														<highlight.icon
															className="text-primary text-xl"
															width="1em"
															height="1em"
														/>
														<strong className="mt-2 block">
															{highlight.title}
														</strong>
														<p className="mt-1 text-sm opacity-50">
															{
																highlight.description
															}
														</p>
													</div>
												</div>
											),
										)}
									</div>
								)}
							</div>
						);
					})}
				</div>
			</div>
		</section>
	);
}
