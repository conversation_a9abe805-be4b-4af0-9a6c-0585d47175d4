import { openai } from "@ai-sdk/openai";
import {
	AIProviderType,
	createAIClient,
	createGeminiClient,
	createOpenAIClient,
	createVolcengineClient,
} from "./lib";

// 为了保持向后兼容性，保留原有的OpenAI模型导出
export const textModel = openai("gpt-4o-mini");
export const imageModel = openai("dall-e-3");
export const audioModel = openai("whisper-1");

// 导出统一的AI客户端和提供商相关内容
export {
	// 提供商类型
	AIProviderType,
	// 客户端创建函数
	createAIClient,
	createOpenAIClient,
	createGeminiClient,
	createVolcengineClient,
};

// 导出其他内容
export * from "ai";
export * from "./lib";
