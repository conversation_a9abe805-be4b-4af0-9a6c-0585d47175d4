import {
	DndContext,
	type Drag<PERSON>ndEvent,
	DragOverlay,
	type DragStartEvent,
	KeyboardSensor,
	PointerSensor,
	closestCenter,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import {
	SortableContext,
	arrayMove,
	horizontalListSortingStrategy,
	sortableKeyboardCoordinates,
	useSortable,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import {
	Carousel,
	CarouselContent,
	CarouselItem,
	CarouselNext,
	CarouselPrevious,
} from "@ui/components/carousel";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import { Check, Edit, GripVertical, Plus, Trash2, X } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";

import type { FrontendTravelTimeline } from "@packages/database/src/types";

interface DaySelectorProps {
	diaryDays: FrontendTravelTimeline[];
	activeDay: string;
	setActiveDay: (dayId: string) => void;
	setDiaryDays: (days: FrontendTravelTimeline[]) => void;
	onAddPointClick?: () => void;
	className?: string;
}

// 定义可排序的时间线项组件
interface SortableTimelineItemProps {
	timeline: FrontendTravelTimeline;
	activeDay: string;
	index: number;
	setActiveDay: (dayId: string) => void;
	setActiveIndex: (index: number) => void;
	openEditDayDialog: (timelineId: string) => void;
	handleDeleteDay: (timelineId: string) => void;
	isDragging?: boolean;
	formatTabDate: (date: Date) => string;
	isEditing?: boolean;
	onStartEdit?: () => void;
	onSaveEdit?: (title: string, date: Date) => void;
	onCancelEdit?: () => void;
}

const SortableTimelineItem = ({
	timeline,
	activeDay,
	index,
	setActiveDay,
	setActiveIndex,
	openEditDayDialog,
	handleDeleteDay,
	isDragging = false,
	formatTabDate,
	isEditing = false,
	onStartEdit,
	onSaveEdit,
	onCancelEdit,
}: SortableTimelineItemProps) => {
	const {
		attributes,
		listeners,
		setNodeRef,
		transform,
		transition,
		isDragging: isSortableDragging,
	} = useSortable({ id: timeline.id });

	const style = {
		transform: CSS.Transform.toString(transform),
		transition,
		opacity: isSortableDragging ? 0.4 : 1,
		zIndex: isSortableDragging ? 999 : "auto",
	};

	const currentlyDragging = isDragging || isSortableDragging;

	const handleDoubleClick = () => {
		if (!isEditing && onStartEdit) {
			onStartEdit();
		}
	};

	const handleSingleClick = () => {
		if (!isEditing) {
			setActiveDay(timeline.id);
			setActiveIndex(index);
		}
	};

	// 如果正在编辑，渲染内联编辑器
	if (isEditing) {
		return (
			<CarouselItem
				key={timeline.id}
				className="pl-1 pr-1 basis-auto"
				ref={setNodeRef}
				style={style}
			>
				<InlineTimelineEditor
					initialTitle={timeline.title}
					initialDate={timeline.date}
					onSave={(title, date) => onSaveEdit?.(title, date)}
					onCancel={() => onCancelEdit?.()}
					isCompact={true}
				/>
			</CarouselItem>
		);
	}

	return (
		<CarouselItem
			key={timeline.id}
			className={cn(
				"pl-1 pr-1 basis-auto transition-all duration-200",
				activeDay === timeline.id
					? "scale-100 opacity-100"
					: "scale-90 opacity-80",
			)}
			ref={setNodeRef}
			style={style}
		>
			<div
				className={cn(
					"relative group rounded-lg border-2 shadow-sm hover:shadow-md transition-all cursor-pointer",
					currentlyDragging
						? "bg-travel-primary/30 border-travel-primary/60 ring-2 ring-travel-primary/40"
						: "",
					activeDay === timeline.id
						? "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700"
						: "bg-white dark:bg-slate-800 border-travel-primary/20 hover:border-travel-primary/60",
				)}
				onDoubleClick={handleDoubleClick}
				title="双击编辑时间线"
			>
				<div className="flex items-center">
					<div
						{...attributes}
						{...listeners}
						className={cn(
							"flex items-center justify-center px-0.5 cursor-grab active:cursor-grabbing h-10 transition-colors duration-200",
							activeDay === timeline.id
								? "text-blue-600 dark:text-blue-400"
								: "text-gray-500/80 hover:text-gray-700 dark:text-gray-400/80 dark:hover:text-gray-300",
						)}
						title="拖动排序"
					>
						<GripVertical className="h-4 w-4" />
					</div>
					<button
						type="button"
						className={cn(
							"h-10 px-2 flex items-center gap-1 rounded-md cursor-pointer flex-1 transition-all duration-200",
							activeDay === timeline.id
								? "text-blue-700 dark:text-blue-300 font-medium"
								: "hover:bg-gray-50 dark:hover:bg-slate-700",
						)}
						onClick={handleSingleClick}
						onKeyDown={(e) => {
							if (e.key === "Enter" || e.key === " ") {
								handleSingleClick();
							}
						}}
					>
						{timeline.title || formatTabDate(timeline.date)}
						<span
							className={cn(
								"text-xs ml-1 transition-colors duration-200",
								activeDay === timeline.id
									? "text-blue-600 dark:text-blue-400"
									: "text-gray-500",
							)}
						>
							({timeline.points.length})
						</span>
					</button>
				</div>
				<div className="absolute -top-1 -right-1 opacity-0 group-hover:opacity-100 transition-opacity">
					<div className="flex gap-1">
						<button
							type="button"
							className="h-6 w-6 bg-background/80 rounded-md flex items-center justify-center cursor-pointer hover:bg-accent"
							onClick={(e) => {
								e.stopPropagation();
								onStartEdit?.();
							}}
							onKeyDown={(e) => {
								if (e.key === "Enter" || e.key === " ") {
									e.stopPropagation();
									onStartEdit?.();
								}
							}}
							title="编辑时间线"
						>
							<Edit className="h-3 w-3" />
						</button>
						<button
							type="button"
							className="h-6 w-6 bg-background/80 text-destructive rounded-md flex items-center justify-center cursor-pointer hover:bg-accent"
							onClick={(e) => {
								e.stopPropagation();
								handleDeleteDay(timeline.id);
							}}
							onKeyDown={(e) => {
								if (e.key === "Enter" || e.key === " ") {
									e.stopPropagation();
									handleDeleteDay(timeline.id);
								}
							}}
							title="删除时间线"
						>
							<Trash2 className="h-3 w-3" />
						</button>
					</div>
				</div>
			</div>
		</CarouselItem>
	);
};

// 内联编辑时间线组件
interface InlineTimelineEditorProps {
	initialTitle?: string;
	initialDate?: Date;
	onSave: (title: string, date: Date) => void;
	onCancel: () => void;
	placeholder?: string;
	isCompact?: boolean;
}

const InlineTimelineEditor = ({
	initialTitle = "",
	initialDate = new Date(),
	onSave,
	onCancel,
	placeholder = "时间线标题",
	isCompact = false,
}: InlineTimelineEditorProps) => {
	const [title, setTitle] = useState(initialTitle);
	const [date, setDate] = useState(initialDate);
	const titleInputRef = useRef<HTMLInputElement>(null);

	useEffect(() => {
		// 自动聚焦到标题输入框
		titleInputRef.current?.focus();
	}, []);

	const handleSave = () => {
		const finalTitle = title.trim() || format(date, "MM月dd日");
		onSave(finalTitle, date);
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		if (e.key === "Enter") {
			e.preventDefault();
			handleSave();
		} else if (e.key === "Escape") {
			e.preventDefault();
			onCancel();
		}
	};

	return (
		<div
			className={cn(
				"bg-white dark:bg-slate-800 border-2 border-blue-300 rounded-lg p-2 shadow-md",
				isCompact ? "min-w-[200px]" : "min-w-[250px]",
			)}
		>
			<div className="space-y-2">
				<input
					ref={titleInputRef}
					type="text"
					value={title}
					onChange={(e) => setTitle(e.target.value)}
					onKeyDown={handleKeyDown}
					placeholder={placeholder}
					className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
				/>
				<input
					type="date"
					value={format(date, "yyyy-MM-dd")}
					onChange={(e) => setDate(new Date(e.target.value))}
					onKeyDown={handleKeyDown}
					className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
				/>
				<div className="flex justify-end gap-1">
					<button
						type="button"
						onClick={handleSave}
						className="p-1 rounded hover:bg-green-100 text-green-600 transition-colors"
						title="保存 (Enter)"
					>
						<Check className="h-4 w-4" />
					</button>
					<button
						type="button"
						onClick={onCancel}
						className="p-1 rounded hover:bg-red-100 text-red-600 transition-colors"
						title="取消 (Esc)"
					>
						<X className="h-4 w-4" />
					</button>
				</div>
			</div>
		</div>
	);
};

const DaySelector = ({
	diaryDays,
	activeDay,
	setActiveDay,
	setDiaryDays,
	onAddPointClick,
	className,
}: DaySelectorProps) => {
	// 移除原有的对话框状态，改为内联编辑状态
	const [isEditingDay, setIsEditingDay] = useState(false);
	const [dayToEdit, setDayToEdit] = useState<string | null>(null);
	const [isAddingNewDay, setIsAddingNewDay] = useState(false);

	// 删除确认对话框状态
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [timelineToDelete, setTimelineToDelete] = useState<{
		id: string;
		title: string;
	} | null>(null);

	// 拖拽相关状态
	const [activeId, setActiveId] = useState<string | null>(null);
	const [initialDiaryDays, setInitialDiaryDays] = useState<
		FrontendTravelTimeline[]
	>([]);
	const draggedItemRef = useRef<FrontendTravelTimeline | null>(null);

	// 获取UTC日期以确保时区一致性
	const getUTCDate = (date?: Date) => {
		if (!date) {
			return new Date(
				Date.UTC(
					new Date().getUTCFullYear(),
					new Date().getUTCMonth(),
					new Date().getUTCDate(),
				),
			);
		}
		return new Date(
			Date.UTC(
				date.getUTCFullYear(),
				date.getUTCMonth(),
				date.getUTCDate(),
			),
		);
	};

	// 添加新的日期
	const handleAddDay = (title: string, date: Date) => {
		const newTimeline: FrontendTravelTimeline = {
			id: Date.now().toString(),
			date: date,
			title: title,
			points: [],
		};

		const updatedDays = [...diaryDays, newTimeline];
		setDiaryDays(updatedDays);
		setActiveDay(newTimeline.id);
		setIsAddingNewDay(false);

		// 滑动到新添加的时间线
		setTimeout(() => {
			if (carouselApiRef.current) {
				const newIndex = updatedDays.length - 1; // 新时间线的索引
				carouselApiRef.current.scrollTo(newIndex);
				setActiveIndex(newIndex);
			}
		}, 100); // 短暂延迟确保DOM更新完成

		toast.success("已添加新时间线");
	};

	// 编辑日期
	const handleEditDay = (timelineId: string, title: string, date: Date) => {
		setDiaryDays(
			diaryDays.map((timeline) =>
				timeline.id === timelineId
					? {
							...timeline,
							date: date,
							title: title,
						}
					: timeline,
			),
		);

		setIsEditingDay(false);
		setDayToEdit(null);
		toast.success("时间线已更新");
	};

	// 开始编辑时间线
	const startEditDay = (timelineId: string) => {
		setDayToEdit(timelineId);
		setIsEditingDay(true);
	};

	// 取消编辑时间线
	const cancelEditDay = () => {
		setIsEditingDay(false);
		setDayToEdit(null);
	};

	// 打开删除确认对话框
	const openDeleteConfirmDialog = (timelineId: string) => {
		const timeline = diaryDays.find((t) => t.id === timelineId);
		if (timeline) {
			setTimelineToDelete({
				id: timeline.id,
				title: timeline.title || formatTabDate(timeline.date),
			});
			setIsDeleteDialogOpen(true);
		}
	};

	// 确认删除时间线
	const confirmDeleteTimeline = () => {
		if (timelineToDelete) {
			handleDeleteDay(timelineToDelete.id);
			setTimelineToDelete(null);
		}
		setIsDeleteDialogOpen(false);
	};

	// 取消删除时间线
	const cancelDeleteTimeline = () => {
		setTimelineToDelete(null);
		setIsDeleteDialogOpen(false);
	};

	// 删除日期
	const handleDeleteDay = (timelineId: string) => {
		if (diaryDays.length <= 1) {
			toast.error("不能删除唯一的时间线");
			return;
		}

		const updatedTimelines = diaryDays.filter(
			(timeline) => timeline.id !== timelineId,
		);
		setDiaryDays(updatedTimelines);

		// 如果删除的是当前活动日期，则设置第一个日期为活动日期
		if (timelineId === activeDay) {
			setActiveDay(updatedTimelines[0].id);
		}

		toast.success("时间线已删除");
	};

	// 格式化日期显示，考虑时区
	const formatTabDate = (date: Date) => {
		const localDate = new Date(
			date.getTime() - date.getTimezoneOffset() * 60000,
		);
		return format(localDate, "MM-dd");
	};

	// 用于存储轮播组件当前激活的索引
	const [activeIndex, setActiveIndex] = useState(0);

	// 轮播API引用
	const carouselApiRef = useRef<any>(null);

	// 当 activeDay 变化时更新激活的索引
	useEffect(() => {
		const index = diaryDays.findIndex((day) => day.id === activeDay);
		if (index !== -1) {
			setActiveIndex(index);
		}
	}, [activeDay, diaryDays]);

	// 滑动到末尾的函数
	const scrollToEnd = () => {
		if (carouselApiRef.current) {
			// 滑动到最后一个位置（包括添加按钮的位置）
			const lastIndex = diaryDays.length; // 添加按钮在时间线数组后面
			carouselApiRef.current.scrollTo(lastIndex);
		}
	};

	// 处理添加新时间线
	const handleStartAddingNewDay = () => {
		setIsAddingNewDay(true);
		// 延迟一帧执行滑动，确保DOM已更新
		requestAnimationFrame(() => {
			scrollToEnd();
		});
	};

	// 取消添加新时间线
	const handleCancelAddingNewDay = () => {
		setIsAddingNewDay(false);
		// 滑动回到当前活动的时间线
		setTimeout(() => {
			if (carouselApiRef.current) {
				const currentIndex = diaryDays.findIndex(
					(day) => day.id === activeDay,
				);
				if (currentIndex !== -1) {
					carouselApiRef.current.scrollTo(currentIndex);
					setActiveIndex(currentIndex);
				}
			}
		}, 100);
	};

	// 配置拖拽传感器
	const sensors = useSensors(
		useSensor(PointerSensor, {
			activationConstraint: {
				distance: 8, // 需要拖动至少8像素才能开始拖拽
			},
		}),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		}),
	);

	// 处理拖拽开始事件
	const handleDragStart = (event: DragStartEvent) => {
		const { active } = event;
		setActiveId(active.id as string);

		// 保存拖拽开始时的排序，以便检测是否有变化
		setInitialDiaryDays([...diaryDays]);

		// 保存被拖拽的项目，用于拖拽叠加层
		const draggedItem = diaryDays.find((item) => item.id === active.id);
		if (draggedItem) {
			draggedItemRef.current = draggedItem;
		}
	};

	// 处理拖拽结束事件
	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;

		// 重置拖拽状态
		setActiveId(null);

		if (over && active.id !== over.id) {
			// 查找拖拽项和目标项的索引
			const oldIndex = diaryDays.findIndex((day) => day.id === active.id);
			const newIndex = diaryDays.findIndex((day) => day.id === over.id);

			if (oldIndex !== -1 && newIndex !== -1) {
				// 重新排序时间线数组
				const newDiaryDays = arrayMove(diaryDays, oldIndex, newIndex);
				setDiaryDays(newDiaryDays);

				// 如果当前活动日期是被拖动的日期，更新活动索引
				if (activeDay === active.id) {
					setActiveIndex(newIndex);
				}

				// 检查排序是否有变化，触发保存
				const hasChanged =
					JSON.stringify(initialDiaryDays) !==
					JSON.stringify(newDiaryDays);
				if (hasChanged) {
					toast.success("时间线顺序已更新");
				}
			}
		}

		// 清除引用
		draggedItemRef.current = null;
	};

	// 处理拖拽取消事件
	const handleDragCancel = () => {
		setActiveId(null);
		draggedItemRef.current = null;
	};

	// 渲染拖拽叠加层（可视化反馈）
	const renderDragOverlay = () => {
		if (!activeId || !draggedItemRef.current) {
			return null;
		}

		return (
			<SortableTimelineItem
				timeline={draggedItemRef.current}
				activeDay={activeDay}
				index={diaryDays.findIndex((d) => d.id === activeId)}
				setActiveDay={setActiveDay}
				setActiveIndex={setActiveIndex}
				openEditDayDialog={() => {}} // 拖拽时不需要
				handleDeleteDay={handleDeleteDay}
				isDragging={true}
				formatTabDate={formatTabDate}
			/>
		);
	};

	return (
		<div className={cn("flex items-center w-full px-4", className)}>
			<Carousel
				className="w-full"
				opts={{ startIndex: activeIndex }}
				setApi={(api) => {
					// 保存轮播API引用
					carouselApiRef.current = api;

					// 当组件初始化或 activeIndex 变化时滚动到对应位置
					if (api && activeIndex !== undefined) {
						api.scrollTo(activeIndex);
					}
				}}
			>
				<div className="relative">
					<DndContext
						sensors={sensors}
						collisionDetection={closestCenter}
						onDragStart={handleDragStart}
						onDragEnd={handleDragEnd}
						onDragCancel={handleDragCancel}
					>
						<SortableContext
							items={diaryDays.map((day) => day.id)}
							strategy={horizontalListSortingStrategy}
						>
							<CarouselContent className="ml-4 mr-4 px-8 pr-16">
								{diaryDays.map((timeline, index) => (
									<SortableTimelineItem
										key={timeline.id}
										timeline={timeline}
										activeDay={activeDay}
										index={index}
										setActiveDay={setActiveDay}
										setActiveIndex={setActiveIndex}
										openEditDayDialog={startEditDay}
										handleDeleteDay={
											openDeleteConfirmDialog
										}
										formatTabDate={formatTabDate}
										isEditing={
											isEditingDay &&
											dayToEdit === timeline.id
										}
										onStartEdit={() =>
											startEditDay(timeline.id)
										}
										onSaveEdit={(title, date) =>
											handleEditDay(
												timeline.id,
												title,
												date,
											)
										}
										onCancelEdit={cancelEditDay}
									/>
								))}

								{/* 内联添加新时间线 */}
								{isAddingNewDay ? (
									<CarouselItem className="pl-1 pr-1 basis-auto">
										<InlineTimelineEditor
											onSave={handleAddDay}
											onCancel={handleCancelAddingNewDay}
											placeholder="新时间线标题"
											isCompact={true}
										/>
									</CarouselItem>
								) : (
									/* 添加时间线图标按钮 */
									<CarouselItem className="pl-1 pr-1 basis-auto">
										<button
											type="button"
											className="relative group rounded-lg border-2 border-dashed border-travel-primary/40 shadow-sm hover:shadow-md transition-all cursor-pointer bg-white/50 hover:bg-travel-primary/10 h-10 min-w-[60px] flex items-center justify-center"
											onClick={handleStartAddingNewDay}
											onKeyDown={(e) => {
												if (
													e.key === "Enter" ||
													e.key === " "
												) {
													e.preventDefault();
													handleStartAddingNewDay();
												}
											}}
											title="添加新时间线"
											aria-label="添加新时间线"
										>
											<Plus className="h-5 w-5 text-travel-primary/70 group-hover:text-travel-primary transition-colors" />
										</button>
									</CarouselItem>
								)}
							</CarouselContent>
						</SortableContext>

						<DragOverlay
							adjustScale={true}
							dropAnimation={{
								duration: 200,
								easing: "cubic-bezier(0.18, 0.67, 0.6, 1.22)",
							}}
						>
							{activeId ? renderDragOverlay() : null}
						</DragOverlay>
					</DndContext>
					<CarouselPrevious className="left-0 bg-background/80 backdrop-blur-sm" />
					<CarouselNext className="right-0 bg-background/80 backdrop-blur-sm" />
				</div>
			</Carousel>

			{/* 删除确认对话框 */}
			<AlertDialog
				open={isDeleteDialogOpen}
				onOpenChange={setIsDeleteDialogOpen}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>确认删除时间线？</AlertDialogTitle>
						<AlertDialogDescription>
							您确定要删除时间线 "{timelineToDelete?.title}" 吗？
							此操作不可逆，该时间线下的所有旅行点位和相关数据将被永久删除。
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel onClick={cancelDeleteTimeline}>
							取消
						</AlertDialogCancel>
						<AlertDialogAction
							onClick={confirmDeleteTimeline}
							className="bg-destructive hover:bg-destructive/90 text-destructive-foreground"
						>
							删除时间线
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</div>
	);
};

export default DaySelector;
