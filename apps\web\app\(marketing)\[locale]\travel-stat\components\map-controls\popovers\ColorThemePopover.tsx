"use client";

import { Badge } from "@ui/components/badge";
import { Palette } from "lucide-react";
import { useTranslatedColorThemes } from "../../../hooks/useTranslatedColorThemes";
import { useTravelStatTranslations } from "../../../hooks/useTravelStatTranslations";
import { getRecommendedColorThemes } from "../../../utils/colorUtils";
import type { ColorThemeType, MapStyleType } from "../../types";

interface ColorThemePopoverProps {
	currentColorTheme: ColorThemeType;
	mapStyle: MapStyleType;
	onColorThemeChange: (theme: ColorThemeType) => void;
}

export function ColorThemePopover({
	currentColorTheme,
	mapStyle,
	onColorThemeChange,
}: ColorThemePopoverProps) {
	const { themes: COLOR_THEMES } = useTranslatedColorThemes();
	const translations = useTravelStatTranslations();

	return (
		<div className="w-full max-w-sm sm:max-w-md max-h-[70vh] flex flex-col">
			{/* 当前主题显示 - 固定区域 */}
			<div className="p-4 border-b border-sky-100 flex-shrink-0">
				<div className="p-3 bg-gradient-to-r from-sky-50 to-blue-50 rounded-lg border border-sky-200">
					<div className="flex items-center gap-2 mb-2">
						<Palette className="w-4 h-4 text-sky-500" />
						<span className="text-sm font-medium text-sky-800">
							{translations.colorThemeSelector.current()}:{" "}
							{COLOR_THEMES[currentColorTheme].name}
						</span>
					</div>
					<div className="flex gap-1 mb-2">
						{COLOR_THEMES[currentColorTheme].previewColors.map(
							(color: string, index: number) => (
								<div
									key={index}
									className="w-4 h-4 rounded-sm border border-white shadow-sm"
									style={{ backgroundColor: color }}
								/>
							),
						)}
					</div>
					<p className="text-xs text-sky-600">
						{COLOR_THEMES[currentColorTheme].description}
					</p>
				</div>
			</div>

			{/* 滚动内容区域 - 使用 PopoverContainer 的样式 */}
			<div
				className="flex-1 overflow-y-auto p-4 space-y-4 
							scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100
							hover:scrollbar-thumb-gray-400"
			>
				{/* 推荐主题 */}
				{(() => {
					const recommendedThemes = getRecommendedColorThemes(
						mapStyle,
						COLOR_THEMES,
					);
					if (recommendedThemes.length > 0) {
						return (
							<div>
								<h5 className="text-xs font-medium text-gray-600 mb-2">
									{translations.colorThemeSelector.recommendedThemes()}{" "}
									(
									{translations.colorThemeSelector.recommendedSubtitle()}
									)
								</h5>
								<div className="grid grid-cols-1 gap-2">
									{recommendedThemes
										.slice(0, 3)
										.map((theme: any) => (
											<button
												key={theme.id}
												type="button"
												onClick={(e) => {
													e.stopPropagation();
													onColorThemeChange(
														theme.id as ColorThemeType,
													);
												}}
												className={`p-2 text-xs rounded border transition-colors text-left ${
													currentColorTheme ===
													theme.id
														? "bg-sky-100 border-sky-300 text-sky-700"
														: "bg-gray-50 border-gray-200 hover:bg-gray-100"
												}`}
											>
												<div className="flex items-center justify-between mb-1">
													<span className="font-medium">
														{theme.name}
													</span>
													{currentColorTheme ===
														theme.id && (
														<div className="w-2 h-2 bg-sky-500 rounded-full" />
													)}
												</div>
												<div className="flex gap-1 mb-1">
													{theme.previewColors.map(
														(
															color: string,
															index: number,
														) => (
															<div
																key={index}
																className="w-3 h-3 rounded-sm border border-white shadow-sm"
																style={{
																	backgroundColor:
																		color,
																}}
															/>
														),
													)}
												</div>
												<p className="text-xs text-gray-500">
													{theme.description}
												</p>
											</button>
										))}
								</div>
							</div>
						);
					}
					return null;
				})()}

				{/* 所有主题 */}
				<div>
					<h5 className="text-xs font-medium text-gray-600 mb-2">
						{translations.colorThemeSelector.allThemes()}
					</h5>
					<div className="space-y-2">
						{Object.values(COLOR_THEMES).map((theme: any) => {
							const isActive = currentColorTheme === theme.id;
							const isRecommended = getRecommendedColorThemes(
								mapStyle,
								COLOR_THEMES,
							).some((t) => t.id === theme.id);

							return (
								<button
									key={theme.id}
									type="button"
									onClick={(e) => {
										e.stopPropagation();
										onColorThemeChange(
											theme.id as ColorThemeType,
										);
									}}
									className={`w-full p-3 rounded-lg border-2 transition-all duration-200 text-left ${
										isActive
											? "border-sky-400 bg-sky-50 shadow-md"
											: "border-gray-200 hover:border-sky-300 hover:bg-sky-25"
									}`}
								>
									<div className="flex items-start justify-between mb-2">
										<div className="flex items-center gap-2">
											<span
												className={`text-sm font-medium ${
													isActive
														? "text-sky-800"
														: "text-gray-700"
												}`}
											>
												{theme.name}
											</span>
											{isActive && (
												<div className="w-2 h-2 bg-sky-500 rounded-full" />
											)}
										</div>
										{isRecommended && (
											<Badge className="text-xs bg-green-100 text-green-700 border-green-300">
												{translations.colorThemeSelector.recommended()}
											</Badge>
										)}
									</div>

									{/* 颜色预览条 */}
									<div className="flex gap-1 mb-2">
										{theme.previewColors.map(
											(color: string, index: number) => (
												<div
													key={index}
													className="w-4 h-4 rounded-sm border border-white shadow-sm"
													style={{
														backgroundColor: color,
													}}
												/>
											),
										)}
									</div>

									<p
										className={`text-xs ${
											isActive
												? "text-sky-600"
												: "text-gray-500"
										}`}
									>
										{theme.description}
									</p>

									{/* 类别标签 */}
									<div className="mt-2">
										<Badge className="text-xs border border-gray-300 bg-gray-50 text-gray-600">
											{theme.category === "classic" &&
												translations.colorThemeSelector.categories.classic()}
											{theme.category === "modern" &&
												translations.colorThemeSelector.categories.modern()}
											{theme.category === "nature" &&
												translations.colorThemeSelector.categories.nature()}
											{theme.category === "vibrant" &&
												translations.colorThemeSelector.categories.vibrant()}
											{theme.category === "minimal" &&
												translations.colorThemeSelector.categories.minimal()}
										</Badge>
									</div>
								</button>
							);
						})}
					</div>
				</div>
			</div>
		</div>
	);
}
