"use client";

import { Badge } from "@ui/components/badge";
import { Card } from "@ui/components/card";
import { Globe, Star } from "lucide-react";
import {
	ATMOSPHERE_CONFIGS,
	ATMOSPHERE_NAMES,
	type AtmosphereTheme,
} from "./AtmosphereSwitcher";

interface AtmosphereDemoProps {
	theme: AtmosphereTheme;
	className?: string;
}

export function AtmosphereDemo({ theme, className = "" }: AtmosphereDemoProps) {
	const config = ATMOSPHERE_CONFIGS[theme];
	const name = ATMOSPHERE_NAMES[theme];

	return (
		<Card
			className={`p-4 bg-white/80 backdrop-blur-sm border-sky-200 ${className}`}
		>
			<div className="flex items-center gap-2 mb-3">
				<Globe className="w-4 h-4 text-sky-500" />
				<span className="text-sm font-medium text-gray-800">
					当前大气层
				</span>
				<Badge className="text-xs">{name}</Badge>
			</div>

			{/* 大气层颜色预览 */}
			<div className="mb-3">
				<div className="flex gap-2 h-12 rounded-lg overflow-hidden border border-gray-200">
					<div
						className="flex-1 flex items-center justify-center text-xs text-white/80 font-medium"
						style={{ backgroundColor: config["space-color"] }}
					>
						太空
					</div>
					<div
						className="flex-1 flex items-center justify-center text-xs text-white/80 font-medium"
						style={{ backgroundColor: config["high-color"] }}
					>
						高空
					</div>
					<div
						className="flex-1 flex items-center justify-center text-xs text-gray-700 font-medium"
						style={{ backgroundColor: config.color }}
					>
						地平线
					</div>
				</div>
			</div>

			{/* 效果参数 */}
			<div className="space-y-2 text-xs">
				<div className="flex justify-between items-center">
					<span className="text-gray-600">星星强度</span>
					<div className="flex items-center gap-1">
						{Array.from({ length: 5 }, (_, i) => (
							<Star
								key={i}
								className={`w-3 h-3 ${
									i < config["star-intensity"] * 5
										? "text-yellow-400 fill-current"
										: "text-gray-300"
								}`}
							/>
						))}
						<span className="ml-2 text-gray-700">
							{Math.round(config["star-intensity"] * 100)}%
						</span>
					</div>
				</div>

				<div className="flex justify-between items-center">
					<span className="text-gray-600">大气混合</span>
					<div className="flex items-center gap-2">
						<div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
							<div
								className="h-full bg-sky-400 transition-all duration-300"
								style={{
									width: `${config["horizon-blend"] * 100}%`,
								}}
							/>
						</div>
						<span className="text-gray-700">
							{Math.round(config["horizon-blend"] * 100)}%
						</span>
					</div>
				</div>

				<div className="flex justify-between items-center">
					<span className="text-gray-600">可见范围</span>
					<span className="text-gray-700">
						{config.range[0]} - {config.range[1]}
					</span>
				</div>
			</div>

			{/* 效果描述 */}
			<div className="mt-3 p-2 bg-sky-50 rounded text-xs text-sky-700">
				{getThemeDescription(theme)}
			</div>
		</Card>
	);
}

function getThemeDescription(theme: AtmosphereTheme): string {
	const descriptions = {
		day: "明亮清澈的白天天空，适合查看地理细节",
		night: "深邃星空，满天繁星闪烁，营造浪漫氛围",
		sunset: "温暖的日落色调，橙红渐变，充满诗意",
		dawn: "清晨的金色光辉，柔和渐变，象征新的开始",
		aurora: "神秘的极光效果，绿色光芒在夜空中舞动",
		"deep-space": "深邃宇宙空间，星空璀璨，探索感十足",
		ocean: "清澈的海洋天空，蓝色基调，清新自然",
		minimal: "极简风格，纯净优雅，不会分散注意力",
	};

	return descriptions[theme] || "自定义大气层效果";
}
