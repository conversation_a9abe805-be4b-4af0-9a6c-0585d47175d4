import { logger } from "@repo/logs";
import { detectProviderTypeFromEnv, getProviderConfigFromEnv } from "./config";
import {
	createCloudflareR2Provider,
	createS3Provider,
	createTencentCOSProvider,
} from "./provider";
import type { StorageProvider, StorageProviderConfig } from "./types";

/**
 * 存储提供商类型
 */
export type StorageProviderType =
	| "s3"
	| "aliyun-oss"
	| "cloudflare-r2"
	| "tencent-cos";

/**
 * 创建存储提供商
 * @param type 提供商类型
 * @param config 提供商配置
 */
export function createStorageProvider(
	type: StorageProviderType,
	config: StorageProviderConfig,
): StorageProvider {
	switch (type) {
		case "s3":
			return createS3Provider(config);
		case "cloudflare-r2":
			return createCloudflareR2Provider(config);
		case "aliyun-oss":
			throw new Error("阿里云OSS的支持尚未实现");
		case "tencent-cos":
			return createTencentCOSProvider(config);
		default:
			throw new Error(`不支持的存储提供商类型: ${type}`);
	}
}

/**
 * 从环境变量创建存储提供商
 * @param type 提供商类型，如果未指定，将尝试从环境变量中检测
 */
export function createStorageProviderFromEnv(
	type?: StorageProviderType,
): StorageProvider {
	// 如果未指定类型，尝试从环境变量中检测
	if (!type) {
		const detectedType = detectProviderTypeFromEnv();
		if (!detectedType) {
			throw new Error("无法从环境变量中检测存储提供商类型，请明确指定");
		}
		type = detectedType;
	}

	try {
		// 根据类型获取配置并创建存储提供商
		const config = getProviderConfigFromEnv(type);
		return createStorageProvider(type, config);
	} catch (error) {
		logger.error(`创建存储提供商失败: ${(error as Error).message}`);
		throw error;
	}
}

/**
 * 获取用于上传文件的签名URL
 * 这是一个便捷函数，使用默认存储提供商来获取签名上传URL
 *
 * @param path 文件路径
 * @param options 选项，包括存储桶名称
 * @param contentType 可选的内容类型，默认为"image/jpeg"
 * @returns 签名上传URL
 */
export async function getSignedUploadUrl(
	path: string,
	options: { bucket: string; region?: string },
	contentType?: string,
): Promise<string> {
	try {
		const provider = getDefaultStorageProvider();
		// 如果提供了 contentType，需要根据提供商扩展接口或使用批量上传接口
		if (contentType) {
			const result = await provider.getBatchSignedUploadUrls({
				bucket: options.bucket,
				paths: [path],
				contentType,
				region: options.region,
			});

			if (!result.urls[path]) {
				throw new Error(`无法获取路径 ${path} 的签名上传URL`);
			}

			return result.urls[path];
		}

		return await provider.getSignedUploadUrl(path, {
			...options,
			contentType,
		});
	} catch (error) {
		logger.error(`获取签名上传URL失败: ${(error as Error).message}`);
		throw error;
	}
}

/**
 * 获取用于访问文件的签名URL
 * 这是一个便捷函数，使用默认存储提供商来获取签名访问URL
 *
 * @param path 文件路径
 * @param options 选项，包括存储桶名称和过期时间
 * @returns 签名访问URL
 */
export async function getSignedUrl(
	path: string,
	options: { bucket: string; expiresIn?: number; region?: string },
): Promise<string> {
	try {
		const provider = getDefaultStorageProvider();
		return await provider.getSignedUrl(path, options);
	} catch (error) {
		logger.error(`获取签名访问URL失败: ${(error as Error).message}`);
		throw error;
	}
}

/**
 * 直接上传文件
 * 这是一个便捷函数，使用默认存储提供商直接上传文件
 *
 * @param options 上传选项
 * @returns 上传结果
 */
export async function uploadFile(options: {
	bucket: string;
	path: string;
	file: Buffer | NodeJS.ReadableStream;
	contentType?: string;
	region?: string;
}): Promise<{
	success: boolean;
	path?: string;
	etag?: string;
	error?: string;
}> {
	try {
		const provider = getDefaultStorageProvider();
		return await provider.uploadFile(options);
	} catch (error) {
		logger.error(`上传文件失败: ${(error as Error).message}`);
		return {
			success: false,
			error: error instanceof Error ? error.message : String(error),
		};
	}
}

/**
 * 获取默认存储提供商
 */
export default function getDefaultStorageProvider(): StorageProvider {
	try {
		return createStorageProviderFromEnv();
	} catch (error) {
		logger.error(`获取默认存储提供商失败: ${(error as Error).message}`);
		throw error;
	}
}

export * from "./types";
export * from "./provider";
export * from "./config";
