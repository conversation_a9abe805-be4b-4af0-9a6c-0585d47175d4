# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

MapMoment is a sophisticated travel application platform built with a monorepo architecture using pnpm workspaces and Turborepo. The project consists of three main applications and comprehensive shared packages for building travel diaries, AI image generation, and video export services.

## Architecture

### Monorepo Structure
- **Root**: Turborepo configuration with pnpm workspaces
- **Apps**: Main applications in `/apps/` directory
- **Packages**: Shared libraries and services in `/packages/` directory
- **Tooling**: Build tools and configurations in `/tooling/` directory

### Core Applications

| Application | Port | Package Name | Description |
|-------------|------|--------------|-------------|
| MapMoment | 3000 | `@repo/web` | Main travel diary application |
| AI Image Generator | 3001 | `@repo/ai-image-generator` | AI-powered image generation service |
| Video Exporter | Background | `video-exporter` | Video processing service |

### Key Technologies
- **Frontend**: Next.js 15 App Router, React 19, TypeScript, Tailwind CSS 4.1
- **Backend**: Hono.js API, Prisma ORM, PostgreSQL (dual database setup)
- **Auth**: Better Auth
- **Maps**: Mapbox GL, Google Maps integration
- **Rich Text**: TipTap with custom travel point nodes
- **AI**: OpenAI, Gemini, Volcengine providers
- **Storage**: AWS S3, Cloudflare R2, Tencent COS
- **Payments**: Stripe, LemonSqueezy
- **Build**: Biome for linting/formatting

## Common Development Commands

### Service Management
```bash
# Development
pnpm dev:mapmoment        # Start main app (localhost:3000)
pnpm dev:ai-images        # Start AI app (localhost:3001)
pnpm dev:video-exporter   # Start video service (background)
pnpm dev:all              # Start all services

# Production
pnpm build:all            # Build all services
pnpm start:all            # Start all in production

# Individual service builds
pnpm build:mapmoment
pnpm build:ai-images
pnpm build:video-exporter
```

### Database Operations
```bash
# Type generation
pnpm db:generate          # Generate types for all databases

# Main database (travel diaries)
pnpm db:migrate --name "migration_name"    # Create migration
pnpm db:studio                             # Database UI

# AI Images database
pnpm db:migrate:ai migration_name          # AI DB migration
pnpm db:studio:ai                          # AI DB UI

# Multi-database script
pnpm --filter database tsx scripts/migrate-ai-images.ts [command]
```

### Code Quality
```bash
pnpm lint                 # Run Biome linter
pnpm lint:fix             # Fix lint issues
pnpm format               # Format code with Biome
pnpm type-check           # TypeScript check
pnpm ci-check             # Full CI pipeline check
```

### Testing
```bash
pnpm test:google-maps           # Test Google Maps integration
pnpm test:format-richtext       # Test rich text formatting
pnpm test:unified-geocoding     # Test geocoding services
```

## Database Architecture

### Dual Database Setup
The project uses **two separate PostgreSQL databases**:

1. **Main Database** (`DATABASE_URL`): User management, travel diaries, feature voting, payments
2. **AI Images Database** (`AI_IMAGES_DATABASE_URL`): AI-generated images and metadata

### Schema Files
- `packages/database/prisma/schema.prisma` - Main database
- `packages/database/prisma/schema-ai-images.prisma` - AI images database

### Database Clients
```typescript
// Main database
import { prisma } from '@repo/database';

// AI images database  
import { aiImagesPrisma } from '@repo/database';
```

## Development Conventions

### Project Structure (from .cursorrules)
- Frontend code: `apps/web/` (Next.js App Router)
- Backend packages: `packages/[api|auth|database|ai|storage|etc]/`
- Use lowercase with dashes for directories (e.g., `auth-wizard`)
- Favor named exports for components
- Group by feature rather than type

### TypeScript Standards
- Use TypeScript for all code
- Prefer interfaces over types
- Avoid enums; use maps instead
- Use functional components with TypeScript interfaces

### React/Next.js Patterns
- Minimize 'use client', 'useEffect', 'setState'
- Favor React Server Components (RSC)
- Use 'nuqs' for URL search parameter state management
- Wrap client components in Suspense with fallback
- Use dynamic loading for non-critical components

### UI/Styling
- Use Shadcn UI, Radix UI, and Tailwind CSS
- Mobile-first responsive design approach
- Use `cn` function for class name concatenation
- Optimize images: WebP format, size data, lazy loading

## Key Package Structure

### Core Packages
- `@repo/api`: Hono.js API routes and business logic
- `@repo/auth`: Better Auth configuration and helpers
- `@repo/database`: Prisma schemas and database clients
- `@repo/ai`: AI service providers (OpenAI, Gemini, Volcengine)
- `@repo/storage`: File storage abstraction (S3, R2, COS)
- `@repo/payments`: Payment processing (Stripe, LemonSqueezy)
- `@repo/mail`: Email services and templates
- `@repo/i18n`: Internationalization support
- `@repo/shared-ui`: Reusable UI components
- `@repo/utils`: Shared utilities and feature voting
- `@repo/video-exporter`: Background video processing

### Feature-Specific Architecture

#### Travel Diary System
- Rich text editor with custom TipTap nodes for travel points
- Mapbox/Google Maps integration for location visualization
- Image upload with storage provider abstraction
- Timeline and statistics generation

#### AI Image Generation
- Multi-provider AI integration (OpenAI, Gemini, Volcengine)
- Template management system
- Separate database for image metadata and collections

#### Video Export Service
- BullMQ job queue processing
- Background video generation from travel data
- Progress tracking and status updates

## Environment Configuration

### Required Environment Variables
```bash
# Databases
DATABASE_URL="postgresql://localhost:5432/travel_memo"
AI_IMAGES_DATABASE_URL="postgresql://localhost:5432/ai_images"

# API Keys
OPENAI_API_KEY="your-openai-key"
NEXTAUTH_SECRET="your-secret"

# Storage
STORAGE_PROVIDER="s3|r2|cos"
AWS_ACCESS_KEY_ID="your-key"
AWS_SECRET_ACCESS_KEY="your-secret"

# Services (optional, defaults provided)
PORT=3000
AI_IMAGES_PORT=3001
```

## Multi-Database Management

### Field Updates Best Practices
```bash
# Main database changes
1. Modify packages/database/prisma/schema.prisma
2. pnpm --filter database migrate --name "description"
3. pnpm --filter database generate

# AI database changes  
1. Modify packages/database/prisma/schema-ai-images.prisma
2. pnpm --filter database tsx scripts/migrate-ai-images.ts migrate description
3. pnpm --filter database tsx scripts/migrate-ai-images.ts generate
```

### User Synchronization
Users must be synchronized across databases since they exist in both systems but serve different purposes.

## Performance Considerations

- Server-first approach with Next.js App Router
- Separate databases for performance isolation
- Image optimization with multiple storage providers
- Background processing for video exports
- Efficient map rendering with proper marker management

## Development Workflow

1. **Daily Development**: Start only needed services with individual commands
2. **Full-Stack Development**: Use `pnpm dev:all` for complete environment
3. **Database Changes**: Always test migrations in development first
4. **Multi-Service Testing**: Use service-specific test commands
5. **Production Deployment**: Build and deploy services independently

## Important Notes

- Always run `pnpm db:generate` after schema changes
- Use the multi-database migration scripts for AI database changes
- Consider user synchronization when making user-related schema changes
- Test cross-service functionality with `dev:all` before deployment
- Follow the monorepo package conventions when adding new functionality