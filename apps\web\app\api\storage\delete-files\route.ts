import { createStorageProviderFromEnv } from "@repo/storage";
import type { StorageProviderType } from "@repo/storage";
import { type NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
	try {
		const requestData = await request.json();
		const { provider, bucket, paths, region } = requestData;

		if (!bucket || !paths || !Array.isArray(paths)) {
			return NextResponse.json(
				{ error: "缺少必要参数 bucket 或 paths 不是数组" },
				{ status: 400 },
			);
		}

		// 创建存储提供商实例
		let storageProvider;

		try {
			// 如果指定了provider，使用指定的provider
			if (provider) {
				storageProvider = createStorageProviderFromEnv(
					provider as StorageProviderType,
				);
			} else {
				// 否则尝试从环境变量中检测默认提供商
				storageProvider = createStorageProviderFromEnv();
			}
		} catch (error) {
			console.error("创建存储提供商失败:", error);
			return NextResponse.json(
				{
					error:
						error instanceof Error
							? error.message
							: "无法创建存储提供商",
				},
				{ status: 500 },
			);
		}

		// 执行批量删除
		const result = await storageProvider.batchDeleteObjects({
			bucket,
			paths,
			region, // 添加区域支持
		});

		return NextResponse.json(result);
	} catch (error) {
		console.error("删除文件错误:", error);
		return NextResponse.json(
			{
				error: error instanceof Error ? error.message : "删除文件失败",
			},
			{ status: 500 },
		);
	}
}
