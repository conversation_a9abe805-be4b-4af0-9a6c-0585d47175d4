import type { SocialPlatform } from "../types/cardTypes";

// 平台尺寸配置
export const PLATFORM_DIMENSIONS = {
	instagram: { width: 1080, height: 1080, aspectRatio: 1 },
	wechat: { width: 1200, height: 900, aspectRatio: 4 / 3 },
	weibo: { width: 1080, height: 1350, aspectRatio: 4 / 5 },
	twitter: { width: 1200, height: 675, aspectRatio: 16 / 9 },
	facebook: { width: 1200, height: 630, aspectRatio: 1.91 },
} as const;

// 基准尺寸（Instagram 正方形）
const BASE_DIMENSIONS = { width: 1080, height: 1080 };

// 计算平台的缩放因子
export function calculatePlatformScale(platform: SocialPlatform): {
	scale: number;
	dimensions: { width: number; height: number };
	aspectRatio: number;
} {
	const dimensions =
		PLATFORM_DIMENSIONS[platform] || PLATFORM_DIMENSIONS.instagram;

	// 基于面积的智能缩放
	const baseArea = BASE_DIMENSIONS.width * BASE_DIMENSIONS.height;
	const platformArea = dimensions.width * dimensions.height;
	const areaScale = Math.sqrt(platformArea / baseArea);

	// 限制缩放范围，避免过大或过小
	const scale = Math.max(0.7, Math.min(1.5, areaScale));

	return {
		scale,
		dimensions: {
			width: dimensions.width,
			height: dimensions.height,
		},
		aspectRatio: dimensions.aspectRatio,
	};
}

// 计算调整后的字体大小
export function calculateAdjustedTypography(
	baseTypography: {
		headerSize?: number;
		bodySize?: number;
		titleSize?: number;
		statsSize?: number;
		smallSize?: number;
	},
	scale: number,
) {
	return {
		titleSize: Math.round((baseTypography.titleSize || 56) * scale),
		statsSize: Math.round((baseTypography.statsSize || 80) * scale),
		headerSize: Math.round((baseTypography.headerSize || 36) * scale),
		bodySize: Math.round((baseTypography.bodySize || 20) * scale),
		smallSize: Math.round((baseTypography.smallSize || 16) * scale),
	};
}

// 计算调整后的布局尺寸
export function calculateAdjustedLayout(
	baseLayout: {
		padding?: number;
		spacing?: number;
		borderRadius?: number;
	},
	scale: number,
) {
	return {
		padding: Math.round((baseLayout.padding || 32) * scale),
		spacing: Math.round((baseLayout.spacing || 20) * scale),
		borderRadius: Math.round((baseLayout.borderRadius || 12) * scale),
	};
}

// 预览区域的智能适配
export function calculatePreviewAdaptation(
	platform: SocialPlatform,
	containerSize: { width: number; height: number },
): {
	previewScale: number;
	previewDimensions: { width: number; height: number };
	containerStyle: React.CSSProperties;
} {
	const platformInfo = calculatePlatformScale(platform);
	const { width: cardWidth, height: cardHeight } = platformInfo.dimensions;

	// 计算预览区域的最佳缩放比例
	const scaleX = containerSize.width / cardWidth;
	const scaleY = containerSize.height / cardHeight;
	const previewScale = Math.min(scaleX, scaleY, 1.0); // 限制最大预览比例为80%

	const previewWidth = cardWidth * previewScale;
	const previewHeight = cardHeight * previewScale;

	return {
		previewScale,
		previewDimensions: {
			width: previewWidth,
			height: previewHeight,
		},
		containerStyle: {
			display: "flex",
			alignItems: "center",
			justifyContent: "center",
			width: "100%",
			height: "100%",
			minHeight: `${previewHeight + 40}px`, // 增加一些边距
		},
	};
}

// 获取平台的友好名称
export function getPlatformDisplayName(platform: SocialPlatform): string {
	const names = {
		instagram: "Instagram",
		wechat: "微信朋友圈",
		weibo: "新浪微博",
		twitter: "Twitter",
		facebook: "Facebook",
	};
	return names[platform] || platform;
}

// 获取平台的图标
export function getPlatformIcon(platform: SocialPlatform): string {
	const icons = {
		instagram: "📷",
		wechat: "💬",
		weibo: "🔥",
		twitter: "🐦",
		facebook: "👥",
	};
	return icons[platform] || "📱";
}

// 获取平台的建议描述
export function getPlatformDescription(platform: SocialPlatform): string {
	const descriptions = {
		instagram: "正方形格式，适合精美展示",
		wechat: "横向布局，适合朋友圈分享",
		weibo: "竖向布局，适合信息展示",
		twitter: "宽屏格式，适合话题传播",
		facebook: "链接预览格式，适合社交分享",
	};
	return descriptions[platform] || "";
}

// 验证平台支持
export function isPlatformSupported(
	platform: string,
): platform is SocialPlatform {
	return platform in PLATFORM_DIMENSIONS;
}

// 社交平台卡片尺寸配置
export interface PlatformDimensions {
	width: number;
	height: number;
	aspectRatio: number;
}

export const platformDimensions: Record<string, PlatformDimensions> = {
	instagram: { width: 1080, height: 1080, aspectRatio: 1 },
	wechat: { width: 1200, height: 900, aspectRatio: 4 / 3 },
	weibo: { width: 1080, height: 1350, aspectRatio: 4 / 5 },
	twitter: { width: 1200, height: 675, aspectRatio: 16 / 9 },
	facebook: { width: 1200, height: 630, aspectRatio: 1.91 },
};

export type LayoutStrategy = "wide" | "tall" | "square";

export interface PlatformLayoutInfo {
	dimensions: PlatformDimensions;
	strategy: LayoutStrategy;
	isWideFormat: boolean;
	isTallFormat: boolean;
	isSquareFormat: boolean;
}

/**
 * 获取平台布局信息
 */
export function getPlatformLayoutInfo(platform: string): PlatformLayoutInfo {
	const dimensions =
		platformDimensions[platform] || platformDimensions.instagram;

	const isWideFormat = dimensions.aspectRatio > 1.5; // Twitter, Facebook
	const isTallFormat = dimensions.aspectRatio < 0.9; // Weibo
	const isSquareFormat =
		dimensions.aspectRatio >= 0.9 && dimensions.aspectRatio <= 1.3; // Instagram, WeChat

	let strategy: LayoutStrategy;
	if (isWideFormat) {
		strategy = "wide";
	} else if (isTallFormat) {
		strategy = "tall";
	} else {
		strategy = "square";
	}

	return {
		dimensions,
		strategy,
		isWideFormat,
		isTallFormat,
		isSquareFormat,
	};
}

/**
 * 计算平台适配的字体大小
 */
export function getAdaptedTypography(
	platform: string,
	baseFontSizes: {
		titleSize?: number;
		statsSize?: number;
		headerSize?: number;
		bodySize?: number;
		smallSize?: number;
	},
	options?: {
		titleWeight?: number;
		fontFamily?: string;
	},
) {
	const { dimensions, isWideFormat } = getPlatformLayoutInfo(platform);
	const scale = Math.min(dimensions.width / 1080, 1.5);

	// 宽屏格式需要相对较小的字体
	const wideFormatMultiplier = isWideFormat ? 0.8 : 1;

	return {
		titleSize: Math.round(
			(baseFontSizes.titleSize || 56) * scale * wideFormatMultiplier,
		),
		statsSize: Math.round(
			(baseFontSizes.statsSize || 80) * scale * wideFormatMultiplier,
		),
		headerSize: Math.round((baseFontSizes.headerSize || 36) * scale),
		bodySize: Math.round((baseFontSizes.bodySize || 20) * scale),
		smallSize: Math.round((baseFontSizes.smallSize || 16) * scale),
		titleWeight: options?.titleWeight || 600,
		fontFamily: options?.fontFamily || "'Inter', sans-serif",
	};
}

/**
 * 获取平台适配的布局值
 */
export function getLayoutValue(
	type: "padding" | "spacing" | "borderRadius",
	baseValue: number,
	platform?: string,
): number {
	if (!platform) return baseValue;

	const { isWideFormat } = getPlatformLayoutInfo(platform);

	// 宽屏格式使用相对较小的内边距和间距
	if (isWideFormat) {
		switch (type) {
			case "padding":
				return Math.round(baseValue * 0.8);
			case "spacing":
				return Math.round(baseValue * 0.7);
			case "borderRadius":
				return Math.round(baseValue * 0.9);
			default:
				return baseValue;
		}
	}

	return baseValue;
}
