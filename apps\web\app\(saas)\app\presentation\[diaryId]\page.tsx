"use client";

import type { FrontendTravelDiary } from "@repo/database/src/types/travel-diary";
import { useTranslations } from "next-intl";
import { useParams } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { transformDiaryContent } from "../../services/diary-service";
import { CoverPage } from "../components/CoverPage";
import { MapStoryPage } from "../components/MapStoryPage";
import { RecapPage } from "../components/RecapPage";
import { ErrorPage } from "../components/common/ErrorPage";
import { LoadingSpinner } from "../components/common/LoadingSpinner";
import { apiClient } from "../utils/api-client";

export default function TravelStoryPage() {
	const t = useTranslations("travelMemo.presentationPage");
	const { diaryId } = useParams<{ diaryId: string }>();
	const [diary, setDiary] = useState<FrontendTravelDiary | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [currentPage, setCurrentPage] = useState(0);
	const [debugInfo, setDebugInfo] = useState<string>("");
	const [animationClass, setAnimationClass] = useState("");

	// Refs for scrolling
	const containerRef = useRef<HTMLDivElement>(null);
	const coverPageRef = useRef<HTMLDivElement>(null);
	const mapPageRef = useRef<HTMLDivElement>(null);
	const recapPageRef = useRef<HTMLDivElement>(null);

	// 仅为第一页添加滚动监听
	useEffect(() => {
		if (currentPage !== 0 || !containerRef.current) return;

		let startY = 0;
		let isScrolling = false;
		let scrollTimeout: NodeJS.Timeout | null = null;

		const resetScrollFlag = () => {
			isScrolling = false;
		};

		const handleTouchStart = (e: TouchEvent) => {
			startY = e.touches[0].clientY;
			setDebugInfo(`触摸开始: ${startY}`);
		};

		const handleTouchMove = (e: TouchEvent) => {
			if (currentPage !== 0) return;

			const currentY = e.touches[0].clientY;
			const diff = startY - currentY;

			setDebugInfo(
				`触摸移动: 起始=${startY}, 当前=${currentY}, 差值=${diff}`,
			);

			// 如果向下滑动超过一定阈值，切换到第二页
			if (diff > 50 && !isScrolling) {
				isScrolling = true;
				setDebugInfo("触发页面切换 (触摸)");
				scrollToPage(1);

				if (scrollTimeout) clearTimeout(scrollTimeout);
				scrollTimeout = setTimeout(resetScrollFlag, 1000);
			}
		};

		const handleWheel = (e: WheelEvent) => {
			if (currentPage !== 0) return;

			setDebugInfo(`滚轮事件: deltaY=${e.deltaY}, deltaX=${e.deltaX}`);

			if (e.deltaY > 30 && !isScrolling) {
				e.preventDefault();
				isScrolling = true;
				setDebugInfo("触发页面切换 (滚轮)");
				scrollToPage(1);

				if (scrollTimeout) clearTimeout(scrollTimeout);
				scrollTimeout = setTimeout(resetScrollFlag, 1000);
			}
		};

		// 添加事件监听
		const container = containerRef.current;

		// 对容器及其所有子元素添加事件监听
		container.addEventListener("touchstart", handleTouchStart, {
			passive: true,
		});
		container.addEventListener("touchmove", handleTouchMove, {
			passive: true,
		});
		container.addEventListener("wheel", handleWheel, { passive: false });

		// 对文档添加事件监听，以防止事件冒泡被阻止
		document.addEventListener("wheel", handleWheel, {
			passive: false,
			capture: true,
		});

		setDebugInfo("已添加滚动监听");

		return () => {
			container.removeEventListener("touchstart", handleTouchStart);
			container.removeEventListener("touchmove", handleTouchMove);
			container.removeEventListener("wheel", handleWheel);
			document.removeEventListener("wheel", handleWheel, {
				capture: true,
			});

			if (scrollTimeout) clearTimeout(scrollTimeout);
		};
	}, [currentPage]);

	// 加载日记数据
	useEffect(() => {
		async function loadDiary() {
			if (!diaryId) {
				setError(t("errorInvalidDiaryId"));
				setIsLoading(false);
				return;
			}

			try {
				setIsLoading(true);
				// 使用API客户端获取日记数据
				const diaryData = await apiClient.getDiary(diaryId as string);

				if (!diaryData) {
					setError(t("errorDiaryNotFound"));
					setIsLoading(false);
					return;
				}

				// 转换为前端使用的数据格式
				const frontendDiary = transformDiaryContent(diaryData);
				setDiary(frontendDiary);
				setIsLoading(false);
			} catch (err) {
				console.error("加载日记失败", err);
				setError(t("errorLoadDiaryFailed"));
				setIsLoading(false);
			}
		}

		loadDiary();
	}, [diaryId, t]);

	// 处理页面切换
	const scrollToPage = (pageIndex: number) => {
		// 如果是同一个页面，不做任何处理
		if (pageIndex === currentPage) return;

		// 设置动画类型
		setAnimationClass("animate-page-transition");

		// 500ms后切换页面
		setTimeout(() => {
			setCurrentPage(pageIndex);
			// 重置动画类，准备下一次动画
			setTimeout(() => {
				setAnimationClass("");
			}, 50);
		}, 500);
	};

	// 监听地图故事页完成播放事件
	const handleMapStoryComplete = () => {
		setDebugInfo("地图故事完成，准备跳转到回顾页面");
		scrollToPage(2);
	};

	if (isLoading) {
		return <LoadingSpinner message={t("loadingMessage")} />;
	}

	if (error || !diary) {
		return <ErrorPage message={error || t("defaultLoadError")} />;
	}

	// 所有点位合并
	const allPoints = diary.timelines
		.flatMap((timeline) => timeline.points)
		.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

	// 计算旅行统计数据
	const stats = {
		totalLocations: allPoints.length,
		totalImages: allPoints.reduce(
			(sum, point) => sum + (point.images?.length || 0),
			0,
		),
		startDate: allPoints.length > 0 ? allPoints[0].timestamp : null,
		endDate:
			allPoints.length > 0
				? allPoints[allPoints.length - 1].timestamp
				: null,
	};

	return (
		<div
			ref={containerRef}
			className="w-full h-screen overflow-hidden relative"
		>
			{/* 开发环境调试信息 */}
			{process.env.NODE_ENV !== "production" && debugInfo && (
				<div className="fixed top-2 right-2 z-50 bg-black/70 text-white text-xs p-2 rounded max-w-xs">
					{debugInfo}
				</div>
			)}

			{/* 当前页面内容 */}
			<div className={`h-full w-full ${animationClass}`}>
				{currentPage === 0 && (
					<div
						ref={coverPageRef}
						className="h-full w-full"
						onWheel={(e) => {
							if (e.deltaY > 30) {
								e.preventDefault();
								scrollToPage(1);
							}
						}}
					>
						<CoverPage
							diary={diary}
							stats={stats}
							onContinue={() => scrollToPage(1)}
						/>
					</div>
				)}

				{currentPage === 1 && (
					<div ref={mapPageRef} className="h-full w-full">
						<MapStoryPage
							diary={diary}
							points={allPoints}
							onComplete={handleMapStoryComplete}
						/>
					</div>
				)}

				{currentPage === 2 && (
					<div ref={recapPageRef} className="h-full w-full">
						<RecapPage
							diary={diary}
							points={allPoints}
							stats={stats}
						/>
					</div>
				)}
			</div>

			{/* 添加动画样式 */}
			<style jsx global>{`
				@keyframes slideUpOut {
					from { transform: translateY(0); }
					to { transform: translateY(-100%); }
				}
				
				@keyframes slideUpIn {
					from { transform: translateY(100%); }
					to { transform: translateY(0); }
				}
				
				.animate-page-transition {
					animation: slideUpOut 500ms cubic-bezier(0.65, 0, 0.35, 1) forwards;
					position: relative;
				}
				
				.animate-page-transition::after {
					content: '';
					position: fixed;
					top: 100%;
					left: 0;
					right: 0;
					bottom: -100%;
					background-color: ${currentPage === 0 ? "#f5f5f5" : currentPage === 1 ? "#ffffff" : "#f8f9fa"};
					animation: slideUpIn 500ms cubic-bezier(0.65, 0, 0.35, 1) forwards;
					z-index: 30;
				}
			`}</style>
		</div>
	);
}
