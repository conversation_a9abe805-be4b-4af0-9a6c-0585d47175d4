"use client";

import { getAnonymousUser, getOrCreateAnonymousId } from "@repo/utils";
import { useCallback, useEffect, useState } from "react";
import type {
	ApiResponse,
	CommentRequest,
	FeatureRequest,
	Product,
	SubmitFeatureRequest,
	UseFeatureVotingOptions,
	UseFeatureVotingReturn,
	VoteRequest,
} from "../types";

/**
 * Feature Voting Hook
 * 管理特性投票的状态和API调用
 */
export function useFeatureVoting(
	options: UseFeatureVotingOptions = {},
): UseFeatureVotingReturn {
	const {
		productId,
		showVoteCounts = false,
		autoRefresh = false,
		refreshInterval = 30000, // 30秒
	} = options;

	const [featureRequests, setFeatureRequests] = useState<FeatureRequest[]>(
		[],
	);
	const [products, setProducts] = useState<Product[]>([]);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// 获取匿名用户信息
	const getAnonymousUserData = useCallback(() => {
		if (typeof window === "undefined") return null;
		return getAnonymousUser();
	}, []);

	// API调用辅助函数
	const apiCall = useCallback(
		async <T>(
			url: string,
			options: RequestInit = {},
		): Promise<ApiResponse<T>> => {
			try {
				const anonymousUser = getAnonymousUserData();
				const headers: HeadersInit = {
					"Content-Type": "application/json",
					...options.headers,
				};

				// 添加匿名用户标识到请求头
				if (anonymousUser?.id) {
					headers["x-anonymous-id"] = anonymousUser.id;
				}

				const response = await fetch(`/api${url}`, {
					...options,
					headers,
				});

				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				return await response.json();
			} catch (err) {
				console.error("API call failed:", err);
				throw err;
			}
		},
		[getAnonymousUserData],
	);

	// 获取特性请求列表
	const fetchFeatureRequests = useCallback(async () => {
		try {
			setIsLoading(true);
			setError(null);

			const params = new URLSearchParams();
			if (productId) params.append("productId", productId);
			if (showVoteCounts) params.append("showVoteCounts", "true");
			params.append("sortBy", "voteCount");
			params.append("sortOrder", "desc");

			const response = await apiCall<FeatureRequest[]>(
				`/feature-requests?${params.toString()}`,
			);

			if (response.success && response.data) {
				setFeatureRequests(response.data);
			} else {
				setError(response.error || "获取特性请求失败");
			}
		} catch (err) {
			setError(err instanceof Error ? err.message : "获取特性请求失败");
		} finally {
			setIsLoading(false);
		}
	}, [productId, showVoteCounts, apiCall]);

	// 获取产品列表
	const fetchProducts = useCallback(async () => {
		try {
			const response = await apiCall<Product[]>("/products");
			if (response.success && response.data) {
				setProducts(response.data);
			}
		} catch (err) {
			console.error("获取产品列表失败:", err);
		}
	}, [apiCall]);

	// 投票
	const vote = useCallback(
		async (featureRequestId: string, voteData?: VoteRequest) => {
			try {
				const anonymousUser = getAnonymousUserData();
				const requestData: VoteRequest = {
					anonymousId: anonymousUser?.id || getOrCreateAnonymousId(),
					voterName: voteData?.voterName || anonymousUser?.name,
					voterEmail: voteData?.voterEmail || anonymousUser?.email,
					...voteData,
				};

				const response = await apiCall(
					`/feature-requests/${featureRequestId}/vote`,
					{
						method: "POST",
						body: JSON.stringify(requestData),
					},
				);

				if (response.success) {
					// 更新本地状态
					setFeatureRequests((prev) =>
						prev.map((fr) =>
							fr.id === featureRequestId
								? {
										...fr,
										hasVoted: true,
										voteCount: fr.voteCount + 1,
									}
								: fr,
						),
					);
				} else {
					throw new Error(response.error || "投票失败");
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : "投票失败";
				setError(errorMessage);
				throw new Error(errorMessage);
			}
		},
		[apiCall, getAnonymousUserData],
	);

	// 取消投票
	const unvote = useCallback(
		async (featureRequestId: string) => {
			try {
				const response = await apiCall(
					`/feature-requests/${featureRequestId}/vote`,
					{
						method: "DELETE",
					},
				);

				if (response.success) {
					// 更新本地状态
					setFeatureRequests((prev) =>
						prev.map((fr) =>
							fr.id === featureRequestId
								? {
										...fr,
										hasVoted: false,
										voteCount: Math.max(
											0,
											fr.voteCount - 1,
										),
									}
								: fr,
						),
					);
				} else {
					throw new Error(response.error || "取消投票失败");
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : "取消投票失败";
				setError(errorMessage);
				throw new Error(errorMessage);
			}
		},
		[apiCall],
	);

	// 提交新特性请求
	const submitFeature = useCallback(
		async (data: SubmitFeatureRequest) => {
			try {
				const anonymousUser = getAnonymousUserData();
				const requestData: SubmitFeatureRequest = {
					...data,
					anonymousId: anonymousUser?.id || getOrCreateAnonymousId(),
					authorName: data.authorName || anonymousUser?.name,
					authorEmail: data.authorEmail || anonymousUser?.email,
				};

				const response = await apiCall("/feature-requests", {
					method: "POST",
					body: JSON.stringify(requestData),
				});

				if (response.success) {
					// 刷新列表
					await fetchFeatureRequests();
				} else {
					throw new Error(response.error || "提交特性请求失败");
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : "提交特性请求失败";
				setError(errorMessage);
				throw new Error(errorMessage);
			}
		},
		[apiCall, getAnonymousUserData, fetchFeatureRequests],
	);

	// 添加评论
	const addComment = useCallback(
		async (featureRequestId: string, comment: CommentRequest) => {
			try {
				const anonymousUser = getAnonymousUserData();
				const requestData: CommentRequest = {
					...comment,
					anonymousId: anonymousUser?.id || getOrCreateAnonymousId(),
					authorName: comment.authorName || anonymousUser?.name,
					authorEmail: comment.authorEmail || anonymousUser?.email,
				};

				const response = await apiCall(
					`/feature-requests/${featureRequestId}/comments`,
					{
						method: "POST",
						body: JSON.stringify(requestData),
					},
				);

				if (!response.success) {
					throw new Error(response.error || "添加评论失败");
				}
			} catch (err) {
				const errorMessage =
					err instanceof Error ? err.message : "添加评论失败";
				setError(errorMessage);
				throw new Error(errorMessage);
			}
		},
		[apiCall, getAnonymousUserData],
	);

	// 刷新数据
	const refresh = useCallback(async () => {
		await Promise.all([fetchFeatureRequests(), fetchProducts()]);
	}, [fetchFeatureRequests, fetchProducts]);

	// 初始化数据加载
	useEffect(() => {
		refresh();
	}, [refresh]);

	// 自动刷新
	useEffect(() => {
		if (!autoRefresh) return;

		const interval = setInterval(() => {
			fetchFeatureRequests();
		}, refreshInterval);

		return () => clearInterval(interval);
	}, [autoRefresh, refreshInterval, fetchFeatureRequests]);

	return {
		featureRequests,
		products,
		isLoading,
		error,
		vote,
		unvote,
		submitFeature,
		addComment,
		refresh,
	};
}
