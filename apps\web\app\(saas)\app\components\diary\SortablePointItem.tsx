import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import type { TravelPointImage } from "@repo/database/src/types/travel-diary";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import { Copy, GripVertical, Trash2 } from "lucide-react";
import { forwardRef } from "react";
import { EditableDate } from "./inline-editing/EditableDate";
import { EditableDescription } from "./inline-editing/EditableDescription";
import { EditableImages } from "./inline-editing/EditableImages";
import { EditableLocation } from "./inline-editing/EditableLocation";

interface TravelPoint {
	id: string;
	date: Date;
	location: string;
	description: string;
	images: TravelPointImage[];
	iconType: string;
	coordinates: { lat: number; lng: number };
}

interface SortablePointItemProps {
	point: TravelPoint;
	isActive?: boolean;
	isHighlighted?: boolean;
	onCopy?: (point: TravelPoint) => void;
	onDelete: (id: string) => void;
	onSelect: (id: string) => void;
	onImagePreview: (point: TravelPoint, index: number) => void;
	getIconBackgroundColor: (iconType: string) => string;
	renderPointIcon: (iconType: string) => React.ReactNode;
	isEditing?: boolean;
	editingFields?: Set<string>;
	onEnterEditMode?: (pointId: string, fieldName: string) => void;
	onExitEditMode?: (
		pointId: string,
		fieldName: string,
		saveChanges: boolean,
	) => void;
	onFieldUpdate?: (pointId: string, fieldName: string, value: any) => void;
	onLocationUpdate?: (
		pointId: string,
		location: string,
		coordinates?: { lat: number; lng: number },
	) => void;
	onImagesUpdate?: (pointId: string, images: TravelPointImage[]) => void;
	getFieldValue?: (
		pointId: string,
		fieldName: string,
		originalValue: any,
	) => any;
}

// 格式化日期显示
const formatDetailDate = (date: Date): string => {
	try {
		const localDate = new Date(
			date.getTime() - date.getTimezoneOffset() * 60000,
		);
		return format(localDate, "PPP");
	} catch (error) {
		console.error("日期格式化错误:", error);
		return "日期格式错误";
	}
};

const SortablePointItem = forwardRef<HTMLDivElement, SortablePointItemProps>(
	(
		{
			point,
			isActive,
			isHighlighted,
			onCopy,
			onDelete,
			onSelect,
			onImagePreview,
			getIconBackgroundColor,
			renderPointIcon,
			isEditing = false,
			editingFields = new Set(),
			onEnterEditMode,
			onExitEditMode,
			onFieldUpdate,
			onLocationUpdate,
			onImagesUpdate,
			getFieldValue,
		},
		ref,
	) => {
		const {
			attributes,
			listeners,
			setNodeRef,
			transform,
			transition,
			isDragging,
		} = useSortable({ id: point.id });

		const style = {
			transform: CSS.Transform.toString(transform),
			transition,
			zIndex: isDragging ? 50 : 1,
			opacity: isDragging ? 0.8 : 1,
		};

		// 处理点击删除按钮
		const handleDeleteClick = (e: React.MouseEvent) => {
			e.stopPropagation(); // 阻止事件冒泡，避免触发点位选择
			onDelete(point.id);
		};

		// 处理复制按钮点击
		const handleCopyClick = (e: React.MouseEvent) => {
			e.stopPropagation();
			if (onCopy) {
				onCopy(point);
			}
		};

		// 内联编辑的辅助函数
		const isFieldEditing = (fieldName: string) =>
			editingFields.has(fieldName);

		const enterEditMode = (fieldName: string) => {
			onEnterEditMode?.(point.id, fieldName);
		};

		const exitEditMode = (fieldName: string, saveChanges: boolean) => {
			onExitEditMode?.(point.id, fieldName, saveChanges);
		};

		const updateField = (fieldName: string, value: any) => {
			onFieldUpdate?.(point.id, fieldName, value);
		};

		const updateLocationField = (
			location: string,
			coordinates?: { lat: number; lng: number },
		) => {
			onLocationUpdate?.(point.id, location, coordinates);
		};

		const updateImagesField = (images: string[]) => {
			// 将字符串数组转换为 TravelPointImage 数组格式
			const travelPointImages = images.map((url, index) => ({
				url,
				alt: undefined,
				caption: undefined,
				description: undefined,
				uploadedAt: new Date().toISOString(),
			}));
			onImagesUpdate?.(point.id, travelPointImages);
		};

		const getCurrentValue = (fieldName: string, originalValue: any) => {
			return (
				getFieldValue?.(point.id, fieldName, originalValue) ??
				originalValue
			);
		};

		// 处理卡片点击（只有在没有编辑模式时才选择）
		const handleCardClick = (e: React.MouseEvent) => {
			// 如果点击的是编辑控件，不触发选择
			if (
				(e.target as HTMLElement).closest(".edit-actions") ||
				(e.target as HTMLElement).closest("input") ||
				(e.target as HTMLElement).closest("textarea") ||
				(e.target as HTMLElement).closest('button[type="button"]')
			) {
				return;
			}

			// 如果当前有字段在编辑，也不触发选择
			if (isEditing) {
				return;
			}

			onSelect(point.id);
		};

		const handleCardKeyDown = (e: React.KeyboardEvent) => {
			// 如果在编辑模式，不处理卡片的键盘事件
			if (isEditing) {
				return;
			}

			if (e.key === "Enter" || e.key === " ") {
				e.preventDefault();
				onSelect(point.id);
			}
		};

		return (
			<div
				ref={setNodeRef}
				style={style}
				className={cn(
					"relative flex items-start bg-muted/20 p-4 rounded-lg border border-border transition-all duration-200 hover:shadow-md group",
					isDragging &&
						"shadow-xl ring-2 ring-travel-primary bg-muted/50",
					isActive &&
						"border-travel-primary bg-travel-primary/5 shadow-md",
					isHighlighted && "ring-2 ring-travel-primary animate-pulse",
					isEditing && "border-blue-300 bg-blue-50/30", // 编辑状态样式
				)}
				onClick={handleCardClick}
				onKeyDown={handleCardKeyDown}
				tabIndex={isEditing ? -1 : 0} // 编辑时移除tabIndex
			>
				{/* 拖拽句柄 */}
				<div
					className="absolute -left-1 top-1/2 transform -translate-y-1/2 cursor-grab opacity-80 hover:opacity-100 transition-opacity bg-muted/40 hover:bg-muted rounded-md border border-border/50 hover:border-travel-primary shadow-sm"
					{...attributes}
					{...listeners}
				>
					<GripVertical className="h-5 w-5 text-muted-foreground hover:text-foreground" />
				</div>

				{/* 图标 */}
				<div
					className={cn(
						"mr-4 w-10 h-10 rounded-full flex items-center justify-center text-white flex-shrink-0 mt-1 z-10 relative",
						getIconBackgroundColor(point.iconType),
					)}
				>
					{renderPointIcon(point.iconType)}
				</div>

				{/* 内容 */}
				<div className="flex-1 min-w-0">
					<div className="flex items-start justify-between flex-wrap sm:flex-nowrap">
						<div className="pr-2 mb-1 sm:mb-0 flex-1">
							{/* 可编辑的位置 */}
							<EditableLocation
								value={getCurrentValue(
									"location",
									point.location,
								)}
								isEditing={isFieldEditing("location")}
								onEnterEdit={() => enterEditMode("location")}
								onExitEdit={(save) =>
									exitEditMode("location", save)
								}
								onValueChange={(location, coordinates) =>
									updateLocationField(location, coordinates)
								}
								onIconTypeChange={(iconType) => {
									// 当地点类型自动识别出图标类型时，同步更新点位的图标类型
									updateField("iconType", iconType);
								}}
							/>

							{/* 可编辑的日期 */}
							<EditableDate
								value={getCurrentValue("date", point.date)}
								isEditing={isFieldEditing("date")}
								onEnterEdit={() => enterEditMode("date")}
								onExitEdit={(save) =>
									exitEditMode("date", save)
								}
								onValueChange={(value) =>
									updateField("date", value)
								}
							/>
						</div>

						{/* 操作按钮 - 只在非编辑模式下显示 */}
						{!isEditing && (
							<div className="flex items-center gap-1 ml-auto sm:ml-0">
								{onCopy && (
									<Button
										variant="ghost"
										size="icon"
										onClick={handleCopyClick}
										className="h-8 w-8 text-muted-foreground hover:text-foreground"
										title="复制点位"
									>
										<Copy className="h-4 w-4" />
									</Button>
								)}
								<Button
									variant="ghost"
									size="icon"
									onClick={handleDeleteClick}
									className="h-8 w-8 text-muted-foreground hover:text-destructive"
									title="删除点位"
								>
									<Trash2 className="h-4 w-4" />
								</Button>
							</div>
						)}
					</div>

					{/* 可编辑的描述 */}
					<EditableDescription
						value={getCurrentValue(
							"description",
							point.description,
						)}
						isEditing={isFieldEditing("description")}
						onEnterEdit={() => enterEditMode("description")}
						onExitEdit={(save) => exitEditMode("description", save)}
						onValueChange={(value) =>
							updateField("description", value)
						}
					/>

					{/* 可编辑的图片 */}
					<EditableImages
						value={getCurrentValue(
							"images",
							point.images.map((img) => img.url),
						)}
						isEditing={isFieldEditing("images")}
						onEnterEdit={() => enterEditMode("images")}
						onExitEdit={(save) => exitEditMode("images", save)}
						onValueChange={updateImagesField}
						onImagePreview={(images, index) => {
							// 创建临时的 TravelPoint 对象用于图片预览
							const tempPoint = {
								...point,
								images: images.map((url, idx) => ({
									url,
									id: `temp-${idx}`,
								})),
							};
							onImagePreview(tempPoint, index);
						}}
					/>
				</div>
			</div>
		);
	},
);

SortablePointItem.displayName = "SortablePointItem";

export default SortablePointItem;
export type { TravelPoint };
