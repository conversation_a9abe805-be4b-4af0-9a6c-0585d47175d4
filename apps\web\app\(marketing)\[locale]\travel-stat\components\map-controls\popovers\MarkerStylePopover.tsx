"use client";

import { Bad<PERSON> } from "@ui/components/badge";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Switch } from "@ui/components/switch";
import { Paintbrush, Sparkles } from "lucide-react";
import { useTranslatedMarkerStyles } from "../../../hooks/useTranslatedMarkerStyles";
import type { MarkerStyleType } from "../../../types/markerTypes";
import {
	GRADIENT_PULSE_THEMES,
	HAND_DRAWN_THEMES,
	MARKER_STYLE_CONFIGS,
	POLAROID_THEMES,
} from "../../../types/markerTypes";
import { PopoverContainer } from "./PopoverContainer";

// 粒子特效主题配置（内联定义避免导入问题）
const PARTICLE_EFFECT_THEMES = {
	fire: {
		particleColor: "rgb(251, 146, 60)", // orange-400
		particleCount: 8,
		animationSpeed: 1.2,
		orbitRadius: 35,
		name: "火焰",
		description: "热情奔放",
	},
	electric: {
		particleColor: "rgb(59, 130, 246)", // blue-500
		particleCount: 10,
		animationSpeed: 1.5,
		orbitRadius: 40,
		name: "电光",
		description: "动感炫酷",
	},
	magic: {
		particleColor: "rgb(168, 85, 247)", // purple-500
		particleCount: 12,
		animationSpeed: 0.8,
		orbitRadius: 45,
		name: "魔法",
		description: "神秘优雅",
	},
	nature: {
		particleColor: "rgb(34, 197, 94)", // green-500
		particleCount: 6,
		animationSpeed: 1.0,
		orbitRadius: 30,
		name: "自然",
		description: "清新宁静",
	},
};

interface MarkerStylePopoverProps {
	currentStyle: MarkerStyleType;
	currentTheme?: string;
	currentEmoji?: string;
	currentEmojiColor?: string;
	hideOutline?: boolean;
	onStyleChange: (style: MarkerStyleType) => void;
	onThemeChange: (theme: string) => void;
	onEmojiChange?: (emoji: string) => void;
	onEmojiColorChange?: (color: string) => void;
	onHideOutlineChange?: (hide: boolean) => void;
}

export function MarkerStylePopover({
	currentStyle,
	currentTheme = "ocean",
	currentEmoji = "📍",
	currentEmojiColor = "transparent",
	hideOutline = false,
	onStyleChange,
	onThemeChange,
	onEmojiChange,
	onEmojiColorChange,
	onHideOutlineChange,
}: MarkerStylePopoverProps) {
	const { t, getMarkerStyleName, getMarkerStyleDescription } =
		useTranslatedMarkerStyles();

	// 只保留已实现的风格
	const implementedStyles = [
		MARKER_STYLE_CONFIGS.classic,
		MARKER_STYLE_CONFIGS["gradient-pulse"],
		MARKER_STYLE_CONFIGS["particle-effect"],
		MARKER_STYLE_CONFIGS["hand-drawn"],
		MARKER_STYLE_CONFIGS.emoji,
		MARKER_STYLE_CONFIGS.polaroid,
	];

	return (
		<PopoverContainer>
			{/* 标题区域 */}
			<div className="flex items-center justify-between mb-4">
				<div className="flex items-center gap-2">
					<Sparkles className="w-4 h-4 text-purple-600" />
					<h3 className="font-semibold text-gray-900">
						{t("popover.title")}
					</h3>
				</div>
			</div>

			{/* 外层轮廓控制 */}
			<div className="mb-4 p-3 bg-gradient-to-r from-sky-50 to-blue-50 rounded-lg border border-sky-200">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-2">
						<Sparkles className="w-4 h-4 text-sky-500" />
						<span className="text-sm font-medium text-sky-800">
							{t("popover.outlineControl")}
						</span>
					</div>
					<div className="flex items-center gap-2">
						<Switch
							checked={!hideOutline}
							onCheckedChange={(checked) =>
								onHideOutlineChange?.(!checked)
							}
							className="data-[state=checked]:bg-sky-500"
						/>
						<Button
							onClick={() => onHideOutlineChange?.(!hideOutline)}
							variant="ghost"
							size="sm"
							className="text-xs text-sky-600 hover:text-sky-800 hover:bg-sky-100 px-2 py-1 h-auto"
						>
							{hideOutline
								? t("popover.hideOutline")
								: t("popover.showOutline")}
						</Button>
					</div>
				</div>
			</div>

			{/* 样式选择网格 */}
			<div className="grid grid-cols-2 gap-2 mb-4">
				{implementedStyles.map((style) => {
					const config =
						MARKER_STYLE_CONFIGS[style.id as MarkerStyleType];
					const isActive = style.id === currentStyle;
					const translatedName = getMarkerStyleName(
						style.id as MarkerStyleType,
					);
					const translatedDescription = getMarkerStyleDescription(
						style.id as MarkerStyleType,
					);

					return (
						<button
							key={style.id}
							type="button"
							onClick={(e) => {
								e.stopPropagation();
								onStyleChange(style.id as MarkerStyleType);
							}}
							className={`p-3 rounded-lg border-2 transition-all duration-200 text-left ${
								isActive
									? "border-sky-400 bg-sky-50 shadow-md"
									: "border-gray-200 hover:border-sky-300 hover:bg-sky-25"
							}`}
						>
							<div className="flex items-start justify-between mb-2">
								<div className="flex items-center gap-2">
									<span className="text-base">
										{config.preview}
									</span>
									<span
										className={`text-sm font-medium ${
											isActive
												? "text-sky-800"
												: "text-gray-700"
										}`}
									>
										{translatedName}
									</span>
									{isActive && (
										<div className="w-2 h-2 bg-sky-500 rounded-full" />
									)}
								</div>
								{config.isPremium && (
									<Badge status="warning" className="text-xs">
										👑
									</Badge>
								)}
							</div>
							<p
								className={`text-xs ${
									isActive ? "text-sky-600" : "text-gray-500"
								}`}
							>
								{translatedDescription}
							</p>
						</button>
					);
				})}
			</div>

			{/* 渐变光晕主题选择器 */}
			{currentStyle === "gradient-pulse" && (
				<div className="mt-6 pt-4 border-t border-gray-200">
					<div className="flex items-center gap-2 mb-3">
						<Paintbrush className="w-4 h-4 text-purple-600" />
						<h4 className="text-sm font-medium text-gray-700">
							{t("popover.gradientPulseThemes")}
						</h4>
					</div>

					<div className="grid grid-cols-3 gap-2">
						{Object.entries(GRADIENT_PULSE_THEMES).map(
							([themeKey, themeConfig]) => {
								const isActive = themeKey === currentTheme;
								// 使用类型安全的方式访问嵌套翻译
								const themeName = (() => {
									try {
										return t(
											`popover.themes.gradient.${themeKey}.name` as any,
										);
									} catch {
										return themeKey;
									}
								})();
								const themeDescription = (() => {
									try {
										return t(
											`popover.themes.gradient.${themeKey}.description` as any,
										);
									} catch {
										return "";
									}
								})();

								return (
									<button
										key={themeKey}
										type="button"
										onClick={() => {
											onThemeChange(themeKey);
										}}
										className={`relative p-3 rounded-lg border-2 transition-all duration-200 text-left ${
											isActive
												? "border-purple-400 bg-purple-50 shadow-md"
												: "border-gray-200 hover:border-purple-300 hover:bg-purple-25"
										}`}
									>
										{/* 激活指示器 */}
										{isActive && (
											<div className="absolute top-1 right-1 w-2 h-2 bg-purple-500 rounded-full" />
										)}

										{/* 渐变预览 */}
										<div
											className="w-full h-8 rounded-md mb-2 border border-white/50"
											style={{
												background: `linear-gradient(135deg, ${themeConfig.primaryColor}, ${themeConfig.secondaryColor})`,
											}}
										/>

										<div className="text-center">
											<span
												className={`text-xs font-medium block ${isActive ? "text-purple-800" : "text-gray-700"}`}
											>
												{themeName}
											</span>
											<div
												className={`text-xs mt-1 ${isActive ? "text-purple-600" : "text-gray-500"}`}
											>
												{themeDescription}
											</div>
										</div>
									</button>
								);
							},
						)}
					</div>
				</div>
			)}

			{/* 粒子特效主题选择器 */}
			{currentStyle === "particle-effect" && (
				<div className="mt-6 pt-4 border-t border-gray-200">
					<div className="flex items-center gap-2 mb-3">
						<Paintbrush className="w-4 h-4 text-orange-600" />
						<h4 className="text-sm font-medium text-gray-700">
							{t("popover.particleEffectThemes")}
						</h4>
					</div>

					<div className="grid grid-cols-2 gap-3">
						{Object.entries(PARTICLE_EFFECT_THEMES).map(
							([themeKey, themeConfig]) => {
								const isActive = themeKey === currentTheme;
								// 使用类型安全的方式访问嵌套翻译
								const themeName = (() => {
									try {
										return t(
											`popover.themes.particle.${themeKey}.name` as any,
										);
									} catch {
										return themeConfig.name || themeKey;
									}
								})();
								const themeDescription = (() => {
									try {
										return t(
											`popover.themes.particle.${themeKey}.description` as any,
										);
									} catch {
										return themeConfig.description || "";
									}
								})();

								return (
									<button
										key={themeKey}
										type="button"
										onClick={() => {
											onThemeChange(themeKey);
										}}
										className={`relative p-3 rounded-lg border-2 transition-all duration-200 text-left ${
											isActive
												? "border-orange-400 bg-orange-50 shadow-md"
												: "border-gray-200 hover:border-orange-300 hover:bg-orange-25"
										}`}
									>
										{/* 激活指示器 */}
										{isActive && (
											<div className="absolute top-1 right-1 w-2 h-2 bg-orange-500 rounded-full" />
										)}

										{/* 粒子效果预览 */}
										<div className="flex items-center justify-center h-8 mb-2 relative">
											{/* 中心点 */}
											<div
												className="w-3 h-3 rounded-full"
												style={{
													backgroundColor:
														themeConfig.particleColor,
												}}
											/>
											{/* 周围的小粒子 */}
											{Array.from(
												{ length: 4 },
												(_, i) => (
													<div
														key={i}
														className="absolute w-1 h-1 rounded-full opacity-70"
														style={{
															backgroundColor:
																themeConfig.particleColor,
															transform: `rotate(${i * 90}deg) translateX(12px)`,
														}}
													/>
												),
											)}
										</div>

										<div className="text-center">
											<span
												className={`text-xs font-medium block ${isActive ? "text-orange-800" : "text-gray-700"}`}
											>
												{themeName}
											</span>
											<div
												className={`text-xs mt-1 ${isActive ? "text-orange-600" : "text-gray-500"}`}
											>
												{themeDescription}
											</div>
											<div
												className={`text-xs mt-1 ${isActive ? "text-orange-500" : "text-gray-400"}`}
											>
												{t("popover.particleCount", {
													count: themeConfig.particleCount,
												})}
											</div>
										</div>
									</button>
								);
							},
						)}
					</div>
				</div>
			)}

			{/* 手绘素描主题选择器 */}
			{currentStyle === "hand-drawn" && (
				<div className="mt-6 pt-4 border-t border-gray-200">
					<div className="flex items-center gap-2 mb-3">
						<Paintbrush className="w-4 h-4 text-slate-600" />
						<h4 className="text-sm font-medium text-gray-700">
							{t("popover.handDrawnThemes")}
						</h4>
					</div>

					<div className="grid grid-cols-2 gap-3">
						{Object.entries(HAND_DRAWN_THEMES).map(
							([themeKey, themeConfig]) => {
								const isActive = themeKey === currentTheme;
								// 使用类型安全的方式访问嵌套翻译
								const themeName = (() => {
									try {
										return t(
											`popover.themes.handDrawn.${themeKey}.name` as any,
										);
									} catch {
										return themeConfig.name || themeKey;
									}
								})();
								const themeDescription = (() => {
									try {
										return t(
											`popover.themes.handDrawn.${themeKey}.description` as any,
										);
									} catch {
										return themeConfig.description || "";
									}
								})();

								return (
									<button
										key={themeKey}
										type="button"
										onClick={() => {
											onThemeChange(themeKey);
										}}
										className={`relative p-3 rounded-lg border-2 transition-all duration-200 text-left ${
											isActive
												? "border-slate-400 bg-slate-50 shadow-md"
												: "border-gray-200 hover:border-slate-300 hover:bg-slate-25"
										}`}
									>
										{/* 激活指示器 */}
										{isActive && (
											<div className="absolute top-1 right-1 w-2 h-2 bg-slate-500 rounded-full" />
										)}

										{/* 手绘笔触预览 */}
										<div className="flex items-center justify-center h-8 mb-2">
											<svg
												width="24"
												height="24"
												viewBox="0 0 24 24"
												aria-label="手绘笔触预览"
											>
												<title>手绘笔触预览</title>
												<circle
													cx="12"
													cy="12"
													r="8"
													fill="none"
													stroke={themeConfig.color}
													strokeWidth={
														themeConfig.strokeWidth
													}
													opacity={
														themeConfig.opacity
													}
													strokeLinecap="round"
													style={{
														filter: `blur(${themeConfig.roughness * 0.2}px)`,
													}}
												/>
											</svg>
										</div>

										<div className="text-center">
											<span
												className={`text-xs font-medium block ${isActive ? "text-slate-800" : "text-gray-700"}`}
											>
												{themeName}
											</span>
											<div
												className={`text-xs mt-1 ${isActive ? "text-slate-600" : "text-gray-500"}`}
											>
												{themeDescription}
											</div>
										</div>
									</button>
								);
							},
						)}
					</div>
				</div>
			)}

			{/* 拍立得主题选择器 */}
			{currentStyle === "polaroid" && (
				<div className="mt-6 pt-4 border-t border-gray-200">
					<div className="flex items-center gap-2 mb-3">
						<Paintbrush className="w-4 h-4 text-pink-600" />
						<h4 className="text-sm font-medium text-gray-700">
							拍立得主题
						</h4>
					</div>

					<div className="grid grid-cols-2 gap-3">
						{Object.entries(POLAROID_THEMES).map(
							([themeKey, themeConfig]) => {
								const isActive = themeKey === currentTheme;

								return (
									<button
										key={themeKey}
										type="button"
										onClick={() => {
											onThemeChange(themeKey);
										}}
										className={`relative p-3 rounded-lg border-2 transition-all duration-200 text-left ${
											isActive
												? "border-pink-400 bg-pink-50 shadow-md"
												: "border-gray-200 hover:border-pink-300 hover:bg-pink-25"
										}`}
									>
										{/* 激活指示器 */}
										{isActive && (
											<div className="absolute top-1 right-1 w-2 h-2 bg-pink-500 rounded-full" />
										)}

										{/* 拍立得相框预览 */}
										<div className="flex items-center justify-center h-8 mb-2">
											<div
												className="w-6 h-8 rounded-sm border border-gray-300 relative overflow-hidden"
												style={{
													backgroundColor:
														themeConfig.frameColor,
													borderWidth: `${themeConfig.borderWidth / 6}px`,
													borderBottomWidth: `${(themeConfig.borderWidth * themeConfig.bottomBorderRatio) / 6}px`,
													boxShadow: `2px 2px 4px ${themeConfig.shadowColor}`,
													transform: `rotate(${themeConfig.rotation / 2}deg)`,
												}}
											>
												{/* 模拟照片区域 */}
												<div className="w-full h-3/4 bg-gray-300" />
											</div>
										</div>

										<div className="text-center">
											<span
												className={`text-xs font-medium block ${isActive ? "text-pink-800" : "text-gray-700"}`}
											>
												{themeConfig.name}
											</span>
											<div
												className={`text-xs mt-1 ${isActive ? "text-pink-600" : "text-gray-500"}`}
											>
												{themeConfig.description}
											</div>
										</div>
									</button>
								);
							},
						)}
					</div>

					{/* 拍立得样式说明 */}
					<div className="mt-4 p-3 bg-gradient-to-r from-pink-50 to-rose-50 rounded-lg border border-pink-200">
						<div className="flex items-center gap-2 mb-2">
							<span className="text-lg">📸</span>
							<span className="text-sm font-medium text-pink-800">
								拍立得样式说明
							</span>
						</div>
						<p className="text-xs text-pink-600 mb-2">
							拍立得样式会显示上传的图片，并以复古拍立得相片的形式呈现，包含相框、阴影和轻微旋转效果。
						</p>
						<div className="text-xs text-pink-500">
							💡 最适合展示有图片的旅行点位
						</div>
					</div>
				</div>
			)}

			{/* Emoji风格选择器 */}
			{currentStyle === "emoji" && (
				<div className="mt-6 pt-4 border-t border-gray-200">
					<div className="flex items-center gap-2 mb-3">
						<Paintbrush className="w-4 h-4 text-amber-600" />
						<h4 className="text-sm font-medium text-gray-700">
							表情符号
						</h4>
					</div>

					{/* Emoji选择网格 */}
					<div className="grid grid-cols-6 gap-2 mb-4">
						{[
							// 旅游相关 emoji (前排)
							"📍",
							"🗺️",
							"✈️",
							"🚗",
							"🚢",
							"🚂",
							"🏖️",
							"🏔️",
							"🏛️",
							"🏰",
							"🗽",
							"🎡",
							"🎢",
							"🎠",
							"🌅",
							"🌄",
							"🌊",
							"🏝️",
							"🏕️",
							"⛰️",
							"🗻",
							"🌋",
							"🏞️",
							"🌉",
							"🎯",
							"🧭",
							"📸",
							"🎒",
							"🧳",
							"🎫",
							// 表情符号
							"😊",
							"😍",
							"🤗",
							"😎",
							"🥰",
							"🤩",
							// 活动相关
							"🍕",
							"☕",
							"🍰",
							"🍜",
							"🍻",
							"🥂",
							"🎵",
							"🎮",
							"📚",
							"💼",
							"🎈",
							"⭐",
							"💎",
							"🔥",
							"💚",
							"❤️",
							"💙",
							"💜",
							"🧡",
						].map((emoji) => {
							const isActive = emoji === currentEmoji;
							return (
								<button
									key={emoji}
									type="button"
									onClick={() => {
										if (onEmojiChange) {
											onEmojiChange(emoji);
										}
									}}
									className={`w-8 h-8 rounded-lg border-2 transition-all duration-200 flex items-center justify-center text-lg hover:scale-110 ${
										isActive
											? "border-amber-400 bg-amber-50 shadow-md"
											: "border-gray-200 hover:border-amber-300 hover:bg-amber-25"
									}`}
								>
									{emoji}
								</button>
							);
						})}
					</div>

					{/* 颜色选择 */}
					<div className="mb-3">
						<h5 className="text-xs font-medium text-gray-600 mb-2">
							背景颜色
						</h5>
						<div className="grid grid-cols-8 gap-2">
							{/* 透明背景选项 */}
							{(() => {
								const isActive =
									currentEmojiColor === "transparent";
								return (
									<button
										key="transparent"
										type="button"
										onClick={() => {
											if (onEmojiColorChange) {
												onEmojiColorChange(
													"transparent",
												);
											}
										}}
										className={`w-6 h-6 rounded-full border-2 border-gray-300 transition-all duration-200 hover:scale-110 relative bg-white ${
											isActive
												? "ring-2 ring-amber-400 ring-offset-1"
												: ""
										}`}
										title="透明背景"
									>
										{/* 斜线表示透明 */}
										<div className="absolute inset-0 flex items-center justify-center">
											<div className="w-4 h-0.5 bg-red-500 rotate-45 rounded-full" />
										</div>
									</button>
								);
							})()}

							{/* 颜色选项 */}
							{[
								"#3b82f6", // blue
								"#ef4444", // red
								"#10b981", // emerald
								"#f59e0b", // amber
								"#8b5cf6", // violet
								"#ec4899", // pink
								"#06b6d4", // cyan
								"#84cc16", // lime
								"#f97316", // orange
								"#6366f1", // indigo
								"#14b8a6", // teal
								"#a855f7", // purple
								"#64748b", // slate
								"#dc2626", // red-600
								"#059669", // emerald-600
								"#d97706", // amber-600
							].map((color) => {
								const isActive = color === currentEmojiColor;
								return (
									<button
										key={color}
										type="button"
										onClick={() => {
											if (onEmojiColorChange) {
												onEmojiColorChange(color);
											}
										}}
										className={`w-6 h-6 rounded-full transition-all duration-200 hover:scale-110 ${
											isActive
												? "ring-2 ring-gray-400 ring-offset-1"
												: ""
										}`}
										style={{ backgroundColor: color }}
										title={`颜色: ${color}`}
									/>
								);
							})}
						</div>
					</div>

					{/* 预览效果 */}
					<div className="bg-gradient-to-r from-amber-50 to-yellow-50 rounded-lg p-3 border border-amber-200">
						<div className="flex items-center gap-3">
							<div className="text-sm font-medium text-amber-800">
								预览效果：
							</div>
							<div
								className={`w-8 h-8 rounded-full border-2 shadow-lg flex items-center justify-center text-lg ${
									currentEmojiColor === "transparent"
										? "border-gray-300 bg-transparent"
										: "border-white"
								}`}
								style={{
									backgroundColor:
										currentEmojiColor === "transparent"
											? "transparent"
											: currentEmojiColor,
								}}
							>
								{currentEmoji}
							</div>
							{currentEmojiColor === "transparent" && (
								<div className="text-xs text-amber-600">
									无背景
								</div>
							)}
						</div>
					</div>
				</div>
			)}

			{/* 底部提示 */}
			<div className="mt-4 pt-3 border-t border-gray-200">
				<p className="text-xs text-gray-500 text-center">
					💡 更多风格和主题正在开发中，敬请期待
				</p>
			</div>
		</PopoverContainer>
	);
}
