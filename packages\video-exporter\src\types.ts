// 导出任务类型
export interface JobData {
	// 任务ID
	taskId: string;
	// 日记ID
	diaryId: string;
	// 用户ID
	userId: string;
	// 导出选项
	options: ExportOptions;
}

// 导出选项
export interface ExportOptions {
	// 视频分辨率: "720p", "1080p" 等
	resolution: string;
	// 视频帧率
	fps: number;
	// 每个点停留的秒数
	pointDuration: number;
}

// 处理结果
export interface ProcessResult {
	// 完成生成的视频URL
	videoUrl: string;
	// 处理用时(毫秒)
	processingTime: number;
}

// 任务状态类型
export type TaskStatus = "pending" | "processing" | "completed" | "failed";
