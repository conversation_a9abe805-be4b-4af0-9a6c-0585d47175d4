-- CreateTable
CREATE TABLE "products" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "products_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "feature_requests" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'under_consideration',
    "voteCount" INTEGER NOT NULL DEFAULT 0,
    "productId" TEXT NOT NULL,
    "userId" TEXT,
    "authorName" TEXT,
    "authorEmail" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "feature_requests_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "feature_votes" (
    "id" TEXT NOT NULL,
    "featureRequestId" TEXT NOT NULL,
    "userId" TEXT,
    "anonymousId" TEXT,
    "voterName" TEXT,
    "voterEmail" TEXT,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "feature_votes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "feature_comments" (
    "id" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "featureRequestId" TEXT NOT NULL,
    "userId" TEXT,
    "anonymousId" TEXT,
    "authorName" TEXT,
    "authorEmail" TEXT,
    "ipAddress" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "feature_comments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "feature_subscriptions" (
    "id" TEXT NOT NULL,
    "featureRequestId" TEXT NOT NULL,
    "userId" TEXT,
    "email" TEXT,
    "notifyOnStatusChange" BOOLEAN NOT NULL DEFAULT true,
    "notifyOnComments" BOOLEAN NOT NULL DEFAULT false,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "feature_subscriptions_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "products_name_key" ON "products"("name");

-- CreateIndex
CREATE INDEX "feature_requests_productId_status_idx" ON "feature_requests"("productId", "status");

-- CreateIndex
CREATE INDEX "feature_votes_featureRequestId_idx" ON "feature_votes"("featureRequestId");

-- CreateIndex
CREATE INDEX "feature_votes_userId_idx" ON "feature_votes"("userId");

-- CreateIndex
CREATE INDEX "feature_votes_anonymousId_idx" ON "feature_votes"("anonymousId");

-- CreateIndex
CREATE INDEX "feature_votes_ipAddress_idx" ON "feature_votes"("ipAddress");

-- CreateIndex
CREATE UNIQUE INDEX "feature_votes_featureRequestId_userId_key" ON "feature_votes"("featureRequestId", "userId");

-- CreateIndex
CREATE UNIQUE INDEX "feature_votes_featureRequestId_anonymousId_key" ON "feature_votes"("featureRequestId", "anonymousId");

-- CreateIndex
CREATE INDEX "feature_comments_featureRequestId_idx" ON "feature_comments"("featureRequestId");

-- CreateIndex
CREATE INDEX "feature_comments_userId_idx" ON "feature_comments"("userId");

-- CreateIndex
CREATE INDEX "feature_comments_anonymousId_idx" ON "feature_comments"("anonymousId");

-- CreateIndex
CREATE INDEX "feature_subscriptions_email_idx" ON "feature_subscriptions"("email");

-- CreateIndex
CREATE UNIQUE INDEX "feature_subscriptions_featureRequestId_userId_key" ON "feature_subscriptions"("featureRequestId", "userId");

-- CreateIndex
CREATE UNIQUE INDEX "feature_subscriptions_featureRequestId_email_key" ON "feature_subscriptions"("featureRequestId", "email");

-- AddForeignKey
ALTER TABLE "feature_requests" ADD CONSTRAINT "feature_requests_productId_fkey" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feature_requests" ADD CONSTRAINT "feature_requests_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feature_votes" ADD CONSTRAINT "feature_votes_featureRequestId_fkey" FOREIGN KEY ("featureRequestId") REFERENCES "feature_requests"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feature_votes" ADD CONSTRAINT "feature_votes_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feature_comments" ADD CONSTRAINT "feature_comments_featureRequestId_fkey" FOREIGN KEY ("featureRequestId") REFERENCES "feature_requests"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feature_comments" ADD CONSTRAINT "feature_comments_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feature_subscriptions" ADD CONSTRAINT "feature_subscriptions_featureRequestId_fkey" FOREIGN KEY ("featureRequestId") REFERENCES "feature_requests"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "feature_subscriptions" ADD CONSTRAINT "feature_subscriptions_userId_fkey" FOREIGN KEY ("userId") REFERENCES "user"("id") ON DELETE CASCADE ON UPDATE CASCADE;
