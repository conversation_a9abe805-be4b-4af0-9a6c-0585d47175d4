import { COLOR_THEMES_BASE } from "../constants/colorThemes";
import type { ColorTheme, ColorThemeType } from "../types/colorTypes";

// 翻译函数类型
type ColorThemeTranslations = {
	getName: (themeId: string) => string;
	getDescription: (themeId: string) => string;
	levelDescriptions: {
		unvisited: () => string;
		firstVisit: () => string;
		secondVisit: () => string;
		multipleVisits: () => string;
		frequent: () => string;
		veryFrequent: () => string;
		extremelyFrequent: () => string;
		superFrequent: () => string;
	};
};

// 获取翻译后的颜色主题
export function getTranslatedColorTheme(
	themeId: ColorThemeType,
	translations: ColorThemeTranslations,
): ColorTheme {
	const baseTheme = COLOR_THEMES_BASE[themeId];
	if (!baseTheme) {
		throw new Error(`Color theme ${themeId} not found`);
	}

	return {
		...baseTheme,
		name: translations.getName(themeId),
		description: translations.getDescription(themeId),
		colors: {
			unvisited: {
				...baseTheme.colors.unvisited,
				description: translations.levelDescriptions.unvisited(),
			},
			level1: {
				...baseTheme.colors.level1,
				description: translations.levelDescriptions.firstVisit(),
			},
			level2: {
				...baseTheme.colors.level2,
				description: translations.levelDescriptions.secondVisit(),
			},
			level3: {
				...baseTheme.colors.level3,
				description: translations.levelDescriptions.multipleVisits(),
			},
			level4: {
				...baseTheme.colors.level4,
				description: translations.levelDescriptions.frequent(),
			},
			level5: {
				...baseTheme.colors.level5,
				description: translations.levelDescriptions.veryFrequent(),
			},
			level6to10: {
				...baseTheme.colors.level6to10,
				description: translations.levelDescriptions.extremelyFrequent(),
			},
			level10plus: {
				...baseTheme.colors.level10plus,
				description: translations.levelDescriptions.superFrequent(),
			},
		},
	};
}

// 获取所有翻译后的颜色主题
export function getAllTranslatedColorThemes(
	translations: ColorThemeTranslations,
): Record<ColorThemeType, ColorTheme> {
	const themes: Record<ColorThemeType, ColorTheme> = {} as any;

	for (const themeId of Object.keys(COLOR_THEMES_BASE) as ColorThemeType[]) {
		themes[themeId] = getTranslatedColorTheme(themeId, translations);
	}

	return themes;
}
