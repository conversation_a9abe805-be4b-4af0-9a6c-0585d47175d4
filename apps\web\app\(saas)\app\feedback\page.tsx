"use client";

import { FeatureVotingBoard } from "@repo/utils/lib/feature-voting";
import { Alert, AlertDescription } from "@ui/components/alert";
import { <PERSON><PERSON> } from "@ui/components/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { AlertCircle, Loader2, RefreshCw } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface Product {
	id: string;
	name: string;
	description?: string;
}

export default function FeedbackPage() {
	const [products, setProducts] = useState<Product[]>([]);
	const [selectedProduct, setSelectedProduct] = useState<string>("");
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	// 加载产品列表
	const loadProducts = async () => {
		setIsLoading(true);
		setError(null);

		try {
			const response = await fetch("/api/products");
			if (response.ok) {
				const data = await response.json();
				if (data.success && data.data) {
					const productList = Array.isArray(data.data)
						? data.data
						: [];
					setProducts(productList);

					// 自动选择第一个产品
					if (productList.length > 0 && !selectedProduct) {
						setSelectedProduct(productList[0].id);
					}
				} else {
					setProducts([]);
				}
			} else {
				throw new Error("加载产品列表失败");
			}
		} catch (err: any) {
			console.error("加载产品失败:", err);
			setError(err?.message || "未知错误");
			toast.error("加载产品失败，请稍后重试");
		} finally {
			setIsLoading(false);
		}
	};

	// 初次加载
	useEffect(() => {
		loadProducts();
	}, []);

	if (isLoading) {
		return (
			<div className="container mx-auto py-8 px-4">
				<div className="flex items-center justify-center h-64">
					<Loader2 className="size-8 animate-spin" />
					<span className="ml-2">加载中...</span>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="container mx-auto py-8 px-4">
				<Alert>
					<AlertCircle className="size-4" />
					<AlertDescription>{error}</AlertDescription>
				</Alert>
				<div className="mt-4">
					<Button onClick={loadProducts}>
						<RefreshCw className="size-4 mr-2" />
						重试
					</Button>
				</div>
			</div>
		);
	}

	if (products.length === 0) {
		return (
			<div className="container mx-auto py-8 px-4">
				<Card>
					<CardHeader>
						<CardTitle>暂无可用产品</CardTitle>
					</CardHeader>
					<CardContent>
						<p className="text-muted-foreground">
							当前没有可用的产品，请联系管理员。
						</p>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="container mx-auto py-8 px-4">
			{/* 页面头部 */}
			<div className="mb-8">
				<div className="flex items-center justify-between">
					<div>
						<h1 className="text-3xl font-bold text-foreground mb-2">
							产品反馈
						</h1>
						<p className="text-muted-foreground">
							查看功能建议，为您喜欢的功能投票，或提交新的想法
						</p>
					</div>

					{/* 产品选择器 */}
					{products.length > 1 && (
						<div className="w-64">
							<Select
								value={selectedProduct}
								onValueChange={setSelectedProduct}
							>
								<SelectTrigger>
									<SelectValue placeholder="选择产品" />
								</SelectTrigger>
								<SelectContent>
									{products.map((product) => (
										<SelectItem
											key={product.id}
											value={product.id}
										>
											{product.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
					)}
				</div>
			</div>

			{/* 功能投票模块 */}
			<div>
				{selectedProduct ? (
					<FeatureVotingBoard
						allowAnonymousVoting={true}
						allowAnonymousSubmissions={true}
						showVoteCounts={true}
						defaultQuery={{
							productId: selectedProduct,
						}}
						className="w-full"
					/>
				) : (
					<Card>
						<CardContent className="pt-6">
							<p className="text-center text-muted-foreground">
								请选择一个产品来查看相关的功能建议
							</p>
						</CardContent>
					</Card>
				)}
			</div>
		</div>
	);
}
