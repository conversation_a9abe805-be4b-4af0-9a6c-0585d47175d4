"use client";

import {
	NodeViewContent,
	type <PERSON>de<PERSON><PERSON>wP<PERSON>,
	NodeViewWrapper,
} from "@tiptap/react";
import { Button } from "@ui/components/button";
import { Calendar as CalendarComponent } from "@ui/components/calendar";
import { Input } from "@ui/components/input";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import {
	CalendarDays,
	Copy,
	GripVertical,
	Plus,
	Settings,
	Trash2,
} from "lucide-react";
import React, { useState, useEffect } from "react";
import { v4 as uuidv4 } from "uuid";

export const TravelTimelineNodeView: React.FC<NodeViewProps> = ({
	node,
	updateAttributes,
	editor,
	getPos,
}) => {
	console.log("[DEBUG] TravelTimelineNodeView 渲染", {
		timelineId: node.attrs.timelineId,
		title: node.attrs.title,
		timestamp: new Date().toISOString(),
		editorDestroyed: editor.isDestroyed,
		nodeSize: node.nodeSize,
		childCount: node.content?.childCount || 0,
	});

	const timelineId = node.attrs.timelineId || null;
	const title = node.attrs.title || "";
	const timelineDate = node.attrs.timelineDate || "";

	const [date, setDate] = useState<Date | undefined>(
		timelineDate ? new Date(timelineDate) : new Date(),
	);
	const [isCalendarOpen, setIsCalendarOpen] = useState(false);

	// 组件挂载时的调试日志
	useEffect(() => {
		console.log("[DEBUG] TravelTimelineNodeView 组件挂载", {
			timelineId,
			title,
			timestamp: new Date().toISOString(),
		});

		return () => {
			console.log("[DEBUG] TravelTimelineNodeView 组件卸载", {
				timelineId,
				title,
				timestamp: new Date().toISOString(),
			});
		};
	}, []);

	// 格式化日期显示
	const displayDate = formatDate(timelineDate);

	// 更新日期属性
	useEffect(() => {
		console.log("[DEBUG] TravelTimelineNodeView 日期更新", {
			timelineId,
			newDate: date?.toISOString(),
			timestamp: new Date().toISOString(),
		});

		if (date) {
			updateAttributes({ timelineDate: date.toISOString() });
		}
	}, [date, updateAttributes, timelineId]);

	const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		updateAttributes({ title: e.target.value });
	};

	// 删除当前时间线
	const handleDelete = () => {
		if (typeof getPos === "function") {
			const pos = getPos();
			editor
				.chain()
				.focus()
				.deleteRange({ from: pos, to: pos + node.nodeSize })
				.run();
		}
	};

	// 添加新的旅行点位
	const handleAddPoint = () => {
		// 使用insertContentAt命令，在当前节点内添加内容
		editor
			.chain()
			.focus()
			.command(({ commands, tr, state }) => {
				if (typeof getPos !== "function") return false;

				// 获取时间线节点的位置
				const timelinePos = getPos();
				const timelineNode = state.doc.nodeAt(timelinePos);

				if (!timelineNode) return false;

				// 创建新的点位节点
				const pointContent = {
					type: "travelPoint",
					attrs: {
						pointId: uuidv4(),
						location: "新地点",
						pointDate: new Date().toISOString(),
					},
					content: [
						{
							type: "paragraph",
						},
					],
				};

				// 在时间线节点末尾插入点位
				const insertPos = timelinePos + timelineNode.nodeSize - 2;

				// 使用commands API插入内容
				return commands.insertContentAt(insertPos, pointContent);
			})
			.run();
	};

	// 复制时间线
	const handleDuplicate = () => {
		if (typeof getPos === "function") {
			const pos = getPos();
			const duplicatedNode = {
				...node.toJSON(),
				attrs: {
					...node.attrs,
					timelineId: uuidv4(),
					title: `${node.attrs.title} (副本)`,
				},
			};

			// 为所有点位生成新的ID
			if (duplicatedNode.content) {
				duplicatedNode.content = duplicatedNode.content.map(
					(point: any) => ({
						...point,
						attrs: {
							...point.attrs,
							pointId: uuidv4(),
						},
					}),
				);
			}

			editor
				.chain()
				.focus()
				.insertContentAt(pos + node.nodeSize, duplicatedNode)
				.run();
		}
	};

	// 快速设置时间线属性
	const handleQuickSettings = () => {
		// 这里可以打开一个更详细的设置面板
		// 暂时使用简单的prompt
		const newTitle = window.prompt("修改时间线标题", title);
		if (newTitle !== null) {
			updateAttributes({ title: newTitle });
		}
	};

	return (
		<NodeViewWrapper className="travel-timeline-node my-6 border border-blue-300 rounded-lg bg-blue-50/70 overflow-hidden group hover:shadow-md transition-all duration-200">
			{/* 时间线标题栏 */}
			<div className="flex justify-between items-center gap-2 p-3 bg-blue-100/80 border-b border-blue-200">
				{/* 拖拽手柄 */}
				<div className="flex items-center gap-2 flex-grow">
					<div
						className="drag-handle opacity-0 group-hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing p-1 hover:bg-blue-200 rounded"
						data-drag-handle
						title="拖拽移动时间线"
					>
						<GripVertical className="h-4 w-4 text-gray-500" />
					</div>
					<div className="flex items-center flex-grow">
						<Input
							type="text"
							value={title}
							onChange={handleTitleChange}
							placeholder="时间线标题"
							className="text-md font-medium bg-transparent border-0 shadow-none p-0 focus:ring-0 focus:border-b focus:border-blue-400 disabled:opacity-70"
							disabled={!editor.isEditable}
						/>
					</div>
				</div>

				<div className="flex items-center gap-2 text-sm text-gray-600">
					<div className="flex items-center gap-1">
						<CalendarDays className="h-4 w-4 text-gray-500 flex-shrink-0" />
						<Popover
							open={isCalendarOpen}
							onOpenChange={setIsCalendarOpen}
						>
							<PopoverTrigger asChild>
								<button
									className={cn(
										"text-xs bg-transparent p-0 cursor-pointer border-0 text-left hover:text-blue-600 transition-colors",
										isCalendarOpen
											? "text-blue-600"
											: "text-gray-600",
									)}
									type="button"
								>
									{displayDate}
								</button>
							</PopoverTrigger>
							<PopoverContent className="w-auto p-0" align="end">
								<CalendarComponent
									mode="single"
									selected={date}
									onSelect={(newDate) => {
										setDate(newDate);
										setIsCalendarOpen(false);
									}}
									initialFocus
								/>
							</PopoverContent>
						</Popover>
					</div>

					{/* 操作按钮组 */}
					<div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
						<Button
							size="icon"
							variant="ghost"
							className="h-6 w-6 text-green-600 hover:text-green-800 hover:bg-green-100"
							onClick={handleAddPoint}
							title="添加旅行点位"
							type="button"
						>
							<Plus className="h-4 w-4" />
						</Button>
						<Button
							size="icon"
							variant="ghost"
							className="h-6 w-6 text-blue-600 hover:text-blue-800 hover:bg-blue-100"
							onClick={handleDuplicate}
							title="复制时间线"
							type="button"
						>
							<Copy className="h-4 w-4" />
						</Button>
						<Button
							size="icon"
							variant="ghost"
							className="h-6 w-6 text-gray-600 hover:text-gray-800 hover:bg-gray-100"
							onClick={handleQuickSettings}
							title="时间线设置"
							type="button"
						>
							<Settings className="h-4 w-4" />
						</Button>
						<Button
							size="icon"
							variant="ghost"
							className="h-6 w-6 text-red-500 hover:text-red-700 hover:bg-red-100"
							onClick={handleDelete}
							title="删除时间线"
							type="button"
						>
							<Trash2 className="h-4 w-4" />
						</Button>
					</div>
				</div>
			</div>

			{/* 时间线内容区 */}
			<div className="p-2 min-h-[60px]">
				<NodeViewContent className="content-area" />
			</div>

			{/* 底部状态栏 */}
			<div className="px-3 py-1 bg-blue-50 border-t border-blue-200 text-xs text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity">
				<span>时间线ID: {timelineId}</span>
				<span className="ml-4">
					点位数量: {node.content?.childCount || 0}
				</span>
			</div>
		</NodeViewWrapper>
	);
};

// 格式化日期显示的辅助函数
function formatDate(dateString: string): string {
	try {
		if (!dateString) return "";

		const date = new Date(dateString);
		if (Number.isNaN(date.getTime())) return "";

		return format(date, "yyyy年MM月dd日");
	} catch (error) {
		console.error("日期格式化错误:", error);
		return "";
	}
}
