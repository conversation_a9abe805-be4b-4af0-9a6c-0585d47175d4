"use client";

import type { Footprint } from "@repo/api/src/routes/diaries/schemas";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { cn } from "@ui/lib";
import type { ReactNode } from "react";

export interface FootprintWithDiaryIndex extends Footprint {
	diaryIndex?: number;
	diaryTitle?: string;
	iconType?: string;
	totalFootprints?: number;
	footprintIndexInDiary?: number;
}

interface FootprintPopoverProps {
	footprint: FootprintWithDiaryIndex;
	isSelected: boolean;
	isNightMode: boolean;
	popoverOpen: boolean;
	children: ReactNode;
	onOpenChange: (open: boolean) => void;
	onViewDetails: (footprintId: string, diaryId: string) => void;
	onImageClick: (
		footprint: FootprintWithDiaryIndex,
		imageIndex: number,
	) => void;
	navigateToPrevLocation: (footprint: FootprintWithDiaryIndex) => void;
	navigateToNextLocation: (footprint: FootprintWithDiaryIndex) => void;
	getIconForType: (iconType?: string) => string;
}

export function FootprintPopover({
	footprint: fp,
	isSelected,
	isNightMode,
	popoverOpen,
	children,
	onOpenChange,
	onViewDetails,
	onImageClick,
	navigateToPrevLocation,
	navigateToNextLocation,
	getIconForType,
}: FootprintPopoverProps) {
	return (
		<Popover open={popoverOpen} onOpenChange={(open) => onOpenChange(open)}>
			<PopoverTrigger asChild>{children}</PopoverTrigger>
			<PopoverContent
				className="w-[420px] p-0 overflow-hidden shadow-lg rounded-md border border-gray-200 bg-white"
				side="top"
				sideOffset={20}
				align="center"
				onEscapeKeyDown={(e) => {
					e.preventDefault();
				}}
				onPointerDownOutside={(e) => {
					e.preventDefault();
				}}
				onInteractOutside={(e) => {
					e.preventDefault();
				}}
				onFocusOutside={(e) => {
					e.preventDefault();
				}}
			>
				<div className="flex flex-col">
					{/* 日记标题和关闭按钮 - 文艺清新风格 */}
					<div
						className="p-3 border-b text-center font-medium relative flex items-center justify-center"
						style={{
							backgroundColor: "rgba(249, 249, 249, 0.95)",
							color: "#4c4c4c",
							borderBottom: "1px solid rgba(200, 200, 200, 0.3)",
							fontFamily: "'Noto Serif SC', serif",
						}}
					>
						{/* 日记标题 */}
						<div className="font-normal tracking-wide px-2">
							{fp.diaryTitle || "未命名日记"}
						</div>

						{/* 右上角关闭按钮 - 更加简约 */}
						<button
							type="button"
							className="absolute right-2 top-2 rounded-full p-1 hover:bg-black/5 transition-colors"
							onClick={(e) => {
								e.stopPropagation();
								onOpenChange(false);
							}}
							onKeyDown={(e) => {
								if (e.key === "Enter" || e.key === " ") {
									e.stopPropagation();
									onOpenChange(false);
								}
							}}
							aria-label="关闭"
						>
							<svg
								xmlns="http://www.w3.org/2000/svg"
								width="16"
								height="16"
								viewBox="0 0 24 24"
								fill="none"
								stroke="currentColor"
								strokeWidth="1.5"
								strokeLinecap="round"
								strokeLinejoin="round"
								className="text-gray-500"
							>
								<title>关闭</title>
								<path d="M18 6 6 18" />
								<path d="m6 6 12 12" />
							</svg>
						</button>
					</div>

					{/* 点位信息 - 文艺清新风格 */}
					<div className="p-4 bg-white">
						{/* 位置名称和图标 + 阅读完整日记按钮 */}
						<div className="flex items-center justify-between mb-3">
							<h3 className="text-lg font-medium flex items-center">
								<span className="text-lg mr-2">
									{getIconForType(fp.iconType || "")}
								</span>
								<span className="text-gray-700 tracking-wide">
									{fp.location}
								</span>
							</h3>

							{/* 阅读完整日记按钮 */}
							{fp.diaryId && (
								<button
									type="button"
									className="rounded-full p-1.5 hover:bg-gray-100 transition-colors text-gray-500 hover:text-gray-800 ml-2"
									onClick={(e) => {
										e.stopPropagation();
										onViewDetails(fp.id, fp.diaryId);
									}}
									aria-label="阅读完整日记"
									title="阅读完整日记"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										width="18"
										height="18"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										strokeWidth="1.5"
										strokeLinecap="round"
										strokeLinejoin="round"
									>
										<title>阅读完整日记</title>
										<path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z" />
										<path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z" />
									</svg>
								</button>
							)}
						</div>

						{/* 位置详情 - 文艺清新风格 - 紧凑布局 */}
						<div className="space-y-3 text-sm">
							{/* 时间和地点信息组 */}
							<div className="flex flex-wrap gap-x-4 gap-y-2 text-gray-600">
								{/* 日期 */}
								{fp.date && (
									<div className="flex items-center gap-1.5">
										<svg
											xmlns="http://www.w3.org/2000/svg"
											width="13"
											height="13"
											viewBox="0 0 24 24"
											fill="none"
											stroke="currentColor"
											strokeWidth="1.5"
											strokeLinecap="round"
											strokeLinejoin="round"
											className="text-gray-400"
										>
											<title>日期</title>
											<rect
												width="18"
												height="18"
												x="3"
												y="4"
												rx="2"
												ry="2"
											/>
											<line
												x1="16"
												x2="16"
												y1="2"
												y2="6"
											/>
											<line x1="8" x2="8" y1="2" y2="6" />
											<line
												x1="3"
												x2="21"
												y1="10"
												y2="10"
											/>
										</svg>
										<span>
											{new Date(
												fp.date,
											).toLocaleDateString("zh-CN")}
										</span>
									</div>
								)}

								{/* 城市和国家 */}
								{(fp.city || fp.country) && (
									<div className="flex items-center gap-1.5">
										<svg
											xmlns="http://www.w3.org/2000/svg"
											width="13"
											height="13"
											viewBox="0 0 24 24"
											fill="none"
											stroke="currentColor"
											strokeWidth="1.5"
											strokeLinecap="round"
											strokeLinejoin="round"
											className="text-gray-400"
										>
											<title>位置</title>
											<path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z" />
											<circle cx="12" cy="10" r="3" />
										</svg>
										<span>
											{[fp.city, fp.country]
												.filter(Boolean)
												.join(", ")}
										</span>
									</div>
								)}

								{/* 时间线信息 */}
								{fp.timelineTitle && (
									<div className="flex items-center gap-1.5">
										<svg
											xmlns="http://www.w3.org/2000/svg"
											width="13"
											height="13"
											viewBox="0 0 24 24"
											fill="none"
											stroke="currentColor"
											strokeWidth="1.5"
											strokeLinecap="round"
											strokeLinejoin="round"
											className="text-gray-400"
										>
											<title>时间线</title>
											<path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z" />
											<path d="M12 6v6l4 2" />
										</svg>
										<span>{fp.timelineTitle}</span>
									</div>
								)}
							</div>

							{/* 显示描述 - 文艺风格 */}
							{fp.description && (
								<div className="mt-2 pt-2 border-t border-gray-100 max-h-32 overflow-y-auto popover-scrollbar">
									<p className="text-gray-600 italic leading-relaxed text-sm">
										{fp.description}
									</p>
								</div>
							)}
						</div>

						{/* 照片展示区域 - 文艺清新风格 - 统一网格布局 */}
						{fp.images && fp.images.length > 0 && (
							<div className="mt-3 pt-3 border-t border-gray-100">
								{/* 照片标题 */}
								<div className="flex items-center gap-1.5 mb-2 text-gray-600">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										width="13"
										height="13"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										strokeWidth="1.5"
										strokeLinecap="round"
										strokeLinejoin="round"
										className="text-gray-400"
									>
										<title>照片</title>
										<path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z" />
										<circle cx="12" cy="13" r="3" />
									</svg>
									<span className="text-sm font-medium">
										{fp.images.length} 张照片
									</span>
								</div>

								{/* 统一网格布局照片 - 单行布局 */}
								<div className="grid grid-cols-4 gap-1.5 pb-3">
									{fp.images.slice(0, 4).map((image, i) => (
										<button
											key={i}
											className="rounded-md overflow-hidden aspect-square relative hover:opacity-95 transition-opacity border border-gray-100 shadow-sm hover:shadow-md"
											onClick={() => onImageClick(fp, i)}
											onKeyDown={(e) => {
												if (
													e.key === "Enter" ||
													e.key === " "
												) {
													onImageClick(fp, i);
												}
											}}
											type="button"
										>
											<img
												src={
													typeof image === "string"
														? image
														: image.url
												}
												alt={
													typeof image === "string"
														? `照片 ${i + 1}`
														: image.alt ||
															image.caption ||
															`照片 ${i + 1}`
												}
												className="w-full h-full object-cover"
											/>
											{i === 3 &&
												fp.images &&
												fp.images.length > 4 && (
													<div className="absolute inset-0 bg-black/40 flex items-center justify-center text-white font-medium text-sm backdrop-blur-[1px]">
														+{fp.images.length - 4}
													</div>
												)}
										</button>
									))}
								</div>
							</div>
						)}

						{/* 底部导航栏 */}
						<div className="mt-4 pt-3 border-t border-gray-100 flex items-center justify-between px-4 pb-3">
							{/* 左侧导航按钮 */}
							<button
								type="button"
								className={cn(
									"rounded-md px-3 py-1.5 border transition-colors flex items-center gap-1.5",
									!fp.totalFootprints ||
										fp.totalFootprints <= 1
										? "border-gray-100 text-gray-300 cursor-not-allowed"
										: "border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-500 hover:text-gray-700",
								)}
								onClick={(e) => {
									e.stopPropagation();
									if (
										fp.totalFootprints &&
										fp.totalFootprints > 1
									) {
										navigateToPrevLocation(fp);
									}
								}}
								onKeyDown={(e) => {
									if (e.key === "Enter" || e.key === " ") {
										e.stopPropagation();
										if (
											fp.totalFootprints &&
											fp.totalFootprints > 1
										) {
											navigateToPrevLocation(fp);
										}
									}
								}}
								aria-label="上一个位置"
								disabled={
									!fp.totalFootprints ||
									fp.totalFootprints <= 1
								}
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="14"
									height="14"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									strokeWidth="1.5"
									strokeLinecap="round"
									strokeLinejoin="round"
								>
									<title>上一个位置</title>
									<polyline points="15 18 9 12 15 6" />
								</svg>
								<span className="text-xs">上一个</span>
							</button>

							{/* 位置计数器 */}
							<div className="text-gray-600 text-xs bg-gray-50 px-3 py-1.5 rounded-full border border-gray-100 font-medium">
								{fp.footprintIndexInDiary !== undefined &&
								fp.totalFootprints !== undefined
									? `${fp.footprintIndexInDiary + 1}/${fp.totalFootprints}`
									: ""}
							</div>

							{/* 右侧导航按钮 */}
							<button
								type="button"
								className={cn(
									"rounded-md px-3 py-1.5 border transition-colors flex items-center gap-1.5",
									!fp.totalFootprints ||
										fp.totalFootprints <= 1
										? "border-gray-100 text-gray-300 cursor-not-allowed"
										: "border-gray-200 hover:border-gray-300 hover:bg-gray-50 text-gray-500 hover:text-gray-700",
								)}
								onClick={(e) => {
									e.stopPropagation();
									if (
										fp.totalFootprints &&
										fp.totalFootprints > 1
									) {
										navigateToNextLocation(fp);
									}
								}}
								onKeyDown={(e) => {
									if (e.key === "Enter" || e.key === " ") {
										e.stopPropagation();
										if (
											fp.totalFootprints &&
											fp.totalFootprints > 1
										) {
											navigateToNextLocation(fp);
										}
									}
								}}
								aria-label="下一个位置"
								disabled={
									!fp.totalFootprints ||
									fp.totalFootprints <= 1
								}
							>
								<span className="text-xs">下一个</span>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="14"
									height="14"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									strokeWidth="1.5"
									strokeLinecap="round"
									strokeLinejoin="round"
								>
									<title>下一个位置</title>
									<polyline points="9 18 15 12 9 6" />
								</svg>
							</button>
						</div>
					</div>
				</div>
			</PopoverContent>
		</Popover>
	);
}
