import { Node, mergeAttributes } from "@tiptap/core";
import { ReactNodeViewRenderer } from "@tiptap/react";
import { v4 as uuidv4 } from "uuid";
import { TravelPointNodeView } from "../TravelPointNodeView";

// 自定义节点属性接口
export interface TravelPointAttributes {
	pointId: string | null;
	location: string;
	pointDate: string;
	images: string[];
}

export const CustomTravelPointNode = Node.create({
	name: "travelPoint",

	group: "block",

	content: "block*",

	draggable: true,

	// 限制只能作为时间线节点的子节点
	validParents: ["travelTimeline"],

	isolating: true,

	addAttributes() {
		return {
			pointId: {
				default: null,
			},
			location: {
				default: "",
			},
			pointDate: {
				default: new Date().toISOString(),
			},
			images: {
				default: [],
			},
		};
	},

	parseHTML() {
		return [
			{
				tag: "div[data-type='travel-point']",
			},
		];
	},

	renderHTML({ HTMLAttributes }) {
		return [
			"div",
			mergeAttributes(HTMLAttributes, { "data-type": "travel-point" }),
			0,
		];
	},

	addNodeView() {
		return ReactNodeViewRenderer(TravelPointNodeView, {
			// 避免在 React 渲染周期内调用 flushSync
			as: "div",
			className: "travel-point-wrapper",
			// 添加这些选项来避免 flushSync 问题
			stopEvent: () => false,
			ignoreMutation: () => false,
			// 使用 contentDOMElementTag 而不是默认的处理方式
			contentDOMElementTag: "div",
		});
	},

	addCommands() {
		return {
			insertTravelPoint:
				() =>
				({ chain }: { chain: any }) => {
					return chain()
						.insertContent({
							type: this.name,
							attrs: {
								pointId: uuidv4(),
								pointDate: new Date().toISOString(),
								location: "新地点",
							},
							content: [
								{
									type: "paragraph",
								},
							],
						})
						.run();
				},
			deleteNode:
				() =>
				({ commands }: { commands: any }) => {
					return commands.deleteNode(this.name);
				},
		};
	},
});
