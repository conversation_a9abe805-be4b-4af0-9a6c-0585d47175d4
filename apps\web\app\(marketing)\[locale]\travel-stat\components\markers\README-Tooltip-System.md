# Tooltip 系统重构说明

## 🎯 重构目标

统一地图点位的tooltip显示系统，消除重复代码，提供清晰的组件结构和更好的可维护性。

## 🔧 新系统架构

### 1. 核心组件

#### `BaseTooltip.tsx` - 基础tooltip组件
- **职责**: 处理定位逻辑、动画效果、Portal渲染
- **模式支持**:
  - `static`: 静态显示（用于地图上的固定tooltip）
  - `dynamic`: 动态跟随鼠标（暂未使用）
  - `global`: 全局Portal渲染（用于点击触发的详细tooltip）

#### `SimpleTooltipContent.tsx` - 简单内容组件
- **职责**: 显示基本信息（城市名、国家）
- **适用场景**: 地图上的静态标签、快速预览
- **变体**: `transparent` | `solid`

#### `DetailedTooltipContent.tsx` - 详细内容组件  
- **职责**: 显示完整信息（图片、日期、描述、操作按钮）
- **适用场景**: 点击marker触发的详细弹窗
- **变体**: `card` | `modal`

#### `UnifiedTooltip.tsx` - 统一tooltip组件
- **职责**: 集成BaseTooltip和内容组件，提供统一API
- **类型**: `simple` | `detailed`
- **智能变体选择**: 根据模式和类型自动选择最佳样式

#### `GlobalTooltip.tsx` - 全局tooltip管理器
- **职责**: 管理全局tooltip状态，处理Portal渲染
- **替代**: 原来的`GlobalPolaroidTooltip`

### 2. 使用场景

```typescript
// 场景1: 地图上的静态简单tooltip - 只在非拍立得样式下显示
{showTooltips && 
  !polaroidTooltipState.isVisible && 
  markerStyle !== "polaroid" && (
  <UnifiedTooltip
    isVisible={true}
    type="simple"
    mode="static"
    point={point}
    onRemovePoint={onRemovePoint}
  />
)}

// 场景2: 点击触发的全局详细tooltip
<UnifiedTooltip
  isVisible={isVisible}
  type="detailed"
  mode="global"
  point={point}
  mousePosition={mousePosition}
  onRemovePoint={onRemovePoint}
  onClose={onClose}
/>
```

## 🗂️ 文件组织

```
components/markers/
├── shared/                    # 共享的tooltip组件
│   ├── BaseTooltip.tsx       # 基础tooltip逻辑
│   ├── SimpleTooltipContent.tsx    # 简单内容组件
│   └── DetailedTooltipContent.tsx  # 详细内容组件
├── UnifiedTooltip.tsx        # 统一tooltip组件
├── GlobalTooltip.tsx         # 全局tooltip管理器
└── README-Tooltip-System.md  # 本说明文档
```

## 🔄 迁移对照

### 旧系统 → 新系统

| 旧组件 | 新组件 | 说明 |
|--------|--------|------|
| `HoverTooltip` | `UnifiedTooltip` (simple/static) | 静态简单tooltip |
| `PolaroidTooltip` | `UnifiedTooltip` (detailed/global) | 详细弹窗tooltip |
| `GlobalPolaroidTooltip` | `GlobalTooltip` | 全局tooltip管理 |

### API变化

#### Marker组件
```typescript
// 旧API
onPolaroidTooltip?: (point, mousePosition) => void

// 新API  
onMarkerClick?: (point, mousePosition) => void
```

## 🎨 样式变体

### 简单Tooltip变体
- `transparent`: 半透明背景，用于静态模式
- `solid`: 实心背景，用于悬浮模式

### 详细Tooltip变体
- `card`: 卡片样式，用于常规弹窗
- `modal`: 模态样式，用于全局弹窗

## 🔒 Tooltip智能显示逻辑

系统实现了智能的tooltip显示逻辑，避免信息冗余和视觉混乱：

### 显示规则
1. **拍立得marker**: 不显示简单tooltip，因为拍立得卡片本身就包含信息
2. **其他marker样式**: 显示简单tooltip提供基本信息 
3. **全局详细tooltip**: 优先级最高，显示时隐藏所有静态tooltip

### 实现机制
```typescript
// MapContainer中的智能显示逻辑
{showTooltips && 
  !polaroidTooltipState.isVisible && 
  markerStyle !== "polaroid" && (
  <UnifiedTooltip type="simple" mode="static" ... />
)}

// GlobalTooltip统一管理全局状态
const { polaroidTooltipState } = useMap();
```

### 用户体验
- ✅ 拍立得marker → 直接显示照片，无额外标签
- ✅ 圆点/emoji marker → 显示简单标签
- ✅ 点击任何marker → 显示详细tooltip，隐藏所有简单标签
- ✅ 关闭详细tooltip → 恢复显示对应的简单标签
- ✅ 避免信息重叠和视觉混乱

## 🚀 优势

1. **代码复用**: 消除了500+行重复代码
2. **统一API**: 一个组件处理所有tooltip场景
3. **类型安全**: 完整的TypeScript支持
4. **易于扩展**: 新增tooltip类型只需添加内容组件
5. **性能优化**: 统一的定位逻辑和Portal管理

## 🔧 扩展指南

### 添加新的tooltip类型

1. 创建内容组件：
```typescript
// NewTooltipContent.tsx
export function NewTooltipContent({ point, onRemovePoint }: Props) {
  return <div>自定义内容</div>;
}
```

2. 在UnifiedTooltip中添加类型：
```typescript
export type TooltipType = "simple" | "detailed" | "new";

// 在组件中添加条件渲染
{type === "new" ? (
  <NewTooltipContent ... />
) : ...}
```

### 添加新的样式变体

在对应的内容组件中添加variant prop和样式逻辑即可。

## 🧪 测试建议

1. **静态tooltip**: 验证在不同marker样式下的显示效果
2. **全局tooltip**: 测试点击不同类型marker的弹窗效果  
3. **响应式**: 确保在不同屏幕尺寸下的定位正确
4. **交互**: 验证删除、关闭等操作功能正常 