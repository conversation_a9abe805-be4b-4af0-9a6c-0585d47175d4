/**
 * Bing Maps地理编码提供商
 */

import { BaseGeocodingProvider } from "../base-provider";
import type {
	GeocodingOptions,
	GeocodingProvider,
	UnifiedGeocodingResult,
} from "../types";

/**
 * Bing Maps地理编码提供商
 */
export class BingProvider extends BaseGeocodingProvider {
	private readonly baseUrl = "https://dev.virtualearth.net/REST/v1/Locations";

	getProviderType(): GeocodingProvider {
		return "bing";
	}

	protected async performGeocode(
		address: string,
		options: GeocodingOptions,
	): Promise<UnifiedGeocodingResult | null> {
		const params = new URLSearchParams({
			query: address,
			key: this.config.apiKey,
			output: "json",
		});

		// 添加可选参数
		if (options.limit) {
			params.set("maxResults", options.limit.toString());
		}
		if (options.country) {
			params.set("countryRegion", options.country);
		}

		const response = await this.makeRequest(`${this.baseUrl}?${params}`);

		if (!response.success || !response.rawData) {
			return null;
		}

		const data = response.rawData;

		if (
			data.statusCode === 200 &&
			data.resourceSets &&
			data.resourceSets.length > 0 &&
			data.resourceSets[0].resources &&
			data.resourceSets[0].resources.length > 0
		) {
			const result = data.resourceSets[0].resources[0];
			const point = result.point;
			const address = result.address;

			return {
				longitude: point.coordinates[1],
				latitude: point.coordinates[0],
				formattedAddress: result.name,
				addressComponents: {
					country: address.countryRegion,
					province: address.adminDistrict,
					city: address.locality,
					district: address.adminDistrict2,
					street: address.addressLine,
					postalCode: address.postalCode,
				},
				confidence: this.mapBingConfidence(result.confidence),
				placeTypes: [result.entityType],
				viewport: result.bbox
					? {
							southwest: {
								lat: result.bbox[0],
								lng: result.bbox[1],
							},
							northeast: {
								lat: result.bbox[2],
								lng: result.bbox[3],
							},
						}
					: undefined,
				provider: "bing",
				responseTime: response.responseTime,
			};
		}

		return null;
	}

	protected async performReverseGeocode(
		longitude: number,
		latitude: number,
		options: GeocodingOptions,
	): Promise<UnifiedGeocodingResult | null> {
		const url = `${this.baseUrl}/${latitude},${longitude}`;
		const params = new URLSearchParams({
			key: this.config.apiKey,
			output: "json",
		});

		const response = await this.makeRequest(`${url}?${params}`);

		if (!response.success || !response.rawData) {
			return null;
		}

		const data = response.rawData;

		if (
			data.statusCode === 200 &&
			data.resourceSets &&
			data.resourceSets.length > 0 &&
			data.resourceSets[0].resources &&
			data.resourceSets[0].resources.length > 0
		) {
			const result = data.resourceSets[0].resources[0];
			const address = result.address;

			return {
				longitude,
				latitude,
				formattedAddress: result.name,
				addressComponents: {
					country: address.countryRegion,
					province: address.adminDistrict,
					city: address.locality,
					district: address.adminDistrict2,
					street: address.addressLine,
					postalCode: address.postalCode,
				},
				confidence: this.mapBingConfidence(result.confidence),
				placeTypes: [result.entityType],
				provider: "bing",
				responseTime: response.responseTime,
			};
		}

		return null;
	}

	/**
	 * 映射Bing的置信度到标准置信度
	 */
	private mapBingConfidence(confidence: string): "high" | "medium" | "low" {
		switch (confidence) {
			case "High":
				return "high";
			case "Medium":
				return "medium";
			case "Low":
				return "low";
			default:
				return "medium";
		}
	}
}
