/**
 * 分析追踪工具函数
 * 用于在整个应用中统一追踪用户行为
 */

export const trackEvent = (eventName: string, props?: Record<string, any>) => {
	if (typeof window !== "undefined" && window.plausible) {
		window.plausible(eventName, { props });
	}
};

// 预定义的事件追踪函数
export const analytics = {
	// 日记相关事件
	diary: {
		created: (diaryId: string) =>
			trackEvent("diary_created", { diary_id: diaryId }),
		opened: (diaryId: string, mode: "edit" | "view") =>
			trackEvent("diary_opened", { diary_id: diaryId, mode }),
		saved: (
			diaryId: string,
			timelinesCount: number,
			pointsCount: number,
			saveType: "auto" | "manual",
		) =>
			trackEvent("diary_saved", {
				diary_id: diaryId,
				timelines_count: timelinesCount,
				total_points: pointsCount,
				save_type: saveType,
			}),
		deleted: (diaryId: string) =>
			trackEvent("diary_deleted", { diary_id: diaryId }),
	},

	// 点位相关事件
	point: {
		added: (iconType: string, hasImages: boolean, location: string) =>
			trackEvent("point_added", {
				icon_type: iconType,
				has_images: hasImages,
				location_type: location.split(",")[0], // 只记录城市名
			}),
		edited: (pointId: string, field: string) =>
			trackEvent("point_edited", { point_id: pointId, field }),
		deleted: (pointId: string, iconType: string) =>
			trackEvent("point_deleted", {
				point_id: pointId,
				icon_type: iconType,
			}),
		copied: (iconType: string, hasImages: boolean, location: string) =>
			trackEvent("point_copied", {
				icon_type: iconType,
				has_images: hasImages,
				location_type: location.split(",")[0],
			}),
	},

	// 功能使用
	feature: {
		used: (
			feature:
				| "rich_text_editor"
				| "map_view"
				| "image_upload"
				| "export_video"
				| "presentation_mode",
		) => trackEvent("feature_used", { feature }),
		modeSwitch: (from: string, to: string) =>
			trackEvent("mode_switched", { from_mode: from, to_mode: to }),
	},

	// 演示模式
	presentation: {
		started: (
			diaryId: string,
			pointsCount: number,
			isExportMode: boolean,
		) =>
			trackEvent("presentation_started", {
				diary_id: diaryId,
				points_count: pointsCount,
				is_export_mode: isExportMode,
			}),
		completed: (diaryId: string, duration: number) =>
			trackEvent("presentation_completed", {
				diary_id: diaryId,
				duration: Math.round(duration / 1000),
			}),
		skipped: (diaryId: string, currentIndex: number, totalPoints: number) =>
			trackEvent("presentation_skipped", {
				diary_id: diaryId,
				current_point_index: currentIndex,
				total_points: totalPoints,
			}),
	},

	// 地图交互
	map: {
		interaction: (
			action: "marker_clicked" | "zoom_changed" | "pan",
			diaryId?: string,
		) => trackEvent("map_interaction", { action, diary_id: diaryId }),
		markerClicked: (pointId: string, diaryId: string) =>
			trackEvent("map_marker_clicked", {
				point_id: pointId,
				diary_id: diaryId,
			}),
	},

	// 用户留存
	user: {
		retention: (
			diariesCount: number,
			totalPoints: number,
			lastActiveDate: string,
		) =>
			trackEvent("user_retention", {
				diaries_count: diariesCount,
				total_points: totalPoints,
				last_active_date: lastActiveDate,
			}),
		sessionStart: () => trackEvent("session_start"),
		sessionEnd: (duration: number) =>
			trackEvent("session_end", {
				duration_minutes: Math.round(duration / 60000),
			}),
	},

	// 错误追踪
	error: {
		encountered: (
			errorType: string,
			errorMessage: string,
			context?: string,
		) =>
			trackEvent("error_encountered", {
				error_type: errorType,
				error_message: errorMessage,
				context,
			}),
	},
};

export default analytics;
