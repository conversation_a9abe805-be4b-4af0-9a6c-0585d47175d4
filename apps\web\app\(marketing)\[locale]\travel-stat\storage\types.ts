import type {
	CardCustomization,
	SocialPlatform,
} from "../components/card-generator/types/cardTypes";
import type {
	AnimationTheme,
	AtmosphereTheme,
	MapProjectionType,
	MapStyleType,
} from "../components/types";
import type { CountryData, TravelPoint } from "../types";
import type { ColorThemeType } from "../types/colorTypes";
import type { MarkerStyleType } from "../types/markerTypes";

// 统一的旅行足迹数据结构
export interface TravelFootprintData {
	// 数据版本，用于迁移和兼容性
	version: string;

	// 最后更新时间
	lastUpdated: string;

	// 旅行数据
	travel: {
		// 旅行点数据
		points: TravelPoint[];
		// 访问过的国家数据
		countries: CountryData[];
	};

	// 地图配置
	mapConfig: {
		// 大气层主题
		atmosphereTheme: AtmosphereTheme;
		// 地图样式
		mapStyle: MapStyleType;
		// 地图投影
		mapProjection: MapProjectionType;
		// 动画主题
		animationTheme: AnimationTheme;
		// 地图视图状态
		viewState: {
			latitude: number;
			longitude: number;
			zoom: number;
		};
	};

	// 样式配置
	styleConfig: {
		// 颜色主题
		colorTheme: ColorThemeType;
		// 标记样式
		markerStyle: MarkerStyleType;
		// 标记主题
		markerTheme: string;
		// 当前Emoji
		currentEmoji: string;
		// Emoji颜色
		currentEmojiColor: string;
		// 隐藏轮廓
		hideOutline: boolean;
	};

	// 卡片生成配置
	cardConfig: {
		// 当前选择的社交平台
		selectedPlatform: SocialPlatform;
		// 当前选择的模板ID（存储模板ID而不是整个模板对象）
		selectedTemplateId: string;
		// 卡片自定义配置
		customization: CardCustomization;
		// 用户信息配置
		userProfile: {
			username?: string;
			avatar?: string;
			bio?: string;
		};
		// 导出设置
		exportSettings: {
			quality: "low" | "medium" | "high";
			includeWatermark: boolean;
			customWatermark?: string;
		};
	};

	// 用户偏好设置
	preferences: {
		// 语言设置
		language?: string;
		// 是否显示动画
		enableAnimations?: boolean;
		// 数据自动保存间隔（毫秒）
		autoSaveInterval?: number;
		// 页面模式偏好
		defaultPageMode?: "editing" | "card-generation";
		// 其他用户偏好
		[key: string]: any;
	};
}

// 数据迁移规则
export interface DataMigration {
	// 源版本
	fromVersion: string;
	// 目标版本
	toVersion: string;
	// 迁移函数
	migrate: (oldData: any) => TravelFootprintData;
}

// 默认数据配置
export const DEFAULT_TRAVEL_DATA: TravelFootprintData = {
	version: "1.0.0",
	lastUpdated: new Date().toISOString(),
	travel: {
		points: [],
		countries: [],
	},
	mapConfig: {
		atmosphereTheme: "day",
		mapStyle: "outdoors",
		mapProjection: "globe",
		animationTheme: "shooting-stars",
		viewState: {
			latitude: 39.9042,
			longitude: 116.4074,
			zoom: 2,
		},
	},
	styleConfig: {
		colorTheme: "classic-blue-green",
		markerStyle: "GRADIENT_PULSE" as MarkerStyleType,
		markerTheme: "ocean",
		currentEmoji: "📍",
		currentEmojiColor: "transparent",
		hideOutline: false,
	},
	cardConfig: {
		selectedPlatform: "instagram",
		selectedTemplateId: "modern-travel-card", // 默认模板ID
		customization: {
			colors: {
				primary: "#3b82f6",
				secondary: "#64748b",
				accent: "#06b6d4",
				background: "#ffffff",
				text: "#1e293b",
			},
			typography: {
				fontFamily: "Inter",
				headerSize: 24,
				bodySize: 14,
				titleWeight: 600,
			},
			layout: {
				padding: 20,
				spacing: 16,
				borderRadius: 12,
				showShadow: true,
			},
			content: {
				showDate: true,
				showUserInfo: true,
				showDetailedStats: true,
				customTitle: "我的旅行足迹",
				customFooter: "探索世界，记录足迹",
			},
		},
		userProfile: {
			username: "",
			avatar: "",
			bio: "",
		},
		exportSettings: {
			quality: "high",
			includeWatermark: false,
			customWatermark: "",
		},
	},
	preferences: {
		language: "zh",
		enableAnimations: true,
		autoSaveInterval: 1000,
		defaultPageMode: "editing",
	},
};

// 存储键常量
export const STORAGE_KEYS = {
	// 主数据键
	MAIN_DATA: "travel-footprint-data",
	// 备份数据键
	BACKUP_DATA: "travel-footprint-backup",
	// 设置键
	SETTINGS: "travel-footprint-settings",
} as const;

// 数据操作结果
export interface DataOperationResult<T = any> {
	success: boolean;
	data?: T;
	error?: string;
	timestamp: string;
}
