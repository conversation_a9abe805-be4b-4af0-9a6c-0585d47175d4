export function usePopoverPosition() {
	// 计算触发器位置 - 传递给 PopoverPortal 进行智能定位
	const calculatePopoverPosition = (
		buttonElement: HTMLButtonElement,
		popoverType?: string,
	) => {
		const rect = buttonElement.getBoundingClientRect();

		// 返回触发器按钮的位置信息
		// PopoverPortal 会根据这个位置自动计算最佳的弹窗位置
		return {
			top: rect.top,
			left: rect.left,
			// 额外信息供 PopoverPortal 参考
			width: rect.width,
			height: rect.height,
			right: rect.right,
			bottom: rect.bottom,
		};
	};

	return { calculatePopoverPosition };
}
