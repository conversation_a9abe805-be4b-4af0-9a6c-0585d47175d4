/**
 * 地图服务抽象接口定义
 * 提供通用的地图操作接口，不同地图服务提供商需要实现这些接口
 */

// 坐标点接口
export interface LatLng {
	lat: number;
	lng: number;
}

// 地图边界接口
export interface MapBounds {
	north: number;
	south: number;
	east: number;
	west: number;
}

// 地图标记选项
export interface MarkerOptions {
	position: LatLng;
	title?: string;
	icon?: string;
	draggable?: boolean;
	isActive?: boolean; // 添加活动状态标记
	onClick?: () => void;
}

// 地图视图选项
export interface MapViewOptions {
	center: LatLng;
	zoom: number;
	minZoom?: number;
	maxZoom?: number;
}

// 地图事件类型
export type MapEventType =
	| "click"
	| "drag"
	| "dragend"
	| "zoom_changed"
	| "bounds_changed"
	| "idle";

// 地图事件处理函数
export type MapEventHandler = (event: any) => void;

// 地图服务接口
export interface MapService {
	// 初始化地图
	init(element: HTMLElement, options: MapViewOptions): Promise<void>;

	// 清理地图资源
	destroy(): void;

	// 添加标记
	addMarker(options: MarkerOptions): string; // 返回标记ID

	// 移除标记
	removeMarker(markerId: string): void;

	// 更新标记位置
	updateMarkerPosition(markerId: string, position: LatLng): void;

	// 设置地图中心点
	setCenter(center: LatLng): void;

	// 设置缩放级别
	setZoom(zoom: number): void;

	// 获取当前边界
	getBounds(): MapBounds;

	// 获取当前中心点
	getCenter(): LatLng;

	// 获取当前缩放级别
	getZoom(): number;

	// 适配显示多个点
	fitBounds(points: LatLng[]): void;

	// 添加事件监听
	addEventListener(type: MapEventType, handler: MapEventHandler): void;

	// 移除事件监听
	removeEventListener(type: MapEventType, handler: MapEventHandler): void;

	// 获取原始地图实例(用于特定地图服务的特殊操作)
	getNativeMapInstance(): any;
}

// 地图服务提供商接口
export interface MapServiceProvider {
	// 加载地图服务所需的资源
	load(): Promise<void>;

	// 创建地图服务实例
	createMapService(): MapService;

	// 检查地图服务是否已加载
	isLoaded(): boolean;

	// 获取提供商名称
	getProviderName(): string;
}

// React组件属性接口
export interface MapProps {
	center?: LatLng;
	zoom?: number;
	minZoom?: number;
	maxZoom?: number;
	markers?: MarkerOptions[];
	onMapClick?: (location: LatLng) => void;
	onMapIdle?: () => void;
	onBoundsChanged?: (bounds: MapBounds) => void;
	onMarkerClick?: (markerId: string) => void;
	onMapInit?: (mapService: MapService) => void; // 添加地图初始化回调
	height?: string | number;
	width?: string | number;
	className?: string;
	providerOptions?: Record<string, any>;
}
