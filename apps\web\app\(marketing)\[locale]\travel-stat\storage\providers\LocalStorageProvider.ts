import type { IDataStorage, StorageConfig } from "../interfaces";
import { DataStorageError } from "../interfaces";

/**
 * LocalStorage 存储提供者
 * 实现基于浏览器 localStorage 的数据存储
 */
export class LocalStorageProvider implements IDataStorage {
	private readonly keyPrefix: string;
	private readonly enableCompression: boolean;
	private readonly errorStrategy: "throw" | "log" | "silent";

	constructor(config: StorageConfig = {}) {
		this.keyPrefix = config.keyPrefix || "travel-footprint:";
		this.enableCompression = config.enableCompression || false;
		this.errorStrategy = config.errorStrategy || "throw";
	}

	/**
	 * 获取完整的存储键
	 */
	private getFullKey(key: string): string {
		return `${this.keyPrefix}${key}`;
	}

	/**
	 * 处理错误
	 */
	private handleError(error: Error, code: string): void {
		const storageError = new DataStorageError(
			`LocalStorage operation failed: ${error.message}`,
			code,
			error,
		);

		switch (this.errorStrategy) {
			case "throw":
				throw storageError;
			case "log":
				console.error(storageError);
				break;
			case "silent":
				// 静默处理，不做任何操作
				break;
		}
	}

	/**
	 * 序列化数据
	 */
	private serialize(value: any): string {
		try {
			const serialized = JSON.stringify(value);

			// 如果启用压缩，可以在这里添加压缩逻辑
			if (this.enableCompression) {
				// TODO: 实现数据压缩
				return serialized;
			}

			return serialized;
		} catch (error) {
			throw new DataStorageError(
				"Failed to serialize data",
				"SERIALIZE_ERROR",
				error as Error,
			);
		}
	}

	/**
	 * 反序列化数据
	 */
	private deserialize<T>(value: string): T | null {
		try {
			// 如果启用压缩，需要先解压
			if (this.enableCompression) {
				// TODO: 实现数据解压
			}

			return JSON.parse(value) as T;
		} catch (error) {
			throw new DataStorageError(
				"Failed to deserialize data",
				"DESERIALIZE_ERROR",
				error as Error,
			);
		}
	}

	/**
	 * 检查 localStorage 是否可用
	 */
	private checkAvailability(): void {
		if (typeof window === "undefined" || !window.localStorage) {
			throw new DataStorageError(
				"localStorage is not available",
				"STORAGE_UNAVAILABLE",
			);
		}
	}

	async get<T = any>(key: string): Promise<T | null> {
		try {
			this.checkAvailability();

			const fullKey = this.getFullKey(key);
			const value = localStorage.getItem(fullKey);

			if (value === null) {
				return null;
			}

			return this.deserialize<T>(value);
		} catch (error) {
			this.handleError(error as Error, "GET_ERROR");
			return null;
		}
	}

	async set<T = any>(key: string, value: T): Promise<void> {
		try {
			this.checkAvailability();

			const fullKey = this.getFullKey(key);
			const serializedValue = this.serialize(value);

			localStorage.setItem(fullKey, serializedValue);
		} catch (error) {
			this.handleError(error as Error, "SET_ERROR");
		}
	}

	async remove(key: string): Promise<void> {
		try {
			this.checkAvailability();

			const fullKey = this.getFullKey(key);
			localStorage.removeItem(fullKey);
		} catch (error) {
			this.handleError(error as Error, "REMOVE_ERROR");
		}
	}

	async clear(): Promise<void> {
		try {
			this.checkAvailability();

			// 只清除带有指定前缀的键
			const keys = await this.keys();
			for (const key of keys) {
				await this.remove(key.replace(this.keyPrefix, ""));
			}
		} catch (error) {
			this.handleError(error as Error, "CLEAR_ERROR");
		}
	}

	async has(key: string): Promise<boolean> {
		try {
			this.checkAvailability();

			const fullKey = this.getFullKey(key);
			return localStorage.getItem(fullKey) !== null;
		} catch (error) {
			this.handleError(error as Error, "HAS_ERROR");
			return false;
		}
	}

	async keys(): Promise<string[]> {
		try {
			this.checkAvailability();

			const keys: string[] = [];
			for (let i = 0; i < localStorage.length; i++) {
				const key = localStorage.key(i);
				if (key?.startsWith(this.keyPrefix)) {
					keys.push(key);
				}
			}
			return keys;
		} catch (error) {
			this.handleError(error as Error, "KEYS_ERROR");
			return [];
		}
	}

	/**
	 * 获取存储使用量信息
	 */
	async getStorageInfo(): Promise<{
		totalSize: number;
		usedSize: number;
		keys: number;
	}> {
		try {
			this.checkAvailability();

			let usedSize = 0;
			const keys = await this.keys();

			for (const key of keys) {
				const value = localStorage.getItem(key);
				if (value) {
					usedSize += key.length + value.length;
				}
			}

			// localStorage 的大小限制通常是 5-10MB
			const totalSize = 5 * 1024 * 1024; // 假设 5MB

			return {
				totalSize,
				usedSize,
				keys: keys.length,
			};
		} catch (error) {
			this.handleError(error as Error, "STORAGE_INFO_ERROR");
			return { totalSize: 0, usedSize: 0, keys: 0 };
		}
	}
}
