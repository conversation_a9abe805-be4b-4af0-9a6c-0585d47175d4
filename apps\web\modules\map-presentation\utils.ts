/**
 * 根据图标类型获取表情符号
 *
 * @param iconType 图标类型
 * @returns 对应的表情符号
 */
export function getIconForType(iconType = ""): string {
	switch (iconType) {
		case "hotel":
			return "🏨";
		case "food":
			return "🍽️";
		case "landmark":
			return "🗿";
		case "beach":
			return "🏖️";
		case "mountain":
			return "⛰️";
		case "museum":
			return "🏛️";
		case "park":
			return "🌳";
		case "shopping":
			return "🛍️";
		case "cafe":
			return "☕";
		case "activity":
			return "🎭";
		case "transport":
			return "🚆";
		case "airport":
			return "✈️";
		default:
			return "📍";
	}
}

/**
 * 格式化日期显示
 *
 * @param dateString 日期字符串
 * @returns 格式化后的日期字符串
 */
export function formatDate(dateString: string): string {
	try {
		const date = new Date(dateString);
		return date.toLocaleDateString("zh-CN", {
			year: "numeric",
			month: "long",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit",
		});
	} catch (error) {
		return dateString;
	}
}

/**
 * 创建防抖函数
 *
 * @param func 要执行的函数
 * @param wait 延迟时间（毫秒）
 * @returns 防抖包装后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
	func: T,
	wait: number,
): (...args: Parameters<T>) => void {
	let timeout: NodeJS.Timeout | null = null;

	return (...args: Parameters<T>) => {
		if (timeout) {
			clearTimeout(timeout);
		}

		timeout = setTimeout(() => {
			func(...args);
		}, wait);
	};
}

/**
 * 创建节流函数
 *
 * @param func 要执行的函数
 * @param limit 限制时间（毫秒）
 * @returns 节流包装后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
	func: T,
	limit: number,
): (...args: Parameters<T>) => void {
	let inThrottle = false;

	return (...args: Parameters<T>) => {
		if (!inThrottle) {
			func(...args);
			inThrottle = true;
			setTimeout(() => {
				inThrottle = false;
			}, limit);
		}
	};
}

/**
 * 生成随机ID
 *
 * @returns 随机ID字符串
 */
export function generateId(): string {
	return Math.random().toString(36).substring(2, 15);
}
