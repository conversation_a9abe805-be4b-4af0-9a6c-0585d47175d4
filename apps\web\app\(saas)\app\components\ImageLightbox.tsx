"use client";

import { ChevronLeft, ChevronRight, X } from "lucide-react";
import { useEffect, useRef } from "react";

interface ImageLightboxProps {
	images: string[];
	initialIndex: number;
	isOpen: boolean;
	onClose: () => void;
	onPrev: () => void;
	onNext: () => void;
}

export function ImageLightbox({
	images,
	initialIndex,
	isOpen,
	onClose,
	onPrev,
	onNext,
}: ImageLightboxProps) {
	const dialogRef = useRef<HTMLDialogElement>(null);

	// 处理对话框的打开和关闭
	useEffect(() => {
		if (!dialogRef.current) return;

		if (isOpen) {
			dialogRef.current.showModal();
			// 防止body滚动
			document.body.style.overflow = "hidden";
		} else {
			dialogRef.current.close();
			// 恢复body滚动
			document.body.style.overflow = "";
		}

		return () => {
			// 确保组件卸载时恢复body滚动
			document.body.style.overflow = "";
		};
	}, [isOpen]);

	// 处理键盘导航
	useEffect(() => {
		if (!isOpen) return;

		const handleKeyDown = (event: KeyboardEvent) => {
			if (event.key === "ArrowLeft") {
				onPrev();
			} else if (event.key === "ArrowRight") {
				onNext();
			} else if (event.key === "Escape") {
				onClose();
			}
		};

		window.addEventListener("keydown", handleKeyDown);
		return () => {
			window.removeEventListener("keydown", handleKeyDown);
		};
	}, [isOpen, onPrev, onNext, onClose]);

	if (!isOpen || images.length === 0) {
		return null;
	}

	const currentImage = images[initialIndex];

	// 处理点击背景关闭
	const handleDialogClick = (e: React.MouseEvent<HTMLDialogElement>) => {
		// 只有当点击的是对话框本身（不是内容区域）时才关闭
		const rect = dialogRef.current?.getBoundingClientRect();
		if (rect) {
			// 点击边缘区域时关闭
			const isOutsideContent =
				e.clientX < rect.left + 20 ||
				e.clientX > rect.right - 20 ||
				e.clientY < rect.top + 20 ||
				e.clientY > rect.bottom - 20;

			if (isOutsideContent) {
				onClose();
			}
		}
	};

	return (
		<dialog
			ref={dialogRef}
			className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4 max-w-full max-h-full m-0 w-full h-full backdrop-blur-sm"
			onClick={handleDialogClick}
			onKeyDown={(e) => {
				// 已经在上面的useEffect中处理全局键盘事件
				// 这里只是为了满足linter要求
				e.stopPropagation();
			}}
			onClose={onClose}
		>
			{/* 关闭按钮 */}
			<button
				type="button"
				className="absolute top-4 right-4 text-white p-2 rounded-full bg-black/50 hover:bg-black/70 transition-colors z-10"
				onClick={(e) => {
					e.stopPropagation(); // 防止点击冒泡
					onClose();
				}}
				aria-label="关闭灯箱"
			>
				<X className="h-6 w-6" />
			</button>

			{/* 上一张按钮 */}
			{images.length > 1 && (
				<button
					type="button"
					className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-black/50 hover:bg-black/70 transition-colors z-10"
					onClick={(e) => {
						e.stopPropagation(); // 防止点击冒泡
						onPrev();
					}}
					aria-label="上一张图片"
				>
					<ChevronLeft className="h-8 w-8" />
				</button>
			)}

			{/* 图片显示 */}
			<div
				className="relative max-w-full max-h-full flex items-center justify-center"
				onClick={(e) => e.stopPropagation()} // 防止点击图片区域时冒泡导致关闭
				onKeyDown={(e) => e.stopPropagation()}
				tabIndex={-1} // 添加 tabIndex 但设为 -1 避免被键盘导航
			>
				<img
					src={currentImage}
					alt={`第 ${initialIndex + 1} 张照片`}
					className="max-w-full max-h-[90vh] object-contain block"
				/>
			</div>

			{/* 下一张按钮 */}
			{images.length > 1 && (
				<button
					type="button"
					className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-black/50 hover:bg-black/70 transition-colors z-10"
					onClick={(e) => {
						e.stopPropagation(); // 防止点击冒泡
						onNext();
					}}
					aria-label="下一张图片"
				>
					<ChevronRight className="h-8 w-8" />
				</button>
			)}

			{/* 图片计数器 */}
			{images.length > 1 && (
				<div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white text-sm bg-black/50 px-3 py-1.5 rounded-full">
					{initialIndex + 1} / {images.length}
				</div>
			)}
		</dialog>
	);
}
