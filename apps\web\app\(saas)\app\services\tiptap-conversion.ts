import type {
	FrontendTravelDiary,
	FrontendTravelPoint,
	FrontendTravelTimeline,
} from "@repo/database/src/types/travel-diary";
import type { JSONContent } from "@tiptap/core";
import { v4 as uuidv4 } from "uuid";

/**
 * 将日记内容转换为TipTap文档
 */
export function convertDiaryToTiptapDoc(
	diary: FrontendTravelDiary,
): JSONContent {
	const content: JSONContent[] = [];

	// 添加标题
	content.push({
		type: "heading",
		attrs: { level: 1 },
		content: [{ type: "text", text: diary.title || "无标题" }],
	});

	// 添加副标题（如果有）
	if (diary.subtitle) {
		content.push({
			type: "paragraph",
			attrs: { class: "diary-subtitle" },
			content: [{ type: "text", text: diary.subtitle }],
		});
	}

	// 添加空行
	content.push({ type: "paragraph" });

	// 处理每个时间线
	diary.timelines.forEach((timeline) => {
		// 创建时间线节点
		const timelineNode: JSONContent = {
			type: "travelTimeline",
			attrs: {
				timelineId: timeline.id,
				title: timeline.title,
				timelineDate: timeline.date.toISOString(),
			},
			content: [],
		};

		// 添加每个点位作为时间线的子节点
		timeline.points.forEach((point) => {
			const pointNode: JSONContent = {
				type: "travelPoint",
				attrs: {
					pointId: point.id,
					location: point.title,
					pointDate: point.timestamp.toISOString(),
					images: point.images || [],
				},
				content: [
					{
						type: "paragraph",
						content: point.description
							? [{ type: "text", text: point.description }]
							: [],
					},
				],
			};

			timelineNode.content?.push(pointNode);
		});

		// 添加时间线节点到内容中
		content.push(timelineNode);
	});

	return {
		type: "doc",
		content,
	};
}

/**
 * 从TipTap文档提取时间线和点位数据
 */
export function extractTimelineDataFromTiptap(content: JSONContent): {
	timelines: FrontendTravelTimeline[];
} {
	const timelines: FrontendTravelTimeline[] = [];

	// 检查内容是否有效
	if (!content.content || !Array.isArray(content.content)) {
		return { timelines };
	}

	// 遍历内容查找时间线节点
	content.content.forEach((node) => {
		if (node.type === "travelTimeline") {
			const timelineId = node.attrs?.timelineId || uuidv4();
			const timelineTitle = node.attrs?.title || "未命名时间线";
			const timelineDate = node.attrs?.timelineDate
				? new Date(node.attrs.timelineDate)
				: new Date();

			const timeline: FrontendTravelTimeline = {
				id: timelineId,
				title: timelineTitle,
				date: timelineDate,
				points: [],
			};

			// 提取时间线中的点位
			if (node.content && Array.isArray(node.content)) {
				node.content.forEach((pointNode) => {
					if (pointNode.type === "travelPoint") {
						const pointId = pointNode.attrs?.pointId || uuidv4();
						const pointTitle =
							pointNode.attrs?.location || "未命名地点";
						const pointDate = pointNode.attrs?.pointDate
							? new Date(pointNode.attrs.pointDate)
							: new Date();
						const pointImages = Array.isArray(
							pointNode.attrs?.images,
						)
							? pointNode.attrs?.images
							: [];

						// 提取点位描述
						let description = "";
						if (
							pointNode.content &&
							Array.isArray(pointNode.content) &&
							pointNode.content.length > 0
						) {
							const paragraphNode = pointNode.content[0];
							if (
								paragraphNode.content &&
								Array.isArray(paragraphNode.content) &&
								paragraphNode.content.length > 0
							) {
								const textNode = paragraphNode.content[0];
								description = textNode.text || "";
							}
						}

						const point: FrontendTravelPoint = {
							id: pointId,
							timelineId: timelineId,
							title: pointTitle,
							description: description,
							latitude: 0, // 初始值，后期可通过地图更新
							longitude: 0, // 初始值，后期可通过地图更新
							coordinates: {
								lat: 0,
								lng: 0,
							},
							iconType: "PIN", // 默认图标类型
							timestamp: pointDate,
							images: pointImages,
							address: null,
						};

						timeline.points.push(point);
					}
				});
			}

			timelines.push(timeline);
		}
	});

	return { timelines };
}

/**
 * 将TipTap文档转换为日记格式（用于保存）
 */
export function convertTiptapDocToDiary(
	diaryId: string,
	content: JSONContent,
	originalDiary?: FrontendTravelDiary,
): FrontendTravelDiary {
	// 提取时间线和点位数据
	const { timelines } = extractTimelineDataFromTiptap(content);

	// 提取标题和副标题
	let title = originalDiary?.title || "无标题日记";
	let subtitle = originalDiary?.subtitle || null;

	if (content.content && Array.isArray(content.content)) {
		// 查找标题
		const headingNode = content.content.find(
			(node) => node.type === "heading" && node.attrs?.level === 1,
		);
		if (
			headingNode?.content &&
			Array.isArray(headingNode.content) &&
			headingNode.content.length > 0
		) {
			title = headingNode.content[0].text || title;
		}

		// 查找副标题
		const subtitleNode = content.content.find(
			(node) =>
				node.type === "paragraph" &&
				node.attrs?.class === "diary-subtitle",
		);
		if (
			subtitleNode?.content &&
			Array.isArray(subtitleNode.content) &&
			subtitleNode.content.length > 0
		) {
			subtitle = subtitleNode.content[0].text || subtitle;
		}
	}

	// 创建日记对象
	return {
		id: diaryId,
		title,
		subtitle,
		coverImage: originalDiary?.coverImage || null,
		timelines,
		richTextDraftContent: content,
		isPublic: originalDiary?.isPublic || false,
	};
}

/**
 * 将前端日记数据转换为 Tiptap 文档（支持传入null或undefined）
 */
export function convertDiaryDataToTiptapDoc(
	diaryData: FrontendTravelDiary | null | undefined,
): JSONContent {
	console.log("convertDiaryDataToTiptapDoc - 开始转换日记数据", {
		hasData: !!diaryData,
		title: diaryData?.title,
		timelineCount: diaryData?.timelines?.length || 0,
	});

	if (!diaryData) {
		console.log("convertDiaryDataToTiptapDoc - 无日记数据，返回空文档");
		return { type: "doc", content: [] };
	}

	const content: JSONContent[] = [];

	// 添加日记标题
	content.push({
		type: "heading",
		attrs: { level: 1 },
		content: [{ type: "text", text: diaryData.title || "我的旅行日记" }],
	});
	console.log("添加标题节点:", diaryData.title || "我的旅行日记");

	// 添加日记副标题
	if (diaryData.subtitle) {
		content.push({
			type: "paragraph",
			content: [{ type: "text", text: diaryData.subtitle }],
		});
		console.log("添加副标题节点:", diaryData.subtitle);
	}

	// 添加每个时间线及其点位
	if (diaryData.timelines && Array.isArray(diaryData.timelines)) {
		console.log(`处理${diaryData.timelines.length}个时间线`);

		diaryData.timelines.forEach((timeline, timelineIndex) => {
			if (!timeline) {
				console.log(`时间线 #${timelineIndex} 为空，跳过`);
				return;
			}

			console.log(
				`处理时间线 #${timelineIndex}: ${timeline.title || "未命名"}`,
			);

			// 添加时间线作为travelTimeline节点
			const timelineNode: JSONContent = {
				type: "travelTimeline",
				attrs: {
					timelineId:
						timeline.id ||
						`timeline_${Date.now()}_${timelineIndex}`,
					title: timeline.title || "未命名时间线",
					timelineDate: timeline.date
						? timeline.date.toISOString()
						: new Date().toISOString(),
				},
				content: [],
			};

			// 添加点位作为时间线的子节点
			if (timeline.points && Array.isArray(timeline.points)) {
				console.log(
					`时间线 #${timelineIndex} 包含 ${timeline.points.length} 个点位`,
				);

				timelineNode.content = timeline.points
					.filter((point) => !!point)
					.map((point, pointIndex) => {
						console.log(
							`处理点位 #${pointIndex}: ${point.title || "未命名"}`,
						);

						const pointDate = point.timestamp
							? point.timestamp.toISOString()
							: new Date().toISOString();

						return {
							type: "travelPoint",
							attrs: {
								pointId:
									point.id ||
									`point_${Date.now()}_${pointIndex}`,
								location: point.title || "未命名地点",
								pointDate,
								images: point.images || [],
							},
							content: [
								{
									type: "paragraph",
									content: point.description
										? [
												{
													type: "text",
													text: point.description,
												},
											]
										: [],
								},
							],
						};
					});
			} else {
				console.log(`时间线 #${timelineIndex} 没有点位或点位数组无效`);
				// 添加一个默认的空点位，确保时间线不为空
				timelineNode.content = [
					{
						type: "travelPoint",
						attrs: {
							pointId: `point_default_${Date.now()}`,
							location: "默认地点",
							pointDate: new Date().toISOString(),
							images: [],
						},
						content: [
							{
								type: "paragraph",
								content: [],
							},
						],
					},
				];
				console.log(`为时间线 #${timelineIndex} 添加了默认点位`);
			}

			// 将时间线节点添加到文档内容
			content.push(timelineNode);
			console.log(
				`时间线 #${timelineIndex} 节点已添加，包含 ${timelineNode.content?.length || 0} 个点位`,
			);
		});
	} else {
		console.log("日记没有时间线或时间线数组无效");
	}

	const result = { type: "doc", content };
	console.log(
		"convertDiaryDataToTiptapDoc - 转换完成，生成节点数:",
		result.content.length,
		"节点类型分布:",
		result.content.map((node) => node.type),
	);
	return result;
}

/**
 * 将 Tiptap 文档转换回前端时间线数据
 */
export function convertTiptapDocToDiaryTimelines(
	doc: JSONContent | null | undefined,
): FrontendTravelTimeline[] {
	if (!doc || !doc.content || !Array.isArray(doc.content)) {
		return [];
	}

	const timelines: FrontendTravelTimeline[] = [];
	let currentTimeline: FrontendTravelTimeline | null = null;
	let mainTitle = "";
	let subtitle = "";

	// 第一次循环：提取标题和副标题
	for (let i = 0; i < doc.content.length; i++) {
		const node = doc.content[i];

		// 提取主标题（H1）
		if (node.type === "heading" && node.attrs?.level === 1) {
			mainTitle = extractTextFromContent(node.content);

			// 副标题通常是主标题后的第一个段落
			if (
				i + 1 < doc.content.length &&
				doc.content[i + 1].type === "paragraph"
			) {
				subtitle = extractTextFromContent(doc.content[i + 1].content);
			}
			break;
		}
	}

	// 第二次循环：提取时间线和点位
	for (let i = 0; i < doc.content.length; i++) {
		const node = doc.content[i];

		// 优先查找自定义的travelTimeline节点类型
		if (node.type === "travelTimeline") {
			const timelineId =
				node.attrs?.timelineId ||
				`timeline_${Date.now()}_${Math.random().toString(36).slice(2)}`;
			const title = node.attrs?.title || "未命名时间线";
			const timelineDate = node.attrs?.timelineDate
				? new Date(node.attrs.timelineDate)
				: new Date();

			// 创建时间线对象
			currentTimeline = {
				id: timelineId,
				title,
				date: timelineDate,
				points: [],
			};

			console.log(`找到travelTimeline节点: ${title}, ID: ${timelineId}`);

			// 处理时间线子节点中的点位
			if (node.content && Array.isArray(node.content)) {
				node.content.forEach((childNode, pointIndex) => {
					if (childNode.type === "travelPoint" && currentTimeline) {
						const attrs = childNode.attrs || {};
						const description =
							extractTextFromContent(childNode.content) || "";

						try {
							const point: FrontendTravelPoint = {
								id:
									attrs.pointId ||
									`point_${Date.now()}_${pointIndex}`,
								timelineId: currentTimeline.id,
								title: attrs.location || "未命名地点",
								description,
								latitude: 0,
								longitude: 0,
								coordinates: {
									lat: 0,
									lng: 0,
								},
								iconType: "PIN",
								timestamp: attrs.pointDate
									? new Date(attrs.pointDate)
									: new Date(),
								images: Array.isArray(attrs.images)
									? attrs.images
									: [],
								address: null,
							};

							currentTimeline.points.push(point);
							console.log(
								`- 添加点位: ${point.title}, ID: ${point.id}`,
							);
						} catch (e) {
							console.error("处理travelPoint节点失败:", e);
						}
					}
				});
			}

			timelines.push(currentTimeline);
			currentTimeline = null;
		}
		// 向后兼容：如果是二级标题，可能是旧格式的时间线
		else if (node.type === "heading" && node.attrs?.level === 2) {
			const title =
				extractTextFromContent(node.content) || "未命名时间线";

			// 检查下一个节点是否是时间线日期
			let timelineDate = new Date();
			if (
				i + 1 < doc.content.length &&
				doc.content[i + 1].type === "paragraph" &&
				doc.content[i + 1].attrs?.class === "timeline-date"
			) {
				const dateText = extractTextFromContent(
					doc.content[i + 1].content,
				);
				if (dateText?.includes(":")) {
					const datePart = dateText.split(":")[1].trim();
					// 尝试解析日期
					try {
						timelineDate = new Date(datePart);
						if (Number.isNaN(timelineDate.getTime())) {
							timelineDate = new Date(); // 如果解析失败，使用当前日期
						}
					} catch (e) {
						console.error("日期解析错误:", e);
					}
				}
			}

			// 创建新的时间线
			currentTimeline = {
				id: `timeline_${Date.now()}_${Math.random().toString(36).slice(2)}`,
				title,
				date: timelineDate,
				points: [],
			};
			timelines.push(currentTimeline);
			console.log(`找到二级标题时间线: ${title}`);
		}
		// 如果是旅行点位节点，添加到当前时间线
		else if (node.type === "travelPoint") {
			// 如果没有当前时间线，创建一个默认的
			if (!currentTimeline) {
				currentTimeline = {
					id: `timeline_default_${Date.now()}`,
					title: "默认时间线",
					date: new Date(),
					points: [],
				};
				timelines.push(currentTimeline);
				console.log("为孤立的travelPoint创建默认时间线");
			}

			const attrs = node.attrs || {};
			const description = extractTextFromContent(node.content) || "";

			try {
				const point: FrontendTravelPoint = {
					id:
						attrs.pointId ||
						`point_${Date.now()}_${Math.random().toString(36).slice(2)}`,
					timelineId: currentTimeline.id,
					title: attrs.location || "未命名地点",
					description,
					latitude: 0,
					longitude: 0,
					coordinates: {
						lat: 0,
						lng: 0,
					},
					iconType: "PIN",
					timestamp: attrs.pointDate
						? new Date(attrs.pointDate)
						: new Date(),
					images: Array.isArray(attrs.images) ? attrs.images : [],
					address: null,
				};

				currentTimeline.points.push(point);
				console.log(
					`添加顶级travelPoint: ${point.title}, ID: ${point.id}`,
				);
			} catch (e) {
				console.error("点位数据处理错误:", e);
			}
		}
	}

	return timelines;
}

/**
 * 从Tiptap文档数据完整重建FrontendTravelDiary结构
 */
export function convertTiptapToFullDiary(
	doc: JSONContent | null | undefined,
	originalDiary?: FrontendTravelDiary,
): FrontendTravelDiary | null {
	if (!doc || !doc.content) {
		return null;
	}

	let title = "我的旅行日记";
	let subtitle = "";

	// 提取标题和副标题
	for (let i = 0; i < doc.content.length; i++) {
		const node = doc.content[i];
		if (node.type === "heading" && node.attrs?.level === 1) {
			title = extractTextFromContent(node.content) || title;

			// 检查下一个节点是否是副标题段落
			if (
				i + 1 < doc.content.length &&
				doc.content[i + 1].type === "paragraph"
			) {
				subtitle =
					extractTextFromContent(doc.content[i + 1].content) ||
					subtitle;
			}
			break;
		}
	}

	// 提取时间线
	const timelines = convertTiptapDocToDiaryTimelines(doc);

	// 创建或更新日记对象
	return {
		id: originalDiary?.id || `diary_${Date.now()}`,
		title,
		subtitle,
		coverImage: originalDiary?.coverImage,
		timelines,
		isPublic: originalDiary?.isPublic ?? false,
	};
}

/**
 * 从 Tiptap 内容中提取纯文本
 */
function extractTextFromContent(content?: JSONContent[]): string {
	if (!content || !Array.isArray(content)) return "";

	return content.reduce((text, node) => {
		if (!node) return text;

		if (node.type === "text" && typeof node.text === "string") {
			return text + node.text;
		}
		if (node.content && Array.isArray(node.content)) {
			return text + extractTextFromContent(node.content);
		}
		return text;
	}, "");
}
