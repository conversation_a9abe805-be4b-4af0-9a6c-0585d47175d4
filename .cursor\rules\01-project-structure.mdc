---
description: 
globs: 
alwaysApply: true
---
# Project Structure Guide

This is a monorepo project using pnpm workspaces. The main structure is organized as follows:

## Core Directories
- `packages/app`: Next.js App Router application (frontend)
- `packages/ai`: AI-related functionality
- `packages/api`: API routes and endpoints
- `packages/auth`: Authentication configuration
- `packages/database`: Database schema and types
- `packages/i18n`: Internationalization
- `packages/logs`: Logging configuration
- `packages/mail`: Email functionality
- `packages/payments`: Payment processing
- `packages/storage`: File storage
- `packages/utils`: Shared utilities

## Key Files
- [package.json](mdc:package.json): Root package configuration
- [pnpm-workspace.yaml](mdc:pnpm-workspace.yaml): Workspace configuration
- [tsconfig.json](mdc:tsconfig.json): TypeScript configuration

## Directory Naming Conventions
- Use lowercase with dashes for directories (e.g., `auth-wizard`)
- Component directories should be descriptive and follow the pattern: `components/feature-name`
- Page directories should match the URL structure

## File Organization
- Keep related files close together
- Group by feature rather than type
- Use index files for cleaner imports
- Place tests next to the files they test

## Database Operation
- migrate changes, execute the command like: `pnpm --filter database migrate --name add_status_field_for_logical_delete`
- push changes to the database: `pnpm --filter database push`
- generate prisma client: `pnpm --filter database generate`

