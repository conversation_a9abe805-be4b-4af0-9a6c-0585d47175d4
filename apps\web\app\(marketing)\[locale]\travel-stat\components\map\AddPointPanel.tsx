"use client";

import type { TravelPointImage } from "@repo/database/src/types/travel-diary";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import { Textarea } from "@ui/components/textarea";
import {
	CalendarIcon,
	Camera,
	ChevronDown,
	ChevronUp,
	FileText,
	MapPin,
	Upload,
	X,
} from "lucide-react";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useTravelStatTranslations } from "../../hooks/useTravelStatTranslations";
import type { GeocodeFeature } from "../../types";
import { imageStorage } from "../../utils/imageStorage";

interface AddPointFormData {
	description: string;
	date: string;
	image: TravelPointImage | null;
}

interface AddPointPanelProps {
	selectedLocation: GeocodeFeature | null;
	isExpanded: boolean;
	onSubmit: (data: AddPointFormData, location: GeocodeFeature) => void;
	onClearLocation: () => void;
}

// LocalStorage key for saving details panel state
const DETAILS_PANEL_STORAGE_KEY = "travel-stat-details-panel-expanded";

export function AddPointPanel({
	selectedLocation,
	isExpanded,
	onSubmit,
	onClearLocation,
}: AddPointPanelProps) {
	// 翻译 Hook
	const t = useTravelStatTranslations();

	// 表单状态
	const [formData, setFormData] = useState<AddPointFormData>({
		description: "",
		date: new Date().toISOString().split("T")[0],
		image: null,
	});

	// UI状态 - 初始化时从localStorage读取状态，默认为true（展开）
	const [imagePreview, setImagePreview] = useState<string>("");
	const [isUploading, setIsUploading] = useState(false);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [showDetails, setShowDetails] = useState(() => {
		// 尝试从localStorage读取保存的状态，默认为true（展开）
		if (typeof window !== "undefined") {
			try {
				const saved = localStorage.getItem(DETAILS_PANEL_STORAGE_KEY);
				return saved !== null ? JSON.parse(saved) : true;
			} catch {
				return true; // 解析失败时默认展开
			}
		}
		return true; // SSR时默认展开
	});

	// 文件输入引用
	const fileInputRef = useRef<HTMLInputElement>(null);
	const panelRef = useRef<HTMLDivElement>(null);

	// 保存详细信息面板状态到localStorage
	const saveDetailsState = useCallback((expanded: boolean) => {
		if (typeof window !== "undefined") {
			try {
				localStorage.setItem(
					DETAILS_PANEL_STORAGE_KEY,
					JSON.stringify(expanded),
				);
			} catch (error) {
				console.warn("无法保存详细信息面板状态:", error);
			}
		}
	}, []);

	// 切换详细信息面板状态
	const toggleDetails = useCallback(() => {
		setShowDetails((prev) => {
			const newState = !prev;
			saveDetailsState(newState);
			return newState;
		});
	}, [saveDetailsState]);

	// 重置表单 - 不重置详细信息面板状态
	const resetForm = useCallback(() => {
		setFormData({
			description: "",
			date: new Date().toISOString().split("T")[0],
			image: null,
		});
		setImagePreview("");
		// 移除 setShowDetails(false) - 保持用户设定的状态
		if (fileInputRef.current) {
			fileInputRef.current.value = "";
		}
	}, []);

	// 处理图片上传 - 使用新的imageStorage
	const handleImageChange = useCallback(
		async (e: React.ChangeEvent<HTMLInputElement>) => {
			const file = e.target.files?.[0];
			if (!file) return;

			// 验证文件类型
			if (!file.type.startsWith("image/")) {
				alert(t.addPointPanel.selectImageFile());
				return;
			}

			// 验证文件大小 (20MB)
			if (file.size > 20 * 1024 * 1024) {
				alert(t.addPointPanel.imageTooLarge());
				return;
			}

			setIsUploading(true);

			try {
				// 🔧 修复：首先初始化数据库，确保对象存储存在
				console.log("🔄 初始化IndexedDB数据库...");
				const isInitialized = await imageStorage.initializeDatabase();
				if (!isInitialized) {
					throw new Error("IndexedDB初始化失败");
				}

				// 创建预览URL（临时显示）
				const previewUrl = URL.createObjectURL(file);
				setImagePreview(previewUrl);

				// 使用新的imageStorage存储文件
				const tempPointId = `temp_${Date.now()}`;
				console.log("🔄 存储文件到IndexedDB...");
				const fileId = await imageStorage.storeFile(file, tempPointId);

				// 创建图片数据对象（只存储文件ID引用）
				const imageData: TravelPointImage = {
					url: `indexeddb:${fileId}`, // 特殊前缀表示这是IndexedDB引用
					description: selectedLocation?.place_name || "Travel photo",
					alt: selectedLocation?.place_name || "Travel photo",
					caption: formData.description,
					uploadedAt: new Date().toISOString(),
				};

				setFormData((prev) => ({
					...prev,
					image: imageData,
				}));

				console.log("✅ 图片处理成功 (ImageStorage):", {
					fileId,
					fileName: file.name,
					fileSize: `${(file.size / 1024 / 1024).toFixed(2)}MB`,
					previewUrl: `${previewUrl.slice(0, 50)}...`,
				});
			} catch (error) {
				console.error("❌ 图片处理失败:", error);
				const errorMessage = error instanceof Error ? error.message : "未知错误";
				alert(`${t.addPointPanel.imageProcessFailed()}: ${errorMessage}`);
				
				// 如果存储失败，清理预览
				if (imagePreview?.startsWith("blob:")) {
					URL.revokeObjectURL(imagePreview);
				}
				setImagePreview("");
			} finally {
				setIsUploading(false);
			}
		},
		[selectedLocation?.place_name, formData.description, t.addPointPanel, imagePreview],
	);

	// 移除图片
	const handleRemoveImage = useCallback(() => {
		// 清理预览URL
		if (imagePreview?.startsWith("blob:")) {
			URL.revokeObjectURL(imagePreview);
		}

		setImagePreview("");
		setFormData((prev) => ({
			...prev,
			image: null,
		}));
		if (fileInputRef.current) {
			fileInputRef.current.value = "";
		}
	}, [imagePreview]);

	// 组件卸载时清理
	useEffect(() => {
		return () => {
			if (imagePreview?.startsWith("blob:")) {
				URL.revokeObjectURL(imagePreview);
			}
		};
	}, [imagePreview]);

	// 点击上传区域
	const handleUploadClick = useCallback(() => {
		fileInputRef.current?.click();
	}, []);

	// 统一添加处理（智能处理当前表单数据）
	const handleAdd = useCallback(async () => {
		if (!selectedLocation) return;

		setIsSubmitting(true);
		try {
			// 使用当前表单数据，如果没有填写详细信息就使用默认值
			await onSubmit(formData, selectedLocation);

			resetForm();
			onClearLocation();

			// 成功提示
			showSuccessToast(t.addPointPanel.addSuccess());
		} catch (error) {
			console.error("添加失败:", error);
			const errorMessage =
				error instanceof Error ? error.message : "未知错误";
			alert(t.addPointPanel.addFailed(errorMessage));
		} finally {
			setIsSubmitting(false);
		}
	}, [
		formData,
		selectedLocation,
		onSubmit,
		resetForm,
		onClearLocation,
		t.addPointPanel,
	]);

	// 成功提示
	const showSuccessToast = useCallback((message: string) => {
		const toast = document.createElement("div");
		toast.className =
			"fixed top-4 right-4 bg-gradient-to-r from-sky-400 to-sky-500 text-white px-4 py-2 rounded-lg shadow-xl z-[9999] text-sm";
		toast.textContent = message;
		document.body.appendChild(toast);

		setTimeout(() => {
			if (document.body.contains(toast)) {
				document.body.removeChild(toast);
			}
		}, 2000);
	}, []);

	// 检查是否有详细信息
	const hasDetails =
		formData.description ||
		formData.image ||
		formData.date !== new Date().toISOString().split("T")[0];

	return (
		<div
			ref={panelRef}
			className={`absolute top-full left-0 right-0 bg-gradient-to-br from-sky-50/95 to-blue-50/90 backdrop-blur-md shadow-2xl rounded-b-xl border-2 border-t-0 border-sky-200/80 dark:border-gray-600 dark:bg-gradient-to-br dark:from-gray-800/95 dark:to-gray-900/90 z-20 overflow-hidden transition-all duration-300 ease-in-out ${
				isExpanded
					? "opacity-100 translate-y-0"
					: "opacity-0 -translate-y-4 pointer-events-none"
			}`}
		>
			{/* 核心内容区域 - 自适应高度 */}
			<div className="p-4 space-y-4">
				{/* 选中位置显示 + 快速操作 */}
				{selectedLocation && (
					<div className="space-y-3">
						{/* 位置信息 */}
						<div className="p-3 bg-gradient-to-r from-sky-50 to-blue-50/80 rounded-lg border border-sky-200/80 shadow-sm">
							<div className="flex items-start justify-between">
								<div className="flex items-start gap-2 flex-1 min-w-0">
									<MapPin className="w-4 h-4 text-sky-600 mt-0.5 flex-shrink-0" />
									<div className="min-w-0 flex-1">
										<p className="text-xs font-medium text-sky-800 mb-0.5">
											{t.addPointPanel.selectedLocation()}
										</p>
										<p className="text-sm text-sky-700 leading-tight break-words">
											{selectedLocation.place_name}
										</p>
									</div>
								</div>
								<Button
									variant="ghost"
									size="sm"
									onClick={onClearLocation}
									className="h-6 w-6 p-0 text-sky-500 hover:text-sky-700 hover:bg-sky-100 rounded-md flex-shrink-0 ml-2"
								>
									<X className="w-3 h-3" />
								</Button>
							</div>
						</div>

						{/* 添加按钮 */}
						<Button
							onClick={handleAdd}
							disabled={isSubmitting}
							className="w-full h-10 bg-gradient-to-r from-sky-500 to-sky-600 hover:from-sky-600 hover:to-sky-700 text-white font-medium rounded-lg shadow-md hover:shadow-lg transition-all duration-200"
						>
							{isSubmitting ? (
								<div className="flex items-center gap-2">
									<div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
									<span className="text-sm">
										{t.addPointPanel.adding()}
									</span>
								</div>
							) : (
								<div className="flex items-center gap-2">
									<MapPin className="w-4 h-4" />
									<span className="text-sm">
										{t.addPointPanel.addToMap()}
									</span>
								</div>
							)}
						</Button>
					</div>
				)}

				{/* 详细信息切换 */}
				<div className="border-t border-sky-200/50 pt-3">
					<button
						type="button"
						onClick={toggleDetails}
						className="w-full flex items-center justify-between py-2 px-3 text-sm font-medium text-sky-700 hover:text-sky-800 hover:bg-sky-50/50 rounded-lg transition-colors"
					>
						<span className="flex items-center gap-2">
							<FileText className="w-4 h-4" />
							{t.addPointPanel.addDetailedInfo()}
							{hasDetails && (
								<span className="w-2 h-2 bg-sky-500 rounded-full" />
							)}
						</span>
						{showDetails ? (
							<ChevronUp className="w-4 h-4" />
						) : (
							<ChevronDown className="w-4 h-4" />
						)}
					</button>

					{/* 详细信息表单 */}
					{showDetails && (
						<div className="mt-3 space-y-3">
							{/* 日期选择 - 紧凑版 */}
							<div className="space-y-2">
								<Label
									htmlFor="date"
									className="text-xs font-medium text-gray-700 flex items-center gap-1.5"
								>
									<CalendarIcon className="w-3.5 h-3.5 text-sky-500" />
									{t.addPointPanel.date()}
								</Label>
								<Input
									id="date"
									type="date"
									value={formData.date}
									onChange={(e) =>
										setFormData((prev) => ({
											...prev,
											date: e.target.value,
										}))
									}
									className="h-9 text-sm bg-white/80 border border-sky-200/60 rounded-lg focus:border-sky-400 transition-colors"
								/>
							</div>

							{/* 照片上传 - 紧凑版 */}
							<div className="space-y-2">
								<Label className="text-xs font-medium text-gray-700 flex items-center gap-1.5">
									<Camera className="w-3.5 h-3.5 text-sky-500" />
									{t.addPointPanel.photo()}
								</Label>

								{/* 隐藏的文件输入 */}
								<input
									ref={fileInputRef}
									type="file"
									accept="image/*"
									onChange={handleImageChange}
									className="hidden"
								/>

								{imagePreview ? (
									/* 有图片时显示预览 */
									<div className="relative group">
										<div className="relative overflow-hidden rounded-lg border border-sky-200/80 shadow-sm">
											<img
												src={imagePreview}
												alt={t.addPointPanel.preview()}
												className="w-full h-24 object-cover transition-transform duration-300 group-hover:scale-105"
											/>
											<div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
										</div>
										<Button
											type="button"
											variant="error"
											size="sm"
											onClick={handleRemoveImage}
											className="absolute top-1 right-1 h-6 w-6 p-0 bg-red-500/90 hover:bg-red-600"
										>
											<X className="w-3 h-3" />
										</Button>
									</div>
								) : (
									/* 无图片时显示上传区域 */
									<Button
										type="button"
										variant="outline"
										onClick={handleUploadClick}
										disabled={isUploading}
										className="w-full h-20 border border-dashed border-sky-300/60 rounded-lg hover:border-sky-400 hover:bg-sky-50/50 transition-all duration-200"
									>
										{isUploading ? (
											<div className="flex flex-col items-center gap-1">
												<div className="animate-spin rounded-full h-5 w-5 border-2 border-sky-500 border-t-transparent" />
												<span className="text-xs font-medium text-sky-700">
													{t.addPointPanel.uploading()}
												</span>
											</div>
										) : (
											<div className="flex flex-col items-center gap-1">
												<Upload className="w-5 h-5 text-sky-600" />
												<span className="text-xs font-medium text-sky-700">
													{t.addPointPanel.clickToUpload()}
												</span>
												<span className="text-xs text-sky-500">
													{t.addPointPanel.jpgPngMax20mb()}
												</span>
											</div>
										)}
									</Button>
								)}
							</div>

							{/* 描述输入 - 紧凑版 */}
							<div className="space-y-2">
								<Label
									htmlFor="description"
									className="text-xs font-medium text-gray-700 flex items-center gap-1.5"
								>
									<FileText className="w-3.5 h-3.5 text-sky-500" />
									{t.addPointPanel.description()}
								</Label>
								<Textarea
									id="description"
									placeholder={t.addPointPanel.shareYourFeelings()}
									value={formData.description}
									onChange={(e) =>
										setFormData((prev) => ({
											...prev,
											description: e.target.value,
										}))
									}
									className="h-16 resize-none text-sm bg-white/80 border border-sky-200/60 rounded-lg focus:border-sky-400 transition-colors"
									rows={3}
								/>
							</div>
						</div>
					)}
				</div>
			</div>
		</div>
	);
}
