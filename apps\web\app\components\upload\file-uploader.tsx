import { type ChangeEvent, useState } from "react";
import { toast } from "sonner";
import { Button } from "../../../modules/ui/components/button";

interface FileUploaderProps {
	bucket: string;
	path?: string; // 可选的固定路径，如果未提供将使用随机路径
	region?: string;
	onSuccess?: (result: { path: string; url?: string }) => void;
	onError?: (error: string) => void;
}

export function FileUploader({
	bucket,
	path,
	region,
	onSuccess,
	onError,
}: FileUploaderProps) {
	const [isUploading, setIsUploading] = useState(false);
	const [uploadProgress, setUploadProgress] = useState(0);

	const uploadFile = async (file: File) => {
		try {
			setIsUploading(true);
			setUploadProgress(10); // 开始上传

			// 生成随机文件名如果没有提供路径
			const filePath = path || `uploads/${Date.now()}-${file.name}`;

			// 创建 FormData
			const formData = new FormData();
			formData.append("bucket", bucket);
			formData.append("path", filePath);
			formData.append("file", file);
			if (region) formData.append("region", region);

			// 上传到服务器端 API
			setUploadProgress(30);
			const response = await fetch("/api/storage/upload-file", {
				method: "POST",
				body: formData,
			});

			setUploadProgress(90);
			const result = await response.json();

			if (!response.ok || !result.success) {
				throw new Error(result.error || "上传失败");
			}

			setUploadProgress(100);
			toast.success("文件上传成功");

			if (onSuccess) {
				onSuccess({
					path: result.path,
					url: result.url,
				});
			}
		} catch (error) {
			console.error("上传文件失败:", error);
			const errorMessage =
				error instanceof Error ? error.message : "上传过程中发生错误";

			toast.error(errorMessage);
			if (onError) onError(errorMessage);
		} finally {
			// 延迟一下，让进度条有时间显示 100%
			setTimeout(() => {
				setIsUploading(false);
				setUploadProgress(0);
			}, 500);
		}
	};

	const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		if (file) {
			uploadFile(file);
		}
	};

	return (
		<div className="flex flex-col gap-2">
			<div className="flex items-center gap-2">
				<Button
					variant="outline"
					onClick={() =>
						document.getElementById("file-upload")?.click()
					}
					disabled={isUploading}
				>
					{isUploading ? "上传中..." : "选择文件"}
				</Button>
				<input
					id="file-upload"
					type="file"
					onChange={handleFileChange}
					className="hidden"
					disabled={isUploading}
				/>
			</div>

			{isUploading && (
				<div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
					<div
						className="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
						style={{ width: `${uploadProgress}%` }}
					></div>
				</div>
			)}
		</div>
	);
}
