"use client";
import { config } from "@repo/config";
import { useSession } from "@saas/auth/hooks/use-session";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { UserMenu } from "@saas/shared/components/UserMenu";
import { Logo } from "@shared/components/Logo";
import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import {
	ChevronLeftIcon,
	ChevronRightIcon,
	HomeIcon,
	MenuIcon,
	SettingsIcon,
	UserCog2Icon,
	UserCogIcon,
} from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { OrganzationSelect } from "../../organizations/components/OrganizationSelect";

// 创建一个全局状态存储，用于在组件之间共享侧边栏收缩状态
import { create } from "zustand";

interface SidebarState {
	isCollapsed: boolean;
	toggleCollapsed: () => void;
	setCollapsed: (collapsed: boolean) => void;
}

export const useSidebarStore = create<SidebarState>((set) => ({
	isCollapsed: false,
	toggleCollapsed: () =>
		set((state) => ({ isCollapsed: !state.isCollapsed })),
	setCollapsed: (collapsed) => set({ isCollapsed: collapsed }),
}));

export function NavBar() {
	const t = useTranslations();
	const pathname = usePathname();
	const { user } = useSession();
	const { activeOrganization } = useActiveOrganization();
	const { isCollapsed, toggleCollapsed, setCollapsed } = useSidebarStore();
	const [isMobile, setIsMobile] = useState(false);

	// 检测窗口大小变化
	useEffect(() => {
		const checkMobile = () => {
			setIsMobile(window.innerWidth < 768);
			// 在移动设备上默认收起侧边栏
			if (window.innerWidth < 768) {
				setCollapsed(true);
			}
		};

		// 初始检查
		checkMobile();

		// 监听窗口大小变化
		window.addEventListener("resize", checkMobile);

		return () => {
			window.removeEventListener("resize", checkMobile);
		};
	}, [setCollapsed]);

	const { useSidebarLayout } = config.ui.saas;

	const orgBasePath = activeOrganization
		? `/app/${activeOrganization.slug}`
		: "/app";

	const menuItems = [
		{
			label: t("app.menu.dashboard"),
			href: "/app",
			icon: HomeIcon,
			isActive: pathname === "/app",
		},
		// {
		// 	label: t("app.menu.aiChatbot"),
		// 	href: activeOrganization
		// 		? `/app/${activeOrganization.slug}/chatbot`
		// 		: "/app/chatbot",
		// 	icon: BotMessageSquareIcon,
		// 	isActive: pathname.includes("/chatbot"),
		// },
		...(activeOrganization
			? [
					{
						label: t("app.menu.organizationSettings"),
						href: `${orgBasePath}/settings`,
						icon: SettingsIcon,
						isActive: pathname.startsWith(
							`${orgBasePath}/settings/`,
						),
					},
				]
			: [
					{
						label: t("app.menu.accountSettings"),
						href: "/app/settings",
						icon: UserCog2Icon,
						isActive: pathname.startsWith("/app/settings/"),
					},
				]),
		...(user?.role === "admin"
			? [
					{
						label: t("app.menu.admin"),
						href: "/app/admin",
						icon: UserCogIcon,
						isActive: pathname.startsWith("/app/admin/"),
					},
				]
			: []),
	];

	// 只有启用侧边栏布局时才显示侧边栏收缩功能
	const showSidebarCollapse = useSidebarLayout && !isMobile;
	// 移动设备上显示汉堡菜单按钮
	const showMobileMenuButton = useSidebarLayout && isMobile;

	return (
		<nav
			className={cn(
				"w-full border-b transition-all duration-300 md:relative",
				{
					"w-full md:fixed md:top-0 md:left-0 md:h-full md:border-r md:border-b-0":
						useSidebarLayout,
					"md:w-[280px]": useSidebarLayout && !isCollapsed,
					"md:w-[72px]": useSidebarLayout && isCollapsed,
					"translate-x-0": !isMobile || (isMobile && !isCollapsed),
					"-translate-x-full": isMobile && isCollapsed,
				},
			)}
		>
			{/* 移动端汉堡菜单按钮，固定在屏幕左侧 */}
			{showMobileMenuButton && (
				<Button
					variant="ghost"
					size="icon"
					onClick={toggleCollapsed}
					className="fixed top-4 left-4 z-50 md:hidden"
				>
					<MenuIcon className="size-5" />
				</Button>
			)}

			<div
				className={cn(
					"container py-4 transition-all duration-300",
					{ "max-w-6xl": !isCollapsed },
					useSidebarLayout &&
						"md:flex md:h-full md:flex-col md:pt-6 md:pb-0",
					useSidebarLayout && !isCollapsed && "md:px-6",
					useSidebarLayout && isCollapsed && "md:px-2",
				)}
			>
				<div className="flex flex-wrap items-center justify-between gap-4">
					<div
						className={cn("flex items-center gap-4 md:gap-2", {
							"md:flex md:w-full md:flex-col md:items-stretch md:align-stretch":
								useSidebarLayout && !isCollapsed,
							"md:items-center": useSidebarLayout && isCollapsed,
						})}
					>
						<Link href="/app" className="block">
							<Logo
								withLabel={!isCollapsed}
								className={isCollapsed ? "mx-auto" : ""}
							/>
						</Link>

						{config.organizations.enable &&
							!config.organizations.hideOrganization &&
							!isCollapsed && (
								<>
									<span
										className={cn(
											"hidden opacity-30 md:block",
											{
												"md:hidden": useSidebarLayout,
											},
										)}
									>
										<ChevronRightIcon className="size-4" />
									</span>

									<OrganzationSelect
										className={cn({
											"md:-mx-2 md:mt-2":
												useSidebarLayout,
										})}
									/>
								</>
							)}
					</div>

					{/* 侧边栏收缩按钮 */}
					{showSidebarCollapse && (
						<Button
							variant="ghost"
							size="icon"
							onClick={toggleCollapsed}
							className={cn(
								"hidden md:flex absolute top-5 hover:bg-muted/50",
								{
									"right-3": !isCollapsed,
									"left-[58px] right-auto": isCollapsed,
								},
							)}
						>
							{isCollapsed ? (
								<ChevronRightIcon className="size-4" />
							) : (
								<ChevronLeftIcon className="size-4" />
							)}
						</Button>
					)}

					<div
						className={cn(
							"mr-0 ml-auto flex items-center justify-end gap-4",
							{
								"md:hidden": useSidebarLayout,
							},
						)}
					>
						<UserMenu />
					</div>
				</div>

				<ul
					className={cn(
						"no-scrollbar -mx-4 -mb-4 mt-6 flex list-none items-center justify-start gap-4 overflow-x-auto px-4 text-sm",
						{
							"md:mx-0 md:my-4 md:flex md:flex-col md:items-stretch md:gap-1 md:px-0":
								useSidebarLayout && !isCollapsed,
							"md:mx-0 md:my-4 md:flex md:flex-col md:items-center md:justify-center md:gap-6 md:px-0":
								useSidebarLayout && isCollapsed,
						},
					)}
				>
					{menuItems.map((menuItem) => (
						<li
							key={menuItem.href}
							className={isCollapsed ? "md:w-full md:px-2" : ""}
						>
							<Link
								href={menuItem.href}
								className={cn(
									"flex items-center gap-2 whitespace-nowrap border-b-2 px-1 pb-3",
									[
										menuItem.isActive
											? "border-primary font-bold"
											: "border-transparent",
									],
									{
										"md:-mx-6 md:border-b-0 md:border-l-2 md:px-6 md:py-2":
											useSidebarLayout && !isCollapsed,
										"md:flex md:w-full md:justify-center md:border-b-0 md:border-l-0 md:px-0 md:py-2 md:hover:bg-muted/50 md:rounded-md":
											useSidebarLayout && isCollapsed,
									},
								)}
								title={isCollapsed ? menuItem.label : undefined}
							>
								<menuItem.icon
									className={`size-5 shrink-0 ${
										menuItem.isActive
											? "text-primary"
											: "opacity-50"
									}`}
								/>
								<span
									className={isCollapsed ? "md:hidden" : ""}
								>
									{menuItem.label}
								</span>
							</Link>
						</li>
					))}
				</ul>

				<div
					className={cn(
						"-mx-4 md:-mx-6 mt-auto mb-0 hidden p-4 md:p-4",
						{
							"md:block": useSidebarLayout && !isCollapsed,
							"md:hidden": isCollapsed,
						},
					)}
				>
					<UserMenu showUserName />
				</div>
			</div>
		</nav>
	);
}
