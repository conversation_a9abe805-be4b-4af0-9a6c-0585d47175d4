import type { ColorTheme, ColorThemeType } from "../types/colorTypes";

// 预定义的颜色主题配置
export const COLOR_THEMES: Record<ColorThemeType, ColorTheme> = {
	// 经典蓝绿色系 (原有配色)
	"classic-blue-green": {
		id: "classic-blue-green",
		name: "经典蓝绿",
		description: "从浅蓝色到深绿色的经典渐变，适合大多数地图样式",
		category: "classic",
		colors: {
			unvisited: {
				rgba: "rgba(200, 200, 200, 0.3)",
				hex: "#c8c8c8",
				description: "未访问",
			},
			level1: {
				rgba: "rgba(147, 197, 253, 0.6)",
				hex: "#93c5fd",
				description: "首次访问",
			},
			level2: {
				rgba: "rgba(59, 130, 246, 0.7)",
				hex: "#3b82f6",
				description: "二次访问",
			},
			level3: {
				rgba: "rgba(37, 99, 235, 0.8)",
				hex: "#2563eb",
				description: "多次访问",
			},
			level4: {
				rgba: "rgba(34, 197, 94, 0.7)",
				hex: "#22c55e",
				description: "常访问",
			},
			level5: {
				rgba: "rgba(22, 163, 74, 0.8)",
				hex: "#16a34a",
				description: "频繁访问",
			},
			level6to10: {
				rgba: "rgba(21, 128, 61, 0.85)",
				hex: "#15803d",
				description: "非常频繁",
			},
			level10plus: {
				rgba: "rgba(245, 158, 11, 0.9)",
				hex: "#f59e0b",
				description: "超高频访问",
			},
		},
		previewColors: ["#93c5fd", "#3b82f6", "#22c55e", "#f59e0b"],
		recommendedForMapStyles: ["streets", "outdoors", "light"],
	},

	// 暖色日落色系
	"warm-sunset": {
		id: "warm-sunset",
		name: "暖色日落",
		description: "从浅橙到深红的暖色调，避免与地图绿色冲突",
		category: "nature",
		colors: {
			unvisited: {
				rgba: "rgba(200, 200, 200, 0.3)",
				hex: "#c8c8c8",
				description: "未访问",
			},
			level1: {
				rgba: "rgba(254, 215, 170, 0.7)",
				hex: "#fed7aa",
				description: "首次访问",
			},
			level2: {
				rgba: "rgba(251, 146, 60, 0.8)",
				hex: "#fb923c",
				description: "二次访问",
			},
			level3: {
				rgba: "rgba(234, 88, 12, 0.8)",
				hex: "#ea580c",
				description: "多次访问",
			},
			level4: {
				rgba: "rgba(220, 38, 38, 0.8)",
				hex: "#dc2626",
				description: "常访问",
			},
			level5: {
				rgba: "rgba(185, 28, 28, 0.8)",
				hex: "#b91c1c",
				description: "频繁访问",
			},
			level6to10: {
				rgba: "rgba(153, 27, 27, 0.85)",
				hex: "#991b1b",
				description: "非常频繁",
			},
			level10plus: {
				rgba: "rgba(120, 53, 15, 0.9)",
				hex: "#78350f",
				description: "超高频访问",
			},
		},
		previewColors: ["#fed7aa", "#fb923c", "#dc2626", "#78350f"],
		recommendedForMapStyles: ["satellite", "outdoors", "dark"],
	},

	// 冷色海洋色系
	"cool-ocean": {
		id: "cool-ocean",
		name: "冷色海洋",
		description: "从浅青到深蓝的冷色调，避免与地图蓝色冲突",
		category: "nature",
		colors: {
			unvisited: {
				rgba: "rgba(200, 200, 200, 0.3)",
				hex: "#c8c8c8",
				description: "未访问",
			},
			level1: {
				rgba: "rgba(165, 243, 252, 0.7)",
				hex: "#a5f3fc",
				description: "首次访问",
			},
			level2: {
				rgba: "rgba(34, 211, 238, 0.7)",
				hex: "#22d3ee",
				description: "二次访问",
			},
			level3: {
				rgba: "rgba(14, 165, 233, 0.8)",
				hex: "#0ea5e9",
				description: "多次访问",
			},
			level4: {
				rgba: "rgba(8, 47, 73, 0.8)",
				hex: "#082f49",
				description: "常访问",
			},
			level5: {
				rgba: "rgba(12, 74, 110, 0.8)",
				hex: "#0c4a6e",
				description: "频繁访问",
			},
			level6to10: {
				rgba: "rgba(7, 89, 133, 0.85)",
				hex: "#075985",
				description: "非常频繁",
			},
			level10plus: {
				rgba: "rgba(30, 41, 59, 0.9)",
				hex: "#1e293b",
				description: "超高频访问",
			},
		},
		previewColors: ["#a5f3fc", "#22d3ee", "#0c4a6e", "#1e293b"],
		recommendedForMapStyles: ["streets", "light", "navigation-day"],
	},

	// 彩虹色系
	"vibrant-rainbow": {
		id: "vibrant-rainbow",
		name: "活力彩虹",
		description: "每个级别使用不同的鲜明颜色，高辨识度",
		category: "vibrant",
		colors: {
			unvisited: {
				rgba: "rgba(200, 200, 200, 0.3)",
				hex: "#c8c8c8",
				description: "未访问",
			},
			level1: {
				rgba: "rgba(239, 68, 68, 0.7)",
				hex: "#ef4444",
				description: "首次访问",
			},
			level2: {
				rgba: "rgba(249, 115, 22, 0.7)",
				hex: "#f97316",
				description: "二次访问",
			},
			level3: {
				rgba: "rgba(245, 158, 11, 0.8)",
				hex: "#f59e0b",
				description: "多次访问",
			},
			level4: {
				rgba: "rgba(34, 197, 94, 0.8)",
				hex: "#22c55e",
				description: "常访问",
			},
			level5: {
				rgba: "rgba(59, 130, 246, 0.8)",
				hex: "#3b82f6",
				description: "频繁访问",
			},
			level6to10: {
				rgba: "rgba(147, 51, 234, 0.85)",
				hex: "#9333ea",
				description: "非常频繁",
			},
			level10plus: {
				rgba: "rgba(219, 39, 119, 0.9)",
				hex: "#db2777",
				description: "超高频访问",
			},
		},
		previewColors: ["#ef4444", "#f97316", "#22c55e", "#9333ea"],
		recommendedForMapStyles: ["dark", "satellite"],
	},

	// 大地色系
	"earth-tones": {
		id: "earth-tones",
		name: "大地色彩",
		description: "棕褐色调，与自然地图样式完美融合",
		category: "nature",
		colors: {
			unvisited: {
				rgba: "rgba(200, 200, 200, 0.3)",
				hex: "#c8c8c8",
				description: "未访问",
			},
			level1: {
				rgba: "rgba(253, 230, 138, 0.7)",
				hex: "#fde68a",
				description: "首次访问",
			},
			level2: {
				rgba: "rgba(251, 191, 36, 0.7)",
				hex: "#fbbf24",
				description: "二次访问",
			},
			level3: {
				rgba: "rgba(217, 119, 6, 0.8)",
				hex: "#d97706",
				description: "多次访问",
			},
			level4: {
				rgba: "rgba(180, 83, 9, 0.8)",
				hex: "#b45309",
				description: "常访问",
			},
			level5: {
				rgba: "rgba(146, 64, 14, 0.8)",
				hex: "#92400e",
				description: "频繁访问",
			},
			level6to10: {
				rgba: "rgba(120, 53, 15, 0.85)",
				hex: "#78350f",
				description: "非常频繁",
			},
			level10plus: {
				rgba: "rgba(69, 26, 3, 0.9)",
				hex: "#451a03",
				description: "超高频访问",
			},
		},
		previewColors: ["#fde68a", "#fbbf24", "#92400e", "#451a03"],
		recommendedForMapStyles: ["outdoors", "satellite", "terrain"],
	},

	// 紫粉色系
	"purple-pink": {
		id: "purple-pink",
		name: "紫粉梦境",
		description: "从浅紫到深粉的优雅色调，现代感十足",
		category: "modern",
		colors: {
			unvisited: {
				rgba: "rgba(200, 200, 200, 0.3)",
				hex: "#c8c8c8",
				description: "未访问",
			},
			level1: {
				rgba: "rgba(233, 213, 255, 0.7)",
				hex: "#e9d5ff",
				description: "首次访问",
			},
			level2: {
				rgba: "rgba(196, 181, 253, 0.7)",
				hex: "#c4b5fd",
				description: "二次访问",
			},
			level3: {
				rgba: "rgba(147, 51, 234, 0.8)",
				hex: "#9333ea",
				description: "多次访问",
			},
			level4: {
				rgba: "rgba(126, 34, 206, 0.8)",
				hex: "#7e22ce",
				description: "常访问",
			},
			level5: {
				rgba: "rgba(236, 72, 153, 0.8)",
				hex: "#ec4899",
				description: "频繁访问",
			},
			level6to10: {
				rgba: "rgba(219, 39, 119, 0.85)",
				hex: "#db2777",
				description: "非常频繁",
			},
			level10plus: {
				rgba: "rgba(157, 23, 77, 0.9)",
				hex: "#9d174d",
				description: "超高频访问",
			},
		},
		previewColors: ["#e9d5ff", "#9333ea", "#ec4899", "#9d174d"],
		recommendedForMapStyles: ["dark", "streets", "navigation-night"],
	},

	// 单色系
	monochrome: {
		id: "monochrome",
		name: "黑白经典",
		description: "简约的黑白灰色调，适合简洁风格",
		category: "minimal",
		colors: {
			unvisited: {
				rgba: "rgba(200, 200, 200, 0.3)",
				hex: "#c8c8c8",
				description: "未访问",
			},
			level1: {
				rgba: "rgba(229, 231, 235, 0.7)",
				hex: "#e5e7eb",
				description: "首次访问",
			},
			level2: {
				rgba: "rgba(156, 163, 175, 0.7)",
				hex: "#9ca3af",
				description: "二次访问",
			},
			level3: {
				rgba: "rgba(107, 114, 128, 0.8)",
				hex: "#6b7280",
				description: "多次访问",
			},
			level4: {
				rgba: "rgba(75, 85, 99, 0.8)",
				hex: "#4b5563",
				description: "常访问",
			},
			level5: {
				rgba: "rgba(55, 65, 81, 0.8)",
				hex: "#374151",
				description: "频繁访问",
			},
			level6to10: {
				rgba: "rgba(31, 41, 55, 0.85)",
				hex: "#1f2937",
				description: "非常频繁",
			},
			level10plus: {
				rgba: "rgba(17, 24, 39, 0.9)",
				hex: "#111827",
				description: "超高频访问",
			},
		},
		previewColors: ["#e5e7eb", "#9ca3af", "#374151", "#111827"],
		recommendedForMapStyles: ["light", "streets", "navigation-day"],
	},

	// 高对比度
	"high-contrast": {
		id: "high-contrast",
		name: "高对比度",
		description: "强烈对比的颜色组合，易于区分",
		category: "vibrant",
		colors: {
			unvisited: {
				rgba: "rgba(200, 200, 200, 0.3)",
				hex: "#c8c8c8",
				description: "未访问",
			},
			level1: {
				rgba: "rgba(0, 0, 0, 0.7)",
				hex: "#000000",
				description: "首次访问",
			},
			level2: {
				rgba: "rgba(255, 255, 255, 0.8)",
				hex: "#ffffff",
				description: "二次访问",
			},
			level3: {
				rgba: "rgba(255, 0, 0, 0.8)",
				hex: "#ff0000",
				description: "多次访问",
			},
			level4: {
				rgba: "rgba(0, 255, 0, 0.8)",
				hex: "#00ff00",
				description: "常访问",
			},
			level5: {
				rgba: "rgba(0, 0, 255, 0.8)",
				hex: "#0000ff",
				description: "频繁访问",
			},
			level6to10: {
				rgba: "rgba(255, 255, 0, 0.85)",
				hex: "#ffff00",
				description: "非常频繁",
			},
			level10plus: {
				rgba: "rgba(255, 0, 255, 0.9)",
				hex: "#ff00ff",
				description: "超高频访问",
			},
		},
		previewColors: ["#000000", "#ff0000", "#0000ff", "#ff00ff"],
		recommendedForMapStyles: ["satellite", "dark"],
	},

	// 柔和糖果色
	"pastel-soft": {
		id: "pastel-soft",
		name: "柔和糖果",
		description: "温柔的马卡龙色调，视觉舒适",
		category: "modern",
		colors: {
			unvisited: {
				rgba: "rgba(240, 240, 240, 0.3)",
				hex: "#f0f0f0",
				description: "未访问",
			},
			level1: {
				rgba: "rgba(254, 226, 226, 0.7)",
				hex: "#fee2e2",
				description: "首次访问",
			},
			level2: {
				rgba: "rgba(254, 215, 170, 0.7)",
				hex: "#fed7aa",
				description: "二次访问",
			},
			level3: {
				rgba: "rgba(254, 240, 138, 0.7)",
				hex: "#fef08a",
				description: "多次访问",
			},
			level4: {
				rgba: "rgba(187, 247, 208, 0.7)",
				hex: "#bbf7d0",
				description: "常访问",
			},
			level5: {
				rgba: "rgba(186, 230, 253, 0.7)",
				hex: "#bae6fd",
				description: "频繁访问",
			},
			level6to10: {
				rgba: "rgba(221, 214, 254, 0.8)",
				hex: "#ddd6fe",
				description: "非常频繁",
			},
			level10plus: {
				rgba: "rgba(252, 207, 238, 0.8)",
				hex: "#fccfee",
				description: "超高频访问",
			},
		},
		previewColors: ["#fee2e2", "#fef08a", "#bae6fd", "#fccfee"],
		recommendedForMapStyles: ["light", "streets"],
	},

	// 霓虹亮色
	"neon-bright": {
		id: "neon-bright",
		name: "霓虹亮色",
		description: "荧光色调，科技感十足",
		category: "vibrant",
		colors: {
			unvisited: {
				rgba: "rgba(50, 50, 50, 0.3)",
				hex: "#323232",
				description: "未访问",
			},
			level1: {
				rgba: "rgba(0, 255, 255, 0.8)",
				hex: "#00ffff",
				description: "首次访问",
			},
			level2: {
				rgba: "rgba(0, 255, 127, 0.8)",
				hex: "#00ff7f",
				description: "二次访问",
			},
			level3: {
				rgba: "rgba(127, 255, 0, 0.8)",
				hex: "#7fff00",
				description: "多次访问",
			},
			level4: {
				rgba: "rgba(255, 255, 0, 0.8)",
				hex: "#ffff00",
				description: "常访问",
			},
			level5: {
				rgba: "rgba(255, 127, 0, 0.8)",
				hex: "#ff7f00",
				description: "频繁访问",
			},
			level6to10: {
				rgba: "rgba(255, 0, 127, 0.85)",
				hex: "#ff007f",
				description: "非常频繁",
			},
			level10plus: {
				rgba: "rgba(127, 0, 255, 0.9)",
				hex: "#7f00ff",
				description: "超高频访问",
			},
		},
		previewColors: ["#00ffff", "#7fff00", "#ff7f00", "#7f00ff"],
		recommendedForMapStyles: ["dark", "satellite", "navigation-night"],
	},
};

// 导出基础颜色主题配置（用于翻译工具）
export const COLOR_THEMES_BASE = COLOR_THEMES;

// 默认颜色主题
export const DEFAULT_COLOR_THEME: ColorThemeType = "classic-blue-green";

// 按类别分组的主题
export const THEMES_BY_CATEGORY = {
	classic: ["classic-blue-green", "monochrome"] as ColorThemeType[],
	modern: ["purple-pink", "pastel-soft"] as ColorThemeType[],
	nature: ["warm-sunset", "cool-ocean", "earth-tones"] as ColorThemeType[],
	vibrant: [
		"vibrant-rainbow",
		"high-contrast",
		"neon-bright",
	] as ColorThemeType[],
	minimal: ["monochrome"] as ColorThemeType[],
};
