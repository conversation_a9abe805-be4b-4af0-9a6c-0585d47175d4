import { auth } from "@repo/auth";
import { logger } from "@repo/logs";
import { Hono } from "hono";

export const authRouter = new Hono()
	.get("/*", async (c) => {
		try {
			return await auth.handler(c.req.raw);
		} catch (error) {
			logger.error("认证GET请求处理错误:", error);
			return new Response(JSON.stringify({ error: "认证处理失败" }), {
				status: 500,
				headers: { "Content-Type": "application/json" },
			});
		}
	})
	.post("/*", async (c) => {
		try {
			return await auth.handler(c.req.raw);
		} catch (error) {
			logger.error("认证POST请求处理错误:", error);
			return new Response(JSON.stringify({ error: "认证处理失败" }), {
				status: 500,
				headers: { "Content-Type": "application/json" },
			});
		}
	});
