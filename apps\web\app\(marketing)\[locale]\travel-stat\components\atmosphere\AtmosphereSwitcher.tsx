"use client";

import { <PERSON><PERSON> } from "@ui/components/button";
import { Card } from "@ui/components/card";
import {
	Cloud,
	Globe,
	Moon,
	Mountain,
	Sun,
	Sunset,
	Waves,
	X,
	Zap,
} from "lucide-react";
import { useEffect, useState } from "react";

// 大气层主题类型定义
export type AtmosphereTheme =
	| "day"
	| "night"
	| "sunset"
	| "dawn"
	| "aurora"
	| "deep-space"
	| "ocean"
	| "minimal";

// 大气层配置接口
export interface AtmosphereConfig {
	color: string;
	"high-color": string;
	"space-color": string;
	"star-intensity": number;
	"horizon-blend": number;
	range: [number, number];
	"vertical-range"?: [number, number];
}

// 大气层配置预设
export const ATMOSPHERE_CONFIGS: Record<AtmosphereTheme, AtmosphereConfig> = {
	day: {
		color: "white",
		"high-color": "#87CEEB",
		"space-color": "#E0F6FF",
		"star-intensity": 0.0,
		"horizon-blend": 0.1,
		range: [0.5, 10],
	},
	night: {
		color: "#1e293b",
		"high-color": "#0f172a",
		"space-color": "#020617",
		"star-intensity": 0.8,
		"horizon-blend": 0.3,
		range: [0.8, 12],
	},
	sunset: {
		color: "#f97316",
		"high-color": "#dc2626",
		"space-color": "#451a03",
		"star-intensity": 0.3,
		"horizon-blend": 0.4,
		range: [0.6, 8],
	},
	dawn: {
		color: "#fbbf24",
		"high-color": "#f59e0b",
		"space-color": "#fef3c7",
		"star-intensity": 0.1,
		"horizon-blend": 0.25,
		range: [0.5, 9],
	},
	aurora: {
		color: "#10b981",
		"high-color": "#059669",
		"space-color": "#064e3b",
		"star-intensity": 0.6,
		"horizon-blend": 0.5,
		range: [0.7, 15],
	},
	"deep-space": {
		color: "#1f2937",
		"high-color": "#111827",
		"space-color": "#000000",
		"star-intensity": 1.0,
		"horizon-blend": 0.8,
		range: [1.0, 20],
	},
	ocean: {
		color: "#0ea5e9",
		"high-color": "#0284c7",
		"space-color": "#bae6fd",
		"star-intensity": 0.0,
		"horizon-blend": 0.2,
		range: [0.4, 6],
	},
	minimal: {
		color: "#f8fafc",
		"high-color": "#e2e8f0",
		"space-color": "#ffffff",
		"star-intensity": 0.0,
		"horizon-blend": 0.05,
		range: [0.3, 5],
	},
};

// 主题名称
export const ATMOSPHERE_NAMES: Record<AtmosphereTheme, string> = {
	day: "白天",
	night: "夜晚",
	sunset: "日落",
	dawn: "黎明",
	aurora: "极光",
	"deep-space": "深空",
	ocean: "海洋",
	minimal: "简约",
};

// 主题图标映射
const THEME_ICONS = {
	day: Sun,
	night: Moon,
	sunset: Sunset,
	dawn: Cloud,
	aurora: Zap,
	"deep-space": Globe,
	ocean: Waves,
	minimal: Mountain,
} as const;

// 主题描述
const THEME_DESCRIPTIONS = {
	day: "明亮清新的白天天空",
	night: "深邃神秘的星空夜晚",
	sunset: "温暖绚烂的日落黄昏",
	dawn: "柔和清新的晨曦黎明",
	aurora: "绚丽多彩的极光夜空",
	"deep-space": "浩瀚无垠的深邃宇宙",
	ocean: "清澈湛蓝的海洋天空",
	minimal: "纯净优雅的极简风格",
} as const;

interface AtmosphereSwitcherProps {
	currentTheme: AtmosphereTheme;
	onThemeChange: (theme: AtmosphereTheme) => void;
	className?: string;
}

export function AtmosphereSwitcher({
	currentTheme,
	onThemeChange,
	className = "",
}: AtmosphereSwitcherProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [isMounted, setIsMounted] = useState(false);

	// 确保只在客户端渲染
	useEffect(() => {
		setIsMounted(true);
	}, []);

	const themes = Object.keys(ATMOSPHERE_NAMES) as AtmosphereTheme[];

	// 如果还没有挂载，显示简化版本
	if (!isMounted) {
		return (
			<Button
				variant="outline"
				size="sm"
				className="bg-white/90 backdrop-blur-sm border-sky-200 shadow-lg transition-all duration-200"
				disabled
			>
				<Globe className="w-4 h-4 mr-1" />
				<span className="text-xs">大气层</span>
			</Button>
		);
	}

	return (
		<div className={`relative ${className}`}>
			{/* 触发按钮 */}
			<Button
				onClick={() => setIsOpen(!isOpen)}
				variant="outline"
				size="sm"
				className="bg-white/90 backdrop-blur-sm border-sky-200 hover:bg-sky-50 shadow-lg transition-all duration-200"
				title="切换地图大气层效果"
			>
				<Globe className="w-4 h-4 mr-1" />
				<span className="text-xs">大气层</span>
			</Button>

			{/* 主题选择面板 */}
			{isOpen && (
				<>
					{/* 背景遮罩 */}
					<div
						className="fixed inset-0 z-40"
						onClick={() => setIsOpen(false)}
						onKeyDown={(e) => {
							if (e.key === "Escape") {
								setIsOpen(false);
							}
						}}
						role="button"
						tabIndex={0}
						aria-label="关闭大气层选择面板"
					/>

					{/* 主题面板 */}
					<Card className="absolute top-full left-0 mt-2 z-50 w-80 bg-white/95 backdrop-blur-sm border-sky-200 shadow-xl">
						<div className="p-4">
							<div className="flex items-center justify-between mb-3">
								<h3 className="text-sm font-semibold text-gray-800">
									选择大气层效果
								</h3>
								<Button
									onClick={() => setIsOpen(false)}
									variant="ghost"
									size="sm"
									className="h-6 w-6 p-0"
								>
									<X className="w-4 h-4" />
								</Button>
							</div>

							<div className="grid grid-cols-2 gap-2">
								{themes.map((theme) => {
									const Icon = THEME_ICONS[theme];
									const isActive = theme === currentTheme;
									const config = ATMOSPHERE_CONFIGS[theme];

									return (
										<button
											key={theme}
											type="button"
											onClick={() => {
												onThemeChange(theme);
												setIsOpen(false);
											}}
											className={`
												relative p-3 rounded-lg border-2 transition-all duration-200 text-left
												${
													isActive
														? "border-sky-400 bg-sky-50 shadow-md"
														: "border-gray-200 hover:border-sky-300 hover:bg-sky-25"
												}
											`}
										>
											{/* 激活指示器 */}
											{isActive && (
												<div className="absolute top-1 right-1 w-2 h-2 bg-sky-500 rounded-full" />
											)}

											{/* 颜色预览条 */}
											<div className="absolute top-2 right-2 flex gap-1">
												<div
													className="w-2 h-8 rounded-sm border border-white/50"
													style={{
														backgroundColor:
															config.color,
													}}
												/>
												<div
													className="w-2 h-8 rounded-sm border border-white/50"
													style={{
														backgroundColor:
															config[
																"high-color"
															],
													}}
												/>
												<div
													className="w-2 h-8 rounded-sm border border-white/50"
													style={{
														backgroundColor:
															config[
																"space-color"
															],
													}}
												/>
											</div>

											<div className="flex items-center gap-2 mb-1 pr-8">
												<Icon
													className={`w-4 h-4 ${isActive ? "text-sky-600" : "text-gray-500"}`}
												/>
												<span
													className={`text-sm font-medium ${isActive ? "text-sky-800" : "text-gray-700"}`}
												>
													{ATMOSPHERE_NAMES[theme]}
												</span>
											</div>

											<p
												className={`text-xs pr-8 ${isActive ? "text-sky-600" : "text-gray-500"}`}
											>
												{THEME_DESCRIPTIONS[theme]}
											</p>

											{/* 星星强度指示器 */}
											{config["star-intensity"] > 0 && (
												<div className="flex items-center gap-1 mt-2">
													{Array.from(
														{ length: 5 },
														(_, i) => (
															<div
																key={i}
																className={`w-1 h-1 rounded-full ${
																	i <
																	config[
																		"star-intensity"
																	] *
																		5
																		? "bg-yellow-400"
																		: "bg-gray-300"
																}`}
															/>
														),
													)}
													<span className="text-xs text-gray-500 ml-1">
														星空
													</span>
												</div>
											)}
										</button>
									);
								})}
							</div>

							{/* 当前主题详情 */}
							<div className="mt-4 p-3 bg-gradient-to-r from-sky-50 to-blue-50 rounded-lg border border-sky-200">
								<div className="flex items-center gap-2 mb-2">
									<Globe className="w-4 h-4 text-sky-500" />
									<span className="text-sm font-medium text-sky-800">
										当前大气层:{" "}
										{ATMOSPHERE_NAMES[currentTheme]}
									</span>
								</div>
								<p className="text-xs text-sky-600 mb-2">
									{THEME_DESCRIPTIONS[currentTheme]}
								</p>

								{/* 配置详情 */}
								<div className="grid grid-cols-2 gap-2 text-xs">
									<div className="text-sky-600">
										星星强度:{" "}
										{Math.round(
											ATMOSPHERE_CONFIGS[currentTheme][
												"star-intensity"
											] * 100,
										)}
										%
									</div>
									<div className="text-sky-600">
										混合度:{" "}
										{Math.round(
											ATMOSPHERE_CONFIGS[currentTheme][
												"horizon-blend"
											] * 100,
										)}
										%
									</div>
								</div>
							</div>
						</div>
					</Card>
				</>
			)}
		</div>
	);
}
