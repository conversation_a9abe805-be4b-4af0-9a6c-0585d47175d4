import { config } from "@repo/config";
import deepmerge from "deepmerge";
import type { Messages } from "../types";

export const importLocale = async (locale: string): Promise<Messages> => {
	return (await import(`../translations/${locale}.json`)).default as Messages;
};

export const getMessagesForLocale = async (
	locale: string,
): Promise<Messages> => {
	const localeMessages = await importLocale(locale);
	if (locale === config.i18n.defaultLocale) {
		return localeMessages;
	}
	const defaultLocaleMessages = await importLocale(config.i18n.defaultLocale);
	// 配置 deepmerge 以替换数组而不是合并数组，避免多语言内容混合
	return deepmerge(defaultLocaleMessages, localeMessages, {
		arrayMerge: (destinationArray, sourceArray) => sourceArray, // 使用源数组替换目标数组
	});
};
