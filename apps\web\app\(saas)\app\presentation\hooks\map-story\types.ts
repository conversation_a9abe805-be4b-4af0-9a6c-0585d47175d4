"use client";

import type mapboxgl from "mapbox-gl";
import type { RefObject } from "react";
import type { StoryState } from "../../constants";
import type { FrontendTravelPoint } from "../../types";

// 视频导出配置
export interface ExportConfig {
	isExportMode: boolean;
	autoPlay: boolean;
	pointDuration: number;
}

export interface UseMapStoryProps {
	points: FrontendTravelPoint[];
	onComplete: () => void;
	config?: ExportConfig;
}

// 核心状态类型
export interface MapStoryState {
	// 基础状态
	currentPointIndex: number;
	isPlaying: boolean;
	isMapReady: boolean;
	showPointInfo: boolean;
	showImages: boolean;
	lightboxOpen: boolean;
	selectedImageIndex: number;
	flyToPointIndex: number | null;
	fitBoundsToPoints: boolean;
	mapInstance: mapboxgl.Map | null;
	spotlightPosition: { x: number; y: number } | null;

	// 地图故事状态
	storyState: StoryState;
	blinkingPoints: number[];
	showStartPrompt: boolean;
	showRestartPrompt: boolean;
	autoPlayCountdown: number;
	manuallyReset: boolean;
	isFinishing: boolean;
	lastPointTimerSet: boolean;
	isTypingCompleted: boolean;
}

// 状态操作方法
export interface MapStorySetters {
	setCurrentPointIndex: (index: number) => void;
	setIsPlaying: (playing: boolean | ((prev: boolean) => boolean)) => void;
	setIsMapReady: (ready: boolean) => void;
	setShowPointInfo: (show: boolean) => void;
	setShowImages: (show: boolean) => void;
	setLightboxOpen: (open: boolean) => void;
	setSelectedImageIndex: (index: number | ((prev: number) => number)) => void;
	setFlyToPointIndex: (index: number | null) => void;
	setFitBoundsToPoints: (fit: boolean) => void;
	setMapInstance: (map: mapboxgl.Map | null) => void;
	setSpotlightPosition: (position: { x: number; y: number } | null) => void;

	setStoryState: (state: StoryState) => void;
	setBlinkingPoints: (points: number[]) => void;
	setShowStartPrompt: (show: boolean) => void;
	setShowRestartPrompt: (show: boolean) => void;
	setAutoPlayCountdown: (count: number | ((prev: number) => number)) => void;
	setManuallyReset: (reset: boolean) => void;
	setIsFinishing: (finishing: boolean) => void;
	setLastPointTimerSet: (set: boolean) => void;
	setIsTypingCompleted: (completed: boolean) => void;
}

// 引用类型
export interface MapStoryRefs {
	isAnimatingRef: RefObject<boolean>;
	currentPointDisplayTimeoutRef: RefObject<NodeJS.Timeout | null>;
	autoPlayTimerRef: RefObject<NodeJS.Timeout | null>;
	blinkTimerRef: RefObject<NodeJS.Timeout | null>;
	activeMarkerRef: RefObject<HTMLDivElement | null>;
	typingPromiseResolverRef: RefObject<(() => void) | null>;
}

// 动画控制方法
export interface MapStoryAnimation {
	finishPresentation: () => Promise<void>;
	advanceToPoint: (targetPointArrayIndex: number) => Promise<void>;
	updateSpotlightPosition: () => void;
}

// 计时器管理方法
export interface MapStoryTimers {
	clearPlayTimer: () => void;
	clearBlinkTimer: () => void;
}

// 导航控制方法
export interface MapStoryNavigation {
	handlePointArrival: (arrivedPoint: FrontendTravelPoint) => Promise<void>;
	goToNextPoint: () => Promise<void>;
	goToPrevPoint: () => Promise<void>;
	startJourney: () => Promise<void>;
	resetPresentation: () => void;
	viewMemories: () => void;
}

// 用户交互方法
export interface MapStoryInteraction {
	handleMarkerClick: (point: FrontendTravelPoint, index: number) => void;
	handleImageClick: (imageSrc: string, index: number) => void;
	handlePrevImage: () => void;
	handleNextImage: () => void;
	handleSliderChange: (values: number[]) => void;
	handleMapLoad: (map: mapboxgl.Map) => void;
	togglePlay: () => Promise<void>;
}

// 上下文类型（共享状态与方法）
export interface MapStoryContext {
	props: UseMapStoryProps;
	state: MapStoryState;
	setters: MapStorySetters;
	refs: MapStoryRefs;
}

// 带计时器的上下文
export interface MapStoryTimerContext extends MapStoryContext {
	timers: MapStoryTimers;
}

// 带动画的上下文
export interface MapStoryAnimationContext extends MapStoryTimerContext {
	animation: MapStoryAnimation;
}

// 带导航的上下文
export interface MapStoryNavigationContext extends MapStoryAnimationContext {
	navigation: MapStoryNavigation;
}

// 完整的返回类型
export interface MapStoryResult
	extends Pick<
			MapStoryState,
			| "storyState"
			| "isPlaying"
			| "isMapReady"
			| "currentPointIndex"
			| "showPointInfo"
			| "showImages"
			| "lightboxOpen"
			| "selectedImageIndex"
			| "flyToPointIndex"
			| "fitBoundsToPoints"
			| "spotlightPosition"
			| "blinkingPoints"
			| "autoPlayCountdown"
			| "showStartPrompt"
			| "showRestartPrompt"
			| "isFinishing"
			| "manuallyReset"
		>,
		Pick<
			MapStorySetters,
			"setLightboxOpen" | "setFlyToPointIndex" | "setMapInstance"
		>,
		MapStoryNavigation,
		MapStoryInteraction {
	// 返回当前点位和图片
	currentPoint: FrontendTravelPoint | null;
	currentImages: string[];

	// Refs
	activeMarkerRef: RefObject<HTMLDivElement | null>;

	// 打字完成回调
	onTypingComplete: () => void;
}
