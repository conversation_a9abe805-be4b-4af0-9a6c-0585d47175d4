import { Camera, Layers, Palette, Sparkles } from "lucide-react";
import React from "react";
import type {
	CardCustomization,
	CardTemplate,
	CustomizationOption,
	SocialPlatform,
} from "../types/cardTypes";
import { ElegantCard } from "./ElegantCard";
import { HandDrawnCard } from "./HandDrawnCard";
import { MinimalCard } from "./MinimalCard";
import { RetroCard } from "./RetroCard";
import { VibrantCard } from "./VibrantCard";

// 多语言工具函数类型
type TranslationFunction = (
	key: string,
	params?: Record<string, any>,
) => string;

// 创建组件包装器以避免直接引用组件
const createComponentWrapper = (Component: React.ComponentType<any>) =>
	Component;

// 获取多语言化的自定义选项
export const getCustomizationOptions = (
	t: TranslationFunction,
): CustomizationOption[] => {
	// 获取标题选项数组
	const titleOptionsResult = t("templates.customization.titleOptions");
	const titleOptions = Array.isArray(titleOptionsResult)
		? (titleOptionsResult as string[])
		: [
				t("templates.customization.titleOptions.0"),
				t("templates.customization.titleOptions.1"),
				t("templates.customization.titleOptions.2"),
				t("templates.customization.titleOptions.3"),
				t("templates.customization.titleOptions.4"),
			];

	return [
		{
			key: "colors.primary",
			label: t("templates.customization.labels.primaryColor"),
			type: "color",
			category: "colors",
			default: "#1f2937",
		},
		{
			key: "colors.accent",
			label: t("templates.customization.labels.accentColor"),
			type: "color",
			category: "colors",
			default: "#3b82f6",
		},
		{
			key: "typography.headerSize",
			label: t("templates.customization.labels.headerSize"),
			type: "slider",
			category: "typography",
			min: 20,
			max: 32,
			step: 2,
			default: 24,
		},
		{
			key: "layout.borderRadius",
			label: t("templates.customization.labels.borderRadius"),
			type: "slider",
			category: "layout",
			min: 0,
			max: 20,
			step: 2,
			default: 12,
		},
		{
			key: "content.showDetailedStats",
			label: t("templates.customization.labels.showDetailedStats"),
			type: "toggle",
			category: "content",
			default: true,
		},
		{
			key: "content.customTitle",
			label: t("templates.customization.labels.customTitle"),
			type: "select",
			category: "content",
			options: titleOptions,
			default:
				titleOptions[0] || t("templates.customization.titleOptions.0"),
		},
	];
};

// 默认自定义配置 - 针对现代社交媒体优化字体大小
const createDefaultCustomization = (
	overrides: Partial<CardCustomization> = {},
	t?: TranslationFunction,
): CardCustomization => ({
	colors: {
		primary: "#1f2937",
		secondary: "#6b7280",
		accent: "#3b82f6",
		background: "#ffffff",
		text: "#111827",
		...overrides.colors,
	},
	typography: {
		fontFamily:
			"'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
		headerSize: 36, // 大幅增加标题字体以提升可读性
		bodySize: 20, // 大幅增加正文字体以确保清晰可见
		titleWeight: 700,
		...overrides.typography,
	},
	layout: {
		padding: 32, // 增加内边距给文字更多呼吸空间
		spacing: 20, // 增加间距提升视觉层次
		borderRadius: 12,
		showShadow: true,
		...overrides.layout,
	},
	content: {
		showDate: true,
		showUserInfo: true,
		showDetailedStats: true,
		customTitle: t
			? t("templates.customization.titleOptions.5")
			: "✈️ 我的旅行足迹",
		customFooter: t
			? t("templates.customization.footerOptions.0")
			: "Map Moment · 记录每一次美好旅程",
		...overrides.content,
	},
});

// 模板配置 - 使用key而不是硬编码文本
export const cardTemplates: CardTemplate[] = [
	{
		id: "minimal",
		name: "minimal", // 改为key
		description: "minimal", // 改为key
		component: MinimalCard,
		preview: "/images/card-templates/minimal.png",
		category: "minimal",
		colors: {
			primary: "#3b82f6",
			secondary: "#10b981",
			accent: "#f59e0b",
			background: "#ffffff",
			text: "#111827",
		},
		typography: {
			fontFamily:
				"'Poppins', -apple-system, BlinkMacSystemFont, sans-serif",
			headerSize: 36,
			bodySize: 20,
			titleWeight: 700,
		},
		layout: {
			padding: 32,
			spacing: 20,
			borderRadius: 12,
			showShadow: true,
		},
		features: ["minimal"], // 改为key数组
		supportedPlatforms: [
			"instagram",
			"wechat",
			"weibo",
			"twitter",
			"facebook",
		],
	},
	{
		id: "vibrant",
		name: "vibrant",
		description: "vibrant",
		component: VibrantCard,
		preview: "/images/card-templates/vibrant.png",
		category: "vibrant",
		colors: {
			primary: "#ec4899",
			secondary: "#f97316",
			accent: "#8b5cf6",
			background: "#ffffff",
			text: "#111827",
		},
		typography: {
			fontFamily:
				"'Poppins', -apple-system, BlinkMacSystemFont, sans-serif",
			headerSize: 36,
			bodySize: 20,
			titleWeight: 700,
		},
		layout: {
			padding: 32,
			spacing: 20,
			borderRadius: 20,
			showShadow: true,
		},
		features: ["vibrant"],
		supportedPlatforms: [
			"instagram",
			"wechat",
			"weibo",
			"twitter",
			"facebook",
		],
	},
	{
		id: "elegant",
		name: "elegant",
		description: "elegant",
		component: ElegantCard,
		preview: "/images/card-templates/elegant.png",
		category: "elegant",
		colors: {
			primary: "#1a1a1a",
			secondary: "#4a4a4a",
			accent: "#d4af37",
			background: "#000000",
			text: "#ffffff",
		},
		typography: {
			fontFamily: "'Playfair Display', serif",
			headerSize: 36,
			bodySize: 20,
			titleWeight: 700,
		},
		layout: {
			padding: 40,
			spacing: 24,
			borderRadius: 0,
			showShadow: true,
		},
		features: ["elegant"],
		supportedPlatforms: [
			"instagram",
			"wechat",
			"weibo",
			"twitter",
			"facebook",
		],
	},
	{
		id: "handdrawn",
		name: "handDrawn",
		description: "handDrawn",
		component: HandDrawnCard as React.ComponentType<any>,
		preview: "/images/card-templates/handdrawn.png",
		category: "handdrawn",
		colors: {
			primary: "#8B4513",
			secondary: "#CD853F",
			accent: "#FFD700",
			background: "#F5F1E8",
			text: "#2F4F4F",
		},
		typography: {
			fontFamily: "'Comic Neue', 'Kalam', cursive",
			headerSize: 28,
			bodySize: 18,
			titleWeight: 700,
		},
		layout: {
			padding: 32,
			spacing: 20,
			borderRadius: 20,
			showShadow: true,
		},
		features: ["handDrawn"],
		supportedPlatforms: [
			"instagram",
			"wechat",
			"weibo",
			"twitter",
			"facebook",
		],
		customizationOptions: [
			{
				key: "colors.primary",
				label: "primaryColor", // 改为key
				type: "color",
				category: "colors",
				default: "#8B4513",
			},
			{
				key: "colors.accent",
				label: "decorativeColor", // 改为key
				type: "color",
				category: "colors",
				default: "#FFD700",
			},
			{
				key: "content.customTitle",
				label: "cardTitle", // 改为key
				type: "select",
				category: "content",
				options: [
					"titleOptions.5", // 改为key索引
					"titleOptions.6",
					"titleOptions.7",
					"titleOptions.8",
					"titleOptions.9",
				],
				default: "titleOptions.5",
			},
		],
		defaultCustomization: createDefaultCustomization({
			colors: {
				primary: "#8B4513",
				secondary: "#CD853F",
				accent: "#FFD700",
				background: "#F5F1E8",
				text: "#2F4F4F",
			},
			typography: {
				fontFamily: "'Comic Neue', 'Kalam', cursive",
				headerSize: 28,
				bodySize: 18,
			},
			content: {
				customTitle: "titleOptions.5", // 改为key
				customFooter: "footerOptions.1", // 改为key
			},
		}),
	},
	{
		id: "retro",
		name: "retro",
		description: "retro",
		component: RetroCard,
		preview: "/images/card-templates/retro.png",
		category: "retro",
		colors: {
			primary: "#d35400",
			secondary: "#e67e22",
			accent: "#f39c12",
			background: "#f4e4c1",
			text: "#8b4513",
		},
		typography: {
			fontFamily: "'Courier New', monospace",
			headerSize: 36,
			bodySize: 20,
			titleWeight: 700,
		},
		layout: {
			padding: 32,
			spacing: 20,
			borderRadius: 8,
			showShadow: true,
		},
		features: ["retro"],
		supportedPlatforms: [
			"instagram",
			"wechat",
			"weibo",
			"twitter",
			"facebook",
		],
	},
];

// 模板分类 - 改为key
export const templateCategories = [
	{
		id: "minimal",
		name: "minimal", // 改为key
		icon: "📷",
		iconComponent: Camera,
		description: "minimal", // 改为key
	},
	{
		id: "vibrant",
		name: "vibrant",
		icon: "✨",
		iconComponent: Sparkles,
		description: "vibrant",
	},
	{
		id: "elegant",
		name: "elegant",
		icon: "🎨",
		iconComponent: Layers,
		description: "elegant",
	},
	{
		id: "handdrawn",
		name: "handDrawn",
		icon: "✏️",
		iconComponent: Palette,
		description: "handDrawn",
	},
	{
		id: "retro",
		name: "retro",
		icon: "��",
		iconComponent: Palette,
		description: "retro",
	},
] as const;

// 多语言工具函数
export function getTemplateName(id: string, t: TranslationFunction): string {
	return t(`templates.names.${id}`);
}

export function getTemplateDescription(
	id: string,
	t: TranslationFunction,
): string {
	return t(`templates.descriptions.${id}`);
}

export function getTemplateFeatures(
	id: string,
	t: TranslationFunction,
): string[] {
	const features = t(`templates.features.${id}`);
	return Array.isArray(features) ? features : [];
}

export function getCategoryName(id: string, t: TranslationFunction): string {
	return t(`templates.categories.${id}`);
}

export function getCategoryDescription(
	id: string,
	t: TranslationFunction,
): string {
	return t(`templates.categoryDescriptions.${id}`);
}

// 获取指定模板
export function getTemplate(id: string): CardTemplate | undefined {
	return cardTemplates.find((template) => template.id === id);
}

// 获取平台支持的模板
export function getTemplatesForPlatform(
	platform: SocialPlatform,
): CardTemplate[] {
	return cardTemplates.filter((template) =>
		template.supportedPlatforms?.includes(platform),
	);
}

// 获取指定分类的模板
export function getTemplatesByCategory(category: string): CardTemplate[] {
	return cardTemplates.filter((template) => template.category === category);
}

export { MinimalCard, VibrantCard, ElegantCard, RetroCard, HandDrawnCard };
