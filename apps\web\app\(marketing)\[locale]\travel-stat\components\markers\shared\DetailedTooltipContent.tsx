"use client";

import { But<PERSON> } from "@ui/components/button";
import { Trash2, X } from "lucide-react";
import { useCallback, useEffect, useMemo, useState } from "react";
import type { TravelPoint } from "../../../types";
import { imageStorage } from "../../../utils/imageStorage";

interface DetailedTooltipContentProps {
	point: TravelPoint;
	onRemovePoint: (pointId: string) => void;
	onClose?: () => void;
	showCloseButton?: boolean;
	showDeleteButton?: boolean;
	variant?: "card" | "modal";
}

export function DetailedTooltipContent({
	point,
	onRemovePoint,
	onClose,
	showCloseButton = true,
	showDeleteButton = true,
	variant = "card",
}: DetailedTooltipContentProps) {
	const [imageError, setImageError] = useState(false);
	const [actualImageUrl, setActualImageUrl] = useState<string | null>(null);
	const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

	// 获取第一张图片
	const firstImage = useMemo(() => {
		if (point.images && point.images.length > 0) {
			return point.images[0];
		}
		if (point.photos && point.photos.length > 0) {
			return { url: point.photos[0], alt: point.city };
		}
		if ((point as any).image?.url) {
			return (point as any).image;
		}
		return null;
	}, [point.images, point.photos, point.city]);

	// 处理图片URL加载 - 使用新的imageStorage
	useEffect(() => {
		if (!firstImage?.url) {
			setActualImageUrl(null);
			return;
		}

		// 🔧 性能优化：如果已经有处理过的URL，避免重复处理
		if (actualImageUrl && !imageError) {
			console.log("🔄 DetailedTooltip: 跳过重复处理，使用现有URL:", {
				pointId: point.id.slice(0, 8),
				urlType: actualImageUrl.startsWith("blob:")
					? "blob"
					: actualImageUrl.startsWith("data:")
						? "data"
						: "other",
			});
			return;
		}

		console.log("🔄 DetailedTooltip: 正在处理图片URL:", {
			pointId: point.id.slice(0, 8),
			originalUrl: firstImage.url.slice(0, 50),
			urlType: firstImage.url.startsWith("indexeddb:")
				? "indexeddb"
				: firstImage.url.startsWith("blob:")
					? "blob"
					: firstImage.url.startsWith("data:")
						? "base64"
						: "other",
		});

		// 使用统一的图片处理逻辑 - 🔧 修复：使用最高清晰度图片
		imageStorage
			.processImageUrl(firstImage.url, {
				priority: "high", // tooltip图片优先级高
				maxWidth: 1500, // 使用最高清晰度 (与ClusterManager.tsx中的HIGH_QUALITY_SIZE一致)
				maxHeight: 1500, // 使用最高清晰度
				useCache: true, // 启用缓存
			})
			.then((url) => {
				if (url) {
					console.log("✅ DetailedTooltip: 图片URL处理成功:", {
						pointId: point.id.slice(0, 8),
						originalUrl: firstImage.url.slice(0, 50),
						processedUrl: url.slice(0, 50),
						urlType: url.startsWith("blob:")
							? "blob"
							: url.startsWith("data:")
								? "data"
								: url.startsWith("http")
									? "🚨HTTP(未压缩!)"
									: "other",
					});

					// 🔧 关键检查：如果处理后仍然是HTTP URL，这是个问题！
					if (url.startsWith("http")) {
						console.error(
							"🚨 DetailedTooltip: 严重错误 - 处理后仍然是HTTP URL!",
							{
								pointId: point.id.slice(0, 8),
								originalUrl: firstImage.url.slice(0, 80),
								processedUrl: url.slice(0, 80),
							},
						);
					}

					setActualImageUrl(url);
					setImageError(false);
				} else {
					console.warn("⚠️ DetailedTooltip: 图片URL处理失败:", {
						pointId: point.id.slice(0, 8),
						originalUrl: firstImage.url.slice(0, 80),
					});
					setImageError(true);
					setActualImageUrl(null);
				}
			})
			.catch((error) => {
				console.error("❌ DetailedTooltip: 图片URL处理异常:", error);
				setImageError(true);
				setActualImageUrl(null);
			});

		// 清理函数：如果URL是blob URL，需要释放
		return () => {
			if (actualImageUrl?.startsWith("blob:")) {
				URL.revokeObjectURL(actualImageUrl);
			}
		};
	}, [
		firstImage?.url,
		point.id,
		// 🔧 移除actualImageUrl依赖，防止无限循环
		// actualImageUrl
	]);

	// 格式化日期
	const formattedDate = useMemo(() => {
		if (!point.date) return "";
		try {
			return new Date(point.date).toLocaleDateString("zh-CN", {
				month: "short",
				day: "numeric",
				year: "2-digit",
			});
		} catch {
			return "";
		}
	}, [point.date]);

	// 图片加载错误处理
	const handleImageError = useCallback(() => {
		console.error("❌ DetailedTooltip图片加载失败:", {
			pointId: point.id.slice(0, 8),
			imageUrl: actualImageUrl
				? `${actualImageUrl.slice(0, 100)}...`
				: null,
		});
		setImageError(true);
	}, [point.id, actualImageUrl]);

	// 处理删除确认
	const handleDeleteClick = useCallback((e: React.MouseEvent) => {
		e.stopPropagation();
		setShowDeleteConfirm(true);
	}, []);

	const handleDeleteConfirm = useCallback(() => {
		onRemovePoint(point.id);
		if (onClose) {
			onClose();
		}
		setShowDeleteConfirm(false);
	}, [onRemovePoint, point.id, onClose]);

	const handleDeleteCancel = useCallback(() => {
		setShowDeleteConfirm(false);
	}, []);

	const containerClass =
		variant === "modal"
			? "bg-gradient-to-br from-sky-50 to-blue-50/50 border-2 border-sky-200/80 rounded-xl shadow-2xl backdrop-blur-sm"
			: "bg-gradient-to-br from-sky-50 to-blue-50/50 border-2 border-sky-200/80 rounded-lg shadow-xl backdrop-blur-sm";

	return (
		<div
			className={`${containerClass} overflow-hidden min-w-0 group relative`}
			style={{ width: "280px" }}
		>
			{/* 关闭按钮 */}
			{showCloseButton && onClose && (
				<button
					type="button"
					onClick={(e) => {
						e.stopPropagation();
						onClose();
					}}
					className="absolute top-2 right-2 w-6 h-6 bg-white/80 hover:bg-white text-gray-500 hover:text-gray-700 rounded-full flex items-center justify-center transition-all duration-200 z-10 shadow-sm border border-sky-200/50 hover:border-sky-300"
					aria-label="关闭"
				>
					<X className="w-3.5 h-3.5" strokeWidth={2} />
				</button>
			)}

			{/* 图片区域 - 固定容器，图片占满 */}
			{actualImageUrl && !imageError && (
				<div className="w-full h-32 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden">
					<img
						src={actualImageUrl}
						alt={firstImage?.alt || point.city}
						className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
						onError={handleImageError}
					/>
				</div>
			)}

			{/* 信息区域 - 紧凑布局 */}
			<div className="p-4 space-y-3">
				{/* 位置信息 */}
				<div className="space-y-1">
					{point.country && (
						<p className="text-xs text-sky-800 leading-tight">
							{point.city} · {point.country}
						</p>
					)}
				</div>

				{/* 描述区域 - 固定高度，带滚动 */}
				{point.description && (
					<div
						className="text-xs text-sky-700 leading-relaxed overflow-y-auto scrollbar-thin scrollbar-thumb-sky-300 scrollbar-track-transparent hover:scrollbar-thumb-sky-400"
						style={{ height: "60px" }}
					>
						{point.description}
					</div>
				)}

				{/* Footer: 日期和操作按钮 */}
				<div className="flex items-center justify-between pt-1 border-t border-sky-200/50">
					{/* 日期显示 */}
					<div className="text-xs text-sky-600 font-medium">
						{formattedDate || "无日期"}
					</div>

					{/* 删除按钮 */}
					{showDeleteButton && (
						<Button
							variant="ghost"
							size="sm"
							onClick={handleDeleteClick}
							className="h-6 w-6 p-0 text-gray-400 hover:text-red-500 hover:bg-red-50 transition-colors"
							aria-label="删除点位"
						>
							<Trash2 className="w-3 h-3" />
						</Button>
					)}
				</div>
			</div>

			{/* 删除确认弹窗 */}
			{showDeleteConfirm && (
				<div className="absolute inset-0 bg-white/95 backdrop-blur-sm flex items-center justify-center z-20 rounded-lg">
					<div className="text-center space-y-3 p-4">
						<p className="text-sm text-gray-700 font-medium">
							确定要删除这个点位吗？
						</p>
						<div className="flex gap-2 justify-center">
							<Button
								variant="outline"
								size="sm"
								onClick={handleDeleteCancel}
								className="text-xs h-7 px-3"
							>
								取消
							</Button>
							<Button
								variant="error"
								size="sm"
								onClick={handleDeleteConfirm}
								className="text-xs h-7 px-3"
							>
								删除
							</Button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
