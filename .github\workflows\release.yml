 name: Release

on:
  push:
    branches: [main]
  workflow_dispatch:
    inputs:
      release_type:
        description: 'Release type'
        required: true
        default: 'patch'
        type: choice
        options:
          - patch
          - minor
          - major
          - prerelease

env:
  NODE_VERSION: '20'
  PNPM_VERSION: '9.3.0'

jobs:
  check-changes:
    name: Check for Changes
    runs-on: ubuntu-latest
    outputs:
      should-release: ${{ steps.check.outputs.should-release }}
      version: ${{ steps.check.outputs.version }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          
      - name: Check for release-worthy changes
        id: check
        run: |
          # 检查是否有值得发布的更改
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            echo "should-release=true" >> $GITHUB_OUTPUT
            echo "version=${{ github.event.inputs.release_type }}" >> $GITHUB_OUTPUT
          else
            # 检查最近的提交是否包含特定关键词
            RECENT_COMMITS=$(git log --oneline -10)
            if echo "$RECENT_COMMITS" | grep -E "(feat|fix|breaking|BREAKING)"; then
              echo "should-release=true" >> $GITHUB_OUTPUT
              echo "version=patch" >> $GITHUB_OUTPUT
            else
              echo "should-release=false" >> $GITHUB_OUTPUT
            fi
          fi

  release:
    name: Create Release
    runs-on: ubuntu-latest
    needs: [check-changes]
    if: needs.check-changes.outputs.should-release == 'true'
    outputs:
      tag: ${{ steps.release.outputs.tag }}
      changelog: ${{ steps.release.outputs.changelog }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}
          
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Generate changelog
        id: changelog
        run: |
          # 生成 changelog（这里是简化版本）
          LAST_TAG=$(git describe --tags --abbrev=0 2>/dev/null || echo "")
          if [ -z "$LAST_TAG" ]; then
            CHANGELOG=$(git log --oneline --pretty=format:"- %s" -20)
          else
            CHANGELOG=$(git log --oneline --pretty=format:"- %s" $LAST_TAG..HEAD)
          fi
          echo "changelog<<EOF" >> $GITHUB_OUTPUT
          echo "$CHANGELOG" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
          
      - name: Bump version
        id: version
        run: |
          CURRENT_VERSION=$(node -p "require('./package.json').version")
          # 这里可以使用 semantic-release 或其他版本管理工具
          echo "current-version=$CURRENT_VERSION" >> $GITHUB_OUTPUT
          
      - name: Create release
        id: release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ steps.version.outputs.current-version }}
          release_name: Release v${{ steps.version.outputs.current-version }}
          body: |
            ## 🚀 What's Changed
            
            ${{ steps.changelog.outputs.changelog }}
            
            ## 📦 Installation
            
            ```bash
            # Clone the repository
            git clone https://github.com/${{ github.repository }}.git
            cd ${{ github.event.repository.name }}
            
            # Install dependencies
            pnpm install
            
            # Build applications
            pnpm build:all
            ```
            
            **Full Changelog**: https://github.com/${{ github.repository }}/compare/${{ github.event.before }}...${{ github.sha }}
          draft: false
          prerelease: ${{ contains(needs.check-changes.outputs.version, 'pre') }}

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [release]
    environment:
      name: production
      url: https://your-app.com
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ needs.release.outputs.tag }}
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}
          
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Generate database client
        run: pnpm db:generate
        env:
          DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}
          
      - name: Build applications
        run: pnpm build:all
        env:
          DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}
          NEXT_PUBLIC_APP_URL: ${{ secrets.PRODUCTION_APP_URL }}
          
      # 在此添加你的部署逻辑
      - name: Deploy to production
        run: |
          echo "🚀 Deploying to production..."
          # 这里添加实际的部署命令
          
  notify:
    name: Notify Release
    runs-on: ubuntu-latest
    needs: [release, deploy-production]
    if: always()
    steps:
      - name: Notify team
        run: |
          echo "📢 Release ${{ needs.release.outputs.tag }} completed!"
          # 可以添加 Slack、Discord 或其他通知