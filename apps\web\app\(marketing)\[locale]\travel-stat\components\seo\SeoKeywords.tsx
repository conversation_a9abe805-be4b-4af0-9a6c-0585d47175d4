import type { TravelStatTranslations } from "../../hooks/useTravelStatTranslations";

interface SeoKeywordsProps {
	translations: TravelStatTranslations;
}

export function SeoKeywords({ translations }: SeoKeywordsProps) {
	const defaultTags = [
		"Map Moment",
		"Travel Memories",
		"Interactive Maps",
		"Travel Cards",
		"Memory Mapping",
		"Journey Tracker",
		"Travel Statistics",
		"Digital Memories",
	];

	const tags = translations.seo?.keywords?.tags() || defaultTags;
	const description =
		translations.seo?.keywords?.description() ||
		"Map Moment - the ultimate tool for preserving travel memories on beautiful interactive maps. Capture precious moments from your journeys, create stunning memory cards, and build a visual story of your adventures. Perfect for travel enthusiasts who want to keep their most cherished memories alive forever.";

	return (
		<div className="text-center py-8 border-t border-sky-100 dark:border-gray-700">
			<div className="flex flex-wrap justify-center gap-2 mb-4">
				{tags.map((tag: string) => (
					<span
						key={tag}
						className="px-3 py-1 bg-sky-100 dark:bg-gray-700 text-sky-700 dark:text-sky-300 rounded-full text-sm font-medium"
					>
						{tag}
					</span>
				))}
			</div>
			<p className="text-sm text-gray-500 dark:text-gray-400 max-w-2xl mx-auto">
				{description}
			</p>
		</div>
	);
}
