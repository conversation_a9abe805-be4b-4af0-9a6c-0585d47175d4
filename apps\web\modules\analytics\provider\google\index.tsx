"use client";

import Script from "next/script";

const googleTagId = process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID as string;

export function AnalyticsScript() {
	return (
		<Script
			async
			src={`https://www.googletagmanager.com/gtag/js?id=${googleTagId}`}
			onLoad={() => {
				if (typeof window === "undefined") {
					return;
				}

				(window as any).dataLayer = (window as any).dataLayer || [];

				function gtag(...args: any[]) {
					(window as any).dataLayer.push(args);
				}

				// 添加gtag到window对象
				(window as any).gtag = gtag;

				gtag("js", new Date());
				gtag("config", googleTagId, {
					// 启用增强测量
					allow_enhanced_conversions: true,
					// 启用自动事件追踪
					allow_google_signals: true,
					// 禁用广告功能（可选，提高隐私）
					allow_ad_personalization_signals: false,
				});
			}}
		/>
	);
}

export function useAnalytics() {
	const trackEvent = (event: string, data?: Record<string, unknown>) => {
		if (typeof window === "undefined" || !(window as any).gtag) {
			return;
		}

		// Google Analytics 4 事件追踪
		(window as any).gtag("event", event, {
			// 设置自定义参数
			custom_parameter_1: data,
			// 添加时间戳
			timestamp: new Date().toISOString(),
			// 添加页面信息
			page_title: document.title,
			page_location: window.location.href,
			...data,
		});
	};

	return {
		trackEvent,
	};
}
