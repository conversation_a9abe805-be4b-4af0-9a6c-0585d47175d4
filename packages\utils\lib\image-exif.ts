/**
 * 从图片中提取 EXIF 数据（位置和时间信息）的工具函数
 */

// 避免在 next.js 中间件中导入 exifreader，使用动态导入
// import ExifReader from 'exifreader';

export interface ImageExifData {
	dateTime?: Date;
	location?: {
		lat: number;
		lng: number;
	};
}

/**
 * 从图片文件中提取 EXIF 数据
 * @param file 图片文件
 * @returns 包含日期和位置信息的对象
 */
export async function extractImageExifData(file: File): Promise<ImageExifData> {
	try {
		// 动态导入 exifreader，确保只在客户端环境中加载
		const ExifReader = (await import("exifreader")).default;

		// 将文件内容读取为 ArrayBuffer
		const arrayBuffer = await file.arrayBuffer();

		// 使用 ExifReader 解析 EXIF 数据
		const tags = ExifReader.load(arrayBuffer);

		console.log("EXIF Tags:", tags);
		// 初始化返回数据
		const exifData: ImageExifData = {};

		// 提取时间信息
		if (tags.DateTimeOriginal) {
			// EXIF 日期时间格式通常为: "YYYY:MM:DD HH:MM:SS"
			const dateTimeStr = tags.DateTimeOriginal.description;
			try {
				const [datePart, timePart] = dateTimeStr.split(" ");
				if (datePart && timePart) {
					const [year, month, day] = datePart.split(":").map(Number);
					const [hour, minute, second] = timePart
						.split(":")
						.map(Number);

					if (
						!Number.isNaN(year) &&
						!Number.isNaN(month) &&
						!Number.isNaN(day)
					) {
						// 月份需要-1，因为JS中月份是0-11
						exifData.dateTime = new Date(
							year,
							month - 1,
							day,
							hour || 0,
							minute || 0,
							second || 0,
						);
					}
				}
			} catch (error) {
				console.error("解析EXIF日期时间出错:", error);
			}
		}

		// 提取 GPS 位置信息
		if (
			tags.GPSLatitude &&
			tags.GPSLatitudeRef &&
			tags.GPSLongitude &&
			tags.GPSLongitudeRef
		) {
			try {
				// 将GPS坐标转换为十进制度数
				const latValues = tags.GPSLatitude.value;
				const latRef = tags.GPSLatitudeRef.value;
				const lngValues = tags.GPSLongitude.value;
				const lngRef = tags.GPSLongitudeRef.value;

				// 计算十进制度数 (度 + 分/60 + 秒/3600)
				let latitude =
					(Array.isArray(latValues)
						? (latValues[0] as unknown as number)
						: (latValues as unknown as number)) +
					(Array.isArray(latValues)
						? (latValues[1] as unknown as number)
						: 0) /
						60 +
					(Array.isArray(latValues)
						? (latValues[2] as unknown as number)
						: 0) /
						3600;
				let longitude =
					(Array.isArray(lngValues)
						? (lngValues[0] as unknown as number)
						: (lngValues as unknown as number)) +
					(Array.isArray(lngValues)
						? (lngValues[1] as unknown as number)
						: 0) /
						60 +
					(Array.isArray(lngValues)
						? (lngValues[2] as unknown as number)
						: 0) /
						3600;

				// 根据南北半球和东西半球调整符号
				if (latRef === "S") latitude = -latitude;
				if (lngRef === "W") longitude = -longitude;

				exifData.location = {
					lat: latitude,
					lng: longitude,
				};
			} catch (error) {
				console.error("解析GPS坐标出错:", error);
			}
		}

		return exifData;
	} catch (error) {
		console.error("提取图片EXIF数据时出错:", error);
		return {};
	}
}
