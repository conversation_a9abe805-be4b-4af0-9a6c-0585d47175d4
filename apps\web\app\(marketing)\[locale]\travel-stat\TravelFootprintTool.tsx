"use client";
import { <PERSON><PERSON> } from "@ui/components/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { ChevronDown, ChevronUp, Edit3, Plus, Sparkles } from "lucide-react";
import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";

import { useAnalytics } from "@modules/analytics";
import { useTravelStatTranslations } from "./hooks/useTravelStatTranslations";

import { CardPreviewArea } from "./components/card-generator";
import { CardConfigPanel } from "./components/card-generator/CardConfigPanel";
import { cardTemplates } from "./components/card-generator/templates";
import type {
	CardCustomization,
	CardTemplate,
	SocialPlatform,
} from "./components/card-generator/types/cardTypes";
import { AddPointPanel } from "./components/map/AddPointPanel";
import { MapContainer } from "./components/map/MapContainer";
import { SearchBox } from "./components/map/SearchBox";
import { GlobalTooltip } from "./components/markers/GlobalTooltip";
import { SeoContentSection } from "./components/seo";
import { CountryList } from "./components/stats/CountryList";
import { TravelStats } from "./components/stats/TravelStats";
import { OperationPanel } from "./components/ui/OperationPanel";
import { ResizablePanel } from "./components/ui/ResizablePanel";
import { MapProvider, useMap } from "./contexts/MapContext";
import { useSearch } from "./hooks/useSearch";
import type { GeocodeFeature } from "./types";
import {
	buildHierarchicalStats,
	calculateStats,
	exportTravelData,
	importTravelData,
} from "./utils/dataUtils";

// 页面模式类型
type PageMode = "editing" | "card-generation";

function TravelFootprintView() {
	// 分析追踪功能
	const { trackEvent } = useAnalytics();

	// 多语言翻译Hook
	const translations = useTravelStatTranslations();

	// 🔍 开发环境渲染计数器
	const renderCountRef = useRef(0);
	renderCountRef.current += 1;
	if (process.env.NODE_ENV === "development") {
		console.log(
			`🔄 TravelFootprintView 渲染次数: ${renderCountRef.current}`,
		);
	}

	// 追踪是否已经执行过首次居中操作
	const hasInitializedMapView = useRef(false);

	// 页面模式状态管理
	const [pageMode, setPageMode] = useState<PageMode>("editing");
	const [isTransitioning, setIsTransitioning] = useState(false);

	// 卡片生成状态管理
	const [selectedPlatform, setSelectedPlatform] =
		useState<SocialPlatform>("instagram");
	const [selectedTemplate, setSelectedTemplate] = useState<CardTemplate>(
		cardTemplates[0],
	);
	// 使用 useMemo 来稳定初始化数据，避免每次渲染都创建新对象
	const initialCustomization = useMemo(() => {
		const template = cardTemplates[0];
		return {
			colors: template.colors,
			typography: template.typography,
			layout: template.layout,
			content: {
				showUserInfo: true,
				showDetailedStats: true,
				customTitle: "我的旅行足迹", // 使用固定的默认值，避免依赖翻译函数
				customFooter: "记录美好时光", // 使用固定的默认值，避免依赖翻译函数
			},
		};
	}, []); // 空依赖数组，确保只在组件挂载时计算一次

	const [customization, setCustomization] =
		useState<CardCustomization>(initialCustomization);

	// 从 Context 获取所有地图和数据相关的状态与函数
	const {
		travelPoints,
		visitedCountries,
		addTravelPoint,
		addTravelPointWithDetails,
		clearAllData,
		setData,
		flyToPoint,
		flyToCountry,
		resetMapView,
		currentTheme,
	} = useMap();

	// 面板状态管理
	const [isPanelExpanded, setIsPanelExpanded] = useState(false);
	const [selectedLocationForForm, setSelectedLocationForForm] =
		useState<GeocodeFeature | null>(null);

	// 搜索功能保持独立
	const {
		searchQuery,
		searchResults,
		isSearching,
		showResults,
		handleSearchChange,
		clearSearch,
	} = useSearch();

	// 页面访问追踪 - 只在首次加载时执行
	useEffect(() => {
		// 检查是否有现有数据
		const hasPoints = localStorage.getItem("travelFootprints");
		const hasCountries = localStorage.getItem("visitedCountries");
		const hasExistingData = Boolean(hasPoints && hasCountries);

		// 追踪页面访问事件
		trackEvent("travel_stat_page_visit", {
			entry_method: "direct", // 可以根据 referrer 或 URL 参数调整
			has_existing_data: hasExistingData,
		});
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []); // 只在组件挂载时执行一次，忽略trackEvent的变化

	// 首次加载时自动居中显示所有点位
	useEffect(() => {
		// 只在首次加载且有点位数据时执行一次
		if (travelPoints.length > 0 && !hasInitializedMapView.current) {
			// 延迟执行，确保地图已经加载完成
			const timer = setTimeout(() => {
				resetMapView(travelPoints);
				hasInitializedMapView.current = true;
				console.log(
					"🗺️ 首次加载：自动居中显示",
					travelPoints.length,
					"个点位",
				);
			}, 1500); // 延迟1.5秒确保地图完全加载

			return () => clearTimeout(timer);
		}
	}, [travelPoints, resetMapView]);

	// 监听数据变化，确保地图在数据加载后重新调整尺寸
	useEffect(() => {
		// 只在数据从空到有数据，或者数据量有显著变化时才触发地图尺寸调整
		const hasData = travelPoints.length > 0 || visitedCountries.length > 0;

		if (hasData) {
			const timer = setTimeout(() => {
				// 触发窗口 resize 事件，让地图重新计算尺寸
				window.dispatchEvent(new Event("resize"));
				console.log("📊 数据加载完成：触发地图尺寸调整", {
					points: travelPoints.length,
					countries: visitedCountries.length,
				});
			}, 300); // 等待DOM更新完成

			return () => clearTimeout(timer);
		}
	}, [travelPoints.length, visitedCountries.length]); // 监听数据数量变化

	// 模式切换函数
	const switchToCardMode = useCallback(async () => {
		if (travelPoints.length === 0) {
			alert("请先添加旅行足迹再生成卡片"); // 使用固定文本
			return;
		}

		// 追踪模式切换事件
		trackEvent("travel_stat_mode_switch", {
			from_mode: "editing",
			to_mode: "card-generation",
			points_count: travelPoints.length,
			countries_count: visitedCountries.length,
		});

		setIsTransitioning(true);
		setTimeout(() => {
			setPageMode("card-generation");
			setIsTransitioning(false);
		}, 300);
	}, [travelPoints.length, visitedCountries.length, trackEvent]);

	const switchToEditMode = useCallback(async () => {
		// 追踪模式切换事件
		trackEvent("travel_stat_mode_switch", {
			from_mode: "card-generation",
			to_mode: "editing",
			points_count: travelPoints.length,
			countries_count: visitedCountries.length,
		});

		setIsTransitioning(true);
		setTimeout(() => {
			setPageMode("editing");
			setIsTransitioning(false);
		}, 300);
	}, [travelPoints.length, visitedCountries.length, trackEvent]);

	// 处理搜索结果选择 - 设置位置并确保面板展开
	const handleSelectResult = useCallback(
		(result: GeocodeFeature) => {
			// 追踪选择搜索结果事件
			trackEvent("travel_stat_search_result_selected", {
				point_name: result.place_name,
				search_query: searchQuery,
			});

			setSelectedLocationForForm(result);
			setIsPanelExpanded(true);
			clearSearch();
		},
		[searchQuery, trackEvent, clearSearch],
	);

	// 处理表单提交
	const handleFormSubmit = useCallback(
		(
			formData: {
				description: string;
				date: string;
				image: any | null;
			},
			location: GeocodeFeature,
		) => {
			const newPoint = addTravelPointWithDetails(location, formData);

			// 追踪添加旅行点位事件
			trackEvent("travel_stat_point_added_with_details", {
				point_name: location.place_name,
				country: newPoint.country,
				city: newPoint.city,
				total_points: travelPoints.length + 1,
				has_image: !!formData.image,
				has_description: !!formData.description,
			});

			// 使用国家级别聚焦，让地图显示整个国家
			flyToCountry(newPoint.coordinates, newPoint.country);
		},
		[
			addTravelPointWithDetails,
			flyToCountry,
			travelPoints.length,
			trackEvent,
		],
	);

	// 处理面板切换
	const handlePanelToggle = useCallback(() => {
		setIsPanelExpanded((prev) => !prev);
	}, []);

	// 处理清除选中位置
	const handleClearLocation = useCallback(() => {
		setSelectedLocationForForm(null);
	}, []);

	// 处理搜索框聚焦 - 不再自动展开面板，等用户选择位置后再展开
	const handleSearchFocus = useCallback(() => {
		// 移除自动展开行为，面板将在选择位置时展开 (handleSelectResult)
	}, []);

	// 处理地图视图重置
	const handleResetView = useCallback(() => {
		trackEvent("travel_stat_map_reset", {
			points_count: travelPoints.length,
		});
		resetMapView(travelPoints);
	}, [resetMapView, travelPoints, trackEvent]);

	// 处理数据导出
	const handleExport = useCallback(() => {
		trackEvent("travel_stat_data_export", {
			points_count: travelPoints.length,
			countries_count: visitedCountries.length,
			export_type: "json",
		});
		exportTravelData(travelPoints, visitedCountries);
	}, [travelPoints, visitedCountries, trackEvent]);

	// 处理数据导入
	const handleImport = useCallback(() => {
		trackEvent("travel_stat_import_attempt");
		importTravelData(
			(points, countries) => {
				trackEvent("travel_stat_data_import_success", {
					imported_points: points.length,
					imported_countries: countries.length,
				});
				setData(points, countries);
			},
			(error) => {
				trackEvent("travel_stat_data_import_failed", {
					error_message: error,
				});
				alert(error);
			},
		);
	}, [setData, trackEvent]);

	// 处理清空数据
	const handleClearData = useCallback(() => {
		// 创建确认对话框
		const dialogContent = `
			<div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; max-width: 400px;">
				<h3 style="margin: 0 0 16px 0; font-size: 18px; font-weight: 600; color: #1f2937;">
					${translations.clearDataDialog.title()}
				</h3>
				<p style="margin: 0 0 20px 0; color: #6b7280; line-height: 1.5;">
					${translations.clearDataDialog.message()}
				</p>
				<div style="margin: 20px 0; padding: 16px; background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px;">
					<label style="display: flex; align-items: flex-start; gap: 12px; cursor: pointer;">
						<input 
							type="checkbox" 
							id="clearImages" 
							style="margin-top: 4px; width: 16px; height: 16px; accent-color: #3b82f6;"
						/>
						<div>
							<div style="font-weight: 500; color: #1f2937; margin-bottom: 4px;">
								${translations.clearDataDialog.clearImagesLabel()}
							</div>
							<div style="color: #6b7280; font-size: 14px; line-height: 1.4;">
								${translations.clearDataDialog.clearImagesDescription()}
							</div>
						</div>
					</label>
				</div>
			</div>
		`;

		// 创建自定义确认对话框
		const dialog = document.createElement("div");
		dialog.style.cssText = `
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0, 0, 0, 0.5);
			display: flex;
			align-items: center;
			justify-content: center;
			z-index: 10000;
			backdrop-filter: blur(4px);
		`;

		const modal = document.createElement("div");
		modal.style.cssText = `
			background: white;
			border-radius: 12px;
			padding: 24px;
			box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
			max-width: 90%;
			width: 480px;
		`;

		const buttonsContainer = document.createElement("div");
		buttonsContainer.style.cssText = `
			display: flex;
			gap: 12px;
			justify-content: flex-end;
			margin-top: 24px;
		`;

		const cancelButton = document.createElement("button");
		cancelButton.textContent = translations.clearDataDialog.cancel();
		cancelButton.style.cssText = `
			padding: 10px 20px;
			border: 1px solid #d1d5db;
			background: white;
			color: #374151;
			border-radius: 6px;
			cursor: pointer;
			font-size: 14px;
			font-weight: 500;
			transition: all 0.2s;
		`;
		cancelButton.onmouseover = () => {
			cancelButton.style.background = "#f9fafb";
		};
		cancelButton.onmouseout = () => {
			cancelButton.style.background = "white";
		};

		const confirmButton = document.createElement("button");
		confirmButton.textContent = translations.clearDataDialog.confirm();
		confirmButton.style.cssText = `
			padding: 10px 20px;
			border: none;
			background: #ef4444;
			color: white;
			border-radius: 6px;
			cursor: pointer;
			font-size: 14px;
			font-weight: 500;
			transition: all 0.2s;
		`;
		confirmButton.onmouseover = () => {
			confirmButton.style.background = "#dc2626";
		};
		confirmButton.onmouseout = () => {
			confirmButton.style.background = "#ef4444";
		};

		modal.innerHTML = dialogContent;
		buttonsContainer.appendChild(cancelButton);
		buttonsContainer.appendChild(confirmButton);
		modal.appendChild(buttonsContainer);
		dialog.appendChild(modal);
		document.body.appendChild(dialog);

		// 处理按钮点击
		const cleanup = () => {
			document.body.removeChild(dialog);
		};

		cancelButton.onclick = cleanup;

		confirmButton.onclick = () => {
			const clearImagesCheckbox = document.getElementById(
				"clearImages",
			) as HTMLInputElement;
			const shouldClearImages = clearImagesCheckbox?.checked || false;

			// 追踪清空数据事件
			trackEvent("travel_stat_data_clear", {
				cleared_points: travelPoints.length,
				cleared_countries: visitedCountries.length,
				clear_images: shouldClearImages,
			});

			clearAllData(shouldClearImages);
			cleanup();
		};

		// 点击背景关闭
		dialog.onclick = (e) => {
			if (e.target === dialog) {
				cleanup();
			}
		};

		// ESC键关闭
		const handleEsc = (e: KeyboardEvent) => {
			if (e.key === "Escape") {
				cleanup();
				document.removeEventListener("keydown", handleEsc);
			}
		};
		document.addEventListener("keydown", handleEsc);
	}, [
		clearAllData,
		travelPoints.length,
		visitedCountries.length,
		trackEvent,
		translations,
	]);

	// 处理位置点击（国家/区域/城市居中显示）
	const handleLocationClick = useCallback(
		(
			coordinates: [number, number],
			name: string,
			level: "country" | "region" | "city",
		) => {
			// 根据层级调整缩放级别
			let zoomLevel = 4; // 默认国家级别

			if (level === "region") {
				zoomLevel = 6; // 区域级别
			} else if (level === "city") {
				zoomLevel = 8; // 城市级别
			} else if (level === "country") {
				// 根据国家名称调整缩放级别（大国用更小的缩放）
				const largeCountries = [
					"中国",
					"China",
					"美国",
					"United States",
					"俄罗斯",
					"Russia",
					"加拿大",
					"Canada",
					"巴西",
					"Brazil",
					"澳大利亚",
					"Australia",
				];
				const mediumCountries = [
					"印度",
					"India",
					"阿根廷",
					"Argentina",
					"哈萨克斯坦",
					"Kazakhstan",
				];

				if (
					largeCountries.some(
						(country) =>
							name.includes(country) || country.includes(name),
					)
				) {
					zoomLevel = 3.5;
				} else if (
					mediumCountries.some(
						(country) =>
							name.includes(country) || country.includes(name),
					)
				) {
					zoomLevel = 4;
				} else {
					zoomLevel = 5; // 小国用更大的缩放级别
				}
			}

			// 追踪位置点击事件
			trackEvent("travel_stat_location_click", {
				location_name: name,
				location_level: level,
				zoom_level: zoomLevel,
			});

			// 使用flyToCountry函数进行居中显示
			flyToCountry(coordinates, name);
		},
		[flyToCountry, trackEvent],
	);

	// 卡片处理函数
	const handlePlatformChange = useCallback(
		(platform: SocialPlatform) => {
			trackEvent("travel_stat_platform_change", {
				from_platform: selectedPlatform,
				to_platform: platform,
			});
			setSelectedPlatform(platform);
		},
		[selectedPlatform, trackEvent],
	);

	const handleTemplateSelect = useCallback(
		(template: CardTemplate) => {
			trackEvent("travel_stat_template_change", {
				from_template: selectedTemplate.id,
				to_template: template.id,
				platform: selectedPlatform,
			});

			setSelectedTemplate(template);
			setCustomization({
				colors: template.colors,
				typography: template.typography,
				layout: template.layout,
				content: {
					showDate: true,
					showUserInfo: true,
					showDetailedStats: true,
					customTitle: "我的旅行足迹", // 使用固定值
					customFooter: "记录美好时光", // 使用固定值
				},
			});
		},
		[selectedTemplate.id, selectedPlatform, trackEvent],
	);

	const handleCustomizationChange = useCallback(
		(newCustomization: CardCustomization) => {
			trackEvent("travel_stat_customization_change", {
				template: selectedTemplate.id,
				platform: selectedPlatform,
			});
			setCustomization(newCustomization);
		},
		[selectedTemplate.id, selectedPlatform, trackEvent],
	);

	const handleCardExport = useCallback(
		(quality: string) => {
			trackEvent("travel_stat_card_export", {
				template: selectedTemplate.id,
				platform: selectedPlatform,
				quality: quality,
				points_count: travelPoints.length,
				countries_count: visitedCountries.length,
			});
			console.log(`Card exported with quality: ${quality}`);
		},
		[
			selectedTemplate.id,
			selectedPlatform,
			travelPoints.length,
			visitedCountries.length,
			trackEvent,
		],
	);

	// 根据模式调整布局
	const getLayoutClasses = () => {
		if (pageMode === "editing") {
			return "grid-cols-1 xl:grid-cols-4"; // 侧边栏 + 地图
		}
		return "grid-cols-1 lg:grid-cols-3 lg:items-stretch"; // 配置面板 + 卡片预览，高度对齐
	};

	// 地图区域样式
	const getMapAreaClasses = () => {
		if (pageMode === "editing") {
			return "xl:col-span-3"; // 占3列
		}
		return "lg:col-span-2"; // 卡片预览占2列
	};

	// 计算统计数据
	const stats = calculateStats(travelPoints, visitedCountries);

	// 构建层级统计数据
	const hierarchicalData = useMemo(() => {
		if (travelPoints.length === 0) {
			return []; // 如果没有数据，直接返回空数组
		}

		return buildHierarchicalStats(travelPoints);
	}, [travelPoints]);

	return (
		<section className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 dark:from-slate-900 dark:via-blue-900/20 dark:to-purple-900/10">
			{/* 背景装饰元素 */}
			<div className="fixed inset-0 overflow-hidden pointer-events-none">
				<div className="absolute -top-40 -left-40 w-80 h-80 bg-gradient-to-r from-blue-200/30 to-purple-200/30 dark:from-blue-800/20 dark:to-purple-800/20 rounded-full blur-3xl animate-pulse" />
				<div className="absolute top-1/3 -right-20 w-60 h-60 bg-gradient-to-r from-pink-200/20 to-blue-200/20 dark:from-pink-800/15 dark:to-blue-800/15 rounded-full blur-3xl animate-pulse delay-1000" />
				<div className="absolute bottom-20 left-1/4 w-40 h-40 bg-gradient-to-r from-purple-200/20 to-pink-200/20 dark:from-purple-800/15 dark:to-pink-800/15 rounded-full blur-3xl animate-pulse delay-2000" />
			</div>

			{/* 页面标题区域 */}
			<div className="relative container mx-auto px-4 pt-20 pb-12">
				<div className="text-center">
					{/* 浮动装饰元素 */}
					<div className="absolute top-8 left-1/4 w-3 h-3 bg-blue-400 dark:bg-blue-300 rounded-full animate-bounce opacity-60" />
					<div className="absolute top-12 right-1/3 w-2 h-2 bg-purple-400 dark:bg-purple-300 rounded-full animate-bounce delay-500 opacity-60" />
					<div className="absolute top-16 right-1/4 w-2.5 h-2.5 bg-pink-400 dark:bg-pink-300 rounded-full animate-bounce delay-1000 opacity-60" />
				</div>
			</div>
			{/* 精美悬浮切换按钮 */}
			<div className="fixed bottom-6 right-6 z-50">
				{pageMode === "editing" ? (
					<button
						type="button"
						onClick={switchToCardMode}
						disabled={travelPoints.length === 0 || isTransitioning}
						className={`group relative w-14 h-14 rounded-full shadow-xl transition-all duration-300 transform ${
							travelPoints.length === 0 || isTransitioning
								? "opacity-50 cursor-not-allowed bg-gray-400"
								: "hover:scale-105 active:scale-95 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
						}`}
						title={
							travelPoints.length === 0
								? translations.navigation.addPointsToGenerate()
								: translations.navigation.generateCard()
						}
					>
						{/* 背景光晕效果 */}
						{travelPoints.length > 0 && !isTransitioning && (
							<div
								className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-400 to-pink-400 opacity-30 animate-pulse"
								style={{
									filter: "blur(8px)",
									transform: "scale(1.3)",
								}}
							/>
						)}

						{/* 主按钮 */}
						<div className="relative w-full h-full rounded-full flex items-center justify-center overflow-hidden">
							{/* 图标 */}
							<Sparkles
								className={`w-5 h-5 text-white transition-all duration-300 ${
									!isTransitioning && travelPoints.length > 0
										? "group-hover:scale-110 drop-shadow-lg"
										: ""
								}`}
							/>
						</div>

						{/* 文字提示 */}
						<div className="absolute bottom-full right-0 mb-2 px-2 py-1 bg-black/80 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none">
							{travelPoints.length === 0
								? translations.navigation.addPointsToGenerate()
								: translations.navigation.generateCard()}
							<div className="absolute top-full right-2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-black/80" />
						</div>
					</button>
				) : (
					<button
						type="button"
						onClick={switchToEditMode}
						disabled={isTransitioning}
						className="group relative w-14 h-14 rounded-full bg-gradient-to-r from-blue-500 to-sky-500 hover:from-blue-600 hover:to-sky-600 shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95"
						title={translations.navigation.switchToEdit()}
					>
						{/* 背景光晕效果 */}
						<div
							className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400 to-sky-400 opacity-30 animate-pulse"
							style={{
								filter: "blur(8px)",
								transform: "scale(1.3)",
							}}
						/>

						{/* 主按钮 */}
						<div className="relative w-full h-full rounded-full flex items-center justify-center overflow-hidden">
							{/* 图标 */}
							<Edit3 className="w-5 h-5 text-white transition-all duration-300 group-hover:scale-110 drop-shadow-lg" />
						</div>

						{/* 文字提示 */}
						<div className="absolute bottom-full right-0 mb-2 px-2 py-1 bg-black/80 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none">
							{translations.navigation.switchToEdit()}
							<div className="absolute top-full right-2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-black/80" />
						</div>
					</button>
				)}
			</div>

			{/* 主要内容区域 */}
			<div
				className={`relative container mx-auto px-4 pb-6 transition-all duration-300 ${isTransitioning ? "opacity-70 scale-[0.98]" : "opacity-100 scale-100"} ${pageMode === "card-generation" ? "min-h-[80vh]" : ""}`}
			>
				{pageMode === "editing" ? (
					// 编辑模式：使用 ResizablePanel 实现可拖拽的分隔条
					<div className="transition-all duration-300">
						{/* 移动端和小屏幕使用传统布局 */}
						<div className="xl:hidden space-y-6">
							{/* 搜索框和编辑面板整合 */}
							<Card className="bg-white/90 backdrop-blur-sm border-sky-200 overflow-visible relative z-20">
								<CardHeader className="px-3 py-2 pb-1">
									<div className="flex items-center justify-between">
										<CardTitle className="text-sm font-medium flex items-center gap-2">
											<Plus className="w-4 h-4 text-sky-500" />
											Add Travel Point
										</CardTitle>
										<Button
											variant="ghost"
											size="sm"
											onClick={handlePanelToggle}
											className="h-6 w-6 p-0 hover:bg-sky-100 dark:hover:bg-gray-700"
										>
											{isPanelExpanded ? (
												<ChevronUp className="w-3 h-3" />
											) : (
												<ChevronDown className="w-3 h-3" />
											)}
										</Button>
									</div>
								</CardHeader>
								<CardContent className="px-3 pb-3 pt-3 overflow-visible">
									<SearchBox
										searchQuery={searchQuery}
										searchResults={searchResults}
										isSearching={isSearching}
										showResults={showResults}
										onSearchChange={handleSearchChange}
										onSelectResult={handleSelectResult}
										onFocus={handleSearchFocus}
										isPanelExpanded={isPanelExpanded}
									/>
								</CardContent>

								{/* 编辑面板 */}
								<AddPointPanel
									selectedLocation={selectedLocationForForm}
									isExpanded={isPanelExpanded}
									onSubmit={handleFormSubmit}
									onClearLocation={handleClearLocation}
								/>
							</Card>

							{/* 地图区域 - 使用动态高度 */}
							<div className="h-[50vh] min-h-[400px]">
								<div className="group relative h-full">
									<div className="absolute inset-0 bg-gradient-to-r from-green-200/20 via-blue-200/20 to-purple-200/20 dark:from-green-800/20 dark:via-blue-800/20 dark:to-purple-800/20 rounded-2xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
									<div className="relative h-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-white/20 dark:border-gray-700/50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
										<MapContainer />
									</div>
								</div>
							</div>

							{/* 统计和操作面板 */}
							<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
								{/* 统计卡片 */}
								<div className="group relative">
									<div className="absolute inset-0 bg-gradient-to-r from-green-200/20 via-blue-200/20 to-purple-200/20 dark:from-green-800/20 dark:via-blue-800/20 dark:to-purple-800/20 rounded-2xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
									<div className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-white/20 dark:border-gray-700/50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
										<TravelStats
											totalPoints={stats.totalPoints}
											citiesCount={stats.citiesCount}
											countriesCount={
												stats.countriesCount
											}
										/>
									</div>
								</div>

								{/* 操作面板 */}
								<div className="group relative">
									<div className="absolute inset-0 bg-gradient-to-r from-orange-200/20 via-pink-200/20 to-purple-200/20 dark:from-orange-800/20 dark:via-pink-800/20 dark:to-purple-800/20 rounded-2xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
									<div className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-white/20 dark:border-gray-700/50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
										<OperationPanel
											onExport={handleExport}
											onImport={handleImport}
											onClear={handleClearData}
											onCardExport={() => {}}
											hasData={travelPoints.length > 0}
										/>
									</div>
								</div>
							</div>

							{/* 访问过的国家 */}
							<div className="group relative">
								<div className="absolute inset-0 bg-gradient-to-r from-purple-200/20 via-pink-200/20 to-blue-200/20 dark:from-purple-800/20 dark:via-pink-800/20 dark:to-blue-800/20 rounded-2xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
								<div className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-white/20 dark:border-gray-700/50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
									<CountryList
										visitedCountries={hierarchicalData}
										travelPoints={travelPoints}
										currentColorTheme={currentTheme}
										onLocationClick={handleLocationClick}
									/>
								</div>
							</div>
						</div>

						{/* 大屏幕使用可拖拽分隔条布局 */}
						<div className="hidden xl:block">
							<ResizablePanel
								defaultLeftWidth={25}
								minLeftWidth={15}
								maxLeftWidth={45}
								className="animate-in slide-in-from-bottom-5 duration-500"
								leftPanel={
									<div className="space-y-6 px-2">
										{/* 搜索框和编辑面板整合 */}
										<Card className="bg-white/90 backdrop-blur-sm border-sky-200 overflow-visible relative z-20">
											<CardHeader className="px-3 py-2 pb-1">
												<div className="flex items-center justify-between">
													<CardTitle className="text-sm font-medium flex items-center gap-2">
														<Plus className="w-4 h-4 text-sky-500" />
														Add Travel Point
													</CardTitle>
													<Button
														variant="ghost"
														size="sm"
														onClick={
															handlePanelToggle
														}
														className="h-6 w-6 p-0 hover:bg-sky-100 dark:hover:bg-gray-700"
													>
														{isPanelExpanded ? (
															<ChevronUp className="w-3 h-3" />
														) : (
															<ChevronDown className="w-3 h-3" />
														)}
													</Button>
												</div>
											</CardHeader>
											<CardContent className="px-3 pb-3 pt-3 overflow-visible">
												<SearchBox
													searchQuery={searchQuery}
													searchResults={
														searchResults
													}
													isSearching={isSearching}
													showResults={showResults}
													onSearchChange={
														handleSearchChange
													}
													onSelectResult={
														handleSelectResult
													}
													onFocus={handleSearchFocus}
													isPanelExpanded={
														isPanelExpanded
													}
												/>
											</CardContent>

											{/* 编辑面板 */}
											<AddPointPanel
												selectedLocation={
													selectedLocationForForm
												}
												isExpanded={isPanelExpanded}
												onSubmit={handleFormSubmit}
												onClearLocation={
													handleClearLocation
												}
											/>
										</Card>

										{/* 统计卡片 */}
										<div className="group relative">
											<div className="absolute inset-0 bg-gradient-to-r from-green-200/20 via-blue-200/20 to-purple-200/20 dark:from-green-800/20 dark:via-blue-800/20 dark:to-purple-800/20 rounded-2xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
											<div className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-white/20 dark:border-gray-700/50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
												<TravelStats
													totalPoints={
														stats.totalPoints
													}
													citiesCount={
														stats.citiesCount
													}
													countriesCount={
														stats.countriesCount
													}
												/>
											</div>
										</div>

										{/* 访问过的国家 */}
										<div className="group relative">
											<div className="absolute inset-0 bg-gradient-to-r from-purple-200/20 via-pink-200/20 to-blue-200/20 dark:from-purple-800/20 dark:via-pink-800/20 dark:to-blue-800/20 rounded-2xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
											<div className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-white/20 dark:border-gray-700/50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
												<CountryList
													visitedCountries={
														hierarchicalData
													}
													travelPoints={travelPoints}
													currentColorTheme={
														currentTheme
													}
													onLocationClick={
														handleLocationClick
													}
												/>
											</div>
										</div>

										{/* 操作面板 */}
										<div className="group relative">
											<div className="absolute inset-0 bg-gradient-to-r from-orange-200/20 via-pink-200/20 to-purple-200/20 dark:from-orange-800/20 dark:via-pink-800/20 dark:to-purple-800/20 rounded-2xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
											<div className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-white/20 dark:border-gray-700/50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
												<OperationPanel
													onExport={handleExport}
													onImport={handleImport}
													onClear={handleClearData}
													onCardExport={() => {}}
													hasData={
														travelPoints.length > 0
													}
												/>
											</div>
										</div>
									</div>
								}
								rightPanel={
									<div className="px-2 h-full">
										<div className="group relative h-full">
											<div className="absolute inset-0 bg-gradient-to-r from-green-200/20 via-blue-200/20 to-purple-200/20 dark:from-green-800/20 dark:via-blue-800/20 dark:to-purple-800/20 rounded-2xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
											<div className="relative h-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-white/20 dark:border-gray-700/50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
												<MapContainer />
											</div>
										</div>
									</div>
								}
							/>
						</div>
					</div>
				) : (
					// 卡片生成模式：保持原有布局
					<div
						className={`grid gap-6 xl:items-stretch transition-all duration-300 ${getLayoutClasses()} ${pageMode === "card-generation" ? "min-h-[70vh]" : ""}`}
					>
						{/* 卡片生成模式：配置面板 */}
						<div className="lg:col-span-1 flex flex-col animate-in slide-in-from-left-5 duration-500">
							<div className="group relative h-full">
								<div className="absolute inset-0 bg-gradient-to-r from-blue-200/30 via-purple-200/30 to-pink-200/30 dark:from-blue-800/20 dark:via-purple-800/20 dark:to-pink-800/20 rounded-2xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
								<div className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-white/20 dark:border-gray-700/50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 h-full">
									<CardConfigPanel
										selectedPlatform={selectedPlatform}
										selectedTemplate={selectedTemplate}
										customization={customization}
										onPlatformChange={handlePlatformChange}
										onTemplateSelect={handleTemplateSelect}
										onCustomizationChange={
											handleCustomizationChange
										}
									/>
								</div>
							</div>
						</div>

						{/* 卡片生成模式：大尺寸卡片预览区域 */}
						{selectedTemplate && (
							<div className="lg:col-span-2 flex flex-col animate-in slide-in-from-right-5 duration-500">
								<div className="group relative flex-1 flex flex-col">
									<div className="absolute inset-0 bg-gradient-to-r from-purple-200/30 via-pink-200/30 to-blue-200/30 dark:from-purple-800/20 dark:via-pink-800/20 dark:to-blue-800/20 rounded-2xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
									<div className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-white/20 dark:border-gray-700/50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 flex-1 flex flex-col">
										{/* CardPreviewArea 现在从 Context 获取地图 props */}
										<div className="flex-1">
											<CardPreviewArea
												selectedTemplate={
													selectedTemplate
												}
												customization={customization}
												platform={selectedPlatform}
												onExport={handleCardExport}
											/>
										</div>
									</div>
								</div>
							</div>
						)}
					</div>
				)}
			</div>

			{/* SEO 内容区域 */}
			<SeoContentSection translations={translations} />

			{/* 全局tooltip */}
			<GlobalTooltip />
		</section>
	);
}

export function TravelFootprintTool() {
	return (
		<MapProvider>
			<TravelFootprintView />
		</MapProvider>
	);
}
