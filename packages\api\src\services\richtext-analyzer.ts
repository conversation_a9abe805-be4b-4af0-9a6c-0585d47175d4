import type { JSONContent } from "@tiptap/core";

/**
 * 富文本内容分析结果
 */
export interface RichTextAnalysisResult {
	/** 提取的纯文本内容 */
	textContent: string;
	/** 图片URL列表 */
	imageUrls: string[];
	/** 结构化的内容块 */
	contentBlocks: ContentBlock[];
}

/**
 * 内容块类型
 */
export interface ContentBlock {
	type: "text" | "image" | "timeline" | "point";
	content: string;
	/** 如果是图片，包含图片URL */
	imageUrl?: string;
	/** 如果是时间线或点位，包含相关属性 */
	attributes?: Record<string, any>;
	/** 在原始内容中的位置 */
	position: number;
}

/**
 * 富文本内容分析器
 * 用于解析Tiptap JSONContent，提取文本、图片等信息
 */
export namespace RichTextAnalyzer {
	/**
	 * 分析富文本内容
	 * @param content Tiptap JSONContent
	 * @returns 分析结果
	 */
	export function analyze(content: JSONContent): RichTextAnalysisResult {
		const result: RichTextAnalysisResult = {
			textContent: "",
			imageUrls: [],
			contentBlocks: [],
		};

		const positionTracker = { value: 0 };

		// 递归遍历内容节点
		traverseContent(content, result, positionTracker);

		// 清理文本内容（移除多余空白）
		result.textContent = result.textContent.trim().replace(/\s+/g, " ");

		return result;
	}

	/**
	 * 分析富文本内容（别名方法，保持向后兼容）
	 * @param content Tiptap JSONContent
	 * @returns 分析结果
	 */
	export function analyzeContent(
		content: JSONContent,
	): RichTextAnalysisResult {
		return analyze(content);
	}

	/**
	 * 递归遍历内容节点
	 */
	function traverseContent(
		node: JSONContent,
		result: RichTextAnalysisResult,
		positionTracker: { value: number },
	): void {
		if (!node) return;

		// 处理不同类型的节点
		switch (node.type) {
			case "text": {
				// 文本节点
				if (node.text) {
					result.textContent += node.text;
					result.contentBlocks.push({
						type: "text",
						content: node.text,
						position: positionTracker.value++,
					});
				}
				break;
			}

			case "paragraph": {
				// 段落节点 - 递归处理子节点
				if (node.content) {
					for (const child of node.content) {
						traverseContent(child, result, positionTracker);
					}
				}
				// 段落结束添加换行
				result.textContent += "\n";
				break;
			}

			case "heading": {
				// 标题节点
				if (node.content) {
					const headingText = extractTextFromNode(node);
					result.textContent += `${headingText}\n`;
					result.contentBlocks.push({
						type: "text",
						content: headingText,
						position: positionTracker.value++,
					});
				}
				break;
			}

			case "image": {
				// 图片节点
				if (node.attrs?.src) {
					result.imageUrls.push(node.attrs.src);
					result.contentBlocks.push({
						type: "image",
						content: node.attrs.alt || "图片",
						imageUrl: node.attrs.src,
						position: positionTracker.value++,
					});
				}
				break;
			}

			case "travelTimeline": {
				// 旅行时间线节点
				const timelineTitle = node.attrs?.title || "时间线";
				result.textContent += `${timelineTitle}\n`;
				result.contentBlocks.push({
					type: "timeline",
					content: timelineTitle,
					attributes: node.attrs,
					position: positionTracker.value++,
				});

				// 递归处理时间线内的点位
				if (node.content) {
					for (const child of node.content) {
						traverseContent(child, result, positionTracker);
					}
				}
				break;
			}

			case "travelPoint": {
				// 旅行点位节点
				const pointLocation = node.attrs?.location || "地点";
				result.textContent += `${pointLocation}: `;
				result.contentBlocks.push({
					type: "point",
					content: pointLocation,
					attributes: node.attrs,
					position: positionTracker.value++,
				});

				// 处理点位内的描述内容
				if (node.content) {
					for (const child of node.content) {
						traverseContent(child, result, positionTracker);
					}
				}
				result.textContent += "\n";
				break;
			}

			default: {
				// 其他节点类型，递归处理子节点
				if (node.content) {
					for (const child of node.content) {
						traverseContent(child, result, positionTracker);
					}
				}
				break;
			}
		}
	}

	/**
	 * 从节点中提取纯文本
	 */
	function extractTextFromNode(node: JSONContent): string {
		if (node.type === "text" && node.text) {
			return node.text;
		}

		if (node.content) {
			return node.content
				.map((child) => extractTextFromNode(child))
				.join("");
		}

		return "";
	}

	/**
	 * 验证图片URL是否有效
	 */
	export function isValidImageUrl(url: string): boolean {
		try {
			const urlObj = new URL(url);
			// 检查是否是HTTP/HTTPS协议
			if (!["http:", "https:"].includes(urlObj.protocol)) {
				return false;
			}
			// 检查是否是常见的图片扩展名
			const imageExtensions = [
				".jpg",
				".jpeg",
				".png",
				".gif",
				".webp",
				".svg",
			];
			const pathname = urlObj.pathname.toLowerCase();
			return (
				imageExtensions.some((ext) => pathname.endsWith(ext)) ||
				pathname.includes("/image/") ||
				urlObj.searchParams.has("format")
			);
		} catch {
			return false;
		}
	}

	/**
	 * 过滤有效的图片URL
	 */
	export function filterValidImageUrls(urls: string[]): string[] {
		return urls.filter((url) => isValidImageUrl(url));
	}
}
