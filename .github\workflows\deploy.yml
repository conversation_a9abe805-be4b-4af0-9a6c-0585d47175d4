 name: Deploy

on:
  push:
    branches: [main]
    tags: ['v*']
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  NODE_VERSION: '20'
  PNPM_VERSION: '9.3.0'

jobs:
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
    environment:
      name: staging
      url: ${{ steps.deploy.outputs.url }}
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}
          
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Generate database client
        run: pnpm db:generate
        env:
          DATABASE_URL: ${{ secrets.STAGING_DATABASE_URL }}
          
      - name: Build applications
        run: pnpm build:all
        env:
          DATABASE_URL: ${{ secrets.STAGING_DATABASE_URL }}
          NEXT_PUBLIC_APP_URL: ${{ secrets.STAGING_APP_URL }}
          
      - name: Deploy to Vercel (Staging)
        id: deploy
        run: |
          # 这里添加你的部署逻辑
          echo "url=https://staging.your-app.com" >> $GITHUB_OUTPUT

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
    environment:
      name: production
      url: ${{ steps.deploy.outputs.url }}
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}
          
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Generate database client
        run: pnpm db:generate
        env:
          DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}
          
      - name: Build applications
        run: pnpm build:all
        env:
          DATABASE_URL: ${{ secrets.PRODUCTION_DATABASE_URL }}
          NEXT_PUBLIC_APP_URL: ${{ secrets.PRODUCTION_APP_URL }}
          
      - name: Deploy to Production
        id: deploy
        run: |
          # 这里添加你的生产部署逻辑
          echo "url=https://your-app.com" >> $GITHUB_OUTPUT

  notify:
    name: Notification
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always()
    steps:
      - name: Notify team
        run: |
          # 可以添加 Slack、Discord 或其他通知逻辑
          echo "Deployment completed"