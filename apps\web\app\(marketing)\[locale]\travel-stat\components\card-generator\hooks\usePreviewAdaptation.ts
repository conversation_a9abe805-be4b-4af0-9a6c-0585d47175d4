import { useEffect, useRef, useState } from "react";
import type { SocialPlatform } from "../types/cardTypes";
import { PLATFORM_DIMENSIONS } from "../utils/platformAdaptation";

export function usePreviewAdaptation(platform: SocialPlatform) {
	const [containerSize, setContainerSize] = useState({
		width: 800,
		height: 600,
	});
	const containerRef = useRef<HTMLDivElement>(null);

	// 监听容器尺寸变化 - 宽度占满模式
	useEffect(() => {
		const updateSize = () => {
			if (containerRef.current) {
				const { offsetWidth, offsetHeight } = containerRef.current;
				setContainerSize({
					width: Math.max(400, offsetWidth - 16), // 最小宽度，减去padding
					height: Math.max(300, offsetHeight - 120), // 最小高度参考值，实际将根据比例自适应
				});
			}
		};

		// 初始化尺寸
		updateSize();

		// 使用ResizeObserver获得更好的性能
		let resizeObserver: ResizeObserver | null = null;

		if (window.ResizeObserver && containerRef.current) {
			resizeObserver = new ResizeObserver(updateSize);
			resizeObserver.observe(containerRef.current);
		} else {
			// 降级到window resize事件
			window.addEventListener("resize", updateSize);
		}

		return () => {
			if (resizeObserver) {
				resizeObserver.disconnect();
			} else {
				window.removeEventListener("resize", updateSize);
			}
		};
	}, []);

	// 计算宽度占满的预览适配信息
	const previewAdaptation = (() => {
		const platformDimensions =
			PLATFORM_DIMENSIONS[platform] || PLATFORM_DIMENSIONS.instagram;
		const { width: cardWidth, height: cardHeight } = platformDimensions;

		// 宽度占满容器（减去边距）
		const margin = 8;
		const scale = (containerSize.width - margin * 2) / cardWidth;
		const actualHeight = cardHeight * scale;

		return {
			scale: Math.max(0.1, scale),
			dimensions: platformDimensions,
			actualHeight,
			containerSize,
		};
	})();

	return {
		containerRef,
		containerSize,
		previewAdaptation,
	};
}
