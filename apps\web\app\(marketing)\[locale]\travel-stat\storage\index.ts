// 接口和类型
export type { IDataStorage, StorageConfig } from "./interfaces";
export { DataStorageError } from "./interfaces";

// 数据类型
export type {
	TravelFootprintData,
	DataOperationResult,
	DataMigration,
} from "./types";
export {
	DEFAULT_TRAVEL_DATA,
	STORAGE_KEYS,
} from "./types";

// 存储提供者
export { LocalStorageProvider } from "./providers/LocalStorageProvider";

// 数据管理器
export { TravelFootprintDataManager } from "./DataManager";

import { TravelFootprintDataManager } from "./DataManager";
// 导入需要在此文件中使用的类型和类
import type { IDataStorage } from "./interfaces";

// 创建默认的数据管理器实例
export const createDataManager = (storage?: IDataStorage) => {
	return new TravelFootprintDataManager(storage);
};

// 单例模式的数据管理器
let globalDataManager: TravelFootprintDataManager | null = null;

export const getGlobalDataManager = (): TravelFootprintDataManager => {
	if (!globalDataManager) {
		globalDataManager = new TravelFootprintDataManager();
	}
	return globalDataManager;
};

export const destroyGlobalDataManager = (): void => {
	if (globalDataManager) {
		globalDataManager.destroy();
		globalDataManager = null;
	}
};
