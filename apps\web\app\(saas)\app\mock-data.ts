import { getUTCDate } from "./components/diary/TravelPointForm";
import type { TravelPoint } from "./components/diary/travel-point-form/types";

/**
 * 定义旅行日记接口
 */
export interface TravelDiary {
	id: string;
	title: string;
	subtitle: string;
	coverImage?: string;
	timelines: TravelTimeline[];
}

/**
 * 定义时间线接口
 */
export interface TravelTimeline {
	id: string;
	title: string;
	date: Date;
	points: TravelPoint[];
}

/**
 * 模拟的旅行点位数据
 * 注意: 这是临时测试数据，将来会被API数据替换
 */
export const mockTravelPoints: TravelPoint[] = [
	{
		id: "1",
		date: getUTCDate(new Date("2023-10-01")),
		location: "东京塔",
		description:
			"东京的标志性建筑，晚上的灯光非常漂亮，可以看到东京湾的夜景。",
		images: [
			{
				url: "https://images.unsplash.com/photo-1536098561742-ca998e48cbcc?q=80&w=1000",
			},
			{
				url: "https://images.unsplash.com/photo-1503899036084-c55cdd92da26?q=80&w=1000",
			},
		],
		iconType: "landmark",
		coordinates: {
			lat: 35.6586,
			lng: 139.7454,
		},
	},
	{
		id: "2",
		date: getUTCDate(new Date("2023-10-02")),
		location: "涩谷十字路口",
		description:
			"世界上最繁忙的十字路口之一，人流量巨大，是东京的城市脉搏。",
		images: [
			{
				url: "https://images.unsplash.com/photo-1542051841857-5f90071e7989?q=80&w=1000",
			},
		],
		iconType: "landmark",
		coordinates: {
			lat: 35.6594,
			lng: 139.7005,
		},
	},
	{
		id: "3",
		date: getUTCDate(new Date("2023-10-03")),
		location: "新宿御苑",
		description:
			"东京市中心的一片绿洲，春天樱花盛开，秋天枫叶如火，四季皆美。",
		images: [
			{
				url: "https://images.unsplash.com/photo-1557684387-08927d28c72a?q=80&w=1000",
			},
		],
		iconType: "park",
		coordinates: {
			lat: 35.6852,
			lng: 139.71,
		},
	},
	{
		id: "4",
		date: getUTCDate(new Date("2023-10-03")),
		location: "一兰拉面 新宿店",
		description: "顶级连锁拉面店，猪骨汤底浓郁，面条劲道，配料新鲜。",
		images: [
			{
				url: "https://images.unsplash.com/photo-1617196035154-1e7e6e28b0db?q=80&w=1000",
			},
		],
		iconType: "food",
		coordinates: {
			lat: 35.6938,
			lng: 139.7034,
		},
	},
	{
		id: "5",
		date: getUTCDate(new Date("2023-10-04")),
		location: "浅草寺",
		description:
			"东京最古老的寺庙，始建于公元628年，雷门和五重塔是标志性建筑。",
		images: [
			{
				url: "https://images.unsplash.com/photo-1551641506-ee5bf4cb45f1?q=80&w=1000",
			},
			{
				url: "https://images.unsplash.com/photo-1545569341-9eb8b30979d9?q=80&w=1000",
			},
		],
		iconType: "landmark",
		coordinates: {
			lat: 35.7147,
			lng: 139.7966,
		},
	},
	{
		id: "6",
		date: getUTCDate(new Date("2023-10-05")),
		location: "箱根汤本站",
		description: "前往箱根温泉区的门户，风景如画，可以看到富士山的轮廓。",
		images: [
			{
				url: "https://images.unsplash.com/photo-1545569341-85bf7be332c8?q=80&w=1000",
			},
		],
		iconType: "landmark",
		coordinates: {
			lat: 35.2323,
			lng: 139.1029,
		},
	},
	{
		id: "7",
		date: getUTCDate(new Date("2023-10-05")),
		location: "箱根町港",
		description:
			"芦之湖上的港口，可以乘坐海盗船游览芦之湖，欣赏富士山美景。",
		images: [
			{
				url: "https://images.unsplash.com/photo-1565618754518-e9be99cf86a3?q=80&w=1000",
			},
		],
		iconType: "landmark",
		coordinates: {
			lat: 35.2047,
			lng: 139.0226,
		},
	},
	{
		id: "8",
		date: getUTCDate(new Date("2023-10-06")),
		location: "银座商业区",
		description: "东京最著名的购物区，云集了世界各大奢侈品牌和百货公司。",
		images: [
			{
				url: "https://images.unsplash.com/photo-1551641506-f9431a001dcf?q=80&w=1000",
			},
		],
		iconType: "landmark",
		coordinates: {
			lat: 35.6721,
			lng: 139.7636,
		},
	},
];

/**
 * 模拟的旅行日记数据
 */
export const mockTravelDiaries: TravelDiary[] = [
	{
		id: "kansai-trip",
		title: "日本关西3日游",
		subtitle: "探索京都、奈良与大阪的文化与美食之旅",
		coverImage:
			"https://images.unsplash.com/photo-1493976040374-85c8e12f0c0e?q=80&w=1000",
		timelines: [
			{
				id: "day-1",
				title: "京都",
				date: getUTCDate(new Date("2023-10-01")),
				points: [
					{
						id: "kix-airport",
						date: getUTCDate(new Date("2023-10-01")),
						location: "关西国际机场",
						description:
							"抵达关西国际机场，搭乘JR特急Haruka前往京都站。机场设施完善，有多种交通方式可选择。",
						images: [
							{
								url: "https://images.unsplash.com/photo-1493780474015-ba834fd0ce2f?q=80&w=1000",
							},
						],
						iconType: "landmark",
						coordinates: {
							lat: 34.432,
							lng: 135.2304,
						},
					},
					{
						id: "kyoto-station",
						date: getUTCDate(new Date("2023-10-01")),
						location: "京都站",
						description:
							"现代化的巨大建筑，集合了购物中心、酒店和交通枢纽于一体。是京都的门户和标志性建筑。",
						images: [
							{
								url: "https://images.unsplash.com/photo-1478436127897-769e1b3f0f36?q=80&w=1000",
							},
							{
								url: "https://images.unsplash.com/photo-1493780474015-ba834fd0ce2f?q=80&w=1000",
							},
							{
								url: "https://images.unsplash.com/photo-1493780474015-ba834fd0ce2f?q=80&w=1000",
							},
						],
						iconType: "landmark",
						coordinates: {
							lat: 34.9858,
							lng: 135.7588,
						},
					},
					{
						id: "fushimi-inari",
						date: getUTCDate(new Date("2023-10-01")),
						location: "伏见稻荷大社",
						description:
							"著名的千本鸟居，绵延于稻荷山中的橙红色鸟居隧道是京都最具代表性的景观之一。",
						images: [
							{
								url: "https://images.unsplash.com/photo-1478436127897-769e1b3f0f36?q=80&w=1000",
							},
							{
								url: "https://images.unsplash.com/photo-1493780474015-ba834fd0ce2f?q=80&w=1000",
							},
							{
								url: "https://images.unsplash.com/photo-1493780474015-ba834fd0ce2f?q=80&w=1000",
							},
							{
								url: "https://images.unsplash.com/photo-1493780474015-ba834fd0ce2f?q=80&w=1000",
							},
							{
								url: "https://images.unsplash.com/photo-1493780474015-ba834fd0ce2f?q=80&w=1000",
							},
							{
								url: "https://images.unsplash.com/photo-1493780474015-ba834fd0ce2f?q=80&w=1000",
							},
							{
								url: "https://images.unsplash.com/photo-1493780474015-ba834fd0ce2f?q=80&w=1000",
							},
							{
								url: "https://images.unsplash.com/photo-1493780474015-ba834fd0ce2f?q=80&w=1000",
							},
						],
						iconType: "landmark",
						coordinates: {
							lat: 34.9671,
							lng: 135.7727,
						},
					},
					{
						id: "gion-district",
						date: getUTCDate(new Date("2023-10-01")),
						location: "祇园花见小路",
						description:
							"京都最著名的艺伎区，保存着传统的木制建筑和石铺道路，特别是晚上很有氛围。",
						images: [
							{
								url: "https://images.unsplash.com/photo-1493780474015-ba834fd0ce2f?q=80&w=1000",
							},
						],
						iconType: "landmark",
						coordinates: {
							lat: 35.0036,
							lng: 135.7755,
						},
					},
				],
			},
			{
				id: "day-2",
				title: "奈良",
				date: getUTCDate(new Date("2023-10-02")),
				points: [
					{
						id: "nara-park",
						date: getUTCDate(new Date("2023-10-02")),
						location: "奈良公园",
						description:
							"以自由漫步的鹿群闻名，这些鹿被视为神的使者，游客可以购买专门的鹿饼来喂食它们。",
						images: [
							{
								url: "https://images.unsplash.com/photo-1493780474015-ba834fd0ce2f?q=80&w=1000",
							},
							{
								url: "https://images.unsplash.com/photo-1493780474015-ba834fd0ce2f?q=80&w=1000",
							},
						],
						iconType: "park",
						coordinates: {
							lat: 34.685,
							lng: 135.843,
						},
					},
					{
						id: "todaiji-temple",
						date: getUTCDate(new Date("2023-10-02")),
						location: "东大寺",
						description:
							"世界上最大的木结构建筑之一，内有高达15米的巨大青铜佛像（大佛），令人叹为观止。",
						images: [
							{
								url: "https://images.unsplash.com/photo-1493780474015-ba834fd0ce2f?q=80&w=1000",
							},
						],
						iconType: "landmark",
						coordinates: {
							lat: 34.6891,
							lng: 135.8397,
						},
					},
					{
						id: "naramachi",
						date: getUTCDate(new Date("2023-10-02")),
						location: "奈良町",
						description:
							"古朴的商业街区，保留着江户时期的建筑风格，有许多精品店、咖啡馆和传统工艺品商店。",
						images: [
							{
								url: "https://images.unsplash.com/photo-1493780474015-ba834fd0ce2f?q=80&w=1000",
							},
						],
						iconType: "landmark",
						coordinates: {
							lat: 34.6778,
							lng: 135.8282,
						},
					},
				],
			},
			{
				id: "day-3",
				title: "大阪",
				date: getUTCDate(new Date("2023-10-03")),
				points: [
					{
						id: "osaka-castle",
						date: getUTCDate(new Date("2023-10-03")),
						location: "大阪城",
						description:
							"日本著名的城堡，始建于16世纪，由丰臣秀吉所建，现在是大阪的象征。城内有博物馆展示大阪历史。",
						images: [
							{
								url: "https://images.unsplash.com/photo-1590274853856-f22d5ee3d228?q=80&w=1000",
							},
						],
						iconType: "landmark",
						coordinates: {
							lat: 34.6873,
							lng: 135.5262,
						},
					},
					{
						id: "dotonbori",
						date: getUTCDate(new Date("2023-10-03")),
						location: "道顿堀",
						description:
							"大阪最著名的美食街和购物区，以霓虹灯广告和巨大的食物招牌闻名，是品尝大阪美食的必去之地。",
						images: [
							{
								url: "https://images.unsplash.com/photo-1590274853856-f22d5ee3d228?q=80&w=1000",
							},
							{
								url: "https://images.unsplash.com/photo-1590274853856-f22d5ee3d228?q=80&w=1000",
							},
						],
						iconType: "food",
						coordinates: {
							lat: 34.6687,
							lng: 135.5022,
						},
					},
					{
						id: "kuromon-market",
						date: getUTCDate(new Date("2023-10-03")),
						location: "黑门市场",
						description:
							"有'大阪厨房'之称的传统市场，拥有近200家店铺，贩售新鲜海鲜、水果、小吃等，可以边走边吃。",
						images: [
							{
								url: "https://images.unsplash.com/photo-1590274853856-f22d5ee3d228?q=80&w=1000",
							},
						],
						iconType: "food",
						coordinates: {
							lat: 34.6659,
							lng: 135.5046,
						},
					},
					{
						id: "umeda-sky",
						date: getUTCDate(new Date("2023-10-03")),
						location: "梅田空中庭院",
						description:
							"由两座40层高楼连接而成的现代建筑，顶层有空中观景台，可俯瞰大阪全景，尤其是夜景非常壮观。",
						images: [
							{
								url: "https://images.unsplash.com/photo-1590274853856-f22d5ee3d228?q=80&w=1000",
							},
						],
						iconType: "landmark",
						coordinates: {
							lat: 34.7052,
							lng: 135.4957,
						},
					},
				],
			},
		],
	},
];

/**
 * 获取模拟旅行点位数据的API适配器
 * 这个函数模拟了API请求，后续可以直接替换为真实的API调用
 */
export async function fetchTravelPoints(): Promise<TravelPoint[]> {
	// 模拟网络延迟
	await new Promise((resolve) => setTimeout(resolve, 300));

	// 返回模拟数据
	return [...mockTravelPoints];
}

/**
 * 获取模拟旅行日记数据的API适配器
 */
export async function fetchTravelDiaries(): Promise<TravelDiary[]> {
	// 模拟网络延迟
	await new Promise((resolve) => setTimeout(resolve, 300));

	// 返回模拟数据
	return [...mockTravelDiaries];
}

/**
 * 获取特定旅行日记的API适配器
 */
export async function fetchTravelDiary(
	id: string,
): Promise<TravelDiary | null> {
	// 模拟网络延迟
	await new Promise((resolve) => setTimeout(resolve, 600));

	// 查找并返回特定日记
	const diary = mockTravelDiaries.find((diary) => diary.id === id);
	return diary || null;
}

/**
 * 添加旅行点位的API适配器
 */
export async function addTravelPoint(point: TravelPoint): Promise<TravelPoint> {
	// 模拟网络延迟
	await new Promise((resolve) => setTimeout(resolve, 500));

	// 在实际应用中，这里会是API调用
	// 现在我们只是返回传入的点位信息，并假装它已被保存
	return point;
}

/**
 * 编辑旅行点位的API适配器
 */
export async function updateTravelPoint(
	id: string,
	data: Partial<TravelPoint>,
): Promise<TravelPoint> {
	// 模拟网络延迟
	await new Promise((resolve) => setTimeout(resolve, 500));

	// 模拟API调用
	// 这里简单返回合并后的对象
	const existingPoint = mockTravelPoints.find((point) => point.id === id);

	if (!existingPoint) {
		throw new Error(`找不到ID为${id}的旅行点位`);
	}

	return {
		...existingPoint,
		...data,
	};
}

/**
 * 删除旅行点位的API适配器
 */
export async function deleteTravelPoint(
	id: string,
): Promise<{ success: boolean }> {
	// 模拟网络延迟
	await new Promise((resolve) => setTimeout(resolve, 500));

	// 模拟API调用
	// 在实际应用中，这会是到后端的DELETE请求
	return { success: true };
}

/**
 * 创建新旅行日记的API适配器
 */
export async function createTravelDiary(
	diary: Omit<TravelDiary, "id">,
): Promise<TravelDiary> {
	// 模拟网络延迟
	await new Promise((resolve) => setTimeout(resolve, 500));

	// 生成唯一ID (在实际应用中这会由后端处理)
	const id = `diary-${Date.now()}`;

	// 构建新的日记对象
	const newDiary: TravelDiary = {
		id,
		...diary,
	};

	// 在实际应用中，这里会是API调用保存日记
	// 现在只是模拟返回创建的对象
	return newDiary;
}

/**
 * 更新旅行日记的API适配器
 */
export async function updateTravelDiary(
	id: string,
	data: Partial<TravelDiary>,
): Promise<TravelDiary> {
	// 模拟网络延迟
	await new Promise((resolve) => setTimeout(resolve, 500));

	// 查找特定日记
	const existingDiary = mockTravelDiaries.find((diary) => diary.id === id);

	if (!existingDiary) {
		throw new Error(`找不到ID为${id}的旅行日记`);
	}

	// 合并数据并返回
	return {
		...existingDiary,
		...data,
	};
}

/**
 * 删除旅行日记的API适配器
 */
export async function deleteTravelDiary(
	id: string,
): Promise<{ success: boolean }> {
	// 模拟网络延迟
	await new Promise((resolve) => setTimeout(resolve, 500));

	// 验证日记是否存在
	const existingDiary = mockTravelDiaries.find((diary) => diary.id === id);

	if (!existingDiary) {
		throw new Error(`找不到ID为${id}的旅行日记`);
	}

	// 模拟成功删除
	return { success: true };
}

/**
 * 添加新时间线到旅行日记的API适配器
 */
export async function addTimelineToTravelDiary(
	diaryId: string,
	timeline: Omit<TravelTimeline, "id">,
): Promise<TravelTimeline> {
	// 模拟网络延迟
	await new Promise((resolve) => setTimeout(resolve, 500));

	// 查找特定日记
	const existingDiary = mockTravelDiaries.find(
		(diary) => diary.id === diaryId,
	);

	if (!existingDiary) {
		throw new Error(`找不到ID为${diaryId}的旅行日记`);
	}

	// 生成时间线ID
	const id = `timeline-${Date.now()}`;

	// 创建新时间线
	const newTimeline: TravelTimeline = {
		id,
		...timeline,
	};

	// 在实际应用中，这会更新数据库
	// 这里仅返回新创建的时间线
	return newTimeline;
}

/**
 * 更新时间线的API适配器
 */
export async function updateTimeline(
	diaryId: string,
	timelineId: string,
	data: Partial<TravelTimeline>,
): Promise<TravelTimeline> {
	// 模拟网络延迟
	await new Promise((resolve) => setTimeout(resolve, 500));

	// 查找特定日记
	const existingDiary = mockTravelDiaries.find(
		(diary) => diary.id === diaryId,
	);

	if (!existingDiary) {
		throw new Error(`找不到ID为${diaryId}的旅行日记`);
	}

	// 查找特定时间线
	const existingTimeline = existingDiary.timelines.find(
		(timeline) => timeline.id === timelineId,
	);

	if (!existingTimeline) {
		throw new Error(`找不到ID为${timelineId}的时间线`);
	}

	// 合并并返回更新后的时间线
	return {
		...existingTimeline,
		...data,
	};
}

/**
 * 删除时间线的API适配器
 */
export async function deleteTimeline(
	diaryId: string,
	timelineId: string,
): Promise<{ success: boolean }> {
	// 模拟网络延迟
	await new Promise((resolve) => setTimeout(resolve, 500));

	// 查找特定日记
	const existingDiary = mockTravelDiaries.find(
		(diary) => diary.id === diaryId,
	);

	if (!existingDiary) {
		throw new Error(`找不到ID为${diaryId}的旅行日记`);
	}

	// 查找特定时间线
	const existingTimeline = existingDiary.timelines.find(
		(timeline) => timeline.id === timelineId,
	);

	if (!existingTimeline) {
		throw new Error(`找不到ID为${timelineId}的时间线`);
	}

	// 模拟成功删除
	return { success: true };
}

/**
 * 添加旅行点位到特定时间线的API适配器
 */
export async function addPointToTimeline(
	diaryId: string,
	timelineId: string,
	point: Omit<TravelPoint, "id">,
): Promise<TravelPoint> {
	// 模拟网络延迟
	await new Promise((resolve) => setTimeout(resolve, 500));

	// 查找特定日记
	const existingDiary = mockTravelDiaries.find(
		(diary) => diary.id === diaryId,
	);

	if (!existingDiary) {
		throw new Error(`找不到ID为${diaryId}的旅行日记`);
	}

	// 查找特定时间线
	const existingTimeline = existingDiary.timelines.find(
		(timeline) => timeline.id === timelineId,
	);

	if (!existingTimeline) {
		throw new Error(`找不到ID为${timelineId}的时间线`);
	}

	// 生成点位ID
	const id = `point-${Date.now()}`;

	// 创建新点位
	const newPoint: TravelPoint = {
		id,
		...point,
	};

	// 在实际应用中，这会更新数据库
	// 这里仅返回新创建的点位
	return newPoint;
}

/**
 * 获取特定时间线的API适配器
 */
export async function fetchTimeline(
	diaryId: string,
	timelineId: string,
): Promise<TravelTimeline | null> {
	// 模拟网络延迟
	await new Promise((resolve) => setTimeout(resolve, 500));

	// 查找特定日记
	const existingDiary = mockTravelDiaries.find(
		(diary) => diary.id === diaryId,
	);

	if (!existingDiary) {
		return null;
	}

	// 查找并返回特定时间线
	const timeline = existingDiary.timelines.find(
		(timeline) => timeline.id === timelineId,
	);

	return timeline || null;
}

/**
 * 搜索旅行日记的API适配器
 */
export async function searchTravelDiaries(
	query: string,
): Promise<TravelDiary[]> {
	// 模拟网络延迟
	await new Promise((resolve) => setTimeout(resolve, 300));

	// 如果查询为空，返回所有日记
	if (!query.trim()) {
		return [...mockTravelDiaries];
	}

	// 简单的搜索逻辑：标题或副标题包含查询字符串
	const lowerQuery = query.toLowerCase();
	return mockTravelDiaries.filter(
		(diary) =>
			diary.title.toLowerCase().includes(lowerQuery) ||
			diary.subtitle.toLowerCase().includes(lowerQuery),
	);
}

/**
 * 搜索旅行点位的API适配器
 */
export async function searchTravelPoints(
	query: string,
): Promise<TravelPoint[]> {
	// 模拟网络延迟
	await new Promise((resolve) => setTimeout(resolve, 300));

	// 如果查询为空，返回所有点位
	if (!query.trim()) {
		return [...mockTravelPoints];
	}

	// 简单的搜索逻辑：地点名称或描述包含查询字符串
	const lowerQuery = query.toLowerCase();
	return mockTravelPoints.filter(
		(point) =>
			point.location.toLowerCase().includes(lowerQuery) ||
			point.description.toLowerCase().includes(lowerQuery),
	);
}

/**
 * 获取按日期范围过滤的旅行点位的API适配器
 */
export async function getTravelPointsByDateRange(
	startDate: Date,
	endDate: Date,
): Promise<TravelPoint[]> {
	// 模拟网络延迟
	await new Promise((resolve) => setTimeout(resolve, 300));

	// 过滤在日期范围内的点位
	return mockTravelPoints.filter((point) => {
		const pointDate = new Date(point.date);
		return pointDate >= startDate && pointDate <= endDate;
	});
}

/**
 * 获取用户所有旅行日记的统计信息
 */
export async function getTravelStats(): Promise<{
	totalDiaries: number;
	totalPoints: number;
	totalDistanceKm: number;
	mostVisitedType: string;
}> {
	// 模拟网络延迟
	await new Promise((resolve) => setTimeout(resolve, 500));

	// 计算点位类型分布
	const typeCounts: Record<string, number> = {};
	mockTravelPoints.forEach((point) => {
		typeCounts[point.iconType] = (typeCounts[point.iconType] || 0) + 1;
	});

	// 找出最常访问的类型
	let mostVisitedType = "landmark";
	let maxCount = 0;

	Object.entries(typeCounts).forEach(([type, count]) => {
		if (count > maxCount) {
			mostVisitedType = type;
			maxCount = count;
		}
	});

	// 返回统计数据
	return {
		totalDiaries: mockTravelDiaries.length,
		totalPoints: mockTravelPoints.length,
		totalDistanceKm: 1250.5, // 模拟数据
		mostVisitedType,
	};
}
