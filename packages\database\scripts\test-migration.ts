import type { TravelPointImage } from "../src/types/travel-diary";

// 模拟旧格式数据
const oldFormatData = {
	timelines: [
		{
			id: "timeline-1",
			title: "Day 1",
			date: "2024-01-01",
			points: [
				{
					id: "point-1",
					location: "Tokyo Tower",
					description: "Amazing view",
					date: "2024-01-01",
					images: [
						"https://example.com/image1.jpg",
						"https://example.com/image2.jpg",
					],
					iconType: "LANDMARK",
					latitude: 35.6586,
					longitude: 139.7454,
					order: 1,
				},
			],
		},
	],
};

// 模拟混合格式数据
const mixedFormatData = {
	timelines: [
		{
			id: "timeline-1",
			title: "Day 1",
			date: "2024-01-01",
			points: [
				{
					id: "point-1",
					location: "Tokyo Tower",
					description: "Amazing view",
					date: "2024-01-01",
					images: [
						"https://example.com/image1.jpg", // 旧格式
						{
							url: "https://example.com/image2.jpg",
							description: "Beautiful sunset",
							alt: "Sunset view",
							caption: "Tokyo sunset",
							uploadedAt: "2024-01-01T12:00:00.000Z",
						}, // 新格式
					],
					iconType: "LANDMARK",
					latitude: 35.6586,
					longitude: 139.7454,
					order: 1,
				},
			],
		},
	],
};

// 导入迁移函数（模拟）
function convertImageStringToObject(imageUrl: string): TravelPointImage {
	return {
		url: imageUrl,
		description: "",
		alt: "",
		caption: "",
		uploadedAt: new Date().toISOString(),
	};
}

function isNewImageFormat(image: any): image is TravelPointImage {
	return (
		typeof image === "object" &&
		image !== null &&
		typeof image.url === "string" &&
		"description" in image
	);
}

function convertTravelPointImages(point: any): any {
	if (!point.images || !Array.isArray(point.images)) {
		return {
			...point,
			images: [],
		};
	}

	const convertedImages: TravelPointImage[] = point.images
		.map((image: any) => {
			// 如果已经是新格式，直接返回
			if (isNewImageFormat(image)) {
				return image;
			}

			// 如果是字符串格式，转换为对象格式
			if (typeof image === "string") {
				return convertImageStringToObject(image);
			}

			// 如果是其他格式，尝试提取 URL
			if (typeof image === "object" && image !== null && image.url) {
				return {
					url: image.url,
					description: image.description || "",
					alt: image.alt || "",
					caption: image.caption || "",
					uploadedAt: image.uploadedAt || new Date().toISOString(),
				};
			}

			// 无法识别的格式，跳过
			console.warn("无法识别的图片格式:", image);
			return null;
		})
		.filter(
			(img: TravelPointImage | null): img is TravelPointImage =>
				img !== null,
		);

	return {
		...point,
		images: convertedImages,
	};
}

function convertDiaryContent(content: any): any {
	if (!content || !Array.isArray(content.timelines)) {
		console.warn("无效的日记内容格式:", content);
		return { timelines: [] };
	}

	const convertedTimelines = content.timelines.map((timeline: any) => {
		if (!Array.isArray(timeline.points)) {
			return timeline;
		}

		const convertedPoints = timeline.points.map((point: any) =>
			convertTravelPointImages(point),
		);

		return {
			...timeline,
			points: convertedPoints,
		};
	});

	return {
		timelines: convertedTimelines,
	};
}

// 测试函数
function runTests() {
	console.log("=== 图片格式迁移逻辑测试 ===\n");

	// 测试1: 旧格式转换
	console.log("测试1: 旧格式数据转换");
	console.log(
		"原始数据:",
		JSON.stringify(oldFormatData.timelines[0].points[0].images, null, 2),
	);

	const convertedOld = convertDiaryContent(oldFormatData);
	console.log(
		"转换后:",
		JSON.stringify(convertedOld.timelines[0].points[0].images, null, 2),
	);

	// 验证转换结果
	const oldImages = convertedOld.timelines[0].points[0].images;
	const allNewFormat = oldImages.every((img: any) => isNewImageFormat(img));
	console.log("✅ 所有图片都是新格式:", allNewFormat);
	console.log("图片数量:", oldImages.length);
	console.log("");

	// 测试2: 混合格式转换
	console.log("测试2: 混合格式数据转换");
	console.log(
		"原始数据:",
		JSON.stringify(mixedFormatData.timelines[0].points[0].images, null, 2),
	);

	const convertedMixed = convertDiaryContent(mixedFormatData);
	console.log(
		"转换后:",
		JSON.stringify(convertedMixed.timelines[0].points[0].images, null, 2),
	);

	// 验证转换结果
	const mixedImages = convertedMixed.timelines[0].points[0].images;
	const allNewFormatMixed = mixedImages.every((img: any) =>
		isNewImageFormat(img),
	);
	console.log("✅ 所有图片都是新格式:", allNewFormatMixed);
	console.log("图片数量:", mixedImages.length);
	console.log("");

	// 测试3: 已经是新格式的数据
	console.log("测试3: 新格式数据保持不变");
	const newFormatData = {
		timelines: [
			{
				id: "timeline-1",
				title: "Day 1",
				date: "2024-01-01",
				points: [
					{
						id: "point-1",
						location: "Tokyo Tower",
						description: "Amazing view",
						date: "2024-01-01",
						images: [
							{
								url: "https://example.com/image1.jpg",
								description: "Beautiful view",
								alt: "Tokyo Tower",
								caption: "Tokyo Tower at sunset",
								uploadedAt: "2024-01-01T12:00:00.000Z",
							},
						],
						iconType: "LANDMARK",
						latitude: 35.6586,
						longitude: 139.7454,
						order: 1,
					},
				],
			},
		],
	};

	const originalJson = JSON.stringify(newFormatData);
	const convertedNew = convertDiaryContent(newFormatData);
	const convertedJson = JSON.stringify(convertedNew);

	console.log("✅ 新格式数据保持不变:", originalJson === convertedJson);
	console.log("");

	console.log("🎉 所有测试通过！迁移逻辑正确。");
}

// 运行测试
if (require.main === module) {
	runTests();
}

export { runTests };
