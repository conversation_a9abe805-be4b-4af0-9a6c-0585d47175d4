/* 自定义滚动条样式 */
.scrollbar-travel::-webkit-scrollbar {
	width: 6px;
	height: 6px;
}

.scrollbar-travel::-webkit-scrollbar-track {
	background: transparent;
}

.scrollbar-travel::-webkit-scrollbar-thumb {
	background-color: rgba(156, 163, 175, 0.5);
	border-radius: 10px;
}

.scrollbar-travel::-webkit-scrollbar-thumb:hover {
	background-color: rgba(107, 114, 128, 0.7);
}

/* 深色模式滚动条 */
.dark .scrollbar-travel::-webkit-scrollbar-thumb {
	background-color: rgba(75, 85, 99, 0.5);
}

.dark .scrollbar-travel::-webkit-scrollbar-thumb:hover {
	background-color: rgba(107, 114, 128, 0.7);
}

/* 清新文艺风格滚动条 */
.scrollbar-artistic::-webkit-scrollbar {
	width: 8px;
	height: 8px;
}

.scrollbar-artistic::-webkit-scrollbar-track {
	background: rgba(226, 232, 240, 0.3);
	border-radius: 10px;
}

.scrollbar-artistic::-webkit-scrollbar-thumb {
	background: linear-gradient(180deg, #b0cde4 0%, #a2c8e0 100%);
	border-radius: 10px;
	border: 2px solid transparent;
	background-clip: content-box;
	transition: all 0.3s ease;
}

.scrollbar-artistic::-webkit-scrollbar-thumb:hover {
	background: linear-gradient(180deg, #93b7d5 0%, #7ca6c8 100%);
	border: 1.5px solid transparent;
	background-clip: content-box;
}

/* 支持火狐浏览器 */
.scrollbar-artistic {
	scrollbar-width: thin;
	scrollbar-color: #a2c8e0 rgba(226, 232, 240, 0.3);
}
