"use client";

import type { LatLng } from "./types";

/**
 * 创建虚线流动动画效果
 *
 * @param polyline - Google Maps Polyline 实例
 * @param options - 动画选项
 * @returns 取消动画的函数
 */
export function createFlowingDashedLine(
	polyline: google.maps.Polyline,
	options: {
		speed?: number; // 流动速度，默认为1
		dashLength?: number; // 虚线长度，默认为20
		gapLength?: number; // 间隙长度，默认为10
		color?: string; // 路线颜色，默认为蓝色
		secondaryColor?: string; // 第二颜色，用于双色效果
	} = {},
): () => void {
	const {
		speed = 1,
		dashLength = 20,
		gapLength = 10,
		color = "#3B82F6", // 蓝色
		secondaryColor = "#60A5FA", // 浅蓝色
	} = options;

	// 设置初始虚线样式
	polyline.setOptions({
		strokeOpacity: 0, // 隐藏原来的线
		icons: [
			{
				icon: {
					path: "M 0,-1 0,1",
					strokeOpacity: 1,
					strokeWeight: 3,
					scale: dashLength,
					strokeColor: color,
				},
				offset: "0",
				repeat: `${dashLength}px ${gapLength}px`, // 虚线模式
			},
			{
				icon: {
					path: "M 0,-1 0,1",
					strokeOpacity: 0.6,
					strokeWeight: 3,
					scale: dashLength / 2,
					strokeColor: secondaryColor,
				},
				offset: `${dashLength + gapLength / 2}px`,
				repeat: `${dashLength + gapLength}px`,
			},
			// 添加箭头图标指示方向
			{
				icon: {
					path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
					fillColor: color,
					fillOpacity: 1,
					strokeColor: color,
					strokeWeight: 1,
					scale: 3,
				},
				offset: "50%", // 箭头显示在路线中间
				repeat: "25%", // 每隔1/4的距离重复一次
			},
		],
	});

	// 动画变量
	let offset = 0;
	let animationFrameId: number | null = null;

	// 动画函数
	const animate = () => {
		offset = (offset + speed) % (dashLength + gapLength);

		const icons = polyline.get("icons");

		// 更新第一个图标的偏移量，创建流动效果
		if (icons && icons.length > 0) {
			icons[0].offset = `${offset}px`;

			// 第二个图标反方向移动，制造交错效果
			if (icons.length > 1) {
				icons[1].offset = `${dashLength + gapLength - offset}px`;
			}

			polyline.set("icons", icons);
		}

		// 继续下一帧动画
		animationFrameId = window.requestAnimationFrame(animate);
	};

	// 启动动画
	animationFrameId = window.requestAnimationFrame(animate);

	// 返回取消动画的函数
	return () => {
		if (animationFrameId !== null) {
			window.cancelAnimationFrame(animationFrameId);
			animationFrameId = null;
		}
	};
}

/**
 * 生成简单的直线路径
 *
 * @param start - 起始坐标
 * @param end - 结束坐标
 * @returns 简单路径点数组
 */
export function generateSmoothPath(
	start: LatLng,
	end: LatLng,
	numPoints = 2, // 不再使用此参数，保留是为了兼容性
): LatLng[] {
	// 直接返回起点和终点，不再生成中间点
	return [start, end];
}

/**
 * 创建渐变色路线
 *
 * @param path - 路径点
 * @param startColor - 起始颜色 (十六进制)
 * @param endColor - 结束颜色 (十六进制)
 * @param map - Google Maps实例
 * @returns 路线段数组
 */
export function createGradientRoute(
	path: LatLng[],
	startColor = "#3B82F6", // 蓝色
	endColor = "#F472B6", // 粉色
	map: google.maps.Map,
): google.maps.Polyline[] {
	if (path.length < 2) return [];

	// 解析颜色函数
	const parseColor = (hex: string) => {
		const r = Number.parseInt(hex.slice(1, 3), 16);
		const g = Number.parseInt(hex.slice(3, 5), 16);
		const b = Number.parseInt(hex.slice(5, 7), 16);
		return { r, g, b };
	};

	// 起始和结束颜色
	const colorStart = parseColor(startColor);
	const colorEnd = parseColor(endColor);

	// 创建多个线段以实现渐变效果
	const segments: google.maps.Polyline[] = [];

	// 为了平滑渐变，每两个点之间创建一个线段
	for (let i = 0; i < path.length - 1; i++) {
		// 计算当前段的颜色
		const ratio = i / (path.length - 2);
		const r = Math.floor(
			colorStart.r + ratio * (colorEnd.r - colorStart.r),
		);
		const g = Math.floor(
			colorStart.g + ratio * (colorEnd.g - colorStart.g),
		);
		const b = Math.floor(
			colorStart.b + ratio * (colorEnd.b - colorStart.b),
		);

		// 转换回十六进制
		const color = `#${r.toString(16).padStart(2, "0")}${g.toString(16).padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;

		// 创建线段
		const segment = new google.maps.Polyline({
			path: [path[i], path[i + 1]],
			strokeColor: color,
			strokeOpacity: 1.0,
			strokeWeight: 3,
			map: map,
			geodesic: true,
		});

		segments.push(segment);
	}

	return segments;
}

/**
 * 创建方向箭头路线
 *
 * @param polyline - Google Maps Polyline 实例
 * @param options - 配置选项
 * @returns 取消动画的函数
 */
export function createAnimatedArrowLine(
	polyline: google.maps.Polyline,
	options: {
		color?: string;
		arrowSize?: number;
		arrowSpacing?: number;
		animationSpeed?: number;
	} = {},
): () => void {
	const {
		color = "#3B82F6",
		arrowSize = 3,
		arrowSpacing = 70, // 箭头间距（像素）
		animationSpeed = 1.5, // 动画速度
	} = options;

	// 设置路线样式
	polyline.setOptions({
		strokeColor: color,
		strokeOpacity: 0.8,
		strokeWeight: 3,
		icons: [
			{
				icon: {
					path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
					fillColor: color,
					fillOpacity: 1,
					strokeColor: color,
					strokeWeight: 1,
					scale: arrowSize,
				},
				offset: "0%",
				repeat: `${arrowSpacing}px`,
			},
		],
	});

	// 动画变量
	let count = 0;
	let animationFrameId: number | null = null;

	// 动画函数
	const animate = () => {
		count = (count + animationSpeed) % arrowSpacing;

		const icons = polyline.get("icons");
		icons[0].offset = `${count}px`;
		polyline.set("icons", icons);

		// 继续下一帧动画
		animationFrameId = window.requestAnimationFrame(animate);
	};

	// 启动动画
	animationFrameId = window.requestAnimationFrame(animate);

	// 返回取消动画的函数
	return () => {
		if (animationFrameId !== null) {
			window.cancelAnimationFrame(animationFrameId);
			animationFrameId = null;
		}
	};
}
