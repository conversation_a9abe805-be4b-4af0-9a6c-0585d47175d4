// =============================================================================
// 颜色策略系统 - 统一管理颜色计算逻辑
// 支持多种策略和配置，易于扩展和维护
// =============================================================================

import { COLOR_THEMES } from "../constants/colorThemes";
import type { ColorTheme, ColorThemeType } from "../types/colorTypes";

// ===== 核心类型定义 =====

/**
 * 颜色级别枚举 - 统一的颜色级别标识
 */
export enum ColorLevel {
	UNVISITED = "unvisited",
	LEVEL1 = "level1",
	LEVEL2 = "level2",
	LEVEL3 = "level3",
	LEVEL4 = "level4",
	LEVEL5 = "level5",
	LEVEL6TO10 = "level6to10",
	LEVEL10PLUS = "level10plus",
}

/**
 * 颜色计算结果
 */
export interface ColorResult {
	level: ColorLevel;
	hex: string;
	rgba: string;
	description: string;
}

/**
 * 颜色策略接口 - 定义颜色计算的通用接口
 */
export interface ColorStrategy {
	readonly name: string;
	readonly description: string;
	readonly version: string;

	/**
	 * 根据输入数据计算颜色级别
	 * @param data 输入数据（可以是访问次数、时间等任何数据）
	 * @returns 颜色级别
	 */
	calculateLevel(data: any): ColorLevel;

	/**
	 * 获取策略配置（用于UI显示和调试）
	 */
	getConfig(): Record<string, any>;

	/**
	 * 设置策略配置
	 */
	setConfig(config: Record<string, any>): void;
}

// ===== 具体策略实现 =====

/**
 * 基于访问次数的颜色策略
 */
export class VisitCountStrategy implements ColorStrategy {
	readonly name = "visit-count";
	readonly description = "根据访问次数确定颜色级别";
	readonly version = "1.0.0";

	private config: {
		thresholds: {
			level1: number;
			level2: number;
			level3: number;
			level4: number;
			level5: number;
			level6to10: number;
			level10plus: number;
		};
	};

	constructor(config?: Partial<VisitCountStrategy["config"]>) {
		// 默认配置 - 可以根据需求调整
		this.config = {
			thresholds: {
				level1: 1,
				level2: 2,
				level3: 3,
				level4: 4,
				level5: 5,
				level6to10: 6,
				level10plus: 11,
			},
			...config,
		};
	}

	calculateLevel(visitCount: number): ColorLevel {
		if (visitCount === 0) {
			return ColorLevel.UNVISITED;
		}

		const { thresholds } = this.config;

		if (visitCount >= thresholds.level10plus) {
			return ColorLevel.LEVEL10PLUS;
		}
		if (visitCount >= thresholds.level6to10) {
			return ColorLevel.LEVEL6TO10;
		}
		if (visitCount >= thresholds.level5) {
			return ColorLevel.LEVEL5;
		}
		if (visitCount >= thresholds.level4) {
			return ColorLevel.LEVEL4;
		}
		if (visitCount >= thresholds.level3) {
			return ColorLevel.LEVEL3;
		}
		if (visitCount >= thresholds.level2) {
			return ColorLevel.LEVEL2;
		}

		return ColorLevel.LEVEL1;
	}

	getConfig(): Record<string, any> {
		return { ...this.config };
	}

	setConfig(config: Record<string, any>): void {
		this.config = { ...this.config, ...config };
	}
}

/**
 * 基于时间距离的颜色策略（示例扩展策略）
 */
export class TimeDistanceStrategy implements ColorStrategy {
	readonly name = "time-distance";
	readonly description = "根据访问时间距离确定颜色级别";
	readonly version = "1.0.0";

	private config: {
		timeThresholds: {
			recent: number; // 最近访问（天数）
			medium: number; // 中等时间
			old: number; // 较老访问
		};
	};

	constructor(config?: Partial<TimeDistanceStrategy["config"]>) {
		this.config = {
			timeThresholds: {
				recent: 30, // 30天内
				medium: 180, // 6个月内
				old: 365, // 1年内
			},
			...config,
		};
	}

	calculateLevel(lastVisitTimestamp: number): ColorLevel {
		const now = Date.now();
		const daysSinceVisit =
			(now - lastVisitTimestamp) / (1000 * 60 * 60 * 24);

		const { timeThresholds } = this.config;

		if (daysSinceVisit <= timeThresholds.recent) {
			return ColorLevel.LEVEL5; // 最近访问 - 深色
		}
		if (daysSinceVisit <= timeThresholds.medium) {
			return ColorLevel.LEVEL3; // 中等时间 - 中等色
		}
		if (daysSinceVisit <= timeThresholds.old) {
			return ColorLevel.LEVEL1; // 较老访问 - 浅色
		}

		return ColorLevel.UNVISITED; // 很久没访问 - 灰色
	}

	getConfig(): Record<string, any> {
		return { ...this.config };
	}

	setConfig(config: Record<string, any>): void {
		this.config = { ...this.config, ...config };
	}
}

/**
 * 复合策略（示例 - 结合访问次数和时间）
 */
export class CompositeStrategy implements ColorStrategy {
	readonly name = "composite";
	readonly description = "结合访问次数和时间距离的复合策略";
	readonly version = "1.0.0";

	private visitCountStrategy: VisitCountStrategy;
	private timeDistanceStrategy: TimeDistanceStrategy;

	constructor() {
		this.visitCountStrategy = new VisitCountStrategy();
		this.timeDistanceStrategy = new TimeDistanceStrategy();
	}

	calculateLevel(data: {
		visitCount: number;
		lastVisitTimestamp: number;
	}): ColorLevel {
		const countLevel = this.visitCountStrategy.calculateLevel(
			data.visitCount,
		);
		const timeLevel = this.timeDistanceStrategy.calculateLevel(
			data.lastVisitTimestamp,
		);

		// 简单的复合逻辑 - 可以根据需求调整
		const countWeight = 0.7;
		const timeWeight = 0.3;

		// 这里可以实现更复杂的复合逻辑
		// 暂时简化为取访问次数的主导作用
		return countLevel;
	}

	getConfig(): Record<string, any> {
		return {
			visitCount: this.visitCountStrategy.getConfig(),
			timeDistance: this.timeDistanceStrategy.getConfig(),
		};
	}

	setConfig(config: Record<string, any>): void {
		if (config.visitCount) {
			this.visitCountStrategy.setConfig(config.visitCount);
		}
		if (config.timeDistance) {
			this.timeDistanceStrategy.setConfig(config.timeDistance);
		}
	}
}

// ===== 策略管理器 =====

/**
 * 颜色策略管理器 - 统一的颜色计算入口
 */
export class ColorStrategyManager {
	private strategy: ColorStrategy;
	private theme: ColorTheme;

	constructor(
		strategy: ColorStrategy = new VisitCountStrategy(),
		themeId: ColorThemeType = "classic-blue-green",
	) {
		this.strategy = strategy;
		this.theme = COLOR_THEMES[themeId];
	}

	/**
	 * 设置颜色策略
	 */
	setStrategy(strategy: ColorStrategy): void {
		this.strategy = strategy;
	}

	/**
	 * 设置颜色主题
	 */
	setTheme(themeId: ColorThemeType): void {
		this.theme = COLOR_THEMES[themeId];
	}

	/**
	 * 计算颜色结果
	 */
	calculateColor(data: any): ColorResult {
		const level = this.strategy.calculateLevel(data);
		const colorConfig = this.theme.colors[level];

		return {
			level,
			hex: colorConfig.hex,
			rgba: colorConfig.rgba,
			description: colorConfig.description,
		};
	}

	/**
	 * 获取十六进制颜色（用于标记）
	 */
	getHexColor(data: any): string {
		return this.calculateColor(data).hex;
	}

	/**
	 * 获取RGBA颜色（用于地图填充）
	 */
	getRgbaColor(data: any): string {
		return this.calculateColor(data).rgba;
	}

	/**
	 * 获取当前策略信息
	 */
	getStrategyInfo(): {
		name: string;
		description: string;
		version: string;
		config: Record<string, any>;
	} {
		return {
			name: this.strategy.name,
			description: this.strategy.description,
			version: this.strategy.version,
			config: this.strategy.getConfig(),
		};
	}

	/**
	 * 获取当前主题信息
	 */
	getThemeInfo(): ColorTheme {
		return { ...this.theme };
	}

	/**
	 * 批量计算颜色（用于优化性能）
	 */
	batchCalculateColors(dataList: any[]): ColorResult[] {
		return dataList.map((data) => this.calculateColor(data));
	}
}

// ===== 预设策略实例 =====

/**
 * 预设的策略实例
 */
export const PRESET_STRATEGIES = {
	/**
	 * 默认访问次数策略
	 */
	visitCount: new VisitCountStrategy(),

	/**
	 * 宽松的访问次数策略（阈值更高）
	 */
	visitCountLoose: new VisitCountStrategy({
		thresholds: {
			level1: 1,
			level2: 3,
			level3: 5,
			level4: 8,
			level5: 12,
			level6to10: 15,
			level10plus: 20,
		},
	}),

	/**
	 * 严格的访问次数策略（阈值更低）
	 */
	visitCountStrict: new VisitCountStrategy({
		thresholds: {
			level1: 1,
			level2: 2,
			level3: 3,
			level4: 4,
			level5: 5,
			level6to10: 6,
			level10plus: 8,
		},
	}),

	/**
	 * 时间距离策略
	 */
	timeDistance: new TimeDistanceStrategy(),

	/**
	 * 复合策略
	 */
	composite: new CompositeStrategy(),
} as const;

// ===== 工厂函数 =====

/**
 * 创建颜色策略管理器的工厂函数
 */
export function createColorStrategyManager(
	strategyName: keyof typeof PRESET_STRATEGIES = "visitCount",
	themeId: ColorThemeType = "classic-blue-green",
): ColorStrategyManager {
	const strategy = PRESET_STRATEGIES[strategyName];
	return new ColorStrategyManager(strategy, themeId);
}

/**
 * 获取所有可用的策略
 */
export function getAvailableStrategies(): Array<{
	key: keyof typeof PRESET_STRATEGIES;
	name: string;
	description: string;
	version: string;
}> {
	return Object.entries(PRESET_STRATEGIES).map(([key, strategy]) => ({
		key: key as keyof typeof PRESET_STRATEGIES,
		name: strategy.name,
		description: strategy.description,
		version: strategy.version,
	}));
}
