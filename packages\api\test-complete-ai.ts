import { RichTextAnalyzer } from "./src/services/richtext-analyzer";
import { TravelAIAnalyzer } from "./src/services/travel-ai-analyzer";
import { TravelDiaryGenerator } from "./src/services/travel-diary-generator";

async function testCompleteAI() {
	console.log("🧪 测试完整的AI功能...\n");

	// 测试富文本内容
	const richTextContent = {
		type: "doc",
		content: [
			{
				type: "paragraph",
				content: [
					{
						type: "text",
						text: "昨天去了北京天安门广场，看了升旗仪式，然后在王府井大街吃了烤鸭。下午去了故宫博物院参观，晚上在三里屯逛街购物。",
					},
				],
			},
		],
	};

	try {
		console.log("📝 测试富文本分析...");

		// 1. 测试富文本分析
		const richTextResult = RichTextAnalyzer.analyzeContent(richTextContent);

		console.log("\n✅ 富文本分析成功!");
		console.log("📝 提取的文本:", richTextResult.textContent);
		console.log("🖼️ 图片数量:", richTextResult.imageUrls.length);
		console.log("📦 内容块数量:", richTextResult.contentBlocks.length);

		// 2. 测试AI分析
		console.log("\n🤖 测试AI分析...");

		const aiAnalyzer = new TravelAIAnalyzer();
		const analysisResult = await aiAnalyzer.analyzeText(
			richTextResult.textContent,
		);

		console.log("\n✅ AI分析成功!");
		console.log("📍 发现地点:", analysisResult.locations.length);
		console.log("⏰ 发现时间:", analysisResult.timeInfo.length);
		console.log("🎯 发现活动:", analysisResult.activities.length);

		// 3. 测试旅行日记生成
		console.log("\n📖 测试旅行日记生成...");

		const diaryResult =
			TravelDiaryGenerator.generateFromTravelInfo(analysisResult);

		console.log("\n✅ 旅行日记生成成功!");
		console.log("📅 生成时间线数量:", diaryResult.timelines.length);
		console.log(
			"📍 总点位数量:",
			diaryResult.timelines.reduce((sum, t) => sum + t.points.length, 0),
		);

		console.log("\n📋 详细结果:");
		console.log("=".repeat(50));

		// 显示富文本分析结果
		console.log("\n📝 富文本分析结果:");
		console.log("提取的文本:", richTextResult.textContent);
		console.log(
			"内容块:",
			JSON.stringify(richTextResult.contentBlocks, null, 2),
		);

		// 显示AI分析结果
		console.log("\n🔍 AI分析结果:");
		console.log(
			"地点信息:",
			JSON.stringify(analysisResult.locations, null, 2),
		);
		console.log(
			"时间信息:",
			JSON.stringify(analysisResult.timeInfo, null, 2),
		);
		console.log(
			"活动信息:",
			JSON.stringify(analysisResult.activities, null, 2),
		);
		console.log("总结:", analysisResult.summary);

		// 显示日记生成结果
		console.log("\n📖 旅行日记结果:");
		diaryResult.timelines.forEach((timeline, timelineIndex) => {
			console.log(`\n时间线 ${timelineIndex + 1}: ${timeline.title}`);
			console.log(`   日期: ${timeline.date}`);
			console.log(`   点位数量: ${timeline.points.length}`);

			timeline.points.forEach((point, pointIndex) => {
				console.log(`\n   点位 ${pointIndex + 1}: ${point.location}`);
				console.log(`     类型: ${point.iconType}`);
				console.log(`     描述: ${point.description}`);
				if (point.coordinates) {
					console.log(
						`     坐标: ${point.coordinates.longitude}, ${point.coordinates.latitude}`,
					);
				}
				if (point.address) {
					console.log(`     地址: ${point.address}`);
				}
			});
		});

		console.log("\n🎉 所有AI功能测试完成!");
	} catch (error) {
		console.error("❌ AI功能测试失败:", error);

		if (error instanceof Error) {
			console.error("错误详情:", error.message);
			console.error("错误堆栈:", error.stack);
		}
	}
}

// 运行测试
testCompleteAI().catch(console.error);
