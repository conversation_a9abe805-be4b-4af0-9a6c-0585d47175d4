# 地理编码服务配置指南

## 🌍 统一使用 Google Maps Geocoding API

为了简化架构并获得最佳的全球地址识别效果，我们已将地理编码服务统一为 **Google Maps Geocoding API**。

## 🔑 API 密钥配置

### 1. 获取 Google Maps API 密钥

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 **Geocoding API** 和 **Maps JavaScript API**
4. 创建 API 密钥
5. 配置 API 密钥限制（推荐限制为你的域名和 IP）

### 2. 环境变量配置

在项目根目录的 `.env.local` 文件中添加：

```bash
# Google Maps API 密钥
GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# AI 服务密钥（用于旅行内容分析）
VOLCENGINE_API_KEY=your_volcengine_api_key_here
```

## 🚀 使用方式

### 基础用法

```typescript
import { GeocodingService } from "./services/geocoding-service";

// 创建服务实例
const geocodingService = new GeocodingService(process.env.GOOGLE_MAPS_API_KEY!);

// 地址转坐标
const result = await geocodingService.geocode("北京天安门广场");

// 坐标转地址
const reverseResult = await geocodingService.reverseGeocode(116.3974, 39.9093);
```

### 智能地理编码（推荐）

```typescript
// 使用智能地理编码，自动优化中文地址查询
const result = await geocodingService.smartGeocode("北京天安门广场", {
  language: "zh-CN",
  region: "CN"
});
```

### 批量地理编码

```typescript
const addresses = ["北京天安门广场", "上海外滩", "广州塔"];
const results = await geocodingService.batchGeocode(addresses, {
  language: "zh-CN",
  region: "CN"
});
```

## 📊 API 配额和定价

### 免费额度
- Google Maps 每月提供 $200 的免费额度
- Geocoding API: $5/1000次请求
- 免费额度约可支持 40,000 次地理编码请求

### 成本优化建议
1. **缓存结果**：相同地址的查询结果可以缓存
2. **批量处理**：使用 `batchGeocode` 方法减少请求频率
3. **智能查询**：使用 `smartGeocode` 提高首次查询成功率
4. **设置配额限制**：在 Google Cloud Console 中设置每日配额限制

## 🔧 测试

运行测试脚本验证配置：

```bash
npm run test:format-richtext
```

测试脚本会验证：
- ✅ 环境变量加载
- ✅ Google Maps API 连接
- ✅ 中文地址地理编码
- ✅ 反向地理编码
- ✅ AI 分析集成

## 🌟 优势

### 相比多提供商方案的优势：

1. **简化架构**：单一 API，减少复杂性
2. **全球覆盖**：Google Maps 数据覆盖全球
3. **中文支持**：对中文地址识别准确度高
4. **稳定可靠**：Google 的基础设施保障
5. **功能丰富**：支持多种查询选项和过滤器

### 特别针对中国地址的优化：

- 自动检测中文地址并添加地区偏好
- 支持简化地址重试机制
- 优化的置信度评分
- 完整的地址组件解析

## 🔍 故障排除

### 常见问题

1. **API 密钥无效**
   - 检查密钥是否正确复制
   - 确认已启用 Geocoding API
   - 检查 API 密钥限制设置

2. **配额超限**
   - 检查 Google Cloud Console 中的配额使用情况
   - 考虑升级到付费计划
   - 实施缓存策略减少请求

3. **中文地址识别不准确**
   - 使用 `smartGeocode` 方法
   - 添加 `language: "zh-CN"` 和 `region: "CN"` 参数
   - 尝试简化地址描述

### 监控和日志

服务内置了详细的日志记录：
- 请求参数和响应
- 错误信息和重试逻辑
- 性能指标和置信度评分

查看日志以诊断问题：
```bash
# 查看应用日志
npm run dev
```

## 📚 相关文档

- [Google Maps Geocoding API 文档](https://developers.google.com/maps/documentation/geocoding)
- [Google Cloud Console](https://console.cloud.google.com/)
- [API 密钥最佳实践](https://cloud.google.com/docs/authentication/api-keys) 