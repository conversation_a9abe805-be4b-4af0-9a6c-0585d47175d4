import { useTranslations } from "next-intl";
import type { AtmosphereTheme } from "../types";

export function useTranslatedAtmosphereThemes() {
	const t = useTranslations("travelStat.atmosphereThemes");

	const getAtmosphereThemeName = (theme: AtmosphereTheme): string => {
		switch (theme) {
			case "day":
				return t("names.day");
			case "night":
				return t("names.night");
			case "sunset":
				return t("names.sunset");
			case "dawn":
				return t("names.dawn");
			case "aurora":
				return t("names.aurora");
			case "deep-space":
				return t("names.deep-space");
			case "ocean":
				return t("names.ocean");
			case "minimal":
				return t("names.minimal");
			default:
				return theme;
		}
	};

	const getAtmosphereThemeDescription = (theme: AtmosphereTheme): string => {
		switch (theme) {
			case "day":
				return t("descriptions.day");
			case "night":
				return t("descriptions.night");
			case "sunset":
				return t("descriptions.sunset");
			case "dawn":
				return t("descriptions.dawn");
			case "aurora":
				return t("descriptions.aurora");
			case "deep-space":
				return t("descriptions.deep-space");
			case "ocean":
				return t("descriptions.ocean");
			case "minimal":
				return t("descriptions.minimal");
			default:
				return "";
		}
	};

	return {
		getAtmosphereThemeName,
		getAtmosphereThemeDescription,
	};
}
