/**
 * 演示模式API客户端
 * 确保演示模式中的所有API请求都能正确传递导出参数
 */

// 导出令牌 - 必须与中间件中的令牌一致
export const EXPORT_AUTH_TOKEN = "j8Kp4Xz2Q9vYfTs7B3e5W6gA1hR0dNmVlCuMoIpLxZyE";

/**
 * 检查当前URL是否处于视频导出模式
 */
export function isVideoExportMode(): boolean {
	if (typeof window === "undefined") return false;

	const urlParams = new URLSearchParams(window.location.search);
	return urlParams.get("export") === "video";
}

/**
 * 获取视频导出令牌（如果存在）
 */
export function getExportToken(): string | null {
	if (typeof window === "undefined") return null;

	const urlParams = new URLSearchParams(window.location.search);
	return urlParams.get("exportToken");
}

/**
 * 向URL添加视频导出参数（如果处于导出模式）
 */
export function addExportParamsToUrl(url: string): string {
	if (!isVideoExportMode()) return url;

	const exportToken = getExportToken();
	if (!exportToken) return url;

	// 检查URL是否已有查询参数
	const hasParams = url.includes("?");
	const separator = hasParams ? "&" : "?";

	return `${url}${separator}export=video&exportToken=${exportToken}`;
}

/**
 * 包装fetch API，自动添加视频导出参数
 * 与原生fetch API保持相同的签名和行为
 */
export async function apiFetch(
	url: string,
	options?: RequestInit,
): Promise<Response> {
	const urlWithExport = addExportParamsToUrl(url);
	return fetch(urlWithExport, options);
}

/**
 * 用于演示模式的API请求
 * 封装了常见的API请求方法，自动添加导出参数
 */
export const apiClient = {
	/**
	 * 获取日记详情
	 */
	getDiary: async (diaryId: string) => {
		const response = await apiFetch(`/api/diaries/diary/${diaryId}`);
		if (!response.ok) {
			throw new Error(`获取日记失败: ${response.status}`);
		}
		const data = await response.json();
		return data.diary;
	},

	/**
	 * 获取日记中的所有点位
	 */
	getDiaryPoints: async (diaryId: string) => {
		const response = await apiFetch(`/api/diaries/diary/${diaryId}/points`);
		if (!response.ok) {
			throw new Error(`获取点位失败: ${response.status}`);
		}
		const data = await response.json();
		return data.points;
	},

	/**
	 * 获取指定时间线
	 */
	getTimeline: async (timelineId: string) => {
		const response = await apiFetch(`/api/diaries/timeline/${timelineId}`);
		if (!response.ok) {
			throw new Error(`获取时间线失败: ${response.status}`);
		}
		const data = await response.json();
		return data.timeline;
	},
};
