import { cn } from "@ui/lib";
import Image from "next/image";

export function Logo({
	withLabel = true,
	className,
}: {
	className?: string;
	withLabel?: boolean;
}) {
	return (
		<span
			className={cn(
				"flex items-center font-semibold text-foreground leading-none",
				className,
			)}
		>
			<Image
				src="/images/light-logo.png"
				alt="MapMoment"
				width={48}
				height={48}
				className="text-primary"
			/>
			{withLabel && <span className="ml-3 text-lg">MapMoment</span>}
		</span>
	);
}
