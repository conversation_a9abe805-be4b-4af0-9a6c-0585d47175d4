export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function generateMetadata() {
	// 这里可以自定义AI图片生成器的标题
	return {
		title: "登录 - AI图片生成器",
		description: "登录您的AI图片生成器账户",
	};
}

export default function LoginPage() {
	return (
		<div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-purple-50 to-blue-50">
			<div className="max-w-md w-full space-y-8 p-8 bg-white rounded-xl shadow-lg">
				<div className="text-center">
					<h2 className="text-3xl font-bold text-gray-900 mb-2">
						AI图片生成器
					</h2>
					<p className="text-gray-600">登录开始创造精美的AI图片</p>
				</div>

				{/* 暂时使用简单的表单，后续可以复用MapMoment的组件 */}
				<div className="space-y-4">
					<div>
						<label
							htmlFor="email"
							className="block text-sm font-medium text-gray-700"
						>
							邮箱地址
						</label>
						<input
							id="email"
							type="email"
							className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
							placeholder="请输入您的邮箱"
						/>
					</div>

					<div>
						<label
							htmlFor="password"
							className="block text-sm font-medium text-gray-700"
						>
							密码
						</label>
						<input
							id="password"
							type="password"
							className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
							placeholder="请输入您的密码"
						/>
					</div>

					<button
						type="button"
						className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
					>
						登录
					</button>
				</div>

				<div className="text-center text-sm text-gray-500">
					还没有账户？{" "}
					<a
						href="/signup"
						className="text-purple-600 hover:text-purple-500"
					>
						立即注册
					</a>
				</div>
			</div>
		</div>
	);
}
