"use client";

import {
	type MapPoint,
	initializeGoogleMap,
	useMapAnimation,
} from "@modules/map-presentation";
import { AnimatePresence, motion } from "framer-motion";
import { Maximize2, Minimize2 } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import type { TravelPoint } from "../../../../components/diary/travel-point-form/types";

interface MiniMapProps {
	footprints: TravelPoint[];
	currentFootprintIndex: number;
}

export function MiniMap({ footprints, currentFootprintIndex }: MiniMapProps) {
	const mapRef = useRef<HTMLDivElement>(null);
	const googleMapRef = useRef<google.maps.Map | null>(null);
	const markersRef = useRef<google.maps.Marker[]>([]);
	const polylineRef = useRef<google.maps.Polyline | null>(null);
	const [isExpanded, setIsExpanded] = useState(false);

	// 用于动画控制的 hook
	const {
		addMarker,
		clearMarkers,
		focusOnRouteBetweenPoints,
		setActiveMarker,
		drawRouteBetweenPoints,
		adjustMapViewToPoints,
	} = useMapAnimation(googleMapRef);

	// 初始化地图
	useEffect(() => {
		// 只在客户端渲染
		if (
			typeof window === "undefined" ||
			!mapRef.current ||
			footprints.length === 0
		) {
			return;
		}

		// 如果地图已存在，不重新初始化
		if (googleMapRef.current) {
			return;
		}

		// 取第一个足迹的坐标作为初始位置
		const initialCoords = footprints[0].coordinates;

		// 将 TravelPoint 转换为 MapPoint 格式
		const mapPoints = convertToMapPoints(footprints);

		// 初始化 Google 地图
		const initMap = async () => {
			try {
				// 确保 mapRef 非空
				if (!mapRef.current) return;
				const map = await initializeGoogleMap(
					mapRef as React.RefObject<HTMLDivElement>,
					{ lat: initialCoords.lat, lng: initialCoords.lng },
					10,
					{
						mapTypeControl: false,
						streetViewControl: false,
						fullscreenControl: false,
						zoomControl: isExpanded, // 在展开模式下显示缩放控件
						mapId: process.env.NEXT_PUBLIC_GOOGLE_MAP_ID, // 使用环境变量中配置的 Map ID
						styles: [
							{
								featureType: "all",
								elementType: "labels.text.fill",
								stylers: [{ color: "#ffffff" }],
							},
							{
								featureType: "all",
								elementType: "labels.text.stroke",
								stylers: [
									{ visibility: "on" },
									{ color: "#3e606f" },
									{ weight: 2 },
									{ gamma: 0.84 },
								],
							},
							{
								featureType: "administrative",
								elementType: "geometry",
								stylers: [
									{ weight: 0.6 },
									{ color: "#1a3541" },
								],
							},
							{
								featureType: "landscape",
								elementType: "geometry",
								stylers: [{ color: "#2c5a71" }],
							},
							{
								featureType: "poi",
								elementType: "geometry",
								stylers: [{ color: "#406d80" }],
							},
							{
								featureType: "poi.park",
								elementType: "geometry",
								stylers: [{ color: "#2c5a71" }],
							},
							{
								featureType: "road",
								elementType: "geometry",
								stylers: [
									{ color: "#29768a" },
									{ lightness: -37 },
								],
							},
							{
								featureType: "transit",
								elementType: "geometry",
								stylers: [{ color: "#406d80" }],
							},
							{
								featureType: "water",
								elementType: "geometry",
								stylers: [{ color: "#193341" }],
							},
						],
					},
				);

				googleMapRef.current = map;

				// 为每个点创建标记
				mapPoints.forEach((point) => {
					addMarker(point);
				});

				// 创建路线 - 使用 drawRouteBetweenPoints 连接相邻点
				for (let i = 0; i < mapPoints.length - 1; i++) {
					drawRouteBetweenPoints(mapPoints[i], mapPoints[i + 1]);
				}

				// 调整地图视图以显示所有点
				adjustMapViewToPoints(mapPoints);

				// 高亮当前点位
				if (mapPoints[currentFootprintIndex]) {
					setActiveMarker(
						mapPoints[currentFootprintIndex],
						currentFootprintIndex,
					);
				}
			} catch (error) {
				console.error("初始化地图失败", error);
			}
		};

		initMap();

		// 响应窗口大小变化
		const handleResize = () => {
			if (googleMapRef.current) {
				google.maps.event.trigger(googleMapRef.current, "resize");
			}
		};

		window.addEventListener("resize", handleResize);

		return () => {
			window.removeEventListener("resize", handleResize);
			clearMarkers();
			if (polylineRef.current) {
				polylineRef.current.setMap(null);
			}
		};
	}, [
		addMarker,
		clearMarkers,
		drawRouteBetweenPoints,
		setActiveMarker,
		adjustMapViewToPoints,
		footprints,
		isExpanded,
		currentFootprintIndex,
	]);

	// 更新当前活动标记
	useEffect(() => {
		if (!googleMapRef.current || footprints.length === 0) {
			return;
		}

		const currentPoint = footprints[currentFootprintIndex];
		if (!currentPoint) {
			return;
		}

		// 将当前点转换为 MapPoint 格式
		const mapPoint = convertToMapPoint(currentPoint);

		// 高亮当前标记
		setActiveMarker(mapPoint, currentFootprintIndex);

		// 平滑移动地图到当前标记位置
		if (googleMapRef.current) {
			googleMapRef.current.panTo({
				lat: currentPoint.coordinates.lat,
				lng: currentPoint.coordinates.lng,
			});
			googleMapRef.current.setZoom(isExpanded ? 13 : 10);
		}
	}, [currentFootprintIndex, footprints, setActiveMarker, isExpanded]);

	// 切换地图展开状态
	const toggleMapExpand = () => {
		setIsExpanded(!isExpanded);

		// 在状态变化后，确保地图重新调整大小
		setTimeout(() => {
			if (googleMapRef.current) {
				google.maps.event.trigger(googleMapRef.current, "resize");

				// 如果有当前足迹，确保它在视图中心
				if (footprints[currentFootprintIndex]) {
					const { lat, lng } =
						footprints[currentFootprintIndex].coordinates;
					googleMapRef.current.setCenter({ lat, lng });
					googleMapRef.current.setZoom(isExpanded ? 10 : 13);
				}
			}
		}, 300);
	};

	// 将 TravelPoint 转换为 MapPoint
	const convertToMapPoint = (point: TravelPoint): MapPoint => {
		return {
			coordinates: point.coordinates,
			location: point.location,
			description: point.description,
			date: point.date.toString(),
			iconType: point.iconType,
			images: point.images,
		};
	};

	// 将 TravelPoint 数组转换为 MapPoint 数组
	const convertToMapPoints = (points: TravelPoint[]): MapPoint[] => {
		return points.map(convertToMapPoint);
	};

	return (
		<div
			className={`fixed z-30 transition-all duration-300 ease-in-out ${
				isExpanded
					? "inset-0 bg-black/90"
					: "bottom-16 right-4 w-48 h-48 rounded-lg shadow-xl"
			}`}
		>
			<div
				ref={mapRef}
				className={`w-full h-full ${isExpanded ? "" : "rounded-lg"}`}
			/>{" "}
			{/* 切换按钮 */}
			<button
				type="button"
				onClick={toggleMapExpand}
				className="absolute top-2 right-2 z-40 rounded-full p-1 shadow" // Removed bg-white
				aria-label={isExpanded ? "收起地图" : "展开地图"}
			>
				{isExpanded ? (
					<Minimize2 size={20} className="text-white" /> // Added text-black
				) : (
					<Maximize2 size={20} className="text-white" /> // Added text-black
				)}
			</button>
			{/* 当前地点信息 */}
			<AnimatePresence>
				{isExpanded && footprints[currentFootprintIndex] && (
					<motion.div
						initial={{ opacity: 0, y: 20 }}
						animate={{ opacity: 1, y: 0 }}
						exit={{ opacity: 0, y: 20 }}
						className="absolute bottom-8 left-1/2 transform -translate-x-1/2 bg-white/90 text-black p-4 rounded-lg shadow-lg max-w-md"
					>
						<h3 className="text-lg font-semibold">
							{footprints[currentFootprintIndex].location}
						</h3>
						<p className="text-sm">
							{footprints[currentFootprintIndex].description}
						</p>
					</motion.div>
				)}
			</AnimatePresence>
		</div>
	);
}
