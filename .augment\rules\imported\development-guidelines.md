# MapMoment 项目开发指导原则

## 1. 项目概述

### 1.1 项目基本信息
- **项目名称**：MapMoment（原名 travel-memo）
- **项目类型**：基于 Monorepo 架构的多服务旅行应用平台
- **主要功能**：旅行日记管理、AI图片生成、视频导出
- **目标用户**：旅行爱好者、内容创作者

### 1.2 服务架构
```
MapMoment 平台
├── 旅行日记主应用 (apps/web) - 端口 3000
├── AI图片生成器 (apps/ai-image-generator) - 端口 3001  
└── 视频导出服务 (packages/video-exporter) - 后台服务
```

### 1.3 核心特性
- 富文本旅行日记编辑（Tiptap集成）
- AI驱动的文本分析和地理编码
- 多地图服务集成（Mapbox、Google Maps）
- 多AI提供商支持（OpenAI、Gemini、火山引擎）
- 多云存储支持（AWS S3、Cloudflare R2、腾讯云COS）

## 2. 技术栈分析

### 2.1 前端技术栈
- **框架**：Next.js 15 (App Router)
- **UI库**：React 19
- **类型系统**：TypeScript 5.8.3
- **样式**：Tailwind CSS 4.1.3
- **UI组件**：Radix UI + shadcn/ui
- **状态管理**：Zustand、Jotai
- **表单处理**：React Hook Form + Zod
- **地图**：Mapbox GL JS、React Google Maps
- **富文本编辑**：Tiptap (ProseMirror)

### 2.2 后端技术栈
- **API框架**：Hono.js
- **数据库**：PostgreSQL (多数据库架构)
- **ORM**：Prisma
- **认证**：Better Auth
- **支付**：Stripe / LemonSqueezy
- **邮件**：支持多提供商（Resend、Plunk、Postmark等）

### 2.3 AI和存储
- **AI服务**：OpenAI、Google Gemini、火山引擎
- **存储服务**：AWS S3、Cloudflare R2、腾讯云COS
- **图片处理**：支持多种格式和尺寸

### 2.4 开发工具
- **包管理**：pnpm workspace
- **构建工具**：Turbo
- **代码质量**：Biome (替代 ESLint + Prettier)
- **测试**：Playwright
- **部署**：Vercel

## 3. 代码结构规范

### 3.1 Monorepo 结构
```
mapmoment/
├── apps/
│   ├── web/                    # MapMoment 主应用
│   └── ai-image-generator/     # AI图片生成器
├── packages/
│   ├── api/                    # API路由和业务逻辑
│   ├── auth/                   # 身份认证
│   ├── database/               # 数据库层
│   ├── ai/                     # AI功能
│   ├── storage/                # 文件存储
│   ├── payments/               # 支付处理
│   ├── mail/                   # 邮件服务
│   ├── video-exporter/         # 视频导出服务
│   ├── utils/                  # 工具函数
│   ├── i18n/                   # 国际化
│   └── shared-ui/              # 共享UI组件
└── tooling/                    # 构建工具配置
    ├── scripts/
    ├── tailwind/
    └── typescript/
```

### 3.2 命名约定
- **文件命名**：kebab-case（如 `travel-diary.tsx`）
- **组件命名**：PascalCase（如 `TravelDiary`）
- **函数命名**：camelCase（如 `createTravelDiary`）
- **常量命名**：SCREAMING_SNAKE_CASE（如 `API_BASE_URL`）
- **包命名**：@repo/package-name（如 `@repo/database`）

### 3.3 路径别名配置
```typescript
// 主应用路径别名
"@modules/*": ["./modules/*"]
"@packages/ai": ["../../packages/ai"]
"@packages/api": ["../../packages/api"]
"@packages/auth": ["../../packages/auth"]
// ... 其他包路径
```

## 4. 编码风格指南

### 4.1 Biome 配置规则
- **格式化**：启用，使用 EditorConfig
- **导入组织**：自动排序和清理
- **禁用规则**：
  - `noExplicitAny`: off（允许使用 any）
  - `noArrayIndexKey`: off
  - `useExhaustiveDependencies`: off

### 4.2 TypeScript 规范
- **严格模式**：关闭（`strict: false`）
- **类型定义**：优先使用 interface 而非 type
- **返回类型**：复杂函数需明确定义返回类型
- **空值检查**：相对宽松的配置

### 4.3 注释和文档
- **支持中文注释**：项目支持中文文档和注释
- **JSDoc**：为公共API和复杂函数添加JSDoc
- **README**：每个包都应有详细的README文档

## 5. 开发工作流程

### 5.1 环境设置
```bash
# 1. 安装依赖
pnpm install

# 2. 环境变量配置
cp .env.example .env.local

# 3. 数据库设置
pnpm db:generate
pnpm db:migrate --name "init"
```

### 5.2 开发服务启动
```bash
# 启动单个服务
pnpm dev:mapmoment     # 主应用
pnpm dev:ai-images     # AI图片生成器
pnpm dev:video-exporter # 视频导出服务

# 启动所有服务
pnpm dev:all
```

### 5.3 代码质量检查
```bash
# 类型检查
pnpm type-check

# 代码格式化
pnpm format

# 代码检查
pnpm lint
pnpm lint:fix

# CI检查
pnpm ci-check
```

### 5.4 添加新功能流程
1. **分析需求**：确定功能属于哪个服务/包
2. **数据库设计**：如需要，先设计数据模型
3. **API设计**：在 packages/api 中添加路由
4. **前端实现**：在对应的 app 中实现UI
5. **测试验证**：编写和运行测试
6. **文档更新**：更新相关文档

## 6. 依赖管理

### 6.1 包管理器使用
- **主包管理器**：pnpm 9.3.0
- **工作区配置**：pnpm-workspace.yaml
- **依赖安装**：始终使用 pnpm 而非 npm/yarn

### 6.2 依赖添加规则
```bash
# 添加到特定包
pnpm --filter @repo/web add package-name
pnpm --filter database add package-name

# 添加到根目录（开发依赖）
pnpm add -D package-name

# 工作区依赖
"@repo/database": "workspace:*"
```

### 6.3 版本管理
- **Node.js**：>= 20
- **TypeScript**：5.8.3
- **Next.js**：15.1.7
- **React**：19.0.0

## 7. 数据库操作指南

### 7.1 多数据库架构
```bash
# 主数据库操作
DATABASE_URL="postgresql://localhost:5432/travel_memo"
pnpm db:generate
pnpm db:migrate

# AI图片数据库操作  
AI_IMAGES_DATABASE_URL="postgresql://localhost:5432/ai_images"
pnpm db:migrate:ai
pnpm db:studio:ai
```

### 7.2 Schema 管理
- **主数据库**：`packages/database/prisma/schema.prisma`
- **AI数据库**：`packages/database/prisma/schema-ai-images.prisma`
- **类型生成**：自动生成 Zod schemas

### 7.3 迁移最佳实践
```bash
# 开发环境
pnpm db:push  # 快速原型

# 生产环境
pnpm db:migrate --name "descriptive_name"
pnpm db:migrate:deploy
```

## 8. 常见任务模式

### 8.1 AI服务集成
```typescript
// 统一AI客户端使用
import { createAIClient, AIProviderType } from '@packages/ai';

const aiClient = createAIClient({
  provider: AIProviderType.OPENAI,
  model: "gpt-4o-mini",
  apiKey: process.env.OPENAI_API_KEY
});
```

### 8.2 存储服务使用
```typescript
// 统一存储接口
import { getSignedUploadUrl, uploadFile } from '@packages/storage';

const uploadUrl = await getSignedUploadUrl(path, { bucket: 'images' });
```

### 8.3 地理编码服务
```typescript
// 统一地理编码服务
import { createGeocodingService } from '@packages/api/services/geocoding';

const geocoder = createGeocodingService();
const result = await geocoder.geocode('北京市');
```

### 8.4 富文本处理
```typescript
// 富文本分析和转换
import { RichTextAnalyzer } from '@packages/api/services/richtext-analyzer';

const analysis = RichTextAnalyzer.analyze(tiptapContent);
```

## 9. 测试和部署

### 9.1 测试策略
```bash
# 端到端测试
pnpm --filter web e2e

# 类型检查
pnpm type-check

# 构建测试
pnpm build:all
```

### 9.2 部署流程
```bash
# 构建
pnpm build:all

# 生产启动
pnpm start:all

# 单独部署
pnpm build:mapmoment && pnpm start:mapmoment
```

### 9.3 环境变量管理
- **开发环境**：`.env.local`
- **生产环境**：通过部署平台配置
- **必需变量**：数据库连接、API密钥、存储配置

## 10. 开发注意事项

### 10.1 性能优化
- 使用 Turbo 缓存构建结果
- 图片懒加载和优化
- API响应缓存
- 数据库查询优化

### 10.2 安全考虑
- API密钥安全存储
- 用户输入验证（Zod）
- 文件上传安全检查
- 数据库访问权限控制

### 10.3 国际化支持
- 使用 next-intl 进行国际化
- 支持中英文界面
- 地理编码支持多语言

### 10.4 错误处理
- 统一错误处理机制
- 详细的错误日志
- 用户友好的错误提示
- API错误状态码规范

## 11. 特殊功能模块指南

### 11.1 富文本编辑器集成
- **编辑器**：Tiptap (基于 ProseMirror)
- **功能**：支持粗体、斜体、标题、列表、图片、链接
- **特色**：自定义旅行点位节点
- **模式切换**：富文本和传统编辑模式无缝切换

### 11.2 地图服务集成
- **主要服务**：Mapbox GL JS、Google Maps API
- **功能**：地点搜索、地理编码、路线规划
- **优化**：中国地区优化配置
- **缓存**：地理编码结果缓存

### 11.3 AI服务架构
- **多提供商**：OpenAI、Google Gemini、火山引擎
- **统一接口**：createAIClient 统一创建客户端
- **功能**：文本生成、图片分析、图片生成
- **配置**：支持模型、温度、最大令牌等参数

### 11.4 存储服务架构
- **多云支持**：AWS S3、Cloudflare R2、腾讯云COS
- **统一接口**：getSignedUploadUrl、uploadFile
- **功能**：文件上传、批量上传、预签名URL
- **安全**：内容类型验证、文件大小限制

## 12. 数据库设计原则

### 12.1 多数据库策略
- **主数据库**：用户、旅行日记、认证等核心数据
- **AI数据库**：AI图片生成、模板、收藏等独立数据
- **同步机制**：用户数据在多数据库间同步

### 12.2 数据模型设计
- **用户模型**：支持多种认证方式、组织成员关系
- **旅行日记**：JSON存储时间线和点位数据
- **富文本草稿**：独立字段存储编辑器状态
- **逻辑删除**：使用status字段而非物理删除

### 12.3 性能优化
- **索引策略**：为查询频繁的字段添加索引
- **分页查询**：大数据集使用游标分页
- **缓存策略**：Redis缓存热点数据
- **连接池**：合理配置数据库连接池

## 13. API设计规范

### 13.1 路由组织
```typescript
// API路由结构
app.route("/auth", authRouter)
app.route("/ai", aiRouter)
app.route("/uploads", uploadsRouter)
app.route("/diaries", diariesRouter)
app.route("/storage", storageRouter)
```

### 13.2 请求/响应格式
- **请求验证**：使用 Zod 进行参数验证
- **错误处理**：统一错误响应格式
- **状态码**：遵循 HTTP 状态码规范
- **分页**：统一分页参数和响应格式

### 13.3 中间件使用
- **CORS**：跨域请求处理
- **日志**：请求日志记录
- **认证**：用户身份验证
- **限流**：API调用频率限制

## 14. 前端开发规范

### 14.1 组件设计原则
- **单一职责**：每个组件只负责一个功能
- **可复用性**：通过props实现组件复用
- **类型安全**：使用TypeScript定义组件props
- **样式隔离**：使用Tailwind CSS类名

### 14.2 状态管理
- **本地状态**：useState、useReducer
- **全局状态**：Zustand（简单状态）、Jotai（复杂状态）
- **服务器状态**：React Query / SWR
- **表单状态**：React Hook Form

### 14.3 性能优化
- **代码分割**：动态导入大型组件
- **图片优化**：Next.js Image组件
- **缓存策略**：适当使用useMemo、useCallback
- **懒加载**：非关键组件延迟加载

## 15. 部署和运维

### 15.1 构建优化
- **Turbo缓存**：利用构建缓存加速
- **并发构建**：多服务并行构建
- **环境变量**：通过dotenv-cli管理
- **类型检查**：构建前进行类型检查

### 15.2 监控和日志
- **错误监控**：集成错误追踪服务
- **性能监控**：关键指标监控
- **日志管理**：结构化日志输出
- **健康检查**：服务健康状态检查

### 15.3 安全措施
- **环境隔离**：开发、测试、生产环境隔离
- **密钥管理**：安全存储API密钥
- **访问控制**：基于角色的权限控制
- **数据备份**：定期数据库备份

---

**注意**：这份指导原则是基于当前项目状态的分析结果，在实际开发过程中应根据项目演进情况适时更新和调整。
