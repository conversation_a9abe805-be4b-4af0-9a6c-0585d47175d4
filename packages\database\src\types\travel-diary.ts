import type { Prisma, TravelDiary as PrismaTravelDiary } from "@prisma/client";
import type { JSONContent } from "@tiptap/core";

// Base Prisma type for TravelDiary
// type PrismaTravelDiary = PrismaTravelDiary; // Already imported

// 使用Prisma的JsonValue类型
type PrismaJsonValue = Prisma.JsonValue;
type TravelDiaryUpdateInput = Prisma.TravelDiaryUpdateInput;

/**
 * 图片对象类型
 */
export interface TravelPointImage {
	url: string;
	description?: string; // 图片描述
	alt?: string; // 图片alt文本
	caption?: string; // 图片标题
	uploadedAt?: string; // 上传时间
}

/**
 * 旅行点位图标类型
 */
export type TravelPointIconType =
	| "PIN"
	| "HOTEL"
	| "FOOD"
	| "LANDMARK"
	| "PARK"
	| "SHOPPING"
	| "TRANSPORT"
	| "OTHER";

/**
 * 基础类型 - 旅行点位
 */
export interface TravelPoint {
	id: string;
	location: string;
	description: string;
	date: string; // ISO 格式日期字符串
	images: TravelPointImage[]; // 修改：从字符串数组改为图片对象数组
	iconType: TravelPointIconType;
	latitude: number;
	longitude: number;
	order: number; // 用于排序的字段
	country?: string; // 新增：国家
	city?: string; // 新增：城市
}

/**
 * 基础类型 - 旅行时间线
 */
export interface TravelTimeline {
	id: string;
	title: string;
	date: string; // ISO 格式日期字符串
	points: TravelPoint[];
}

/**
 * 基础类型 - 旅行日记内容
 */
export interface TravelDiaryContent {
	timelines: TravelTimeline[];
}

/**
 * 基础类型 - 旅行日记
 * 代表数据库中存储的完整旅行日记对象
 */
export interface TravelDiary {
	id: string;
	userId: string;
	title: string;
	subtitle: string;
	coverImage: string | null;
	content: TravelDiaryContent;
	isPublic: boolean;
	createdAt: Date;
	updatedAt: Date;
}

// ---------------------------------------------
// 输入/请求类型 - 用于API和服务间交互
// ---------------------------------------------

/**
 * 创建旅行日记输入接口
 */
export interface CreateTravelDiaryInput {
	title: string;
	subtitle?: string;
	coverImage?: string;
	content?: TravelDiaryContent;
	isPublic?: boolean;
}

/**
 * 更新旅行日记输入接口
 */
export interface UpdateTravelDiaryInput {
	title?: string;
	subtitle?: string;
	coverImage?: string;
	content?: TravelDiaryContent;
	isPublic?: boolean;
}

/**
 * 添加时间线输入接口
 */
export interface AddTimelineInput {
	title: string;
	date: string;
}

/**
 * 添加旅行点位输入接口
 */
export interface AddTravelPointInput {
	location: string;
	description: string;
	date: string;
	images: TravelPointImage[]; // 修改：从字符串数组改为图片对象数组
	iconType: TravelPointIconType;
	latitude: number;
	longitude: number;
}

// ---------------------------------------------
// 前端特化类型 - 用于前端UI组件
// ---------------------------------------------

/**
 * 前端使用的地理坐标类型
 */
export interface GeoCoordinates {
	lat: number;
	lng: number;
}

/**
 * Frontend representation of a Travel Point
 */
export interface FrontendTravelPoint {
	id: string;
	timelineId: string;
	title: string;
	description?: string | null;
	latitude: number;
	longitude: number;
	coordinates: {
		lat: number;
		lng: number;
	};
	iconType: string;
	timestamp: Date;
	images?: TravelPointImage[]; // 修改：从字符串数组改为图片对象数组
	address?: string | null;
}

/**
 * Frontend representation of a Travel Timeline
 */
export interface FrontendTravelTimeline {
	id: string;
	title: string;
	date: Date;
	points: FrontendTravelPoint[];
}

/**
 * Frontend representation of a Travel Diary
 */
export interface FrontendTravelDiary {
	id: string;
	title: string;
	subtitle?: string | null;
	coverImage?: string | null;
	timelines: FrontendTravelTimeline[];
	richTextDraftContent?: JSONContent | null;
	isPublic: boolean;
}

// ---------------------------------------------
// 响应类型 - 用于API响应
// ---------------------------------------------

/**
 * 日记列表响应类型
 */
export interface DiaryListResponse {
	diaries: TravelDiary[];
}

/**
 * 单个日记响应类型
 */
export interface DiaryResponse {
	diary: TravelDiary;
}

/**
 * 通用操作结果类型
 */
export interface OperationResult {
	success: boolean;
	message?: string;
}

// ---------------------------------------------
// 工具函数 - 帮助类型转换
// ---------------------------------------------

/**
 * Safely checks if a value is a valid Date object.
 */
function isValidDate(d: unknown): d is Date {
	return d instanceof Date && !Number.isNaN(d.getTime());
}

/**
 * Parses a potential date string or object into a Date, returning a default if invalid.
 */
function safeParseDate(value: unknown, defaultValue: Date = new Date()): Date {
	if (!value) return defaultValue;
	try {
		const date = new Date(value as string | number | Date);
		return isValidDate(date) ? date : defaultValue;
	} catch (e) {
		console.warn("Failed to parse date value:", value, e);
		return defaultValue;
	}
}

/**
 * Converts Prisma TravelDiary content (JSON) to FrontendTimelines.
 */
export function transformContentToFrontendTimelines(
	content: PrismaJsonValue | null | undefined,
	timelineIdPrefix = "diary",
): FrontendTravelTimeline[] {
	if (!Array.isArray(content)) {
		if (content != null) {
			console.warn(
				"Invalid content format for timelines, expected array:",
				content,
			);
		}
		return [];
	}

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	return content.reduce(
		(
			acc: FrontendTravelTimeline[],
			timelineData: any,
			timelineIndex: number,
		) => {
			if (!timelineData || typeof timelineData !== "object") {
				console.warn(
					`Skipping invalid timeline data at index ${timelineIndex}:`,
					timelineData,
				);
				return acc;
			}

			const currentTimelineId =
				typeof timelineData.id === "string" && timelineData.id
					? timelineData.id
					: `${timelineIdPrefix}-timeline-${timelineIndex}`;
			const pointsArray = Array.isArray(timelineData.points)
				? timelineData.points
				: [];

			const timeline: FrontendTravelTimeline = {
				id: currentTimelineId,
				title:
					typeof timelineData.title === "string"
						? timelineData.title
						: `Day ${timelineIndex + 1}`,
				date: safeParseDate(timelineData.date),
				// eslint-disable-next-line @typescript-eslint/no-explicit-any
				points: pointsArray.reduce(
					(
						pointAcc: FrontendTravelPoint[],
						pointData: any,
						pointIndex: number,
					) => {
						if (!pointData || typeof pointData !== "object") {
							console.warn(
								`Skipping invalid point data in timeline ${currentTimelineId} at index ${pointIndex}:`,
								pointData,
							);
							return pointAcc;
						}

						// 处理图片数据：支持新旧格式
						let processedImages: TravelPointImage[] = [];
						if (Array.isArray(pointData.images)) {
							processedImages = pointData.images
								.map((img: any) => {
									// 如果是字符串（旧格式），转换为新格式
									if (typeof img === "string") {
										return {
											url: img,
											description: "",
											alt: "",
											caption: "",
										} as TravelPointImage;
									}
									// 如果已经是对象格式（新格式），直接使用
									if (
										typeof img === "object" &&
										img !== null &&
										typeof img.url === "string"
									) {
										return {
											url: img.url,
											description: img.description || "",
											alt: img.alt || "",
											caption: img.caption || "",
											uploadedAt: img.uploadedAt || "",
										} as TravelPointImage;
									}
									// 无效格式，返回空对象
									return {
										url: "",
										description: "",
										alt: "",
										caption: "",
									} as TravelPointImage;
								})
								.filter((img: TravelPointImage) => img.url); // 过滤掉无效的图片
						}

						const point: FrontendTravelPoint = {
							id:
								typeof pointData.id === "string" && pointData.id
									? pointData.id
									: `${currentTimelineId}-point-${pointIndex}`,
							timelineId: currentTimelineId,
							title:
								typeof pointData.title === "string"
									? pointData.title
									: "Untitled Point",
							description:
								typeof pointData.description === "string"
									? pointData.description
									: null,
							latitude:
								typeof pointData.latitude === "number"
									? pointData.latitude
									: 0,
							longitude:
								typeof pointData.longitude === "number"
									? pointData.longitude
									: 0,
							coordinates: {
								lat:
									typeof pointData.latitude === "number"
										? pointData.latitude
										: 0,
								lng:
									typeof pointData.longitude === "number"
										? pointData.longitude
										: 0,
							},
							iconType:
								typeof pointData.iconType === "string"
									? pointData.iconType
									: "default",
							timestamp: safeParseDate(pointData.timestamp),
							images: processedImages, // 使用处理后的图片数组
							address:
								typeof pointData.address === "string"
									? pointData.address
									: null,
						};
						pointAcc.push(point);
						return pointAcc;
					},
					[],
				),
			};
			acc.push(timeline);
			return acc;
		},
		[],
	);
}

/**
 * Converts FrontendTimelines to Prisma TravelDiary content (JSON).
 */
export function transformFrontendTimelinesToContent(
	timelines: FrontendTravelTimeline[],
): PrismaJsonValue {
	if (!Array.isArray(timelines)) {
		console.error(
			"Invalid input for transformFrontendTimelinesToContent: expected array.",
		);
		return [];
	}
	return timelines.map((timeline) => ({
		id: timeline.id,
		title: timeline.title,
		date: isValidDate(timeline.date)
			? timeline.date.toISOString()
			: new Date().toISOString(),
		points: Array.isArray(timeline.points)
			? timeline.points.map((point) => ({
					id: point.id,
					title: point.title,
					description: point.description,
					latitude: point.latitude,
					longitude: point.longitude,
					coordinates: {
						lat: point.latitude,
						lng: point.longitude,
					},
					iconType: point.iconType,
					timestamp: isValidDate(point.timestamp)
						? point.timestamp.toISOString()
						: new Date().toISOString(),
					// 将图片对象数组转换为JSON兼容格式
					images: point.images
						? point.images.map((img) => ({
								url: img.url,
								description: img.description || "",
								alt: img.alt || "",
								caption: img.caption || "",
								uploadedAt: img.uploadedAt || "",
							}))
						: [],
					address: point.address,
				}))
			: [],
	}));
}

/**
 * Converts Prisma TravelDiary to FrontendTravelDiary format.
 */
export function transformDiaryToFrontend(
	diary: PrismaTravelDiary,
): FrontendTravelDiary {
	const timelines = transformContentToFrontendTimelines(
		diary.content,
		diary.id,
	);

	let richTextDraftContent: JSONContent | null = null;
	if (diary.richTextDraftContent) {
		if (
			typeof diary.richTextDraftContent === "object" &&
			diary.richTextDraftContent !== null
		) {
			richTextDraftContent = diary.richTextDraftContent as JSONContent;
		} else if (typeof diary.richTextDraftContent === "string") {
			try {
				richTextDraftContent = JSON.parse(diary.richTextDraftContent);
			} catch (e) {
				console.error(
					"Failed to parse richTextDraftContent string from DB:",
					diary.richTextDraftContent,
					e,
				);
			}
		}
	}

	return {
		id: diary.id,
		title: diary.title,
		subtitle: diary.subtitle ?? null,
		coverImage: diary.coverImage ?? null,
		timelines: timelines,
		richTextDraftContent: richTextDraftContent,
		isPublic: diary.isPublic,
	};
}

/**
 * Converts FrontendTravelDiary to format suitable for Prisma update/create.
 */
export function transformFrontendDiaryToBackend(
	frontendDiary: FrontendTravelDiary,
): Omit<TravelDiaryUpdateInput, "user" | "createdAt" | "updatedAt" | "userId"> {
	let richTextDraftForPrisma: Prisma.InputJsonValue | undefined = undefined;
	if (frontendDiary.richTextDraftContent) {
		richTextDraftForPrisma =
			frontendDiary.richTextDraftContent as Prisma.InputJsonValue;
	}

	return {
		title: frontendDiary.title,
		subtitle: frontendDiary.subtitle,
		coverImage: frontendDiary.coverImage,
		content: transformFrontendTimelinesToContent(
			frontendDiary.timelines,
		) as Prisma.InputJsonValue,
		richTextDraftContent: richTextDraftForPrisma,
		isPublic: frontendDiary.isPublic,
	};
}
