"use client";

import { But<PERSON> } from "@ui/components/button";
import Link from "next/link";

export function PoetryHero() {
	const features = [
		{
			icon: "📍",
			title: "智能地图标记",
			description: "一键在地图上标记重要时刻",
		},
		{
			icon: "🎬",
			title: "自动视频生成",
			description: "AI智能生成精美回忆视频",
		},
		{
			icon: "🌟",
			title: "轻松分享",
			description: "一键分享到社交平台",
		},
	];

	return (
		<section className="relative min-h-screen flex items-center overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20 dark:from-slate-950 dark:via-slate-900 dark:to-slate-900">
			{/* 现代背景装饰 */}
			<div className="absolute inset-0 overflow-hidden">
				{/* 大圆形渐变 */}
				<div className="absolute -top-1/2 -right-1/2 w-full h-full bg-gradient-to-bl from-blue-400/10 via-indigo-400/5 to-transparent rounded-full blur-3xl" />
				<div className="absolute -bottom-1/2 -left-1/2 w-full h-full bg-gradient-to-tr from-blue-500/8 via-cyan-400/3 to-transparent rounded-full blur-3xl" />

				{/* 网格背景 */}
				<div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.03)_1px,transparent_1px)] bg-[size:72px_72px] [mask-image:radial-gradient(ellipse_at_center,black_50%,transparent_100%)]" />
			</div>

			<div className="container relative z-10 mx-auto px-4 py-20">
				<div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-start">
					{/* 左侧内容区 */}
					<div className="space-y-8 pt-2">
						{/* 主标题区域 */}
						<div className="space-y-6">
							<h1 className="text-5xl md:text-5xl lg:text-6xl font-bold tracking-tight leading-none">
								<span className="bg-gradient-to-r from-blue-600 via-blue-500 to-indigo-600 bg-clip-text text-transparent">
									在地图上记录时刻
								</span>
							</h1>
							<p className="text-xl md:text-2xl text-slate-600 dark:text-slate-300 font-light leading-relaxed max-w-lg">
								每个地点都有故事，每段旅程都值得纪念
							</p>
						</div>

						{/* 功能列表 */}
						<div className="space-y-6">
							{features.map((feature, index) => (
								<div
									key={index}
									className="flex items-center space-x-4 p-4 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-2xl border border-slate-200/50 dark:border-slate-700/50 shadow-sm hover:shadow-md transition-all duration-300"
								>
									<div className="text-2xl">
										{feature.icon}
									</div>
									<div>
										<div className="font-medium text-slate-900 dark:text-white">
											{feature.title}
										</div>
										<div className="text-sm text-slate-600 dark:text-slate-400">
											{feature.description}
										</div>
									</div>
								</div>
							))}
						</div>

						{/* CTA按钮组 */}
						<div className="flex flex-col sm:flex-row gap-4 pt-4">
							<Button
								size="lg"
								className="group px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 text-lg font-semibold"
							>
								<Link
									href="/auth/login"
									className="flex items-center space-x-2"
								>
									<span>开始我的地图故事</span>
									<svg
										className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<title>开始箭头</title>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M13 7l5 5m0 0l-5 5m5-5H6"
										/>
									</svg>
								</Link>
							</Button>

							<Button
								variant="outline"
								size="lg"
								className="px-8 py-4 border-2 border-slate-200 dark:border-slate-700 text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-800 rounded-2xl transition-all duration-300 text-lg font-semibold"
							>
								<Link
									href="#demo"
									className="flex items-center space-x-2"
								>
									<svg
										className="w-5 h-5"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<title>播放图标</title>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-6-10h6m0 4a9 9 0 11-18 0 9 9 0 0118 0z"
										/>
									</svg>
									<span>观看演示</span>
								</Link>
							</Button>
						</div>
					</div>

					{/* 右侧产品展示区 */}
					<div className="relative">
						{/* 主产品截图 */}
						<div className="relative z-10">
							<div className="bg-white dark:bg-slate-800 rounded-3xl shadow-2xl p-6 border border-slate-200 dark:border-slate-700">
								{/* 浏览器顶栏 */}
								<div className="flex items-center space-x-2 mb-4">
									<div className="flex space-x-2">
										<div className="w-3 h-3 bg-red-400 rounded-full" />
										<div className="w-3 h-3 bg-yellow-400 rounded-full" />
										<div className="w-3 h-3 bg-green-400 rounded-full" />
									</div>
									<div className="flex-1 bg-slate-100 dark:bg-slate-700 rounded-lg px-3 py-1.5 text-sm text-slate-500 dark:text-slate-400">
										mapmoment.app
									</div>
								</div>

								{/* 应用界面占位图 */}
								<div className="aspect-[4/3] bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-slate-700 dark:to-slate-600 rounded-xl relative overflow-hidden">
									{/* 地图区域 */}
									<div className="absolute inset-4 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
										<div className="text-center">
											<div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mb-4 mx-auto">
												<svg
													className="w-8 h-8 text-white"
													fill="none"
													stroke="currentColor"
													viewBox="0 0 24 24"
												>
													<title>地图界面</title>
													<path
														strokeLinecap="round"
														strokeLinejoin="round"
														strokeWidth={2}
														d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"
													/>
												</svg>
											</div>
											<div className="text-sm text-slate-600 dark:text-slate-300 font-medium">
												地图界面预览
											</div>
										</div>
									</div>

									{/* 标记点 */}
									<div className="absolute top-8 left-8 w-4 h-4 bg-red-500 rounded-full animate-ping" />
									<div className="absolute top-8 left-8 w-4 h-4 bg-red-500 rounded-full" />

									<div
										className="absolute bottom-12 right-12 w-4 h-4 bg-green-500 rounded-full animate-ping"
										style={{ animationDelay: "1s" }}
									/>
									<div className="absolute bottom-12 right-12 w-4 h-4 bg-green-500 rounded-full" />
								</div>
							</div>
						</div>

						{/* 浮动的功能卡片 */}
						<div className="absolute -top-6 -right-6 bg-white dark:bg-slate-800 rounded-2xl shadow-lg p-4 border border-slate-200 dark:border-slate-700 z-20">
							<div className="flex items-center space-x-3">
								<div className="w-10 h-10 bg-gradient-to-br from-green-400 to-green-500 rounded-xl flex items-center justify-center">
									<svg
										className="w-5 h-5 text-white"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<title>视频图标</title>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
										/>
									</svg>
								</div>
								<div>
									<div className="text-sm font-semibold text-slate-900 dark:text-white">
										视频生成
									</div>
									<div className="text-xs text-slate-500 dark:text-slate-400">
										已完成 3 个
									</div>
								</div>
							</div>
						</div>

						<div className="absolute -bottom-6 -left-6 bg-white dark:bg-slate-800 rounded-2xl shadow-lg p-4 border border-slate-200 dark:border-slate-700 z-20">
							<div className="flex items-center space-x-3">
								<div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-purple-500 rounded-xl flex items-center justify-center">
									<svg
										className="w-5 h-5 text-white"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<title>分享图标</title>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
										/>
									</svg>
								</div>
								<div>
									<div className="text-sm font-semibold text-slate-900 dark:text-white">
										已分享
									</div>
									<div className="text-xs text-slate-500 dark:text-slate-400">
										126 次浏览
									</div>
								</div>
							</div>
						</div>

						{/* 背景装饰 */}
						<div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 rounded-3xl blur-3xl -z-10 scale-110" />
					</div>
				</div>
			</div>
		</section>
	);
}
