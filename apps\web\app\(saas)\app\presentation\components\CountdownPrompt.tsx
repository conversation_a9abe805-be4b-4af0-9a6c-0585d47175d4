"use client";
import { useTranslations } from "next-intl";

interface CountdownPromptProps {
	countdown: number;
	isVisible: boolean;
}

export function CountdownPrompt({
	countdown,
	isVisible,
}: CountdownPromptProps) {
	const t = useTranslations("travelMemo.countdownPrompt");
	if (!isVisible || countdown <= 0) return null;

	return (
		<div className="absolute left-24 top-1/5 transform -translate-y-[-10px] z-30 pointer-events-none">
			<div className="relative bg-black/80 backdrop-blur-sm text-white px-4 py-2 rounded-lg max-w-[240px] shadow-lg">
				<div className="font-medium text-sm mb-1">
					{t("autoPlayMessage", { countdown })}
				</div>
				<div className="text-xs text-gray-300">
					{t("manualControlHint")}
				</div>
				{/* 指向播放按钮的小三角 */}
				<div className="absolute left-[-8px] top-1/2 transform -translate-y-1/2 border-8 border-transparent border-r-black/80" />
			</div>
		</div>
	);
}
