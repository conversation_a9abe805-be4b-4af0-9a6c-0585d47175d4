import { createStorageProviderFromEnv } from "@repo/storage";
import type { StorageProviderType } from "@repo/storage";
import { type NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
	try {
		const requestData = await request.json();
		const { provider, bucket, paths, path, contentType, region } =
			requestData;

		// 同时支持 paths 数组和单个 path
		if (!bucket || (!paths && !path)) {
			return NextResponse.json(
				{ error: "缺少必要参数 bucket 或 paths/path" },
				{ status: 400 },
			);
		}

		// 获取存储提供商实例
		let storageProvider;

		try {
			// 如果指定了provider，使用指定的provider
			if (provider) {
				storageProvider = createStorageProviderFromEnv(
					provider as StorageProviderType,
				);
			} else {
				// 否则尝试从环境变量中检测默认提供商
				storageProvider = createStorageProviderFromEnv();
			}
		} catch (error) {
			console.error("创建存储提供商失败:", error);
			return NextResponse.json(
				{
					error:
						error instanceof Error
							? error.message
							: "无法创建存储提供商",
				},
				{ status: 500 },
			);
		}

		// 如果提供了 paths 数组，则使用批量获取
		if (paths && Array.isArray(paths)) {
			// 获取批量签名上传URL
			const result = await storageProvider.getBatchSignedUploadUrls({
				bucket,
				paths,
				contentType,
				region,
			});

			return NextResponse.json(result);
		}
		// 否则使用单个路径获取
		else if (path) {
			// 获取单个签名上传URL
			const url = await storageProvider.getSignedUploadUrl(path, {
				bucket,
				contentType,
				region,
			});

			return NextResponse.json({ url });
		} else {
			return NextResponse.json(
				{ error: "paths 必须是一个数组或提供单个 path" },
				{ status: 400 },
			);
		}
	} catch (error) {
		console.error("获取上传URL错误:", error);
		return NextResponse.json(
			{
				error:
					error instanceof Error ? error.message : "获取上传URL失败",
			},
			{ status: 500 },
		);
	}
}
