"use client";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { Button } from "@ui/components/button";
import { format } from "date-fns";
import { CalendarDays, Camera, MapPin, Trash2 } from "lucide-react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";

interface DiaryCardProps {
	id?: string;
	title: string;
	location: string;
	imageUrl: string;
	date: string;
	excerpt: string;
	// 新增可选属性
	endDate?: string;
	footprintsCount?: number;
	photosCount?: number;
	// 删除功能回调
	onDelete?: (id: string) => Promise<void>;
}

const DiaryCard = ({
	id,
	title,
	location,
	imageUrl,
	date,
	excerpt,
	endDate,
	footprintsCount = 0,
	photosCount = 0,
	onDelete,
}: DiaryCardProps) => {
	const t = useTranslations("travelMemo.diaryCard");

	// 日期显示格式化
	const formatDateDisplay = () => {
		if (!date) return t("dateNotSet");

		const startDate = new Date(date);
		const formattedStart = format(startDate, "yyyy.MM.dd");

		if (endDate) {
			const end = new Date(endDate);
			const formattedEnd = format(end, "yyyy.MM.dd");
			return `${formattedStart} - ${formattedEnd}`;
		}

		return formattedStart;
	};

	// 图片懒加载和错误处理
	const [imageError, setImageError] = useState(false);
	const [isImageLoaded, setIsImageLoaded] = useState(false);
	const imgRef = useRef<HTMLImageElement>(null);
	const fallbackImageUrl =
		"https://images.pexels.com/photos/1051075/pexels-photo-1051075.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2";

	// 删除确认对话框状态
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false); // 实现图片懒加载 - 使用IntersectionObserver优化性能
	useEffect(() => {
		// 创建一个观察器实例
		const observer = new IntersectionObserver(
			(entries) => {
				entries.forEach((entry) => {
					if (entry.isIntersecting && imgRef.current) {
						const img = imgRef.current;
						// 设置图片src以触发加载
						img.src = imageError ? fallbackImageUrl : imageUrl;
						// 一旦加载，停止观察
						observer.unobserve(img);
					}
				});
			},
			// 提前100px开始加载图片，提升用户体验
			{ rootMargin: "100px", threshold: 0.1 },
		);

		// 开始观察图片元素
		if (imgRef.current) {
			observer.observe(imgRef.current);
		}

		// 清理函数
		return () => {
			if (imgRef.current) {
				observer.unobserve(imgRef.current);
			}
			observer.disconnect();
		};
	}, [imageUrl, imageError, fallbackImageUrl]);

	// 处理删除操作
	const handleDelete = async (e: React.MouseEvent) => {
		// 阻止事件冒泡，避免触发Link的点击事件
		e.preventDefault();
		e.stopPropagation();

		if (!id || !onDelete) return;

		// 打开确认对话框
		setIsDeleteDialogOpen(true);
	};

	// 确认删除
	const confirmDelete = async () => {
		if (!id || !onDelete) return;

		try {
			await onDelete(id);
			// 删除成功后关闭对话框
			setIsDeleteDialogOpen(false);
		} catch (error) {
			console.error("删除日记失败:", error);
			// 即使失败也关闭对话框
			setIsDeleteDialogOpen(false);
		}
	};

	const href = id ? `/app/diary/${id}` : "/app/diary/new";
	return (
		<>
			<Link href={href} className="block relative">
				<div className="travel-card group animate-fade-in border border-border rounded-lg shadow-sm hover:shadow-lg hover:scale-[1.02] transition-all duration-300 ease-in-out transform-gpu cursor-pointer h-full flex flex-col">
					<div className="relative overflow-hidden h-40">
						{/* 懒加载的图片 */}
						<div
							className={`absolute inset-0 bg-gray-100 ${
								isImageLoaded ? "opacity-0" : "opacity-100"
							} transition-opacity duration-300`}
						/>
						<img
							ref={imgRef}
							data-src={imageUrl}
							alt={title}
							onError={() => setImageError(true)}
							onLoad={() => setIsImageLoaded(true)}
							className={`absolute inset-0 w-full h-full object-cover group-hover:scale-110 transition-transform duration-300 ease-in-out transform-gpu ${
								isImageLoaded ? "opacity-100" : "opacity-0"
							}`}
						/>
						<div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4 text-white">
							<h3 className="font-bold text-lg">{title}</h3>
							<p className="text-sm text-white/80">{location}</p>
						</div>
					</div>
					<div className="p-4 flex-1 flex flex-col">
						{/* 日期区域 - 显示具体日期范围 */}
						<div className="flex items-center text-sm text-gray-500 mb-3">
							<CalendarDays className="h-4 w-4 mr-1" />
							<span>{formatDateDisplay()}</span>
						</div>
						{/* 内容摘要 - 固定高度区域 */}
						<div className="flex-1">
							<p className="text-gray-600 line-clamp-3 min-h-[4.5rem]">
								{excerpt}
							</p>
						</div>
						{/* 统计信息 - 始终显示，保证卡片高度一致 */}
						<div className="mt-3 flex items-center space-x-4 text-xs text-gray-500">
							<div className="flex items-center">
								<MapPin className="h-3 w-3 mr-1" />
								<span>
									{t("footprintsCount", {
										count: footprintsCount,
									})}
								</span>
							</div>
							<div className="flex items-center">
								<Camera className="h-3 w-3 mr-1" />
								<span>
									{t("photosCount", { count: photosCount })}
								</span>
							</div>
						</div>
					</div>

					{/* 删除按钮 - 悬停时显示 */}
					{onDelete && id && (
						<Button
							variant="outline"
							size="icon"
							className="absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200 h-8 w-8 rounded-full bg-red-500/80 hover:bg-red-600 text-white shadow-md"
							onClick={handleDelete}
							onKeyDown={(e) =>
								e.key === "Enter" &&
								handleDelete(e as unknown as React.MouseEvent)
							}
							aria-label={t("deleteDiaryLabel")}
						>
							<Trash2 className="h-4 w-4" />
						</Button>
					)}
				</div>
			</Link>

			{/* 删除确认对话框 */}
			<AlertDialog
				open={isDeleteDialogOpen}
				onOpenChange={setIsDeleteDialogOpen}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>
							{t("deleteConfirm.title")}
						</AlertDialogTitle>
						<AlertDialogDescription>
							{t("deleteConfirm.descriptionPart1", { title })}
							<span className="font-semibold text-destructive">
								{t("deleteConfirm.irreversible")}
							</span>
							{t("deleteConfirm.descriptionPart2")}
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>
							{t("deleteConfirm.cancel")}
						</AlertDialogCancel>
						<AlertDialogAction
							onClick={confirmDelete}
							className="bg-red-500 hover:bg-red-600 text-white"
						>
							{t("deleteConfirm.confirm")}
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</>
	);
};

export default DiaryCard;
