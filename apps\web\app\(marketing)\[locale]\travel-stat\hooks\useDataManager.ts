"use client";

import { useCallback, useEffect, useRef, useState } from "react";
import type {
	CardCustomization,
	SocialPlatform,
} from "../components/card-generator/types/cardTypes";
import type {
	AnimationTheme,
	AtmosphereTheme,
	MapProjectionType,
	MapStyleType,
} from "../components/types";
import { getGlobalDataManager } from "../storage";
import type { TravelFootprintData } from "../storage/types";
import type { GeocodeFeature, TravelPoint } from "../types";
import type { ColorThemeType } from "../types/colorTypes";
import type { MarkerStyleType } from "../types/markerTypes";

/**
 * 统一的数据管理器 Hook
 * 替代原有的分散存储 hooks，提供统一的数据管理接口
 */
export function useDataManager() {
	const dataManager = useRef(getGlobalDataManager());
	const [isInitialized, setIsInitialized] = useState(false);
	const [data, setData] = useState<TravelFootprintData | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	// 初始化数据管理器
	useEffect(() => {
		const initializeData = async () => {
			try {
				setIsLoading(true);
				setError(null);

				const result = await dataManager.current.initialize();

				if (result.success && result.data) {
					setData(result.data);
					setIsInitialized(true);
				} else {
					setError(result.error || "初始化数据管理器失败");
				}
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
			} finally {
				setIsLoading(false);
			}
		};

		if (!isInitialized) {
			initializeData();
		}
	}, [isInitialized]);

	// 更新本地状态
	const updateLocalState = useCallback(() => {
		const currentData = dataManager.current.getData();
		setData(currentData);
	}, []);

	// 旅行数据操作
	const addTravelPoint = useCallback(
		async (feature: GeocodeFeature): Promise<TravelPoint | null> => {
			try {
				const result =
					await dataManager.current.addTravelPoint(feature);
				if (result.success && result.data) {
					updateLocalState();
					return result.data;
				}
				setError(result.error || "添加旅行点失败");
				return null;
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
				return null;
			}
		},
		[updateLocalState],
	);

	const removeTravelPoint = useCallback(
		async (pointId: string): Promise<boolean> => {
			try {
				const result =
					await dataManager.current.removeTravelPoint(pointId);
				if (result.success) {
					updateLocalState();
					return true;
				}
				setError(result.error || "删除旅行点失败");
				return false;
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
				return false;
			}
		},
		[updateLocalState],
	);

	// 地图配置操作
	const updateAtmosphereTheme = useCallback(
		async (theme: AtmosphereTheme): Promise<boolean> => {
			try {
				const result = await dataManager.current.updateMapConfig({
					atmosphereTheme: theme,
				});
				if (result.success) {
					updateLocalState();
					return true;
				}
				setError(result.error || "更新大气层主题失败");
				return false;
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
				return false;
			}
		},
		[updateLocalState],
	);

	const updateMapStyle = useCallback(
		async (style: MapStyleType): Promise<boolean> => {
			try {
				const result = await dataManager.current.updateMapConfig({
					mapStyle: style,
				});
				if (result.success) {
					updateLocalState();
					return true;
				}
				setError(result.error || "更新地图样式失败");
				return false;
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
				return false;
			}
		},
		[updateLocalState],
	);

	const updateMapProjection = useCallback(
		async (projection: MapProjectionType): Promise<boolean> => {
			try {
				const result = await dataManager.current.updateMapConfig({
					mapProjection: projection,
				});
				if (result.success) {
					updateLocalState();
					return true;
				}
				setError(result.error || "更新地图投影失败");
				return false;
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
				return false;
			}
		},
		[updateLocalState],
	);

	const updateAnimationTheme = useCallback(
		async (theme: AnimationTheme): Promise<boolean> => {
			try {
				const result = await dataManager.current.updateMapConfig({
					animationTheme: theme,
				});
				if (result.success) {
					updateLocalState();
					return true;
				}
				setError(result.error || "更新动画主题失败");
				return false;
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
				return false;
			}
		},
		[updateLocalState],
	);

	const updateViewState = useCallback(
		async (viewState: {
			latitude: number;
			longitude: number;
			zoom: number;
		}): Promise<boolean> => {
			try {
				const result = await dataManager.current.updateMapConfig({
					viewState,
				});
				if (result.success) {
					updateLocalState();
					return true;
				}
				setError(result.error || "更新视图状态失败");
				return false;
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
				return false;
			}
		},
		[updateLocalState],
	);

	// 样式配置操作
	const updateColorTheme = useCallback(
		async (theme: ColorThemeType): Promise<boolean> => {
			try {
				const result = await dataManager.current.updateStyleConfig({
					colorTheme: theme,
				});
				if (result.success) {
					updateLocalState();
					return true;
				}
				setError(result.error || "更新颜色主题失败");
				return false;
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
				return false;
			}
		},
		[updateLocalState],
	);

	const updateMarkerStyle = useCallback(
		async (style: MarkerStyleType): Promise<boolean> => {
			try {
				const result = await dataManager.current.updateStyleConfig({
					markerStyle: style,
				});
				if (result.success) {
					updateLocalState();
					return true;
				}
				setError(result.error || "更新标记样式失败");
				return false;
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
				return false;
			}
		},
		[updateLocalState],
	);

	const updateMarkerTheme = useCallback(
		async (theme: string): Promise<boolean> => {
			try {
				const result = await dataManager.current.updateStyleConfig({
					markerTheme: theme,
				});
				if (result.success) {
					updateLocalState();
					return true;
				}
				setError(result.error || "更新标记主题失败");
				return false;
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
				return false;
			}
		},
		[updateLocalState],
	);

	const updateEmoji = useCallback(
		async (emoji: string): Promise<boolean> => {
			try {
				const result = await dataManager.current.updateStyleConfig({
					currentEmoji: emoji,
				});
				if (result.success) {
					updateLocalState();
					return true;
				}
				setError(result.error || "更新Emoji失败");
				return false;
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
				return false;
			}
		},
		[updateLocalState],
	);

	const updateEmojiColor = useCallback(
		async (color: string): Promise<boolean> => {
			try {
				const result = await dataManager.current.updateStyleConfig({
					currentEmojiColor: color,
				});
				if (result.success) {
					updateLocalState();
					return true;
				}
				setError(result.error || "更新Emoji颜色失败");
				return false;
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
				return false;
			}
		},
		[updateLocalState],
	);

	const updateHideOutline = useCallback(
		async (hide: boolean): Promise<boolean> => {
			try {
				const result = await dataManager.current.updateStyleConfig({
					hideOutline: hide,
				});
				if (result.success) {
					updateLocalState();
					return true;
				}
				setError(result.error || "更新轮廓显示状态失败");
				return false;
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
				return false;
			}
		},
		[updateLocalState],
	);

	// 卡片配置操作
	const updateSelectedPlatform = useCallback(
		async (platform: SocialPlatform): Promise<boolean> => {
			try {
				const result = await dataManager.current.updateCardConfig({
					selectedPlatform: platform,
				});
				if (result.success) {
					updateLocalState();
					return true;
				}
				setError(result.error || "更新选择平台失败");
				return false;
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
				return false;
			}
		},
		[updateLocalState],
	);

	const updateSelectedTemplateId = useCallback(
		async (templateId: string): Promise<boolean> => {
			try {
				const result = await dataManager.current.updateCardConfig({
					selectedTemplateId: templateId,
				});
				if (result.success) {
					updateLocalState();
					return true;
				}
				setError(result.error || "更新选择模板失败");
				return false;
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
				return false;
			}
		},
		[updateLocalState],
	);

	const updateCardCustomization = useCallback(
		async (customization: Partial<CardCustomization>): Promise<boolean> => {
			try {
				const result =
					await dataManager.current.updateCardCustomization(
						customization,
					);
				if (result.success) {
					updateLocalState();
					return true;
				}
				setError(result.error || "更新卡片自定义配置失败");
				return false;
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
				return false;
			}
		},
		[updateLocalState],
	);

	const updateUserProfile = useCallback(
		async (
			userProfile: Partial<
				TravelFootprintData["cardConfig"]["userProfile"]
			>,
		): Promise<boolean> => {
			try {
				const result =
					await dataManager.current.updateUserProfile(userProfile);
				if (result.success) {
					updateLocalState();
					return true;
				}
				setError(result.error || "更新用户资料失败");
				return false;
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
				return false;
			}
		},
		[updateLocalState],
	);

	const updateExportSettings = useCallback(
		async (
			exportSettings: Partial<
				TravelFootprintData["cardConfig"]["exportSettings"]
			>,
		): Promise<boolean> => {
			try {
				const result =
					await dataManager.current.updateExportSettings(
						exportSettings,
					);
				if (result.success) {
					updateLocalState();
					return true;
				}
				setError(result.error || "更新导出设置失败");
				return false;
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
				return false;
			}
		},
		[updateLocalState],
	);

	// 用户偏好操作
	const updatePreferences = useCallback(
		async (
			preferences: Partial<TravelFootprintData["preferences"]>,
		): Promise<boolean> => {
			try {
				const result =
					await dataManager.current.updatePreferences(preferences);
				if (result.success) {
					updateLocalState();
					return true;
				}
				setError(result.error || "更新用户偏好失败");
				return false;
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
				return false;
			}
		},
		[updateLocalState],
	);

	// 数据操作
	const clearAllData = useCallback(async (): Promise<boolean> => {
		try {
			const result = await dataManager.current.clearAllData();
			if (result.success) {
				updateLocalState();
				return true;
			}
			setError(result.error || "清空数据失败");
			return false;
		} catch (err) {
			setError(err instanceof Error ? err.message : String(err));
			return false;
		}
	}, [updateLocalState]);

	const importData = useCallback(
		async (importedData: TravelFootprintData): Promise<boolean> => {
			try {
				const result =
					await dataManager.current.importData(importedData);
				if (result.success) {
					updateLocalState();
					return true;
				}
				setError(result.error || "导入数据失败");
				return false;
			} catch (err) {
				setError(err instanceof Error ? err.message : String(err));
				return false;
			}
		},
		[updateLocalState],
	);

	const exportData = useCallback((): TravelFootprintData | null => {
		return dataManager.current.exportData();
	}, []);

	// 销毁
	useEffect(() => {
		return () => {
			// 组件卸载时不销毁全局数据管理器，因为它可能被其他组件使用
		};
	}, []);

	// 便捷的数据访问器
	const travelData = data?.travel || { points: [], countries: [] };
	const mapConfig = data?.mapConfig;
	const styleConfig = data?.styleConfig;
	const cardConfig = data?.cardConfig;
	const preferences = data?.preferences;

	return {
		// 状态
		isInitialized,
		isLoading,
		error,
		data,

		// 旅行数据
		travelPoints: travelData.points,
		visitedCountries: travelData.countries,

		// 地图配置
		atmosphereTheme: mapConfig?.atmosphereTheme,
		mapStyle: mapConfig?.mapStyle,
		mapProjection: mapConfig?.mapProjection,
		animationTheme: mapConfig?.animationTheme,
		viewState: mapConfig?.viewState,

		// 样式配置
		colorTheme: styleConfig?.colorTheme,
		markerStyle: styleConfig?.markerStyle,
		markerTheme: styleConfig?.markerTheme,
		currentEmoji: styleConfig?.currentEmoji,
		currentEmojiColor: styleConfig?.currentEmojiColor,
		hideOutline: styleConfig?.hideOutline,

		// 卡片配置
		selectedPlatform: cardConfig?.selectedPlatform,
		selectedTemplateId: cardConfig?.selectedTemplateId,
		cardCustomization: cardConfig?.customization,
		userProfile: cardConfig?.userProfile,
		exportSettings: cardConfig?.exportSettings,

		// 用户偏好
		preferences,

		// 操作方法
		addTravelPoint,
		removeTravelPoint,
		clearAllData,
		importData,
		exportData,

		// 配置更新方法
		updateAtmosphereTheme,
		updateMapStyle,
		updateMapProjection,
		updateAnimationTheme,
		updateViewState,
		updateColorTheme,
		updateMarkerStyle,
		updateMarkerTheme,
		updateEmoji,
		updateEmojiColor,
		updateHideOutline,

		// 卡片配置更新方法
		updateSelectedPlatform,
		updateSelectedTemplateId,
		updateCardCustomization,
		updateUserProfile,
		updateExportSettings,

		// 用户偏好更新方法
		updatePreferences,

		// 错误处理
		clearError: () => setError(null),
	};
}

export type UseDataManagerReturn = ReturnType<typeof useDataManager>;
