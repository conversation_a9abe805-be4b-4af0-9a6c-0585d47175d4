# 多数据库配置指南

## 概述

这个项目支持多数据库架构，不同的应用可以使用独立的数据库实例，同时复用基础模块。

## 数据库架构

```
┌─────────────────┐    ┌─────────────────┐
│   Travel Memo   │    │  AI Images DB   │
│   Database      │    │                 │
│                 │    │                 │
│ - users         │    │ - users (sync)  │
│ - travel_diary  │    │ - ai_images     │
│ - sessions      │    │ - favorites     │
│ - accounts      │    │ - comments      │
│ - ...           │    │ - templates     │
└─────────────────┘    └─────────────────┘
```

## 环境变量配置

```bash
# 主数据库（旅行日记）
DATABASE_URL="postgresql://username:password@localhost:5432/travel_memo"

# AI图片生成器数据库
AI_IMAGES_DATABASE_URL="postgresql://username:password@localhost:5432/ai_images"

# 生产环境可以使用不同的数据库服务器
# AI_IMAGES_DATABASE_URL="************************************************/ai_images"
```

## Schema 文件

- `schema.prisma` - 主数据库（旅行日记）
- `schema-ai-images.prisma` - AI图片生成器数据库

## 数据库客户端

```typescript
// 使用主数据库
import { prisma } from '@repo/database';

// 使用AI图片数据库
import { aiImagesPrisma } from '@repo/database';
```

## 🔄 表字段更新最佳实践

### 1. 主数据库（旅行日记）字段更新

```bash
# 1. 修改 schema.prisma 文件
# 2. 创建迁移
pnpm --filter database migrate --name "add_new_field_to_travel_diary"

# 3. 生成新的类型
pnpm --filter database generate

# 4. 推送到开发数据库（可选，用于快速原型）
pnpm --filter database push
```

### 2. AI图片数据库字段更新

```bash
# 1. 修改 schema-ai-images.prisma 文件
# 2. 使用专用脚本创建迁移
pnpm --filter database tsx scripts/migrate-ai-images.ts migrate add_new_field_to_ai_images

# 3. 生成新的类型
pnpm --filter database tsx scripts/migrate-ai-images.ts generate

# 4. 推送到开发数据库（可选）
pnpm --filter database tsx scripts/migrate-ai-images.ts push
```

### 3. 批量操作

```bash
# 同时更新所有数据库的类型
pnpm --filter database generate:all

# 查看所有数据库状态
pnpm --filter database studio              # 主数据库
pnpm --filter database studio:ai-images    # AI图片数据库
```

## 📋 迁移管理工具

我们提供了专门的迁移管理脚本 `scripts/migrate-ai-images.ts`：

```bash
# 查看帮助
pnpm --filter database tsx scripts/migrate-ai-images.ts

# 生成类型
pnpm --filter database tsx scripts/migrate-ai-images.ts generate

# 创建迁移
pnpm --filter database tsx scripts/migrate-ai-images.ts migrate add_image_categories

# 推送到开发环境
pnpm --filter database tsx scripts/migrate-ai-images.ts push

# 启动数据库管理界面
pnpm --filter database tsx scripts/migrate-ai-images.ts studio

# 生产环境部署
pnpm --filter database tsx scripts/migrate-ai-images.ts deploy
```

## 🚀 实际操作示例

### 示例1：为AI图片生成器添加分类字段

1. **修改schema文件**：
```prisma
// packages/database/prisma/schema-ai-images.prisma
model AiImageGeneration {
  // ... 现有字段
  category    String?   // 新增：图片分类
  subcategory String?   // 新增：子分类
  // ...
}
```

2. **创建迁移**：
```bash
pnpm --filter database tsx scripts/migrate-ai-images.ts migrate add_image_categories
```

3. **生成类型**：
```bash
pnpm --filter database tsx scripts/migrate-ai-images.ts generate
```

4. **更新代码**：
```typescript
// 现在可以使用新字段
const image = await aiImagesPrisma.aiImageGeneration.create({
  data: {
    prompt: "一只猫",
    category: "动物",
    subcategory: "宠物",
    // ...
  }
});
```

### 示例2：为旅行日记添加新字段

1. **修改schema文件**：
```prisma
// packages/database/prisma/schema.prisma
model TravelDiary {
  // ... 现有字段
  weather     String?   // 新增：天气信息
  temperature Int?      // 新增：温度
  // ...
}
```

2. **创建迁移**：
```bash
pnpm --filter database migrate --name "add_weather_fields"
```

3. **生成类型**：
```bash
pnpm --filter database generate
```

## 🔄 跨数据库字段同步

当需要在多个数据库中保持某些字段一致时：

### 1. 用户信息同步

```typescript
// packages/api/src/services/user-sync.ts
export async function syncUserAcrossDatabases(userId: string) {
  // 从主数据库获取用户信息
  const mainUser = await prisma.user.findUnique({
    where: { id: userId }
  });

  if (!mainUser) return;

  // 同步到AI图片数据库
  await aiImagesPrisma.user.upsert({
    where: { id: userId },
    update: {
      name: mainUser.name,
      email: mainUser.email,
      image: mainUser.image,
      // 只同步必要字段
    },
    create: {
      id: mainUser.id,
      name: mainUser.name,
      email: mainUser.email,
      emailVerified: mainUser.emailVerified,
      // ...
    }
  });
}
```

### 2. 字段变更时的同步策略

```typescript
// 当主数据库用户字段更新时
export async function updateUserAndSync(userId: string, data: UpdateUserData) {
  // 1. 更新主数据库
  const updatedUser = await prisma.user.update({
    where: { id: userId },
    data
  });

  // 2. 同步到其他数据库
  await syncUserAcrossDatabases(userId);

  return updatedUser;
}
```

## 📦 Package.json 脚本

我们在 `packages/database/package.json` 中提供了便捷脚本：

```json
{
  "scripts": {
    "generate": "prisma generate",
    "generate:ai-images": "prisma generate --schema=prisma/schema-ai-images.prisma",
    "generate:all": "pnpm run generate && pnpm run generate:ai-images",
    "migrate": "dotenv -c -e ../../.env -- prisma migrate dev",
    "migrate:ai-images": "dotenv -c -e ../../.env -- prisma migrate dev --schema=prisma/schema-ai-images.prisma",
    "push": "dotenv -c -e ../../.env -- prisma db push --skip-generate",
    "push:ai-images": "dotenv -c -e ../../.env -- prisma db push --skip-generate --schema=prisma/schema-ai-images.prisma",
    "studio": "dotenv -c -e ../../.env -- prisma studio",
    "studio:ai-images": "dotenv -c -e ../../.env -- prisma studio --schema=prisma/schema-ai-images.prisma"
  }
}
```

## 用户数据同步

由于不同数据库需要用户信息，我们提供了用户同步功能：

```typescript
import { syncUserFromMainDatabase } from '@repo/api/services/ai-image-service';

// 当用户在主应用注册时，同步到AI图片数据库
await syncUserFromMainDatabase({
  id: user.id,
  name: user.name,
  email: user.email,
  emailVerified: user.emailVerified,
  // ... 其他字段
});
```

## 部署策略

### 开发环境
- 可以使用同一个PostgreSQL实例的不同数据库
- 简化配置和管理
- 使用 `push` 命令快速原型开发

### 生产环境
- 推荐使用独立的数据库实例
- 更好的性能隔离和扩展性
- 独立的备份和恢复策略
- 使用 `migrate deploy` 命令部署

## ⚠️ 重要注意事项

### 1. 迁移顺序
- 总是先在开发环境测试迁移
- 生产环境部署前备份数据库
- 考虑迁移的向后兼容性

### 2. 字段类型变更
```bash
# 危险操作：改变字段类型可能导致数据丢失
# 建议分步骤进行：
# 1. 添加新字段
# 2. 迁移数据
# 3. 删除旧字段
```

### 3. 索引管理
```prisma
// 为新字段添加索引
model AiImageGeneration {
  category String?
  
  @@index([category])  // 添加索引提高查询性能
}
```

### 4. 数据一致性
- 跨数据库操作不支持事务
- 需要实现补偿机制
- 考虑使用事件驱动架构

## 优势

1. **数据隔离** - 不同应用的数据完全分离
2. **独立扩展** - 可以根据需要独立扩展数据库
3. **安全性** - 降低数据泄露风险
4. **维护性** - 独立的备份和恢复策略
5. **性能** - 避免单一数据库的性能瓶颈
6. **灵活的迁移** - 每个数据库可以独立演进

## 注意事项

1. **用户同步** - 需要确保用户数据在不同数据库间的一致性
2. **事务处理** - 跨数据库事务需要特殊处理
3. **连接管理** - 注意数据库连接池的配置
4. **监控** - 需要监控多个数据库实例的健康状态
5. **迁移协调** - 相关字段变更需要协调多个数据库的迁移时机 