// 数据存储抽象接口
export interface IDataStorage {
	/**
	 * 获取数据
	 * @param key 存储键
	 * @returns Promise<T | null>
	 */
	get<T = any>(key: string): Promise<T | null>;

	/**
	 * 设置数据
	 * @param key 存储键
	 * @param value 数据值
	 * @returns Promise<void>
	 */
	set<T = any>(key: string, value: T): Promise<void>;

	/**
	 * 删除数据
	 * @param key 存储键
	 * @returns Promise<void>
	 */
	remove(key: string): Promise<void>;

	/**
	 * 清空所有数据
	 * @returns Promise<void>
	 */
	clear(): Promise<void>;

	/**
	 * 检查键是否存在
	 * @param key 存储键
	 * @returns Promise<boolean>
	 */
	has(key: string): Promise<boolean>;

	/**
	 * 获取所有键
	 * @returns Promise<string[]>
	 */
	keys(): Promise<string[]>;
}

// 数据存储错误类型
export class DataStorageError extends Error {
	constructor(
		message: string,
		public readonly code: string,
		public readonly cause?: Error,
	) {
		super(message);
		this.name = "DataStorageError";
	}
}

// 存储配置
export interface StorageConfig {
	// 存储键前缀
	keyPrefix?: string;
	// 是否启用数据压缩
	enableCompression?: boolean;
	// 数据版本（用于迁移）
	version?: string;
	// 错误处理策略
	errorStrategy?: "throw" | "log" | "silent";
}
