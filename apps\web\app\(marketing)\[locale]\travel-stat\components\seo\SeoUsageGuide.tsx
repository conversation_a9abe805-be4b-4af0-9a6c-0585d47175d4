import type { TravelStatTranslations } from "../../hooks/useTravelStatTranslations";

interface SeoUsageGuideProps {
	translations: TravelStatTranslations;
}

interface StepItemProps {
	stepNumber: number;
	title: string;
	description: string;
	bgColor: string;
}

function StepItem({ stepNumber, title, description, bgColor }: StepItemProps) {
	return (
		<div className="flex items-start space-x-4">
			<div
				className={`flex-shrink-0 w-8 h-8 ${bgColor} text-white rounded-full flex items-center justify-center font-semibold text-sm`}
			>
				{stepNumber}
			</div>
			<div>
				<h4 className="font-semibold text-gray-900 dark:text-white mb-1">
					{title}
				</h4>
				<p className="text-gray-600 dark:text-gray-300 text-sm">
					{description}
				</p>
			</div>
		</div>
	);
}

export function SeoUsageGuide({ translations }: SeoUsageGuideProps) {
	const leftSteps = [
		{
			stepNumber: 1,
			title:
				translations.seo?.howToUse?.steps?.searchAndAdd?.title() ||
				"Mark Your Memory Spots",
			description:
				translations.seo?.howToUse?.steps?.searchAndAdd?.description() ||
				"Search for any place where you've created special memories. Add cities, landmarks, or hidden gems to your personal Map Moment collection.",
			bgColor: "bg-blue-500",
		},
		{
			stepNumber: 2,
			title:
				translations.seo?.howToUse?.steps?.viewStatistics?.title() ||
				"Track Your Journey",
			description:
				translations.seo?.howToUse?.steps?.viewStatistics?.description() ||
				"Watch your Map Moment collection grow! See how many countries you've explored, cities you've visited, and precious moments you've captured.",
			bgColor: "bg-green-500",
		},
		{
			stepNumber: 3,
			title:
				translations.seo?.howToUse?.steps?.generateCards?.title() ||
				"Create Memory Cards",
			description:
				translations.seo?.howToUse?.steps?.generateCards?.description() ||
				"Transform your Map Moment collection into beautiful shareable cards. Perfect for social media or keeping as digital keepsakes of your adventures.",
			bgColor: "bg-purple-500",
		},
	];

	const rightSteps = [
		{
			stepNumber: 4,
			title:
				translations.seo?.howToUse?.steps?.customizeMapStyle?.title() ||
				"Style Your Memories",
			description:
				translations.seo?.howToUse?.steps?.customizeMapStyle?.description() ||
				"Choose map themes that reflect your travel style. From satellite views to artistic designs, make your Map Moment collection uniquely yours.",
			bgColor: "bg-orange-500",
		},
		{
			stepNumber: 5,
			title:
				translations.seo?.howToUse?.steps?.exportBackupData?.title() ||
				"Preserve Your Moments",
			description:
				translations.seo?.howToUse?.steps?.exportBackupData?.description() ||
				"Keep your precious Map Moment collection safe with backup options. Your travel memories deserve to be preserved forever.",
			bgColor: "bg-cyan-500",
		},
		{
			stepNumber: 6,
			title:
				translations.seo?.howToUse?.steps?.shareToSocial?.title() ||
				"Share Your Story",
			description:
				translations.seo?.howToUse?.steps?.shareToSocial?.description() ||
				"Let the world see your incredible journey! Share your Map Moment cards on Instagram, Twitter, Facebook and inspire others to create their own memory maps.",
			bgColor: "bg-pink-500",
		},
	];

	return (
		<div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900 rounded-2xl p-8 border border-sky-100 dark:border-gray-700">
			<h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">
				{translations.seo?.howToUse?.title() ||
					"How to Create Your Map Moment Collection"}
			</h2>
			<div className="grid md:grid-cols-2 gap-8">
				<div className="space-y-4">
					{leftSteps.map((step) => (
						<StepItem
							key={step.stepNumber}
							stepNumber={step.stepNumber}
							title={step.title}
							description={step.description}
							bgColor={step.bgColor}
						/>
					))}
				</div>
				<div className="space-y-4">
					{rightSteps.map((step) => (
						<StepItem
							key={step.stepNumber}
							stepNumber={step.stepNumber}
							title={step.title}
							description={step.description}
							bgColor={step.bgColor}
						/>
					))}
				</div>
			</div>
		</div>
	);
}
