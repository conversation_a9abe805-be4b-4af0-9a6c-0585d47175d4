---
description: 
globs: 
alwaysApply: true
---
# 前端组件模块化设计原则

## 🎯 核心目标

确保前端页面组件和页面保持整洁，符合高内聚低耦合、模块化、可扩展的设计原则。

## 📏 文件大小限制

### 严格限制
- **每个文件代码不能超过 1000 行**
- 超过 1000 行必须拆分成多个合理的子文件
- 优先考虑功能职责进行拆分

### 拆分建议
- **组件文件**: 300-500 行为理想范围
- **页面文件**: 500-800 行为理想范围  
- **工具函数**: 200-300 行为理想范围
- **类型定义**: 100-200 行为理想范围

## 🏗️ 组件拆分策略

### 1. 按功能职责拆分
```typescript
// ❌ 错误：单个大组件
const DiaryPage = () => {
  // 1000+ 行的巨大组件
  return (
    <div>
      {/* 地图组件 */}
      {/* 编辑器组件 */}
      {/* 侧边栏组件 */}
      {/* 工具栏组件 */}
    </div>
  );
};

// ✅ 正确：按功能拆分
const DiaryPage = () => {
  return (
    <div>
      <DiaryMap />
      <DiaryEditor />
      <DiarySidebar />
      <DiaryToolbar />
    </div>
  );
};
```

### 2. 提取自定义 Hook
```typescript
// ❌ 错误：组件内包含大量状态逻辑
const DiaryEditor = () => {
  const [content, setContent] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  // 50+ 行状态逻辑
  
  return <div>{/* UI */}</div>;
};

// ✅ 正确：提取状态逻辑到自定义 Hook
const useDiaryEditor = () => {
  // 状态逻辑移到独立文件
};

const DiaryEditor = () => {
  const { content, isLoading, error, actions } = useDiaryEditor();
  return <div>{/* 纯 UI */}</div>;
};
```

### 3. 分离工具函数
```typescript
// ❌ 错误：组件内包含复杂工具函数
const DiaryMap = () => {
  const calculateRoute = (points) => {
    // 100+ 行复杂计算
  };
  
  const processGeometry = (geometry) => {
    // 50+ 行几何处理
  };
  
  return <div>{/* UI */}</div>;
};

// ✅ 正确：工具函数独立文件
// utils/mapCalculations.ts
export const calculateRoute = (points) => { /* ... */ };
export const processGeometry = (geometry) => { /* ... */ };

// DiaryMap.tsx
import { calculateRoute, processGeometry } from './utils/mapCalculations';
```

## 📁 推荐目录结构

### 页面级组织
```
app/(saas)/travel-memo/diary/[diaryId]/
├── page.tsx                    # 主页面入口 (< 200 行)
├── components/                 # 页面特定组件
│   ├── DiaryMap/              # 地图组件模块
│   │   ├── index.tsx          # 组件入口
│   │   ├── DiaryMap.tsx       # 主组件
│   │   ├── MapControls.tsx    # 地图控制
│   │   ├── hooks/             # 地图相关 Hook
│   │   └── utils/             # 地图工具函数
│   ├── DiaryEditor/           # 编辑器组件模块
│   │   ├── index.tsx
│   │   ├── DiaryEditor.tsx
│   │   ├── EditorToolbar.tsx
│   │   └── hooks/
│   └── DiarySidebar/          # 侧边栏组件模块
├── hooks/                     # 页面级 Hook
├── utils/                     # 页面级工具函数
└── types/                     # 页面级类型定义
```

### 组件内部结构
```
components/DiaryMap/
├── index.tsx                  # 导出入口
├── DiaryMap.tsx              # 主组件 (< 500 行)
├── MapControls.tsx           # 控制组件
├── MapMarkers.tsx            # 标记组件
├── hooks/
│   ├── useMapStyle.ts        # 地图样式 Hook
│   ├── useMapData.ts         # 地图数据 Hook
│   └── useMapInteraction.ts  # 交互逻辑 Hook
├── utils/
│   ├── mapCalculations.ts    # 计算工具
│   ├── geoUtils.ts          # 地理工具
│   └── mapHelpers.ts        # 通用辅助
└── types/
    ├── mapTypes.ts          # 地图类型
    └── geometryTypes.ts     # 几何类型
```

## 🔧 高内聚低耦合原则

### 1. 高内聚 - 相关功能聚合
```typescript
// ✅ 正确：地图相关功能聚合在一个模块
// components/DiaryMap/
export const DiaryMap = () => { /* 地图渲染 */ };
export const MapControls = () => { /* 地图控制 */ };
export const useMapStyle = () => { /* 地图样式逻辑 */ };
export const mapUtils = { /* 地图工具函数 */ };
```

### 2. 低耦合 - 减少模块间依赖
```typescript
// ❌ 错误：强耦合
const DiaryEditor = ({ mapInstance, sidebarState }) => {
  // 直接依赖其他组件的内部状态
};

// ✅ 正确：通过 props 和 context 解耦
const DiaryEditor = ({ onContentChange, isReadOnly }) => {
  // 只依赖必要的接口
};
```

### 3. 接口隔离
```typescript
// ✅ 接口隔离原则
interface MapProps {
  center: [number, number];
  zoom: number;
  onMapChange: (mapState: MapState) => void;
}

interface EditorProps {
  content: string;
  onChange: (content: string) => void;
  isReadOnly: boolean;
}
```

## 🔄 可扩展性设计

### 1. 插件化架构
```typescript
// components/DiaryEditor/plugins/
export interface EditorPlugin {
  name: string;
  toolbar?: ToolbarPlugin;
  shortcuts?: ShortcutPlugin;
  render?: RenderPlugin;
}

// 扩展新功能时只需添加插件
const diaryEditorPlugins = [
  imagePlugin,
  mapPlugin,
  videoPlugin,
  // 新功能插件可以独立添加
];
```

### 2. 配置驱动
```typescript
// 通过配置扩展功能
const mapConfig = {
  styles: ['streets', 'satellite', 'outdoors'],
  controls: ['zoom', 'style', 'fullscreen'],
  animations: ['floating-particles', 'aurora'],
  // 新配置项可以独立添加
};
```

## 🧩 组件抽象层次

### 1. 原子组件 (50-100 行)
```typescript
// components/ui/Button.tsx
export const Button = ({ variant, size, children, ...props }) => {
  return <button className={cn(buttonVariants({ variant, size }))} {...props}>
    {children}
  </button>;
};
```

### 2. 分子组件 (100-300 行)  
```typescript
// components/TravelPointForm.tsx
export const TravelPointForm = ({ point, onSave, onCancel }) => {
  // 组合多个原子组件
  return (
    <Form>
      <Input />
      <Textarea />
      <Button />
    </Form>
  );
};
```

### 3. 组织组件 (300-500 行)
```typescript
// components/DiaryEditor.tsx
export const DiaryEditor = () => {
  // 组合多个分子组件
  return (
    <div>
      <EditorToolbar />
      <EditorContent />
      <EditorFooter />
    </div>
  );
};
```

### 4. 页面模板 (500-800 行)
```typescript
// app/diary/[diaryId]/page.tsx
export default function DiaryPage() {
  // 组合多个组织组件
  return (
    <Layout>
      <DiaryHeader />
      <DiaryContent />
      <DiaryFooter />
    </Layout>
  );
};
```

## 🚦 拆分检查清单

### 文件大小检查
- [ ] 组件文件 < 500 行
- [ ] 页面文件 < 800 行  
- [ ] Hook 文件 < 300 行
- [ ] 工具文件 < 300 行
- [ ] 类型文件 < 200 行

### 职责单一检查
- [ ] 每个组件只负责一个UI职责
- [ ] 每个Hook只管理一类状态
- [ ] 每个工具函数只解决一个问题
- [ ] 每个类型文件只定义相关类型

### 依赖关系检查
- [ ] 组件间通过props通信
- [ ] 跨层级状态使用Context
- [ ] 避免循环依赖
- [ ] 最小化外部依赖

### 可扩展性检查
- [ ] 新功能可以独立添加
- [ ] 配置可以动态修改
- [ ] 插件可以热插拔
- [ ] API接口保持稳定

## 🎯 实施建议

1. **渐进式重构**: 从最大的文件开始拆分
2. **保持测试**: 拆分时确保测试覆盖
3. **文档同步**: 更新相关文档和注释
4. **性能考虑**: 避免过度拆分影响性能
5. **团队协作**: 拆分前与团队讨论方案

遵循这些原则，确保前端代码库保持整洁、可维护和可扩展。
