"use client";

import { useCallback, useEffect, useRef, useState } from "react";

import { useTravelStatTranslations } from "../../hooks/useTravelStatTranslations";

interface ResizablePanelProps {
	leftPanel: React.ReactNode;
	rightPanel: React.ReactNode;
	defaultLeftWidth?: number; // 百分比，0-100
	minLeftWidth?: number; // 最小宽度百分比
	maxLeftWidth?: number; // 最大宽度百分比
	className?: string;
}

export function ResizablePanel({
	leftPanel,
	rightPanel,
	defaultLeftWidth = 25, // 默认左侧面板占25%
	minLeftWidth = 15, // 最小15%
	maxLeftWidth = 50, // 最大50%
	className = "",
}: ResizablePanelProps) {
	const [leftWidth, setLeftWidth] = useState(defaultLeftWidth);
	const [isDragging, setIsDragging] = useState(false);
	const [dragStart, setDragStart] = useState(0);
	const [startWidth, setStartWidth] = useState(0);
	const [leftHeight, setLeftHeight] = useState(600); // 左侧面板高度
	const [showInitialHint, setShowInitialHint] = useState(true); // 初次提示
	const containerRef = useRef<HTMLDivElement>(null);
	const leftPanelRef = useRef<HTMLDivElement>(null);
	const rightPanelRef = useRef<HTMLDivElement>(null);

	// 获取翻译
	const travelStatT = useTravelStatTranslations();

	const handleMouseDown = useCallback(
		(e: React.MouseEvent) => {
			e.preventDefault();
			setIsDragging(true);
			setDragStart(e.clientX);
			setStartWidth(leftWidth);
			setShowInitialHint(false); // 用户开始拖拽后隐藏初次提示

			// 添加全局事件监听
			document.body.style.cursor = "col-resize";
			document.body.style.userSelect = "none";
		},
		[leftWidth],
	);

	const handleMouseMove = useCallback(
		(e: MouseEvent) => {
			if (!isDragging || !containerRef.current) return;

			const containerRect = containerRef.current.getBoundingClientRect();
			const deltaX = e.clientX - dragStart;
			const deltaPercent = (deltaX / containerRect.width) * 100;
			const newWidth = Math.min(
				maxLeftWidth,
				Math.max(minLeftWidth, startWidth + deltaPercent),
			);

			setLeftWidth(newWidth);
		},
		[isDragging, dragStart, startWidth, minLeftWidth, maxLeftWidth],
	);

	const handleMouseUp = useCallback(() => {
		setIsDragging(false);
		document.body.style.cursor = "";
		document.body.style.userSelect = "";
	}, []);

	useEffect(() => {
		if (isDragging) {
			document.addEventListener("mousemove", handleMouseMove);
			document.addEventListener("mouseup", handleMouseUp);

			return () => {
				document.removeEventListener("mousemove", handleMouseMove);
				document.removeEventListener("mouseup", handleMouseUp);
			};
		}
	}, [isDragging, handleMouseMove, handleMouseUp]);

	// 监听左侧面板高度变化，同步到右侧面板
	useEffect(() => {
		if (!leftPanelRef.current) return;

		const resizeObserver = new ResizeObserver((entries) => {
			for (const entry of entries) {
				const { height } = entry.contentRect;
				const newHeight = Math.max(600, height); // 最小高度600px
				setLeftHeight(newHeight);
			}
		});

		resizeObserver.observe(leftPanelRef.current);

		return () => {
			resizeObserver.disconnect();
		};
	}, []);

	// 初次提示动画 - 5秒后自动隐藏
	useEffect(() => {
		const timer = setTimeout(() => {
			setShowInitialHint(false);
		}, 5000);

		return () => clearTimeout(timer);
	}, []);

	const rightWidth = 100 - leftWidth;

	return (
		<div ref={containerRef} className={`flex w-full ${className}`}>
			{/* 左侧面板 */}
			<div
				ref={leftPanelRef}
				style={{ width: `${leftWidth}%` }}
				className="flex-shrink-0 transition-none"
			>
				{leftPanel}
			</div>

			{/* 分隔条 */}
			<div
				className="group relative flex-shrink-0 w-3 flex items-stretch justify-center cursor-col-resize select-none z-30"
				style={{ height: `${leftHeight}px` }}
				onMouseDown={handleMouseDown}
				onMouseEnter={() => setShowInitialHint(false)} // 鼠标悬停时隐藏初次提示
			>
				{/* 初次加载的闪烁提示 */}
				{showInitialHint && (
					<div className="absolute inset-0 pointer-events-none">
						<div className="absolute inset-y-0 -inset-x-1 bg-gradient-to-r from-blue-400/20 via-purple-400/30 to-pink-400/20 rounded-lg animate-pulse" />
					</div>
				)}

				{/* 背景区域 - 可拖拽区域更大一些 */}
				<div className="absolute inset-y-0 -inset-x-2 flex items-center justify-center">
					{/* 分隔条主体 - 增加默认可见性 */}
					<div
						className={`relative w-1 h-full transition-all duration-200 ${
							isDragging
								? "bg-gradient-to-b from-blue-400 via-purple-400 to-pink-400 shadow-lg shadow-purple-400/25"
								: "bg-gradient-to-b from-blue-200/60 via-slate-300/70 to-blue-200/60 dark:from-blue-800/50 dark:via-slate-600/60 dark:to-blue-800/50 group-hover:from-blue-400/90 group-hover:via-purple-400/90 group-hover:to-pink-400/90"
						} rounded-full shadow-sm`}
					>
						{/* 装饰性光晕效果 */}
						<div
							className={`absolute inset-0 rounded-full transition-all duration-200 ${
								isDragging
									? "bg-gradient-to-b from-blue-400/50 via-purple-400/50 to-pink-400/50 blur-sm scale-150"
									: showInitialHint
										? "bg-gradient-to-b from-blue-400/30 via-purple-400/30 to-pink-400/30 blur-sm scale-125 animate-pulse"
										: "bg-gradient-to-b from-blue-300/25 via-slate-300/25 to-blue-300/25 group-hover:from-blue-400/40 group-hover:via-purple-400/40 group-hover:to-pink-400/40 group-hover:blur-sm group-hover:scale-125"
							}`}
						/>
					</div>

					{/* 拖拽指示器 - 增加默认可见性 */}
					<div
						className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 transition-all duration-200 ${
							isDragging
								? "opacity-100 scale-110"
								: showInitialHint
									? "opacity-80 scale-105 animate-bounce"
									: "opacity-60 group-hover:opacity-100 group-hover:scale-105"
						}`}
					>
						{/* <div className="flex flex-col items-center justify-center space-y-0.5 p-1.5 rounded-lg bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm shadow-lg border border-white/40 dark:border-gray-700/60">
							<GripVertical
								className={`w-1.5 h-5 transition-colors duration-200 ${
									isDragging
										? "text-purple-600 dark:text-purple-400"
										: showInitialHint
											? "text-blue-500 dark:text-blue-400"
											: "text-slate-500 dark:text-slate-400 group-hover:text-blue-500 dark:group-hover:text-blue-400"
								}`}
							/>
						</div> */}
					</div>

					{/* 悬停时的额外提示文字 */}
					<div
						className={`absolute -top-8 left-1/2 transform -translate-x-1/2 transition-all duration-200 pointer-events-none ${
							isDragging
								? "opacity-100 translate-y-0"
								: showInitialHint
									? "opacity-100 translate-y-0"
									: "opacity-0 translate-y-2 group-hover:opacity-100 group-hover:translate-y-0"
						}`}
					>
						<div className="px-2 py-1 bg-black/80 text-white text-xs rounded whitespace-nowrap">
							{isDragging
								? travelStatT.resizablePanel.draggingHint()
								: travelStatT.resizablePanel.dragHint()}
							<div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-black/80" />
						</div>
					</div>
				</div>

				{/* 拖拽时的全屏遮罩指示器 */}
				{isDragging && (
					<div className="fixed inset-0 z-40 cursor-col-resize pointer-events-none">
						<div className="absolute inset-0 bg-blue-500/5 dark:bg-blue-400/5" />
					</div>
				)}
			</div>

			{/* 右侧面板 */}
			<div
				ref={rightPanelRef}
				style={{
					width: `${rightWidth}%`,
					height: `${leftHeight}px`,
				}}
				className="flex-1 min-w-0 transition-none"
			>
				{rightPanel}
			</div>
		</div>
	);
}
