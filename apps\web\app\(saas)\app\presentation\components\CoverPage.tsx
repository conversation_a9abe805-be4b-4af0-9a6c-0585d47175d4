"use client";

import { ChevronDown } from "lucide-react";
import { useTranslations } from "next-intl";
import { useEffect, useRef } from "react";
import type { TravelDiary } from "../types";
import { formatDateRange } from "../utils/date-utils";
import { GlobalFontStyles } from "./common/GlobalFontStyles";

interface CoverPageProps {
	diary: TravelDiary;
	stats: {
		totalLocations: number;
		totalImages: number;
		startDate: Date | null;
		endDate: Date | null;
	};
	onContinue: () => void;
}

export function CoverPage({ diary, stats, onContinue }: CoverPageProps) {
	const t = useTranslations("travelMemo.coverPage");
	// 默认封面图
	const defaultCoverImage =
		"https://images.unsplash.com/photo-1488085061387-422e29b40080?q=80&w=1000";
	const coverImage = diary.coverImage || defaultCoverImage;
	const containerRef = useRef<HTMLDivElement>(null);

	// 格式化日期范围
	const dateRangeText =
		stats.startDate && stats.endDate
			? formatDateRange(stats.startDate, stats.endDate)
			: "";

	// 添加触摸滑动支持
	useEffect(() => {
		if (!containerRef.current) return;

		let startY = 0;
		let isScrolling = false;

		const handleTouchStart = (e: TouchEvent) => {
			startY = e.touches[0].clientY;
		};

		const handleTouchMove = (e: TouchEvent) => {
			if (isScrolling) return;

			const currentY = e.touches[0].clientY;
			const diff = startY - currentY;

			// 向下滑动超过阈值，切换页面
			if (diff > 50) {
				isScrolling = true;
				onContinue();
			}
		};

		const container = containerRef.current;
		container.addEventListener("touchstart", handleTouchStart, {
			passive: true,
		});
		container.addEventListener("touchmove", handleTouchMove, {
			passive: true,
		});

		return () => {
			container.removeEventListener("touchstart", handleTouchStart);
			container.removeEventListener("touchmove", handleTouchMove);
		};
	}, [onContinue]);

	return (
		<div
			ref={containerRef}
			className="relative h-full w-full flex flex-col items-center justify-center text-white travel-memo-content"
		>
			{/* 引入全局字体样式 */}
			<GlobalFontStyles />

			{/* 背景图 */}
			<div className="absolute inset-0 z-0">
				<div
					className="w-full h-full bg-cover bg-center"
					style={{ backgroundImage: `url(${coverImage})` }}
				/>
				<div className="absolute inset-0 bg-black/60" />
			</div>

			{/* 内容 */}
			<div className="relative z-10 max-w-3xl mx-auto px-4 text-center">
				<h1 className="text-4xl md:text-6xl font-bold mb-4 leading-tight travel-memo-title">
					{diary.title}
				</h1>

				<p className="text-xl md:text-2xl text-white/90 mb-6 travel-memo-subtitle">
					{diary.subtitle}
				</p>

				<div className="text-lg md:text-xl text-white/80 mb-12 travel-memo-text">
					{dateRangeText}
				</div>

				<div className="flex justify-center gap-10 mb-10">
					<div className="text-center">
						<div className="text-3xl font-bold travel-memo-number">
							{stats.totalLocations}
						</div>
						<div className="text-sm text-white/70 travel-memo-label">
							{t("statsLabelLocations")}
						</div>
					</div>

					<div className="text-center">
						<div className="text-3xl font-bold travel-memo-number">
							{stats.totalImages}
						</div>
						<div className="text-sm text-white/70 travel-memo-label">
							{t("statsLabelPhotos")}
						</div>
					</div>
				</div>
			</div>

			{/* 底部引导箭头和提示文字 */}
			<button
				type="button"
				className="absolute bottom-8 left-0 right-0 flex flex-col items-center cursor-pointer bg-transparent border-none focus:outline-none focus:ring-2 focus:ring-white/30 rounded-full"
				onClick={onContinue}
				aria-label={t("startButtonAriaLabel")}
			>
				<ChevronDown className="h-10 w-10 text-white/70 animate-bounce" />
				<p className="text-white/70 text-sm mt-2 travel-memo-text">
					{t("startJourneyPrompt")}
				</p>
			</button>
		</div>
	);
}
