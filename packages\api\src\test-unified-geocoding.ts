/**
 * 测试统一地理编码服务
 * 验证多提供商负载均衡和故障转移功能
 */

import { resolve } from "node:path";
import { config } from "dotenv";
import {
	type GeocodingConfig,
	UnifiedGeocodingService,
	createChinaOptimizedConfig,
	createGeocodingService,
} from "./services/geocoding";

// 加载环境变量
const envFiles = [".env.local", ".env"];
for (const envFile of envFiles) {
	try {
		const envPath = resolve(process.cwd(), "../../", envFile);
		config({ path: envPath });
		console.log(`✅ 已加载环境变量文件: ${envFile}`);
		break;
	} catch (error) {
		// 继续尝试下一个文件
	}
}

async function testUnifiedGeocodingService() {
	console.log("🔍 测试统一地理编码服务...\n");

	// 收集可用的API密钥
	const apiKeys = {
		google: process.env.GOOGLE_MAPS_API_KEY,
		bing: process.env.BING_MAPS_API_KEY,
		mapbox: process.env.MAPBOX_API_KEY,
		baidu: process.env.BAIDU_MAPS_API_KEY,
		amap: process.env.AMAP_API_KEY,
	};

	const availableKeys = Object.entries(apiKeys)
		.filter(([_, value]) => value)
		.map(([key, _]) => key);

	console.log(`📋 可用的API密钥: ${availableKeys.join(", ")}`);

	if (availableKeys.length === 0) {
		console.error("❌ 未找到任何API密钥，请配置至少一个地理编码服务");
		return;
	}

	// 创建服务配置
	const filteredApiKeys = Object.fromEntries(
		Object.entries(apiKeys).filter(([_, value]) => value),
	) as any;

	// 测试1: 使用便捷工厂函数
	console.log("\n🧪 测试1: 使用便捷工厂函数");
	const simpleConfig = createChinaOptimizedConfig(filteredApiKeys);
	const simpleService = createGeocodingService(simpleConfig);
	await testBasicGeocoding(simpleService, "便捷服务");

	// 测试2: 使用中国优化配置
	console.log("\n🧪 测试2: 使用中国优化配置");
	const chinaConfig = createChinaOptimizedConfig(filteredApiKeys);
	const chinaService = new UnifiedGeocodingService(chinaConfig);
	await testChineseAddresses(chinaService);

	// 测试3: 自定义配置测试
	console.log("\n🧪 测试3: 自定义配置测试");
	await testCustomConfiguration(filteredApiKeys);

	// 测试4: 负载均衡策略测试
	console.log("\n🧪 测试4: 负载均衡策略测试");
	await testLoadBalancingStrategies(filteredApiKeys);

	// 测试5: 批量地理编码测试
	console.log("\n🧪 测试5: 批量地理编码测试");
	await testBatchGeocoding(chinaService);

	// 测试6: 统计信息测试
	console.log("\n🧪 测试6: 统计信息测试");
	await testStatistics(chinaService);

	console.log("\n🎉 所有测试完成!");
}

async function testBasicGeocoding(service: any, serviceName: string) {
	console.log(`\n📍 ${serviceName} - 基础地理编码测试:`);

	const testAddresses = [
		"北京天安门广场",
		"上海外滩",
		"New York Times Square",
	];

	for (const address of testAddresses) {
		try {
			console.log(`\n🔍 测试地址: ${address}`);
			const result = await service.geocode(address);

			if (result) {
				console.log(
					`✅ 成功: ${result.formattedAddress || result.address}`,
				);
				console.log(`📍 坐标: ${result.latitude}, ${result.longitude}`);
				console.log(`🎯 置信度: ${result.confidence}`);
				console.log(`🏢 提供商: ${result.provider || "未知"}`);
			} else {
				console.log("❌ 失败: 未找到结果");
			}
		} catch (error) {
			console.log(
				`❌ 错误: ${error instanceof Error ? error.message : String(error)}`,
			);
		}
	}
}

async function testChineseAddresses(service: UnifiedGeocodingService) {
	console.log("\n🇨🇳 中文地址专项测试:");

	const chineseAddresses = [
		"北京市朝阳区三里屯",
		"上海市浦东新区陆家嘴",
		"广州市天河区珠江新城",
		"深圳市南山区科技园",
		"杭州市西湖区西湖",
	];

	for (const address of chineseAddresses) {
		try {
			console.log(`\n🔍 测试中文地址: ${address}`);

			// 使用智能地理编码
			const result = await service.smartGeocode(address, {
				language: "zh-CN",
				region: "CN",
			});

			if (result) {
				console.log(`✅ 成功: ${result.formattedAddress}`);
				console.log(`📍 坐标: ${result.latitude}, ${result.longitude}`);
				console.log(`🎯 置信度: ${result.confidence}`);
				console.log(`🏢 提供商: ${result.provider}`);
				console.log(`⏱️ 响应时间: ${result.responseTime}ms`);

				// 显示地址组件
				const components = result.addressComponents;
				if (components.country)
					console.log(`🌍 国家: ${components.country}`);
				if (components.province)
					console.log(`🏛️ 省份: ${components.province}`);
				if (components.city) console.log(`🏙️ 城市: ${components.city}`);
				if (components.district)
					console.log(`🏘️ 区县: ${components.district}`);
			} else {
				console.log("❌ 失败: 未找到结果");
			}
		} catch (error) {
			console.log(
				`❌ 错误: ${error instanceof Error ? error.message : String(error)}`,
			);
		}
	}
}

async function testCustomConfiguration(apiKeys: any) {
	console.log("\n⚙️ 自定义配置测试:");

	// 创建自定义配置
	const providers: GeocodingConfig[] = [];

	// 添加可用的提供商
	if (apiKeys.google) {
		providers.push({
			provider: "google",
			apiKey: apiKeys.google,
			weight: 2,
			priority: 1,
			enabled: true,
		});
	}

	if (apiKeys.amap) {
		providers.push({
			provider: "amap",
			apiKey: apiKeys.amap,
			weight: 3,
			priority: 2,
			enabled: true,
		});
	}

	if (providers.length === 0) {
		console.log("⚠️ 跳过自定义配置测试 - 没有可用的提供商");
		return;
	}

	const customService = new UnifiedGeocodingService({
		providers,
		strategy: "weighted",
		enableCache: true,
		cacheTTL: 1800, // 30分钟
		defaultOptions: {
			language: "zh-CN",
		},
	});

	await testBasicGeocoding(customService, "自定义配置服务");
}

async function testLoadBalancingStrategies(apiKeys: any) {
	console.log("\n⚖️ 负载均衡策略测试:");

	const providers: GeocodingConfig[] = [];

	// 只添加前两个可用的提供商进行测试
	const availableProviders = Object.entries(apiKeys)
		.filter(([_, value]) => value)
		.slice(0, 2);

	for (const [providerName, apiKey] of availableProviders) {
		providers.push({
			provider: providerName as any,
			apiKey: apiKey as string,
			weight: Math.random() * 3 + 1, // 随机权重
			priority: Math.floor(Math.random() * 5) + 1, // 随机优先级
			enabled: true,
		});
	}

	if (providers.length < 2) {
		console.log("⚠️ 跳过负载均衡测试 - 需要至少2个提供商");
		return;
	}

	const strategies = [
		"random",
		"round_robin",
		"weighted",
		"priority",
		"smart",
	] as const;

	for (const strategy of strategies) {
		console.log(`\n🎯 测试策略: ${strategy}`);

		const service = new UnifiedGeocodingService({
			providers,
			strategy,
			enableCache: false, // 禁用缓存以测试负载均衡
		});

		// 测试多次请求观察负载分布
		const testAddress = "北京市";
		for (let i = 0; i < 3; i++) {
			try {
				const result = await service.geocode(testAddress);
				if (result) {
					console.log(
						`  请求${i + 1}: ${result.provider} (${result.responseTime}ms)`,
					);
				}
			} catch (error) {
				console.log(`  请求${i + 1}: 失败`);
			}
		}
	}
}

async function testBatchGeocoding(service: UnifiedGeocodingService) {
	console.log("\n📦 批量地理编码测试:");

	const addresses = ["北京市", "上海市", "广州市", "深圳市", "杭州市"];

	try {
		console.log(`🔍 批量处理 ${addresses.length} 个地址...`);
		const results = await service.batchGeocode(addresses);

		console.log("\n📊 批量处理结果:");
		console.log(`总数: ${results.length}`);
		console.log(`成功: ${results.filter((r) => r.result !== null).length}`);
		console.log(`失败: ${results.filter((r) => r.result === null).length}`);

		for (const result of results) {
			if (result.result) {
				console.log(
					`✅ ${result.address} -> ${result.result.formattedAddress} (${result.provider})`,
				);
			} else {
				console.log(
					`❌ ${result.address} -> 失败 ${result.error ? `(${result.error})` : ""}`,
				);
			}
		}
	} catch (error) {
		console.log(
			`❌ 批量处理错误: ${error instanceof Error ? error.message : String(error)}`,
		);
	}
}

async function testStatistics(service: UnifiedGeocodingService) {
	console.log("\n📊 统计信息测试:");

	try {
		const stats = service.getStats();

		console.log("\n📈 总体统计:");
		console.log(`总请求数: ${stats.totalRequests}`);
		console.log(`成功请求数: ${stats.successfulRequests}`);
		console.log(`失败请求数: ${stats.failedRequests}`);
		console.log(`平均响应时间: ${Math.round(stats.averageResponseTime)}ms`);
		console.log(
			`成功率: ${stats.totalRequests > 0 ? ((stats.successfulRequests / stats.totalRequests) * 100).toFixed(2) : 0}%`,
		);

		console.log("\n🏢 提供商统计:");
		for (const [provider, providerStats] of Object.entries(
			stats.providerStats,
		)) {
			console.log(`  ${provider}:`);
			console.log(`    请求数: ${providerStats.requests}`);
			console.log(`    成功数: ${providerStats.successes}`);
			console.log(`    失败数: ${providerStats.failures}`);
			console.log(
				`    平均响应时间: ${Math.round(providerStats.averageResponseTime)}ms`,
			);
			console.log(
				`    最后使用: ${providerStats.lastUsed.toLocaleString()}`,
			);
		}
	} catch (error) {
		console.log(
			`❌ 获取统计信息错误: ${error instanceof Error ? error.message : String(error)}`,
		);
	}
}

// 运行测试
testUnifiedGeocodingService()
	.then(() => {
		console.log("\n🎉 测试完成!");
		process.exit(0);
	})
	.catch((error) => {
		console.error("❌ 测试失败:", error);
		process.exit(1);
	});
