"use client";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@ui/components/card";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Camera, Palette, Settings } from "lucide-react";
import React from "react";

import { useTravelStatTranslations } from "../../hooks/useTravelStatTranslations";
import { TemplateSelector } from "./components/TemplateSelector";
import type {
	CardCustomization,
	CardTemplate,
	SocialPlatform,
} from "./types/cardTypes";

interface CardConfigPanelProps {
	selectedPlatform: SocialPlatform;
	selectedTemplate: CardTemplate;
	customization: CardCustomization;
	onPlatformChange: (platform: SocialPlatform) => void;
	onTemplateSelect: (template: CardTemplate) => void;
	onCustomizationChange: (customization: CardCustomization) => void;
}

export function CardConfigPanel({
	selectedPlatform,
	selectedTemplate,
	customization,
	onPlatformChange,
	onTemplateSelect,
	onCustomizationChange,
}: CardConfigPanelProps) {
	const travelStatT = useTravelStatTranslations();

	// 平台配置
	const platforms: {
		value: SocialPlatform;
		label: string;
		icon: string;
		dimensions: { width: number; height: number };
		aspectRatio: string;
		description: string;
	}[] = [
		{
			value: "instagram",
			label: travelStatT.cardGenerator.platforms.instagram(),
			icon: "📷",
			dimensions: { width: 1080, height: 1080 },
			aspectRatio: "1:1",
			description: travelStatT.cardGenerator.platforms.instagram(),
		},
		{
			value: "wechat",
			label: travelStatT.cardGenerator.platforms.wechat(),
			icon: "💬",
			dimensions: { width: 1200, height: 900 },
			aspectRatio: "4:3",
			description: travelStatT.cardGenerator.platforms.wechat(),
		},
		{
			value: "weibo",
			label: travelStatT.cardGenerator.platforms.weibo(),
			icon: "🔥",
			dimensions: { width: 1080, height: 1350 },
			aspectRatio: "4:5",
			description: travelStatT.cardGenerator.platforms.weibo(),
		},
		{
			value: "twitter",
			label: travelStatT.cardGenerator.platforms.twitter(),
			icon: "🐦",
			dimensions: { width: 1200, height: 675 },
			aspectRatio: "16:9",
			description: travelStatT.cardGenerator.platforms.twitter(),
		},
		{
			value: "facebook",
			label: travelStatT.cardGenerator.platforms.facebook(),
			icon: "👥",
			dimensions: { width: 1200, height: 630 },
			aspectRatio: "1.91:1",
			description: travelStatT.cardGenerator.platforms.facebook(),
		},
	];

	// 处理自定义标题变化
	const handleCustomTitleChange = (value: string) => {
		onCustomizationChange({
			...customization,
			content: {
				...customization.content,
				customTitle: value,
			},
		});
	};

	// 处理自定义页脚变化
	const handleCustomFooterChange = (value: string) => {
		onCustomizationChange({
			...customization,
			content: {
				...customization.content,
				customFooter: value,
			},
		});
	};

	// 处理自定义日期文本变化
	const handleCustomDateChange = (value: string) => {
		onCustomizationChange({
			...customization,
			content: {
				...customization.content,
				customDate: value,
			},
		});
	};

	return (
		<div className="flex flex-col min-h-full space-y-4">
			{/* 平台选择 */}
			<Card className="bg-white/90 backdrop-blur-sm border-purple-200 flex-shrink-0">
				<CardHeader className="px-4 py-3">
					<CardTitle className="text-lg font-medium flex items-center gap-2">
						<Camera className="w-5 h-5 text-purple-500" />
						{travelStatT.cardGenerator.platforms.title()}
					</CardTitle>
				</CardHeader>
				<CardContent className="px-4 pb-4">
					<Select
						value={selectedPlatform}
						onValueChange={(value) =>
							onPlatformChange(value as SocialPlatform)
						}
					>
						<SelectTrigger className="w-full">
							<SelectValue
								placeholder={travelStatT.cardGenerator.platforms.title()}
							/>
						</SelectTrigger>
						<SelectContent>
							{platforms.map((platform) => (
								<SelectItem
									key={platform.value}
									value={platform.value}
								>
									<div className="flex items-center gap-2">
										<span>{platform.icon}</span>
										<span className="font-medium">
											{platform.label}
										</span>
										<span className="text-xs text-gray-500">
											{platform.dimensions.width} ×{" "}
											{platform.dimensions.height} ·{" "}
											{platform.aspectRatio}
										</span>
									</div>
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</CardContent>
			</Card>

			{/* 模板选择 */}
			<Card className="bg-white/90 backdrop-blur-sm border-purple-200 flex-shrink-0">
				<CardHeader className="px-4 py-3">
					<CardTitle className="text-lg font-medium flex items-center gap-2">
						<Palette className="w-5 h-5 text-purple-500" />
						{travelStatT.cardGenerator.templates.title()}
					</CardTitle>
				</CardHeader>
				<CardContent className="px-4 pb-4">
					<TemplateSelector
						selectedTemplate={selectedTemplate}
						selectedPlatform={selectedPlatform}
						onTemplateSelect={onTemplateSelect}
					/>
				</CardContent>
			</Card>

			{/* 内容设置 - 合并内容自定义和显示选项 */}
			<Card className="bg-white/90 backdrop-blur-sm border-purple-200 flex-1 flex flex-col">
				<CardHeader className="px-4 py-3">
					<CardTitle className="text-lg font-medium flex items-center gap-2">
						<Settings className="w-5 h-5 text-purple-500" />
						{travelStatT.cardGenerator.customization.title()}
					</CardTitle>
				</CardHeader>
				<CardContent className="px-4 pb-4 space-y-4 flex-1 flex flex-col">
					{/* 卡片标题 */}
					<div className="space-y-2">
						<label
							htmlFor="customTitle"
							className="text-sm font-medium text-gray-700"
						>
							{travelStatT.cardGenerator.customization.cardTitle()}
						</label>
						<input
							id="customTitle"
							type="text"
							value={customization.content?.customTitle || ""}
							onChange={(e) =>
								handleCustomTitleChange(e.target.value)
							}
							className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
							placeholder={travelStatT.cardGenerator.customization.cardTitlePlaceholder()}
						/>
					</div>

					{/* 页脚文字 */}
					<div className="space-y-2">
						<label
							htmlFor="customFooter"
							className="text-sm font-medium text-gray-700"
						>
							{travelStatT.cardGenerator.customization.footer()}
						</label>
						<input
							id="customFooter"
							type="text"
							value={customization.content?.customFooter || ""}
							onChange={(e) =>
								handleCustomFooterChange(e.target.value)
							}
							className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
							placeholder={travelStatT.cardGenerator.customization.footerPlaceholder()}
						/>
					</div>

					{/* 分隔线 */}
					<div className="border-t border-gray-200 my-4" />

					{/* 自定义日期文本 */}
					<div className="space-y-2">
						<label
							htmlFor="customDate"
							className="text-sm font-medium text-gray-700"
						>
							{travelStatT.cardGenerator.customization.customDate()}
						</label>
						<input
							id="customDate"
							type="text"
							value={customization.content?.customDate || ""}
							onChange={(e) =>
								handleCustomDateChange(e.target.value)
							}
							className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
							placeholder={travelStatT.cardGenerator.customization.customDatePlaceholder()}
						/>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
