// 定义 AI 提供商接口，确保所有提供商实现同样的方法

export interface AIProvider {
	name: string;
	supportedFeatures: {
		text: boolean;
		image: boolean;
		imageAnalysis: boolean;
		audio: boolean;
	};

	generateText(options: {
		systemPrompt?: string;
		userPrompt: string;
		maxTokens?: number;
		temperature?: number;
	}): Promise<string>;

	generateImage?(prompt: string, options?: any): Promise<string>;
	transcribeAudio?(audioData: Blob | Buffer, options?: any): Promise<string>;
	analyzeImage?(
		imageUrl: string,
		prompt: string,
		options?: any,
	): Promise<string>;
}

// 支持的 AI 提供商枚举
export enum AIProviderType {
	OPENAI = "openai",
	GEMINI = "gemini",
	VOLCENGINE = "volcengine",
}

// 创建 AI 模型配置接口
export interface AIModelConfig {
	provider: AIProviderType;
	model: string;
	apiKey?: string;
	apiEndpoint?: string;
	options?: Record<string, any>;
}
