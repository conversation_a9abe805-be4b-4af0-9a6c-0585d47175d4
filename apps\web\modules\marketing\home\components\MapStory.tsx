"use client";

import { Button } from "@ui/components/button";
import Link from "next/link";
import { useEffect, useState } from "react";

export function MapStory() {
	const [activePin, setActivePin] = useState(0);
	const [isVisible, setIsVisible] = useState(false);

	const travelPins = [
		{
			name: "巴黎埃菲尔铁塔",
			story: "在塞纳河畔看着铁塔灯火辉煌，那一刻仿佛整个世界都安静了下来...",
			position: { x: "35%", y: "25%" },
			color: "bg-rose-400",
		},
		{
			name: "京都清水寺",
			story: "穿着和服走过石板路，听着木屐敲击地面的声音，时光仿佛倒流百年...",
			position: { x: "65%", y: "40%" },
			color: "bg-pink-400",
		},
		{
			name: "托斯卡纳乡村",
			story: "金黄的麦田随风摇摆，远山如黛，这就是诗人笔下的田园诗篇...",
			position: { x: "20%", y: "55%" },
			color: "bg-amber-400",
		},
		{
			name: "冰岛蓝湖",
			story: "在地热温泉中仰望极光，那绿色的光芒在天空中舞蹈，如同梦境一般...",
			position: { x: "80%", y: "20%" },
			color: "bg-cyan-400",
		},
	];

	useEffect(() => {
		const timer = setTimeout(() => setIsVisible(true), 300);
		return () => clearTimeout(timer);
	}, []);

	useEffect(() => {
		const interval = setInterval(() => {
			setActivePin((prev) => (prev + 1) % travelPins.length);
		}, 4000);
		return () => clearInterval(interval);
	}, [travelPins.length]);

	return (
		<section
			id="story"
			className="py-20 bg-gradient-to-br from-indigo-50 via-white to-purple-50 dark:from-slate-800 dark:via-slate-900 dark:to-indigo-900"
		>
			<div className="container mx-auto px-4">
				<div className="grid lg:grid-cols-2 gap-16 items-center">
					{/* 左侧：文本内容 */}
					<div className="space-y-8">
						<div
							className={`transition-all duration-1000 ${isVisible ? "opacity-100 transform translate-y-0" : "opacity-0 transform translate-y-8"}`}
						>
							<h2 className="text-4xl md:text-5xl font-bold text-gray-800 dark:text-white mb-6 leading-tight">
								在地图上
								<span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
									绘制你的足迹
								</span>
							</h2>
							<p className="text-xl text-gray-600 dark:text-gray-300 leading-relaxed mb-8">
								每一个地标都承载着故事，每一条路线都记录着成长。
								用智能地图记录你走过的每一步，让足迹成为最美的回忆线条。
							</p>
						</div>

						{/* 故事卡片 */}
						<div
							className={`transition-all duration-1000 delay-300 ${isVisible ? "opacity-100 transform translate-y-0" : "opacity-0 transform translate-y-8"}`}
						>
							<div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-white/50 dark:border-slate-700/50">
								<div className="flex items-center gap-4 mb-4">
									<div
										className={`w-4 h-4 rounded-full ${travelPins[activePin].color} animate-pulse`}
									/>
									<h3 className="text-2xl font-bold text-gray-800 dark:text-white">
										{travelPins[activePin].name}
									</h3>
								</div>
								<p className="text-gray-600 dark:text-gray-300 leading-relaxed text-lg italic">
									"{travelPins[activePin].story}"
								</p>
							</div>
						</div>

						{/* 功能特色 */}
						<div
							className={`transition-all duration-1000 delay-500 ${isVisible ? "opacity-100 transform translate-y-0" : "opacity-0 transform translate-y-8"}`}
						>
							<div className="grid sm:grid-cols-2 gap-6">
								<div className="flex items-start gap-4">
									<div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-xl flex items-center justify-center text-white text-xl shadow-lg">
										🎯
									</div>
									<div>
										<h4 className="font-bold text-gray-800 dark:text-white mb-2">
											智能定位
										</h4>
										<p className="text-gray-600 dark:text-gray-300 text-sm">
											精准记录每个位置
										</p>
									</div>
								</div>
								<div className="flex items-start gap-4">
									<div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-xl flex items-center justify-center text-white text-xl shadow-lg">
										🗺️
									</div>
									<div>
										<h4 className="font-bold text-gray-800 dark:text-white mb-2">
											路线回放
										</h4>
										<p className="text-gray-600 dark:text-gray-300 text-sm">
											重温旅行的每一步
										</p>
									</div>
								</div>
							</div>
						</div>

						{/* 行动按钮 */}
						<div
							className={`transition-all duration-1000 delay-700 ${isVisible ? "opacity-100 transform translate-y-0" : "opacity-0 transform translate-y-8"}`}
						>
							<Button
								size="lg"
								className="px-8 py-4 text-lg bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
							>
								<Link
									href="/auth/login"
									className="flex items-center gap-2"
								>
									<span>开始标记足迹</span>
									<svg
										className="w-5 h-5"
										fill="none"
										stroke="currentColor"
										viewBox="0 0 24 24"
									>
										<title>地图图标</title>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m-6 3l6-3"
										/>
									</svg>
								</Link>
							</Button>
						</div>
					</div>

					{/* 右侧：交互式地图 */}
					<div
						className={`transition-all duration-1000 delay-200 ${isVisible ? "opacity-100 transform translate-x-0" : "opacity-0 transform translate-x-8"}`}
					>
						<div className="relative aspect-square max-w-lg mx-auto">
							{/* 地图背景 */}
							<div className="absolute inset-0 bg-gradient-to-br from-blue-100 via-indigo-50 to-purple-100 dark:from-slate-700 dark:via-slate-600 dark:to-indigo-800 rounded-3xl shadow-2xl overflow-hidden">
								{/* 装饰性地图元素 */}
								<div className="absolute inset-0">
									{/* 大陆轮廓 */}
									<svg
										className="w-full h-full opacity-20"
										viewBox="0 0 400 400"
									>
										<title>世界地图轮廓</title>
										<path
											d="M50 150 Q100 120 150 140 Q200 130 250 150 Q300 140 350 160 L350 250 Q300 270 250 260 Q200 280 150 270 Q100 260 50 250 Z"
											fill="currentColor"
											className="text-blue-400 dark:text-slate-500"
										/>
										<path
											d="M100 80 Q150 70 200 85 Q250 75 300 90 L300 130 Q250 140 200 135 Q150 145 100 130 Z"
											fill="currentColor"
											className="text-indigo-400 dark:text-slate-400"
										/>
									</svg>
								</div>

								{/* 旅行足迹点 */}
								{travelPins.map((pin, index) => (
									<div
										key={index}
										className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
										style={{
											left: pin.position.x,
											top: pin.position.y,
										}}
										onClick={() => setActivePin(index)}
										onKeyDown={(e) => {
											if (
												e.key === "Enter" ||
												e.key === " "
											) {
												setActivePin(index);
											}
										}}
										role="button"
										tabIndex={0}
									>
										{/* 脉冲动画 */}
										<div
											className={`absolute inset-0 ${pin.color} rounded-full animate-ping ${index === activePin ? "opacity-75" : "opacity-0"}`}
										/>

										{/* 主要标记点 */}
										<div
											className={`relative w-6 h-6 ${pin.color} rounded-full shadow-lg transform transition-all duration-300 ${
												index === activePin
													? "scale-150 shadow-xl"
													: "scale-100 hover:scale-125"
											}`}
										>
											<div className="absolute inset-1 bg-white rounded-full" />
										</div>

										{/* 标签 */}
										{index === activePin && (
											<div className="absolute top-8 left-1/2 transform -translate-x-1/2 bg-white dark:bg-slate-800 px-3 py-1 rounded-lg shadow-lg whitespace-nowrap border border-gray-200 dark:border-slate-700">
												<p className="text-sm font-medium text-gray-800 dark:text-white">
													{pin.name}
												</p>
											</div>
										)}
									</div>
								))}

								{/* 连接线动画 */}
								<svg className="absolute inset-0 w-full h-full pointer-events-none">
									<title>旅行路线</title>
									<defs>
										<linearGradient
											id="pathGradient"
											x1="0%"
											y1="0%"
											x2="100%"
											y2="100%"
										>
											<stop
												offset="0%"
												stopColor="#6366f1"
												stopOpacity="0.3"
											/>
											<stop
												offset="100%"
												stopColor="#8b5cf6"
												stopOpacity="0.3"
											/>
										</linearGradient>
									</defs>
									<path
										d={`M ${35}% ${25}% Q ${50}% ${35}% ${65}% ${40}% Q ${55}% ${50}% ${20}% ${55}% Q ${50}% ${40}% ${80}% ${20}%`}
										fill="none"
										stroke="url(#pathGradient)"
										strokeWidth="2"
										strokeDasharray="5,5"
										className="animate-pulse"
									/>
								</svg>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
	);
}
