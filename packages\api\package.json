{"dependencies": {"@paralleldrive/cuid2": "^2.2.2", "@repo/ai": "workspace:*", "@repo/auth": "workspace:*", "@repo/config": "workspace:*", "@repo/database": "workspace:*", "@repo/i18n": "workspace:*", "@repo/logs": "workspace:*", "@repo/mail": "workspace:*", "@repo/payments": "workspace:*", "@repo/storage": "workspace:*", "@repo/utils": "workspace:*", "@scalar/hono-api-reference": "^0.5.175", "@sindresorhus/slugify": "^2.2.1", "dotenv": "^16.5.0", "hono": "^4.7.2", "hono-openapi": "^0.4.5", "nanoid": "^5.1.2", "openai": "^4.85.4", "openapi-merge": "^1.3.3", "playwright": "^1.52.0", "playwright-core": "^1.52.0", "prosemirror-markdown": "^1.13.2", "prosemirror-model": "^1.25.1", "prosemirror-schema-basic": "^1.2.4", "use-intl": "^3.26.5", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@tiptap/core": "^2.12.0", "@types/react": "19.0.10", "encoding": "^0.1.13", "prisma": "^6.6.0", "typescript": "5.8.3"}, "main": "./index.ts", "name": "@repo/api", "scripts": {"type-check": "tsc --noEmit"}, "version": "0.0.0"}