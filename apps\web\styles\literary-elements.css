/* 文艺清新视觉元素装饰 */

/* 日记卡片装饰边框 */
.diary-card-decorated {
	position: relative;
	overflow: hidden;
}

.diary-card-decorated::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 4px;
	background: linear-gradient(to right, #bfdbfe, #93c5fd, #60a5fa);
	border-radius: 4px 4px 0 0;
}

/* 诗意水墨风背景 */
.poetic-bg {
	background-image: linear-gradient(
			to bottom,
			rgba(255, 255, 255, 0.9),
			rgba(255, 255, 255, 0.6)
		),
		url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%2393c5fd' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
}

/* 诗意边框装饰 */
.poetic-border {
	border: 1px solid rgba(147, 197, 253, 0.2);
	box-shadow: 0 2px 10px rgba(147, 197, 253, 0.05);
}

/* 日记装饰线条 */
.diary-line {
	position: relative;
	padding-bottom: 0.5rem;
}

.diary-line::after {
	content: "";
	position: absolute;
	bottom: 0;
	left: 25%;
	width: 50%;
	height: 1px;
	background: linear-gradient(
		to right,
		transparent,
		rgba(147, 197, 253, 0.3),
		transparent
	);
}

/* 诗意图片框 */
.poetic-image-frame {
	border-radius: 0.5rem;
	border: 1px solid rgba(147, 197, 253, 0.3);
	padding: 3px;
	background-color: white;
	box-shadow: 0 3px 5px -1px rgba(147, 197, 253, 0.08), 0 1px 2px 0
		rgba(0, 0, 0, 0.02);
	transition: all 0.3s ease;
}

.poetic-image-frame:hover {
	transform: translateY(-2px);
	box-shadow: 0 6px 10px -1px rgba(147, 197, 253, 0.12), 0 2px 3px 0
		rgba(0, 0, 0, 0.05);
}

/* 古风装饰角 */
.corner-decoration {
	position: relative;
}

.corner-decoration::before,
.corner-decoration::after {
	content: "";
	position: absolute;
	width: 8px;
	height: 8px;
	border-style: solid;
	border-color: rgba(147, 197, 253, 0.4);
}

.corner-decoration::before {
	top: 0;
	left: 0;
	border-width: 1px 0 0 1px;
}

.corner-decoration::after {
	bottom: 0;
	right: 0;
	border-width: 0 1px 1px 0;
}

/* 旅行点位气泡 */
.travel-point-bubble {
	position: relative;
	border-radius: 1rem;
	padding: 1rem;
	background-color: rgba(255, 255, 255, 0.8);
	border: 1px solid rgba(147, 197, 253, 0.2);
	box-shadow: 0 2px 6px rgba(147, 197, 253, 0.08);
}

.travel-point-bubble::before {
	content: "";
	position: absolute;
	top: 50%;
	left: -8px;
	transform: translateY(-50%);
	width: 0;
	height: 0;
	border-style: solid;
	border-width: 8px 8px 8px 0;
	border-color: transparent rgba(147, 197, 253, 0.2) transparent transparent;
}
