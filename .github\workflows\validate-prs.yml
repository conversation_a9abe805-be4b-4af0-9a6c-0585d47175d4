name: Validate PRs

on:
  pull_request:
    branches: [main, develop]

env:
  DATABASE_URL: ${{ secrets.DATABASE_URL }}
  NODE_VERSION: '20'
  PNPM_VERSION: '9.3.0'

jobs:
  changes:
    name: Detect Changes
    runs-on: ubuntu-latest
    outputs:
      has-code-changes: ${{ steps.changes.outputs.has-code-changes }}
      has-deps-changes: ${{ steps.changes.outputs.has-deps-changes }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Detect changes
        id: changes
        run: |
          # 检测代码变更
          CODE_CHANGES=$(git diff --name-only origin/main...HEAD | grep -E '\.(ts|tsx|js|jsx)$' | wc -l)
          DEPS_CHANGES=$(git diff --name-only origin/main...HEAD | grep -E '(package\.json|pnpm-lock\.yaml)$' | wc -l)
          
          echo "has-code-changes=$((CODE_CHANGES > 0))" >> $GITHUB_OUTPUT
          echo "has-deps-changes=$((DEPS_CHANGES > 0))" >> $GITHUB_OUTPUT

  install:
    name: Install Dependencies
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}
          
      - name: Get pnpm store directory
        shell: bash
        run: echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV
          
      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: ${{ runner.os }}-pnpm-store-
            
      - name: Install dependencies
        run: pnpm install --frozen-lockfile

  lint:
    name: Lint & Format Check
    runs-on: ubuntu-latest
    needs: [install, changes]
    if: needs.changes.outputs.has-code-changes == 'true'
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}
          
      - name: Restore cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Run Biome lint
        run: pnpm lint
        
      - name: Run Biome format check
        run: pnpm prettier

  type-check:
    name: Type Check
    runs-on: ubuntu-latest
    needs: [install, changes]
    if: needs.changes.outputs.has-code-changes == 'true'
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}
          
      - name: Restore cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Generate database client
        run: pnpm db:generate
        
      - name: Type check
        run: pnpm type-check

  build:
    name: Build Check
    runs-on: ubuntu-latest
    needs: [install, changes]
    if: needs.changes.outputs.has-code-changes == 'true'
    strategy:
      matrix:
        app: [mapmoment, ai-images]
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}
          
      - name: Restore cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Generate database client
        run: pnpm db:generate
        
      - name: Build ${{ matrix.app }}
        run: pnpm build:${{ matrix.app }}
  e2e:
    name: E2E Tests
    timeout-minutes: 60
    runs-on: ubuntu-latest
    needs: [install, changes, build]
    if: needs.changes.outputs.has-code-changes == 'true'
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          
      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}
          
      - name: Restore cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        
      - name: Setup database
        run: |
          pnpm --filter database push
          pnpm --filter database generate
          
      - name: Install Playwright browsers
        run: pnpm --filter web exec playwright install --with-deps
        
      - name: Run Playwright tests
        run: pnpm --filter web e2e:ci
        
      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: apps/web/playwright-report/
          retention-days: 30

  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: [changes]
    if: needs.changes.outputs.has-deps-changes == 'true'
    steps:
      - uses: actions/checkout@v4
      
      - name: Run npm audit
        run: npm audit --audit-level=moderate
        continue-on-error: true
        
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'table'
          exit-code: '0'

  pr-summary:
    name: PR Summary
    runs-on: ubuntu-latest
    needs: [lint, type-check, build, e2e, security]
    if: always()
    steps:
      - name: Create PR summary
        run: |
          echo "## 🚀 PR Validation Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Check | Status | Details |" >> $GITHUB_STEP_SUMMARY
          echo "|-------|--------|---------|" >> $GITHUB_STEP_SUMMARY
          echo "| 🔍 Lint & Format | ${{ needs.lint.result }} | Code style and formatting |" >> $GITHUB_STEP_SUMMARY
          echo "| 🏗️ Type Check | ${{ needs.type-check.result }} | TypeScript compilation |" >> $GITHUB_STEP_SUMMARY
          echo "| 📦 Build | ${{ needs.build.result }} | Application builds |" >> $GITHUB_STEP_SUMMARY
          echo "| 🧪 E2E Tests | ${{ needs.e2e.result }} | End-to-end testing |" >> $GITHUB_STEP_SUMMARY
          echo "| 🔒 Security | ${{ needs.security.result }} | Vulnerability scanning |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [[ "${{ needs.lint.result }}" == "success" && "${{ needs.type-check.result }}" == "success" && "${{ needs.build.result }}" == "success" ]]; then
            echo "✅ **All checks passed!** This PR is ready for review." >> $GITHUB_STEP_SUMMARY
          else
            echo "❌ **Some checks failed.** Please fix the issues before merging." >> $GITHUB_STEP_SUMMARY
          fi
