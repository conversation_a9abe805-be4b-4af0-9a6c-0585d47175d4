"use client";

import type { LatLng } from "./types";

/**
 * 为路线添加虚线动画效果
 *
 * @param polyline - Google地图路线对象
 * @param options - 动画选项
 * @returns 取消动画的函数
 */
export function createFlowingDashedLine(
	polyline: google.maps.Polyline,
	options: {
		color?: string;
		speed?: number;
		dashLength?: number;
		gapLength?: number;
	} = {},
): () => void {
	const {
		color = "#3B82F6",
		speed = 1,
		dashLength = 20,
		gapLength = 10,
	} = options;

	// 设置虚线样式
	polyline.setOptions({
		strokeOpacity: 0, // 隐藏原有线条
		icons: [
			{
				icon: {
					path: "M 0,-1 0,1",
					strokeOpacity: 1,
					strokeWeight: polyline.get?.("strokeWeight") || 3,
					scale: dashLength,
					strokeColor: color,
				},
				offset: "0",
				repeat: `${dashLength}px ${gapLength}px`,
			},
			// 添加箭头标记指示方向
			{
				icon: {
					path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
					fillColor: color,
					fillOpacity: 1,
					strokeColor: color,
					scale: 3,
				},
				offset: "50%",
				repeat: "25%",
			},
		],
	});

	// 动画参数
	let offset = 0;
	let animationId: number | null = null;

	// 动画函数
	const animate = () => {
		offset = (offset + speed) % (dashLength + gapLength);

		const icons = polyline.get("icons");
		if (icons && icons.length > 0) {
			icons[0].offset = `${offset}px`;
			polyline.set("icons", icons);
		}

		animationId = window.requestAnimationFrame(animate);
	};

	// 启动动画
	animationId = window.requestAnimationFrame(animate);

	// 返回取消动画的函数
	return () => {
		if (animationId) {
			window.cancelAnimationFrame(animationId);
			animationId = null;
		}
	};
}

/**
 * 创建带有单一箭头的实线路径
 *
 * @param polyline - Google地图路线对象
 * @param options - 配置选项
 * @returns 取消动画的函数（如果需要动画）
 */
export function createSolidLineWithSingleArrow(
	polyline: google.maps.Polyline,
	options: {
		color?: string;
		weight?: number;
		arrowSize?: number;
		animate?: boolean;
	} = {},
): () => void {
	const {
		color = "#3B82F6",
		weight = 3,
		arrowSize = 3,
		animate = true,
	} = options;

	// 设置实线样式
	polyline.setOptions({
		strokeColor: color,
		strokeOpacity: 0.9,
		strokeWeight: weight,
		icons: [
			{
				icon: {
					path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
					fillColor: color,
					fillOpacity: 1,
					strokeColor: color,
					strokeWeight: 1,
					scale: arrowSize,
				},
				offset: "50%", // 箭头位于线条中间
			},
		],
	});

	// 如果不需要动画，则直接返回空函数
	if (!animate) {
		return () => {};
	}

	// 否则，为箭头添加简单的来回动画
	let arrowOffset = 45; // 起始位置（百分比）
	let direction = 1; // 动画方向
	let animationId: number | null = null;

	// 动画函数
	const animateArrow = () => {
		// 在45%到55%之间来回移动箭头，创建微妙的强调效果
		arrowOffset += 0.1 * direction;
		if (arrowOffset >= 55) direction = -1;
		if (arrowOffset <= 45) direction = 1;

		const icons = polyline.get("icons");
		if (icons && icons.length > 0) {
			icons[0].offset = `${arrowOffset}%`;
			polyline.set("icons", icons);
		}

		animationId = window.requestAnimationFrame(animateArrow);
	};

	// 启动动画
	animationId = window.requestAnimationFrame(animateArrow);

	// 返回取消动画的函数
	return () => {
		if (animationId) {
			window.cancelAnimationFrame(animationId);
			animationId = null;
		}
	};
}

/**
 * 创建带有渐进式绘制效果的实线路径
 * 这个函数结合了路径生长动画和箭头标记
 *
 * @param map - Google地图实例
 * @param path - 路径点数组
 * @param options - 配置选项
 * @returns 取消动画的函数和Polyline对象
 */
export function createProgressivePathAnimation(
	map: google.maps.Map,
	path: LatLng[],
	options: {
		color?: string;
		weight?: number;
		duration?: number;
		onComplete?: () => void;
	} = {},
): { cancel: () => void; polyline: google.maps.Polyline } {
	const {
		color = "#3B82F6",
		weight = 3,
		duration = 1500, // 动画持续时间，毫秒
		onComplete,
	} = options;

	// 创建初始路线，只包含第一个点
	const polyline = new google.maps.Polyline({
		path: [{ lat: path[0].lat, lng: path[0].lng }],
		geodesic: true,
		strokeColor: color,
		strokeOpacity: 1.0,
		strokeWeight: weight,
		map: map,
	});

	let animationId: number | null = null;
	let startTime: number | null = null;

	// 动画函数 - 逐渐增加路径点
	const animate = (timestamp: number) => {
		if (!startTime) startTime = timestamp;

		// 计算动画进度 (0 到 1 之间)
		const elapsed = timestamp - startTime;
		const progress = Math.min(elapsed / duration, 1);

		// 根据进度计算当前应显示的路径点
		if (path.length > 1) {
			const currentPointIndex = Math.max(
				1,
				Math.floor(progress * (path.length - 1)) + 1,
			);

			// 截取当前应显示的路径部分
			const currentPath = path.slice(0, currentPointIndex);
			polyline.setPath(
				currentPath.map((p) => ({ lat: p.lat, lng: p.lng })),
			);
		}

		// 如果动画未完成，继续下一帧
		if (progress < 1) {
			animationId = window.requestAnimationFrame(animate);
		} else {
			// 动画结束，设置完整路径
			polyline.setPath(path.map((p) => ({ lat: p.lat, lng: p.lng })));

			// 动画完成后，添加箭头效果
			createSolidLineWithSingleArrow(polyline, {
				color: color,
				weight: weight,
				animate: true,
			});

			// 调用完成回调
			if (onComplete) {
				onComplete();
			}
		}
	};

	// 启动动画
	animationId = window.requestAnimationFrame(animate);

	return {
		cancel: () => {
			if (animationId) {
				window.cancelAnimationFrame(animationId);
				animationId = null;
			}
		},
		polyline,
	};
}

/**
 * 使用球面插值生成平滑路径
 *
 * @param start - 起始点
 * @param end - 终点
 * @param numPoints - 插值点数量
 * @returns 平滑路径点数组
 */
export function generateSmoothPath(
	start: LatLng,
	end: LatLng,
	numPoints = 20,
): LatLng[] {
	// 检查球面几何库是否可用
	if (!window.google?.maps?.geometry?.spherical) {
		console.warn("Google Maps球面几何库未加载，无法生成平滑路径");
		return [start, end];
	}

	const path: LatLng[] = [];
	path.push(start);

	// 创建Google地图LatLng对象
	const startLatLng = new google.maps.LatLng(start.lat, start.lng);
	const endLatLng = new google.maps.LatLng(end.lat, end.lng);

	// 使用球面插值生成中间点
	for (let i = 1; i < numPoints - 1; i++) {
		const fraction = i / (numPoints - 1);
		const point = google.maps.geometry.spherical.interpolate(
			startLatLng,
			endLatLng,
			fraction,
		);

		path.push({
			lat: point.lat(),
			lng: point.lng(),
		});
	}

	path.push(end);
	return path;
}

/**
 * 创建可以使用的动画线条
 * @param fromPoint 起始点
 * @param toPoint 目标点
 * @param map 地图实例
 * @param options 配置选项
 */
export function createAnimatedRoute(
	fromPoint: LatLng,
	toPoint: LatLng,
	map: google.maps.Map,
	options: {
		color?: string;
		weight?: number;
		duration?: number;
		useSmoothPath?: boolean;
		onComplete?: () => void;
	} = {},
) {
	const {
		color = "#3B82F6",
		weight = 3,
		duration = 1500,
		useSmoothPath = true,
		onComplete,
	} = options;

	// 生成路径点
	let path: LatLng[];

	// 如果启用平滑路径且球面几何工具可用
	if (useSmoothPath && window.google?.maps?.geometry?.spherical) {
		// 使用球面插值生成平滑路径
		path = generateSmoothPath(fromPoint, toPoint, 20);
	} else {
		// 否则使用简单的直线路径
		path = [fromPoint, toPoint];
	}

	// 创建渐进式路径动画
	return createProgressivePathAnimation(map, path, {
		color,
		weight,
		duration,
		onComplete,
	});
}
