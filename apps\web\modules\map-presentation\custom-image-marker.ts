"use client";

import type { MapPoint } from "./types";

interface CustomMarkerOptions {
	imageUrl: string;
	size?: number;
	borderColor?: string;
	borderWidth?: number;
	scale?: number;
	anchor?: { x: number; y: number };
}

/**
 * 创建自定义圆形图片标记
 *
 * @param point 地图点位数据
 * @param options 自定义标记选项
 * @returns Google Maps 自定义标记对象
 */
export function createCustomImageMarker(
	map: google.maps.Map,
	point: MapPoint,
	options: CustomMarkerOptions,
): google.maps.marker.AdvancedMarkerElement {
	const {
		imageUrl,
		size = 40,
		borderColor = "#FFFFFF",
		borderWidth = 2,
		scale = 1,
		anchor = { x: 0.5, y: 0.5 },
	} = options;
	// 创建标记的DOM元素
	const markerElement = document.createElement("div");
	Object.assign(markerElement.style, {
		position: "relative",
		width: `${size}px`,
		height: `${size}px`,
		display: "flex",
		alignItems: "center",
		justifyContent: "center",
		borderRadius: "50%",
		boxShadow: "0 4px 12px 0 rgba(59,130,246,0.15)",
		background: "linear-gradient(135deg, #f0abfc 0%, #3b82f6 100%)", // 渐变外圈
		border: "3px solid #fff",
		padding: "2px",
		boxSizing: "border-box",
		// 设置CSS变换原点为中心，确保标记中心与地图点位对齐
		transformOrigin: "center center",
	});

	// 内层图片容器
	const imgWrapper = document.createElement("div");
	Object.assign(imgWrapper.style, {
		width: "100%",
		height: "100%",
		borderRadius: "50%",
		overflow: "hidden",
		background: "#fff",
		display: "flex",
		alignItems: "center",
		justifyContent: "center",
		border: "2px solid #3b82f6", // 内圈蓝色
		boxSizing: "border-box",
	});

	// 图片
	const img = document.createElement("img");
	Object.assign(img.style, {
		width: "100%",
		height: "100%",
		objectFit: "cover",
		borderRadius: "50%",
		display: "block",
	});

	img.src = imageUrl;

	// 错误处理同原来
	img.onerror = () => {
		console.error("标记图片加载失败:", imageUrl);
		// 设置一个默认的图标或背景色
		markerElement.textContent = point.location
			.substring(0, 1)
			.toUpperCase();
		Object.assign(markerElement.style, {
			display: "flex",
			alignItems: "center",
			justifyContent: "center",
			backgroundColor: "#3B82F6", // 蓝色背景
			color: "#FFFFFF",
			fontSize: `${size / 2}px`,
			fontWeight: "bold",
		});
	};

	// 确保图片加载成功
	img.onload = () => {
		console.log("标记图片加载成功:", imageUrl);
	};

	imgWrapper.appendChild(img);
	markerElement.appendChild(imgWrapper); // 创建标记
	try {
		console.log("创建高级标记元素，位置:", point.coordinates);

		// 创建一个容器元素，这个容器将用来调整标记的位置
		const containerElement = document.createElement("div");
		containerElement.style.position = "relative";

		// 调整标记位置，使其中心对齐到地图点位
		// 我们将标记向左上角偏移自身尺寸的一半，使其中心点与地图点位重合
		markerElement.style.position = "absolute";
		markerElement.style.left = `-${size / 2}px`;
		markerElement.style.top = `-${size / 2}px`;

		containerElement.appendChild(markerElement);

		// 使用容器元素作为标记内容
		const marker = new google.maps.marker.AdvancedMarkerElement({
			map,
			position: {
				lat: point.coordinates.lat,
				lng: point.coordinates.lng,
			},
			content: containerElement,
			title: point.location,
		});

		return marker;
	} catch (error) {
		console.error("创建高级标记元素失败:", error);
		throw error;
	}
}

/**
 * 为地图点位创建圆形图片标记
 *
 * @param map Google Maps 实例
 * @param point 地图点位数据
 * @returns 标记对象
 */
export function createCircleImageMarker(
	map: google.maps.Map,
	point: MapPoint,
): google.maps.marker.AdvancedMarkerElement | google.maps.Marker {
	try {
		// 获取点位的第一张图片作为标记图标
		const defaultImageUrl = "/images/default-location.jpg"; // 默认图片路径
		const imageUrl =
			point.images && point.images.length > 0
				? typeof point.images[0] === "string"
					? point.images[0]
					: point.images[0].url
				: defaultImageUrl;

		console.log("创建标记，尝试加载 Marker 库");

		// 检查是否支持高级标记API
		if (window.google?.maps?.marker?.AdvancedMarkerElement) {
			console.log("使用高级标记API创建标记");

			try {
				return createCustomImageMarker(map, point, {
					imageUrl,
					size: 40,
					borderColor: "#FFFFFF",
					borderWidth: 3,
				});
			} catch (error) {
				console.error("高级标记创建失败，回退到普通标记:", error);
				// 如果高级标记创建失败，回退到普通标记
				return createFallbackMarker(map, point, imageUrl);
			}
		} else {
			console.log("不支持高级标记API，使用普通标记");
			return createFallbackMarker(map, point, imageUrl);
		}
	} catch (error) {
		console.error("创建标记过程中出现错误:", error);
		// 出错时使用最简单的标记
		return new google.maps.Marker({
			position: {
				lat: point.coordinates.lat,
				lng: point.coordinates.lng,
			},
			map,
			title: point.location,
			animation: google.maps.Animation.DROP,
		});
	}
}

/**
 * 创建回退的普通标记（当高级标记不可用时）
 */
function createFallbackMarker(
	map: google.maps.Map,
	point: MapPoint,
	imageUrl: string,
): google.maps.Marker {
	console.log("创建回退标记");

	// 创建标记选项
	const markerOptions: google.maps.MarkerOptions = {
		position: {
			lat: point.coordinates.lat,
			lng: point.coordinates.lng,
		},
		map,
		title: point.location,
		animation: google.maps.Animation.DROP,
	};

	// 尝试使用自定义图标
	try {
		// 使用内置的图标而不是尝试SVG
		markerOptions.icon = {
			path: google.maps.SymbolPath.CIRCLE,
			scale: 8,
			fillColor: "#3B82F6",
			fillOpacity: 1,
			strokeColor: "#FFFFFF",
			strokeWeight: 2,
		};

		// 如果有自定义图片，尝试使用
		if (imageUrl) {
			// 使用简单的图片URL作为图标，避免SVG可能导致的问题
			// 如果普通URL不起作用，再回退到使用内置图标
			try {
				markerOptions.icon = {
					url: imageUrl,
					scaledSize: new google.maps.Size(40, 40),
					anchor: new google.maps.Point(20, 20),
				};
				console.log("使用原始图片作为图标:", imageUrl);
			} catch (e) {
				console.error("无法使用原始图片作为图标，使用内置图标:", e);
			}
		}
	} catch (error) {
		console.error("设置自定义图标失败:", error);
	}

	// 创建并返回标记
	return new google.maps.Marker(markerOptions);
}

/**
 * 创建圆形图标URL (使用SVG)
 * 适用于不支持高级标记API的情况
 * 注意：这个函数可能在某些环境中不工作
 */
function createCircularIcon(imageUrl: string): string {
	try {
		// 确保图片URL是绝对路径
		const absoluteImageUrl = imageUrl.startsWith("http")
			? imageUrl
			: `${window.location.origin}${imageUrl.startsWith("/") ? "" : "/"}${imageUrl}`;

		const encoded = encodeURIComponent(absoluteImageUrl);

		console.log("创建圆形SVG图标，使用图片:", absoluteImageUrl);

		// 修复SVG，使用更简单的结构避免clip-path兼容性问题
		return `data:image/svg+xml;utf-8,
		<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" width="40" height="40">
		  <defs>
			<clipPath id="circle">
			  <circle cx="20" cy="20" r="18" />
			</clipPath>
		  </defs>
		  <circle cx="20" cy="20" r="20" fill="white" />
		  <circle cx="20" cy="20" r="18" fill="none" stroke="white" stroke-width="4"/>
		  <image clip-path="url(%23circle)" href="${encoded}" width="40" height="40" x="0" y="0" />
		</svg>`;
	} catch (error) {
		console.error("创建圆形SVG图标失败:", error);
		return imageUrl; // 出错时直接返回原始URL
	}
}
