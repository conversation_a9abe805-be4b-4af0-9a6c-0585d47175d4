"use client";

import type { TravelPoint } from "../../types";
import { BaseTooltip, type TooltipMode } from "./shared/BaseTooltip";
import { DetailedTooltipContent } from "./shared/DetailedTooltipContent";
import { SimpleTooltipContent } from "./shared/SimpleTooltipContent";

export type TooltipType = "simple" | "detailed";

interface UnifiedTooltipProps {
	isVisible: boolean;
	type: TooltipType;
	mode: TooltipMode;
	point: TravelPoint;
	mousePosition?: { x: number; y: number };
	onRemovePoint: (id: string) => void;
	onClose?: () => void;
	className?: string;
	// 选项
	showDeleteButton?: boolean;
	showCloseButton?: boolean;
	closeOnClickOutside?: boolean;
	variant?: "transparent" | "solid" | "card" | "modal";
}

export function UnifiedTooltip({
	isVisible,
	type,
	mode,
	point,
	mousePosition,
	onRemovePoint,
	onClose,
	className = "",
	showDeleteButton = true,
	showCloseButton = true,
	closeOnClickOutside = true,
	variant,
}: UnifiedTooltipProps) {
	// 根据type和variant确定具体的样式变体
	const getContentVariant = () => {
		if (variant) {
			return variant;
		}
		// 默认变体
		if (type === "simple") {
			return mode === "static" ? "transparent" : "solid";
		}
		return mode === "global" ? "modal" : "card";
	};

	const contentVariant = getContentVariant();

	return (
		<BaseTooltip
			isVisible={isVisible}
			mode={mode}
			mousePosition={mousePosition}
			onClose={onClose}
			closeOnClickOutside={closeOnClickOutside}
			className={className}
		>
			{type === "simple" ? (
				<SimpleTooltipContent
					point={point}
					onRemovePoint={onRemovePoint}
					showDeleteButton={showDeleteButton}
					variant={contentVariant as "transparent" | "solid"}
				/>
			) : (
				<DetailedTooltipContent
					point={point}
					onRemovePoint={onRemovePoint}
					onClose={onClose}
					showCloseButton={showCloseButton}
					showDeleteButton={showDeleteButton}
					variant={contentVariant as "card" | "modal"}
				/>
			)}
		</BaseTooltip>
	);
}
