/**
 * Feature Voting UI组件配置
 * 将项目中的UI组件映射到Feature Voting模块需要的接口
 */

import { Alert, AlertDescription } from "@ui/components/alert";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { Textarea } from "@ui/components/textarea";
import { cn } from "@ui/lib";
import {
	AlertCircle,
	Calendar,
	ChevronUp,
	Lightbulb,
	Loader2,
	MessageCircle,
	Plus,
	RefreshCw,
	Send,
	TrendingUp,
} from "lucide-react";
// TODO: 修复 feature-voting 模块导入问题
// import type { UIComponents } from "../../../../../packages/shared-ui/src/feature-voting";
type UIComponents = any; // 临时类型定义

/**
 * UI组件配置对象
 * 将项目的UI组件映射到Feature Voting模块的接口
 */
export const uiComponents: UIComponents = {
	// 基础组件
	Button,
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	Input,
	Label,
	Textarea,
	Alert,
	AlertDescription,

	// Select组件
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,

	// Tabs组件
	Tabs,
	TabsContent,
	TabsList,
	TabsTrigger,

	// 图标组件
	ChevronUp,
	MessageCircle,
	Calendar,
	AlertCircle,
	Loader2,
	RefreshCw,
	Send,
	Lightbulb,
	TrendingUp,
	Plus,

	// 工具函数
	cn,
};
