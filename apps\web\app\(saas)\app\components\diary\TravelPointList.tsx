import type { TravelPointIconType } from "@repo/database/src/types/travel-diary";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@ui/components/alert-dialog";
import { ScrollArea } from "@ui/components/scroll-area";
import { cn } from "@ui/lib";
import { MapPin } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { AddNewPointItem } from "./AddNewPointItem";
import { ImageCarousel } from "./ImageCarousel";
import SortablePointItem, { type TravelPoint } from "./SortablePointItem";
import TravelPointForm from "./TravelPointForm";
import "./drag-styles.css";
import { useInlineEdit } from "./hooks/useInlineEdit";

// 引入 dnd-kit 相关库
import {
	DndContext,
	type DragEndEvent,
	DragOverlay,
	type DragStartEvent,
	KeyboardSensor,
	PointerSensor,
	closestCenter,
	useSensor,
	useSensors,
} from "@dnd-kit/core";
import {
	SortableContext,
	arrayMove,
	sortableKeyboardCoordinates,
	verticalListSortingStrategy,
} from "@dnd-kit/sortable";

import { useAnalytics } from "@modules/analytics";

interface TravelPointListProps {
	points: TravelPoint[];
	onAddPoint: (point: TravelPoint) => void;
	onEditPoint: (id: string, point: Partial<TravelPoint>) => void;
	onDeletePoint?: (id: string) => void;
	onReorderPoints?: (reorderedPoints: TravelPoint[]) => void;
	onInsertPoint?: (point: TravelPoint, afterIndex: number) => void;
	isAddDialogOpen?: boolean;
	onAddDialogClose?: () => void;
	activePointId?: string;
	onPointSelect?: (pointId: string) => void;
	className?: string;
}

const TravelPointList = ({
	points,
	onAddPoint,
	onEditPoint,
	onDeletePoint,
	onReorderPoints,
	onInsertPoint,
	isAddDialogOpen = false,
	onAddDialogClose,
	activePointId,
	onPointSelect,
	className,
}: TravelPointListProps) => {
	const { trackEvent } = useAnalytics();

	// 表单相关状态 - 保留简单添加对话框
	const [isLocalAddDialogOpen, setIsLocalAddDialogOpen] =
		useState<boolean>(false);

	// 删除确认对话框状态
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [pointToDeleteId, setPointToDeleteId] = useState<string | null>(null);

	// 拖拽相关状态
	const [activeId, setActiveId] = useState<string | null>(null);
	const [recentlyMovedPoint, setRecentlyMovedPoint] = useState<string | null>(
		null,
	);

	// 图片预览状态
	const [imagePreview, setImagePreview] = useState<{
		images: string[];
		locationName: string;
		initialIndex: number;
		isOpen: boolean;
	}>({
		images: [],
		locationName: "",
		initialIndex: 0,
		isOpen: false,
	});

	// 内联编辑功能
	const {
		editState,
		enterEditMode,
		exitEditMode,
		updateField,
		updateLocationField,
		updateImagesField,
		getFieldValue,
		isEditing,
		cleanup,
	} = useInlineEdit({
		onPointUpdate: (id, changes) => {
			// 确保图片字段的正确处理
			if (changes.images && Array.isArray(changes.images)) {
				// 如果是 TravelPointImage[] 格式，直接传递
				onEditPoint(id, changes);
			} else {
				onEditPoint(id, changes);
			}
		},
		onPointAdd: onAddPoint,
		autoSaveDelay: 1500,
	});

	// 创建传感器（用于拖拽检测）
	const sensors = useSensors(
		// 指针传感器（鼠标/触摸）- 添加激活约束确保只有通过拖拽手柄才能拖拽
		useSensor(PointerSensor, {
			activationConstraint: {
				distance: 8, // 需要移动8像素才激活拖拽
			},
		}),
		// 键盘传感器 - 允许使用键盘导航和排序
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		}),
	);

	// 创建每个点位的引用集合
	const pointRefs = useRef<Map<string, HTMLDivElement>>(new Map());

	// 清理定时器
	useEffect(() => {
		return cleanup;
	}, [cleanup]);

	// 同步外部添加对话框状态
	useEffect(() => {
		console.log("[TravelPointList] 外部对话框状态变化:", {
			isAddDialogOpen,
			currentLocalState: isLocalAddDialogOpen,
		});

		// 同步外部状态到本地状态
		setIsLocalAddDialogOpen(isAddDialogOpen);
	}, [isAddDialogOpen]);

	// 监听活动点位变化，自动滚动到对应位置
	useEffect(() => {
		if (activePointId) {
			const pointElement = pointRefs.current.get(activePointId);
			if (pointElement) {
				// 平滑滚动到选中的点位
				pointElement.scrollIntoView({
					behavior: "smooth",
					block: "center",
				});
			}
		}
	}, [activePointId]);

	// 获取图标背景色
	const getIconBackgroundColor = (iconType: string) => {
		switch (iconType as TravelPointIconType) {
			case "PIN":
				return "bg-rose-400/70";
			case "HOTEL":
				return "bg-sky-400/70";
			case "FOOD":
				return "bg-amber-300/70";
			case "LANDMARK":
				return "bg-violet-300/70";
			case "PARK":
				return "bg-teal-400/70";
			case "SHOPPING":
				return "bg-pink-300/70";
			case "TRANSPORT":
				return "bg-blue-300/70";
			case "OTHER":
				return "bg-gray-400/70";
			default:
				return "bg-slate-400/70";
		}
	};

	// 渲染点位图标
	const renderPointIcon = (iconType: string) => {
		const iconClassName = "w-5 h-5 text-slate-700";

		switch (iconType as TravelPointIconType) {
			case "PIN":
				return <MapPin className={iconClassName} />;
			case "HOTEL":
				return <span className="text-base">🏨</span>;
			case "FOOD":
				return <span className="text-base">🍽️</span>;
			case "LANDMARK":
				return <span className="text-base">🗿</span>;
			case "PARK":
				return <span className="text-base">🌳</span>;
			case "SHOPPING":
				return <span className="text-base">🛍️</span>;
			case "TRANSPORT":
				return <span className="text-base">🚗</span>;
			case "OTHER":
				return <span className="text-base">❓</span>;
			default:
				return <MapPin className={iconClassName} />;
		}
	};

	// 打开图片轮播
	const openImageCarousel = (point: TravelPoint, initialIndex = 0) => {
		// 确保图片数组存在且非空
		if (!point.images || point.images.length === 0) {
			toast.error("没有可查看的图片");
			return;
		}

		// 提取图片 URL 数组
		const imageUrls = point.images.map((img) =>
			typeof img === "string" ? img : img.url,
		);

		setImagePreview({
			images: imageUrls,
			locationName: point.location || "位置",
			initialIndex: initialIndex < point.images.length ? initialIndex : 0,
			isOpen: true,
		});
	};

	// 关闭图片轮播
	const closeImageCarousel = () => {
		setImagePreview((prev) => ({ ...prev, isOpen: false }));
	};

	// 处理删除点位
	const handleDeleteClick = (pointId: string) => {
		setPointToDeleteId(pointId);
		setIsDeleteDialogOpen(true);
	};

	// 确认删除点位
	const confirmDeletePoint = () => {
		if (pointToDeleteId && onDeletePoint) {
			onDeletePoint(pointToDeleteId);
			setPointToDeleteId(null);
			toast.success("旅行点位已删除");
		}
		setIsDeleteDialogOpen(false);
	};

	// 取消删除点位
	const cancelDeletePoint = () => {
		setPointToDeleteId(null);
		setIsDeleteDialogOpen(false);
	};

	// DnD拖拽开始处理函数
	const handleDragStart = (event: DragStartEvent) => {
		const { active } = event;
		setActiveId(active.id as string);
	};

	// DnD拖拽结束处理函数
	const handleDragEnd = (event: DragEndEvent) => {
		const { active, over } = event;

		if (over && active.id !== over.id) {
			const oldIndex = points.findIndex(
				(point) => point.id === active.id,
			);
			const newIndex = points.findIndex((point) => point.id === over.id);

			if (oldIndex !== -1 && newIndex !== -1) {
				const newPoints = arrayMove(points, oldIndex, newIndex);

				// 调用父组件的重新排序回调函数
				if (onReorderPoints) {
					onReorderPoints(newPoints);
				}

				// 记录最近移动的点位ID，用于高亮动画
				setRecentlyMovedPoint(active.id as string);

				// 1.5秒后取消高亮
				setTimeout(() => {
					setRecentlyMovedPoint(null);
				}, 1500);

				toast.success("点位顺序已更新");
			}
		}

		setActiveId(null);
	};

	// 处理添加对话框关闭
	const handleAddDialogClose = (isOpen: boolean) => {
		console.log("[TravelPointList] 添加对话框状态变化:", {
			isOpen,
			currentLocalState: isLocalAddDialogOpen,
		});

		setIsLocalAddDialogOpen(isOpen);

		// 如果对话框关闭，通知父组件
		if (!isOpen && onAddDialogClose) {
			console.log("[TravelPointList] 通知父组件对话框已关闭");
			onAddDialogClose();
		}
	};

	// 复制点位功能
	const handleCopyPoint = (point: TravelPoint) => {
		// 创建新的点位 - 复制所有内容但生成新的ID和时间戳
		const newPoint: TravelPoint = {
			...point,
			id: `copy-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`, // 生成唯一ID
			location: `${point.location} (副本)`, // 在位置名称后添加"(副本)"标识
		};

		// 找到被复制点位在列表中的位置
		const originalIndex = points.findIndex((p) => p.id === point.id);

		if (onInsertPoint && originalIndex !== -1) {
			// 在被复制点位的下方插入新点位
			onInsertPoint(newPoint, originalIndex);
		} else {
			// 回退方案：添加到列表末尾
			onAddPoint(newPoint);
		}

		// Analytics: 追踪点位复制
		trackEvent("point_copied", {
			icon_type: point.iconType,
			has_images: (point.images?.length || 0) > 0,
			location_type: point.location.split(",")[0], // 只记录城市名
		});

		// 显示成功提示
		toast.success(`已复制点位 "${point.location}"`);
	};

	return (
		<div
			className={cn(
				"w-full bg-card rounded-lg shadow-sm p-4 flex flex-col h-full",
				className,
			)}
		>
			{/* 添加点位表单 - 保留用于复杂添加 */}
			<TravelPointForm
				isOpen={isLocalAddDialogOpen}
				onOpenChange={handleAddDialogClose}
				onAddPoint={onAddPoint}
			/>

			{/* 点位列表区域 */}
			<ScrollArea className="flex-1 scrollbar-travel pr-2">
				<div className="pr-1">
					{points.length === 0 ? (
						<div className="text-center py-12 border-2 border-dashed border-muted rounded-lg">
							<div className="w-16 h-16 mx-auto rounded-full bg-muted/30 flex items-center justify-center mb-2">
								<MapPin className="w-8 h-8 text-muted-foreground" />
							</div>
							<p className="text-muted-foreground mb-4">
								您的旅程将显示在这里。添加第一个旅行点位开始记录您的旅行。
							</p>
							{/* 空状态下的添加新点位 */}
							<AddNewPointItem
								onAddPoint={onAddPoint}
								getIconBackgroundColor={getIconBackgroundColor}
								renderPointIcon={renderPointIcon}
								onImagePreview={openImageCarousel}
							/>
						</div>
					) : (
						<div className="space-y-4 relative">
							{/* 时间轴线 */}
							<div className="absolute left-[36px] top-6 bottom-1 w-[2px] bg-slate-200/80 dark:bg-slate-700/80 z-0 rounded-full" />

							{/* 使用 DndContext 包装可排序列表 */}
							<DndContext
								sensors={sensors}
								collisionDetection={closestCenter}
								onDragStart={handleDragStart}
								onDragEnd={handleDragEnd}
							>
								<SortableContext
									items={points.map((point) => point.id)}
									strategy={verticalListSortingStrategy}
								>
									{points.map((point) => (
										<div
											key={point.id}
											ref={(el) => {
												if (el) {
													pointRefs.current.set(
														point.id,
														el,
													);
												}
											}}
										>
											<SortablePointItem
												point={point}
												isActive={
													activePointId === point.id
												}
												isHighlighted={
													recentlyMovedPoint ===
													point.id
												}
												onCopy={handleCopyPoint}
												onDelete={handleDeleteClick}
												onSelect={(id) =>
													onPointSelect?.(id)
												}
												onImagePreview={
													openImageCarousel
												}
												getIconBackgroundColor={
													getIconBackgroundColor
												}
												renderPointIcon={
													renderPointIcon
												}
												// 内联编辑相关属性
												isEditing={isEditing(point.id)}
												editingFields={
													editState.editingFields.get(
														point.id,
													) || new Set()
												}
												onEnterEditMode={enterEditMode}
												onExitEditMode={exitEditMode}
												onFieldUpdate={updateField}
												onLocationUpdate={
													updateLocationField
												}
												onImagesUpdate={
													updateImagesField
												}
												getFieldValue={getFieldValue}
											/>
										</div>
									))}
								</SortableContext>

								{/* 拖拽叠加层 - 显示当前拖拽的项目 */}
								<DragOverlay adjustScale={true}>
									{activeId ? (
										<div className="opacity-80">
											<SortablePointItem
												point={
													points.find(
														(p) =>
															p.id === activeId,
													)!
												}
												isActive={false}
												onCopy={handleCopyPoint}
												onDelete={handleDeleteClick}
												onSelect={() => {}}
												onImagePreview={
													openImageCarousel
												}
												getIconBackgroundColor={
													getIconBackgroundColor
												}
												renderPointIcon={
													renderPointIcon
												}
												onImagesUpdate={
													updateImagesField
												}
											/>
										</div>
									) : null}
								</DragOverlay>
							</DndContext>

							{/* 添加新点位区域 - 在现有点位列表下方 */}
							<AddNewPointItem
								onAddPoint={onAddPoint}
								getIconBackgroundColor={getIconBackgroundColor}
								renderPointIcon={renderPointIcon}
								onImagePreview={openImageCarousel}
								className="mt-6"
							/>
						</div>
					)}
				</div>
			</ScrollArea>

			{/* 图片轮播组件 */}
			<ImageCarousel
				images={imagePreview.images}
				locationName={imagePreview.locationName}
				initialIndex={imagePreview.initialIndex}
				isOpen={imagePreview.isOpen}
				onOpenChange={(open) => {
					if (!open) closeImageCarousel();
				}}
			/>

			{/* 删除确认对话框 */}
			<AlertDialog
				open={isDeleteDialogOpen}
				onOpenChange={setIsDeleteDialogOpen}
			>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>确认删除旅行点位？</AlertDialogTitle>
						<AlertDialogDescription>
							此操作不可逆。该点位的所有信息和照片将被删除。
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel onClick={cancelDeletePoint}>
							取消
						</AlertDialogCancel>
						<AlertDialogAction
							onClick={confirmDeletePoint}
							className="bg-destructive hover:bg-destructive/90 text-destructive-foreground"
						>
							删除
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</div>
	);
};

export default TravelPointList;
