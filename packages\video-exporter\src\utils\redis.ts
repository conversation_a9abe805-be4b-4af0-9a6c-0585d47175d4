import { logger } from "@repo/logs";
import type Redis from "ioredis";

/**
 * 创建 Redis 连接
 * MVP阶段暂时禁用Redis连接
 */
export function createRedisConnection(): Redis {
	// MVP阶段：注释掉Redis连接逻辑
	logger.info("MVP阶段：跳过Redis连接创建");

	// 返回null，调用方需要处理null情况
	return null as any;

	/* MVP阶段注释掉的Redis连接逻辑
	// 打印环境变量情况以便调试
	logger.info(
		`UPSTASH_REDIS_URL 环境变量是否存在: ${Bo<PERSON>an(process.env.UPSTASH_REDIS_URL)}`,
	);

	// 优先使用Upstash Redis URL
	if (process.env.UPSTASH_REDIS_URL) {
		logger.info(
			`使用Upstash Redis连接: ${process.env.UPSTASH_REDIS_URL.replace(/:[^:]*@/, ":****@")}`,
		);
		const redis = new Redis(process.env.UPSTASH_REDIS_URL, {
			maxRetriesPerRequest: null,
			enableReadyCheck: true,
			tls: {
				rejectUnauthorized: false,
			},
			retryStrategy: (times: number) => {
				// 重连策略：基础延迟 * 尝试次数 (最大 10秒)
				const delay = Math.min(times * 1000, 10000);
				logger.info(`Redis 连接重试 #${times}, 延迟 ${delay}ms`);
				return delay;
			},
		});

		setupRedisEvents(redis);
		return redis;
	}

	// 备用：使用传统连接方式
	// 从环境变量获取 Redis 连接配置
	const host = process.env.REDIS_HOST || "localhost";
	const port = Number.parseInt(process.env.REDIS_PORT || "6379", 10);
	const password = process.env.REDIS_PASSWORD;
	const db = Number.parseInt(process.env.REDIS_DB || "0", 10);

	// 创建 Redis 客户端
	const redis = new Redis({
		host,
		port,
		password: password || undefined,
		db,
		maxRetriesPerRequest: null,
		enableReadyCheck: true,
		retryStrategy: (times: number) => {
			// 重连策略：基础延迟 * 尝试次数 (最大 10秒)
			const delay = Math.min(times * 1000, 10000);
			logger.info(`Redis 连接重试 #${times}, 延迟 ${delay}ms`);
			return delay;
		},
	});

	setupRedisEvents(redis);
	return redis;
	*/
}

// 设置Redis事件监听
// MVP阶段：注释掉Redis事件监听逻辑
function setupRedisEvents(redis: Redis) {
	// MVP阶段：跳过Redis事件监听设置
	logger.info("MVP阶段：跳过Redis事件监听设置");

	/* MVP阶段注释掉的Redis事件监听逻辑
	// 连接事件处理
	redis.on("connect", () => {
		logger.info("Redis 连接已建立");
	});

	redis.on("ready", () => {
		logger.info("Redis 连接已就绪");
	});

	redis.on("error", (err) => {
		logger.error(`Redis 错误: ${err.message}`);
	});

	redis.on("close", () => {
		logger.info("Redis 连接已关闭");
	});

	redis.on("reconnecting", () => {
		logger.info("Redis 正在重新连接...");
	});
	*/
}
