"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { authClient } from "@repo/auth/client";
import { useRouter } from "next/navigation";
import { useSearchParams } from "next/navigation";
import { useState } from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import { z } from "zod";

// 这些组件需要从各自的应用中传入或者也提取到共享包中
interface LoginFormProps {
	// UI组件
	Alert: any;
	AlertDescription: any;
	AlertTitle: any;
	Button: any;
	Form: any;
	FormControl: any;
	FormField: any;
	FormItem: any;
	FormLabel: any;
	Input: any;

	// 图标
	icons: {
		AlertTriangle: any;
		ArrowRight: any;
		Eye: any;
		EyeOff: any;
		Key: any;
		Mailbox: any;
	};

	// 翻译函数
	t: (key: string) => string;

	// 自定义配置
	redirectAfterSignIn?: string;
	enablePasswordLogin?: boolean;
	enableMagicLink?: boolean;

	// 可选的自定义组件
	LoginModeSwitch?: any;
	SocialSigninButton?: any;
	OrganizationInvitationAlert?: any;

	// 错误处理
	getAuthErrorMessage?: (code?: string) => string;
}

const formSchema = z.union([
	z.object({
		mode: z.literal("magic-link"),
		email: z.string().email(),
	}),
	z.object({
		mode: z.literal("password"),
		email: z.string().email(),
		password: z.string().min(1),
	}),
]);

type FormValues = z.infer<typeof formSchema>;

export function LoginForm({
	Alert,
	AlertDescription,
	AlertTitle,
	Button,
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	Input,
	icons,
	t,
	redirectAfterSignIn = "/app",
	enablePasswordLogin = true,
	enableMagicLink = true,
	LoginModeSwitch,
	SocialSigninButton,
	OrganizationInvitationAlert,
	getAuthErrorMessage = (code) => "登录失败，请重试",
}: LoginFormProps) {
	const router = useRouter();
	const searchParams = useSearchParams();

	const [showPassword, setShowPassword] = useState(false);
	const invitationId = searchParams.get("invitationId");
	const email = searchParams.get("email");
	const redirectTo = searchParams.get("redirectTo");

	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			email: email ?? "",
			password: "",
			mode: enablePasswordLogin ? "password" : "magic-link",
		},
	});

	const redirectPath = invitationId
		? `/app/organization-invitation/${invitationId}`
		: (redirectTo ?? redirectAfterSignIn);

	const onSubmit: SubmitHandler<FormValues> = async (values) => {
		try {
			if (values.mode === "password") {
				const { error } = await authClient.signIn.email({
					...values,
				});

				if (error) {
					throw error;
				}

				router.replace(redirectPath);
			} else {
				const { error } = await authClient.signIn.magicLink({
					...values,
					callbackURL: redirectPath,
				});

				if (error) {
					throw error;
				}
			}
		} catch (e) {
			form.setError("root", {
				message: getAuthErrorMessage(
					e && typeof e === "object" && "code" in e
						? (e.code as string)
						: undefined,
				),
			});
		}
	};

	const signinMode = form.watch("mode");

	return (
		<div>
			<h1 className="font-extrabold text-2xl md:text-3xl">
				{t("auth.login.title")}
			</h1>
			<p className="mt-1 mb-6 text-foreground/60">
				{t("auth.login.subtitle")}
			</p>

			{form.formState.isSubmitSuccessful &&
			signinMode === "magic-link" ? (
				<Alert variant="success">
					<icons.Mailbox className="size-6" />
					<AlertTitle>
						{t("auth.login.hints.linkSent.title")}
					</AlertTitle>
					<AlertDescription>
						{t("auth.login.hints.linkSent.message")}
					</AlertDescription>
				</Alert>
			) : (
				<>
					{invitationId && OrganizationInvitationAlert && (
						<OrganizationInvitationAlert className="mb-6" />
					)}

					<Form {...form}>
						<form
							className="space-y-4"
							onSubmit={form.handleSubmit(onSubmit)}
						>
							{enableMagicLink &&
								enablePasswordLogin &&
								LoginModeSwitch && (
									<LoginModeSwitch
										activeMode={signinMode}
										onChange={(mode) =>
											form.setValue(
												"mode",
												mode as typeof signinMode,
											)
										}
									/>
								)}

							{form.formState.isSubmitted &&
								form.formState.errors.root?.message && (
									<Alert variant="error">
										<icons.AlertTriangle className="size-6" />
										<AlertTitle>
											{form.formState.errors.root.message}
										</AlertTitle>
									</Alert>
								)}

							<FormField
								control={form.control}
								name="email"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											{t("auth.login.email")}
										</FormLabel>
										<FormControl>
											<Input
												type="email"
												placeholder={t(
													"auth.login.emailPlaceholder",
												)}
												{...field}
											/>
										</FormControl>
									</FormItem>
								)}
							/>

							{signinMode === "password" && (
								<FormField
									control={form.control}
									name="password"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												{t("auth.login.password")}
											</FormLabel>
											<FormControl>
												<div className="relative">
													<Input
														type={
															showPassword
																? "text"
																: "password"
														}
														placeholder={t(
															"auth.login.passwordPlaceholder",
														)}
														{...field}
													/>
													<Button
														type="button"
														variant="ghost"
														size="sm"
														className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
														onClick={() =>
															setShowPassword(
																!showPassword,
															)
														}
													>
														{showPassword ? (
															<icons.EyeOff className="size-4" />
														) : (
															<icons.Eye className="size-4" />
														)}
													</Button>
												</div>
											</FormControl>
										</FormItem>
									)}
								/>
							)}

							<Button
								type="submit"
								className="w-full"
								loading={form.formState.isSubmitting}
							>
								{signinMode === "password"
									? t("auth.login.signIn")
									: t("auth.login.sendMagicLink")}
								<icons.ArrowRight className="ml-2 size-4" />
							</Button>
						</form>
					</Form>
				</>
			)}
		</div>
	);
}
