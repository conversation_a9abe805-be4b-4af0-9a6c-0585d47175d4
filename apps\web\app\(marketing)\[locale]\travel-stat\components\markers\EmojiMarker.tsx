"use client";

import { useMemo } from "react";
import type { BaseMarkerProps } from "./types";

interface EmojiMarkerProps extends BaseMarkerProps {
	emoji?: string;
	backgroundColor?: string;
	size?: "small" | "medium" | "large";
	hideOutline?: boolean;
}

export function EmojiMarker({
	point,
	onRemovePoint,
	isSelected = false,
	scale = 1,
	animationSpeed = "normal",
	emoji = "📍",
	backgroundColor = "#3b82f6",
	size = "medium",
	hideOutline = false,
}: EmojiMarkerProps) {
	// 根据尺寸计算大小
	const dimensions = useMemo(() => {
		const baseSize = size === "small" ? 32 : size === "large" ? 48 : 40;
		return {
			width: baseSize * scale,
			height: baseSize * scale,
			fontSize: baseSize * 0.6 * scale,
		};
	}, [size, scale]);

	// 根据动画速度调整脉动频率
	const speedMultiplier = useMemo(() => {
		switch (animationSpeed) {
			case "slow":
				return 0.8;
			case "fast":
				return 1.5;
			default:
				return 1;
		}
	}, [animationSpeed]);

	return (
		<div className="relative group">
			{hideOutline ? (
				// 无轮廓模式 - 只显示 emoji
				<div
					className="flex items-center justify-center cursor-pointer hover:scale-110 transition-all duration-300"
					style={{
						width: `${dimensions.width}px`,
						height: `${dimensions.height}px`,
						transform: isSelected ? "scale(1.2)" : "scale(1)",
					}}
				>
					<span
						className="select-none"
						style={{
							fontSize: `${dimensions.fontSize * 1.2}px`, // 稍微放大 emoji
							lineHeight: 1,
							filter: "drop-shadow(0 2px 4px rgba(0,0,0,0.4))",
						}}
					>
						{emoji}
					</span>
				</div>
			) : (
				// 标准模式 - 带背景和轮廓
				<>
					{/* 背景光晕效果 - 只在非透明背景时显示 */}
					{backgroundColor !== "transparent" && (
						<div
							className="absolute rounded-full"
							style={{
								width: `${dimensions.width * 1.8}px`,
								height: `${dimensions.height * 1.8}px`,
								background: `radial-gradient(circle, ${backgroundColor}30 0%, ${backgroundColor}20 50%, transparent 80%)`,
								animation: `emoji-pulse ${2 / speedMultiplier}s ease-in-out infinite`,
								transform: "translate(-50%, -50%)",
								top: "50%",
								left: "50%",
							}}
						/>
					)}

					{/* 主体容器 */}
					<div
						className={`relative z-10 rounded-full border-2 cursor-pointer hover:scale-110 transition-all duration-300 flex items-center justify-center overflow-hidden ${
							backgroundColor === "transparent"
								? "border-gray-300 shadow-md"
								: "border-white shadow-lg"
						}`}
						style={{
							width: `${dimensions.width}px`,
							height: `${dimensions.height}px`,
							backgroundColor:
								backgroundColor === "transparent"
									? "transparent"
									: backgroundColor,
							transform: isSelected ? "scale(1.2)" : "scale(1)",
							boxShadow:
								backgroundColor === "transparent"
									? isSelected
										? "0 0 20px rgba(0,0,0,0.3), 0 0 40px rgba(0,0,0,0.1)"
										: "0 2px 8px rgba(0,0,0,0.15)"
									: isSelected
										? `0 0 20px ${backgroundColor}80, 0 0 40px ${backgroundColor}40`
										: `0 4px 12px ${backgroundColor}40`,
						}}
					>
						{/* Emoji图标 */}
						<span
							className="select-none"
							style={{
								fontSize: `${dimensions.fontSize}px`,
								lineHeight: 1,
								filter: "drop-shadow(0 1px 2px rgba(0,0,0,0.3))",
							}}
						>
							{emoji}
						</span>
					</div>
				</>
			)}

			{/* 选中时的外环 - 只在非无轮廓模式时显示 */}
			{isSelected && !hideOutline && (
				<div
					className="absolute rounded-full border-2 border-white/50"
					style={{
						width: `${dimensions.width * 1.4}px`,
						height: `${dimensions.height * 1.4}px`,
						animation: `emoji-ring ${1.5 / speedMultiplier}s ease-in-out infinite`,
						transform: "translate(-50%, -50%)",
						top: "50%",
						left: "50%",
					}}
				/>
			)}

			{/* CSS动画样式注入 */}
			<style jsx>{`
        @keyframes emoji-pulse {
          0%, 100% {
            transform: translate(-50%, -50%) scale(0.8);
            opacity: 0.4;
          }
          50% {
            transform: translate(-50%, -50%) scale(1.2);
            opacity: 0.1;
          }
        }
        
        @keyframes emoji-ring {
          0%, 100% {
            transform: translate(-50%, -50%) scale(1);
            opacity: 0.6;
          }
          50% {
            transform: translate(-50%, -50%) scale(1.1);
            opacity: 0.3;
          }
        }
      `}</style>
		</div>
	);
}
