"use client";

import { Badge } from "@ui/components/badge";
import { But<PERSON> } from "@ui/components/button";
import {} from "@ui/components/card";
import { AlertCircle, CheckCircle, CreditCard, Loader2 } from "lucide-react";
import React, {
	useState,
	useCallback,
	useRef,
	useLayoutEffect,
	useMemo,
	useEffect,
} from "react";
import { useMapExporter } from "../export";
import { MapContainer } from "../map/MapContainer";
import { PLATFORM_DIMENSIONS } from "./utils/platformAdaptation";

import { useMap } from "../../contexts/MapContext";
import { useTravelStatTranslations } from "../../hooks/useTravelStatTranslations";
// 导入MapContext的TravelPoint类型
import type { TravelPoint as MapTravelPoint } from "../../types";
import type {
	CardCustomization,
	CardTemplate,
	SocialPlatform,
} from "./types/cardTypes";

interface CardPreviewAreaProps {
	selectedTemplate: CardTemplate;
	customization: CardCustomization;
	platform: SocialPlatform;
	onExport: (quality: any) => void;
}

export function CardPreviewArea({
	selectedTemplate,
	customization,
	platform,
	onExport,
}: CardPreviewAreaProps) {
	const {
		travelPoints: mapTravelPoints,
		visitedCountries,
		// Omit other map props for now as MapContainer will be refactored
	} = useMap();

	const travelStatT = useTravelStatTranslations();

	const [isExporting, setIsExporting] = useState(false);
	const [exportStatus, setExportStatus] = useState<
		"idle" | "success" | "error"
	>("idle");
	const [exportMessage, setExportMessage] = useState<string>("");

	const previewContainerRef = useRef<HTMLDivElement>(null);
	const mainContainerRef = useRef<HTMLDivElement>(null);
	const cardRef = useRef<HTMLDivElement>(null);
	const mapRef = useRef<any>(null);
	const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });

	// 将MapContext的TravelPoint转换为EnhancedMapExporter期望的格式
	const adaptedTravelPoints = useMemo(() => {
		return mapTravelPoints.map((point: MapTravelPoint) => ({
			...point, // 保留所有原有属性包括timestamp
			date: new Date(point.timestamp).toISOString(), // 添加date字段用于卡片组件
		}));
	}, [mapTravelPoints]);

	// 为卡片组件单独准备数据（不含timestamp）
	const cardTravelPoints = useMemo(() => {
		return mapTravelPoints.map((point: MapTravelPoint) => ({
			id: point.id,
			name: point.name,
			city: point.city,
			country: point.country,
			coordinates: point.coordinates,
			date: new Date(point.timestamp).toISOString(),
			photos: point.photos,
			description: point.description,
			tags: point.tags,
		}));
	}, [mapTravelPoints]);

	// 初始化 MapExporter
	const {
		exportMap,
		isExporting: mapExporting,
		currentMethod,
	} = useMapExporter();

	useLayoutEffect(() => {
		const updateContainerSize = () => {
			const mainContainer = mainContainerRef.current;
			const previewContainer = previewContainerRef.current;

			if (!mainContainer || !previewContainer) {
				return;
			}

			// 获取主容器的总尺寸
			const mainRect = mainContainer.getBoundingClientRect();
			// 获取预览容器的实际尺寸
			const previewRect = previewContainer.getBoundingClientRect();

			// 计算工具栏占用高度
			// 工具栏: mb-4 + padding + content ≈ 70px
			const reservedHeight = 80;
			const horizontalPadding = 16; // 为卡片左右留出一些边距

			// 计算实际可用的预览区域尺寸
			// 宽度是关键，高度将自适应
			const availableWidth = Math.max(
				400, // 确保最小宽度能显示完整卡片
				mainRect.width - horizontalPadding,
			);

			// 高度计算更灵活，主要用于容器尺寸监听
			const availableHeight = Math.max(
				300, // 较小的最小高度，因为高度将自适应
				mainRect.height - reservedHeight,
			);

			// 使用预览容器的实际尺寸而不是计算的可用尺寸
			// 这样能确保卡片最大化填充实际的预览空间
			const actualContainerWidth = previewRect.width;
			const actualContainerHeight = previewRect.height;

			// Debug日志 - 容器尺寸计算（宽度占满模式）
			console.log("🔍 [CardPreview Debug] 宽度占满模式 - 容器尺寸计算:", {
				mainContainerSize: {
					width: mainRect.width,
					height: mainRect.height,
				},
				previewContainerActualSize: {
					width: previewRect.width,
					height: previewRect.height,
				},
				settings: {
					reservedHeight,
					horizontalPadding,
					note: "高度将根据平台比例自适应",
				},
				calculatedAvailableSize: {
					width: availableWidth,
					height: availableHeight,
					note: "宽度用于卡片缩放，高度用于容器监听",
				},
				finalUsedSize: {
					width:
						actualContainerWidth > 0
							? actualContainerWidth
							: availableWidth,
					height:
						actualContainerHeight > 0
							? actualContainerHeight
							: availableHeight,
					note: "实际使用宽度进行缩放计算",
				},
			});

			setContainerSize({
				width:
					actualContainerWidth > 0
						? actualContainerWidth
						: availableWidth,
				height:
					actualContainerHeight > 0
						? actualContainerHeight
						: availableHeight,
			});
		};

		// 初始化尺寸
		updateContainerSize();

		// 使用ResizeObserver监听主容器变化
		const resizeObserver = new ResizeObserver(updateContainerSize);

		if (mainContainerRef.current) {
			resizeObserver.observe(mainContainerRef.current);
		}

		// 同时监听window resize作为backup
		window.addEventListener("resize", updateContainerSize);

		return () => {
			resizeObserver.disconnect();
			window.removeEventListener("resize", updateContainerSize);
		};
	}, []);

	// 优化的缩放计算逻辑 - 宽度占满，高度自适应
	const previewData = useMemo(() => {
		if (containerSize.width === 0 || containerSize.height === 0) {
			return {
				scale: 0,
				dimensions: { width: 0, height: 0 },
				actualHeight: 0,
			};
		}

		const platformDimensions =
			PLATFORM_DIMENSIONS[platform] || PLATFORM_DIMENSIONS.instagram;
		const { width: cardWidth, height: cardHeight } = platformDimensions;

		// 使用较小的边距以最大化卡片显示
		const margin = 8;

		// 宽度占满容器（减去边距）
		const scale = (containerSize.width - margin * 2) / cardWidth;

		// 根据缩放比例计算实际显示的卡片高度
		const actualCardHeight = cardHeight * scale;

		// Debug日志 - 缩放计算
		console.log("🎯 [CardPreview Debug] 宽度占满缩放计算:", {
			platform,
			cardOriginalSize: {
				width: cardWidth,
				height: cardHeight,
				aspectRatio: (cardWidth / cardHeight).toFixed(2),
			},
			containerSize: {
				width: containerSize.width,
				height: containerSize.height,
			},
			margin,
			availableWidth: containerSize.width - margin * 2,
			scaleCalculation: {
				scale: scale.toFixed(3),
				scalePercentage: `${(scale * 100).toFixed(1)}%`,
			},
			finalCardSize: {
				width: Math.round(cardWidth * scale),
				height: Math.round(actualCardHeight),
			},
			heightUtilization: {
				needed: actualCardHeight,
				available: containerSize.height,
				efficiency: `${((actualCardHeight / containerSize.height) * 100).toFixed(1)}%`,
			},
		});

		return {
			scale: Math.max(0.1, scale), // 确保最小缩放比例
			dimensions: platformDimensions,
			actualHeight: actualCardHeight,
		};
	}, [platform, containerSize]);

	// 地图尺寸强制更新函数
	const forceMapResize = useCallback(() => {
		// 获取当前页面中所有的 Mapbox 地图实例
		const mapContainers = document.querySelectorAll(".mapboxgl-map");
		mapContainers.forEach((container) => {
			const map = (container as any)._map;
			if (map && typeof map.resize === "function") {
				try {
					map.resize();
					console.log("🔄 卡片模式下强制调整地图尺寸");
				} catch (error) {
					console.warn("⚠️ 卡片模式地图尺寸调整失败:", error);
				}
			}
		});
	}, []);

	// 监听缩放变化，强制地图更新尺寸
	useEffect(() => {
		if (previewData.scale > 0) {
			// 当缩放比例变化时，延迟调整地图尺寸
			const timer = setTimeout(() => {
				forceMapResize();
			}, 200);

			return () => clearTimeout(timer);
		}
	}, [previewData.scale, forceMapResize]);

	// 导出卡片功能 - 使用统一的导出器
	const handleExportCard = useCallback(async () => {
		if (!cardRef.current) {
			setExportStatus("error");
			setExportMessage(travelStatT.cardGenerator.preview.cardNotReady());
			setTimeout(() => {
				setExportStatus("idle");
				setExportMessage("");
			}, 3000);
			return;
		}

		setIsExporting(true);
		setExportStatus("idle");

		try {
			// 动态导入统一的卡片导出函数
			const { exportCard } = await import(
				"../export/exporters/html2canvas-exporter"
			);

			const filename = `travel_card_${selectedTemplate.id}_${platform}_${Date.now()}.png`;

			// 使用统一的导出器
			await exportCard(cardRef.current, mapRef.current, {
				width: previewData.dimensions.width,
				height: previewData.dimensions.height,
				scale: 2,
				filename,
			});

			setExportStatus("success");
			setExportMessage(
				travelStatT.cardGenerator.preview.cardExportSuccess(filename),
			);
			onExport({ type: "card", filename });
		} catch (error) {
			console.error("❌ [卡片导出调试] 导出卡片失败:", error);
			setExportStatus("error");
			setExportMessage(
				travelStatT.cardGenerator.preview.cardExportFailed(),
			);
		} finally {
			setIsExporting(false);
			setTimeout(() => {
				setExportStatus("idle");
				setExportMessage("");
			}, 3000);
		}
	}, [
		cardRef,
		previewData.dimensions,
		selectedTemplate.id,
		platform,
		onExport,
		mapRef,
	]);

	// 渲染智能适配的卡片预览
	const renderCardPreview = () => {
		if (!selectedTemplate || previewData.scale === 0) {
			return (
				<div className="flex flex-col items-center justify-center gap-4 text-gray-500">
					<Loader2 className="w-8 h-8 animate-spin text-purple-500" />
					<span className="text-sm">
						{travelStatT.cardGenerator.preview.calculating()}
					</span>
				</div>
			);
		}

		const TemplateComponent = selectedTemplate.component;
		const { dimensions, scale } = previewData;

		// 创建嵌入式地图组件 - 优化交互性能
		const EmbeddedMap = (
			<div
				className="w-full h-full relative"
				style={{
					// 确保地图容器有正确的层级和交互
					zIndex: 10,
					// 在缩放环境下确保事件正常处理
					transform: "translateZ(0)", // 强制硬件加速
					isolation: "isolate", // 创建独立的堆叠上下文
					// 确保指针事件能够正常工作
					pointerEvents: "auto",
					// 确保内容不被裁切
					overflow: "visible",
				}}
				ref={(element) => {
					// 通过回调ref获取地图实例，确保与 html2canvas-exporter 兼容
					if (element) {
						const mapElement = element.querySelector(
							".mapboxgl-map",
						) as any;
						if (mapElement?._map) {
							const map = mapElement._map;

							// 设置WebGL上下文的preserveDrawingBuffer为true以支持导出
							try {
								const canvas = map.getCanvas();
								const gl =
									canvas.getContext("webgl") ||
									canvas.getContext("experimental-webgl");
								if (
									gl &&
									!gl.getContextAttributes()
										.preserveDrawingBuffer
								) {
									console.log(
										"🔧 [地图设置] 需要重新创建地图实例以启用preserveDrawingBuffer",
									);
									// 注意：理想情况下应该在地图初始化时设置，这里只是标记
								} else if (gl) {
									console.log(
										"✅ [地图设置] WebGL上下文已启用preserveDrawingBuffer",
									);
								}
							} catch (error) {
								console.warn(
									"⚠️ [地图设置] 无法检查WebGL上下文:",
									error,
								);
							}

							// 创建兼容的map引用结构
							mapRef.current = {
								current: map,
								getMap: () => map,
							};
						}
					}
				}}
			>
				<MapContainer />
			</div>
		);

		return (
			<div
				className="relative flex items-center justify-center w-full"
				style={{
					// 让容器高度完全适应卡片实际高度
					height: `${dimensions.height * scale}px`,
					// 水平居中，完全占用可用宽度
					padding: "8px",
				}}
			>
				<div
					ref={cardRef}
					data-card-root
					className="relative shadow-2xl rounded-lg"
					style={{
						transform: `scale(${scale})`,
						transformOrigin: "center center",
						width: `${dimensions.width}px`,
						height: `${dimensions.height}px`,
						transition: "transform 0.3s ease-in-out",
						// 确保不被裁切
						overflow: "visible",
						// 确保缩放后的容器仍能处理事件
						pointerEvents: "auto",
						// 为缩放容器提供独立的渲染上下文
						isolation: "isolate",
					}}
				>
					<TemplateComponent
						mapComponent={EmbeddedMap}
						travelPoints={cardTravelPoints}
						visitedCountries={visitedCountries}
						customization={customization}
						platform={platform}
						isPreview={true}
					/>
				</div>
			</div>
		);
	};

	return (
		<div ref={mainContainerRef} className="w-full min-h-full flex flex-col">
			{/* 顶部工具栏 - 简化显示 */}
			<div className="mb-4 flex items-center justify-between bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-sm flex-shrink-0">
				<div className="flex items-center gap-3">
					<div className="flex items-center gap-2">
						<Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white font-medium">
							{selectedTemplate.name}
						</Badge>
						<Badge className="bg-white border border-gray-300 text-gray-700 font-medium">
							{platform.toUpperCase()}
						</Badge>
					</div>
				</div>

				<div className="flex items-center gap-2">
					{exportStatus === "success" && (
						<div className="flex items-center gap-1 text-green-600 text-sm">
							<CheckCircle className="w-4 h-4" />
							{exportMessage || "导出成功"}
						</div>
					)}
					{exportStatus === "error" && (
						<div className="flex items-center gap-1 text-red-600 text-sm">
							<AlertCircle className="w-4 h-4" />
							{exportMessage || "导出失败"}
						</div>
					)}

					<Button
						size="sm"
						onClick={handleExportCard}
						disabled={isExporting || mapExporting}
						className="bg-purple-600 hover:bg-purple-700 text-white"
					>
						{isExporting || mapExporting ? (
							<Loader2 className="w-4 h-4 animate-spin mr-2" />
						) : (
							<CreditCard className="w-4 h-4 mr-2" />
						)}
						{travelStatT.cardGenerator.preview.exportCard()}
					</Button>
				</div>
			</div>

			{/* 卡片预览区域 - 动态高度，宽度占满 */}
			<div
				ref={previewContainerRef}
				className="flex items-start justify-center flex-1 pt-4"
				style={{
					// 移除overflow限制，让容器自然适应卡片高度
					overflow: "visible",
					// 动态最小高度，基于实际卡片高度加上边距
					minHeight: previewData.actualHeight
						? `${previewData.actualHeight + 40}px`
						: "400px",
				}}
			>
				{renderCardPreview()}
			</div>
		</div>
	);
}
