# 🚀 MapMoment 服务管理指南

## 📋 服务概览

MapMoment 项目包含三个主要服务：

| 服务 | 描述 | 端口 | 包名 |
|------|------|------|------|
| **MapMoment** | 旅行日记主应用 | 3000 | `@repo/web` |
| **AI Images** | AI图片生成器 | 3001 | `@repo/ai-image-generator` |
| **Video Exporter** | 视频导出服务 | 后台服务 | `video-exporter` |

## 🔧 开发环境启动

### 单独启动服务

```bash
# 启动旅行日记主应用 (http://localhost:3000)
pnpm dev:mapmoment

# 启动AI图片生成器 (http://localhost:3001)
pnpm dev:ai-images

# 启动视频导出服务 (后台服务)
pnpm dev:video-exporter

# 同时启动所有服务
pnpm dev:all
```

### 服务访问地址

- **MapMoment 主应用**: http://localhost:3000
- **AI图片生成器**: http://localhost:3001
- **Video Exporter**: 后台服务，无Web界面

## 🏗️ 生产环境部署

### 构建服务

```bash
# 构建单个服务
pnpm build:mapmoment
pnpm build:ai-images
pnpm build:video-exporter

# 构建所有服务
pnpm build:all
```

### 启动生产服务

```bash
# 启动单个服务
pnpm start:mapmoment
pnpm start:ai-images
pnpm start:video-exporter

# 启动所有服务
pnpm start:all
```

## 🗄️ 数据库管理

### 生成数据库类型

```bash
# 生成所有数据库的类型
pnpm db:generate
```

### 数据库迁移

```bash
# 主数据库迁移
pnpm db:migrate --name "migration_name"

# AI图片数据库迁移
pnpm db:migrate:ai migration_name
```

### 数据库管理界面

```bash
# 主数据库管理界面
pnpm db:studio

# AI图片数据库管理界面
pnpm db:studio:ai
```

## 🔄 开发工作流程

### 1. 日常开发

```bash
# 1. 启动需要的服务
pnpm dev:mapmoment    # 开发旅行日记功能
pnpm dev:ai-images    # 开发AI图片功能

# 2. 数据库操作（如需要）
pnpm db:generate      # 生成类型
pnpm db:studio        # 查看数据
```

### 2. 全栈开发

```bash
# 启动所有服务进行全栈开发
pnpm dev:all

# 在另一个终端查看数据库
pnpm db:studio
```

### 3. 功能测试

```bash
# 运行特定测试
pnpm test:google-maps
pnpm test:format-richtext
pnpm test:unified-geocoding
```

## 📦 服务依赖关系

```
┌─────────────────┐    ┌─────────────────┐
│   MapMoment     │    │   AI Images     │
│   (Port 3000)   │    │   (Port 3001)   │
│                 │    │                 │
│ - 主数据库      │    │ - AI图片数据库  │
│ - 认证服务      │    │ - 共享认证      │
│ - 文件存储      │    │ - 共享存储      │
│ - 支付系统      │    │ - 共享支付      │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     │
         ┌─────────────────┐
         │ Video Exporter  │
         │ (Background)    │
         │                 │
         │ - 队列处理      │
         │ - 视频生成      │
         │ - 文件处理      │
         └─────────────────┘
```

## 🌍 环境变量配置

确保在 `.env.local` 中配置以下变量：

```bash
# 数据库配置
DATABASE_URL="postgresql://localhost:5432/travel_memo"
AI_IMAGES_DATABASE_URL="postgresql://localhost:5432/ai_images"

# 服务端口（可选，使用默认值）
PORT=3000                    # MapMoment 主应用
AI_IMAGES_PORT=3001         # AI图片生成器

# API密钥
OPENAI_API_KEY="your-openai-key"
NEXTAUTH_SECRET="your-secret"

# 存储配置
STORAGE_PROVIDER="s3"
AWS_ACCESS_KEY_ID="your-key"
AWS_SECRET_ACCESS_KEY="your-secret"
```

## 🚨 故障排除

### 端口冲突

```bash
# 检查端口占用
netstat -ano | findstr :3000
netstat -ano | findstr :3001

# 或者修改端口
PORT=3002 pnpm dev:mapmoment
```

### 数据库连接问题

```bash
# 检查数据库连接
pnpm db:studio

# 重新生成数据库类型
pnpm db:generate
```

### 依赖问题

```bash
# 清理并重新安装
pnpm clean
pnpm install

# 重新构建
pnpm build:all
```

## 📊 监控和日志

### 开发环境

- 服务日志会直接显示在终端
- 使用浏览器开发者工具查看前端日志
- 数据库查询日志在开发模式下会显示

### 生产环境

- 配置适当的日志记录
- 使用进程管理器（如PM2）管理服务
- 设置健康检查和监控

## 🔄 兼容性说明

为了保持向后兼容，我们保留了原有的脚本：

```bash
# 这些命令仍然可用
pnpm video-exporter    # 等同于 pnpm dev:video-exporter
pnpm dev               # 等同于 pnpm dev:all
pnpm start             # 等同于 pnpm start:all
pnpm build             # 等同于 pnpm build:all
```

## 💡 最佳实践

1. **开发时**：只启动需要的服务，节省资源
2. **测试时**：使用 `dev:all` 启动完整环境
3. **部署时**：分别构建和部署各个服务
4. **监控时**：为每个服务设置独立的健康检查
5. **扩展时**：可以独立扩展不同的服务实例 