import { Button } from "@ui/components/button";
import { Calendar } from "@ui/components/calendar";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { cn } from "@ui/lib";
import { format } from "date-fns";
import { Clock } from "lucide-react";
import { useTranslations } from "next-intl";
import { useId } from "react";
import type { FormFieldProps } from "./types";

export function DateSelector({ formData, setFormData }: FormFieldProps) {
	const dateId = useId();
	const t = useTranslations();

	// 格式化日期显示
	const formatDetailDate = (date: Date) => {
		const localDate = new Date(
			date.getTime() - date.getTimezoneOffset() * 60000,
		);
		return format(localDate, "PPP");
	};

	return (
		<div>
			<label htmlFor={dateId} className="block text-sm font-medium mb-1">
				{t("travelMemo.travelPointForm.labels.date")}
			</label>
			<div className="flex items-center">
				<Clock className="w-5 h-5 mr-2 text-travel-dark/60 flex-shrink-0" />
				<div className="w-full">
					<Popover>
						<PopoverTrigger asChild>
							<Button
								id={dateId}
								variant={"outline"}
								className={cn(
									"w-full justify-start text-left font-normal h-10",
									!formData.date && "text-muted-foreground",
								)}
							>
								{formData.date ? (
									formatDetailDate(formData.date)
								) : (
									<span>
										{t(
											"travelMemo.travelPointForm.placeholders.selectDate",
										)}
									</span>
								)}
							</Button>
						</PopoverTrigger>
						<PopoverContent className="w-auto p-0" align="start">
							<Calendar
								mode="single"
								selected={formData.date}
								onSelect={(date) =>
									setFormData({
										...formData,
										date,
									})
								}
								initialFocus
								className="p-3 pointer-events-auto"
							/>
						</PopoverContent>
					</Popover>
				</div>
			</div>
		</div>
	);
}
