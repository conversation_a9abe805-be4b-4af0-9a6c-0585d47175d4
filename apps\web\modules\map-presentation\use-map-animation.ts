"use client";

import type { AnimationPlaybackControls } from "framer-motion";
import { useCallback, useRef } from "react";
import { createPulseEffect } from "./active-marker-animation";
import { createCircleImageMarker } from "./custom-image-marker";
import {
	createAnimatedRoute,
	createFlowingDashedLine,
	createSolidLineWithSingleArrow,
	generateSmoothPath,
} from "./route-animation-utils";
import type {
	ActiveMarkerState,
	AnimationCallbacks,
	LatLng,
	MapAnimationConfig,
	MapPoint,
	MarkerRef,
	PolylineRef,
} from "./types";
import { formatDate } from "./utils";

/**
 * 默认动画配置
 */
const DEFAULT_CONFIG: Required<MapAnimationConfig> = {
	routeAnimationDuration: 1.5,
	routeColor: "#3B82F6",
	routeWeight: 3,
	markerAnimation: 2, // 对应 google.maps.Animation.DROP
	transitionDelay: 500,
	routeAnimationStyle: "arrow", // 默认使用箭头动画
	useSmoothPath: true, // 默认使用平滑路径
};

/**
 * 地图动画Hook
 *
 * 提供与地图动画相关的功能：
 * - 添加/清除标记点
 * - 绘制/动画展示路线
 * - 地图视角智能调整
 * - 距离计算和最佳缩放级别
 */
export function useMapAnimation(
	mapRef: React.MutableRefObject<any | null>,
	config: MapAnimationConfig = {},
) {
	// 合并默认配置
	const mergedConfig = { ...DEFAULT_CONFIG, ...config };

	// 引用集合
	const markersRef = useRef<MarkerRef[]>([]);
	const polylinesRef = useRef<PolylineRef[]>([]);
	const animationControlsRef = useRef<AnimationPlaybackControls | null>(null);
	const pulseAnimationIdRef = useRef<number | null>(null);
	const activeMarkerRef = useRef<ActiveMarkerState | null>(null); // 跟踪当前活动标记
	const pulseAnimationRef = useRef<number | null>(null); // 保存脉冲动画的requestAnimationFrame ID

	/**
	 * 检查 Google Maps API 是否可用
	 */
	const isGoogleMapsAvailable = useCallback(() => {
		return typeof window !== "undefined" && !!window.google?.maps;
	}, []);

	/**
	 * 清除所有标记点
	 */
	const clearMarkers = useCallback(() => {
		// 停止任何正在进行的脉冲动画
		if (pulseAnimationRef.current) {
			window.cancelAnimationFrame(pulseAnimationRef.current);
			pulseAnimationRef.current = null;
		}

		// 重置活动标记引用
		if (activeMarkerRef.current?.pulseEffect) {
			activeMarkerRef.current.pulseEffect.setMap(null);
		}
		activeMarkerRef.current = null;

		markersRef.current.forEach((marker) => {
			marker.setMap(null);
		});
		markersRef.current = [];
	}, []);

	/**
	 * 清除所有路线
	 */
	const clearPolylines = useCallback(() => {
		polylinesRef.current.forEach((line) => {
			line.setMap(null);
		});
		polylinesRef.current = [];
	}, []);
	/**
	 * 添加标记点
	 */
	const addMarker = useCallback(
		(point: MapPoint) => {
			if (!mapRef.current || !isGoogleMapsAvailable()) {
				return null;
			}

			// 使用自定义圆形图片标记
			const marker = createCircleImageMarker(mapRef.current, point);

			// 添加点击事件，显示详细信息窗口
			marker.addListener("click", () => {
				// 构建信息窗口内容
				let imagesHtml = "";
				if (point.images && point.images.length > 0) {
					imagesHtml = `
            <div style="display: flex; gap: 4px; margin-top: 8px; overflow-x: auto; padding-bottom: 4px;">
              ${point.images
					.map(
						(imgSrc) => `
                  <img src="${imgSrc}" alt="${point.location}" style="width: 60px; height: 40px; object-fit: cover; border-radius: 4px; border: 1px solid #eee;">
                `,
					)
					.join("")}
            </div>
          `;
				}

				const contentString = `
          <div style="padding: 8px; max-width: 250px; font-family: sans-serif;">
            <h3 style="margin: 0 0 4px 0; font-size: 1rem; font-weight: 500;">${point.location}</h3>
            <p style="margin: 0 0 8px 0; font-size: 0.85rem; color: #555; max-height: 60px; overflow-y: auto;">${point.description}</p>
            <p style="margin: 0; font-size: 0.75rem; color: #777;">${formatDate(point.date)}</p>
            ${imagesHtml}
          </div>
        `;

				const infoWindow = new window.google.maps.InfoWindow({
					content: contentString,
				});

				infoWindow.open(mapRef.current, marker);
			});

			markersRef.current.push(marker as any);
			return marker;
		},
		[mapRef, isGoogleMapsAvailable],
	);

	/**
	 * 直接绘制两点之间的路线（无动画）
	 */
	const drawRouteBetweenPoints = useCallback(
		(fromPoint: MapPoint, toPoint: MapPoint) => {
			if (!mapRef.current || !isGoogleMapsAvailable()) {
				return null;
			}

			const polyline = new window.google.maps.Polyline({
				path: [
					{
						lat: fromPoint.coordinates.lat,
						lng: fromPoint.coordinates.lng,
					},
					{
						lat: toPoint.coordinates.lat,
						lng: toPoint.coordinates.lng,
					},
				],
				geodesic: true,
				strokeColor: mergedConfig.routeColor,
				strokeOpacity: 1.0,
				strokeWeight: mergedConfig.routeWeight,
				map: mapRef.current,
			});

			polylinesRef.current.push(polyline);
			return polyline;
		},
		[
			mapRef,
			mergedConfig.routeColor,
			mergedConfig.routeWeight,
			isGoogleMapsAvailable,
		],
	);
	/**
	 * 动画绘制路线
	 */
	const animateRoute = useCallback(
		(fromPoint: MapPoint, toPoint: MapPoint, onComplete?: () => void) => {
			if (!mapRef.current || !isGoogleMapsAvailable()) {
				return null;
			}

			// 验证坐标有效性
			const startLat = fromPoint.coordinates.lat;
			const startLng = fromPoint.coordinates.lng;
			const endLat = toPoint.coordinates.lat;
			const endLng = toPoint.coordinates.lng;

			// 检查坐标是否有效数字
			if (
				!Number.isFinite(startLat) ||
				!Number.isFinite(startLng) ||
				!Number.isFinite(endLat) ||
				!Number.isFinite(endLng)
			) {
				console.error("无效的地图坐标:", { fromPoint, toPoint });
				if (onComplete) {
					onComplete();
				}
				return null;
			}

			// 检查是否有球面几何工具可用于生成平滑路径
			let smoothPath: LatLng[] = [];
			let useSmoothPath = !!window.google?.maps?.geometry?.spherical;

			if (useSmoothPath) {
				// 使用球面插值生成平滑路径
				try {
					// 从路线动画模块导入的函数
					smoothPath = generateSmoothPath(
						{ lat: startLat, lng: startLng },
						{ lat: endLat, lng: endLng },
						20, // 生成20个中间点以获得平滑效果
					);
				} catch (error) {
					console.warn("生成平滑路径失败:", error);
					useSmoothPath = false;
				}
			}

			// 如果无法生成平滑路径，则使用简单的直线路径
			if (!useSmoothPath || smoothPath.length < 2) {
				smoothPath = [
					{ lat: startLat, lng: startLng },
					{ lat: endLat, lng: endLng },
				];
			}

			// 创建初始路线 - 仅包含起点
			const polyline = new window.google.maps.Polyline({
				path: [smoothPath[0]],
				geodesic: true,
				strokeColor: mergedConfig.routeColor,
				strokeOpacity: 1.0,
				strokeWeight: mergedConfig.routeWeight,
				map: mapRef.current,
			});

			polylinesRef.current.push(polyline);

			// 存储动画清理函数
			let cancelAnimation: (() => void) | null = null;
			const animationMode = mergedConfig.routeAnimationStyle || "arrow"; // 默认使用箭头动画

			// 设置路线动画完成时的回调
			const onAnimationComplete = () => {
				// 根据动画模式应用相应的效果
				if (animationMode === "arrow") {
					try {
						// 应用箭头动画效果
						cancelAnimation = createSolidLineWithSingleArrow(
							polyline,
							{
								color: mergedConfig.routeColor,
								arrowSize: 3,
							},
						);
					} catch (error) {
						console.warn("应用箭头动画效果失败:", error);
					}
				} else if (animationMode === "dashed") {
					try {
						// 应用虚线流动动画效果
						cancelAnimation = createFlowingDashedLine(polyline, {
							color: mergedConfig.routeColor,
							speed: 1.5,
							dashLength: 20,
							gapLength: 10,
						});
					} catch (error) {
						console.warn("应用虚线动画效果失败:", error);
					}
				}

				// 调用完成回调
				if (onComplete) {
					onComplete();
				}
			};

			// 直接使用简单的计时器实现路径增长动画
			let startTime: number | null = null;
			const duration = mergedConfig.routeAnimationDuration * 1000; // 转换为毫秒

			// 记录上次有效的进度值，用于在出现无效值时恢复
			let lastValidProgress = 0;
			let currentPathIndex = 0;

			// 创建动画帧函数 - 使用平滑路径点进行动画
			const animateFrame = (timestamp: number) => {
				if (!startTime) {
					startTime = timestamp;
				}

				// 计算动画进度 (0 到 1 之间)
				const elapsed = timestamp - startTime;
				const rawProgress = elapsed / duration;

				// 确保进度在有效范围内
				const progress = Math.max(0, Math.min(1, rawProgress));

				// 如果进度值有效，更新最后有效进度
				if (!Number.isNaN(progress)) {
					lastValidProgress = progress;
				}

				// 使用有效的进度值（当前进度或最后有效进度）
				const safeProgress = Number.isNaN(progress)
					? lastValidProgress
					: progress;

				// 计算当前应该显示的路径点索引
				const targetIndex = Math.floor(
					safeProgress * (smoothPath.length - 1),
				);

				// 如果需要添加更多路径点
				if (targetIndex > currentPathIndex) {
					// 更新当前路径包含所有直到目标索引的点
					const updatedPath = smoothPath.slice(0, targetIndex + 1);
					try {
						polyline.setPath(updatedPath);
						currentPathIndex = targetIndex;
					} catch (error) {
						console.error("设置平滑路径时出错:", error);
					}
				}

				// 如果动画未完成，继续下一帧
				if (rawProgress < 1) {
					animationFrameId =
						window.requestAnimationFrame(animateFrame);
				} else {
					// 动画结束，设置完整路径
					try {
						polyline.setPath(smoothPath);

						// 应用路线完成后的效果
						onAnimationComplete();
					} catch (error) {
						console.error("设置最终路径时出错:", error);
					}
				}
			};

			// 存储动画帧标识以便取消
			let animationFrameId = window.requestAnimationFrame(animateFrame);

			// 创建模拟的动画控制对象
			const controls = {
				stop: () => {
					if (animationFrameId) {
						window.cancelAnimationFrame(animationFrameId);
					}

					// 取消任何活动的路线装饰动画
					if (cancelAnimation) {
						cancelAnimation();
					}
				},
			};

			// 保存动画控制引用
			animationControlsRef.current = controls as any;
			return controls;
		},
		[
			mapRef,
			mergedConfig.routeColor,
			mergedConfig.routeWeight,
			mergedConfig.routeAnimationDuration,
			mergedConfig.routeAnimationStyle, // 添加依赖
			isGoogleMapsAvailable,
		],
	);

	/**
	 * 计算两点之间的距离（公里）
	 */
	const calculateDistance = useCallback(
		(point1: MapPoint, point2: MapPoint): number => {
			if (!point1 || !point2) {
				return 0;
			}

			const lat1 = point1.coordinates.lat;
			const lng1 = point1.coordinates.lng;
			const lat2 = point2.coordinates.lat;
			const lng2 = point2.coordinates.lng;

			// 使用 Haversine 公式计算地球表面两点间的距离
			const toRad = (value: number) => (value * Math.PI) / 180;
			const R = 6371; // 地球半径，单位为公里

			const dLat = toRad(lat2 - lat1);
			const dLng = toRad(lng2 - lng1);

			const a =
				Math.sin(dLat / 2) * Math.sin(dLat / 2) +
				Math.cos(toRad(lat1)) *
					Math.cos(toRad(lat2)) *
					Math.sin(dLng / 2) *
					Math.sin(dLng / 2);

			const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
			const distance = R * c;

			return distance;
		},
		[],
	);

	/**
	 * 根据距离确定最佳缩放级别
	 */
	const getBestZoomForDistance = useCallback((distance: number): number => {
		// 根据经验值设置不同距离对应的缩放级别
		if (distance < 1) {
			return 15; // <1km: 很近
		}
		if (distance < 5) {
			return 13; // 1-5km: 近距离
		}
		if (distance < 20) {
			return 11; // 5-20km: 适中
		}
		if (distance < 50) {
			return 10; // 20-50km: 较远
		}
		if (distance < 100) {
			return 9; // 50-100km: 远
		}
		if (distance < 500) {
			return 7; // 100-500km: 很远
		}
		return 5; // >500km: 超远距离
	}, []);

	/**
	 * 聚焦到两点之间的路线
	 */
	const focusOnRouteBetweenPoints = useCallback(
		(point1: MapPoint, point2: MapPoint, animate = true) => {
			if (
				!mapRef.current ||
				!point1 ||
				!point2 ||
				!isGoogleMapsAvailable()
			) {
				return;
			}

			// 创建一个边界对象
			const bounds = new window.google.maps.LatLngBounds();

			// 将两个点添加到边界中
			bounds.extend(
				new window.google.maps.LatLng(
					point1.coordinates.lat,
					point1.coordinates.lng,
				),
			);
			bounds.extend(
				new window.google.maps.LatLng(
					point2.coordinates.lat,
					point2.coordinates.lng,
				),
			);

			// 计算两点之间的中点
			const midPoint = {
				lat: (point1.coordinates.lat + point2.coordinates.lat) / 2,
				lng: (point1.coordinates.lng + point2.coordinates.lng) / 2,
			};

			// 计算两点之间的距离
			const distance = calculateDistance(point1, point2);

			// 根据距离获取最佳缩放级别
			const zoomLevel = getBestZoomForDistance(distance);

			if (animate) {
				// 先平滑移动到中点
				mapRef.current.panTo(midPoint);

				// 然后应用最佳缩放级别，带延迟以确保平滑过渡
				setTimeout(() => {
					if (mapRef.current) {
						mapRef.current.setZoom(zoomLevel);
					}
				}, mergedConfig.transitionDelay);

				// 在特定情况下调整视角，添加内边距
				// setTimeout(() => {
				// 	if (mapRef.current && distance > 5) {
				// 		// 只有当距离较远时才调整边界，以避免小距离时频繁调整
				// 		mapRef.current.fitBounds(bounds, {
				// 			top: 50, // 像素数
				// 			right: 50,
				// 			bottom: 50,
				// 			left: 50,
				// 		});

				// 		// 确保缩放级别不会过低
				// 		// setTimeout(() => {
				// 		// 	if (
				// 		// 		mapRef.current &&
				// 		// 		mapRef.current.getZoom() > zoomLevel
				// 		// 	) {
				// 		// 		mapRef.current.setZoom(zoomLevel);
				// 		// 	}
				// 		// }, mergedConfig.transitionDelay / 2);
				// 	}
				// }, mergedConfig.transitionDelay * 1.6);
			} else {
				// 非动画模式，直接设置中心和缩放级别
				mapRef.current.setCenter(midPoint);
				mapRef.current.setZoom(zoomLevel);
			}
		},
		[
			mapRef, // 添加依赖
			calculateDistance,
			getBestZoomForDistance,
			mergedConfig.transitionDelay,
			isGoogleMapsAvailable,
		],
	);

	/**
	 * 调整地图视图以适应多个点位
	 */
	const adjustMapViewToPoints = useCallback(
		(points: MapPoint[]) => {
			if (
				!mapRef.current ||
				points.length === 0 ||
				!isGoogleMapsAvailable()
			) {
				return;
			}

			// 创建一个边界对象
			const bounds = new window.google.maps.LatLngBounds();

			// 将所有点添加到边界中
			points.forEach((point) => {
				bounds.extend(
					new window.google.maps.LatLng(
						point.coordinates.lat,
						point.coordinates.lng,
					),
				);
			});

			// 调整地图以适应所有点
			mapRef.current.fitBounds(bounds, 50);

			// 避免过度缩放
			setTimeout(() => {
				if (mapRef.current && mapRef.current.getZoom() > 15) {
					mapRef.current.setZoom(15);
				}
			}, mergedConfig.transitionDelay / 2);
		},
		[mapRef, mergedConfig.transitionDelay, isGoogleMapsAvailable],
	);
	/**
	 * 执行三阶段动画：中点视图->路线动画->终点聚焦
	 */
	const executeThreeStageAnimation = useCallback(
		(
			fromPoint: MapPoint,
			toPoint: MapPoint,
			callbacks?: AnimationCallbacks,
		) => {
			if (!mapRef.current || !isGoogleMapsAvailable()) {
				return;
			}

			// 第1阶段：将视角移动到两点之间的中点并调整缩放级别
			const midPoint = {
				lat: (fromPoint.coordinates.lat + toPoint.coordinates.lat) / 2,
				lng: (fromPoint.coordinates.lng + toPoint.coordinates.lng) / 2,
			};

			// 计算两点之间的距离
			const distance = calculateDistance(fromPoint, toPoint);

			// 根据距离获取最佳缩放级别
			const zoomLevel = getBestZoomForDistance(distance);

			// 移动到中点并设置适当的缩放级别
			mapRef.current.panTo(midPoint);

			// 延迟设置缩放级别，让移动动画先完成
			setTimeout(() => {
				if (!mapRef.current) {
					return;
				}

				mapRef.current.setZoom(zoomLevel);

				if (callbacks?.onMidPointReached) {
					callbacks.onMidPointReached();
				}

				// 第2阶段：等地图加载完成后，开始路线动画
				setTimeout(() => {
					// 添加新标记
					const marker = addMarker(toPoint); // 绘制动画路线，并在动画完成后应用效果
					if (mapRef.current) {
						// 使用新的动画路径函数创建平滑动画效果
						const { polyline, cancel } = createAnimatedRoute(
							{
								lat: fromPoint.coordinates.lat,
								lng: fromPoint.coordinates.lng,
							},
							{
								lat: toPoint.coordinates.lat,
								lng: toPoint.coordinates.lng,
							},
							mapRef.current,
							{
								color: mergedConfig.routeColor || "#3B82F6",
								weight: mergedConfig.routeWeight || 3,
								duration: 1500, // 1.5秒动画
								useSmoothPath: true,
								onComplete: () => {
									// 保存最新的路线引用，供外部函数访问
									if (mapRef.current) {
										mapRef.current.set(
											"latestPolyline",
											polyline,
										);
									}

									// 调用路线动画完成回调
									if (callbacks?.onRouteAnimationComplete) {
										callbacks.onRouteAnimationComplete();
									}

									// 调用目标点聚焦完成回调
									if (callbacks?.onTargetFocusComplete) {
										callbacks.onTargetFocusComplete();
									}
								},
							},
						);

						// 保存最新路线引用
						polylinesRef.current.push(polyline);
					}
				}, mergedConfig.transitionDelay); // 等待地图加载完成
			}, mergedConfig.transitionDelay);
		},
		[
			mapRef,
			calculateDistance,
			getBestZoomForDistance,
			addMarker,
			mergedConfig.routeColor,
			mergedConfig.routeWeight, // 添加依赖
			mergedConfig.transitionDelay,
			isGoogleMapsAvailable,
			polylinesRef,
		],
	);
	/**
	 * 设置当前活动点位
	 */
	const setActiveMarker = useCallback(
		(point: MapPoint, markerIndex?: number) => {
			if (!mapRef.current || !isGoogleMapsAvailable()) {
				return;
			}

			// 查找匹配的标记
			let targetMarker: MarkerRef | undefined;

			if (
				typeof markerIndex === "number" &&
				markerIndex >= 0 &&
				markerIndex < markersRef.current.length
			) {
				// 如果提供了索引，直接使用索引找到标记
				targetMarker = markersRef.current[markerIndex];
			} else {
				// 否则，通过点位的坐标找到标记
				targetMarker = markersRef.current.find((marker) => {
					// 对于高级标记，需要使用 position 属性
					const position = marker.position || marker.getPosition?.();

					// 处理不同类型的标记位置
					const lat =
						typeof position?.lat === "function"
							? position.lat()
							: position?.lat;
					const lng =
						typeof position?.lng === "function"
							? position.lng()
							: position?.lng;

					return (
						lat === point.coordinates.lat &&
						lng === point.coordinates.lng
					);
				});
			}

			if (!targetMarker) {
				return;
			}

			// 更新活动标记引用
			const pulseEffect = createPulseEffect(mapRef.current, point);
			activeMarkerRef.current = {
				marker: targetMarker,
				point,
				pulseEffect,
			};

			return targetMarker;
		},
		[mapRef, isGoogleMapsAvailable],
	);

	return {
		// 标记和路线操作
		addMarker,
		clearMarkers,
		clearPolylines,

		// 路线绘制
		drawRouteBetweenPoints,
		animateRoute,

		// 距离和缩放计算
		calculateDistance,
		getBestZoomForDistance,

		// 视图调整
		focusOnRouteBetweenPoints,
		adjustMapViewToPoints,

		// 高级动画
		executeThreeStageAnimation,

		// 引用
		markersRef,
		polylinesRef,

		// 检查 API 是否可用
		isGoogleMapsAvailable,

		// 高亮动画
		setActiveMarker,
	};
}
