"use client";

import { Badge } from "@ui/components/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Check, Eye } from "lucide-react";
import { cardTemplates } from "./templates";
import type { CardTemplate } from "./types/cardTypes";

interface TemplateSelectorProps {
	selectedTemplate: string;
	onTemplateSelect: (templateId: string) => void;
	className?: string;
}

export function TemplateSelector({
	selectedTemplate,
	onTemplateSelect,
	className = "",
}: TemplateSelectorProps) {
	// 现在显示所有模板，不再需要分类过滤
	const filteredTemplates = cardTemplates;

	return (
		<Card className={`template-selector ${className}`}>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Eye className="w-5 h-5" />
					选择卡片模板
				</CardTitle>
				<p className="text-sm text-gray-600">
					选择一个你喜欢的模板风格，开始创建专属的旅行分享卡片
				</p>
			</CardHeader>

			<CardContent className="space-y-6">
				{/* 模板网格 */}
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
					{filteredTemplates.map((template) => (
						<TemplateCard
							key={template.id}
							template={template}
							isSelected={selectedTemplate === template.id}
							onSelect={() => onTemplateSelect(template.id)}
						/>
					))}
				</div>

				{filteredTemplates.length === 0 && (
					<div className="text-center py-8 text-gray-500">
						该分类下暂无模板
					</div>
				)}
			</CardContent>
		</Card>
	);
}

// 模板卡片组件
interface TemplateCardProps {
	template: CardTemplate;
	isSelected: boolean;
	onSelect: () => void;
}

function TemplateCard({ template, isSelected, onSelect }: TemplateCardProps) {
	// 获取模板类型
	const getTemplateCategoryName = (id: string) => {
		const categories: Record<string, string> = {
			minimal: "极简",
			vibrant: "活力",
			elegant: "优雅",
			retro: "复古",
		};
		return categories[id] || "模板";
	};

	return (
		<button
			type="button"
			className={`template-card relative cursor-pointer transition-all duration-200 w-full ${
				isSelected
					? "ring-2 ring-blue-500 ring-offset-2"
					: "hover:shadow-md"
			}`}
			onClick={onSelect}
		>
			<Card className="h-full">
				{/* 预览图区域 */}
				<div className="relative aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 rounded-t-lg overflow-hidden">
					{/* 预览占位图 */}
					<div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
						<div className="text-center space-y-2">
							<div className="text-4xl mb-2">
								{{
									minimal: "📊",
									vibrant: "🌈",
									elegant: "👑",
									retro: "📸",
								}[template.id] || "📋"}
							</div>
							<div className="text-sm text-gray-500">
								{template.name}预览
							</div>
						</div>
					</div>

					{/* 选中指示器 */}
					{isSelected && (
						<div className="absolute top-2 right-2">
							<div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
								<Check className="w-4 h-4 text-white" />
							</div>
						</div>
					)}

					{/* 分类标签 */}
					<div className="absolute top-2 left-2">
						<Badge className="bg-white/90 text-gray-700 text-xs">
							{getTemplateCategoryName(template.id)}
						</Badge>
					</div>
				</div>

				{/* 模板信息 */}
				<CardContent className="p-4">
					<div className="space-y-2">
						<div className="flex items-center justify-between">
							<h3 className="font-medium text-gray-900">
								{template.name}
							</h3>
							{template.id === "minimal" && (
								<Badge className="text-xs">推荐</Badge>
							)}
						</div>

						<p className="text-sm text-gray-600 line-clamp-2">
							{template.description}
						</p>

						{/* 特性标签 */}
						<div className="flex flex-wrap gap-1 pt-2">
							{template.features
								.slice(0, 3)
								.map((feature, index) => (
									<Badge key={index} className="text-xs">
										{feature}
									</Badge>
								))}
							{template.features.length > 3 && (
								<Badge className="text-xs">
									+{template.features.length - 3}
								</Badge>
							)}
						</div>
					</div>
				</CardContent>
			</Card>
		</button>
	);
}
