import { LocaleLink } from "@i18n/routing";
import { <PERSON><PERSON> } from "@ui/components/button";
import Image from "next/image";
import Link from "next/link";
import heroImageDark from "../../../../public/images/hero-image-dark.png";
import heroImage from "../../../../public/images/hero-image.png";

export function Hero() {
	return (
		<div className="relative max-w-full overflow-x-hidden bg-linear-to-b from-0% from-card to-[50vh] to-background">
			<div className="absolute left-1/2 z-10 ml-[-500px] h-[500px] w-[1000px] rounded-full bg-linear-to-r from-primary to-bg opacity-20 blur-[150px]" />
			<div className="container relative z-20 pt-44 pb-12 text-center lg:pb-16">
				<div className="container pt-44 pb-12 text-center lg:pb-16">
					<div className="mb-4 flex justify-center">
						<div className="mx-auto flex flex-wrap items-center justify-center rounded-full border border-highlight/30 bg-card/80 backdrop-blur-sm p-px px-4 py-1 font-normal text-highlight text-sm shadow-sm">
							<span className="flex items-center gap-2 rounded-full font-semibold text-highlight">
								<span className="size-2 rounded-full bg-highlight animate-pulse" />
								新功能:
							</span>
							<span className="ml-1 block font-medium text-foreground/60">
								智能地图足迹自动记录上线
							</span>
						</div>
					</div>

					<h1 className="mx-auto max-w-3xl text-balance font-bold text-5xl lg:text-7xl text-white drop-shadow-lg">
						在地图上绘制你的旅行足迹
					</h1>

					<p className="mx-auto mt-4 max-w-lg text-balance text-white/80 text-lg drop-shadow-md">
						每一次出发都是一个故事的开始。用智能地图记录你的足迹，
						让旅行的美好时光永远不会褪色。
					</p>

					<div className="mt-6 flex flex-col items-center justify-center gap-3 md:flex-row">
						<Button
							size="lg"
							variant="primary"
							className="bg-primary/90 backdrop-blur-sm hover:bg-primary shadow-lg"
						>
							<Link href="/auth/login">开始记录足迹</Link>
						</Button>
						<Button
							variant="outline"
							size="lg"
							className="border-white/30 bg-white/10 backdrop-blur-sm text-white hover:bg-white/20"
						>
							<LocaleLink href="/docs">了解更多</LocaleLink>
						</Button>
					</div>

					<div className="mt-16 px-8 text-center">
						<h5 className="font-semibold text-white/50 text-xs uppercase tracking-wider">
							专为旅行者打造，基于现代技术构建
						</h5>

						<div className="mt-4 flex flex-col-reverse items-center justify-center gap-4 text-white/50 md:flex-row md:gap-8">
							<div className="text-sm font-medium">
								React · Next.js · TypeScript · Tailwind CSS
							</div>
						</div>
					</div>

					<div className="mx-auto mt-16 max-w-5xl rounded-2xl border border-white/20 bg-card/20 backdrop-blur-sm p-2 shadow-lg">
						<Image
							src={heroImage}
							alt="旅行足迹地图界面预览"
							className="block rounded-xl dark:hidden opacity-90"
							priority
						/>
						<Image
							src={heroImageDark}
							alt="旅行足迹地图界面预览"
							className="hidden rounded-xl dark:block opacity-90"
							priority
						/>
					</div>
				</div>
			</div>
		</div>
	);
}
