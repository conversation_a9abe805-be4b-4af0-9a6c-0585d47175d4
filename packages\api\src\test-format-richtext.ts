/**
 * 测试富文本格式化功能
 * 这个脚本用于验证我们的AI分析、地理编码和数据生成功能
 *
 * 运行方式：
 * 方式1（推荐）：npm run test:format-richtext
 * 方式2：cd packages/api && tsx src/test-format-richtext.ts
 */

import { resolve } from "node:path";
// 手动加载环境变量
import { config } from "dotenv";

// 尝试加载根目录的环境变量文件
const envFiles = [".env.local", ".env"];
for (const envFile of envFiles) {
	try {
		const envPath = resolve(process.cwd(), "../../", envFile);
		config({ path: envPath });
		console.log(`✅ 已加载环境变量文件: ${envFile}`);
		break;
	} catch (error) {
		// 继续尝试下一个文件
	}
}

// 使用动态导入来避免ES模块问题
async function runTest() {
	console.log("🚀 开始测试富文本格式化功能...\n");

	try {
		// 动态导入模块
		const { RichTextAnalyzer } = await import(
			"./services/richtext-analyzer"
		);
		const { TravelAIAnalyzer } = await import(
			"./services/travel-ai-analyzer"
		);
		const { createChinaGeocodingService } = await import(
			"./services/geocoding-service"
		);
		const { TravelDiaryGenerator } = await import(
			"./services/travel-diary-generator"
		);

		// 测试用的富文本内容
		const testContent = {
			type: "doc",
			content: [
				{
					type: "paragraph",
					content: [
						{
							type: "text",
							text: "今天去了北京天安门广场，感受了首都的庄严氛围。",
						},
					],
				},
				{
					type: "paragraph",
					content: [
						{
							type: "text",
							text: "下午在王府井大街购物，买了一些特色纪念品。",
						},
					],
				},
				{
					type: "paragraph",
					content: [
						{
							type: "text",
							text: "晚上登上了长城，欣赏了壮丽的夜景。",
						},
					],
				},
			],
		};

		// 1. 测试富文本分析
		console.log("📝 测试富文本分析...");
		const analysisResult = RichTextAnalyzer.analyzeContent(testContent);
		console.log("✅ 富文本分析成功:", {
			textLength: analysisResult.textContent.length,
			imageCount: analysisResult.imageUrls.length,
			blockCount: analysisResult.contentBlocks.length,
		});

		// 2. 测试地理编码服务
		console.log("\n🗺️ 测试百度地图地理编码服务...");

		// 检查是否有百度地图API密钥
		if (!process.env.BAIDU_MAPS_API_KEY) {
			console.log(
				"⚠️ 跳过地理编码测试 - 未配置BAIDU_MAPS_API_KEY环境变量",
			);
		} else {
			// 使用中国优化的地理编码服务，优先使用百度地图
			const geocodingService = createChinaGeocodingService({
				baidu: process.env.BAIDU_MAPS_API_KEY,
				amap: process.env.AMAP_API_KEY,
				google: process.env.GOOGLE_MAPS_API_KEY,
			});

			// 测试地理编码
			const testAddress = "北京天安门广场";
			console.log(`🔍 测试地理编码: ${testAddress}`);

			const geocodeResult = await geocodingService.smartGeocode(
				testAddress,
				{
					language: "zh-CN",
					region: "CN",
				},
			);

			if (geocodeResult) {
				console.log("✅ 地理编码成功:", {
					address: geocodeResult.address,
					coordinates: `${geocodeResult.longitude}, ${geocodeResult.latitude}`,
					confidence: geocodeResult.confidence,
					country: geocodeResult.country,
					city: geocodeResult.city,
				});

				// 测试反向地理编码
				console.log(
					`🔄 测试反向地理编码: ${geocodeResult.longitude}, ${geocodeResult.latitude}`,
				);
				const reverseResult = await geocodingService.reverseGeocode(
					geocodeResult.longitude,
					geocodeResult.latitude,
					{ language: "zh-CN" },
				);

				if (reverseResult) {
					console.log("✅ 反向地理编码成功:", {
						address: reverseResult.address,
						confidence: reverseResult.confidence,
					});
				} else {
					console.log("❌ 反向地理编码失败");
				}

				// 测试批量地理编码
				console.log("\n📦 测试批量地理编码...");
				const batchAddresses = [
					"北京天安门广场",
					"上海外滩",
					"广州珠江新城",
				];

				const batchResults = await geocodingService.batchGeocode(
					batchAddresses,
					{ language: "zh-CN", region: "CN" },
				);

				console.log("✅ 批量地理编码完成:");
				batchResults.forEach((result, index) => {
					if (result.result) {
						console.log(
							`  ${index + 1}. ${result.address} -> ${result.result.address} (${result.result.confidence})`,
						);
					} else {
						console.log(
							`  ${index + 1}. ${result.address} -> 失败`,
						);
					}
				});

				// 显示统计信息
				console.log("\n📊 地理编码服务统计:");
				const stats = geocodingService.getStats();
				console.log(`总请求数: ${stats.totalRequests}`);
				console.log(`成功请求数: ${stats.successfulRequests}`);
				console.log(`失败请求数: ${stats.failedRequests}`);
				console.log(
					`平均响应时间: ${Math.round(stats.averageResponseTime)}ms`,
				);
				console.log(
					`成功率: ${((stats.successfulRequests / stats.totalRequests) * 100).toFixed(2)}%`,
				);
			} else {
				console.log("❌ 地理编码失败");
			}
		}

		// 3. 测试AI分析
		console.log("\n🤖 测试AI分析...");

		// 创建地理编码服务用于AI分析
		const geocodingService = createChinaGeocodingService({
			baidu: process.env.BAIDU_MAPS_API_KEY || "test",
			amap: process.env.AMAP_API_KEY,
		});

		// 配置火山引擎AI分析器
		const aiAnalyzer = new TravelAIAnalyzer({
			provider: "volcengine",
			apiKey: process.env.VOLCENGINE_API_KEY,
			secretKey: process.env.VOLCENGINE_SECRET_KEY,
		});

		try {
			const travelInfo = await aiAnalyzer.analyzeText(
				analysisResult.textContent,
			);

			console.log("✅ AI分析成功:", {
				locationsCount: travelInfo.locations.length,
				timesCount: travelInfo.timeInfo.length,
				activitiesCount: travelInfo.activities.length,
				summary: `${travelInfo.summary?.substring(0, 100)}...`,
			});

			// 显示分析的地点信息
			if (travelInfo.locations.length > 0) {
				console.log("\n📍 识别的地点:");
				travelInfo.locations.forEach((location, index) => {
					console.log(
						`  ${index + 1}. ${location.name} (${location.type}, 置信度: ${location.confidence})`,
					);
					if (location.coordinates) {
						console.log(
							`     坐标: ${location.coordinates.longitude}, ${location.coordinates.latitude}`,
						);
					}
					if (location.address) {
						console.log(`     地址: ${location.address}`);
					}
				});
			}

			// 显示分析的活动信息
			if (travelInfo.activities.length > 0) {
				console.log("\n🎯 识别的活动:");
				travelInfo.activities.forEach((activity, index) => {
					console.log(
						`  ${index + 1}. ${activity.description} (${activity.type})`,
					);
					if (activity.location) {
						console.log(`     地点: ${activity.location}`);
					}
				});
			}

			// 4. 测试旅行日记生成
			console.log("\n📖 测试旅行日记生成...");
			const diaryData =
				TravelDiaryGenerator.generateFromTravelInfo(travelInfo);

			console.log("✅ 旅行日记生成成功:", {
				timelinesCount: diaryData.timelines.length,
				pointsCount: diaryData.timelines.reduce(
					(sum: number, timeline: any) =>
						sum + timeline.points.length,
					0,
				),
			});

			// 显示生成的时间线
			diaryData.timelines.forEach((timeline: any, index: number) => {
				console.log(
					`  时间线 ${index + 1}: ${timeline.title} (${timeline.points.length} 个点位)`,
				);
				timeline.points.forEach((point: any, pointIndex: number) => {
					console.log(
						`    点位 ${pointIndex + 1}: ${point.location} - ${point.description}`,
					);
				});
			});
		} catch (aiError) {
			console.log(
				"⚠️ AI分析跳过:",
				aiError instanceof Error ? aiError.message : String(aiError),
			);
			console.log("这可能是因为AI服务配置问题，但地理编码功能正常工作");
		}

		console.log("\n🎉 所有测试完成！");
	} catch (error) {
		console.error("❌ 测试失败:", error);
		process.exit(1);
	}
}

// 运行测试
runTest();
