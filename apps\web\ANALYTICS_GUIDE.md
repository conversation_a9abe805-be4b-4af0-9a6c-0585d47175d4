# 分析功能集成指南

## 🎯 概述

本指南介绍如何在旅行日记应用中正确使用和配置分析功能，追踪用户行为以优化产品体验。

## 🚀 快速开始

### 1. 环境配置

```bash
# .env.local
NEXT_PUBLIC_PLAUSIBLE_URL=yourdomain.com
```

### 2. 基础使用

```typescript
import { analytics } from '@/app/utils/analytics';

// 追踪日记创建
analytics.diary.created(diaryId);

// 追踪点位添加
analytics.point.added(iconType, hasImages, location);

// 追踪功能使用
analytics.feature.used('presentation_mode');
```

## 📊 关键指标追踪

### 核心用户行为
- **日记创建/编辑/保存**：了解用户内容创作习惯
- **点位操作**：分析用户如何记录旅行轨迹
- **演示模式使用**：评估核心功能价值
- **地图交互**：优化地图用户体验

### 功能使用统计
- **富文本编辑器使用率**
- **图片上传功能使用**
- **视频导出功能使用**
- **模式切换行为**

## 🏗️ 实施位置

### 1. 日记编辑器 (`DiaryEditor.tsx`)
```typescript
// 已集成：
// - 日记保存追踪（自动/手动）
// - 模式切换追踪
// - 错误处理追踪
```

### 2. 点位管理 (`TravelPointList.tsx`)
```typescript
// 已集成：
// - 点位复制追踪
// - 点位删除追踪
// 
// 待添加：
// - 点位添加追踪
// - 点位编辑追踪
```

### 3. 演示模式 (`MapStoryPage.tsx`)
```typescript
// 已集成：
// - 演示开始追踪
// - 演示跳过追踪
//
// 待添加：
// - 演示完成追踪
// - 演示时长追踪
```

## 📈 推荐分析策略

### MVP 阶段关注指标

1. **用户激活**
   - 创建首个日记的用户比例
   - 添加首个点位的用户比例
   - 完成首次演示的用户比例

2. **功能采用率**
   - 各个功能的使用频率
   - 用户偏好的点位类型
   - 演示模式的使用模式

3. **用户留存**
   - 7天内回访率
   - 平均创建日记数量
   - 平均点位数量

### 隐私保护措施

- ✅ 不收集个人身份信息
- ✅ 地理位置只记录城市级别
- ✅ 使用无Cookie的Plausible Analytics
- ✅ 符合GDPR合规要求

## 🔧 高级配置

### 自定义事件追踪

```typescript
import { trackEvent } from '@/app/utils/analytics';

// 自定义事件
trackEvent('custom_feature_used', {
  feature_name: 'advanced_filter',
  user_type: 'premium',
  usage_count: 1
});
```

### 错误追踪

```typescript
import { analytics } from '@/app/utils/analytics';

try {
  // 业务逻辑
} catch (error) {
  analytics.error.encountered(
    'diary_save_error',
    error.message,
    'DiaryEditor'
  );
}
```

### 性能追踪

```typescript
// 追踪长时间操作
const startTime = Date.now();
await saveDiary();
const duration = Date.now() - startTime;

trackEvent('performance_metric', {
  operation: 'diary_save',
  duration_ms: duration
});
```

## 📋 部署检查清单

- [ ] 环境变量已配置
- [ ] Plausible域名已设置
- [ ] 分析脚本已集成到Layout
- [ ] 关键用户路径已添加追踪
- [ ] 错误处理已添加分析
- [ ] 隐私政策已更新

## 🔍 监控和优化

### 日常监控指标

1. **每日活跃用户 (DAU)**
2. **日记创建率**
3. **功能使用分布**
4. **用户会话时长**
5. **错误发生率**

### 优化建议

- 定期回顾最受欢迎的功能
- 识别用户流失点进行优化
- A/B测试新功能的采用率
- 根据地理数据优化本地化

## 🚨 注意事项

1. **数据质量**：确保事件命名一致性
2. **性能影响**：避免过度追踪影响性能
3. **隐私合规**：定期审查数据收集范围
4. **错误处理**：分析服务不可用时的降级方案

## 📞 支持

遇到问题时，请检查：
1. 浏览器控制台是否有错误
2. Plausible脚本是否正确加载
3. 环境变量是否正确配置
4. 域名是否在Plausible中正确设置 