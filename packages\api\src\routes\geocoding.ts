/**
 * 地理编码API路由
 * 提供地理编码服务的HTTP接口
 */

import { zValidator } from "@hono/zod-validator";
import { logger } from "@repo/logs";
import { Hono } from "hono";
import { z } from "zod";
import { GeocodingService } from "../services/geocoding-service";

const app = new Hono();

// 创建地理编码服务实例
const geocodingService = new GeocodingService();

/**
 * 地理编码请求参数验证
 */
const geocodeSchema = z.object({
	address: z.string().min(1, "地址不能为空"),
	language: z.string().optional(),
	region: z.string().optional(),
	country: z.string().optional(),
	limit: z.number().int().min(1).max(10).optional(),
});

/**
 * 反向地理编码请求参数验证
 */
const reverseGeocodeSchema = z.object({
	longitude: z.number().min(-180).max(180),
	latitude: z.number().min(-90).max(90),
	language: z.string().optional(),
	region: z.string().optional(),
});

/**
 * 批量地理编码请求参数验证
 */
const batchGeocodeSchema = z.object({
	addresses: z.array(z.string().min(1)).min(1).max(20, "最多支持20个地址"),
	language: z.string().optional(),
	region: z.string().optional(),
	country: z.string().optional(),
});

/**
 * POST /geocode - 地理编码（地址转坐标）
 */
app.post("/geocode", zValidator("json", geocodeSchema), async (c) => {
	try {
		const { address, ...options } = c.req.valid("json");

		logger.info("收到地理编码请求", { address, options });

		const result = await geocodingService.geocode(address, options);

		if (result) {
			return c.json({
				success: true,
				data: result,
			});
		}

		return c.json(
			{
				success: false,
				error: "未找到地理编码结果",
			},
			404,
		);
	} catch (error) {
		logger.error("地理编码API错误", {
			error: error instanceof Error ? error.message : String(error),
		});

		return c.json(
			{
				success: false,
				error: "地理编码服务错误",
			},
			500,
		);
	}
});

/**
 * POST /reverse-geocode - 反向地理编码（坐标转地址）
 */
app.post(
	"/reverse-geocode",
	zValidator("json", reverseGeocodeSchema),
	async (c) => {
		try {
			const { longitude, latitude, ...options } = c.req.valid("json");

			logger.info("收到反向地理编码请求", {
				longitude,
				latitude,
				options,
			});

			const result = await geocodingService.reverseGeocode(
				longitude,
				latitude,
				options,
			);

			if (result) {
				return c.json({
					success: true,
					data: result,
				});
			}

			return c.json(
				{
					success: false,
					error: "未找到反向地理编码结果",
				},
				404,
			);
		} catch (error) {
			logger.error("反向地理编码API错误", {
				error: error instanceof Error ? error.message : String(error),
			});

			return c.json(
				{
					success: false,
					error: "反向地理编码服务错误",
				},
				500,
			);
		}
	},
);

/**
 * POST /batch-geocode - 批量地理编码
 */
app.post(
	"/batch-geocode",
	zValidator("json", batchGeocodeSchema),
	async (c) => {
		try {
			const { addresses, ...options } = c.req.valid("json");

			logger.info("收到批量地理编码请求", {
				addressesCount: addresses.length,
				options,
			});

			const results = await geocodingService.batchGeocode(
				addresses,
				options,
			);

			return c.json({
				success: true,
				data: results,
				summary: {
					total: results.length,
					successful: results.filter((r) => r.result !== null).length,
					failed: results.filter((r) => r.result === null).length,
				},
			});
		} catch (error) {
			logger.error("批量地理编码API错误", {
				error: error instanceof Error ? error.message : String(error),
			});

			return c.json(
				{
					success: false,
					error: "批量地理编码服务错误",
				},
				500,
			);
		}
	},
);

/**
 * POST /smart-geocode - 智能地理编码
 */
app.post("/smart-geocode", zValidator("json", geocodeSchema), async (c) => {
	try {
		const { address, ...options } = c.req.valid("json");

		logger.info("收到智能地理编码请求", { address, options });

		const result = await geocodingService.smartGeocode(address, options);

		if (result) {
			return c.json({
				success: true,
				data: result,
			});
		}

		return c.json(
			{
				success: false,
				error: "未找到智能地理编码结果",
			},
			404,
		);
	} catch (error) {
		logger.error("智能地理编码API错误", {
			error: error instanceof Error ? error.message : String(error),
		});

		return c.json(
			{
				success: false,
				error: "智能地理编码服务错误",
			},
			500,
		);
	}
});

/**
 * GET /stats - 获取服务统计信息
 */
app.get("/stats", async (c) => {
	try {
		const stats = geocodingService.getStats();

		return c.json({
			success: true,
			data: stats,
		});
	} catch (error) {
		logger.error("获取地理编码统计信息错误", {
			error: error instanceof Error ? error.message : String(error),
		});

		return c.json(
			{
				success: false,
				error: "获取统计信息失败",
			},
			500,
		);
	}
});

/**
 * POST /clear-cache - 清除缓存
 */
app.post("/clear-cache", async (c) => {
	try {
		geocodingService.clearCache();

		return c.json({
			success: true,
			message: "缓存已清除",
		});
	} catch (error) {
		logger.error("清除地理编码缓存错误", {
			error: error instanceof Error ? error.message : String(error),
		});

		return c.json(
			{
				success: false,
				error: "清除缓存失败",
			},
			500,
		);
	}
});

/**
 * POST /reset-stats - 重置统计信息
 */
app.post("/reset-stats", async (c) => {
	try {
		geocodingService.resetStats();

		return c.json({
			success: true,
			message: "统计信息已重置",
		});
	} catch (error) {
		logger.error("重置地理编码统计信息错误", {
			error: error instanceof Error ? error.message : String(error),
		});

		return c.json(
			{
				success: false,
				error: "重置统计信息失败",
			},
			500,
		);
	}
});

/**
 * GET /cost-analysis - 获取成本分析报告
 */
app.get("/cost-analysis", async (c) => {
	try {
		const unifiedService = geocodingService.getUnifiedService();
		const costAnalysis = unifiedService.getCostAnalysis();

		return c.json({
			success: true,
			data: costAnalysis,
		});
	} catch (error) {
		logger.error("获取成本分析报告错误", {
			error: error instanceof Error ? error.message : String(error),
		});

		return c.json(
			{
				success: false,
				error: "获取成本分析失败",
			},
			500,
		);
	}
});

/**
 * GET /health - 健康检查
 */
app.get("/health", async (c) => {
	try {
		const stats = geocodingService.getStats();
		const unifiedService = geocodingService.getUnifiedService();
		const loadBalancerStats = unifiedService.getStats();

		return c.json({
			success: true,
			status: "healthy",
			data: {
				totalProviders: Object.keys(loadBalancerStats.providerStats)
					.length,
				availableProviders: Object.values(
					loadBalancerStats.providerStats,
				).filter((stat: any) => stat.requests > 0 || stat.successes > 0)
					.length,
				totalRequests: stats.totalRequests,
				successRate:
					stats.totalRequests > 0
						? `${((stats.successfulRequests / stats.totalRequests) * 100).toFixed(2)}%`
						: "0%",
				averageResponseTime: `${Math.round(stats.averageResponseTime)}ms`,
			},
		});
	} catch (error) {
		logger.error("地理编码健康检查错误", {
			error: error instanceof Error ? error.message : String(error),
		});

		return c.json(
			{
				success: false,
				status: "unhealthy",
				error: "健康检查失败",
			},
			500,
		);
	}
});

export default app;
