"use client";

import { Search } from "lucide-react";
import { useTranslations } from "next-intl";
import dynamic from "next/dynamic";
import { useCallback, useEffect, useRef, useState } from "react";

// 动态导入SearchBox组件，禁用SSR
const MapboxSearchBox = dynamic(
	() => import("@mapbox/search-js-react").then((mod) => mod.SearchBox),
	{ ssr: false },
);

interface StandaloneSearchBoxProps {
	onPlaceSelect: (place: any) => void;
	onInputChange?: (value: string) => void;
	containerClassName?: string;
	initialValue?: string;
}

export function StandaloneSearchBox({
	onPlaceSelect,
	onInputChange,
	containerClassName = "relative w-full",
	initialValue = "",
}: StandaloneSearchBoxProps) {
	const t = useTranslations();
	const [inputValue, setInputValue] = useState(initialValue);
	const searchBoxRef = useRef<any>(null);

	// 添加调试日志 - 组件挂载和关键状态变化
	useEffect(() => {
		console.log("[StandaloneSearchBox] 组件挂载/更新", {
			initialValue,
			inputValue,
			searchBoxRef: searchBoxRef.current,
		});
	}, [initialValue, inputValue]);

	useEffect(() => {
		console.log("[StandaloneSearchBox] initialValue 变化检测", {
			initialValue,
			currentInputValue: inputValue,
			shouldUpdate: initialValue !== inputValue,
		});

		if (initialValue !== inputValue) {
			console.log(
				`[StandaloneSearchBox] 同步 inputValue: "${inputValue}" -> "${initialValue}"`,
			);
			setInputValue(initialValue);
		}
	}, [initialValue]);

	// 轻量级的触摸事件修复，只针对 popover 选项
	useEffect(() => {
		let timeoutId: NodeJS.Timeout;

		const handleTouchFix = () => {
			// 延迟查找下拉选项，确保它们已经渲染
			timeoutId = setTimeout(() => {
				// 只查找最常见的建议选项
				const suggestions =
					document.querySelectorAll('[role="option"]');

				suggestions.forEach((suggestion: any) => {
					if (!suggestion.hasAttribute("data-touch-fixed")) {
						// 标记已处理，避免重复添加
						suggestion.setAttribute("data-touch-fixed", "true");

						// 简单的触摸事件转换
						suggestion.addEventListener(
							"touchend",
							(e: TouchEvent) => {
								e.preventDefault();
								console.log(
									"[StandaloneSearchBox] 触摸选择项目",
								);

								// 触发点击事件
								suggestion.click();
							},
							{ passive: false },
						);
					}
				});

				if (suggestions.length > 0) {
					console.log(
						`[StandaloneSearchBox] 为 ${suggestions.length} 个选项添加了触摸支持`,
					);
				}
			}, 200);
		};

		// 监听DOM变化，但只关注重要变化
		const observer = new MutationObserver((mutations) => {
			// 只在有新的节点添加时才处理
			const hasNewNodes = mutations.some(
				(mutation) => mutation.addedNodes.length > 0,
			);
			if (hasNewNodes) {
				handleTouchFix();
			}
		});

		if (searchBoxRef.current) {
			observer.observe(document.body, {
				childList: true,
				subtree: true,
			});
		}

		return () => {
			observer.disconnect();
			if (timeoutId) {
				clearTimeout(timeoutId);
			}
		};
	}, []);

	// 使用官方 Theme API 来自定义样式
	const customTheme = {
		variables: {
			border: "none",
			boxShadow: "none",
			borderRadius: "0.5rem",
			colorBackground: "#fafafe",
			padding: "0 0 0 2.5rem",
			unit: "0.875rem", // 14px
			lineHeight: "1.25rem",
		},
		icons: {
			search: "", // 设置为空字符串来隐藏搜索图标
		},
	};

	const handleInternalInputChange = useCallback(
		(value: string) => {
			console.log(
				`[StandaloneSearchBox] 内部输入变化: "${inputValue}" -> "${value}"`,
			);
			setInputValue(value);
			if (onInputChange) {
				console.log("[StandaloneSearchBox] 调用 onInputChange 回调");
				onInputChange(value);
			}
		},
		[onInputChange, inputValue],
	);

	const handleRetrieve = useCallback(
		(res: any) => {
			console.log("[StandaloneSearchBox] handleRetrieve 开始", {
				response: res,
				features: res?.features,
			});

			if (res?.features && res.features.length > 0) {
				const selectedPlace = res.features[0];
				console.log(
					"[StandaloneSearchBox] 选中的地点特征",
					selectedPlace,
				);

				const coordinates = selectedPlace.geometry.coordinates;
				console.log("[StandaloneSearchBox] 地点坐标", coordinates);

				const addressInfo = extractAddressInfo(selectedPlace);
				console.log(
					"[StandaloneSearchBox] 解析的地址信息",
					addressInfo,
				);

				const transformedPlace = {
					geometry: {
						location: {
							lat: () => coordinates[1],
							lng: () => coordinates[0],
						},
					},
					name:
						selectedPlace.properties?.name ||
						addressInfo.name ||
						"",
					formatted_address:
						selectedPlace.properties?.full_address ||
						addressInfo.formattedAddress ||
						"",
					address_components: parseAddressComponents(
						selectedPlace,
						addressInfo,
					),
					mapbox_data: {
						country: addressInfo.country,
						region: addressInfo.region,
						place: addressInfo.place,
						poi_category:
							selectedPlace.properties?.poi_category || [],
						poi_category_ids:
							selectedPlace.properties?.poi_category_ids || [],
						feature_type:
							selectedPlace.properties?.feature_type || "",
						maki: selectedPlace.properties?.maki || "",
						brand: selectedPlace.properties?.brand || [],
					},
				};

				console.log(
					"[StandaloneSearchBox] 转换后的地点数据",
					transformedPlace,
				);
				console.log(
					"[StandaloneSearchBox] 准备调用 onPlaceSelect 回调",
				);
				onPlaceSelect(transformedPlace);
			} else {
				console.warn(
					"[StandaloneSearchBox] 没有找到有效的地点特征",
					res,
				);
			}
		},
		[onPlaceSelect],
	);

	const extractAddressInfo = (feature: any) => {
		console.log("[StandaloneSearchBox] extractAddressInfo 开始", feature);

		const info = {
			name: "",
			formattedAddress: "",
			country: null as any,
			region: null as any,
			place: null as any,
		};

		info.name = feature.properties?.name || "";

		info.formattedAddress =
			feature.properties?.full_address ||
			feature.properties?.address ||
			feature.properties?.place_formatted ||
			"";

		if (feature.properties?.context) {
			console.log(
				"[StandaloneSearchBox] 使用 properties.context 解析",
				feature.properties.context,
			);

			if (feature.properties.context.country) {
				info.country = {
					name: feature.properties.context.country.name || "",
					country_code:
						feature.properties.context.country.country_code || "",
				};
			}

			if (feature.properties.context.region) {
				info.region = {
					name: feature.properties.context.region.name || "",
					region_code:
						feature.properties.context.region.region_code || "",
				};
			}

			if (feature.properties.context.place) {
				info.place = {
					name: feature.properties.context.place.name || "",
				};
			}
		} else if (feature.context) {
			console.log(
				"[StandaloneSearchBox] 使用 context 数组解析",
				feature.context,
			);

			feature.context.forEach((item: any) => {
				const id = item.id || "";
				if (id.startsWith("country")) {
					info.country = {
						name: item.text,
						country_code: item.short_code?.toUpperCase(),
					};
				} else if (id.startsWith("region")) {
					info.region = {
						name: item.text,
						region_code: item.short_code?.toUpperCase(),
					};
				} else if (id.startsWith("place")) {
					info.place = {
						name: item.text,
					};
				}
			});
		}

		console.log("[StandaloneSearchBox] extractAddressInfo 结果", info);
		return info;
	};

	const parseAddressComponents = (feature: any, addressInfo: any) => {
		console.log("[StandaloneSearchBox] parseAddressComponents 开始", {
			feature,
			addressInfo,
		});

		const components = [];

		if (addressInfo.country) {
			components.push({
				long_name: addressInfo.country.name || "",
				short_name: addressInfo.country.country_code || "",
				types: ["country"],
			});
		}

		if (addressInfo.region) {
			components.push({
				long_name: addressInfo.region.name || "",
				short_name: addressInfo.region.region_code || "",
				types: ["administrative_area_level_1"],
			});
		}

		if (addressInfo.place) {
			components.push({
				long_name: addressInfo.place.name || "",
				short_name: addressInfo.place.name || "",
				types: ["locality"],
			});
		}

		if (feature.properties?.context?.postcode) {
			components.push({
				long_name: feature.properties.context.postcode.name || "",
				short_name: feature.properties.context.postcode.name || "",
				types: ["postal_code"],
			});
		}

		console.log(
			"[StandaloneSearchBox] parseAddressComponents 结果",
			components,
		);
		return components;
	};

	return (
		<div className={containerClassName} ref={searchBoxRef}>
			<div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground z-10 pointer-events-none">
				<Search className="w-4 h-4" />
			</div>

			<div className="border border-input rounded-md h-10 overflow-hidden">
				<MapboxSearchBox
					accessToken={process.env.NEXT_PUBLIC_MAPBOX_TOKEN || ""}
					options={{
						language: "en",
						proximity: "ip",
					}}
					value={inputValue}
					onChange={handleInternalInputChange}
					onRetrieve={handleRetrieve}
					placeholder={t(
						"travelMemo.travelPointForm.placeholders.searchLocation",
					)}
					popoverOptions={{
						placement: "bottom-start",
						flip: true,
					}}
					theme={customTheme}
				/>
			</div>
		</div>
	);
}
