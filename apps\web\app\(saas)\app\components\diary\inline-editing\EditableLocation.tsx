import { Button } from "@ui/components/button";
import { cn } from "@ui/lib";
import { Check, MapPin, X } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { StandaloneSearchBox } from "../travel-point-form/StandaloneSearchBox";
import { getIconTypeFromMapboxData } from "../utils/mapbox-icon-mapper";

interface EditableLocationProps {
	value: string;
	isEditing: boolean;
	onEnterEdit: () => void;
	onExitEdit: (save: boolean) => void;
	onValueChange: (
		value: string,
		coordinates?: { lat: number; lng: number },
	) => void;
	onIconTypeChange?: (iconType: string) => void;
	className?: string;
}

export function EditableLocation({
	value,
	isEditing,
	onEnterEdit,
	onExitEdit,
	onValueChange,
	onIconTypeChange,
	className,
}: EditableLocationProps) {
	const [localValue, setLocalValue] = useState(value);
	const [selectedCoordinates, setSelectedCoordinates] = useState<
		{ lat: number; lng: number } | undefined
	>();
	const searchBoxKey = useRef(0);

	// 当编辑状态改变时，同步本地值
	useEffect(() => {
		if (isEditing) {
			setLocalValue(value);
			// 重置搜索框以确保显示正确的初始值
			searchBoxKey.current += 1;
		}
	}, [isEditing, value]);

	const handleSave = () => {
		onValueChange(localValue.trim(), selectedCoordinates);
		onExitEdit(true);
	};

	const handleCancel = () => {
		setLocalValue(value);
		setSelectedCoordinates(undefined);
		onExitEdit(false);
	};

	const handlePlaceSelect = (place: any) => {
		console.log("[EditableLocation] handlePlaceSelect", place);

		if (!place.geometry?.location) {
			console.error("[EditableLocation] 地点缺少坐标信息", place);
			return;
		}

		// 获取位置名称
		const locationName = place.name || place.formatted_address || "";

		// 获取坐标
		const coordinates = {
			lat: place.geometry.location.lat(),
			lng: place.geometry.location.lng(),
		};

		// 自动选择图标类型（如果有 Mapbox 数据且提供了回调）
		if (place.mapbox_data && onIconTypeChange) {
			const autoSelectedIconType = getIconTypeFromMapboxData({
				poi_category: place.mapbox_data.poi_category,
				poi_category_ids: place.mapbox_data.poi_category_ids,
				feature_type: place.mapbox_data.feature_type,
				maki: place.mapbox_data.maki,
				brand: place.mapbox_data.brand,
			});

			// 将图标类型转换为应用中使用的格式
			const iconTypeMapping: Record<string, string> = {
				hotel: "HOTEL",
				food: "FOOD",
				landmark: "LANDMARK",
				beach: "LANDMARK", // 海滩归类为地标
				mountain: "LANDMARK", // 山地归类为地标
				museum: "LANDMARK", // 博物馆归类为地标
				park: "PARK",
				shopping: "SHOPPING",
				cafe: "FOOD", // 咖啡厅归类为餐饮
				activity: "OTHER", // 活动归类为其他
				transport: "TRANSPORT",
				airport: "TRANSPORT", // 机场归类为交通
				default: "PIN",
			};

			const finalIconType =
				iconTypeMapping[autoSelectedIconType] || "PIN";
			console.log(
				`[EditableLocation] 自动选择图标类型: ${finalIconType}`,
			);

			// 回调通知父组件更新图标类型
			onIconTypeChange(finalIconType);
		}

		console.log("[EditableLocation] 更新位置", {
			locationName,
			coordinates,
		});

		setLocalValue(locationName);
		setSelectedCoordinates(coordinates);
	};

	const handleInputChange = (newValue: string) => {
		console.log("[EditableLocation] 输入变化", newValue);
		setLocalValue(newValue);
		// 如果用户手动输入，清除坐标信息
		if (selectedCoordinates) {
			setSelectedCoordinates(undefined);
		}
	};

	const handleKeyDown = (e: React.KeyboardEvent) => {
		e.stopPropagation(); // 防止触发父组件的键盘事件

		if (e.key === "Escape") {
			e.preventDefault();
			handleCancel();
		} else if (e.key === "Enter") {
			e.preventDefault();
			handleSave();
		}
	};

	if (isEditing) {
		return (
			<div className={cn("flex flex-col gap-2 py-1", className)}>
				<div className="flex items-center gap-2">
					<MapPin className="h-4 w-4 text-muted-foreground flex-shrink-0" />
					<div className="flex-1">
						<StandaloneSearchBox
							key={searchBoxKey.current}
							onPlaceSelect={handlePlaceSelect}
							onInputChange={handleInputChange}
							initialValue={localValue}
							containerClassName="w-full"
						/>
					</div>
				</div>

				{localValue && selectedCoordinates && (
					<div className="text-xs text-green-600 flex items-center gap-1 ml-6">
						📍 已获取坐标信息
					</div>
				)}

				<div className="flex items-center gap-2 ml-6">
					<Button
						size="sm"
						onClick={handleSave}
						disabled={!localValue.trim()}
						className="h-7 px-3 text-xs"
						onKeyDown={handleKeyDown}
					>
						<Check className="mr-1 h-3 w-3" />
						保存
					</Button>
					<Button
						size="sm"
						variant="outline"
						onClick={handleCancel}
						className="h-7 px-3 text-xs"
						onKeyDown={handleKeyDown}
					>
						<X className="mr-1 h-3 w-3" />
						取消
					</Button>
				</div>
			</div>
		);
	}

	return (
		<button
			type="button"
			className={cn(
				"font-semibold truncate max-w-[200px] sm:max-w-none cursor-pointer hover:bg-muted/50 rounded px-1 py-0.5 transition-colors text-left inline-flex items-center",
				className,
			)}
			onClick={onEnterEdit}
			onKeyDown={(e) => {
				if (e.key === "Enter" || e.key === " ") {
					e.preventDefault();
					onEnterEdit();
				}
			}}
			title="点击编辑位置"
		>
			<MapPin className="mr-1 h-3 w-3 text-muted-foreground" />
			{value || "未命名位置"}
		</button>
	);
}
