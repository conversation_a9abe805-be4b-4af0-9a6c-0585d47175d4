{"name": "@repo/video-exporter", "version": "0.0.0", "private": true, "scripts": {"dev": "tsx watch src/index.ts", "start": "node dist/index.js", "build": "tsup src/index.ts --format cjs,esm --dts", "lint": "eslint . --ext .ts", "clean": "rm -rf dist", "video-exporter": "pnpm --filter video-exporter dev"}, "dependencies": {"@repo/database": "workspace:*", "@repo/logs": "workspace:*", "@repo/config": "workspace:*", "bullmq": "^5.1.1", "dotenv": "^16.3.1", "ioredis": "^5.3.2", "playwright": "^1.52.0", "playwright-core": "^1.52.0", "zod": "^3.24.2"}, "devDependencies": {"@repo/tsconfig": "workspace:*", "@types/node": "^20.10.5", "eslint": "^8.56.0", "tsup": "^8.0.1", "tsx": "^4.7.0", "typescript": "^5.3.3"}}