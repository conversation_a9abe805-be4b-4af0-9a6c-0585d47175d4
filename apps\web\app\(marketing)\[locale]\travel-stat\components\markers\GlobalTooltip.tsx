"use client";

import { useMap } from "../../contexts/MapContext";
import { type TooltipType, UnifiedTooltip } from "./UnifiedTooltip";

export function GlobalTooltip() {
	const { polaroidTooltipState, hidePolaroidTooltip, removeTravelPoint } =
		useMap();

	if (
		!polaroidTooltipState.isVisible ||
		!polaroidTooltipState.point ||
		!polaroidTooltipState.mousePosition
	) {
		return null;
	}

	// 根据点位的数据类型决定tooltip类型
	// 如果有图片，使用详细tooltip；否则使用简单tooltip
	const hasImages =
		polaroidTooltipState.point.images?.length > 0 ||
		polaroidTooltipState.point.photos?.length > 0 ||
		(polaroidTooltipState.point as any).image?.url;

	const tooltipType: TooltipType = hasImages ? "detailed" : "detailed"; // 暂时都使用详细模式

	return (
		<UnifiedTooltip
			isVisible={polaroidTooltipState.isVisible}
			type={tooltipType}
			mode="global"
			point={polaroidTooltipState.point}
			mousePosition={polaroidTooltipState.mousePosition}
			onRemovePoint={(pointId) => {
				removeTravelPoint(pointId);
				hidePolaroidTooltip();
			}}
			onClose={hidePolaroidTooltip}
			closeOnClickOutside={true}
		/>
	);
}
