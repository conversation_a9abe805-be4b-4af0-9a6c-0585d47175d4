export const dynamic = "force-dynamic";
export const revalidate = 0;

export async function generateMetadata() {
	return {
		title: "设置 - AI图片生成器",
		description: "管理您的AI图片生成器账户设置",
	};
}

export default function SettingsPage() {
	return (
		<div className="min-h-screen bg-gray-50 py-12">
			<div className="max-w-4xl mx-auto px-4">
				<div className="mb-8">
					<h1 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
						账户设置
					</h1>
					<p className="text-gray-600 mt-2">
						管理您的AI图片生成器账户和偏好设置
					</p>
				</div>

				{/* 简化的设置导航 */}
				<div className="bg-white rounded-lg shadow-md">
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
						<div className="p-4 border border-gray-200 rounded-lg hover:border-purple-300 cursor-pointer">
							<h3 className="font-semibold text-lg mb-2">
								个人信息
							</h3>
							<p className="text-sm text-gray-600">
								管理您的基本信息和头像
							</p>
						</div>

						<div className="p-4 border border-gray-200 rounded-lg hover:border-purple-300 cursor-pointer">
							<h3 className="font-semibold text-lg mb-2">
								账户安全
							</h3>
							<p className="text-sm text-gray-600">
								密码、两步验证等安全设置
							</p>
						</div>

						<div className="p-4 border border-gray-200 rounded-lg hover:border-purple-300 cursor-pointer">
							<h3 className="font-semibold text-lg mb-2">
								订阅管理
							</h3>
							<p className="text-sm text-gray-600">
								查看和管理您的订阅套餐
							</p>
						</div>

						<div className="p-4 border border-gray-200 rounded-lg hover:border-purple-300 cursor-pointer">
							<h3 className="font-semibold text-lg mb-2">
								生成偏好
							</h3>
							<p className="text-sm text-gray-600">
								设置默认的图片生成参数
							</p>
						</div>

						<div className="p-4 border border-gray-200 rounded-lg hover:border-purple-300 cursor-pointer">
							<h3 className="font-semibold text-lg mb-2">
								通知设置
							</h3>
							<p className="text-sm text-gray-600">
								管理邮件和应用内通知
							</p>
						</div>

						<div className="p-4 border border-gray-200 rounded-lg hover:border-purple-300 cursor-pointer">
							<h3 className="font-semibold text-lg mb-2">
								数据导出
							</h3>
							<p className="text-sm text-gray-600">
								下载您的创作历史和数据
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
