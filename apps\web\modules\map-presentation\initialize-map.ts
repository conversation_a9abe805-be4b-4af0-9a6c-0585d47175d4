"use client";

import type { MapPoint } from "./types";

// 扩展 Window 接口以包含动态回调函数

interface MapInitializeOptions {
	mapTypeControl?: boolean;
	streetViewControl?: boolean;
	fullscreenControl?: boolean;
	zoomControl?: boolean;
	scaleControl?: boolean;
	initialZoom?: number;
	defaultMapType?: string;
	apiKey?: string;
}

const DEFAULT_OPTIONS: MapInitializeOptions = {
	mapTypeControl: false,
	streetViewControl: false,
	fullscreenControl: false,
	zoomControl: true,
	scaleControl: true,
	initialZoom: 12,
	defaultMapType: "roadmap",
};

/**
 * 初始化Google地图
 *
 * @param mapContainerRef - 地图容器的引用
 * @param points - 需要显示在地图上的点位（可选）
 * @param options - 地图初始化选项
 * @returns 初始化完成的Promise
 */
export async function initializeGoogleMap(
	mapContainerRef: React.RefObject<HTMLDivElement>,
	initialCenter: google.maps.LatLngLiteral,
	initialZoom: number,
	options: MapOptions = {},
): Promise<google.maps.Map> {
	// 合并默认选项和用户选项
	const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
	console.log("初始化地图选项:", mergedOptions);
	// 确保有默认的 mapId，这对于高级标记功能是必需的
	if (!mergedOptions.mapId) {
		// 从环境变量获取 Map ID
		const envMapId = process.env.NEXT_PUBLIC_GOOGLE_MAP_ID;
		mergedOptions.mapId = envMapId || "8f065c79bb311736"; // 优先使用环境变量中的 Map ID

		if (!envMapId) {
			console.warn(
				"没有在环境变量中找到 NEXT_PUBLIC_GOOGLE_MAP_ID，使用默认 ID。请确保此 ID 在 Google Cloud Console 中有效",
			);
		}
	}

	return new Promise((resolve, reject) => {
		if (!mapContainerRef.current) {
			reject(new Error("地图容器不存在"));
			return;
		}

		// 检查Google Maps API是否已加载
		if (typeof window !== "undefined" && window.google?.maps) {
			// 如果API已加载，直接创建地图实例
			try {
				// 明确使用 google.maps.Map
				const map = new google.maps.Map(
					mapContainerRef.current as HTMLElement,
					{
						center: initialCenter,
						zoom: initialZoom,
						styles: options.styles,
						mapTypeControl: options.mapTypeControl ?? false,
						streetViewControl: options.streetViewControl ?? false,
						fullscreenControl: options.fullscreenControl ?? false,
						zoomControl: options.zoomControl ?? true,
						scaleControl: options.scaleControl ?? true,
						mapId: options.mapId,
						tilt: 45,
						heading: options.heading,
					},
				);

				// 如果提供了点位，调整地图视图以显示所有点位
				if (options.points && options.points.length > 0) {
					fitMapToBounds(map, options.points);
				}

				resolve(map);
			} catch (error) {
				reject(error);
			}
		} else {
			// 如果API尚未加载，先加载API
			loadGoogleMapsApi(
				mergedOptions.apiKey ||
					process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ||
					"",
			)
				.then(() => {
					if (!window.google?.maps) {
						reject(new Error("Google Maps API 加载失败"));
						return;
					}
					// 明确使用 google.maps.Map
					const map = new google.maps.Map(
						mapContainerRef.current as HTMLElement,
						{
							center: initialCenter,
							zoom: initialZoom,
							styles: options.styles,
							mapTypeControl: options.mapTypeControl ?? false,
							streetViewControl:
								options.streetViewControl ?? false,
							fullscreenControl:
								options.fullscreenControl ?? false,
							zoomControl: options.zoomControl ?? true,
							scaleControl: options.scaleControl ?? true,
							mapId: options.mapId,
							tilt: 45,
							heading: options.heading,
						},
					);

					// 如果提供了点位，调整地图视图以显示所有点位
					if (options.points && options.points.length > 0) {
						fitMapToBounds(map, options.points);
					}

					resolve(map);
				})
				.catch((error) => {
					reject(error);
				});
		}
	});
}

/**
 * 加载Google Maps API
 */
function loadGoogleMapsApi(apiKey: string): Promise<void> {
	return new Promise((resolve, reject) => {
		// 如果API已加载，直接返回
		if (typeof window !== "undefined" && window.google?.maps) {
			resolve();
			return;
		}

		// 创建回调函数名称
		const callbackName = `googleMapsInitialize_${Math.random().toString(36).substring(2, 9)}`;
		// 设置回调函数
		(window as any)[callbackName] = async () => {
			// 清理全局回调
			delete (window as any)[callbackName];

			// 预加载 marker 库，确保 AdvancedMarkerElement 可用
			try {
				if (
					typeof window.google !== "undefined" &&
					typeof window.google.maps !== "undefined" &&
					typeof window.google.maps.importLibrary === "function"
				) {
					await window.google.maps.importLibrary("marker");
					console.log("Google Maps Marker 库加载成功");
				}
			} catch (error) {
				console.warn("Google Maps Marker 库加载失败:", error);
				// 虽然 marker 库加载失败，但核心地图功能可能正常，所以继续 resolve
			}

			resolve();
		};

		// 创建脚本标签
		const script = document.createElement("script");
		script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&callback=${callbackName}&libraries=marker`;
		console.log("加载 Google Maps API:", script.src);
		script.async = true;
		script.defer = true;
		script.onerror = () => reject(new Error("Google Maps API加载失败"));

		// 添加到文档
		document.head.appendChild(script);
	});
}

/**
 * 调整地图视图以适应所有点位
 */
function fitMapToBounds(map: any, points: MapPoint[]): void {
	if (!map || !window.google?.maps || points.length === 0) return;

	const bounds = new window.google.maps.LatLngBounds();

	// 将所有点添加到边界中
	points.forEach((point) => {
		bounds.extend(
			new window.google.maps.LatLng(
				point.coordinates.lat,
				point.coordinates.lng,
			),
		);
	});

	// 适配地图显示所有点位
	map.fitBounds(bounds);

	// 稍微缩小一点，留出边距
	const listener = window.google.maps.event.addListenerOnce(
		map,
		"idle",
		() => {
			if (map.getZoom() > 12) {
				map.setZoom(map.getZoom() - 1);
			}
		},
	);
}

// 确保 MapOptions 接口也包含 tilt 和 heading (如果需要)
export interface MapOptions extends google.maps.MapOptions {
	styles?: google.maps.MapTypeStyle[];
	mapId?: string;
	points?: MapPoint[]; // 添加 points 属性，类型为 MapPoint 数组
	// tilt?: number; // google.maps.MapOptions 已包含 tilt
	// heading?: number; // google.maps.MapOptions 已包含 heading
}
