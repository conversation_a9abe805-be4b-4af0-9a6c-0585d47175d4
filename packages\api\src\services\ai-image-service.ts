import { aiImagesPrisma } from "@repo/database";
import type { AiImagesTypes } from "@repo/database";

export interface CreateImageGenerationParams {
	userId: string;
	prompt: string;
	negativePrompt?: string;
	model?: string;
	style?: string;
	size?: string;
	quality?: string;
}

export interface UpdateImageGenerationParams {
	id: string;
	imageUrl?: string;
	thumbnailUrl?: string;
	status?: string;
	errorMessage?: string;
	metadata?: any;
}

// 创建图片生成记录
export async function createImageGeneration(
	params: CreateImageGenerationParams,
): Promise<AiImagesTypes.AiImageGeneration> {
	return await aiImagesPrisma.aiImageGeneration.create({
		data: {
			userId: params.userId,
			prompt: params.prompt,
			negativePrompt: params.negativePrompt,
			model: params.model || "dall-e-3",
			style: params.style,
			size: params.size || "1024x1024",
			quality: params.quality || "standard",
			status: "pending",
		},
		include: {
			user: {
				select: {
					id: true,
					name: true,
					username: true,
					image: true,
				},
			},
		},
	});
}

// 更新图片生成记录
export async function updateImageGeneration(
	params: UpdateImageGenerationParams,
): Promise<AiImagesTypes.AiImageGeneration> {
	return await aiImagesPrisma.aiImageGeneration.update({
		where: { id: params.id },
		data: {
			imageUrl: params.imageUrl,
			thumbnailUrl: params.thumbnailUrl,
			status: params.status,
			errorMessage: params.errorMessage,
			metadata: params.metadata,
			updatedAt: new Date(),
		},
	});
}

// 获取用户的图片生成历史
export async function getUserImageGenerations(
	userId: string,
	page = 1,
	limit = 20,
) {
	const skip = (page - 1) * limit;

	const [images, total] = await Promise.all([
		aiImagesPrisma.aiImageGeneration.findMany({
			where: { userId },
			orderBy: { createdAt: "desc" },
			skip,
			take: limit,
			include: {
				user: {
					select: {
						id: true,
						name: true,
						username: true,
						image: true,
					},
				},
				_count: {
					select: {
						favorites: true,
						comments: true,
					},
				},
			},
		}),
		aiImagesPrisma.aiImageGeneration.count({
			where: { userId },
		}),
	]);

	return {
		images,
		total,
		page,
		limit,
		totalPages: Math.ceil(total / limit),
	};
}

// 获取公开的图片画廊
export async function getPublicImageGallery(page = 1, limit = 20) {
	const skip = (page - 1) * limit;

	const [images, total] = await Promise.all([
		aiImagesPrisma.aiImageGeneration.findMany({
			where: {
				isPublic: true,
				status: "completed",
				imageUrl: { not: null },
			},
			orderBy: { createdAt: "desc" },
			skip,
			take: limit,
			include: {
				user: {
					select: {
						id: true,
						name: true,
						username: true,
						image: true,
					},
				},
				_count: {
					select: {
						favorites: true,
						comments: true,
					},
				},
			},
		}),
		aiImagesPrisma.aiImageGeneration.count({
			where: {
				isPublic: true,
				status: "completed",
				imageUrl: { not: null },
			},
		}),
	]);

	return {
		images,
		total,
		page,
		limit,
		totalPages: Math.ceil(total / limit),
	};
}

// 收藏/取消收藏图片
export async function toggleFavorite(userId: string, imageId: string) {
	const existingFavorite = await aiImagesPrisma.aiImageFavorite.findUnique({
		where: {
			userId_imageId: {
				userId,
				imageId,
			},
		},
	});

	if (existingFavorite) {
		// 取消收藏
		await aiImagesPrisma.aiImageFavorite.delete({
			where: { id: existingFavorite.id },
		});

		// 减少点赞数
		await aiImagesPrisma.aiImageGeneration.update({
			where: { id: imageId },
			data: { likes: { decrement: 1 } },
		});

		return { favorited: false };
	}

	// 添加收藏
	await aiImagesPrisma.aiImageFavorite.create({
		data: {
			userId,
			imageId,
		},
	});

	// 增加点赞数
	await aiImagesPrisma.aiImageGeneration.update({
		where: { id: imageId },
		data: { likes: { increment: 1 } },
	});

	return { favorited: true };
}

// 创建模板
export async function createTemplate(params: {
	userId: string;
	name: string;
	description?: string;
	prompt: string;
	negativePrompt?: string;
	model: string;
	style?: string;
	size: string;
	quality: string;
	isPublic?: boolean;
}): Promise<AiImagesTypes.AiImageTemplate> {
	return await aiImagesPrisma.aiImageTemplate.create({
		data: params,
	});
}

// 获取用户模板
export async function getUserTemplates(userId: string) {
	return await aiImagesPrisma.aiImageTemplate.findMany({
		where: { userId },
		orderBy: { createdAt: "desc" },
	});
}

// 获取公开模板
export async function getPublicTemplates(page = 1, limit = 20) {
	const skip = (page - 1) * limit;

	return await aiImagesPrisma.aiImageTemplate.findMany({
		where: { isPublic: true },
		orderBy: { usageCount: "desc" },
		skip,
		take: limit,
		include: {
			user: {
				select: {
					id: true,
					name: true,
					username: true,
					image: true,
				},
			},
		},
	});
}

// 使用模板（增加使用次数）
export async function useTemplate(templateId: string) {
	return await aiImagesPrisma.aiImageTemplate.update({
		where: { id: templateId },
		data: { usageCount: { increment: 1 } },
	});
}

// 用户同步功能（从主数据库同步用户信息）
export async function syncUserFromMainDatabase(userData: {
	id: string;
	name: string;
	email: string;
	emailVerified: boolean;
	image?: string;
	username?: string;
	role?: string;
	createdAt: Date;
	updatedAt: Date;
}) {
	return await aiImagesPrisma.user.upsert({
		where: { id: userData.id },
		update: {
			name: userData.name,
			email: userData.email,
			emailVerified: userData.emailVerified,
			image: userData.image,
			username: userData.username,
			role: userData.role,
			updatedAt: userData.updatedAt,
		},
		create: userData,
	});
}
