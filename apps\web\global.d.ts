import type { Messages } from "@repo/i18n";
import type { JSX as Jsx } from "react/jsx-runtime";

// temporary fix for mdx types
// TODO: remove once mdx has fully compatibility with react 19
declare global {
	namespace JSX {
		type ElementClass = Jsx.ElementClass;
		type Element = Jsx.Element;
		type IntrinsicElements = Jsx.IntrinsicElements;
	}
}

declare global {
	interface IntlMessages extends Messages {}

	// Google Maps API 全局声明
	interface Window {
		google?: {
			maps: {
				Map: any;
				Marker: any;
				Polyline: any;
				LatLng: any;
				LatLngBounds: any;
				event: any;
				InfoWindow: any;
				Animation: {
					DROP: number;
					BOUNCE: number;
				};
				MapTypeId: {
					ROADMAP: string;
					SATELLITE: string;
					HYBRID: string;
					TERRAIN: string;
				};
			};
		};
		// Google Maps API 回调函数
		[key: `googleMapsInitialize_${string}`]: () => void;
	}
}

declare global {
	interface Window {
		plausible?: (
			eventName: string,
			options?: { props?: Record<string, any> },
		) => void;
	}
}
