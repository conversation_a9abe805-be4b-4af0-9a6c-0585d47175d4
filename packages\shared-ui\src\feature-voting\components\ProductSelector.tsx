"use client";

import type { ProductSelectorProps } from "../types";

/**
 * 产品选择器组件
 */
export function ProductSelector({
	products,
	selectedProductId,
	onProductChange,
	className,
	ui,
}: ProductSelectorProps) {
	const {
		Label,
		Select,
		SelectContent,
		SelectItem,
		SelectTrigger,
		SelectValue,
		cn,
	} = ui;

	return (
		<div className={cn("space-y-2", className)}>
			<Label htmlFor="product-select">选择产品</Label>
			<Select
				value={selectedProductId || ""}
				onValueChange={onProductChange}
			>
				<SelectTrigger id="product-select">
					<SelectValue placeholder="请选择一个产品" />
				</SelectTrigger>
				<SelectContent>
					{products.map((product) => (
						<SelectItem key={product.id} value={product.id}>
							<div className="flex flex-col">
								<span className="font-medium">
									{product.name}
								</span>
								{product.description && (
									<span className="text-xs text-muted-foreground">
										{product.description}
									</span>
								)}
							</div>
						</SelectItem>
					))}
				</SelectContent>
			</Select>
		</div>
	);
}
