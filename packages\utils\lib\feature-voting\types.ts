// 基础数据类型
export interface Product {
	id: string;
	name: string;
	description?: string;
	createdAt: Date;
	updatedAt: Date;
	_count?: {
		featureRequests: number;
	};
}

export interface FeatureRequest {
	id: string;
	title: string;
	description: string;
	status: FeatureStatus;
	voteCount: number;
	productId: string;
	userId?: string;
	authorName?: string;
	authorEmail?: string;
	anonymousId?: string;
	ipAddress?: string;
	userAgent?: string;
	createdAt: Date;
	updatedAt: Date;
	product?: Pick<Product, "id" | "name">;
	user?: Pick<User, "id" | "name">;
	hasVoted?: boolean;
	commentCount?: number;
	_count?: {
		votes: number;
		comments: number;
		subscriptions: number;
	};
}

export interface FeatureVote {
	id: string;
	featureRequestId: string;
	userId?: string;
	anonymousId?: string;
	voterName?: string;
	voterEmail?: string;
	ipAddress?: string;
	userAgent?: string;
	createdAt: Date;
	user?: Pick<User, "id" | "name" | "email">;
}

export interface FeatureComment {
	id: string;
	content: string;
	featureRequestId: string;
	userId?: string;
	authorName?: string;
	authorEmail?: string;
	anonymousId?: string;
	ipAddress?: string;
	userAgent?: string;
	createdAt: Date;
	updatedAt: Date;
	user?: Pick<User, "id" | "name" | "email">;
}

export interface FeatureSubscription {
	id: string;
	featureRequestId: string;
	userId?: string;
	anonymousId?: string;
	subscriberName?: string;
	subscriberEmail: string;
	ipAddress?: string;
	userAgent?: string;
	createdAt: Date;
	user?: Pick<User, "id" | "name" | "email">;
}

export interface User {
	id: string;
	name?: string;
	email: string;
	role?: string;
}

// 特性状态枚举
export const FEATURE_STATUSES = [
	"under_consideration",
	"planned",
	"in_progress",
	"completed",
	"wont_do",
] as const;

export type FeatureStatus = (typeof FEATURE_STATUSES)[number];

// 状态显示映射
export const FEATURE_STATUS_LABELS: Record<FeatureStatus, string> = {
	under_consideration: "待考虑",
	planned: "计划中",
	in_progress: "开发中",
	completed: "已完成",
	wont_do: "不考虑",
};

export const FEATURE_STATUS_COLORS: Record<FeatureStatus, string> = {
	under_consideration: "yellow",
	planned: "blue",
	in_progress: "orange",
	completed: "green",
	wont_do: "gray",
};

// API 请求和响应类型
export interface ApiResponse<T = any> {
	success: boolean;
	data?: T;
	error?: string;
	message?: string;
}

export interface PaginationInfo {
	page: number;
	limit: number;
	total: number;
	totalPages: number;
}

export interface FeatureRequestsResponse {
	featureRequests: FeatureRequest[];
	pagination: PaginationInfo;
}

// 查询参数类型
export interface FeatureRequestQuery {
	productId?: string;
	status?: FeatureStatus;
	showVoteCounts?: boolean;
	sortBy?: "createdAt" | "voteCount" | "title";
	sortOrder?: "asc" | "desc";
	page?: number;
	limit?: number;
	search?: string;
}

// 表单数据类型
export interface CreateFeatureRequestData {
	title: string;
	description: string;
	productId: string;
	authorName?: string;
	authorEmail?: string;
	anonymousId?: string;
}

export interface VoteData {
	anonymousId?: string;
	voterName?: string;
	voterEmail?: string;
}

// 匿名用户API数据类型 (用于API调用)
export interface AnonymousUserApiData {
	anonymousId: string;
	voterName?: string;
	voterEmail?: string;
	browserFingerprint?: {
		userAgent: string;
		language: string;
		timezone: string;
		screenResolution: string;
		colorDepth: number;
		platform: string;
	};
}

// 组件配置类型
export interface FeatureVotingConfig {
	apiBaseUrl?: string;
	showVoteCounts?: boolean;
	allowAnonymousVoting?: boolean;
	allowAnonymousSubmissions?: boolean;
	maxSubmissionsPerAnonymousUser?: number;
	rateLimitConfig?: {
		votesPerMinute?: number;
		submissionsPerDay?: number;
	};
}

// Hook 返回类型
export interface UseFeatureVotingReturn {
	// 数据
	featureRequests: FeatureRequest[];
	products: Product[];
	isLoading: boolean;
	error: string | null;

	// 分页
	pagination: PaginationInfo | null;

	// 操作
	vote: (featureRequestId: string, voteData?: VoteData) => Promise<void>;
	unvote: (featureRequestId: string) => Promise<void>;
	submitFeatureRequest: (data: CreateFeatureRequestData) => Promise<void>;
	loadFeatureRequests: (query?: FeatureRequestQuery) => Promise<void>;
	loadProducts: () => Promise<void>;

	// 状态
	votingStates: Record<string, boolean>;
	submissionState: {
		isSubmitting: boolean;
		success: boolean;
		error: string | null;
	};
}
