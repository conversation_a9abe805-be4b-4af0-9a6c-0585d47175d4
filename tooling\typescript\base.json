{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"allowJs": true, "composite": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": false, "incremental": false, "inlineSources": false, "isolatedModules": true, "module": "Preserve", "target": "ES2021", "moduleResolution": "bundler", "noUnusedLocals": false, "noUnusedParameters": false, "preserveWatchOutput": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": false, "strictNullChecks": false, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "noFallthroughCasesInSwitch": false, "noUncheckedIndexedAccess": false, "allowUnreachableCode": true, "allowUnusedLabels": true, "noImplicitOverride": false, "exactOptionalPropertyTypes": false, "allowSyntheticDefaultImports": true}, "display": "<PERSON><PERSON><PERSON>", "exclude": ["node_modules", "dist", ".next", "build"]}