import { But<PERSON> } from "@ui/components/button";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>eader,
	DialogTitle,
} from "@ui/components/dialog";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";
import { toast } from "sonner";

import { DateSelector } from "./travel-point-form/DateSelector";
import { DescriptionField } from "./travel-point-form/DescriptionField";
import { IconTypeSelector } from "./travel-point-form/IconTypeSelector";
import { ImageUploader } from "./travel-point-form/ImageUploader";
import { LocationField } from "./travel-point-form/LocationField";
import { ScrollbarStyles } from "./travel-point-form/ScrollbarStyles";

import type { TravelPoint } from "./travel-point-form/types";

// 使用字符串字面量作为 id，确保它们与 Prisma 枚举值匹配
export const iconOptions: { id: string; label: string }[] = [
	{ id: "PIN", label: "travelMemo.travelPointForm.iconOptions.pin" },
	{
		id: "LANDMARK",
		label: "travelMemo.travelPointForm.iconOptions.landmark",
	},
	{ id: "FOOD", label: "travelMemo.travelPointForm.iconOptions.food" },
	{ id: "PARK", label: "travelMemo.travelPointForm.iconOptions.park" },
	{ id: "HOTEL", label: "travelMemo.travelPointForm.iconOptions.hotel" },
	{
		id: "SHOPPING",
		label: "travelMemo.travelPointForm.iconOptions.shopping",
	},
	{
		id: "TRANSPORT",
		label: "travelMemo.travelPointForm.iconOptions.transport",
	},
	{ id: "OTHER", label: "travelMemo.travelPointForm.iconOptions.other" },
];

// 使用 UTC 时间来确保时区一致性
export const getUTCDate = (date?: Date) => {
	if (!date)
		return new Date(
			Date.UTC(
				new Date().getUTCFullYear(),
				new Date().getUTCMonth(),
				new Date().getUTCDate(),
			),
		);
	return new Date(
		Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate()),
	);
};

interface TravelPointFormProps {
	isOpen: boolean;
	onOpenChange: (open: boolean) => void;
	onAddPoint?: (point: TravelPoint) => void;
	onEditPoint?: (id: string, point: Partial<TravelPoint>) => void;
	pointToEdit?: TravelPoint;
	isEditing?: boolean;
}

// 静态变量，用于在多次添加点位时保存上次选择的日期
let lastSelectedDate: Date | null = null;

const TravelPointForm = ({
	isOpen,
	onOpenChange,
	onAddPoint,
	onEditPoint,
	pointToEdit,
	isEditing = false,
}: TravelPointFormProps) => {
	// 初始化表单状态 - 使用上次选择的日期或当天日期
	const [formData, setFormData] = useState<Partial<TravelPoint>>(
		isEditing && pointToEdit
			? { ...pointToEdit }
			: {
					// 优先使用上次选择的日期
					date: lastSelectedDate
						? getUTCDate(lastSelectedDate)
						: getUTCDate(),
					iconType: "PIN",
					images: [],
				},
	);
	const t = useTranslations();

	// 添加调试日志，跟踪表单数据的变化
	useEffect(() => {
		console.log("[TravelPointForm] 表单数据更新", {
			isEditing,
			pointToEdit,
			formData,
			lastSelectedDate,
		});

		// 保存当前选择的日期，用于下次打开表单时设置初始日期
		if (formData.date && !isEditing) {
			lastSelectedDate = formData.date;
			console.log(
				"[TravelPointForm] 保存上次选择的日期",
				lastSelectedDate,
			);
		}
	}, [formData, isEditing, pointToEdit]);

	// 重置表单数据当对话框打开时
	useEffect(() => {
		if (isOpen) {
			console.log("[TravelPointForm] 对话框打开，重置表单", {
				isEditing,
				pointToEdit,
				lastSelectedDate,
			});

			const newFormData =
				isEditing && pointToEdit
					? { ...pointToEdit }
					: {
							// 优先使用上次选择的日期，如果是第一次打开则使用当天日期
							date: lastSelectedDate
								? getUTCDate(lastSelectedDate)
								: getUTCDate(),
							iconType: "PIN",
							images: [],
						};

			console.log("[TravelPointForm] 设置新的表单数据", newFormData);
			setFormData(newFormData);
		}
	}, [isOpen, isEditing, pointToEdit]);

	// 处理保存点位
	const handleSavePoint = () => {
		console.log("[TravelPointForm] 开始保存点位", {
			isEditing,
			formData,
			pointToEdit,
		});

		// 坐标检查 - 简化了验证逻辑
		if (!formData.location || !formData.date || !formData.coordinates) {
			console.error("[TravelPointForm] 表单验证失败", {
				location: formData.location,
				date: formData.date,
				coordinates: formData.coordinates,
			});
			toast.error(t("travelMemo.travelPointForm.toasts.invalidLocation"));
			return;
		}

		if (isEditing && pointToEdit && onEditPoint) {
			// 编辑现有点位
			console.log("[TravelPointForm] 编辑现有点位", {
				id: pointToEdit.id,
				changes: formData,
			});
			onEditPoint(pointToEdit.id, formData);
		} else if (onAddPoint) {
			// 添加新点位
			const newTravelPoint: TravelPoint = {
				id: Date.now().toString(),
				date: formData.date as Date,
				location: formData.location as string,
				description: formData.description || "",
				images: formData.images || [],
				// 确保类型是 string
				iconType: formData.iconType as string,
				coordinates: formData.coordinates as {
					lat: number;
					lng: number;
				},
			};

			console.log("[TravelPointForm] 添加新点位", newTravelPoint);

			// 保存最后选择的日期
			lastSelectedDate = formData.date as Date;

			onAddPoint(newTravelPoint);
		}

		console.log("[TravelPointForm] 点位保存完成，关闭对话框");
		// 关闭对话框但不重置日期，这样下次打开时会记住上次选择的日期
		onOpenChange(false);
	};

	// 阻止表单提交的处理函数
	const handleFormSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		console.log("[TravelPointForm] 表单提交被阻止");
		// 不在这里做任何实际提交操作，提交由按钮点击事件处理
	};

	return (
		<Dialog
			open={isOpen}
			onOpenChange={onOpenChange}
			// 重要：禁用点击外部区域关闭对话框
			modal={true}
		>
			<DialogContent
				className="max-w-3xl max-h-[85vh] overflow-hidden flex flex-col"
				// 禁用点击外部区域关闭对话框
				onPointerDownOutside={(e) => {
					// 阻止默认行为，避免对话框关闭
					e.preventDefault();
				}}
				// 同样禁用按Esc键关闭，避免用户在搜索和填写表单时意外关闭
				onEscapeKeyDown={(e) => {
					e.preventDefault();
				}}
			>
				<DialogHeader className="flex-shrink-0">
					<DialogTitle>
						{isEditing
							? t("travelMemo.travelPointForm.modalTitle.edit")
							: t("travelMemo.travelPointForm.modalTitle.add")}
					</DialogTitle>
				</DialogHeader>
				{/* 用div包裹整个表单内容，设置为可滚动区域 */}
				<div
					className="flex-1 overflow-y-auto custom-scrollbar pr-2"
					onSubmit={handleFormSubmit}
				>
					<div className="grid gap-4">
						{/* 照片上传 */}
						<ImageUploader
							formData={formData}
							setFormData={setFormData}
						/>

						<div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
							{/* 使用新的位置搜索组件 */}
							<LocationField
								formData={formData}
								setFormData={setFormData}
							/>

							{/* 日期选择 */}
							<DateSelector
								formData={formData}
								setFormData={setFormData}
							/>
						</div>

						{/* 描述 */}
						<DescriptionField
							formData={formData}
							setFormData={setFormData}
						/>

						{/* 图标类型 */}
						<IconTypeSelector
							formData={formData}
							setFormData={setFormData}
							iconOptions={iconOptions}
						/>

						<div className="flex justify-between items-center mt-2">
							<Button
								type="button"
								variant="outline"
								onClick={() => onOpenChange(false)}
							>
								{t("travelMemo.travelPointForm.buttons.cancel")}
							</Button>

							<Button
								type="button" // 确保按钮类型是button，不会触发表单提交
								className="bg-travel-primary text-travel-dark hover:bg-travel-primary/90"
								onClick={handleSavePoint}
								disabled={formData.location === undefined}
							>
								{isEditing
									? t(
											"travelMemo.travelPointForm.buttons.save",
										)
									: t(
											"travelMemo.travelPointForm.buttons.add",
										)}
							</Button>
						</div>
					</div>
				</div>
			</DialogContent>

			{/* 滚动条样式 */}
			<ScrollbarStyles />
		</Dialog>
	);
};

export default TravelPointForm;
