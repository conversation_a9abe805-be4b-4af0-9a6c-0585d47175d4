"use client";

export function MapStoryStyles() {
	return (
		<style jsx global>{`
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
      .animate-fadeIn {
        animation: fadeIn 1s ease-out forwards;
      }
      
      /* 隐藏滚动条但保留功能 */
      .scrollbar-hide {
        -ms-overflow-style: none;  /* IE and Edge */
        scrollbar-width: none;  /* Firefox */
      }
      .scrollbar-hide::-webkit-scrollbar {
        display: none;  /* Chrome, Safari and Opera */
      }
      
      /* 点位脉冲动画 */
      .pulse-animation {
        animation: pulse 2s infinite;
      }
      @keyframes pulse {
        0% {
          box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
        }
        70% {
          box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
        }
        100% {
          box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
        }
      }
      
      /* 闪烁动画 */
      .blink-animation {
        animation: blink 1.2s infinite alternate;
      }
      @keyframes blink {
        0% {
          opacity: 0.4;
          transform: scale(0.8);
        }
        100% {
          opacity: 1;
          transform: scale(1.2);
        }
      }
      
      /* 完成状态路线动画 */
      @keyframes dash {
        to {
          stroke-dashoffset: 20;
        }
      }
      
      /* 光线流动动画 */
      .glow-line-animation {
        animation: glow-line 4s infinite ease-in-out;
        opacity: 0;
      }
      @keyframes glow-line {
        0% {
          transform: translateX(-100%);
          opacity: 0;
        }
        10% {
          opacity: 1;
        }
        90% {
          opacity: 1;
        }
        100% {
          transform: translateX(100%);
          opacity: 0;
        }
      }
      
      /* 悬浮动画 */
      .animate-float {
        animation: float 3s ease-in-out infinite;
      }
      @keyframes float {
        0% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-10px);
        }
        100% {
          transform: translateY(0px);
        }
      }
    `}</style>
	);
}
