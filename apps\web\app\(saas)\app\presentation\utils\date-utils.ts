/**
 * 格式化日期，返回 YYYY年MM月DD日 格式
 */
export function formatDate(date: Date): string {
	return `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`;
}

/**
 * 格式化日期范围，返回 YYYY年MM月DD日 - YYYY年MM月DD日 格式
 * 如果是同一年，则第二个日期不显示年份
 * 如果是同一月，则第二个日期不显示月份
 */
export function formatDateRange(startDate: Date, endDate: Date): string {
	const startYear = startDate.getFullYear();
	const endYear = endDate.getFullYear();
	const startMonth = startDate.getMonth() + 1;
	const endMonth = endDate.getMonth() + 1;
	const startDay = startDate.getDate();
	const endDay = endDate.getDate();

	// 如果是同一天
	if (
		startYear === endYear &&
		startMonth === endMonth &&
		startDay === endDay
	) {
		return `${startYear}年${startMonth}月${startDay}日`;
	}

	// 如果是同一年
	if (startYear === endYear) {
		// 如果是同一月
		if (startMonth === endMonth) {
			return `${startYear}年${startMonth}月${startDay}日 - ${endDay}日`;
		}
		return `${startYear}年${startMonth}月${startDay}日 - ${endMonth}月${endDay}日`;
	}

	// 不同年
	return `${startYear}年${startMonth}月${startDay}日 - ${endYear}年${endMonth}月${endDay}日`;
}

/**
 * 计算两个日期之间的天数差
 */
export function getDaysDifference(startDate: Date, endDate: Date): number {
	const millisecondsPerDay = 24 * 60 * 60 * 1000;
	const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
	return Math.ceil(diffTime / millisecondsPerDay);
}
