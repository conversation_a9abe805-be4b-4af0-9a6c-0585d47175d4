import type {
	ApiResponse,
	CreateFeatureRequestData,
	FeatureRequest,
	FeatureRequestQuery,
	Product,
	VoteData,
} from "./types";

export class FeatureVotingApiClient {
	private baseUrl: string;
	private headers: Record<string, string>;

	constructor(baseUrl = "/api", headers: Record<string, string> = {}) {
		this.baseUrl = baseUrl;
		this.headers = {
			"Content-Type": "application/json",
			...headers,
		};
	}

	// 设置匿名用户ID到请求头
	setAnonymousId(anonymousId: string) {
		this.headers["x-anonymous-id"] = anonymousId;
	}

	// 设置认证token
	setAuthToken(token: string) {
		this.headers.Authorization = `Bearer ${token}`;
	}

	// 通用的fetch封装
	private async request<T>(
		endpoint: string,
		options: RequestInit = {},
	): Promise<ApiResponse<T>> {
		try {
			const response = await fetch(`${this.baseUrl}${endpoint}`, {
				headers: {
					...this.headers,
					...options.headers,
				},
				...options,
			});

			const data = await response.json();

			if (!response.ok) {
				return {
					success: false,
					error: data.error || `HTTP ${response.status}`,
				};
			}

			return data;
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : "网络请求失败",
			};
		}
	}

	// 获取产品列表
	async getProducts(): Promise<ApiResponse<Product[]>> {
		return this.request<Product[]>("/products");
	}

	// 获取特性请求列表
	async getFeatureRequests(
		query: FeatureRequestQuery = {},
	): Promise<ApiResponse<FeatureRequest[]>> {
		const searchParams = new URLSearchParams();

		Object.entries(query).forEach(([key, value]) => {
			if (value !== undefined && value !== null) {
				searchParams.append(key, String(value));
			}
		});

		const queryString = searchParams.toString();
		const endpoint = `/feature-requests${queryString ? `?${queryString}` : ""}`;

		return this.request<FeatureRequest[]>(endpoint);
	}

	// 获取单个特性请求详情
	async getFeatureRequest(id: string): Promise<ApiResponse<FeatureRequest>> {
		return this.request<FeatureRequest>(`/feature-requests/${id}`);
	}

	// 为特性请求投票
	async voteForFeature(
		featureRequestId: string,
		voteData: VoteData = {},
	): Promise<ApiResponse<void>> {
		return this.request<void>(
			`/feature-requests/${featureRequestId}/vote`,
			{
				method: "POST",
				body: JSON.stringify(voteData),
			},
		);
	}

	// 取消对特性请求的投票
	async unvoteFeature(featureRequestId: string): Promise<ApiResponse<void>> {
		return this.request<void>(
			`/feature-requests/${featureRequestId}/vote`,
			{
				method: "DELETE",
			},
		);
	}

	// 提交新的特性请求
	async submitFeatureRequest(
		data: CreateFeatureRequestData,
	): Promise<ApiResponse<FeatureRequest>> {
		return this.request<FeatureRequest>("/feature-requests", {
			method: "POST",
			body: JSON.stringify(data),
		});
	}

	// 获取特性请求的评论
	async getFeatureRequestComments(
		featureRequestId: string,
	): Promise<ApiResponse<any[]>> {
		return this.request<any[]>(
			`/feature-requests/${featureRequestId}/comments`,
		);
	}

	// 添加评论到特性请求
	async addFeatureRequestComment(
		featureRequestId: string,
		content: string,
		commentData: {
			authorName?: string;
			authorEmail?: string;
			anonymousId?: string;
		} = {},
	): Promise<ApiResponse<any>> {
		return this.request<any>(
			`/feature-requests/${featureRequestId}/comments`,
			{
				method: "POST",
				body: JSON.stringify({
					content,
					...commentData,
				}),
			},
		);
	}

	// 订阅特性请求更新
	async subscribeToFeatureRequest(
		featureRequestId: string,
		subscriptionData: {
			subscriberName?: string;
			subscriberEmail: string;
			anonymousId?: string;
		},
	): Promise<ApiResponse<void>> {
		return this.request<void>(
			`/feature-requests/${featureRequestId}/subscribe`,
			{
				method: "POST",
				body: JSON.stringify(subscriptionData),
			},
		);
	}

	// 取消订阅特性请求更新
	async unsubscribeFromFeatureRequest(
		featureRequestId: string,
	): Promise<ApiResponse<void>> {
		return this.request<void>(
			`/feature-requests/${featureRequestId}/subscribe`,
			{
				method: "DELETE",
			},
		);
	}
}

// 默认导出一个实例
export const featureVotingApi = new FeatureVotingApiClient();
