# 三层目录结构统计面板实现

## 概述

已成功实现了旅行足迹统计面板的三层目录结构显示功能，支持国家->区域->城市的层级展示，每个层级都有自己的访问次数统计，支持展开/收缩操作，并且可以点击任意层级进行地图居中显示。

## 实现的功能

### 1. 三层目录结构
- **国家层级**: 显示访问过的国家，包含访问次数统计
- **区域层级**: 显示国家下的省份/州/区域（如果数据中包含）
- **城市层级**: 显示具体的城市，包含该城市的旅行点位数量

### 2. 展开/收缩功能
- 国家层级可以展开查看下属区域和城市
- 区域层级可以展开查看下属城市
- 使用chevron图标指示展开状态
- 支持多级同时展开

### 3. 点击居中显示
- 点击任意层级（国家/区域/城市）都会在地图上居中显示该区域
- 根据层级自动调整缩放级别：
  - 国家级别：3.5-5倍缩放（根据国家大小调整）
  - 区域级别：6倍缩放
  - 城市级别：8倍缩放

### 4. 访问次数统计
- 每个层级都显示对应的访问次数
- 使用颜色编码表示访问频率
- 按访问次数降序排列

## 文件修改清单

### 1. 数据处理逻辑 (`apps/web/app/(marketing)/[locale]/travel-stat/utils/dataUtils.ts`)

新增功能：
- `extractRegionInfo()`: 从地址中提取区域信息
- `buildHierarchicalStats()`: 构建三层目录结构数据
- `extractRegionFromPoint()`: 从旅行点中提取区域信息
- 新增类型定义：`HierarchicalCountryData`, `RegionData`, `CityData`

### 2. 统计面板组件 (`apps/web/app/(marketing)/[locale]/travel-stat/components/stats/CountryList.tsx`)

重构为支持三层结构：
- 添加展开/收缩状态管理
- 实现三个渲染函数：`renderCountry()`, `renderRegion()`, `renderCity()`
- 支持键盘导航（满足无障碍要求）
- 添加位置点击处理器

### 3. 主组件更新 (`apps/web/app/(marketing)/[locale]/travel-stat/TravelFootprintTool.tsx`)

- 添加 `handleLocationClick()` 函数处理地图居中显示
- 更新 `CountryList` 组件调用，传递新的props
- 集成地图控制功能

### 4. 分析事件追踪 (`apps/web/app/(marketing)/[locale]/travel-stat/utils/analytics.ts`)

- 新增 `travel_stat_location_click` 事件类型
- 支持追踪用户点击不同层级的行为

## 使用方式

1. **查看统计**: 在侧边栏统计面板中查看三层目录结构
2. **展开层级**: 点击国家或区域前的箭头图标展开/收缩
3. **地图居中**: 点击任意层级名称，地图会自动居中并调整到合适的缩放级别
4. **颜色指示**: 根据访问次数显示不同颜色的指示器

## 数据结构

```typescript
interface HierarchicalCountryData {
  name: string;                    // 国家名称
  code: string;                    // 国家代码
  visitCount: number;              // 访问次数
  totalPoints: number;             // 总点位数
  coordinates?: [number, number];  // 国家中心坐标
  regions: RegionData[];           // 区域列表
  cities: CityData[];              // 直属城市列表（没有区域的城市）
}

interface RegionData {
  name: string;                    // 区域名称
  visitCount: number;              // 访问次数
  coordinates?: [number, number];  // 区域中心坐标
  cities: CityData[];              // 城市列表
}

interface CityData {
  name: string;                    // 城市名称
  visitCount: number;              // 访问次数
  coordinates: [number, number];   // 城市坐标
  points: TravelPoint[];           // 该城市的旅行点位
}
```

## 特性亮点

1. **智能分层**: 自动根据地址信息提取区域层级，没有区域信息的城市直接放在国家下
2. **响应式设计**: 支持键盘导航和无障碍访问
3. **性能优化**: 使用React hooks优化渲染性能
4. **用户体验**: 平滑的展开/收缩动画和地图过渡效果
5. **数据完整性**: 保持与现有数据结构的兼容性

## 后续优化建议

1. **区域识别增强**: 可以集成更完善的地理数据库来提高区域识别准确性
2. **批量操作**: 支持批量展开/收缩所有层级
3. **搜索过滤**: 在统计面板中添加搜索功能
4. **数据导出**: 支持按层级结构导出统计数据
5. **可视化增强**: 添加统计图表显示访问分布 