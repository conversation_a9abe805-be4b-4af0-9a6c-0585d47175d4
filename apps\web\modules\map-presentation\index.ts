"use client";

// 类型导出
export * from "./types";

// 工具函数导出
export * from "./utils";

// 地图初始化函数导出
export { initializeGoogleMap } from "./initialize-map";

// 地图动画 Hook 导出
export { useMapAnimation } from "./use-map-animation";

// 活动标记动画导出
export {
	createPulseEffect,
	startPulseAnimation,
	stopPulseAnimation,
} from "./active-marker-animation";

// 路线动画导出
export {
	createSolidLineWithSingleArrow,
	generateSmoothPath,
	createAnimatedRoute,
} from "./route-animation-utils";
