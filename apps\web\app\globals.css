@import "tailwindcss";
@import "fumadocs-ui/css/neutral.css";
@import "fumadocs-ui/css/preset.css";
@import "@repo/tailwind-config/theme.css";
@import "@repo/tailwind-config/tailwind-animate.css";
@import "../styles/custom-scrollbar.css";

@source "../node_modules/fumadocs-ui/dist/**/*.js";

@variant dark (&:where(.dark, .dark *));

pre.shiki {
	@apply mb-4 rounded-lg p-6;
}

#nd-sidebar {
	@apply bg-card! top-[4.5rem] md:h-[calc(100dvh-4.5rem)]!;

	button[data-search-full] {
		@apply bg-transparent;
	}
}

#nd-page .prose {
	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		a {
			@apply no-underline!;
		}
	}
}

div[role="tablist"].bg-secondary {
	@apply bg-muted!;
}

input[cmdk-input] {
	@apply border-none focus-visible:ring-0;
}

/* Custom scrollbar for popover content - fresh and artistic style */
.popover-scrollbar::-webkit-scrollbar {
	width: 6px; /* 滚动条宽度 */
	height: 6px; /* 用于水平滚动条 (如果需要) */
}

.popover-scrollbar::-webkit-scrollbar-track {
	background: #f3f4f6; /* 轨道背景色 (Tailwind gray-100) - 清新淡雅 */
	border-radius: 10px; /* 圆角 */
}

.popover-scrollbar::-webkit-scrollbar-thumb {
	background: #d1d5db; /* 滑块颜色 (Tailwind gray-300) - 柔和的灰色 */
	border-radius: 10px; /* 圆角 */
}

.popover-scrollbar::-webkit-scrollbar-thumb:hover {
	background: #9ca3af; /* 滑块悬停颜色 (Tailwind gray-400) - 略深一点 */
}

/* 兼容 Firefox */
.popover-scrollbar {
	scrollbar-width: thin; /* 细滚动条 */
	scrollbar-color: #d1d5db #f3f4f6; /* 滑块颜色 轨道颜色 */
}

/* Diary Header 动画效果 */
@keyframes slide-down {
	0% {
		opacity: 0;
		transform: translateY(-10px);
	}
	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes slide-left {
	0% {
		opacity: 0;
		transform: translateX(10px);
	}
	100% {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes fade-in-up {
	0% {
		opacity: 0;
		transform: translateY(20px);
	}
	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

.animate-slide-down {
	animation: slide-down 0.6s ease-out;
}

.animate-slide-left {
	animation: slide-left 0.5s ease-out 0.1s both;
}

.animate-fade-in-up {
	animation: fade-in-up 0.7s ease-out;
}

/* Diary Header 丝滑过渡效果 */
.diary-header-transition {
	transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

.diary-title-smooth {
	transition: font-size 0.7s cubic-bezier(0.4, 0, 0.2, 1), text-align 0.7s
		cubic-bezier(0.4, 0, 0.2, 1), margin 0.7s cubic-bezier(0.4, 0, 0.2, 1),
		transform 0.7s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 按钮悬停缩放效果 */
.diary-button:hover {
	transform: scale(1.05);
	transition: all 0.2s ease-out;
}

.diary-button {
	transition: all 0.2s ease-out;
}
