"use client";

import { useAnalytics } from "@modules/analytics";
import { useCallback, useRef, useState } from "react";
import { MAPBOX_GEOCODING_API, MAPBOX_TOKEN } from "../constants/mapConfig";
import type { GeocodeFeature, GeocodeResponse } from "../types";

export function useSearch() {
	const { trackEvent } = useAnalytics();
	const [searchQuery, setSearchQuery] = useState("");
	const [searchResults, setSearchResults] = useState<GeocodeFeature[]>([]);
	const [isSearching, setIsSearching] = useState(false);
	const [showResults, setShowResults] = useState(false);
	const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

	// 地理编码搜索
	const searchPlaces = useCallback(
		async (query: string) => {
			if (!query.trim() || !MAPBOX_TOKEN) return;

			setIsSearching(true);

			// 追踪搜索开始事件
			trackEvent("travel_stat_search_start", {
				query: query,
				query_length: query.length,
			});

			try {
				const response = await fetch(
					`${MAPBOX_GEOCODING_API}/${encodeURIComponent(query)}.json?access_token=${MAPBOX_TOKEN}&limit=5&types=place,region,country,locality`,
				);

				if (response.ok) {
					const data: GeocodeResponse = await response.json();
					setSearchResults(data.features);
					setShowResults(data.features.length > 0);

					// 追踪搜索结果事件
					trackEvent("travel_stat_search_results", {
						query: query,
						results_count: data.features.length,
						has_results: data.features.length > 0,
					});
				}
			} catch (error) {
				console.error("搜索地点失败:", error);

				// 追踪搜索失败事件
				trackEvent("travel_stat_search_failed", {
					query: query,
					error_message:
						error instanceof Error
							? error.message
							: "Unknown error",
				});
			} finally {
				setIsSearching(false);
			}
		},
		[trackEvent],
	);

	// 防抖搜索
	const handleSearchChange = useCallback(
		(value: string) => {
			setSearchQuery(value);

			if (searchTimeoutRef.current) {
				clearTimeout(searchTimeoutRef.current);
			}

			if (value.trim()) {
				searchTimeoutRef.current = setTimeout(() => {
					searchPlaces(value);
				}, 300);
			} else {
				setSearchResults([]);
				setShowResults(false);
			}
		},
		[searchPlaces],
	);

	// 清空搜索
	const clearSearch = useCallback(() => {
		if (searchQuery) {
			// 追踪搜索清空事件
			trackEvent("travel_stat_search_clear", {
				previous_query: searchQuery,
				had_results: searchResults.length > 0,
			});
		}

		setSearchQuery("");
		setSearchResults([]);
		setShowResults(false);
	}, [searchQuery, searchResults.length, trackEvent]);

	return {
		searchQuery,
		searchResults,
		isSearching,
		showResults,
		handleSearchChange,
		clearSearch,
	};
}
