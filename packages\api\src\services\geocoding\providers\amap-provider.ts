/**
 * 高德地图地理编码提供商
 */

import { BaseGeocodingProvider } from "../base-provider";
import type {
	GeocodingOptions,
	GeocodingProvider,
	UnifiedGeocodingResult,
} from "../types";

/**
 * 高德地图地理编码提供商
 */
export class AmapProvider extends BaseGeocodingProvider {
	private readonly baseUrl = "https://restapi.amap.com/v3/geocode";

	getProviderType(): GeocodingProvider {
		return "amap";
	}

	protected async performGeocode(
		address: string,
		options: GeocodingOptions,
	): Promise<UnifiedGeocodingResult | null> {
		const url = `${this.baseUrl}/geo`;
		const params = new URLSearchParams({
			key: this.config.apiKey,
			address: address,
			output: "JSON",
		});

		// 添加可选参数
		if (options.limit) {
			params.set("batch", "false"); // 高德不支持批量，但可以限制结果
		}

		const response = await this.makeRequest(`${url}?${params}`);

		if (!response.success || !response.rawData) {
			return null;
		}

		const data = response.rawData;

		if (data.status === "1" && data.geocodes && data.geocodes.length > 0) {
			const result = data.geocodes[0];
			const [longitude, latitude] = result.location
				.split(",")
				.map(Number);

			return {
				longitude,
				latitude,
				formattedAddress: this.buildFormattedAddress(result),
				addressComponents: {
					country: result.country,
					province: result.province,
					city: result.city,
					district: result.district,
					street: result.street,
					streetNumber: result.number,
				},
				confidence: this.mapAmapLevel(result.level),
				placeTypes: [result.level],
				placeId: result.adcode,
				provider: "amap",
				responseTime: response.responseTime,
			};
		}

		return null;
	}

	protected async performReverseGeocode(
		longitude: number,
		latitude: number,
		options: GeocodingOptions,
	): Promise<UnifiedGeocodingResult | null> {
		const url = `${this.baseUrl}/regeo`;
		const params = new URLSearchParams({
			key: this.config.apiKey,
			location: `${longitude},${latitude}`,
			output: "JSON",
			extensions: "base",
		});

		const response = await this.makeRequest(`${url}?${params}`);

		if (!response.success || !response.rawData) {
			return null;
		}

		const data = response.rawData;

		if (data.status === "1" && data.regeocode) {
			const regeocode = data.regeocode;
			const addressComponent = regeocode.addressComponent;

			return {
				longitude,
				latitude,
				formattedAddress: regeocode.formatted_address,
				addressComponents: {
					country: addressComponent.country,
					province: addressComponent.province,
					city: addressComponent.city,
					district: addressComponent.district,
					street: addressComponent.streetNumber?.street,
					streetNumber: addressComponent.streetNumber?.number,
				},
				confidence: "high",
				placeTypes: ["reverse_geocoded"],
				placeId: addressComponent.adcode,
				provider: "amap",
				responseTime: response.responseTime,
			};
		}

		return null;
	}

	/**
	 * 构建格式化地址
	 */
	private buildFormattedAddress(result: any): string {
		const parts = [
			result.country,
			result.province,
			result.city,
			result.district,
			result.street,
			result.number,
		].filter(Boolean);

		return parts.join("");
	}

	/**
	 * 映射高德地图的匹配级别到置信度
	 */
	private mapAmapLevel(level: string): "high" | "medium" | "low" {
		const highLevels = ["门牌号", "单元号", "兴趣点", "道路"];
		const mediumLevels = ["道路交叉路口", "热点商圈", "村庄"];

		if (highLevels.includes(level)) return "high";
		if (mediumLevels.includes(level)) return "medium";
		return "low";
	}
}
