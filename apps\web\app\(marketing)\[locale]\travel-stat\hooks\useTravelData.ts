"use client";

import { useAnalytics } from "@modules/analytics";
import type { TravelPointImage } from "@repo/database/src/types/travel-diary";
import { useCallback, useEffect, useMemo, useState } from "react";
import type { CountryData, GeocodeFeature, TravelPoint } from "../types";
import { extractCountryInfo } from "../utils/dataUtils";
import { imageStorage } from "../utils/imageStorage";

export function useTravelData() {
	const { trackEvent } = useAnalytics();
	const [travelPoints, setTravelPoints] = useState<TravelPoint[]>([]);
	const [visitedCountries, setVisitedCountries] = useState<CountryData[]>([]);

	// 清理无效的图片URL引用
	const cleanupInvalidImageUrls = useCallback(
		async (points: TravelPoint[]): Promise<TravelPoint[]> => {
			console.log("🧹 开始清理无效图片URL...");

			const cleanedPoints = await Promise.all(
				points.map(async (point) => {
					if (!point.images?.length) return point;

					const cleanedImages = await Promise.all(
						point.images.map(
							async (image): Promise<TravelPointImage | null> => {
								const { url } = image;

								// 保留有效的URL类型
								if (
									url.startsWith("indexeddb:") ||
									url.startsWith("data:") ||
									url.startsWith("http")
								) {
									return image;
								}

								// 检查blob URL是否有效
								if (url.startsWith("blob:")) {
									try {
										const response = await fetch(url);
										if (response.ok) {
											console.log(
												"✅ blob URL仍然有效:",
												url.slice(0, 50),
											);
											return image;
										}
									} catch {
										// blob URL无效，忽略错误
									}

									console.warn(
										"🗑️ 清理失效的blob URL:",
										url.slice(0, 50),
									);
									return null; // 移除失效的blob URL
								}

								// 保留其他类型的URL（以防万一）
								return image;
							},
						),
					);

					// 过滤掉null值
					const validImages = cleanedImages.filter(
						(img): img is TravelPointImage => img !== null,
					);

					if (validImages.length !== point.images.length) {
						console.log(
							`🔧 点位 ${point.id.slice(0, 8)} 清理了 ${point.images.length - validImages.length} 个无效图片引用`,
						);
					}

					return {
						...point,
						images: validImages,
					};
				}),
			);

			const totalCleaned = points.reduce((sum, point, index) => {
				const original = point.images?.length || 0;
				const cleaned = cleanedPoints[index].images?.length || 0;
				return sum + (original - cleaned);
			}, 0);

			if (totalCleaned > 0) {
				console.log(
					`✅ 图片URL清理完成，共清理 ${totalCleaned} 个无效引用`,
				);
			}

			return cleanedPoints;
		},
		[],
	);

	// 从 localStorage 加载数据
	useEffect(() => {
		const loadData = async () => {
			const savedPoints = localStorage.getItem("travelFootprints");
			const savedCountries = localStorage.getItem("visitedCountries");

			let loadedPoints = 0;
			let loadedCountries = 0;

			if (savedPoints) {
				try {
					const points = JSON.parse(savedPoints);
					// 🔧 修复：反序列化时处理 Date 对象
					const normalizedPoints = points.map((point: any) => ({
						...point,
						// 如果有 date 字段且不是 Date 对象，转换为 Date
						...(point.date && typeof point.date === "string"
							? { date: new Date(point.date) }
							: {}),
					}));

					// 🧹 清理无效的图片URL
					const cleanedPoints =
						await cleanupInvalidImageUrls(normalizedPoints);

					setTravelPoints(cleanedPoints);
					loadedPoints = cleanedPoints.length;

					// 如果清理后数据有变化，重新保存到localStorage
					if (
						JSON.stringify(cleanedPoints) !==
						JSON.stringify(normalizedPoints)
					) {
						localStorage.setItem(
							"travelFootprints",
							JSON.stringify(cleanedPoints),
						);
						console.log("💾 清理后的数据已重新保存到localStorage");
					}

					console.log("✅ 成功加载旅行足迹数据:", {
						点位数量: loadedPoints,
						示例点位: cleanedPoints[0]
							? {
									id: cleanedPoints[0].id.slice(0, 8),
									name: cleanedPoints[0].name,
									hasImages:
										!!cleanedPoints[0].images?.length,
									imageCount:
										cleanedPoints[0].images?.length || 0,
									imageTypes: cleanedPoints[0].images?.map(
										(img) =>
											img.url.startsWith("indexeddb:")
												? "indexeddb"
												: img.url.startsWith("blob:")
													? "blob"
													: img.url.startsWith(
																"data:",
															)
														? "base64"
														: "other",
									),
									dateType: typeof cleanedPoints[0].date,
								}
							: null,
					});
				} catch (error) {
					console.error("加载旅行足迹数据失败:", error);
				}
			}

			if (savedCountries) {
				try {
					const countries = JSON.parse(savedCountries);
					// 确保每个国家对象都有必需的字段
					const normalizedCountries = countries.map(
						(country: any) => ({
							...country,
							visitedCities: country.visitedCities || [],
						}),
					);
					setVisitedCountries(normalizedCountries);
					loadedCountries = normalizedCountries.length;
				} catch (error) {
					console.error("加载国家数据失败:", error);
				}
			}

			// 追踪数据加载事件（仅在有数据时）
			if (loadedPoints > 0 || loadedCountries > 0) {
				trackEvent("travel_stat_data_loaded", {
					points_count: loadedPoints,
					countries_count: loadedCountries,
				});
			}
		};

		loadData();
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []); // 只在组件挂载时执行一次，忽略trackEvent和cleanupInvalidImageUrls的变化

	// 保存数据到 localStorage
	const saveToLocalStorage = useCallback(
		(points: TravelPoint[], countries: CountryData[]) => {
			try {
				const serializedPoints = JSON.stringify(points);
				const serializedCountries = JSON.stringify(countries);

				localStorage.setItem("travelFootprints", serializedPoints);
				localStorage.setItem("visitedCountries", serializedCountries);

				console.log("💾 数据已保存到localStorage:", {
					点位数量: points.length,
					国家数量: countries.length,
					数据大小: {
						points: `${(serializedPoints.length / 1024).toFixed(1)}KB`,
						countries: `${(serializedCountries.length / 1024).toFixed(1)}KB`,
						总计: `${((serializedPoints.length + serializedCountries.length) / 1024).toFixed(1)}KB`,
					},
				});
			} catch (error) {
				console.error("保存数据失败:", error);
				// 如果是因为存储空间不足，提示用户
				if (
					error instanceof Error &&
					error.name === "QuotaExceededError"
				) {
					alert("存储空间不足，请清理一些数据或减少图片大小");
				}
			}
		},
		[],
	);

	// 添加旅行点
	const addTravelPoint = useCallback(
		(feature: GeocodeFeature) => {
			const { country, countryCode } = extractCountryInfo(feature);
			const city = feature.place_name.split(",")[0];

			const newPoint: TravelPoint = {
				id: `${Date.now()}-${Math.random()}`,
				name: feature.place_name,
				coordinates: feature.center,
				country,
				city,
				timestamp: Date.now(),
			};

			const updatedPoints = [...travelPoints, newPoint];

			// 更新国家统计 - 采用不可变方式
			let countryExists = false;
			const updatedCountries = visitedCountries.map((c) => {
				if (c.name === country) {
					countryExists = true;
					// 确保 visitedCities 是数组，防止未定义错误
					const currentVisitedCities = c.visitedCities || [];
					// 找到匹配的国家，返回一个新的更新过的对象
					return {
						...c,
						visitCount: c.visitCount + 1,
						totalPoints: c.totalPoints + 1,
						lastVisit: new Date().toISOString(),
						// 添加城市到访问列表（如果不存在）
						visitedCities:
							city && !currentVisitedCities.includes(city)
								? [...currentVisitedCities, city]
								: currentVisitedCities,
					};
				}
				// 不是目标国家，返回原始对象
				return c;
			});

			// 如果国家不存在，则添加新国家
			if (!countryExists && country) {
				const currentDate = new Date().toISOString();
				updatedCountries.push({
					name: country,
					code: countryCode,
					visitCount: 1,
					visitedCities: city ? [city] : [],
					totalPoints: 1,
					firstVisit: currentDate,
					lastVisit: currentDate,
				});
			}

			setTravelPoints(updatedPoints);
			setVisitedCountries(updatedCountries);
			saveToLocalStorage(updatedPoints, updatedCountries);

			return newPoint;
		},
		[travelPoints, visitedCountries, saveToLocalStorage],
	);

	// 添加包含详细信息的旅行点
	const addTravelPointWithDetails = useCallback(
		(
			feature: GeocodeFeature,
			details: {
				description: string;
				date: string;
				image: TravelPointImage | null;
			},
		) => {
			const { country, countryCode } = extractCountryInfo(feature);
			const city = feature.place_name.split(",")[0];

			const selectedDate = new Date(details.date);

			const newPoint: TravelPoint = {
				id: `${Date.now()}-${Math.random()}`,
				name: feature.place_name, // 直接使用位置名称
				coordinates: feature.center,
				country,
				city,
				timestamp: selectedDate.getTime(),
				date: selectedDate,
				description: details.description,
				images: details.image ? [details.image] : [],
			};

			const updatedPoints = [...travelPoints, newPoint];

			// 更新国家统计 - 采用不可变方式
			let countryExists = false;
			const updatedCountries = visitedCountries.map((c) => {
				if (c.name === country) {
					countryExists = true;
					// 确保 visitedCities 是数组，防止未定义错误
					const currentVisitedCities = c.visitedCities || [];
					// 找到匹配的国家，返回一个新的更新过的对象
					return {
						...c,
						visitCount: c.visitCount + 1,
						totalPoints: c.totalPoints + 1,
						lastVisit: new Date().toISOString(),
						// 添加城市到访问列表（如果不存在）
						visitedCities:
							city && !currentVisitedCities.includes(city)
								? [...currentVisitedCities, city]
								: currentVisitedCities,
					};
				}
				// 不是目标国家，返回原始对象
				return c;
			});

			// 如果国家不存在，则添加新国家
			if (!countryExists && country) {
				const currentDate = new Date().toISOString();
				updatedCountries.push({
					name: country,
					code: countryCode,
					visitCount: 1,
					visitedCities: city ? [city] : [],
					totalPoints: 1,
					firstVisit: currentDate,
					lastVisit: currentDate,
				});
			}

			setTravelPoints(updatedPoints);
			setVisitedCountries(updatedCountries);
			saveToLocalStorage(updatedPoints, updatedCountries);

			return newPoint;
		},
		[travelPoints, visitedCountries, saveToLocalStorage],
	);

	// 删除旅行点
	const removeTravelPoint = useCallback(
		async (pointId: string) => {
			const pointToRemove = travelPoints.find((p) => p.id === pointId);
			if (!pointToRemove) return;

			// 🗑️ 清理相关的IndexedDB文件
			if (pointToRemove.images?.length) {
				for (const image of pointToRemove.images) {
					if (image.url.startsWith("indexeddb:")) {
						const fileId = image.url.replace("indexeddb:", "");
						try {
							await imageStorage.deleteFile(fileId);
							console.log("🗑️ 已删除关联的图片文件:", fileId);
						} catch (error) {
							console.warn("⚠️ 删除图片文件失败:", fileId, error);
						}
					}
				}
			}

			// 追踪删除点位事件
			trackEvent("travel_stat_point_removed", {
				point_name: pointToRemove.name,
				country: pointToRemove.country,
				city: pointToRemove.city,
				remaining_points: travelPoints.length - 1,
			});

			const updatedPoints = travelPoints.filter((p) => p.id !== pointId);

			// 更新国家统计 - 采用不可变方式
			const updatedCountries = visitedCountries
				.map((c) => {
					if (c.name === pointToRemove.country) {
						// 找到匹配的国家，返回一个新的更新过的对象
						return {
							...c,
							visitCount: c.visitCount - 1,
							totalPoints: c.totalPoints - 1,
						};
					}
					return c;
				})
				.filter((c) => c.visitCount > 0); // 过滤掉访问次数为0的国家

			setTravelPoints(updatedPoints);
			setVisitedCountries(updatedCountries);
			saveToLocalStorage(updatedPoints, updatedCountries);
		},
		[travelPoints, visitedCountries, saveToLocalStorage, trackEvent],
	);

	// 清空所有数据
	const clearAllData = useCallback(
		async (clearImages = false) => {
			// 🗑️ 根据选项决定是否清理IndexedDB数据
			if (clearImages) {
				try {
					await imageStorage.clearAll();
					console.log("🗑️ 已清理所有IndexedDB图片数据");
				} catch (error) {
					console.warn("⚠️ 清理IndexedDB数据失败:", error);
				}
			} else {
				console.log("📷 保留IndexedDB图片数据（用户选择不清理）");
			}

			// 追踪清空数据事件
			trackEvent("travel_stat_data_clear_completed", {
				cleared_points: travelPoints.length,
				cleared_countries: visitedCountries.length,
				cleared_images: clearImages,
			});

			setTravelPoints([]);
			setVisitedCountries([]);
			localStorage.removeItem("travelFootprints");
			localStorage.removeItem("visitedCountries");
		},
		[travelPoints.length, visitedCountries.length, trackEvent],
	);

	// 设置数据（用于导入）
	const setData = useCallback(
		async (points: TravelPoint[], countries: CountryData[]) => {
			// 🧹 清理导入数据中的无效图片URL
			const cleanedPoints = await cleanupInvalidImageUrls(points);

			// 确保每个国家对象都有必需的字段
			const normalizedCountries = countries.map((country) => ({
				...country,
				visitedCities: country.visitedCities || [],
			}));

			setTravelPoints(cleanedPoints);
			setVisitedCountries(normalizedCountries);
			saveToLocalStorage(cleanedPoints, normalizedCountries);
		},
		[saveToLocalStorage, cleanupInvalidImageUrls],
	);

	return useMemo(() => {
		return {
			travelPoints,
			visitedCountries,
			addTravelPoint,
			addTravelPointWithDetails,
			removeTravelPoint,
			clearAllData,
			setData,
		};
	}, [
		travelPoints,
		visitedCountries,
		addTravelPoint,
		addTravelPointWithDetails,
		removeTravelPoint,
		clearAllData,
		setData,
	]);
}

export type UseTravelDataReturn = ReturnType<typeof useTravelData>;
