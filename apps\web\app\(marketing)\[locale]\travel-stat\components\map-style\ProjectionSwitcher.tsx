"use client";

import { Button } from "@ui/components/button";
import { Card } from "@ui/components/card";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@ui/components/dropdown-menu";
import { ChevronDown, Globe, Map as MapIcon } from "lucide-react";
import { useState } from "react";

// 投影类型定义
export type MapProjectionType =
	| "mercator"
	| "globe"
	| "albers"
	| "lambertConformalConic"
	| "equalEarth"
	| "equirectangular"
	| "naturalEarth"
	| "winkelTripel";

// 投影配置
export const MAP_PROJECTION_CONFIGS: Record<
	MapProjectionType,
	{
		name: string;
		description: string;
		category: "2D" | "3D" | "等面积" | "等角" | "妥协";
		icon: "globe" | "map";
		useCase: string;
		needsParams?: boolean;
	}
> = {
	mercator: {
		name: "墨卡托投影",
		description: "经典网络地图投影，保持角度但极地失真",
		category: "等角",
		icon: "map",
		useCase: "导航和细节查看",
	},
	globe: {
		name: "3D 球体",
		description: "真实的地球球体视图，沉浸式体验",
		category: "3D",
		icon: "globe",
		useCase: "全球视角展示",
	},
	albers: {
		name: "阿尔伯斯投影",
		description: "等面积圆锥投影，面积准确",
		category: "等面积",
		icon: "map",
		useCase: "统计数据展示",
		needsParams: true,
	},
	lambertConformalConic: {
		name: "兰伯特投影",
		description: "等角圆锥投影，角度准确",
		category: "等角",
		icon: "map",
		useCase: "中纬度地区导航",
		needsParams: true,
	},
	equalEarth: {
		name: "等面积地球",
		description: "现代伪圆柱投影，平衡失真",
		category: "等面积",
		icon: "map",
		useCase: "世界地图展示",
	},
	equirectangular: {
		name: "等距圆柱",
		description: "简单矩形投影，经纬线规则网格",
		category: "2D",
		icon: "map",
		useCase: "简单数据展示",
	},
	naturalEarth: {
		name: "自然地球",
		description: "视觉优化的伪圆柱投影",
		category: "妥协",
		icon: "map",
		useCase: "美观世界地图",
	},
	winkelTripel: {
		name: "温克尔三重",
		description: "国家地理推荐，综合平衡投影",
		category: "妥协",
		icon: "map",
		useCase: "综合性世界地图",
	},
};

const CATEGORY_COLORS: Record<string, string> = {
	"2D": "bg-blue-100 text-blue-700 border-blue-200",
	"3D": "bg-purple-100 text-purple-700 border-purple-200",
	等面积: "bg-green-100 text-green-700 border-green-200",
	等角: "bg-orange-100 text-orange-700 border-orange-200",
	妥协: "bg-gray-100 text-gray-700 border-gray-200",
};

interface ProjectionSwitcherProps {
	currentProjection: MapProjectionType;
	onProjectionChange: (projection: MapProjectionType) => void;
	compact?: boolean;
}

export function ProjectionSwitcher({
	currentProjection,
	onProjectionChange,
	compact = false,
}: ProjectionSwitcherProps) {
	const [isOpen, setIsOpen] = useState(false);

	const currentConfig = MAP_PROJECTION_CONFIGS[currentProjection];
	const IconComponent = currentConfig.icon === "globe" ? Globe : MapIcon;

	if (compact) {
		return (
			<DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
				<DropdownMenuTrigger asChild>
					<Button
						variant="outline"
						size="sm"
						className="bg-white/90 backdrop-blur-sm border-sky-200 hover:bg-sky-50 shadow-lg transition-all duration-200"
						title={`当前投影: ${currentConfig.name}`}
					>
						<IconComponent className="w-4 h-4 mr-1" />
						<span className="text-xs">投影</span>
						<ChevronDown className="w-3 h-3 ml-1" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent
					align="start"
					className="w-80 bg-white/95 backdrop-blur-sm border-sky-200"
				>
					<DropdownMenuLabel className="text-sm font-semibold">
						选择地图投影
					</DropdownMenuLabel>
					<DropdownMenuSeparator />

					{Object.entries(MAP_PROJECTION_CONFIGS).map(
						([key, config]) => {
							const projectionKey = key as MapProjectionType;
							const ProjectionIcon =
								config.icon === "globe" ? Globe : MapIcon;
							const isActive =
								projectionKey === currentProjection;

							return (
								<DropdownMenuItem
									key={key}
									onClick={() => {
										onProjectionChange(projectionKey);
										setIsOpen(false);
									}}
									className={`cursor-pointer p-3 ${
										isActive
											? "bg-sky-100"
											: "hover:bg-sky-50"
									}`}
								>
									<div className="flex items-start gap-3 w-full">
										<ProjectionIcon
											className={`w-4 h-4 mt-0.5 ${
												isActive
													? "text-sky-600"
													: "text-gray-500"
											}`}
										/>
										<div className="flex-1 min-w-0">
											<div className="flex items-center gap-2 mb-1">
												<span
													className={`text-sm font-medium ${
														isActive
															? "text-sky-700"
															: "text-gray-700"
													}`}
												>
													{config.name}
												</span>
												<span
													className={`text-xs px-1.5 py-0.5 rounded border ${
														CATEGORY_COLORS[
															config.category
														]
													}`}
												>
													{config.category}
												</span>
											</div>
											<p className="text-xs text-gray-500 mb-1">
												{config.description}
											</p>
											<p className="text-xs text-gray-400">
												适用: {config.useCase}
											</p>
										</div>
									</div>
								</DropdownMenuItem>
							);
						},
					)}
				</DropdownMenuContent>
			</DropdownMenu>
		);
	}

	return (
		<Card className="p-4 bg-white/80 backdrop-blur-sm border-sky-200">
			<h4 className="text-sm font-semibold text-gray-800 mb-3 flex items-center gap-2">
				<IconComponent className="w-4 h-4 text-sky-500" />
				地图投影
			</h4>

			<div className="space-y-2">
				{Object.entries(MAP_PROJECTION_CONFIGS).map(([key, config]) => {
					const projectionKey = key as MapProjectionType;
					const ProjectionIcon =
						config.icon === "globe" ? Globe : MapIcon;
					const isActive = projectionKey === currentProjection;

					return (
						<button
							key={key}
							type="button"
							onClick={() => onProjectionChange(projectionKey)}
							className={`w-full text-left p-3 rounded-md border transition-all duration-200 ${
								isActive
									? "bg-sky-100 border-sky-300 shadow-sm"
									: "bg-white/60 border-gray-200 hover:bg-sky-50 hover:border-sky-200"
							}`}
						>
							<div className="flex items-start gap-3">
								<ProjectionIcon
									className={`w-4 h-4 mt-0.5 ${
										isActive
											? "text-sky-600"
											: "text-gray-500"
									}`}
								/>
								<div className="flex-1 min-w-0">
									<div className="flex items-center gap-2 mb-1">
										<span
											className={`text-sm font-medium ${
												isActive
													? "text-sky-700"
													: "text-gray-700"
											}`}
										>
											{config.name}
										</span>
										<span
											className={`text-xs px-1.5 py-0.5 rounded border ${
												CATEGORY_COLORS[config.category]
											}`}
										>
											{config.category}
										</span>
									</div>
									<p className="text-xs text-gray-500 mb-1">
										{config.description}
									</p>
									<p className="text-xs text-gray-400">
										适用: {config.useCase}
									</p>
								</div>
							</div>
						</button>
					);
				})}
			</div>
		</Card>
	);
}
