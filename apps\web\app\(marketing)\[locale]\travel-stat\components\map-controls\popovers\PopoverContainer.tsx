"use client";

import type { ReactNode } from "react";

interface PopoverContainerProps {
	children: ReactNode;
	maxHeight?: string;
	className?: string;
}

/**
 * 统一的 popover 容器组件
 * 确保所有 popover 都有一致的滚动行为和尺寸限制
 */
export function PopoverContainer({
	children,
	maxHeight = "70vh",
	className = "",
}: PopoverContainerProps) {
	return (
		<div
			className={`w-full max-w-sm sm:max-w-md flex flex-col ${className}`}
			style={{ maxHeight }}
		>
			{/* 滚动内容区域 */}
			<div
				className="flex-1 overflow-y-auto p-4 
							scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100
							hover:scrollbar-thumb-gray-400"
			>
				{children}
			</div>
		</div>
	);
}
