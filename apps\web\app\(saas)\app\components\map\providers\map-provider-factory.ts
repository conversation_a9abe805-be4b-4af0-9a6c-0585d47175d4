"use client";

import type { MapServiceProvider } from "../types";
import baiduMapProvider from "./baidu-map-provider";
import reactGoogleMapProvider from "./react-google-map-provider";

// 地图提供商类型
export type MapProviderType = "google" | "baidu";

/**
 * 地图提供商工厂类
 * 用于管理不同的地图提供商实现，并提供统一的访问接口
 */
class MapProviderFactory {
	private static instance: MapProviderFactory;
	private providers: Map<MapProviderType, MapServiceProvider> = new Map();
	private currentProvider: MapProviderType = "google";

	private constructor() {
		// 注册默认提供商 - 使用React Google Maps实现
		this.registerProvider("google", reactGoogleMapProvider);
		// 注册百度地图提供商
		this.registerProvider("baidu", baiduMapProvider);
	}

	/**
	 * 获取工厂单例实例
	 */
	public static getInstance(): MapProviderFactory {
		if (!MapProviderFactory.instance) {
			MapProviderFactory.instance = new MapProviderFactory();
		}
		return MapProviderFactory.instance;
	}

	/**
	 * 注册地图提供商
	 */
	public registerProvider(
		type: MapProviderType,
		provider: MapServiceProvider,
	): void {
		this.providers.set(type, provider);
	}

	/**
	 * 设置当前使用的地图提供商
	 */
	public setCurrentProvider(type: MapProviderType): void {
		if (!this.providers.has(type)) {
			throw new Error(`未注册的地图提供商类型: ${type}`);
		}
		this.currentProvider = type;
	}

	/**
	 * 获取当前地图提供商
	 */
	public getCurrentProvider(): MapServiceProvider {
		const provider = this.providers.get(this.currentProvider);
		if (!provider) {
			throw new Error(`未找到地图提供商: ${this.currentProvider}`);
		}
		return provider;
	}

	/**
	 * 获取当前地图提供商类型
	 */
	public getCurrentProviderType(): MapProviderType {
		return this.currentProvider;
	}

	/**
	 * 获取指定类型的地图提供商
	 */
	public getProvider(type: MapProviderType): MapServiceProvider {
		const provider = this.providers.get(type);
		if (!provider) {
			throw new Error(`未找到地图提供商: ${type}`);
		}
		return provider;
	}

	/**
	 * 检查指定类型的提供商是否已注册
	 */
	public hasProvider(type: MapProviderType): boolean {
		return this.providers.has(type);
	}

	/**
	 * 获取所有已注册的提供商类型
	 */
	public getRegisteredProviderTypes(): MapProviderType[] {
		return Array.from(this.providers.keys());
	}
}

// 导出工厂单例
export default MapProviderFactory.getInstance();
