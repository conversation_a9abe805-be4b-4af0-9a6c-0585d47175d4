// 使用统一的类型导入，简化导入路径
import type {
	AnimationTheme,
	AtmosphereTheme,
	ColorThemeType,
	CountryData,
	MapProjectionType,
	MapStyleType,
	MarkerStyleType,
	ReactNode,
	RefObject,
	TravelPoint,
} from "../types";

export interface MapControlsProps {
	mapLoaded: boolean;
	atmosphereTheme: AtmosphereTheme;
	mapStyle: MapStyleType;
	animationTheme: AnimationTheme;
	mapProjection: MapProjectionType;
	currentColorTheme: ColorThemeType;
	markerStyle: MarkerStyleType;
	markerTheme: string;
	currentEmoji: string;
	currentEmojiColor: string;
	hideOutline: boolean;
	mapRef: RefObject<mapboxgl.Map | null>;
	travelPoints: TravelPoint[];
	visitedCountries: CountryData[];
	// 显示控制
	showTooltips: boolean;
	showMarkers: boolean;
	onAtmosphereChange: (theme: AtmosphereTheme) => void;
	onMapStyleChange: (style: MapStyleType) => void;
	onAnimationChange: (theme: AnimationTheme) => void;
	onProjectionChange: (projection: MapProjectionType) => void;
	onColorThemeChange: (theme: ColorThemeType) => void;
	onMarkerStyleChange: (style: MarkerStyleType) => void;
	onMarkerThemeChange: (theme: string) => void;
	onEmojiChange: (emoji: string) => void;
	onEmojiColorChange: (color: string) => void;
	onHideOutlineChange: (hide: boolean) => void;
	onShowTooltipsChange: (show: boolean) => void;
	onShowMarkersChange: (show: boolean) => void;
}

export interface ControlItem {
	id: string;
	icon: ReactNode;
	title: string;
	buttonText: string;
	hasPopover?: boolean;
	popoverContent?: ReactNode;
	onClick?: () => void;
	disabled?: boolean;
}
