"use client";

import React from "react";
import type { FeatureRequest } from "../types";

interface VoteButtonProps {
	featureRequest: FeatureRequest;
	onVote: (featureRequestId: string) => Promise<void>;
	onUnvote: (featureRequestId: string) => Promise<void>;
	isVoting?: boolean;
	showVoteCount?: boolean;
	size?: "sm" | "md" | "lg";
	variant?: "default" | "outline" | "ghost";
	className?: string;
	disabled?: boolean;
}

export function VoteButton({
	featureRequest,
	onVote,
	onUnvote,
	isVoting = false,
	showVoteCount = true,
	size = "md",
	variant = "default",
	className = "",
	disabled = false,
}: VoteButtonProps) {
	const { id, hasVoted, voteCount } = featureRequest;

	const handleClick = async () => {
		if (disabled || isVoting) {
			return;
		}

		try {
			if (hasVoted) {
				await onUnvote(id);
			} else {
				await onVote(id);
			}
		} catch (error) {
			console.error("投票操作失败:", error);
		}
	};

	// 尺寸样式
	const sizeClasses = {
		sm: "px-2 py-1 text-xs",
		md: "px-3 py-2 text-sm",
		lg: "px-4 py-3 text-base",
	};

	// 变体样式
	const getVariantClasses = (isActive: boolean) => {
		const baseClasses =
			"font-medium rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2";

		if (variant === "outline") {
			return isActive
				? `${baseClasses} bg-blue-50 border-2 border-blue-500 text-blue-700 hover:bg-blue-100 focus:ring-blue-500`
				: `${baseClasses} bg-white border-2 border-gray-300 text-gray-700 hover:border-gray-400 hover:bg-gray-50 focus:ring-gray-500`;
		}

		if (variant === "ghost") {
			return isActive
				? `${baseClasses} bg-blue-100 text-blue-700 hover:bg-blue-200 focus:ring-blue-500`
				: `${baseClasses} bg-transparent text-gray-600 hover:bg-gray-100 focus:ring-gray-500`;
		}

		// default variant
		return isActive
			? `${baseClasses} bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-sm`
			: `${baseClasses} bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500 border border-gray-300`;
	};

	const buttonClasses = [
		sizeClasses[size],
		getVariantClasses(hasVoted || false),
		disabled || isVoting
			? "opacity-50 cursor-not-allowed"
			: "cursor-pointer",
		"inline-flex items-center gap-2",
		className,
	].join(" ");

	return (
		<button
			type="button"
			className={buttonClasses}
			onClick={handleClick}
			disabled={disabled || isVoting}
			aria-label={hasVoted ? "取消投票" : "投票支持"}
			title={hasVoted ? "点击取消投票" : "点击投票支持此功能"}
		>
			{/* 投票图标 */}
			<span
				className={`transition-transform duration-200 ${hasVoted ? "scale-110" : ""}`}
			>
				{hasVoted ? (
					<svg
						className="w-4 h-4"
						fill="currentColor"
						viewBox="0 0 20 20"
						xmlns="http://www.w3.org/2000/svg"
						aria-label="已投票图标"
					>
						<title>已投票</title>
						<path
							fillRule="evenodd"
							d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"
							clipRule="evenodd"
						/>
					</svg>
				) : (
					<svg
						className="w-4 h-4"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
						xmlns="http://www.w3.org/2000/svg"
						aria-label="投票图标"
					>
						<title>投票</title>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
						/>
					</svg>
				)}
			</span>

			{/* 投票状态文本 */}
			<span>{isVoting ? "..." : hasVoted ? "已投票" : "投票"}</span>

			{/* 投票数量 */}
			{showVoteCount && (
				<span
					className={`font-bold ${hasVoted ? "text-current" : "text-gray-500"}`}
				>
					{voteCount}
				</span>
			)}
		</button>
	);
}
