"use client";

import { cn } from "@ui/lib";
import { Loader2 } from "lucide-react";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import {
	FullscreenControl,
	GeolocateControl,
	Layer,
	type MapboxEvent,
	Marker,
	NavigationControl,
	ScaleControl,
	Source,
} from "react-map-gl";
import MapGL from "react-map-gl"; // 使用 MapGL 代替 GoogleMap
import "mapbox-gl/dist/mapbox-gl.css"; // 引入 Mapbox 样式
import mapboxgl from "mapbox-gl"; // 导入mapboxgl类型

// 使用Mapbox测试API密钥（仅用于开发环境）
const DEV_API_KEY =
	"pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4M29iazA2Z2gycXA4N2pmbDZmangifQ.-g_vE53SD2WrJ6tFX7QHmA";
const MAPBOX_ACCESS_TOKEN = process.env.NEXT_PUBLIC_MAPBOX_TOKEN || DEV_API_KEY;
// 读取 Map Style
const MAPBOX_STYLE =
	process.env.NEXT_PUBLIC_MAPBOX_STYLE ||
	"mapbox://styles/mapbox/streets-v11";

// 默认地图中心和缩放级别
const DEFAULT_CENTER = { lat: 20, lng: 0 };
const DEFAULT_ZOOM = 4;

// 点位类型 - 保持颜色和标签
const ICON_TYPES = {
	PIN: { label: "地点", color: "#3b82f6" },
	LANDMARK: { label: "景点", color: "#7CB342" },
	FOOD: { label: "餐厅", color: "#FF5252" },
	PARK: { label: "公园", color: "#4CAF50" },
	HOTEL: { label: "酒店", color: "#FFA000" },
	SHOPPING: { label: "购物", color: "#BA68C8" },
	TRANSPORT: { label: "交通", color: "#607D8B" },
	OTHER: { label: "其他", color: "#2196F3" },
};

interface TravelPoint {
	id: string;
	date?: Date | undefined;
	location: string;
	description: string;
	images: string[];
	iconType: string;
	coordinates?: { lat: number; lng: number };
}

interface JourneyMapProps {
	points: TravelPoint[];
	activePointId?: string;
	onPointSelect?: (pointId: string) => void;
	className?: string;
	mapResizeTrigger?: number;
}

/**
 * CustomMarker - 自定义标记组件
 */
function CustomMarker({
	point,
	index,
	isActive,
	onClick,
}: {
	point: TravelPoint;
	index: number;
	isActive: boolean;
	onClick?: () => void;
}) {
	// 根据状态和图标类型获取样式
	const iconStyle =
		ICON_TYPES[point.iconType as keyof typeof ICON_TYPES] || ICON_TYPES.PIN;
	const color = iconStyle.color;
	const pinSize = isActive ? "w-9 h-9" : "w-7 h-7";
	const labelSize = isActive ? "text-sm" : "text-xs";
	const zIndex = isActive ? 10 : 5;
	const truncatedLabel =
		point.location.length > 15
			? `${point.location.substring(0, 13)}...`
			: point.location;

	return (
		<Marker
			longitude={getValidCoordinates(point).lng}
			latitude={getValidCoordinates(point).lat}
			anchor="bottom"
			onClick={onClick}
		>
			<div className="flex flex-col items-center" style={{ zIndex }}>
				{/* 位置名称标签 */}
				<div
					className="bg-white/90 px-2 py-1 rounded-md shadow-sm mb-1 text-xs font-medium border whitespace-nowrap max-w-[150px] truncate transition-all duration-200"
					style={{
						color: color,
						borderColor: `${color}40`, // 添加透明度的边框
						transform: isActive ? "scale(1.05)" : "scale(1)",
					}}
				>
					{truncatedLabel}
				</div>

				{/* 大头针样式 */}
				<div className="relative">
					<div
						className={`${pinSize} rounded-full border-2 border-white shadow-md flex items-center justify-center transition-all duration-200`}
						style={{
							backgroundColor: color,
							transform: isActive ? "scale(1.1)" : "scale(1)",
						}}
					>
						<span className={`${labelSize} font-bold text-white`}>
							{index + 1}
						</span>
					</div>
					<div
						className="absolute -bottom-3 left-1/2 transform -translate-x-1/2 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent"
						style={{
							height: "10px",
							borderTopColor: color,
						}}
					/>
				</div>
			</div>
		</Marker>
	);
}

// 添加辅助函数统一处理坐标安全访问
function getValidCoordinates(point: TravelPoint) {
	return {
		lng: point?.coordinates?.lng || 0,
		lat: point?.coordinates?.lat || 0,
	};
}

/**
 * 行程地图组件 - 展示旅行路线和地点
 */
const JourneyMap = ({
	points,
	activePointId,
	onPointSelect,
	className,
	mapResizeTrigger,
}: JourneyMapProps) => {
	const [mapInstance, setMapInstance] = useState<mapboxgl.Map | null>(null);
	const [error, setError] = useState<string | null>(null);
	const [isMapLoaded, setIsMapLoaded] = useState(false);
	const [mapInitialized, setMapInitialized] = useState(false);

	// 默认视图状态
	const initialViewState = {
		longitude: DEFAULT_CENTER.lng,
		latitude: DEFAULT_CENTER.lat,
		zoom: DEFAULT_ZOOM,
	};

	// 获取激活点位的索引
	const activePointIndex = useMemo(() => {
		if (!activePointId) return -1;
		return points.findIndex((point) => point.id === activePointId);
	}, [points, activePointId]);

	// 生成路线GeoJSON
	const routeGeoJson = useMemo(() => {
		if (points.length <= 1) return null;

		// 过滤掉没有有效坐标的点
		const validPoints = points.filter((point) => {
			// 使用可选链安全访问属性
			return (
				point?.coordinates?.lat !== undefined &&
				point?.coordinates?.lng !== undefined
			);
		});

		if (validPoints.length <= 1) {
			return null;
		}

		return {
			type: "Feature" as const,
			properties: {},
			geometry: {
				type: "LineString" as const,
				coordinates: validPoints.map((point) => {
					const coords = getValidCoordinates(point);
					return [coords.lng, coords.lat];
				}),
			},
		};
	}, [points]);

	// 适配地图到所有点位
	const fitMapToPoints = useCallback(
		(map: mapboxgl.Map, pointsToFit: TravelPoint[]) => {
			if (!map || pointsToFit.length === 0) return;

			try {
				const bounds = new mapboxgl.LngLatBounds();
				pointsToFit.forEach((point) => {
					const coords = getValidCoordinates(point);
					bounds.extend([coords.lng, coords.lat]);
				});

				map.fitBounds(bounds, {
					padding: 100,
					duration: 1000,
					essential: true,
				});
			} catch (err) {
				console.error("适配地图视图失败:", err);
			}
		},
		[],
	);

	// 处理地图加载完成
	const handleMapLoad = useCallback(
		(evt: MapboxEvent<undefined> & { target: mapboxgl.Map }) => {
			try {
				const map = evt.target;
				setMapInstance(map);
				map.resize();
				setIsMapLoaded(true);
				setMapInitialized(true);

				// 添加3D建筑图层
				if (!map.getLayer("3d-buildings")) {
					map.addLayer({
						id: "3d-buildings",
						source: "composite",
						"source-layer": "building",
						filter: ["==", "extrude", "true"],
						type: "fill-extrusion",
						minzoom: 15,
						paint: {
							"fill-extrusion-color": "#aaa",
							"fill-extrusion-height": [
								"interpolate",
								["linear"],
								["zoom"],
								15,
								0,
								15.05,
								["get", "height"],
							],
							"fill-extrusion-base": [
								"interpolate",
								["linear"],
								["zoom"],
								15,
								0,
								15.05,
								["get", "min_height"],
							],
							"fill-extrusion-opacity": 0.6,
						},
					});
				}

				// 如果有点位，则适配地图视图
				if (points.length > 0) {
					fitMapToPoints(map, points);
				}
			} catch (err) {
				console.error("地图加载错误:", err);
			}
		},
		[points, fitMapToPoints],
	);

	// 处理地图加载错误
	const handleMapError = useCallback((evt: any) => {
		console.error("地图加载错误:", evt);
		setError("地图加载失败，请刷新页面重试");
	}, []);

	// 处理点位选择
	const handleMarkerClick = useCallback(
		(pointId: string) => {
			if (onPointSelect) {
				onPointSelect(pointId);
			}
		},
		[onPointSelect],
	);

	// 确保地图容器在组件挂载后存在
	useEffect(() => {
		if (!mapInitialized) {
			// 检查一下mapbox是否已经加载
			console.log(
				"Mapbox GL 状态:",
				typeof mapboxgl !== "undefined" ? "已加载" : "未加载",
			);
			console.log(
				"Map token:",
				`${MAPBOX_ACCESS_TOKEN.substring(0, 10)}...`,
			);
		}
	}, [mapInitialized]);

	// 当activePointId变化时，让地图聚焦到选中的点位
	useEffect(() => {
		if (!mapInstance || !isMapLoaded || !activePointId) return;

		const activePoint = points.find((point) => point.id === activePointId);
		if (!activePoint) return;

		try {
			const coords = getValidCoordinates(activePoint);
			mapInstance.flyTo({
				center: [coords.lng, coords.lat],
				zoom: 13,
				duration: 1000,
				essential: true,
			});
		} catch (err) {
			console.error("飞行到点位失败:", err);
		}
	}, [activePointId, mapInstance, isMapLoaded, points]);

	// 监听 mapResizeTrigger 变化以重置地图尺寸
	useEffect(() => {
		if (mapInstance && isMapLoaded && mapResizeTrigger) {
			// 添加一个小的延迟，以确保父容器的布局更新已完成
			const timer = setTimeout(() => {
				mapInstance.resize();
				console.log(
					"Map resized due to panel adjustment",
					mapResizeTrigger,
				);
			}, 50); // 50ms 延迟，可以根据需要调整
			return () => clearTimeout(timer);
		}
	}, [mapResizeTrigger, mapInstance, isMapLoaded]);

	// 监听points变化，重新适配地图视图
	useEffect(() => {
		if (mapInstance && isMapLoaded && points.length > 0) {
			// 延迟一帧执行，确保DOM已更新
			requestAnimationFrame(() => {
				try {
					fitMapToPoints(mapInstance, points);
					console.log(
						`[JourneyMap] 地图适配到新的点位集合，点位数量: ${points.length}`,
					);
				} catch (err) {
					console.error("重新适配地图视图失败:", err);
				}
			});
		} else if (mapInstance && isMapLoaded && points.length === 0) {
			// 如果没有点位，重置到默认视图
			mapInstance.flyTo({
				center: [DEFAULT_CENTER.lng, DEFAULT_CENTER.lat],
				zoom: DEFAULT_ZOOM,
				duration: 1000,
				essential: true,
			});
			console.log("[JourneyMap] 重置到默认视图 - 无点位");
		}
	}, [points, mapInstance, isMapLoaded, fitMapToPoints]);

	// 调试日志
	useEffect(() => {
		// 使用 useEffect 依赖为空数组，确保只在组件挂载时运行一次
		console.log("JourneyMap 初始化，点位数量:", points.length);

		// 检查无效点位的逻辑只在开发环境执行，减少性能影响
		if (process.env.NODE_ENV === "development") {
			const invalidPoints = points.filter(
				(p) =>
					!p?.coordinates ||
					typeof p?.coordinates?.lat !== "number" ||
					typeof p?.coordinates?.lng !== "number",
			);
			if (invalidPoints.length > 0) {
				console.warn(`发现 ${invalidPoints.length} 个无效坐标点`);
			}
		}
		// 注意：不要以 points 作为依赖，以避免频繁重新验证引起性能问题
	}, []); // 空依赖数组，只在挂载时执行一次

	// 加载错误
	if (error) {
		return (
			<div
				className={cn(
					"flex items-center justify-center bg-destructive/10 border border-destructive/50 text-destructive p-4 rounded-md h-full w-full",
					className,
				)}
			>
				{error}
			</div>
		);
	}

	return (
		<div className={cn("relative w-full h-full", className)}>
			{/* 加载状态 */}
			{!isMapLoaded && (
				<div className="absolute inset-0 z-10 flex flex-col items-center justify-center bg-muted rounded-md">
					<Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
					<span className="mt-2 text-sm text-muted-foreground">
						正在加载地图...
					</span>
				</div>
			)}

			{/* MapGL 组件 */}
			<MapGL
				mapboxAccessToken={MAPBOX_ACCESS_TOKEN}
				initialViewState={initialViewState}
				style={{ width: "100%", height: "100%" }}
				mapStyle={MAPBOX_STYLE}
				onLoad={handleMapLoad}
				onError={handleMapError}
				attributionControl={false}
				reuseMaps
			>
				{/* 地图控件 */}
				<GeolocateControl position="top-right" />
				<FullscreenControl position="top-right" />
				<NavigationControl position="top-right" />
				<ScaleControl />

				{/* 路线图层 */}
				{isMapLoaded && routeGeoJson && (
					<Source id="route" type="geojson" data={routeGeoJson}>
						{/* 背景路线 - 灰色 */}
						<Layer
							id="route-background"
							type="line"
							source="route"
							layout={{
								"line-join": "round",
								"line-cap": "round",
							}}
							paint={{
								"line-color": "#CBD5E1", // 浅灰色
								"line-width": 5,
								"line-opacity": 0.6,
							}}
						/>

						{/* 主路线 - 蓝色 */}
						<Layer
							id="route-main"
							type="line"
							source="route"
							layout={{
								"line-join": "round",
								"line-cap": "round",
							}}
							paint={{
								"line-color": "#3b82f6", // 蓝色
								"line-width": 3,
								"line-opacity": 0.8,
							}}
						/>
					</Source>
				)}

				{/* 点位标记 */}
				{isMapLoaded &&
					points.map((point, index) => (
						<CustomMarker
							key={point.id}
							point={point}
							index={index}
							isActive={point.id === activePointId}
							onClick={() => handleMarkerClick(point.id)}
						/>
					))}
			</MapGL>

			{/* 点位计数覆盖层 */}
			<div className="absolute top-4 left-4 bg-white/90 shadow-md p-3 rounded-lg z-10">
				<p className="text-sm font-medium">总地点数: {points.length}</p>
			</div>
		</div>
	);
};

export default JourneyMap;
