import { createId } from "@paralleldrive/cuid2";
import type {
	TravelDiaryContent,
	TravelPoint,
	TravelPointIconType,
	TravelTimeline,
} from "@repo/database/src/types/travel-diary";
import { logger } from "@repo/logs";
import type { GeocodingResult } from "./geocoding-service";
import type {
	ActivityInfo,
	LocationInfo,
	TravelInfo,
} from "./travel-ai-analyzer";

/**
 * 生成选项
 */
export interface GenerationOptions {
	/** 默认图标类型 */
	defaultIconType?: string;
	/** 时间线分组策略 */
	timelineGrouping?: "daily" | "activity" | "location";
	/** 是否合并相近的地点 */
	mergeNearbyLocations?: boolean;
	/** 合并距离阈值（公里） */
	mergeDistanceThreshold?: number;
}

/**
 * 地理编码匹配结果
 */
export interface LocationMatch {
	locationInfo: LocationInfo;
	geocodingResult: GeocodingResult | null;
	confidence: number;
}

/**
 * 旅行日记生成器
 */
export namespace TravelDiaryGenerator {
	/**
	 * 从旅行信息生成日记内容（简化接口）
	 */
	export function generateFromTravelInfo(
		travelInfo: TravelInfo,
		options: GenerationOptions = {},
	): TravelDiaryContent {
		try {
			logger.info("开始从旅行信息生成日记内容", {
				locationsCount: travelInfo.locations.length,
				timeInfoCount: travelInfo.timeInfo.length,
				activitiesCount: travelInfo.activities.length,
			});

			// 将LocationInfo转换为LocationMatch
			const locationMatches: LocationMatch[] = travelInfo.locations.map(
				(location) => ({
					locationInfo: location,
					geocodingResult: location.coordinates
						? {
								longitude: location.coordinates.longitude,
								latitude: location.coordinates.latitude,
								address: location.address || location.name,
								country: location.country,
								province: location.province,
								city: location.city,
								district: location.district,
								confidence: "high" as const,
								placeId: undefined,
							}
						: null,
					confidence: location.confidence,
				}),
			);

			// 1. 创建时间线
			const timelines = createTimelines(
				travelInfo,
				locationMatches,
				options,
			);

			// 2. 为每个时间线创建点位
			for (const timeline of timelines) {
				timeline.points = createPointsForTimeline(
					timeline,
					travelInfo,
					locationMatches,
					options,
				);
			}

			// 3. 排序和优化
			optimizeTimelines(timelines);

			const result: TravelDiaryContent = { timelines };

			logger.info("旅行日记内容生成完成", {
				timelinesCount: timelines.length,
				totalPointsCount: timelines.reduce(
					(sum, t) => sum + t.points.length,
					0,
				),
			});

			return result;
		} catch (error) {
			logger.error("生成旅行日记内容失败", {
				error: error instanceof Error ? error.message : String(error),
			});
			throw new Error(
				`生成旅行日记失败: ${error instanceof Error ? error.message : String(error)}`,
			);
		}
	}

	/**
	 * 生成旅行日记内容
	 */
	export async function generateDiaryContent(
		travelInfo: TravelInfo,
		locationMatches: LocationMatch[],
		options: GenerationOptions = {},
	): Promise<TravelDiaryContent> {
		try {
			logger.info("开始生成旅行日记内容", {
				locationsCount: travelInfo.locations.length,
				timeInfoCount: travelInfo.timeInfo.length,
				activitiesCount: travelInfo.activities.length,
				matchesCount: locationMatches.length,
			});

			// 1. 创建时间线
			const timelines = createTimelines(
				travelInfo,
				locationMatches,
				options,
			);

			// 2. 为每个时间线创建点位
			for (const timeline of timelines) {
				timeline.points = createPointsForTimeline(
					timeline,
					travelInfo,
					locationMatches,
					options,
				);
			}

			// 3. 排序和优化
			optimizeTimelines(timelines);

			const result: TravelDiaryContent = { timelines };

			logger.info("旅行日记内容生成完成", {
				timelinesCount: timelines.length,
				totalPointsCount: timelines.reduce(
					(sum, t) => sum + t.points.length,
					0,
				),
			});

			return result;
		} catch (error) {
			logger.error("生成旅行日记内容失败", {
				error: error instanceof Error ? error.message : String(error),
			});
			throw new Error(
				`生成旅行日记失败: ${error instanceof Error ? error.message : String(error)}`,
			);
		}
	}

	/**
	 * 创建时间线
	 */
	function createTimelines(
		travelInfo: TravelInfo,
		locationMatches: LocationMatch[],
		options: GenerationOptions,
	): TravelTimeline[] {
		const grouping = options.timelineGrouping || "daily";

		switch (grouping) {
			case "daily":
				return createDailyTimelines(travelInfo);
			case "activity":
				return createActivityTimelines(travelInfo);
			case "location":
				return createLocationTimelines(travelInfo, locationMatches);
			default:
				return createDailyTimelines(travelInfo);
		}
	}

	/**
	 * 创建按日期分组的时间线
	 */
	function createDailyTimelines(travelInfo: TravelInfo): TravelTimeline[] {
		const timelineMap = new Map<string, TravelTimeline>();

		// 从时间信息中提取日期
		for (const timeInfo of travelInfo.timeInfo) {
			if (timeInfo.inferredDate) {
				const date = new Date(timeInfo.inferredDate);
				const dateKey = date.toISOString().split("T")[0];

				if (!timelineMap.has(dateKey)) {
					timelineMap.set(dateKey, {
						id: createId(),
						title: formatTimelineTitle(date),
						date: dateKey,
						points: [],
					});
				}
			}
		}

		// 如果没有明确的时间信息，创建默认时间线
		if (timelineMap.size === 0) {
			const today = new Date();
			const dateKey = today.toISOString().split("T")[0];
			timelineMap.set(dateKey, {
				id: createId(),
				title: "旅行记录",
				date: dateKey,
				points: [],
			});
		}

		return Array.from(timelineMap.values()).sort((a, b) =>
			a.date.localeCompare(b.date),
		);
	}

	/**
	 * 创建按活动分组的时间线
	 */
	function createActivityTimelines(travelInfo: TravelInfo): TravelTimeline[] {
		const activityGroups = new Map<string, ActivityInfo[]>();

		// 按活动类型分组
		for (const activity of travelInfo.activities) {
			const key = activity.type;
			if (!activityGroups.has(key)) {
				activityGroups.set(key, []);
			}
			activityGroups.get(key)!.push(activity);
		}

		const timelines: TravelTimeline[] = [];
		let index = 1;

		for (const [activityType, activities] of Array.from(activityGroups)) {
			const timeline: TravelTimeline = {
				id: createId(),
				title: getActivityTypeTitle(activityType),
				date: new Date().toISOString().split("T")[0],
				points: [],
			};
			timelines.push(timeline);
			index++;
		}

		// 如果没有活动信息，创建默认时间线
		if (timelines.length === 0) {
			timelines.push({
				id: createId(),
				title: "旅行记录",
				date: new Date().toISOString().split("T")[0],
				points: [],
			});
		}

		return timelines;
	}

	/**
	 * 创建按地点分组的时间线
	 */
	function createLocationTimelines(
		travelInfo: TravelInfo,
		locationMatches: LocationMatch[],
	): TravelTimeline[] {
		const cityGroups = new Map<string, LocationMatch[]>();

		// 按城市分组
		for (const match of locationMatches) {
			const city =
				match.geocodingResult?.city ||
				match.geocodingResult?.address ||
				match.locationInfo.name;

			if (!cityGroups.has(city)) {
				cityGroups.set(city, []);
			}
			cityGroups.get(city)!.push(match);
		}

		const timelines: TravelTimeline[] = [];

		for (const [city, matches] of Array.from(cityGroups)) {
			const timeline: TravelTimeline = {
				id: createId(),
				title: `${city}之旅`,
				date: new Date().toISOString().split("T")[0],
				points: [],
			};
			timelines.push(timeline);
		}

		// 如果没有地点信息，创建默认时间线
		if (timelines.length === 0) {
			timelines.push({
				id: createId(),
				title: "旅行记录",
				date: new Date().toISOString().split("T")[0],
				points: [],
			});
		}

		return timelines;
	}

	/**
	 * 为时间线创建点位
	 */
	function createPointsForTimeline(
		timeline: TravelTimeline,
		travelInfo: TravelInfo,
		locationMatches: LocationMatch[],
		options: GenerationOptions,
	): TravelPoint[] {
		const points: TravelPoint[] = [];

		// 为每个地点创建点位（无论是否有地理坐标）
		for (const match of locationMatches) {
			const point = createTravelPoint(match, travelInfo, options);
			points.push(point);
		}

		// 如果没有地点信息，但有活动信息，为活动创建点位
		if (points.length === 0 && travelInfo.activities.length > 0) {
			for (const activity of travelInfo.activities) {
				const activityPoint = createActivityPoint(
					activity,
					timeline.date,
				);
				points.push(activityPoint);
			}
		}

		// 如果仍然没有点位，创建一个默认点位
		if (points.length === 0) {
			points.push(createDefaultPoint(timeline.date));
		}

		// 排序点位
		return sortPoints(points);
	}

	/**
	 * 创建旅行点位
	 */
	function createTravelPoint(
		match: LocationMatch,
		travelInfo: TravelInfo,
		options: GenerationOptions,
	): TravelPoint {
		const geocoding = match.geocodingResult;
		const locationInfo = match.locationInfo;

		// 查找与该地点直接相关的活动（更精确的匹配）
		const relatedActivities = travelInfo.activities.filter((activity) => {
			// 检查活动地点是否包含当前地点名称
			if (activity.location?.includes(locationInfo.name)) return true;

			// 检查活动描述是否提到当前地点
			if (activity.description.includes(locationInfo.name)) return true;

			// 对于特定地点类型，匹配相应的活动类型
			if (
				locationInfo.type === "restaurant" &&
				activity.type === "dining"
			)
				return true;
			if (
				locationInfo.type === "hotel" &&
				activity.type === "accommodation"
			)
				return true;
			if (
				locationInfo.type === "attraction" &&
				activity.type === "sightseeing"
			)
				return true;

			return false;
		});

		// 生成个性化描述
		const description = generatePersonalizedDescription(
			locationInfo,
			relatedActivities,
		);

		// 确定图标类型
		const iconType = determineIconType(
			locationInfo,
			relatedActivities,
			options,
		);

		// 查找相关时间
		const relatedTime = travelInfo.timeInfo.find((time) =>
			time.description.includes(locationInfo.name),
		);

		const pointDate = relatedTime?.inferredDate || new Date().toISOString();

		// 使用地理编码结果或默认坐标
		const latitude = geocoding?.latitude || 39.9042; // 默认北京坐标
		const longitude = geocoding?.longitude || 116.4074;
		const country = geocoding?.country || "中国";
		const city = geocoding?.city || "未知城市";
		const location =
			geocoding?.address || locationInfo.address || locationInfo.name;

		return {
			id: createId(),
			location,
			description,
			date: pointDate,
			images: [], // 第一阶段暂时为空，后续会添加图片处理
			iconType,
			latitude,
			longitude,
			order: 0, // 后续会重新排序
			country,
			city,
		};
	}

	/**
	 * 为活动创建点位
	 */
	function createActivityPoint(
		activity: ActivityInfo,
		timelineDate: string,
	): TravelPoint {
		return {
			id: createId(),
			location: activity.location || "活动地点",
			description: activity.description,
			date: `${timelineDate}T12:00:00.000Z`,
			images: [],
			iconType: determineIconTypeFromActivity(activity.type),
			latitude: 39.9042, // 默认坐标
			longitude: 116.4074,
			order: 0,
			country: "中国",
			city: "未知城市",
		};
	}

	/**
	 * 根据活动类型确定图标
	 */
	function determineIconTypeFromActivity(
		activityType: ActivityInfo["type"],
	): TravelPointIconType {
		switch (activityType) {
			case "dining":
				return "FOOD";
			case "accommodation":
				return "HOTEL";
			case "sightseeing":
				return "LANDMARK";
			case "shopping":
				return "SHOPPING";
			case "transport":
				return "TRANSPORT";
			default:
				return "PIN";
		}
	}

	/**
	 * 创建默认点位
	 */
	function createDefaultPoint(date: string): TravelPoint {
		return {
			id: createId(),
			location: "旅行地点",
			description: "请添加旅行描述",
			date: `${date}T12:00:00.000Z`,
			images: [],
			iconType: "PIN",
			latitude: 39.9042, // 北京坐标作为默认值
			longitude: 116.4074,
			order: 0,
			country: "中国",
			city: "北京",
		};
	}

	/**
	 * 生成个性化的点位描述
	 */
	function generatePersonalizedDescription(
		locationInfo: LocationInfo,
		activities: ActivityInfo[],
	): string {
		// 基础描述模板
		const locationTemplates = {
			city: [
				"探索了这座美丽的城市",
				"在这座城市留下了足迹",
				"感受了这里的城市魅力",
			],
			attraction: [
				"参观了这个著名景点",
				"欣赏了这里的美景",
				"体验了这个特色景点",
			],
			restaurant: [
				"在这里享用了美食",
				"品尝了当地特色菜",
				"体验了这里的美食文化",
			],
			hotel: [
				"在这里住宿休息",
				"选择了这里作为落脚点",
				"在这里度过了舒适的时光",
			],
			transport: [
				"经过了这个交通枢纽",
				"在这里中转",
				"利用这里的交通设施",
			],
			other: ["在这里留下了回忆", "体验了这个地方", "探索了这里的特色"],
		};

		// 活动描述模板
		const activityTemplates = {
			sightseeing: ["观光游览", "欣赏风景", "拍照留念", "感受文化"],
			dining: ["品尝美食", "享用大餐", "体验当地菜系", "美食探索"],
			shopping: ["购物休闲", "买纪念品", "逛街购物", "选购特产"],
			transport: ["交通出行", "乘坐交通工具", "路过此地", "中转休息"],
			accommodation: ["住宿休息", "入住酒店", "过夜停留", "休息整顿"],
			other: ["其他活动", "特色体验", "文化交流", "休闲娱乐"],
		};

		// 获取基础描述
		const templates =
			locationTemplates[locationInfo.type] || locationTemplates.other;
		const baseDescription =
			templates[Math.floor(Math.random() * templates.length)];

		// 如果有相关活动，添加活动描述
		if (activities.length > 0) {
			const activityDescriptions = activities.map((activity) => {
				const templates =
					activityTemplates[activity.type] || activityTemplates.other;
				return templates[Math.floor(Math.random() * templates.length)];
			});

			// 去重并组合
			const uniqueActivities = Array.from(new Set(activityDescriptions));
			if (uniqueActivities.length > 0) {
				return `${baseDescription}，${uniqueActivities.join("、")}`;
			}
		}

		// 如果有原始描述，优先使用
		if (locationInfo.description) {
			return locationInfo.description;
		}

		return baseDescription;
	}

	/**
	 * 确定图标类型
	 */
	function determineIconType(
		locationInfo: LocationInfo,
		activities: ActivityInfo[],
		options: GenerationOptions,
	): TravelPointIconType {
		// 根据地点类型确定图标
		switch (locationInfo.type) {
			case "restaurant":
				return "FOOD";
			case "hotel":
				return "HOTEL";
			case "attraction":
				return "LANDMARK";
			case "transport":
				return "TRANSPORT";
			default:
				break;
		}

		// 根据活动类型确定图标
		if (activities.length > 0) {
			const activityType = activities[0].type;
			switch (activityType) {
				case "dining":
					return "FOOD";
				case "accommodation":
					return "HOTEL";
				case "sightseeing":
					return "LANDMARK";
				case "shopping":
					return "SHOPPING";
				case "transport":
					return "TRANSPORT";
				default:
					break;
			}
		}

		// 确保返回有效的图标类型
		const defaultIcon = options.defaultIconType;
		if (
			defaultIcon &&
			[
				"PIN",
				"HOTEL",
				"FOOD",
				"LANDMARK",
				"PARK",
				"SHOPPING",
				"TRANSPORT",
				"OTHER",
			].includes(defaultIcon)
		) {
			return defaultIcon as TravelPointIconType;
		}

		return "PIN";
	}

	/**
	 * 排序点位
	 */
	function sortPoints(points: TravelPoint[]): TravelPoint[] {
		// 按时间排序
		const sorted = points.sort((a, b) => {
			const dateA = new Date(a.date);
			const dateB = new Date(b.date);
			return dateA.getTime() - dateB.getTime();
		});

		// 更新order字段
		sorted.forEach((point, index) => {
			point.order = index;
		});

		return sorted;
	}

	/**
	 * 优化时间线
	 */
	function optimizeTimelines(timelines: TravelTimeline[]): void {
		// 移除空的时间线
		const nonEmptyTimelines = timelines.filter((t) => t.points.length > 0);

		// 如果所有时间线都为空，保留第一个
		if (nonEmptyTimelines.length === 0 && timelines.length > 0) {
			timelines.splice(1);
		} else {
			timelines.splice(0, timelines.length, ...nonEmptyTimelines);
		}

		// 按日期排序时间线
		timelines.sort((a, b) => a.date.localeCompare(b.date));
	}

	/**
	 * 格式化时间线标题
	 */
	function formatTimelineTitle(date: Date): string {
		const options: Intl.DateTimeFormatOptions = {
			year: "numeric",
			month: "long",
			day: "numeric",
		};
		return date.toLocaleDateString("zh-CN", options);
	}

	/**
	 * 获取活动类型标题
	 */
	function getActivityTypeTitle(activityType: string): string {
		const titleMap: Record<string, string> = {
			sightseeing: "观光游览",
			dining: "美食体验",
			shopping: "购物休闲",
			transport: "交通出行",
			accommodation: "住宿休息",
			other: "其他活动",
		};
		return titleMap[activityType] || "旅行活动";
	}
}
