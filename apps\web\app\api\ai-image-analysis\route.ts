import {
	createGeminiClient,
	createOpenAIClient,
	createVolcengineClient,
} from "@packages/ai";
import { NextResponse } from "next/server";

/**
 * 处理图片分析请求的 API 路由
 */
export async function POST(req: Request) {
	try {
		const { imageUrl, question, provider, systemPrompt } = await req.json();

		if (!imageUrl) {
			return NextResponse.json({ error: "缺少图片URL" }, { status: 400 });
		}

		if (!question) {
			return NextResponse.json({ error: "缺少问题" }, { status: 400 });
		}

		let aiClient;
		let result;

		// 选择使用的AI提供商
		switch (provider) {
			case "openai":
				// 使用 OpenAI 客户端
				aiClient = createOpenAIClient(
					"gpt-4o",
					process.env.OPENAI_API_KEY,
				);
				break;

			case "gemini":
				// 使用 Google Gemini 客户端
				aiClient = createGeminiClient(process.env.GEMINI_API_KEY!);
				break;

			case "volcengine":
				// 使用火山引擎客户端
				aiClient = createVolcengineClient(
					process.env.VOLCENGINE_API_KEY!,
					process.env.VOLCENGINE_SECRET_KEY!,
				);
				break;

			default:
				// 默认使用 OpenAI
				aiClient = createOpenAIClient(
					"gpt-4o",
					process.env.OPENAI_API_KEY,
				);
		}

		// 使用图片分析功能
		const options: Record<string, any> = {};

		// 如果提供了系统提示词，添加到选项中
		if (systemPrompt) {
			options.systemPrompt = systemPrompt;
		}

		// 根据不同提供商设置特定选项
		if (provider === "openai") {
			options.visionModel = "gpt-4-vision-preview";
		} else if (provider === "volcengine") {
			options.model = "ep-20250317191902-5fh22";
		}

		// 调用图片分析方法
		result = await aiClient.analyzeImage(imageUrl, question, options);

		return NextResponse.json({ result });
	} catch (error: any) {
		console.error("AI图片分析处理错误:", error);
		return NextResponse.json(
			{ error: error.message || "处理请求时发生错误" },
			{ status: 500 },
		);
	}
}
