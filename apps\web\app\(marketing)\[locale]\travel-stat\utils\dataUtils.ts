import { normalizeMapboxCountryResult } from "../constants/countryNameMapping";
import type { GeocodeFeature } from "../types";

// 提取国家信息（使用增强的映射系统）
export function extractCountryInfo(feature: GeocodeFeature) {
	// 使用新的 Mapbox 结果处理函数
	const country = normalizeMapboxCountryResult({
		place_name: feature.place_name,
		context: feature.context,
	});

	// 提取国家代码
	let countryCode = "";
	const context = feature.context || [];
	for (const item of context) {
		if (item.id.startsWith("country")) {
			countryCode = item.short_code || "";
			break;
		}
	}

	// 调试信息
	console.log("🏷️ 提取的国家信息 (增强版):", {
		original: feature.place_name,
		country,
		countryCode,
		context: context.map((c) => ({
			id: c.id,
			text: c.text,
			short_code: c.short_code,
		})),
	});

	return { country, countryCode };
}

// 从地址中提取区域/省份信息
export function extractRegionInfo(placeName: string, context: any[] = []) {
	// 从context中提取区域信息
	let region = "";
	for (const item of context) {
		if (item.id.startsWith("region") || item.id.startsWith("province")) {
			region = item.text || "";
			break;
		}
	}

	// 如果context中没有找到，尝试从place_name中解析
	if (!region && placeName) {
		const parts = placeName.split(",").map((p) => p.trim());
		// 通常格式是：城市, 区域/省份, 国家
		if (parts.length >= 3) {
			region = parts[1];
		}
	}

	return region;
}

// 计算统计数据
export function calculateStats(travelPoints: any[], visitedCountries: any[]) {
	return {
		totalPoints: travelPoints.length,
		countriesCount: visitedCountries.length,
		citiesCount: new Set(travelPoints.map((p) => p.city)).size,
	};
}

// 三层目录结构的统计数据类型
export interface RegionData {
	name: string;
	visitCount: number;
	cities: CityData[];
	coordinates?: [number, number]; // 区域中心坐标
}

export interface CityData {
	name: string;
	visitCount: number;
	coordinates: [number, number]; // 城市坐标
	points: any[]; // 该城市的所有旅行点
}

export interface HierarchicalCountryData {
	name: string;
	code: string;
	visitCount: number;
	totalPoints: number;
	firstVisit: string;
	lastVisit: string;
	coordinates?: [number, number]; // 国家中心坐标
	regions: RegionData[];
	cities: CityData[]; // 没有区域信息的城市直接放在国家下
}

// 构建三层目录结构的统计数据
export function buildHierarchicalStats(
	travelPoints: any[],
): HierarchicalCountryData[] {
	const countryMap = new Map<string, HierarchicalCountryData>();

	// 遍历所有旅行点，构建层级结构
	for (const point of travelPoints) {
		const countryName = point.country;
		const cityName = point.city;

		// 从原始地址中提取区域信息（这里需要根据实际数据结构调整）
		const regionName = extractRegionFromPoint(point);

		// 确保国家存在
		if (!countryMap.has(countryName)) {
			countryMap.set(countryName, {
				name: countryName,
				code: "", // 可以从现有的visitedCountries中获取
				visitCount: 0,
				totalPoints: 0,
				firstVisit: point.timestamp
					? new Date(point.timestamp).toISOString()
					: new Date().toISOString(),
				lastVisit: point.timestamp
					? new Date(point.timestamp).toISOString()
					: new Date().toISOString(),
				coordinates: point.coordinates,
				regions: [],
				cities: [],
			});
		}

		const country = countryMap.get(countryName)!;
		country.visitCount += 1;
		country.totalPoints += 1;

		// 更新访问时间
		const pointDate = point.timestamp
			? new Date(point.timestamp).toISOString()
			: new Date().toISOString();
		if (pointDate < country.firstVisit) {
			country.firstVisit = pointDate;
		}
		if (pointDate > country.lastVisit) {
			country.lastVisit = pointDate;
		}

		// 如果有区域信息，添加到区域中
		if (regionName) {
			let region = country.regions.find((r) => r.name === regionName);
			if (!region) {
				region = {
					name: regionName,
					visitCount: 0,
					cities: [],
					coordinates: point.coordinates,
				};
				country.regions.push(region);
			}
			region.visitCount += 1;

			// 添加城市到区域中
			let city = region.cities.find((c) => c.name === cityName);
			if (!city) {
				city = {
					name: cityName,
					visitCount: 0,
					coordinates: point.coordinates,
					points: [],
				};
				region.cities.push(city);
			}
			city.visitCount += 1;
			city.points.push(point);
		} else {
			// 没有区域信息，直接添加到国家的城市列表中
			let city = country.cities.find((c) => c.name === cityName);
			if (!city) {
				city = {
					name: cityName,
					visitCount: 0,
					coordinates: point.coordinates,
					points: [],
				};
				country.cities.push(city);
			}
			city.visitCount += 1;
			city.points.push(point);
		}
	}

	// 转换为数组并排序
	const result = Array.from(countryMap.values());

	// 按访问次数排序
	result.sort((a, b) => b.visitCount - a.visitCount);

	// 对每个国家的区域和城市也进行排序
	result.forEach((country) => {
		country.regions.sort((a, b) => b.visitCount - a.visitCount);
		country.cities.sort((a, b) => b.visitCount - a.visitCount);

		country.regions.forEach((region) => {
			region.cities.sort((a, b) => b.visitCount - a.visitCount);
		});
	});

	return result;
}

// 从旅行点中提取区域信息的辅助函数
function extractRegionFromPoint(point: any): string {
	if (point.region) {
		return point.region;
	}

	// 尝试从名称中解析区域信息
	if (point.name && typeof point.name === "string") {
		const parts = point.name.split(",").map((p) => p.trim());

		// 通常格式是：具体地点, 城市, 区域/省份, 国家
		if (parts.length >= 3) {
			// 第三个部分可能是区域/省份
			const possibleRegion = parts[parts.length - 2]; // 倒数第二个

			// 简单的区域识别逻辑，可以根据需要扩展
			if (
				possibleRegion &&
				possibleRegion !== point.city &&
				possibleRegion !== point.country
			) {
				return possibleRegion;
			}
		}
	}

	return ""; // 没有找到区域信息
}

// 数据导出
export function exportTravelData(travelPoints: any[], visitedCountries: any[]) {
	const data = {
		travelPoints,
		visitedCountries,
		exportDate: new Date().toISOString(),
	};

	const blob = new Blob([JSON.stringify(data, null, 2)], {
		type: "application/json",
	});
	const url = URL.createObjectURL(blob);
	const a = document.createElement("a");
	a.href = url;
	a.download = `travel-footprints-${new Date().toISOString().split("T")[0]}.json`;
	a.click();
	URL.revokeObjectURL(url);
}

// 数据导入
export function importTravelData(
	onSuccess: (points: any[], countries: any[]) => void,
	onError: (error: string) => void,
) {
	const input = document.createElement("input");
	input.type = "file";
	input.accept = ".json";
	input.onchange = (e) => {
		const file = (e.target as HTMLInputElement).files?.[0];
		if (!file) return;

		const reader = new FileReader();
		reader.onload = (e) => {
			try {
				const data = JSON.parse(e.target?.result as string);
				if (data.travelPoints && Array.isArray(data.travelPoints)) {
					onSuccess(
						data.travelPoints || [],
						data.visitedCountries || [],
					);
				} else {
					onError("导入数据失败，请检查文件格式");
				}
			} catch (error) {
				onError("导入数据失败，请检查文件格式");
			}
		};
		reader.readAsText(file);
	};
	input.click();
}
