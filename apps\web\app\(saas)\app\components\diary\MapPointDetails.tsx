import { Button } from "@ui/components/button";
import {
	<PERSON>,
	Card<PERSON>ontent,
	CardDescription,
	<PERSON><PERSON><PERSON><PERSON>,
	Card<PERSON>eader,
	CardTitle,
} from "@ui/components/card";
import { format } from "date-fns";
import Image from "next/image";

interface TravelPoint {
	id: string;
	date: Date;
	location: string;
	description: string;
	images: string[];
	iconType: string;
	coordinates: { lat: number; lng: number };
}

interface MapPointDetailsProps {
	point: TravelPoint;
	onClose: () => void;
}

const MapPointDetails = ({ point, onClose }: MapPointDetailsProps) => {
	const getIconForType = (iconType: string): string => {
		switch (iconType) {
			case "hotel":
				return "🏨";
			case "food":
				return "🍽️";
			case "landmark":
				return "🗿";
			case "beach":
				return "🏖️";
			case "mountain":
				return "⛰️";
			case "museum":
				return "🏛️";
			case "park":
				return "🌳";
			default:
				return "📍";
		}
	};

	return (
		<Card className="w-full max-w-md">
			<CardHeader className="pb-2">
				<CardTitle className="flex items-center gap-2">
					<span>{getIconForType(point.iconType)}</span>
					{point.location}
				</CardTitle>
				<CardDescription>
					{format(new Date(point.date), "MMMM d, yyyy")}
				</CardDescription>
			</CardHeader>
			<CardContent className="pb-2">
				<p className="mb-4">{point.description}</p>
				{point.images && point.images.length > 0 && (
					<div className="grid grid-cols-2 gap-2">
						{point.images.slice(0, 4).map((image, index) => (
							<div
								key={index}
								className="relative aspect-square rounded-md overflow-hidden"
							>
								<Image
									src={image}
									alt={`${point.location} image ${index + 1}`}
									fill
									className="object-cover"
								/>
							</div>
						))}
						{point.images.length > 4 && (
							<div className="col-span-2 text-center text-sm text-muted-foreground mt-1">
								+{point.images.length - 4} more images
							</div>
						)}
					</div>
				)}
			</CardContent>
			<CardFooter>
				<Button variant="outline" size="sm" onClick={onClose}>
					Close
				</Button>
			</CardFooter>
		</Card>
	);
};

export default MapPointDetails;
