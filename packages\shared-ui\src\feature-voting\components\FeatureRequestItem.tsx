"use client";

import { useState } from "react";
import type { FeatureRequestItemProps } from "../types";
import { FeatureStatusBadge } from "./FeatureStatusBadge";
import { VoteButton } from "./VoteButton";

/**
 * 特性请求项组件
 */
export function FeatureRequestItem({
	featureRequest,
	showVoteCount = true,
	allowVoting = true,
	allowComments = true,
	onVote,
	onUnvote,
	onComment,
	className,
	ui,
}: FeatureRequestItemProps) {
	const [showComments, setShowComments] = useState(false);
	const {
		Button,
		Card,
		CardContent,
		CardHeader,
		MessageCircle,
		Calendar,
		cn,
	} = ui;

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString("zh-CN", {
			year: "numeric",
			month: "short",
			day: "numeric",
		});
	};

	const handleVote = async (featureRequestId: string) => {
		if (onVote) {
			await onVote(featureRequestId);
		}
	};

	const handleUnvote = async (featureRequestId: string) => {
		if (onUnvote) {
			await onUnvote(featureRequestId);
		}
	};

	const handleCommentClick = () => {
		setShowComments(!showComments);
	};

	return (
		<Card className={cn("w-full", className)}>
			<CardHeader className="pb-3">
				<div className="flex items-start justify-between gap-4">
					<div className="flex-1 min-w-0">
						<div className="flex items-center gap-2 mb-2">
							<h3 className="font-semibold text-lg leading-tight">
								{featureRequest.title}
							</h3>
							<FeatureStatusBadge
								status={featureRequest.status}
								ui={ui}
							/>
						</div>

						<p className="text-muted-foreground text-sm line-clamp-2 mb-3">
							{featureRequest.description}
						</p>

						<div className="flex items-center gap-4 text-xs text-muted-foreground">
							{featureRequest.product && (
								<span className="font-medium">
									{featureRequest.product.name}
								</span>
							)}

							{featureRequest.author && (
								<span>
									由 {featureRequest.author.name} 提交
								</span>
							)}

							<div className="flex items-center gap-1">
								<Calendar className="h-3 w-3" />
								<span>
									{formatDate(featureRequest.createdAt)}
								</span>
							</div>
						</div>
					</div>

					{allowVoting && onVote && onUnvote && (
						<VoteButton
							featureRequestId={featureRequest.id}
							hasVoted={featureRequest.hasVoted || false}
							voteCount={featureRequest.voteCount}
							showCount={showVoteCount}
							onVote={handleVote}
							onUnvote={handleUnvote}
							ui={ui}
						/>
					)}
				</div>
			</CardHeader>

			{allowComments && (
				<CardContent className="pt-0">
					<div className="flex items-center justify-between">
						<Button
							variant="ghost"
							size="sm"
							onClick={handleCommentClick}
							className="text-muted-foreground hover:text-foreground"
						>
							<MessageCircle className="h-4 w-4 mr-1" />
							<span>
								{featureRequest.commentCount || 0} 条评论
							</span>
						</Button>
					</div>

					{showComments && (
						<div className="mt-4 pt-4 border-t">
							<div className="text-sm text-muted-foreground">
								评论功能开发中...
							</div>
						</div>
					)}
				</CardContent>
			)}
		</Card>
	);
}
