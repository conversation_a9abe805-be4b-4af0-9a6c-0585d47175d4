"use client";

import { useAnalytics } from "@modules/analytics";
import { useCallback, useEffect, useState, useMemo } from "react";
import { COLOR_THEMES } from "../constants/colorThemes";
import type { ColorThemeType } from "../types/colorTypes";
import {
	getAvailableColorThemes,
	getColorThemesByCategory,
	getCurrentThemeInfo,
	getRecommendedColorThemes,
	updateStrategyTheme,
} from "../utils/colorUtils";

// 本地存储键
const STORAGE_KEY = "travel-footprint-color-theme";

export function useColorTheme() {
	const { trackEvent } = useAnalytics();

	// 从本地存储读取主题，默认使用经典蓝绿主题
	const [currentTheme, setCurrentTheme] = useState<ColorThemeType>(() => {
		if (typeof window !== "undefined") {
			const saved = localStorage.getItem(STORAGE_KEY);
			if (saved && saved in COLOR_THEMES) {
				return saved as ColorThemeType;
			}
		}
		return "classic-blue-green";
	});

	// 设置主题并保存到本地存储
	const changeTheme = useCallback(
		(themeId: ColorThemeType) => {
			const oldTheme = currentTheme;
			setCurrentTheme(themeId);
			updateStrategyTheme(themeId);

			// 追踪颜色主题切换事件
			trackEvent("travel_stat_color_theme_change", {
				from_theme: oldTheme,
				to_theme: themeId,
			});

			// 保存到本地存储
			if (typeof window !== "undefined") {
				localStorage.setItem(STORAGE_KEY, themeId);
			}
		},
		[currentTheme, trackEvent],
	);

	// 初始化时设置颜色主题
	useEffect(() => {
		updateStrategyTheme(currentTheme);
	}, [currentTheme]);

	// 获取当前主题数据
	const getCurrentThemeData = useCallback(() => {
		return getCurrentThemeInfo();
	}, []);

	// 获取所有可用主题
	const getAvailableThemes = useCallback(() => {
		return getAvailableColorThemes(COLOR_THEMES);
	}, []);

	// 根据类别获取主题
	const getThemesByCategory = useCallback(
		(category: "classic" | "modern" | "nature" | "vibrant" | "minimal") => {
			return getColorThemesByCategory(category, COLOR_THEMES);
		},
		[],
	);

	// 获取推荐主题
	const getRecommendedThemes = useCallback((mapStyle: string) => {
		return getRecommendedColorThemes(mapStyle, COLOR_THEMES);
	}, []);

	return useMemo(() => {
		return {
			currentTheme,
			changeTheme,
			getCurrentThemeData,
			getAvailableThemes,
			getThemesByCategory,
			getRecommendedThemes,
		};
	}, [
		currentTheme,
		changeTheme,
		getCurrentThemeData,
		getAvailableThemes,
		getThemesByCategory,
		getRecommendedThemes,
	]);
}

export type UseColorThemeReturn = ReturnType<typeof useColorTheme>;
