/**
 * Feature Voting 模块使用示例
 *
 * 这个文件展示了如何在实际项目中使用feature-voting组件
 * 需要在使用的项目中提供UI组件和图标
 */

import React from "react";
import {
	FeatureRequestList,
	FeatureVotingPage,
	SubmitFeatureForm,
	type UIComponents,
	useFeatureVoting,
} from "../index";

// 示例：如何在Next.js项目中使用
export function ExampleUsageInNextJS() {
	// 从项目的UI组件库中导入所需组件
	// 这些需要根据实际项目的组件库进行调整
	const ui: UIComponents = {
		// 基础组件 - 需要从项目的UI库导入
		Button: require("@ui/components/button").Button,
		Card: require("@ui/components/card").Card,
		CardContent: require("@ui/components/card").CardContent,
		CardHeader: require("@ui/components/card").CardHeader,
		CardTitle: require("@ui/components/card").CardTitle,
		Input: require("@ui/components/input").Input,
		Label: require("@ui/components/label").Label,
		Textarea: require("@ui/components/textarea").Textarea,
		Alert: require("@ui/components/alert").Alert,
		AlertDescription: require("@ui/components/alert").AlertDescription,

		// Select组件
		Select: require("@ui/components/select").Select,
		SelectContent: require("@ui/components/select").SelectContent,
		SelectItem: require("@ui/components/select").SelectItem,
		SelectTrigger: require("@ui/components/select").SelectTrigger,
		SelectValue: require("@ui/components/select").SelectValue,

		// Tabs组件
		Tabs: require("@ui/components/tabs").Tabs,
		TabsContent: require("@ui/components/tabs").TabsContent,
		TabsList: require("@ui/components/tabs").TabsList,
		TabsTrigger: require("@ui/components/tabs").TabsTrigger,

		// 图标 - 需要从lucide-react导入
		ChevronUp: require("lucide-react").ChevronUp,
		MessageCircle: require("lucide-react").MessageCircle,
		Calendar: require("lucide-react").Calendar,
		AlertCircle: require("lucide-react").AlertCircle,
		Loader2: require("lucide-react").Loader2,
		RefreshCw: require("lucide-react").RefreshCw,
		Send: require("lucide-react").Send,
		Lightbulb: require("lucide-react").Lightbulb,
		TrendingUp: require("lucide-react").TrendingUp,
		Plus: require("lucide-react").Plus,

		// 工具函数
		cn: require("@ui/lib").cn,
	};

	return (
		<div className="container mx-auto py-8">
			<FeatureVotingPage
				defaultProductId="travel-memo"
				showSubmitForm={true}
				allowVoting={true}
				allowComments={true}
				ui={ui}
			/>
		</div>
	);
}

// 示例：只使用特性列表组件
export function ExampleFeatureListOnly() {
	const ui: UIComponents = {
		// ... 同上面的UI组件配置
	} as UIComponents;

	return (
		<div className="max-w-4xl mx-auto">
			<h2 className="text-2xl font-bold mb-6">用户反馈</h2>
			<FeatureRequestList
				productId="travel-memo"
				showVoteCounts={true}
				allowVoting={true}
				allowComments={false}
				ui={ui}
			/>
		</div>
	);
}

// 示例：只使用提交表单
export function ExampleSubmitFormOnly() {
	const ui: UIComponents = {
		// ... 同上面的UI组件配置
	} as UIComponents;

	const { products, submitFeature } = useFeatureVoting();

	const handleSubmit = async (data: any) => {
		try {
			await submitFeature(data);
			alert("提交成功！");
		} catch (error) {
			alert("提交失败，请重试");
		}
	};

	return (
		<div className="max-w-2xl mx-auto">
			<h2 className="text-2xl font-bold mb-6">提交新想法</h2>
			<SubmitFeatureForm
				products={products}
				onSubmit={handleSubmit}
				ui={ui}
			/>
		</div>
	);
}

// 示例：自定义样式
export function ExampleWithCustomStyles() {
	const ui: UIComponents = {
		// ... UI组件配置
	} as UIComponents;

	return (
		<div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
			<div className="container mx-auto py-12">
				<FeatureVotingPage
					className="bg-white rounded-xl shadow-lg p-8"
					defaultProductId="travel-memo"
					ui={ui}
				/>
			</div>
		</div>
	);
}

// 示例：在不同产品中使用
export function ExampleMultiProduct() {
	const ui: UIComponents = {
		// ... UI组件配置
	} as UIComponents;

	const [selectedProduct, setSelectedProduct] = React.useState("travel-memo");

	const products = [
		{ id: "travel-memo", name: "旅行足迹" },
		{ id: "ai-generator", name: "AI图片生成器" },
		{ id: "other-app", name: "其他应用" },
	];

	return (
		<div className="container mx-auto py-8">
			<div className="mb-6">
				<h1 className="text-3xl font-bold mb-4">产品反馈中心</h1>
				<div className="flex gap-2">
					{products.map((product) => (
						<button
							key={product.id}
							type="button"
							onClick={() => setSelectedProduct(product.id)}
							className={`px-4 py-2 rounded-lg ${
								selectedProduct === product.id
									? "bg-blue-500 text-white"
									: "bg-gray-200 text-gray-700"
							}`}
						>
							{product.name}
						</button>
					))}
				</div>
			</div>

			<FeatureRequestList
				productId={selectedProduct}
				showVoteCounts={true}
				allowVoting={true}
				allowComments={true}
				ui={ui}
			/>
		</div>
	);
}
