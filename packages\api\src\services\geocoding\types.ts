/**
 * 地理编码服务统一类型定义
 */

/**
 * 地理编码提供商类型
 */
export type GeocodingProvider = "google" | "bing" | "mapbox" | "baidu" | "amap";

/**
 * 地理编码配置
 */
export interface GeocodingConfig {
	/** 提供商类型 */
	provider: GeocodingProvider;
	/** API密钥 */
	apiKey: string;
	/** 权重（用于负载均衡） */
	weight?: number;
	/** 是否启用 */
	enabled?: boolean;
	/** 每日配额限制 */
	dailyQuota?: number;
	/** 当前已使用配额 */
	usedQuota?: number;
	/** 优先级（数字越小优先级越高） */
	priority?: number;
}

/**
 * 统一的地理编码结果
 */
export interface UnifiedGeocodingResult {
	/** 经度 */
	longitude: number;
	/** 纬度 */
	latitude: number;
	/** 格式化地址 */
	formattedAddress: string;
	/** 地址组件 */
	addressComponents: {
		country?: string;
		countryCode?: string;
		province?: string;
		city?: string;
		district?: string;
		street?: string;
		streetNumber?: string;
		postalCode?: string;
	};
	/** 置信度 */
	confidence: "high" | "medium" | "low";
	/** 地点类型 */
	placeTypes?: string[];
	/** 边界框 */
	viewport?: {
		northeast: { lat: number; lng: number };
		southwest: { lat: number; lng: number };
	};
	/** 提供商特定的ID */
	placeId?: string;
	/** 使用的提供商 */
	provider: GeocodingProvider;
	/** 响应时间（毫秒） */
	responseTime: number;
}

/**
 * 地理编码选项
 */
export interface GeocodingOptions {
	/** 限制结果数量 */
	limit?: number;
	/** 国家代码限制 */
	country?: string;
	/** 城市限制（用于百度地图等提供商） */
	city?: string;
	/** 语言偏好 */
	language?: string;
	/** 地区偏好 */
	region?: string;
	/** 地点类型过滤 */
	types?: string[];
	/** 是否启用缓存 */
	enableCache?: boolean;
	/** 缓存TTL（秒） */
	cacheTTL?: number;
	/** 超时时间（毫秒） */
	timeout?: number;
	/** 重试次数 */
	retries?: number;
}

/**
 * 批量地理编码结果
 */
export interface BatchGeocodingResult {
	/** 输入地址 */
	address: string;
	/** 地理编码结果 */
	result: UnifiedGeocodingResult | null;
	/** 错误信息 */
	error?: string;
	/** 使用的提供商 */
	provider?: GeocodingProvider;
}

/**
 * 地理编码统计信息
 */
export interface GeocodingStats {
	/** 总请求数 */
	totalRequests: number;
	/** 成功请求数 */
	successfulRequests: number;
	/** 失败请求数 */
	failedRequests: number;
	/** 平均响应时间 */
	averageResponseTime: number;
	/** 各提供商使用统计 */
	providerStats: Record<
		GeocodingProvider,
		{
			requests: number;
			successes: number;
			failures: number;
			averageResponseTime: number;
			lastUsed: Date;
		}
	>;
}

/**
 * 地理编码错误类型
 */
export class GeocodingError extends Error {
	constructor(
		message: string,
		public provider: GeocodingProvider,
		public code?: string,
		public originalError?: Error,
	) {
		super(message);
		this.name = "GeocodingError";
	}
}

/**
 * 提供商特定的原始响应接口
 */
export interface ProviderResponse {
	provider: GeocodingProvider;
	rawData: any;
	success: boolean;
	error?: string;
	responseTime: number;
}
