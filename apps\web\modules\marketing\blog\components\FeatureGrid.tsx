import { cn } from "@ui/lib";
import Image from "next/image";

interface Feature {
	icon: string;
	title: string;
	description: string;
	image?: string;
	imageAlt?: string;
}

interface FeatureGridProps {
	features: Feature[];
	columns?: 2 | 3 | 4;
	className?: string;
}

export function FeatureGrid({
	features,
	columns = 3,
	className,
}: FeatureGridProps) {
	const getGridCols = () => {
		switch (columns) {
			case 2:
				return "md:grid-cols-2";
			case 3:
				return "md:grid-cols-3";
			case 4:
				return "md:grid-cols-2 lg:grid-cols-4";
			default:
				return "md:grid-cols-3";
		}
	};

	return (
		<div className={cn("mb-8", className)}>
			<div className={cn("grid gap-6 grid-cols-1", getGridCols())}>
				{features.map((feature, index) => (
					<div
						key={index}
						className="group relative"
						style={{
							animationDelay: `${index * 150}ms`,
						}}
					>
						{/* 背景装饰 */}
						<div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 to-purple-50/30 dark:from-blue-900/10 dark:to-purple-900/10 rounded-2xl rotate-1 group-hover:rotate-0 transition-transform duration-300" />

						{/* 主卡片 */}
						<div className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-white/50 dark:border-gray-700/50 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-[1.02]">
							{/* 图标 */}
							<div className="mb-4">
								<div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center text-2xl shadow-lg group-hover:scale-110 transition-transform duration-300">
									{feature.icon}
								</div>
							</div>

							{/* 标题 */}
							<h3 className="text-lg font-bold text-gray-800 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">
								{feature.title}
							</h3>

							{/* 图片 */}
							{feature.image && (
								<div className="relative mb-4 overflow-hidden rounded-lg bg-gradient-to-br from-blue-50/30 to-purple-50/30 dark:from-blue-900/20 dark:to-purple-900/20 group-hover:scale-[1.02] transition-transform duration-300">
									<Image
										src={feature.image}
										alt={feature.imageAlt || feature.title}
										width={600}
										height={400}
										className="w-full h-auto transition-transform duration-300 group-hover:scale-105"
										sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
									/>
								</div>
							)}

							{/* 描述 */}
							<p className="text-gray-600 dark:text-gray-300 leading-relaxed text-sm">
								{feature.description}
							</p>

							{/* 悬浮效果指示器 */}
							<div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
								<div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400 animate-pulse" />
							</div>
						</div>
					</div>
				))}
			</div>
		</div>
	);
}
