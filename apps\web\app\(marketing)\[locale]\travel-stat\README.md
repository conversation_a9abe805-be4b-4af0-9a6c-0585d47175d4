# Travel Stat 页面目录结构

## 📁 目录结构

```
travel-stat/
├── README.md                    # 项目说明文档
├── page.tsx                     # Next.js 页面入口
├── index.ts                     # 统一导出文件
├── TravelFootprintTool.tsx      # 主要工具组件
│
├── components/                  # 组件目录
│   ├── index.ts                # 组件统一导出
│   │
│   ├── animation/              # 🎨 动画组件模块
│   │   ├── index.ts
│   │   ├── BackgroundAnimations.tsx
│   │   ├── AnimationThemeSwitcher.tsx
│   │   ├── AnimationController.tsx
│   │   ├── ThreeJsBackground.tsx
│   │   ├── TSParticlesBackground.tsx
│   │   └── TSParticlesBackgroundSimple.tsx
│   │
│   ├── atmosphere/             # 🌍 大气层组件模块
│   │   ├── index.ts
│   │   ├── AtmosphereSwitcher.tsx
│   │   └── AtmosphereDemo.tsx
│   │
│   ├── map-style/              # 🗺️ 地图样式组件模块
│   │   ├── index.ts
│   │   ├── MapStyleSwitcher.tsx
│   │   ├── MapStyleDemo.tsx
│   │   └── ProjectionSwitcher.tsx
│   │
│   ├── map/                    # 🗺️ 地图核心组件模块
│   │   ├── index.ts
│   │   ├── MapContainer.tsx
│   │   ├── MapLegend.tsx
│   │   └── SearchBox.tsx
│   │
│   ├── map-controls/           # 🎛️ 地图控制组件模块
│   │   ├── index.ts
│   │   ├── components/
│   │   ├── constants/
│   │   ├── hooks/
│   │   └── utils/
│   │
│   ├── markers/                # 📍 标记组件模块
│   │   ├── index.ts
│   │   └── MarkerFactory.tsx
│   │
│   ├── stats/                  # 📊 统计组件模块
│   │   ├── index.ts
│   │   ├── TravelStats.tsx
│   │   └── CountryList.tsx
│   │
│   ├── export/                 # 📤 导出组件模块
│   │   ├── index.ts
│   │   ├── MapExporter.tsx
│   │   ├── EnhancedMapExporter.tsx
│   │   └── EXPORT_SOLUTIONS.md
│   │
│   ├── card-generator/         # 🎴 卡片生成器模块
│   │   ├── index.ts
│   │   ├── components/
│   │   ├── templates/
│   │   ├── types/
│   │   └── utils/
│   │
│   ├── ui/                     # 🎨 UI 组件模块
│   │   ├── index.ts
│   │   ├── MarkerStyleSelector.tsx
│   │   └── OperationPanel.tsx
│   │
│   └── layout/                 # 📐 布局组件模块
│       ├── index.ts
│       └── MemoriesGallery.tsx
│
├── hooks/                      # 🪝 自定义 Hook
│   ├── useMapControl.ts
│   ├── useSearch.ts
│   ├── useTravelData.ts
│   ├── useColorTheme.ts
│   └── useMarkerStyle.ts
│
├── utils/                      # 🛠️ 工具函数
│   ├── colorUtils.ts
│   ├── dataUtils.ts
│   └── mapUtils.ts
│
├── types/                      # 📝 类型定义
│   ├── index.ts
│   ├── colorTypes.ts
│   ├── markerTypes.ts
│   └── mapTypes.ts
│
├── constants/                  # 📋 常量配置
│   └── mapConfig.ts
│
└── docs/                       # 📚 文档目录
    └── ANIMATION_SYSTEM.md
```

## 🎯 设计原则

### 1. 模块化设计
- **按功能分组**: 相关功能的组件放在同一个模块下
- **单一职责**: 每个模块只负责一个特定的功能领域
- **清晰边界**: 模块间通过明确的接口进行交互

### 2. 层次化结构
- **组件层**: 可复用的 UI 组件
- **业务层**: 特定业务逻辑的组件
- **工具层**: 通用工具函数和 Hook
- **类型层**: TypeScript 类型定义

### 3. 统一导出
- 每个模块都有自己的 `index.ts` 文件
- 根目录的 `index.ts` 统一管理所有导出
- 支持按需导入和批量导入

## 📦 模块说明

### 🎨 Animation 模块
负责所有动画效果，包括背景动画、粒子效果、3D 动画等。

**主要组件:**
- `BackgroundAnimations`: 背景动画控制器
- `AnimationThemeSwitcher`: 动画主题切换器
- `ThreeJsBackground`: 3D 背景动画
- `TSParticlesBackground`: 2D 粒子动画

### 🌍 Atmosphere 模块
处理地图大气层效果和相关控制。

**主要组件:**
- `AtmosphereSwitcher`: 大气层主题切换器
- `AtmosphereDemo`: 大气层效果演示

### 🗺️ Map-Style 模块
管理地图样式、投影方式等视觉效果。

**主要组件:**
- `MapStyleSwitcher`: 地图样式切换器
- `ProjectionSwitcher`: 地图投影切换器
- `MapStyleDemo`: 地图样式演示

### 🗺️ Map 模块
地图的核心功能组件。

**主要组件:**
- `MapContainer`: 地图容器组件
- `MapLegend`: 地图图例
- `SearchBox`: 地点搜索框

### 📍 Markers 模块
处理地图标记的显示和交互。

**主要组件:**
- `MarkerFactory`: 标记工厂，根据不同样式创建标记

### 📊 Stats 模块
统计数据的展示和处理。

**主要组件:**
- `TravelStats`: 旅行统计信息
- `CountryList`: 访问过的国家列表

### 📤 Export 模块
地图和数据的导出功能。

**主要组件:**
- `MapExporter`: 基础地图导出器
- `EnhancedMapExporter`: 增强版地图导出器

## 🚀 使用方法

### 导入单个组件
```typescript
import { MapContainer } from './components/map/MapContainer';
import { TravelStats } from './components/stats/TravelStats';
```

### 导入整个模块
```typescript
import * as MapComponents from './components/map';
import * as AnimationComponents from './components/animation';
```

### 从根目录导入
```typescript
import { 
  TravelFootprintTool,
  MapContainer,
  TravelStats,
  BackgroundAnimations 
} from './travel-stat';
```

## 🔧 维护指南

### 添加新组件
1. 确定组件所属的功能模块
2. 在对应模块目录下创建组件文件
3. 更新模块的 `index.ts` 导出文件
4. 如需要，更新根目录的 `index.ts`

### 重构现有组件
1. 保持组件的公共接口不变
2. 更新相关的类型定义
3. 确保所有导入路径正确
4. 运行测试确保功能正常

### 文件大小控制
- 单个组件文件不超过 500 行
- 如果超过，考虑拆分成多个子组件
- 提取复杂逻辑到自定义 Hook
- 分离工具函数到 utils 目录

## 📈 性能优化

### 代码分割
- 使用动态导入 (`React.lazy`) 加载大型组件
- 按模块进行代码分割
- 避免不必要的依赖导入

### 组件优化
- 使用 `React.memo` 优化纯组件
- 合理使用 `useMemo` 和 `useCallback`
- 避免在渲染函数中创建对象和函数

## 🧪 测试策略

### 单元测试
- 每个组件都应有对应的测试文件
- 测试文件放在组件同级目录
- 使用 Jest + React Testing Library

### 集成测试
- 测试模块间的交互
- 验证数据流的正确性
- 确保导入导出的一致性

---

通过这种模块化的组织方式，代码更加清晰、可维护，同时也便于团队协作和功能扩展。 