"use client";

import { useState } from "react";
import { ClosingSection } from "./closing-section";
import { ErrorState } from "./error-state";
import { Loading } from "./loading";
import { MapPresentation } from "./map-presentation";
import { OpeningSection } from "./opening-section";
import { calculateActiveTimelineIndex } from "./timeline-utils";
import { useDiaryData } from "./use-diary-data";

export function TravelDiaryPresentation() {
	// 使用自定义钩子加载旅行日记数据
	const { diary, isLoading, error } = useDiaryData();

	// 视图状态
	const [showOpening, setShowOpening] = useState(true);
	const [showClosing, setShowClosing] = useState(false);
	const [currentFootprintIndex, setCurrentFootprintIndex] = useState(0);

	// 根据当前足迹索引计算活动的时间线索引
	const activeTimelineIndex = diary
		? calculateActiveTimelineIndex(currentFootprintIndex, diary)
		: 0;

	// 开始探索，隐藏开场页并显示主内容
	const startExploring = () => {
		setShowOpening(false);
	};

	// 显示结束页面
	const showEndingPage = () => {
		setShowClosing(true);
	};

	// 返回首页
	const returnToOpening = () => {
		setShowOpening(true);
		setShowClosing(false);
		setCurrentFootprintIndex(0);
	};

	// 加载状态
	if (isLoading) {
		return <Loading />;
	}

	// 错误状态
	if (error || !diary) {
		return <ErrorState message={error?.message} />;
	}

	return (
		<div className="relative w-full h-screen overflow-hidden bg-neutral-950 text-white">
			{/* 开场部分 */}
			{showOpening && (
				<OpeningSection
					diary={diary}
					onStartExploring={startExploring}
				/>
			)}{" "}
			{/* 主内容区域 - 使用地图组件 */}
			{!showOpening && !showClosing && (
				<MapPresentation diary={diary} onFinish={showEndingPage} />
			)}
			{/* 结束部分 */}
			{showClosing && (
				<ClosingSection
					diary={diary}
					onReturnToOpening={returnToOpening}
				/>
			)}
		</div>
	);
}
