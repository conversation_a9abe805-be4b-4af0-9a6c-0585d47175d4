"use client";

import { useCallback } from "react";
import type { MapStoryContext, MapStoryTimers } from "./types";
import { createLogger } from "./utils";

// 创建计时器模块专用的日志记录器
const logger = createLogger("MapStoryTimers");

export function useMapStoryTimers(context: MapStoryContext): MapStoryTimers {
	const { refs } = context;

	// 计时器清理函数
	const clearPlayTimer = useCallback(() => {
		if (refs.currentPointDisplayTimeoutRef.current) {
			logger.debug("清除点位展示计时器");
			clearTimeout(refs.currentPointDisplayTimeoutRef.current);
			refs.currentPointDisplayTimeoutRef.current = null;
		}
	}, [refs.currentPointDisplayTimeoutRef]);

	const clearBlinkTimer = useCallback(() => {
		if (refs.blinkTimerRef.current) {
			logger.debug("清除点位闪烁计时器");
			clearInterval(refs.blinkTimerRef.current);
			refs.blinkTimerRef.current = null;
		}
	}, [refs.blinkTimerRef]);

	return {
		clearPlayTimer,
		clearBlinkTimer,
	};
}
