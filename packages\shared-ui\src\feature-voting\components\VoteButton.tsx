"use client";

import { useState } from "react";
import type { VoteButtonProps } from "../types";

/**
 * 投票按钮组件
 */
export function VoteButton({
	featureRequestId,
	hasVoted,
	voteCount = 0,
	showCount = true,
	onVote,
	onUnvote,
	isLoading = false,
	className,
	ui,
}: VoteButtonProps) {
	const [isVoting, setIsVoting] = useState(false);
	const { Button, ChevronUp, cn } = ui;

	const handleClick = async () => {
		if (isVoting || isLoading) return;

		try {
			setIsVoting(true);
			if (hasVoted) {
				await onUnvote(featureRequestId);
			} else {
				await onVote(featureRequestId);
			}
		} catch (error) {
			console.error("投票操作失败:", error);
		} finally {
			setIsVoting(false);
		}
	};

	return (
		<Button
			variant={hasVoted ? "primary" : "outline"}
			size="sm"
			onClick={handleClick}
			disabled={isVoting || isLoading}
			className={cn(
				"flex flex-col items-center gap-1 h-auto py-2 px-3 min-w-[60px]",
				"transition-all duration-200",
				hasVoted && "bg-primary text-primary-foreground",
				className,
			)}
		>
			<ChevronUp
				className={cn(
					"h-4 w-4 transition-transform duration-200",
					hasVoted && "scale-110",
				)}
			/>
			{showCount && (
				<span className="text-xs font-medium">{voteCount}</span>
			)}
		</Button>
	);
}
