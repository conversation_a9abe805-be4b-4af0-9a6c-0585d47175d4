/**
 * 视频导出API工具
 * 提供包装函数自动将视频导出模式参数添加到API请求中
 */

// 导出令牌 - 必须与中间件中的令牌一致
export const EXPORT_AUTH_TOKEN = "j8Kp4Xz2Q9vYfTs7B3e5W6gA1hR0dNmVlCuMoIpLxZyE";

/**
 * 检查当前URL是否处于视频导出模式
 */
export function isVideoExportMode(): boolean {
	if (typeof window === "undefined") return false;

	const urlParams = new URLSearchParams(window.location.search);
	return urlParams.get("export") === "video";
}

/**
 * 获取视频导出令牌（如果存在）
 */
export function getExportToken(): string | null {
	if (typeof window === "undefined") return null;

	const urlParams = new URLSearchParams(window.location.search);
	return urlParams.get("exportToken");
}

/**
 * 向URL添加视频导出参数（如果处于导出模式）
 */
export function addExportParamsToUrl(url: string): string {
	if (!isVideoExportMode()) return url;

	const exportToken = getExportToken();
	if (!exportToken) return url;

	// 检查URL是否已有查询参数
	const hasParams = url.includes("?");
	const separator = hasParams ? "&" : "?";

	return `${url}${separator}export=video&exportToken=${exportToken}`;
}

/**
 * 包装fetch API，自动添加视频导出参数
 * 与原生fetch API保持相同的签名和行为
 */
export async function fetchWithExport(
	url: string,
	options?: RequestInit,
): Promise<Response> {
	const urlWithExport = addExportParamsToUrl(url);
	return fetch(urlWithExport, options);
}
