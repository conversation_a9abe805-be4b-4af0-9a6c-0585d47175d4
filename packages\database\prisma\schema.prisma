datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  // directUrl = env("DIRECT_URL")
}

generator client {
  provider = "prisma-client-js"
}

generator zod {
  provider         = "zod-prisma-types"
  output           = "../src/zod"
  createInputTypes = false
  addIncludeType   = false
  addSelectType    = false
}

model User {
  id                 String       @id
  name               String
  email              String
  emailVerified      Boolean
  image              String?
  createdAt          DateTime
  updatedAt          DateTime
  username           String?
  role               String?
  banned             Boolean?
  banReason          String?
  banExpires         DateTime?
  onboardingComplete Boolean      @default(false)
  paymentsCustomerId String?
  locale             String?
  sessions           Session[]
  accounts           Account[]
  passkeys           Passkey[]
  invitations        Invitation[]
  purchases          Purchase[]
  memberships        Member[]
  aiChats            AiChat[]
  travelDiaries      TravelDiary[]
  
  // Feature Voting 模块关联
  featureRequests      FeatureRequest[]
  featureVotes         FeatureVote[]
  featureComments      FeatureComment[]
  featureSubscriptions FeatureSubscription[]

  @@unique([email])
  @@unique([username])
  @@map("user")
}

model Session {
  id        String   @id
  expiresAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  impersonatedBy String?

  activeOrganizationId String?

  token     String
  createdAt DateTime
  updatedAt DateTime

  @@unique([token])
  @@map("session")
}

model Account {
  id           String    @id
  accountId    String
  providerId   String
  userId       String
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken  String?
  refreshToken String?
  idToken      String?
  expiresAt    DateTime?
  password     String?

  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String   @id
  identifier String
  value      String
  expiresAt  DateTime

  createdAt DateTime?
  updatedAt DateTime?

  @@map("verification")
}

model Passkey {
  id           String    @id
  name         String?
  publicKey    String
  userId       String
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  credentialID String
  counter      Int
  deviceType   String
  backedUp     Boolean
  transports   String?
  createdAt    DateTime?

  @@map("passkey")
}

model Organization {
  id                 String       @id
  name               String
  slug               String?
  logo               String?
  createdAt          DateTime
  metadata           String?
  paymentsCustomerId String?
  members            Member[]
  invitations        Invitation[]
  purchases          Purchase[]
  aiChats            AiChat[]

  @@unique([slug])
  @@map("organization")
}

model Member {
  id             String       @id
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userId         String
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  role           String
  createdAt      DateTime

  @@unique([userId, organizationId])
  @@map("member")
}

model Invitation {
  id             String       @id
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  email          String
  role           String?
  status         String
  expiresAt      DateTime
  inviterId      String
  user           User         @relation(fields: [inviterId], references: [id], onDelete: Cascade)

  @@map("invitation")
}

enum PurchaseType {
  SUBSCRIPTION
  ONE_TIME
}

model Purchase {
  id             String        @id @default(cuid())
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String?
  user           User?         @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId         String?
  type           PurchaseType
  customerId     String
  subscriptionId String?       @unique
  productId      String
  status         String?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt

  @@index([subscriptionId])
}

model AiChat {
  id             String        @id @default(cuid())
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userId         String?
  user           User?         @relation(fields: [userId], references: [id], onDelete: Cascade)
  title          String?
  messages       Json[]
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
}

model TravelDiary {
  id          String    @id @default(cuid())
  title       String
  subtitle    String?
  coverImage  String?
  content     Json      // 存储时间线和点位的 JSON 数据
  richTextDraftContent Json? // 新增：存储富文本编辑器的草稿内容
  isPublic    Boolean   @default(false)
  userId      String
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  status      Int       @default(0) // 逻辑删除状态：0=正常, 1=已删除
  
  @@map("travel_diary")
}

// 视频导出任务模型
model VideoExportTask {
  id             String    @id @default(cuid())
  diaryId        String    // 关联的日记ID
  userId         String    // 请求导出的用户ID
  status         String    // 任务状态: pending, processing, completed, failed
  options        Json      // 导出选项（分辨率、帧率等）
  videoUrl       String?   // 生成的视频URL（完成后）
  errorMessage   String?   // 如果失败，错误信息
  progress       Int       @default(0) // 进度百分比
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  // 关联的日记（如果有关联关系）
  // diary          TravelDiary @relation(fields: [diaryId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([diaryId])
  @@map("video_export_tasks")
}

// ===============================================
// Feature Voting Module
// ===============================================

// 产品模型
model Product {
  id              String           @id @default(cuid())
  name            String           @unique
  description     String?
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  featureRequests FeatureRequest[]

  @@map("products")
}

// 特性请求模型
model FeatureRequest {
  id          String   @id @default(cuid())
  title       String
  description String
  status      String   @default("under_consideration") // e.g., under_consideration, planned, in_progress, completed
  voteCount   Int      @default(0)
  productId   String
  product     Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  // 提交者信息 - 支持登录和非登录用户
  userId      String?  // 登录用户ID（可选）
  user        User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  authorName  String?  // 非登录用户的昵称（可选）
  authorEmail String?  // 非登录用户的邮箱（可选，用于通知）
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  votes         FeatureVote[]
  comments      FeatureComment[]
  subscriptions FeatureSubscription[]

  @@index([productId, status])
  @@map("feature_requests")
}

// 特性投票模型 - 支持登录和非登录用户
model FeatureVote {
  id               String         @id @default(cuid())
  featureRequestId String
  featureRequest   FeatureRequest @relation(fields: [featureRequestId], references: [id], onDelete: Cascade)
  
  // 投票者身份 - 二选一
  userId           String?        // 登录用户ID（可选）
  user             User?          @relation(fields: [userId], references: [id], onDelete: Cascade)
  anonymousId      String?        // 匿名用户标识（浏览器指纹或UUID，可选）
  
  // 投票者信息（用于匿名用户）
  voterName        String?        // 投票者昵称（可选）
  voterEmail       String?        // 投票者邮箱（可选，用于通知）
  ipAddress        String?        // IP地址（用于防刷票）
  userAgent        String?        // 用户代理（用于防刷票）
  
  createdAt        DateTime       @default(now())

  // 确保同一用户/匿名用户对同一特性只能投一票
  @@unique([featureRequestId, userId])
  @@unique([featureRequestId, anonymousId])
  @@index([featureRequestId])
  @@index([userId])
  @@index([anonymousId])
  @@index([ipAddress]) // 用于防刷票查询
  @@map("feature_votes")
}

// 特性评论模型 - 支持登录和非登录用户
model FeatureComment {
  id               String         @id @default(cuid())
  content          String
  featureRequestId String
  featureRequest   FeatureRequest @relation(fields: [featureRequestId], references: [id], onDelete: Cascade)
  
  // 评论者身份 - 二选一
  userId           String?        // 登录用户ID（可选）
  user             User?          @relation(fields: [userId], references: [id], onDelete: Cascade)
  anonymousId      String?        // 匿名用户标识（可选）
  
  // 评论者信息（用于匿名用户）
  authorName       String?        // 评论者昵称（可选）
  authorEmail      String?        // 评论者邮箱（可选，用于通知）
  ipAddress        String?        // IP地址（用于防刷票）
  
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt

  @@index([featureRequestId])
  @@index([userId])
  @@index([anonymousId])
  @@map("feature_comments")
}

// 邮箱订阅模型 - 用于匿名用户接收通知
model FeatureSubscription {
  id               String         @id @default(cuid())
  featureRequestId String
  featureRequest   FeatureRequest @relation(fields: [featureRequestId], references: [id], onDelete: Cascade)
  
  // 订阅者身份 - 二选一
  userId           String?        // 登录用户ID（可选）
  user             User?          @relation(fields: [userId], references: [id], onDelete: Cascade)
  email            String?        // 匿名用户邮箱（可选）
  
  // 订阅设置
  notifyOnStatusChange Boolean    @default(true)  // 状态变更通知
  notifyOnComments     Boolean    @default(false) // 新评论通知
  isActive             Boolean    @default(true)  // 订阅是否激活
  
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt

  @@unique([featureRequestId, userId])
  @@unique([featureRequestId, email])
  @@index([email])
  @@map("feature_subscriptions")
}
