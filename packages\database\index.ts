// 主数据库（旅行日记）
export { travelMemoPrisma as prisma } from "./src/clients/travel-memo";
export { db } from "./src/client";
export type {
	User as TravelUser,
	TravelDiary,
	Session as TravelSession,
	Account as TravelAccount,
	Passkey,
	Organization,
	Member,
	Invitation,
	Purchase,
	AiChat,
	VideoExportTask,
	Verification,
	PurchaseType,
} from "@prisma/client";

// Zod schemas
export {
	PurchaseSchema,
	PurchaseTypeSchema,
	UserSchema,
	TravelDiarySchema,
	SessionSchema,
	AccountSchema,
	PasskeySchema,
	OrganizationSchema,
	MemberSchema,
	InvitationSchema,
	AiChatSchema,
	VideoExportTaskSchema,
	VerificationSchema,
} from "./src/zod";

// AI图片生成器数据库
export { aiImagesPrisma } from "./src/clients/ai-images";

// 共享类型和工具
export * from "./src/types";
export * from "./src/models/travel-diary";
