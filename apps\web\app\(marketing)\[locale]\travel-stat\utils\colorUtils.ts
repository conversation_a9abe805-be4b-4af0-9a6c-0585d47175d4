import type { ColorTheme, ColorThemeType } from "../types/colorTypes";
import {
	type ColorStrategyManager,
	createColorStrategyManager,
} from "./colorStrategy";

// =============================================================================
// 颜色工具函数 - 使用策略系统的现代化API
// =============================================================================

// 全局策略管理器实例
let globalStrategyManager: ColorStrategyManager = createColorStrategyManager();

// ===== 策略管理器API =====

/**
 * 获取全局策略管理器实例
 * @returns 策略管理器实例
 */
export function getStrategyManager(): ColorStrategyManager {
	return globalStrategyManager;
}

/**
 * 设置全局策略管理器
 * @param manager 新的策略管理器实例
 */
export function setStrategyManager(manager: ColorStrategyManager): void {
	globalStrategyManager = manager;
}

/**
 * 更新策略管理器的颜色主题
 * @param themeId 颜色主题ID
 */
export function updateStrategyTheme(themeId: ColorThemeType): void {
	globalStrategyManager.setTheme(themeId);
}

// ===== 颜色计算API =====

/**
 * 根据数据计算颜色
 * @param data 输入数据（通常是访问次数）
 * @returns 颜色结果对象
 */
export function calculateColor(data: any) {
	return globalStrategyManager.calculateColor(data);
}

/**
 * 获取十六进制颜色
 * @param data 输入数据
 * @returns 十六进制颜色字符串
 */
export function getColorHex(data: any): string {
	return globalStrategyManager.getHexColor(data);
}

/**
 * 获取RGBA颜色
 * @param data 输入数据
 * @returns RGBA颜色字符串
 */
export function getColorRgba(data: any): string {
	return globalStrategyManager.getRgbaColor(data);
}

/**
 * 批量计算颜色（性能优化）
 * @param dataList 数据列表
 * @returns 颜色结果列表
 */
export function batchCalculateColors(dataList: any[]) {
	return globalStrategyManager.batchCalculateColors(dataList);
}

// ===== 主题管理API =====

/**
 * 获取所有可用的颜色主题
 * @param allThemes 所有颜色主题的对象
 */
export function getAvailableColorThemes(
	allThemes: Record<ColorThemeType, ColorTheme>,
): ColorTheme[] {
	return Object.values(allThemes);
}

/**
 * 根据类别获取颜色主题
 * @param category 主题类别
 * @param allThemes 所有颜色主题的对象
 */
export function getColorThemesByCategory(
	category: ColorTheme["category"],
	allThemes: Record<ColorThemeType, ColorTheme>,
): ColorTheme[] {
	return Object.values(allThemes).filter(
		(theme) => theme.category === category,
	);
}

/**
 * 获取推荐的颜色主题（基于地图样式）
 * @param mapStyle 地图样式
 * @param allThemes 所有颜色主题的对象
 */
export function getRecommendedColorThemes(
	mapStyle: string,
	allThemes: Record<ColorThemeType, ColorTheme>,
): ColorTheme[] {
	return Object.values(allThemes).filter((theme) =>
		theme.recommendedForMapStyles?.includes(mapStyle),
	);
}

/**
 * 获取当前策略信息
 */
export function getCurrentStrategyInfo() {
	return globalStrategyManager.getStrategyInfo();
}

/**
 * 获取当前主题信息
 */
export function getCurrentThemeInfo(): ColorTheme {
	return globalStrategyManager.getThemeInfo();
}

// ===== 工厂函数 =====

/**
 * 创建独立的策略管理器实例
 * @param strategyName 策略名称
 * @param themeId 主题ID
 * @returns 新的策略管理器实例
 */
export function createIndependentStrategyManager(
	strategyName: Parameters<
		typeof createColorStrategyManager
	>[0] = "visitCount",
	themeId: ColorThemeType = "classic-blue-green",
) {
	return createColorStrategyManager(strategyName, themeId);
}
