import type { FrontendTravelPoint } from "@packages/database/src/types";
import type { TravelPoint } from "../travel-point-form/types";

// 安全地转换日期，确保返回有效的Date对象
function ensureValidDate(timestamp: Date | string | null | undefined): Date {
	if (!timestamp) {
		return new Date(); // 如果没有提供日期，返回当前日期
	}

	if (timestamp instanceof Date) {
		// 检查Date对象是否有效
		if (Number.isNaN(timestamp.getTime())) {
			console.warn("无效的Date对象，使用当前日期:", timestamp);
			return new Date();
		}
		return timestamp;
	}

	if (typeof timestamp === "string") {
		const date = new Date(timestamp);
		if (Number.isNaN(date.getTime())) {
			console.warn("无效的日期字符串，使用当前日期:", timestamp);
			return new Date();
		}
		return date;
	}

	console.warn("未知的日期格式，使用当前日期:", timestamp);
	return new Date();
}

// 转换前端旅行点位到表单旅行点位
export function transformToFormTravelPoint(
	fp: FrontendTravelPoint,
): TravelPoint {
	return {
		id: fp.id,
		date: ensureValidDate(fp.timestamp), // 使用安全的日期转换
		location: fp.title,
		description: fp.description || "",
		images: fp.images || [],
		iconType: fp.iconType,
		coordinates: { lat: fp.latitude, lng: fp.longitude },
	};
}

// 转换旅行点位数组为地图渲染格式
export function transformPointsForMap(frontendPoints: FrontendTravelPoint[]): {
	id: string;
	date?: Date;
	location: string;
	description: string;
	images: string[];
	iconType: string;
	coordinates?: { lat: number; lng: number };
}[] {
	return frontendPoints.map((point) => ({
		id: point.id,
		date: point.timestamp,
		location: point.title || "无标题",
		description: point.description || "",
		images: (point.images || []).map((img: string | { url: string }) =>
			typeof img === "string" ? img : img.url,
		),
		iconType: point.iconType || "PIN",
		coordinates:
			point.latitude !== undefined && point.longitude !== undefined
				? {
						lat: Number(point.latitude),
						lng: Number(point.longitude),
					}
				: undefined,
	}));
}

// 格式化日期
export function formatDate(dateStr: string | undefined): string {
	if (!dateStr) return "未知日期";
	try {
		const date = new Date(dateStr);
		return date.toLocaleDateString("zh-CN", {
			year: "numeric",
			month: "long",
			day: "numeric",
		});
	} catch (e) {
		return dateStr;
	}
}

// 调试日志工具
export function logDataBeforeRender(data: any) {
	if (process.env.NODE_ENV === "development") {
		setTimeout(() => {
			console.log("地图渲染数据:", data);
		}, 0);
	}
}
