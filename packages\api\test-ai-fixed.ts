import { TravelAIAnalyzer } from "./src/services/travel-ai-analyzer";

async function testAIFixed() {
	console.log("🧪 测试修复后的AI功能...\n");

	// 创建AI分析器（使用OpenAI作为默认）
	const analyzer = new TravelAIAnalyzer({
		provider: "openai",
	});

	// 测试文本
	const testText =
		"昨天去了北京天安门广场，看了升旗仪式，然后在王府井大街吃了烤鸭。";

	try {
		console.log("📝 测试文本:", testText);
		console.log("\n🔍 开始AI分析...");

		const result = await analyzer.analyzeText(testText);

		console.log("\n✅ AI分析成功!");
		console.log("📍 发现地点:", result.locations.length);
		console.log("⏰ 发现时间:", result.timeInfo.length);
		console.log("🎯 发现活动:", result.activities.length);

		console.log("\n📋 详细结果:");
		console.log("地点信息:", JSON.stringify(result.locations, null, 2));
		console.log("时间信息:", JSON.stringify(result.timeInfo, null, 2));
		console.log("活动信息:", JSON.stringify(result.activities, null, 2));
		console.log("总结:", result.summary);
	} catch (error) {
		console.error("❌ AI分析失败:", error);

		if (error instanceof Error) {
			console.error("错误详情:", error.message);
			console.error("错误堆栈:", error.stack);
		}
	}
}

// 运行测试
testAIFixed().catch(console.error);
