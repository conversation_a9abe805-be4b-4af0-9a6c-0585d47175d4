"use client";

import type { TravelPointImage } from "@repo/database/src/types/travel-diary";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { useEffect, useState } from "react";

// Mock 翻译函数 - 必须在 AddPointPanel 导入前设置
const mockTranslations = {
	meta: {
		title: () => "Travel Statistics Tool",
		description: () => "Create and share your travel map",
	},
	navigation: {
		editMode: () => "Edit Mode",
		cardGeneration: () => "Card Generation",
		switchToEdit: () => "Switch to Edit",
		switchToCard: () => "Switch to Card",
		addFootprintsFirst: () => "Add footprints first",
		addPointsToGenerate: () => "Add points to generate",
		generateCard: () => "Generate Card",
	},
	search: {
		title: () => "Search",
		placeholder: () => "Search locations...",
		noResults: () => "No results found",
		suggestions: () => "Suggestions",
		recent: () => "Recent",
		popular: () => "Popular",
	},
	addPoint: {
		title: () => "Add Point",
		selectLocation: () => "Select Location",
		uploadPhoto: () => "Upload Photo",
		description: () => "Description",
		date: () => "Date",
		submit: () => "Add to Map",
	},
};

// 现在安全地导入 AddPointPanel
import { AddPointPanel } from "../../components/map/AddPointPanel";

// 导入实际的类型定义（与真实接口匹配）
interface GeocodeFeature {
	id: string;
	place_name: string;
	center: [number, number];
	context?: Array<{ id: string; text: string; short_code?: string }>;
}

interface AddPointFormData {
	description: string;
	date: string;
	image: TravelPointImage | null;
}

// 模拟位置数据
const mockLocations: GeocodeFeature[] = [
	{
		id: "poi.123",
		place_name: "Eiffel Tower, Paris, France",
		center: [2.2945, 48.8584],
		context: [
			{ id: "country.123", text: "France", short_code: "fr" },
			{ id: "region.123", text: "Île-de-France" },
			{ id: "place.123", text: "Paris" },
		],
	},
	{
		id: "poi.456",
		place_name: "Times Square, New York, NY, USA",
		center: [-73.9857, 40.7581],
		context: [
			{ id: "country.456", text: "United States", short_code: "us" },
			{ id: "region.456", text: "New York" },
			{ id: "place.456", text: "Manhattan" },
		],
	},
	{
		id: "poi.789",
		place_name: "Tokyo Tower, Tokyo, Japan",
		center: [139.7454, 35.6586],
		context: [
			{ id: "country.789", text: "Japan", short_code: "jp" },
			{ id: "region.789", text: "Tokyo" },
			{ id: "place.789", text: "Minato City" },
		],
	},
];

export default function AddPointPanelDemo() {
	const [selectedLocation, setSelectedLocation] =
		useState<GeocodeFeature | null>(null);
	const [isExpanded, setIsExpanded] = useState(true);
	const [submittedData, setSubmittedData] = useState<
		Array<{
			formData: AddPointFormData;
			location: GeocodeFeature;
			timestamp: string;
		}>
	>([]);
	const [testLog, setTestLog] = useState<string[]>([]);

	// 🔧 确保 mock 翻译系统在组件渲染时可用
	useEffect(() => {
		// 重新确认 mock 设置（防止在某些情况下丢失）
		(window as any).useTravelStatTranslations = () => mockTranslations;

		// 添加日志
		const timestamp = new Date().toLocaleTimeString();
		setTestLog((prev) => [
			...prev,
			`[${timestamp}] 🔧 Mock 翻译系统已初始化`,
		]);

		// 清理函数
		return () => {
			(window as any).useTravelStatTranslations = undefined;
		};
	}, []);

	// 添加日志
	const addLog = (message: string) => {
		const timestamp = new Date().toLocaleTimeString();
		setTestLog((prev) => [...prev, `[${timestamp}] ${message}`]);
	};

	// 处理位置选择
	const handleLocationSelect = (location: GeocodeFeature) => {
		setSelectedLocation(location);
		setIsExpanded(true);
		addLog(`选择位置: ${location.place_name}`);
	};

	// 处理清除位置
	const handleClearLocation = () => {
		setSelectedLocation(null);
		addLog("清除选择的位置");
	};

	// 处理表单提交
	const handleSubmit = (
		formData: AddPointFormData,
		location: GeocodeFeature,
	) => {
		const submission = {
			formData,
			location,
			timestamp: new Date().toISOString(),
		};

		setSubmittedData((prev) => [...prev, submission]);
		addLog(
			`提交成功: ${location.place_name} - ${formData.description || "无描述"}`,
		);

		// 模拟提交后的状态重置
		setTimeout(() => {
			addLog("模拟提交完成，组件应该已重置");
		}, 100);
	};

	return (
		<div className="min-h-screen bg-gray-50 p-8">
			<div className="max-w-6xl mx-auto">
				<h1 className="text-3xl font-bold mb-4">
					AddPointPanel 组件测试
				</h1>
				<p className="text-gray-600 mb-8">
					这是一个独立的测试页面，用于验证图片上传和预览功能是否正常工作。
				</p>

				<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
					{/* 左侧：组件测试区 */}
					<div className="space-y-6">
						{/* 测试说明 */}
						<Card>
							<CardHeader>
								<CardTitle>🧪 测试说明</CardTitle>
							</CardHeader>
							<CardContent>
								<p className="text-sm text-gray-600 mb-3">
									请按照以下步骤测试组件：
								</p>
								<ol className="text-sm list-decimal list-inside space-y-1">
									<li>选择一个测试位置</li>
									<li>上传一张图片</li>
									<li>检查是否立即显示预览</li>
									<li>填写描述信息</li>
									<li>提交表单</li>
									<li>查看右侧的日志和提交记录</li>
								</ol>
							</CardContent>
						</Card>

						{/* 位置选择器 */}
						<Card>
							<CardHeader>
								<CardTitle>📍 位置选择器</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="space-y-2">
									{mockLocations.map((location) => (
										<Button
											key={location.id}
											variant={
												selectedLocation?.id ===
												location.id
													? "primary"
													: "outline"
											}
											size="sm"
											onClick={() =>
												handleLocationSelect(location)
											}
											className="w-full justify-start text-left"
										>
											{location.place_name}
										</Button>
									))}
								</div>
								{selectedLocation && (
									<div className="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">
										<p className="text-sm font-medium text-green-800">
											当前选择:{" "}
											{selectedLocation.place_name}
										</p>
										<Button
											variant="ghost"
											size="sm"
											onClick={handleClearLocation}
											className="mt-2 text-red-600 hover:text-red-800"
										>
											清除选择
										</Button>
									</div>
								)}
							</CardContent>
						</Card>

						{/* AddPointPanel 组件 */}
						<Card>
							<CardHeader>
								<CardTitle>🎯 AddPointPanel 组件</CardTitle>
							</CardHeader>
							<CardContent className="p-0">
								<div className="relative">
									<div className="p-4 border-b bg-sky-50">
										<p className="text-sm text-sky-700">
											组件状态:{" "}
											{isExpanded ? "展开" : "收起"} |
											位置:{" "}
											{selectedLocation
												? "已选择"
												: "未选择"}
										</p>
										<Button
											variant="outline"
											size="sm"
											onClick={() =>
												setIsExpanded(!isExpanded)
											}
											className="mt-2"
										>
											{isExpanded
												? "收起面板"
												: "展开面板"}
										</Button>
									</div>

									{/* 实际的 AddPointPanel 组件 */}
									<div className="relative">
										<AddPointPanel
											selectedLocation={selectedLocation}
											isExpanded={isExpanded}
											onSubmit={handleSubmit}
											onClearLocation={
												handleClearLocation
											}
										/>
									</div>
								</div>
							</CardContent>
						</Card>
					</div>

					{/* 右侧：日志和结果 */}
					<div className="space-y-6">
						{/* 实时日志 */}
						<Card>
							<CardHeader>
								<CardTitle>📝 测试日志</CardTitle>
								<Button
									variant="outline"
									size="sm"
									onClick={() => setTestLog([])}
								>
									清除日志
								</Button>
							</CardHeader>
							<CardContent>
								<div className="bg-gray-900 text-green-400 p-3 rounded-lg font-mono text-xs max-h-64 overflow-y-auto">
									{testLog.length === 0 ? (
										<p>等待操作...</p>
									) : (
										testLog.map((log, index) => (
											<div key={index} className="mb-1">
												{log}
											</div>
										))
									)}
								</div>
							</CardContent>
						</Card>

						{/* 提交记录 */}
						<Card>
							<CardHeader>
								<CardTitle>📋 提交记录</CardTitle>
								<Button
									variant="outline"
									size="sm"
									onClick={() => setSubmittedData([])}
								>
									清除记录
								</Button>
							</CardHeader>
							<CardContent>
								{submittedData.length === 0 ? (
									<p className="text-gray-500 text-sm">
										暂无提交记录
									</p>
								) : (
									<div className="space-y-3 max-h-96 overflow-y-auto">
										{submittedData.map((item, index) => (
											<div
												key={index}
												className="p-3 bg-gray-50 rounded-lg border"
											>
												<h4 className="font-medium text-sm mb-2">
													#{index + 1}{" "}
													{item.location.place_name}
												</h4>
												<div className="text-xs text-gray-600 space-y-1">
													<p>
														<strong>日期:</strong>{" "}
														{item.formData.date}
													</p>
													<p>
														<strong>描述:</strong>{" "}
														{item.formData
															.description ||
															"无"}
													</p>
													<p>
														<strong>图片:</strong>{" "}
														{item.formData.image
															? "已上传"
															: "无"}
													</p>
													<p>
														<strong>
															提交时间:
														</strong>{" "}
														{new Date(
															item.timestamp,
														).toLocaleString()}
													</p>
												</div>
											</div>
										))}
									</div>
								)}
							</CardContent>
						</Card>

						{/* 浏览器控制台提示 */}
						<Card>
							<CardHeader>
								<CardTitle>🔍 调试提示</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="text-sm text-gray-600 space-y-2">
									<p>
										<strong>浏览器控制台:</strong>按 F12
										打开开发者工具查看详细日志
									</p>
									<p>
										<strong>关键日志标识:</strong>
									</p>
									<ul className="list-disc list-inside text-xs space-y-1 ml-4">
										<li>🖼️ [Image Upload] - 图片上传相关</li>
										<li>🎨 [UI Render] - UI渲染判断</li>
										<li>🔍 [State Monitor] - 状态监控</li>
										<li>🚨 [CRITICAL BUG] - 严重错误</li>
									</ul>
								</div>
							</CardContent>
						</Card>
					</div>
				</div>
			</div>
		</div>
	);
}
