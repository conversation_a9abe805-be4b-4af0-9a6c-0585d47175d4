"use client";

import { cn } from "@ui/lib"; // 假设 cn 函数在 utils 中
import { ChevronLeft, ChevronRight, ImageIcon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useRef, useState } from "react";
import { ANIMATION_DURATION } from "../constants";

interface ImageGalleryProps {
	images: string[];
	isVisible: boolean;
	animationDuration?: number; // ms for fade, 可选
	onImageClick: (imageSrc: string, index: number) => void;
}

export function ImageGallery({
	images,
	isVisible,
	animationDuration = ANIMATION_DURATION.contentFade,
	onImageClick,
}: ImageGalleryProps) {
	const t = useTranslations("travelMemo.imageGallery");
	const scrollRef = useRef<HTMLDivElement>(null);
	const [scrollPosition, setScrollPosition] = useState(0);
	const [canScrollLeft, setCanScrollLeft] = useState(false);
	const [canScrollRight, setCanScrollRight] = useState(false);
	const [imageLoadStatus, setImageLoadStatus] = useState<
		Record<number, { loaded: boolean; loading: boolean; error: boolean }>
	>({});

	const [isMounted, setIsMounted] = useState(false);
	const [isAnimating, setIsAnimating] = useState(false);

	// 立即开始预加载图片，不依赖于组件是否可见
	useEffect(() => {
		if (!images.length) return;

		// 初始化加载状态
		const initialLoadStatus: Record<
			number,
			{ loaded: boolean; loading: boolean; error: boolean }
		> = {};
		images.forEach((_, index) => {
			initialLoadStatus[index] = {
				loaded: false,
				loading: false,
				error: false,
			};
		});
		setImageLoadStatus(initialLoadStatus);

		// 优先加载前几张图片，然后再加载其他图片
		const priorityCount = Math.min(3, images.length); // 优先加载前三张

		// 优先加载函数
		const loadImage = (imgSrc: string, index: number) => {
			if (imageLoadStatus[index]?.loading) return; // 防止重复加载

			setImageLoadStatus((prev) => ({
				...prev,
				[index]: { ...prev[index], loading: true },
			}));

			const img = new Image();
			img.src = imgSrc;

			img.onload = () => {
				setImageLoadStatus((prev) => ({
					...prev,
					[index]: { loaded: true, loading: false, error: false },
				}));
			};

			img.onerror = () => {
				console.error(`无法加载图片: ${imgSrc}`);
				setImageLoadStatus((prev) => ({
					...prev,
					[index]: { loaded: false, loading: false, error: true },
				}));
			};
		};

		// 先加载优先图片
		for (let i = 0; i < priorityCount; i++) {
			loadImage(images[i], i);
		}

		// 延迟加载其余图片
		if (images.length > priorityCount) {
			const timer = setTimeout(() => {
				for (let i = priorityCount; i < images.length; i++) {
					loadImage(images[i], i);
				}
			}, 300); // 短暂延迟后加载其余图片

			return () => clearTimeout(timer);
		}
	}, [images]);

	// Effect 1: 处理组件的挂载和卸载逻辑
	useEffect(() => {
		let unmountTimer: NodeJS.Timeout | null = null;
		if (isVisible) {
			if (!isMounted) {
				setIsMounted(true);
			}
		} else {
			// 仅当 isMounted 为 true 时，才启动卸载动画和逻辑
			if (isMounted) {
				// 退场动画由下面的 isAnimating effect 处理
				unmountTimer = setTimeout(() => {
					setIsMounted(false); // 动画应该已经结束
				}, animationDuration); // animationDuration 是动画的总时长
			}
		}
		return () => {
			if (unmountTimer) clearTimeout(unmountTimer);
		};
	}, [isVisible, isMounted, animationDuration]);

	// Effect 2: 处理动画状态的切换，依赖于 isVisible 和 isMounted
	useEffect(() => {
		let animationStartTimer: NodeJS.Timeout | null = null;
		if (isVisible && isMounted) {
			// 只有当组件可见并且已挂载时
			// 启动入场动画
			animationStartTimer = setTimeout(() => {
				setIsAnimating(true);
			}, 20); // 小延迟，确保DOM已挂载完毕
		} else {
			// 如果不可见或未挂载，则不应该处于 animating 状态
			setIsAnimating(false);
		}
		return () => {
			if (animationStartTimer) clearTimeout(animationStartTimer);
		};
	}, [isVisible, isMounted]);

	// 更新滚动按钮可见性
	const checkScrollButtons = useCallback(() => {
		if (scrollRef.current) {
			const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
			setCanScrollLeft(scrollLeft > 0);
			setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
		}
	}, []);

	// 监听滚动和窗口大小变化
	useEffect(() => {
		if (!isMounted) return;

		const currentRef = scrollRef.current;
		checkScrollButtons(); // 初始检查

		// 添加事件监听
		const handleScroll = () => checkScrollButtons();
		const handleResize = () => {
			// 在窗口大小变化时重新检查
			setTimeout(checkScrollButtons, 100); // 延迟确保布局已更新
		};

		currentRef?.addEventListener("scroll", handleScroll);
		window.addEventListener("resize", handleResize);

		return () => {
			currentRef?.removeEventListener("scroll", handleScroll);
			window.removeEventListener("resize", handleResize);
		};
	}, [isMounted, checkScrollButtons, images.length]);

	// 处理滚动
	const handleScroll = useCallback((direction: "left" | "right") => {
		if (scrollRef.current) {
			const scrollAmount = scrollRef.current.clientWidth * 0.8;
			scrollRef.current.scrollBy({
				left: direction === "left" ? -scrollAmount : scrollAmount,
				behavior: "smooth",
			});
		}
	}, []);

	// 如果没有挂载或没有图片，则不渲染
	if (!isMounted || images.length === 0) {
		return null;
	}

	return (
		<div
			className={cn(
				"w-full max-w-lg h-32 mx-auto bg-slate-800/70 backdrop-blur-md rounded-lg shadow-lg overflow-hidden border border-slate-700/50",
				"transition-all duration-500 ease-in-out",
				isAnimating
					? "opacity-100 transform translate-y-0"
					: "opacity-0 transform -translate-y-16",
				"flex items-center px-2 group",
			)}
			style={{
				transitionDuration: `${animationDuration}ms`,
				transitionTimingFunction: "cubic-bezier(0.34, 1.56, 0.64, 1)",
			}}
		>
			{/* 左滚动按钮 */}
			<button
				type="button"
				onClick={() => handleScroll("left")}
				className={cn(
					"absolute left-1 top-1/2 transform -translate-y-1/2 z-10 p-1 bg-black/30 text-white rounded-full",
					"opacity-0 group-hover:opacity-100 transition-opacity",
					!canScrollLeft && "opacity-0 pointer-events-none",
				)}
				aria-label={t("scrollLeftButtonLabel")}
				disabled={!canScrollLeft}
			>
				<ChevronLeft className="h-5 w-5" />
			</button>

			{/* 图片容器 */}
			<div
				ref={scrollRef}
				className="flex space-x-2 overflow-x-auto scrollbar-hide h-full py-2 items-center"
				onScroll={(e) => {
					setScrollPosition(e.currentTarget.scrollLeft);
					checkScrollButtons();
				}}
			>
				{images.map((imgSrc, index) => (
					<button
						key={`gallery-image-${index}`}
						type="button"
						className="h-full flex-shrink-0 cursor-pointer hover:scale-105 transition-transform duration-200 border-0 bg-transparent p-0"
						onClick={() => onImageClick(imgSrc, index)}
						aria-label={t("viewImageButtonLabel", {
							indexPlusOne: index + 1,
						})}
					>
						<div className="relative h-full">
							{/* 占位图 - 始终显示 */}
							<div
								className={cn(
									"h-full w-20 flex items-center justify-center bg-slate-700 rounded border border-slate-600 transition-opacity duration-300",
									imageLoadStatus[index]?.loaded
										? "opacity-0 absolute inset-0"
										: "opacity-100",
								)}
							>
								<ImageIcon className="h-8 w-8 text-slate-400" />
							</div>

							{/* 实际图片 - 使用淡入效果 */}
							{(imageLoadStatus[index]?.loading ||
								imageLoadStatus[index]?.loaded) && (
								<img
									src={imgSrc}
									alt={t("imageAltText", {
										indexPlusOne: index + 1,
									})}
									className={cn(
										"h-full w-auto object-cover rounded border border-slate-600 shadow-md hover:border-blue-400 transition-all",
										"duration-300",
										imageLoadStatus[index]?.loaded
											? "opacity-100"
											: "opacity-0",
									)}
									onLoad={() => {
										setImageLoadStatus((prev) => ({
											...prev,
											[index]: {
												loaded: true,
												loading: false,
												error: false,
											},
										}));
									}}
									onError={() => {
										setImageLoadStatus((prev) => ({
											...prev,
											[index]: {
												loaded: false,
												loading: false,
												error: true,
											},
										}));
									}}
								/>
							)}
						</div>
					</button>
				))}
			</div>

			{/* 右滚动按钮 */}
			<button
				type="button"
				onClick={() => handleScroll("right")}
				className={cn(
					"absolute right-1 top-1/2 transform -translate-y-1/2 z-10 p-1 bg-black/30 text-white rounded-full",
					"opacity-0 group-hover:opacity-100 transition-opacity",
					!canScrollRight && "opacity-0 pointer-events-none",
				)}
				aria-label={t("scrollRightButtonLabel")}
				disabled={!canScrollRight}
			>
				<ChevronRight className="h-5 w-5" />
			</button>
		</div>
	);
}

// Add scrollbar-hide utility if not available in Tailwind config
/*
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none;  // IE and Edge
  scrollbar-width: none;  // Firefox
}
*/
