"use client";

import { Badge } from "@ui/components/badge";
import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import {
	AlertCircle,
	CheckCircle,
	Download,
	Eye,
	Loader2,
	Share2,
	<PERSON>rk<PERSON>,
} from "lucide-react";
import React, { useState, useCallback } from "react";

import type {
	CardCustomization,
	CardExportOptions,
	CardTemplate,
	CountryData,
	ExportQuality,
	SocialPlatform,
	TravelPoint,
} from "./types/cardTypes";
import { exportCard } from "./utils/cardExportUtils";

interface CardPreviewPanelProps {
	mapImageData: {
		dataURL: string;
		dimensions: { width: number; height: number };
	} | null;
	travelPoints: TravelPoint[];
	visitedCountries: CountryData[];
	selectedTemplate: CardTemplate;
	customization: CardCustomization;
	platform: SocialPlatform;
	onExport?: (quality: ExportQuality) => void;
}

export function CardPreviewPanel({
	mapImageData,
	travelPoints,
	visitedCountries,
	selectedTemplate,
	customization,
	platform,
	onExport,
}: CardPreviewPanelProps) {
	const [isExporting, setIsExporting] = useState(false);
	const [exportStatus, setExportStatus] = useState<
		"idle" | "success" | "error"
	>("idle");
	const [exportMessage, setExportMessage] = useState("");

	// 导出质量选项
	const qualityOptions: {
		value: ExportQuality;
		label: string;
		description: string;
	}[] = [
		{ value: "low", label: "标准", description: "快速导出" },
		{ value: "medium", label: "高清", description: "推荐质量" },
		{ value: "high", label: "超清", description: "最佳质量" },
	];

	// 默认地图图片数据
	const defaultMapImageData = {
		dataURL:
			"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjYwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iYmciIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojNjc4OGVhO3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiM3NjRiYTI7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2JnKSIvPgogIDx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iMC4zZW0iPuWcsOWbvuWKoOi9veS4rS4uLjwvdGV4dD4KPC9zdmc+",
		dimensions: { width: 800, height: 600 },
	};

	// 获取实际的地图数据
	const actualMapImageData = mapImageData || defaultMapImageData;

	// 处理导出
	const handleExport = useCallback(
		async (quality: ExportQuality = "medium") => {
			if (!selectedTemplate) {
				return;
			}

			setIsExporting(true);
			setExportStatus("idle");

			try {
				const options: CardExportOptions = {
					platform,
					quality,
					filename: `travel-card-${selectedTemplate.id}-${Date.now()}`,
				};

				const result = await exportCard(
					selectedTemplate,
					actualMapImageData,
					travelPoints,
					visitedCountries,
					customization,
					options,
				);

				if (result.success) {
					setExportStatus("success");
					setExportMessage("卡片导出成功！");
					onExport?.(quality);
				} else {
					setExportStatus("error");
					setExportMessage(result.error || "导出失败");
				}
			} catch (error) {
				setExportStatus("error");
				setExportMessage(
					error instanceof Error ? error.message : "导出时发生错误",
				);
			} finally {
				setIsExporting(false);
				// 3秒后清除状态
				setTimeout(() => {
					setExportStatus("idle");
					setExportMessage("");
				}, 3000);
			}
		},
		[
			selectedTemplate,
			platform,
			actualMapImageData,
			travelPoints,
			visitedCountries,
			customization,
			onExport,
		],
	);

	// 渲染卡片预览
	const renderCardPreview = () => {
		if (!selectedTemplate) {
			return (
				<div className="w-full h-48 bg-gray-100 rounded-lg flex items-center justify-center">
					<p className="text-gray-500">请选择模板</p>
				</div>
			);
		}

		const TemplateComponent = selectedTemplate.component;

		return (
			<div className="flex justify-center">
				<TemplateComponent
					mapImageData={actualMapImageData}
					travelPoints={travelPoints}
					visitedCountries={visitedCountries}
					customization={customization}
					platform={platform}
				/>
			</div>
		);
	};

	return (
		<div className="space-y-4 h-full flex flex-col">
			{/* 预览区域 */}
			<Card className="bg-white/90 backdrop-blur-sm border-purple-200 flex-1">
				<CardHeader className="px-4 py-3">
					<CardTitle className="text-lg font-medium flex items-center gap-2">
						<Eye className="w-5 h-5 text-purple-500" />
						实时预览
					</CardTitle>
					<div className="flex items-center gap-2 text-xs text-gray-600">
						<Badge className="text-xs">
							{selectedTemplate.name}
						</Badge>
						<span>•</span>
						<Badge className="text-xs">{platform}</Badge>
						{mapImageData && (
							<>
								<span>•</span>
								<span className="text-green-600">
									实时地图 ✓
								</span>
							</>
						)}
					</div>
				</CardHeader>
				<CardContent className="px-4 pb-4 flex-1 flex flex-col">
					{/* 预览内容 */}
					<div className="bg-gray-50 rounded-lg p-4 flex-1 flex items-center justify-center">
						{renderCardPreview()}
					</div>

					{/* 预览说明 */}
					<div className="mt-3 text-center">
						<p className="text-xs text-gray-500">
							预览为真实尺寸，所见即所得
						</p>
						{!mapImageData && (
							<p className="text-xs text-orange-600 mt-1">
								使用默认地图，切换到编辑模式调整地图后再切换回来获取实时地图
							</p>
						)}
					</div>
				</CardContent>
			</Card>

			{/* 状态消息 */}
			{exportStatus !== "idle" && (
				<Card className="p-3 bg-white/90 backdrop-blur-sm">
					<div
						className={`flex items-center gap-2 justify-center text-sm ${
							exportStatus === "success"
								? "text-green-600"
								: "text-red-600"
						}`}
					>
						{exportStatus === "success" ? (
							<CheckCircle className="w-4 h-4" />
						) : (
							<AlertCircle className="w-4 h-4" />
						)}
						<span className="font-medium">{exportMessage}</span>
					</div>
				</Card>
			)}

			{/* 导出操作 */}
			<Card className="bg-white/90 backdrop-blur-sm border-purple-200">
				<CardHeader className="px-4 py-3">
					<CardTitle className="text-lg font-medium flex items-center gap-2">
						<Download className="w-5 h-5 text-purple-500" />
						导出卡片
					</CardTitle>
				</CardHeader>
				<CardContent className="px-4 pb-4">
					{/* 导出质量选择 */}
					<div className="grid grid-cols-3 gap-2 mb-4">
						{qualityOptions.map((option) => (
							<Button
								key={option.value}
								onClick={() => handleExport(option.value)}
								disabled={isExporting}
								variant={
									option.value === "medium"
										? "secondary"
										: "outline"
								}
								size="sm"
								className="flex flex-col h-auto py-3 px-2"
							>
								{isExporting ? (
									<Loader2 className="w-4 h-4 animate-spin mb-1" />
								) : (
									<Download className="w-4 h-4 mb-1" />
								)}
								<span className="text-xs font-medium">
									{option.label}
								</span>
								<span className="text-xs text-gray-500">
									{option.description}
								</span>
							</Button>
						))}
					</div>

					{/* 其他操作 */}
					<div className="grid grid-cols-2 gap-2">
						<Button
							variant="outline"
							size="sm"
							disabled={isExporting}
							className="w-full"
						>
							<Share2 className="w-4 h-4 mr-2" />
							分享
						</Button>
						<Button
							variant="outline"
							size="sm"
							disabled={isExporting}
							className="w-full"
						>
							复制
						</Button>
					</div>
				</CardContent>
			</Card>

			{/* 数据统计 */}
			<Card className="bg-white/90 backdrop-blur-sm border-purple-200">
				<CardHeader className="px-4 py-3">
					<CardTitle className="text-lg font-medium flex items-center gap-2">
						<Sparkles className="w-5 h-5 text-purple-500" />
						数据统计
					</CardTitle>
				</CardHeader>
				<CardContent className="px-4 pb-4">
					<div className="grid grid-cols-3 gap-4 text-center">
						<div>
							<div className="text-xl font-bold text-blue-600">
								{travelPoints.length}
							</div>
							<div className="text-xs text-gray-600">足迹点</div>
						</div>
						<div>
							<div className="text-xl font-bold text-green-600">
								{new Set(travelPoints.map((p) => p.city)).size}
							</div>
							<div className="text-xs text-gray-600">城市</div>
						</div>
						<div>
							<div className="text-xl font-bold text-purple-600">
								{visitedCountries.length}
							</div>
							<div className="text-xs text-gray-600">国家</div>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
