"use client";
import { useCallback, useEffect, useState } from "react";
import { imageCache } from "../../utils/imageCache";

interface PerformanceStats {
	markerCount: number;
	visibleMarkers: number;
	clusteredMarkers: number;
	imageCache: {
		totalEntries: number;
		totalSize: number;
		formattedSize: string;
	};
	renderTime: number;
	memoryUsage?: {
		usedJSHeapSize: number;
		totalJSHeapSize: number;
		jsHeapSizeLimit: number;
	};
}

interface PerformanceMonitorProps {
	travelPoints: any[];
	isClusterMode: boolean;
	mapLoaded: boolean;
	onToggleVisibility?: () => void;
}

export function PerformanceMonitor({
	travelPoints,
	isClusterMode,
	mapLoaded,
	onToggleVisibility,
}: PerformanceMonitorProps) {
	const [stats, setStats] = useState<PerformanceStats>({
		markerCount: 0,
		visibleMarkers: 0,
		clusteredMarkers: 0,
		imageCache: {
			totalEntries: 0,
			totalSize: 0,
			formattedSize: "0 B",
		},
		renderTime: 0,
	});
	const [isVisible, setIsVisible] = useState(false);
	const [isExpanded, setIsExpanded] = useState(false);

	// 更新性能统计
	const updateStats = useCallback(() => {
		const startTime = performance.now();

		// 基础统计
		const markerCount = travelPoints.length;
		const visibleMarkers = isClusterMode
			? Math.min(markerCount, 100) // 聚合模式下限制显示数量
			: markerCount;
		const clusteredMarkers = isClusterMode
			? markerCount - visibleMarkers
			: 0;

		// 图片缓存统计
		const cacheStats = imageCache.getCacheStats();

		// 内存使用统计（如果支持）
		let memoryUsage;
		if ("memory" in performance) {
			const perfMemory = (performance as any).memory;
			memoryUsage = {
				usedJSHeapSize: perfMemory.usedJSHeapSize,
				totalJSHeapSize: perfMemory.totalJSHeapSize,
				jsHeapSizeLimit: perfMemory.jsHeapSizeLimit,
			};
		}

		const renderTime = performance.now() - startTime;

		setStats({
			markerCount,
			visibleMarkers,
			clusteredMarkers,
			imageCache: cacheStats,
			renderTime,
			memoryUsage,
		});
	}, [travelPoints.length, isClusterMode]);

	// 定期更新统计
	useEffect(() => {
		if (!mapLoaded) return;

		updateStats();
		const interval = setInterval(updateStats, 2000); // 每2秒更新一次

		return () => clearInterval(interval);
	}, [mapLoaded, updateStats]);

	// 格式化字节数
	const formatBytes = (bytes: number): string => {
		if (bytes === 0) return "0 B";
		const k = 1024;
		const sizes = ["B", "KB", "MB", "GB"];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
	};

	// 计算性能分数
	const getPerformanceScore = (): {
		score: number;
		grade: string;
		color: string;
	} => {
		let score = 100;

		// 标记数量影响
		if (stats.markerCount > 200) score -= 30;
		else if (stats.markerCount > 100) score -= 15;
		else if (stats.markerCount > 50) score -= 5;

		// 聚合模式优化
		if (isClusterMode && stats.markerCount > 50) score += 20;

		// 缓存效率
		if (stats.imageCache.totalEntries > 0) score += 10;

		// 内存使用
		if (stats.memoryUsage) {
			const memoryUsageRatio =
				stats.memoryUsage.usedJSHeapSize /
				stats.memoryUsage.jsHeapSizeLimit;
			if (memoryUsageRatio > 0.8) score -= 20;
			else if (memoryUsageRatio > 0.6) score -= 10;
		}

		score = Math.max(0, Math.min(100, score));

		let grade: string;
		let color: string;
		if (score >= 90) {
			grade = "A+";
			color = "text-green-600";
		} else if (score >= 80) {
			grade = "A";
			color = "text-green-500";
		} else if (score >= 70) {
			grade = "B";
			color = "text-yellow-500";
		} else if (score >= 60) {
			grade = "C";
			color = "text-orange-500";
		} else {
			grade = "D";
			color = "text-red-500";
		}

		return { score, grade, color };
	};

	const performanceScore = getPerformanceScore();

	// 清理缓存
	const handleClearCache = () => {
		imageCache.clearCache();
		updateStats();
	};

	if (!isVisible) {
		return (
			<button
				type="button"
				onClick={() => setIsVisible(true)}
				className="fixed bottom-4 right-4 bg-blue-600 text-white px-3 py-2 rounded-lg shadow-lg hover:bg-blue-700 transition-colors z-50 text-sm"
				title="显示性能监控"
			>
				⚡ 性能
			</button>
		);
	}

	return (
		<div className="fixed bottom-4 right-4 bg-white rounded-lg shadow-xl border z-50 min-w-64">
			{/* 标题栏 */}
			<div className="flex items-center justify-between p-3 border-b">
				<div className="flex items-center gap-2">
					<span className="text-sm font-medium">⚡ 性能监控</span>
					<span
						className={`text-xs px-2 py-1 rounded ${performanceScore.color} bg-opacity-10`}
					>
						{performanceScore.grade} ({performanceScore.score})
					</span>
				</div>
				<div className="flex items-center gap-1">
					<button
						type="button"
						onClick={() => setIsExpanded(!isExpanded)}
						className="text-gray-400 hover:text-gray-600 text-xs p-1"
						title={isExpanded ? "收起" : "展开"}
					>
						{isExpanded ? "📄" : "📊"}
					</button>
					<button
						type="button"
						onClick={() => setIsVisible(false)}
						className="text-gray-400 hover:text-gray-600 text-xs p-1"
						title="隐藏"
					>
						✕
					</button>
				</div>
			</div>

			{/* 基础统计 */}
			<div className="p-3 space-y-2">
				<div className="grid grid-cols-2 gap-2 text-xs">
					<div className="flex justify-between">
						<span className="text-gray-600">总标记:</span>
						<span className="font-mono">{stats.markerCount}</span>
					</div>
					<div className="flex justify-between">
						<span className="text-gray-600">可见:</span>
						<span className="font-mono text-green-600">
							{stats.visibleMarkers}
						</span>
					</div>
					{isClusterMode && (
						<div className="flex justify-between col-span-2">
							<span className="text-gray-600">聚合:</span>
							<span className="font-mono text-blue-600">
								{stats.clusteredMarkers}
							</span>
						</div>
					)}
				</div>

				{/* 缓存统计 */}
				<div className="border-t pt-2">
					<div className="flex justify-between text-xs mb-1">
						<span className="text-gray-600">图片缓存:</span>
						<span className="font-mono">
							{stats.imageCache.totalEntries} 项
						</span>
					</div>
					<div className="flex justify-between text-xs">
						<span className="text-gray-600">缓存大小:</span>
						<span className="font-mono">
							{stats.imageCache.formattedSize}
						</span>
					</div>
				</div>

				{/* 操作按钮 */}
				<div className="flex gap-2 pt-2">
					<button
						type="button"
						onClick={handleClearCache}
						className="flex-1 text-xs bg-red-50 text-red-600 px-2 py-1 rounded hover:bg-red-100 transition-colors"
					>
						清理缓存
					</button>
					<button
						type="button"
						onClick={updateStats}
						className="flex-1 text-xs bg-blue-50 text-blue-600 px-2 py-1 rounded hover:bg-blue-100 transition-colors"
					>
						刷新
					</button>
				</div>
			</div>

			{/* 详细信息 */}
			{isExpanded && (
				<div className="border-t p-3 space-y-2">
					<div className="text-xs text-gray-600 font-medium mb-2">
						详细信息
					</div>

					{/* 渲染时间 */}
					<div className="flex justify-between text-xs">
						<span className="text-gray-600">渲染时间:</span>
						<span className="font-mono">
							{stats.renderTime.toFixed(2)}ms
						</span>
					</div>

					{/* 内存使用 */}
					{stats.memoryUsage && (
						<div className="space-y-1">
							<div className="text-xs text-gray-600">
								内存使用:
							</div>
							<div className="flex justify-between text-xs pl-2">
								<span className="text-gray-500">已用:</span>
								<span className="font-mono">
									{formatBytes(
										stats.memoryUsage.usedJSHeapSize,
									)}
								</span>
							</div>
							<div className="flex justify-between text-xs pl-2">
								<span className="text-gray-500">总计:</span>
								<span className="font-mono">
									{formatBytes(
										stats.memoryUsage.totalJSHeapSize,
									)}
								</span>
							</div>
							<div className="flex justify-between text-xs pl-2">
								<span className="text-gray-500">限制:</span>
								<span className="font-mono">
									{formatBytes(
										stats.memoryUsage.jsHeapSizeLimit,
									)}
								</span>
							</div>
						</div>
					)}

					{/* 优化建议 */}
					<div className="border-t pt-2">
						<div className="text-xs text-gray-600 font-medium mb-1">
							优化建议
						</div>
						<div className="space-y-1 text-xs text-gray-500">
							{stats.markerCount > 100 && !isClusterMode && (
								<div>• 启用聚合模式以提升性能</div>
							)}
							{stats.imageCache.totalEntries === 0 &&
								stats.markerCount > 0 && (
									<div>
										• 图片缓存未启用，可能影响加载速度
									</div>
								)}
							{stats.memoryUsage &&
								stats.memoryUsage.usedJSHeapSize /
									stats.memoryUsage.jsHeapSizeLimit >
									0.7 && (
									<div>• 内存使用较高，考虑清理缓存</div>
								)}
							{performanceScore.score >= 90 && (
								<div className="text-green-600">
									✓ 性能表现优秀
								</div>
							)}
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
