"use client";

import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@ui/components/popover";
import { Separator } from "@ui/components/separator";
import { cn } from "@ui/lib/utils";
import { Download, Loader2, Palette, Settings, Sparkles } from "lucide-react";
import React, { useState } from "react";
import { exportPresets } from "./presets";
import type { ExportOptions } from "./types";

interface ExportOptionsPopoverProps {
	onExport: (options: ExportOptions) => void;
	isExporting?: boolean;
	disabled?: boolean;
	className?: string;
}

export function ExportOptionsPopover({
	onExport,
	isExporting = false,
	disabled = false,
	className,
}: ExportOptionsPopoverProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [selectedPreset, setSelectedPreset] = useState(exportPresets[0]);

	const handleExport = (presetId?: string) => {
		const preset = presetId
			? exportPresets.find((p) => p.id === presetId) || exportPresets[0]
			: selectedPreset;

		onExport({
			...preset.options,
			filename: `travel_map_${preset.id}_${Date.now()}.png`,
		});
		setIsOpen(false);
	};

	const triggerButtonContent = isExporting ? (
		<>
			<Loader2 className="w-4 h-4 animate-spin mr-2" />
			导出中...
		</>
	) : (
		<>
			<Download className="w-4 h-4 mr-2" />
			导出地图
		</>
	);

	return (
		<Popover open={isOpen} onOpenChange={setIsOpen}>
			<PopoverTrigger asChild>
				<Button
					disabled={disabled || isExporting}
					className={cn(
						"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-medium shadow-lg hover:shadow-xl transition-all duration-200",
						className,
					)}
				>
					{triggerButtonContent}
				</Button>
			</PopoverTrigger>

			<PopoverContent
				className="w-96 p-0 bg-white/95 backdrop-blur-sm border border-gray-200/80"
				sideOffset={8}
				align="end"
			>
				{/* Header */}
				<div className="p-4 border-b border-gray-100">
					<div className="flex items-center gap-2 mb-1">
						<Palette className="w-5 h-5 text-purple-600" />
						<h3 className="font-semibold text-gray-900">
							导出选项
						</h3>
					</div>
					<p className="text-sm text-gray-600">
						选择适合的导出模式，打造完美的旅行地图
					</p>
				</div>

				{/* Preset Grid */}
				<div className="p-4">
					<div className="grid grid-cols-1 gap-3">
						{exportPresets.map((preset) => {
							const IconComponent = preset.icon;
							const isSelected = selectedPreset.id === preset.id;

							return (
								<button
									key={preset.id}
									type="button"
									onClick={() => setSelectedPreset(preset)}
									className={cn(
										"group relative p-4 rounded-lg border-2 transition-all duration-300 text-left",
										"hover:shadow-md hover:scale-[1.02]",
										isSelected
											? "border-purple-300 bg-purple-50/80 shadow-sm"
											: "border-gray-200 bg-white/80 hover:border-gray-300",
									)}
								>
									{/* Selection Indicator */}
									{isSelected && (
										<div className="absolute -top-1 -right-1 w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center">
											<Sparkles className="w-3 h-3 text-white" />
										</div>
									)}

									<div className="flex items-start gap-3">
										{/* Icon */}
										<div
											className={cn(
												"flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center",
												preset.color || "bg-gray-500",
											)}
										>
											<IconComponent className="w-5 h-5 text-white" />
										</div>

										{/* Content */}
										<div className="flex-1 min-w-0">
											<div className="flex items-center gap-2 mb-1">
												<h4 className="font-medium text-gray-900 group-hover:text-purple-700">
													{preset.name}
												</h4>
												{preset.badge && (
													<Badge className="text-xs bg-purple-100 text-purple-700 border-purple-200">
														{preset.badge}
													</Badge>
												)}
											</div>
											<p className="text-sm text-gray-600 leading-relaxed">
												{preset.description}
											</p>

											{/* Technical Details */}
											<div className="flex items-center gap-2 mt-2 text-xs text-gray-500">
												<span className="bg-gray-100 px-2 py-1 rounded">
													{preset.options.scale}x
												</span>
												<span className="bg-gray-100 px-2 py-1 rounded">
													{preset.options.format.toUpperCase()}
												</span>
												<span className="bg-gray-100 px-2 py-1 rounded">
													Q{preset.options.quality}
												</span>
											</div>
										</div>
									</div>
								</button>
							);
						})}
					</div>
				</div>

				<Separator className="bg-gray-100" />

				{/* Footer Actions */}
				<div className="p-4 bg-gray-50/80">
					<div className="flex items-center justify-between gap-3">
						{/* Current Selection Info */}
						<div className="flex items-center gap-2 text-sm text-gray-600">
							<Settings className="w-4 h-4" />
							<span>已选择: {selectedPreset.name}</span>
						</div>

						{/* Action Buttons */}
						<div className="flex gap-2">
							<Button
								variant="outline"
								size="sm"
								onClick={() => setIsOpen(false)}
								className="bg-white"
							>
								取消
							</Button>
							<Button
								size="sm"
								onClick={() => handleExport()}
								disabled={isExporting}
								className="bg-purple-600 hover:bg-purple-700 text-white"
							>
								{isExporting ? (
									<Loader2 className="w-4 h-4 animate-spin mr-2" />
								) : (
									<Download className="w-4 h-4 mr-2" />
								)}
								导出
							</Button>
						</div>
					</div>
				</div>
			</PopoverContent>
		</Popover>
	);
}
