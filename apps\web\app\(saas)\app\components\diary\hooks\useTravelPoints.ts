import type {
	FrontendTravelDiary,
	FrontendTravelPoint,
	FrontendTravelTimeline,
} from "@packages/database/src/types";
import { useTranslations } from "next-intl";
import { useCallback, useState } from "react";
import { toast } from "sonner";
import type { TravelPoint } from "../travel-point-form/types";

interface UseTravelPointsProps {
	diary: FrontendTravelDiary | null;
	activeTimelineId: string;
	onDiaryUpdate: (updatedDiary: FrontendTravelDiary) => void;
	onMarkAsModified: () => void;
}

export function useTravelPoints({
	diary,
	activeTimelineId,
	onDiaryUpdate,
	onMarkAsModified,
}: UseTravelPointsProps) {
	const t = useTranslations("travelMemo.diaryEditor");
	const [activePointId, setActivePointId] = useState<string>("");
	const [isAddPointDialogOpen, setIsAddPointDialogOpen] =
		useState<boolean>(false);

	// 添加旅行点位
	const handleAddPoint = useCallback(
		(newPoint: TravelPoint) => {
			if (!diary) return;
			onMarkAsModified();

			const newFrontendPoint: FrontendTravelPoint = {
				id: newPoint.id,
				timelineId: activeTimelineId,
				title: newPoint.location,
				description: newPoint.description || null,
				latitude: newPoint.coordinates.lat,
				longitude: newPoint.coordinates.lng,
				coordinates: {
					lat: newPoint.coordinates.lat,
					lng: newPoint.coordinates.lng,
				},
				iconType: newPoint.iconType as string,
				timestamp: newPoint.date,
				images: newPoint.images || [],
				address: null,
			};

			const updatedDiary: FrontendTravelDiary = {
				...diary,
				timelines: diary.timelines.map(
					(timeline: FrontendTravelTimeline) =>
						timeline.id === activeTimelineId
							? {
									...timeline,
									points: [
										...timeline.points,
										newFrontendPoint,
									],
								}
							: timeline,
				),
			};

			onDiaryUpdate(updatedDiary);
			setIsAddPointDialogOpen(false);
			console.log("点位添加成功，关闭对话框");
			toast.success(t("toasts.pointAdded"));
		},
		[diary, activeTimelineId, onDiaryUpdate, onMarkAsModified, t],
	);

	// 编辑旅行点位
	const handleEditPoint = useCallback(
		(id: string, updatedData: Partial<TravelPoint>) => {
			if (!diary) return;
			onMarkAsModified();

			const updatedDiary: FrontendTravelDiary = {
				...diary,
				timelines: diary.timelines.map(
					(timeline: FrontendTravelTimeline) =>
						timeline.id === activeTimelineId
							? {
									...timeline,
									points: timeline.points.map(
										(point: FrontendTravelPoint) => {
											if (point.id !== id) return point;

											const newPointData: Partial<FrontendTravelPoint> =
												{};
											if (
												updatedData.description !==
												undefined
											)
												newPointData.description =
													updatedData.description;
											if (updatedData.images)
												newPointData.images =
													updatedData.images;
											if (updatedData.iconType)
												newPointData.iconType =
													updatedData.iconType as string;
											if (
												updatedData.location !==
												undefined
											)
												newPointData.title =
													updatedData.location;
											if (updatedData.date !== undefined)
												newPointData.timestamp =
													updatedData.date;
											if (updatedData.coordinates) {
												newPointData.latitude =
													updatedData.coordinates.lat;
												newPointData.longitude =
													updatedData.coordinates.lng;
											}

											return {
												...point,
												...newPointData,
											};
										},
									),
								}
							: timeline,
				),
			};

			onDiaryUpdate(updatedDiary);
			toast.success(t("toasts.pointUpdated"));
		},
		[diary, activeTimelineId, onDiaryUpdate, onMarkAsModified, t],
	);

	// 删除旅行点位
	const handleDeletePoint = useCallback(
		(id: string) => {
			if (!diary) return;
			onMarkAsModified();

			if (activePointId === id) {
				setActivePointId("");
			}

			const updatedDiary: FrontendTravelDiary = {
				...diary,
				timelines: diary.timelines.map(
					(timeline: FrontendTravelTimeline) =>
						timeline.id === activeTimelineId
							? {
									...timeline,
									points: timeline.points.filter(
										(point: FrontendTravelPoint) =>
											point.id !== id,
									),
								}
							: timeline,
				),
			};

			onDiaryUpdate(updatedDiary);
		},
		[
			diary,
			activeTimelineId,
			activePointId,
			onDiaryUpdate,
			onMarkAsModified,
		],
	);

	// 重新排序旅行点位
	const handleReorderPoints = useCallback(
		(reorderedPoints: TravelPoint[]) => {
			if (!diary) return;
			onMarkAsModified();

			const updatedDiary: FrontendTravelDiary = {
				...diary,
				timelines: diary.timelines.map(
					(timeline: FrontendTravelTimeline) =>
						timeline.id === activeTimelineId
							? {
									...timeline,
									points: reorderedPoints.map(
										(
											p: TravelPoint,
										): FrontendTravelPoint => ({
											id: p.id,
											timelineId: activeTimelineId,
											title: p.location,
											description: p.description || null,
											latitude: p.coordinates.lat,
											longitude: p.coordinates.lng,
											coordinates: {
												lat: p.coordinates.lat,
												lng: p.coordinates.lng,
											},
											iconType: p.iconType as string,
											timestamp: p.date,
											images: p.images || [],
											address: null,
										}),
									),
								}
							: timeline,
				),
			};

			onDiaryUpdate(updatedDiary);
		},
		[diary, activeTimelineId, onDiaryUpdate, onMarkAsModified],
	);

	// 在指定位置插入旅行点位（用于复制功能）
	const handleInsertPoint = useCallback(
		(newPoint: TravelPoint, afterIndex: number) => {
			if (!diary) return;
			onMarkAsModified();

			const newFrontendPoint: FrontendTravelPoint = {
				id: newPoint.id,
				timelineId: activeTimelineId,
				title: newPoint.location,
				description: newPoint.description || null,
				latitude: newPoint.coordinates.lat,
				longitude: newPoint.coordinates.lng,
				coordinates: {
					lat: newPoint.coordinates.lat,
					lng: newPoint.coordinates.lng,
				},
				iconType: newPoint.iconType as string,
				timestamp: newPoint.date,
				images: newPoint.images || [],
				address: null,
			};

			const updatedDiary: FrontendTravelDiary = {
				...diary,
				timelines: diary.timelines.map(
					(timeline: FrontendTravelTimeline) =>
						timeline.id === activeTimelineId
							? {
									...timeline,
									points: [
										// 在指定位置插入新点位
										...timeline.points.slice(
											0,
											afterIndex + 1,
										),
										newFrontendPoint,
										...timeline.points.slice(
											afterIndex + 1,
										),
									],
								}
							: timeline,
				),
			};

			onDiaryUpdate(updatedDiary);
			toast.success(t("toasts.pointAdded"));
		},
		[diary, activeTimelineId, onDiaryUpdate, onMarkAsModified, t],
	);

	// 选择旅行点位
	const handlePointSelect = useCallback((pointId: string) => {
		setActivePointId(pointId);
	}, []);

	// 打开添加点位对话框
	const handleOpenAddPointDialog = useCallback(() => {
		console.log("[useTravelPoints] 添加旅行点位按钮被点击", {
			currentState: isAddPointDialogOpen,
			activeTimelineId,
		});
		setIsAddPointDialogOpen(true);
		console.log("[useTravelPoints] isAddPointDialogOpen 设置为 true");
	}, [isAddPointDialogOpen, activeTimelineId]);

	// 关闭添加点位对话框
	const handleAddPointDialogClose = useCallback(() => {
		console.log("[useTravelPoints] 接收到对话框关闭通知");
		setIsAddPointDialogOpen(false);
		console.log("[useTravelPoints] isAddPointDialogOpen 重置为 false");
	}, []);

	return {
		// 状态
		activePointId,
		isAddPointDialogOpen,

		// 方法
		handleAddPoint,
		handleEditPoint,
		handleDeletePoint,
		handleReorderPoints,
		handleInsertPoint,
		handlePointSelect,
		handleOpenAddPointDialog,
		handleAddPointDialogClose,

		// 状态控制方法
		setActivePointId,
		setIsAddPointDialogOpen,
	};
}
