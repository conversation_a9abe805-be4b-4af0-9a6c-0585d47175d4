---
type: "agent_requested"
---

# Windows Terminal Commands Guide

## Command Chaining
- **Windows PowerShell**: Use `;` or `|` instead of `&&`
  ```powershell
  # ❌ Linux/Mac style
  cd apps/web && npm run build
  
  # ✅ Windows PowerShell style
  cd apps/web; npm run build
```

## File Paths
- **Always wrap paths in double quotes** when they contain spaces or special characters
  ```powershell
  # ❌ Without quotes
  npx tsc --noEmit app/(saas)/app/components/file.tsx
  
  # ✅ With quotes
  npx tsc --noEmit "app/(saas)/app/components/file.tsx"
  ```

## Directory Navigation
- Use backslashes `\` or forward slashes `/` (both work in PowerShell)
- Prefer forward slashes for consistency with cross-platform tools
  ```powershell
  # ✅ Both work
  cd "apps\web"
  cd "apps/web"
  ```

## Environment Variables
- Use `$env:` prefix for environment variables
  ```powershell
  # ❌ Linux/Mac style
  NODE_ENV=production npm start
  
  # ✅ Windows PowerShell style
  $env:NODE_ENV="production"; npm start
  ```

## File Operations
- Use PowerShell cmdlets when available
  ```powershell
  # Copy files
  Copy-Item "source.txt" "destination.txt"
  
  # Remove files
  Remove-Item "file.txt"
  
  # Create directory
  New-Item -ItemType Directory "new-folder"
  ```

## Package Manager Commands
- npm, pnpm, yarn work the same way
- Use quotes for complex script names
  ```powershell
  npm run "build:production"
  pnpm --filter database migrate --name "add_status_field"
  ```

## Common Windows-Specific Issues
1. **Execution Policy**: May need to run `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`
2. **Long Paths**: Enable long path support if needed
3. **Case Sensitivity**: Windows is case-insensitive but be consistent
4. **Special Characters**: Always quote paths with parentheses, spaces, or special chars

## TypeScript/Node.js Commands
```powershell
# Type checking with quoted paths
npx tsc --noEmit --skipLibCheck "app/(saas)/app/components/file.tsx"

# Running scripts
npm run type-check
pnpm run dev

# Database operations
pnpm --filter database migrate --name "migration_name"
pnpm --filter database push
pnpm --filter database generate
```

## Best Practices
1. Always use double quotes for file paths
2. Use semicolons `;` for command chaining
3. Test commands in PowerShell before automation
4. Be aware of execution policy restrictions
5. Use forward slashes in paths for cross-platform compatibility



