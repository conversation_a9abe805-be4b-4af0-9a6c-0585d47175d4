// 动画持续时间(毫秒)
export const ANIMATION_DURATION = {
	pointTransition: 6000, // 点位之间的过渡
	contentFade: 800, // 内容淡入淡出
	pause: 4000, // 每个点位停留时间
	blinkInterval: 2000, // 点位闪烁间隔
	autoPlayDelay: 5000, // 自动播放前的等待时间
	lastPointPause: 5000, // 最后一个点位停留时间
	pauseBeforeCompletion: 2000, // 结束前的额外等待时间
	riseAnimation: 2000, // 结束时相机上升的动画时间
	overviewFit: 1000, // 概览模式调整视角的时间
	setupFlightToPathStart: 1500, // 准备阶段：飞向路径起点的动画时长
	arrivalFocusFlight: 1200, // 抵达阶段：聚焦到目标点的动画时长
};

// 预设颜色主题
export const THEME_COLORS = {
	primary: "#3B82F6", // 蓝色
	secondary: "#10B981", // 绿色
	accent: "#F97316", // 橙色
};

// 地图故事状态
export enum StoryState {
	OVERVIEW = "overview", // 俯视路线概览状态
	INITIALIZING = "initializing", // 初始化状态，例如startJourney的下降动画
	FLYING_TO_POINT = "flying_to_point", // 正在飞向某个点位
	POINT_ARRIVED_DISPLAYING = "point_arrived_displaying", // 到达点位，显示信息
	POINT_CONTENT_DISPLAYED = "point_content_displayed", // 点位内容已完全显示(打字效果完成)
	PAUSED_ON_POINT = "paused_on_point", // 在某个点位上暂停
	PLAYING = "playing", // 正常播放状态
	ANIMATING_TO_COMPLETED_VIEW = "animating_to_completed_view", // 执行结束动画（相机上升）
	COMPLETED = "completed", // 播放完成状态
}

// 新增相机参数常量
export const CAMERA_SETTINGS = {
	// 用于"抵达并聚焦"到某个点时的视角参数
	arrivalZoom: 15,
	arrivalPitch: 50,
	arrivalBearing: 0, // 默认正北

	// 用于路径动画开始前，计算一个合适的固定缩放和俯仰
	// 这些值可以根据具体场景调整
	pathAnimationMinZoom: 12, // 保证路径可见的最小缩放
	pathAnimationDefaultPitch: 45, // 路径动画的默认俯仰角
};
