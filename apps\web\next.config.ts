import { withContentCollections } from "@content-collections/next";
import type { NextConfig } from "next";
import nextIntlPlugin from "next-intl/plugin";
import path from "node:path";

const withNextIntl = nextIntlPlugin("./modules/i18n/request.ts");

const nextConfig: NextConfig = {
	images: {
		remotePatterns: [
			{
				// google profile images
				protocol: "https",
				hostname: "lh3.googleusercontent.com",
			},
			{
				// github profile images
				protocol: "https",
				hostname: "avatars.githubusercontent.com",
			},
			{
				// unsplash images
				protocol: "https",
				hostname: "images.unsplash.com",
			},
			{
				// unsplash cdn
				protocol: "https",
				hostname: "plus.unsplash.com",
			},
			{
				// cloudinary images
				protocol: "https",
				hostname: "res.cloudinary.com",
			},
			{
				// imgur images
				protocol: "https",
				hostname: "i.imgur.com",
			},
			{
				// pexels images
				protocol: "https",
				hostname: "images.pexels.com",
			},
			{
				// postformat images
				protocol: "https",
				hostname: "postformat.info",
			},
		],
	},
	// 添加 webpack 配置以支持路径别名
	webpack: (config) => {
		config.resolve.alias = {
			...config.resolve.alias,
			"@modules": path.resolve(__dirname, "modules"),
			"@packages/ai": path.resolve(__dirname, "../../packages/ai"),
			"@packages/api": path.resolve(__dirname, "../../packages/api"),
			"@packages/auth": path.resolve(__dirname, "../../packages/auth"),
			"@packages/database": path.resolve(
				__dirname,
				"../../packages/database",
			),
			"@packages/i18n": path.resolve(__dirname, "../../packages/i18n"),
			"@packages/logs": path.resolve(__dirname, "../../packages/logs"),
			"@packages/mail": path.resolve(__dirname, "../../packages/mail"),
			"@packages/payments": path.resolve(
				__dirname,
				"../../packages/payments",
			),
			"@packages/storage": path.resolve(
				__dirname,
				"../../packages/storage",
			),
			"@packages/utils": path.resolve(__dirname, "../../packages/utils"),
		};
		config.module.rules.push({
			test: /\.(woff|woff2|eot|ttf|otf)$/i,
			type: "asset/resource",
		});

		// 将 playwright 相关包标记为外部依赖
		config.externals = [
			...(config.externals || []),
			"playwright",
			"playwright-core",
			"chromium-bidi",
			"electron",
			/playwright-core\/.*/, // 排除所有 playwright-core 下的文件
		];

		// 添加HTML文件处理规则
		config.module.rules.push({
			test: /\.html$/,
			loader: "file-loader",
			options: {
				name: "[name].[ext]",
			},
		});

		return config;
	},
	async redirects() {
		return [
			{
				source: "/app/settings",
				destination: "/app/settings/general",
				permanent: true,
			},
			{
				source: "/app/:organizationSlug/settings",
				destination: "/app/:organizationSlug/settings/general",
				permanent: true,
			},
			{
				source: "/app/admin",
				destination: "/app/admin/users",
				permanent: true,
			},
		];
	},
	eslint: {
		ignoreDuringBuilds: true,
	},
	typescript: {
		ignoreBuildErrors: true,
	},
	// 使用新的 serverExternalPackages 配置
	serverExternalPackages: ["@repo/api", "@repo/auth", "@repo/database"],
};

export default withContentCollections(withNextIntl(nextConfig));
