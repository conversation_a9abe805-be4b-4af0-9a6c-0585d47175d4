// 暂时注释掉，直到生成了AI图片数据库的类型
// import { PrismaClient } from '../generated/ai-images';

// declare global {
//   // eslint-disable-next-line no-var
//   var __aiImagesPrisma: PrismaClient | undefined;
// }

// export const aiImagesPrisma =
//   globalThis.__aiImagesPrisma ??
//   new PrismaClient({
//     log:
//       process.env.NODE_ENV === 'development'
//         ? ['query', 'error', 'warn']
//         : ['error'],
//   });

// if (process.env.NODE_ENV !== 'production') {
//   globalThis.__aiImagesPrisma = aiImagesPrisma;
// }

// 临时导出，避免编译错误
export const aiImagesPrisma = null as any;
