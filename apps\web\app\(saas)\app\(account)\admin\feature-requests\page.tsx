"use client";

import { <PERSON><PERSON>, AlertDescription } from "@ui/components/alert";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	<PERSON>alogFooter,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@ui/components/dialog";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@ui/components/table";
import { Textarea } from "@ui/components/textarea";
import { cn } from "@ui/lib";
import {
	AlertCircle,
	Calendar,
	CheckCircle,
	Clock,
	Edit,
	Eye,
	Loader2,
	Plus,
	Refresh<PERSON><PERSON>,
	Trash,
	X,
	Zap,
} from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

interface Product {
	id: string;
	name: string;
	description?: string;
	createdAt: string;
	updatedAt: string;
}

interface User {
	id: string;
	name: string;
	email: string;
}

interface FeatureRequest {
	id: string;
	title: string;
	description: string;
	status: string;
	priority: string;
	productId: string;
	product: Product;
	user?: User;
	authorName?: string;
	authorEmail?: string;
	anonymousId?: string;
	createdAt: string;
	updatedAt: string;
	_count: {
		votes: number;
		comments: number;
		subscriptions: number;
	};
}

// 状态配置
const statusConfig = {
	under_consideration: {
		label: "考虑中",
		color: "bg-yellow-100 text-yellow-800 border-yellow-200",
		icon: Clock,
	},
	planned: {
		label: "已计划",
		color: "bg-blue-100 text-blue-800 border-blue-200",
		icon: Calendar,
	},
	in_progress: {
		label: "开发中",
		color: "bg-purple-100 text-purple-800 border-purple-200",
		icon: Zap,
	},
	completed: {
		label: "已完成",
		color: "bg-green-100 text-green-800 border-green-200",
		icon: CheckCircle,
	},
	wont_do: {
		label: "不会做",
		color: "bg-gray-100 text-gray-800 border-gray-200",
		icon: X,
	},
};

// 优先级配置
const priorityConfig = {
	low: { label: "低", color: "bg-gray-100 text-gray-800" },
	medium: { label: "中", color: "bg-blue-100 text-blue-800" },
	high: { label: "高", color: "bg-orange-100 text-orange-800" },
	urgent: { label: "紧急", color: "bg-red-100 text-red-800" },
};

export default function AdminFeatureRequestsPage() {
	const [featureRequests, setFeatureRequests] = useState<FeatureRequest[]>(
		[],
	);
	const [products, setProducts] = useState<Product[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [selectedProduct, setSelectedProduct] = useState("all");
	const [selectedStatus, setSelectedStatus] = useState("all");
	const [searchTerm, setSearchTerm] = useState("");

	// 编辑对话框状态
	const [editDialogOpen, setEditDialogOpen] = useState(false);
	const [editingRequest, setEditingRequest] = useState<FeatureRequest | null>(
		null,
	);
	const [editForm, setEditForm] = useState({
		title: "",
		description: "",
		status: "",
		priority: "",
		productId: "",
	});

	// 创建对话框状态
	const [createDialogOpen, setCreateDialogOpen] = useState(false);
	const [createForm, setCreateForm] = useState({
		title: "",
		description: "",
		status: "under_consideration",
		priority: "medium",
		productId: "",
		authorName: "",
		authorEmail: "",
	});

	// 详情对话框状态
	const [detailDialogOpen, setDetailDialogOpen] = useState(false);
	const [detailRequest, setDetailRequest] = useState<FeatureRequest | null>(
		null,
	);

	// 删除确认对话框状态
	const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
	const [deletingRequest, setDeletingRequest] =
		useState<FeatureRequest | null>(null);
	const [isDeleting, setIsDeleting] = useState(false);

	// 加载数据
	const loadData = async () => {
		setIsLoading(true);
		setError(null);

		try {
			// 加载产品列表
			const productsResponse = await fetch("/api/admin/products");
			if (productsResponse.ok) {
				const productsData = await productsResponse.json();
				if (productsData.success && productsData.data) {
					setProducts(productsData.data.products || []);
				} else {
					setProducts([]);
				}
			}

			// 加载特性请求
			const params = new URLSearchParams();
			if (selectedProduct !== "all") {
				params.append("productId", selectedProduct);
			}
			if (selectedStatus !== "all") {
				params.append("status", selectedStatus);
			}
			if (searchTerm) {
				params.append("search", searchTerm);
			}

			const featuresResponse = await fetch(
				`/api/admin/feature-requests?${params}`,
			);
			if (featuresResponse.ok) {
				const featuresData = await featuresResponse.json();
				if (featuresData.success && featuresData.data) {
					setFeatureRequests(featuresData.data.featureRequests || []);
				} else {
					setFeatureRequests([]);
				}
			} else {
				throw new Error("加载特性请求失败");
			}
		} catch (err: any) {
			console.error("加载数据失败:", err);
			setError(err?.message || "未知错误");
			toast.error("加载数据失败，请稍后重试");
		} finally {
			setIsLoading(false);
		}
	};

	// 打开编辑对话框
	const handleEdit = (request: FeatureRequest) => {
		setEditingRequest(request);
		setEditForm({
			title: request.title,
			description: request.description,
			status: request.status,
			priority: request.priority,
			productId: request.productId,
		});
		setEditDialogOpen(true);
	};

	// 处理编辑提交
	const handleEditSubmit = async () => {
		if (!editingRequest) return;

		try {
			const response = await fetch(
				`/api/admin/feature-requests/${editingRequest.id}`,
				{
					method: "PUT",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify(editForm),
				},
			);

			if (response.ok) {
				toast.success("特性请求更新成功");
				setEditDialogOpen(false);
				setEditingRequest(null);
				loadData();
			} else {
				throw new Error("更新失败");
			}
		} catch (err: any) {
			console.error("更新失败:", err);
			toast.error("更新失败，请稍后重试");
		}
	};

	// 处理创建提交
	const handleCreateSubmit = async () => {
		if (!createForm.title || !createForm.productId) {
			toast.error("请填写必填字段");
			return;
		}

		try {
			const response = await fetch("/api/admin/feature-requests", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(createForm),
			});

			if (response.ok) {
				toast.success("特性请求创建成功");
				setCreateDialogOpen(false);
				setCreateForm({
					title: "",
					description: "",
					status: "under_consideration",
					priority: "medium",
					productId: "",
					authorName: "",
					authorEmail: "",
				});
				loadData();
			} else {
				throw new Error("创建失败");
			}
		} catch (err: any) {
			console.error("创建失败:", err);
			toast.error("创建失败，请稍后重试");
		}
	};

	// 查看详情
	const handleViewDetail = async (request: FeatureRequest) => {
		try {
			const response = await fetch(
				`/api/admin/feature-requests/${request.id}`,
			);
			if (response.ok) {
				const data = await response.json();
				setDetailRequest(data.data);
				setDetailDialogOpen(true);
			} else {
				throw new Error("加载详情失败");
			}
		} catch (err: any) {
			console.error("加载详情失败:", err);
			toast.error("加载详情失败，请稍后重试");
		}
	};

	// 删除特性请求
	const handleDelete = async () => {
		if (!deletingRequest) return;

		setIsDeleting(true);
		try {
			const response = await fetch(
				`/api/admin/feature-requests/${deletingRequest.id}`,
				{
					method: "DELETE",
				},
			);

			if (response.ok) {
				toast.success("特性请求删除成功");
				setDeleteDialogOpen(false);
				setDeletingRequest(null);
				loadData();
			} else {
				throw new Error("删除失败");
			}
		} catch (err: any) {
			console.error("删除失败:", err);
			toast.error("删除失败，请稍后重试");
		} finally {
			setIsDeleting(false);
		}
	};

	// 初次加载和过滤条件变化时重新加载
	useEffect(() => {
		loadData();
	}, [selectedProduct, selectedStatus, searchTerm]);

	if (isLoading) {
		return (
			<div className="container mx-auto py-8 px-4">
				<div className="flex items-center justify-center h-64">
					<Loader2 className="size-8 animate-spin" />
					<span className="ml-2">加载中...</span>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="container mx-auto py-8 px-4">
				<Alert>
					<AlertCircle className="size-4" />
					<AlertDescription>{error}</AlertDescription>
				</Alert>
			</div>
		);
	}

	return (
		<div className="container mx-auto py-8 px-4">
			{/* 页面头部 */}
			<div className="flex justify-between items-center mb-6">
				<div>
					<h1 className="text-3xl font-bold text-foreground">
						特性请求管理
					</h1>
					<p className="text-muted-foreground">
						管理用户提交的特性请求
					</p>
				</div>
				<div className="flex gap-2">
					<Button onClick={loadData} variant="outline" size="sm">
						<RefreshCw className="size-4 mr-2" />
						刷新
					</Button>
					<Dialog
						open={createDialogOpen}
						onOpenChange={setCreateDialogOpen}
					>
						<DialogTrigger asChild>
							<Button>
								<Plus className="size-4 mr-2" />
								创建特性请求
							</Button>
						</DialogTrigger>
						<DialogContent className="max-w-2xl">
							<DialogHeader>
								<DialogTitle>创建特性请求</DialogTitle>
								<DialogDescription>
									创建一个新的特性请求
								</DialogDescription>
							</DialogHeader>
							<div className="grid gap-4 py-4">
								<div className="grid grid-cols-4 items-center gap-4">
									<Label
										htmlFor="create-title"
										className="text-right"
									>
										标题 *
									</Label>
									<Input
										id="create-title"
										value={createForm.title}
										onChange={(e) =>
											setCreateForm({
												...createForm,
												title: e.target.value,
											})
										}
										className="col-span-3"
									/>
								</div>
								<div className="grid grid-cols-4 items-start gap-4">
									<Label
										htmlFor="create-description"
										className="text-right pt-2"
									>
										描述
									</Label>
									<Textarea
										id="create-description"
										value={createForm.description}
										onChange={(e) =>
											setCreateForm({
												...createForm,
												description: e.target.value,
											})
										}
										className="col-span-3"
										rows={3}
									/>
								</div>
								<div className="grid grid-cols-4 items-center gap-4">
									<Label
										htmlFor="create-product"
										className="text-right"
									>
										产品 *
									</Label>
									<Select
										value={createForm.productId}
										onValueChange={(value) =>
											setCreateForm({
												...createForm,
												productId: value,
											})
										}
									>
										<SelectTrigger className="col-span-3">
											<SelectValue placeholder="选择产品" />
										</SelectTrigger>
										<SelectContent>
											{products.map((product) => (
												<SelectItem
													key={product.id}
													value={product.id}
												>
													{product.name}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
								</div>
								<div className="grid grid-cols-4 items-center gap-4">
									<Label
										htmlFor="create-status"
										className="text-right"
									>
										状态
									</Label>
									<Select
										value={createForm.status}
										onValueChange={(value) =>
											setCreateForm({
												...createForm,
												status: value,
											})
										}
									>
										<SelectTrigger className="col-span-3">
											<SelectValue />
										</SelectTrigger>
										<SelectContent>
											{Object.entries(statusConfig).map(
												([key, config]) => (
													<SelectItem
														key={key}
														value={key}
													>
														{config.label}
													</SelectItem>
												),
											)}
										</SelectContent>
									</Select>
								</div>
								<div className="grid grid-cols-4 items-center gap-4">
									<Label
										htmlFor="create-priority"
										className="text-right"
									>
										优先级
									</Label>
									<Select
										value={createForm.priority}
										onValueChange={(value) =>
											setCreateForm({
												...createForm,
												priority: value,
											})
										}
									>
										<SelectTrigger className="col-span-3">
											<SelectValue />
										</SelectTrigger>
										<SelectContent>
											{Object.entries(priorityConfig).map(
												([key, config]) => (
													<SelectItem
														key={key}
														value={key}
													>
														{config.label}
													</SelectItem>
												),
											)}
										</SelectContent>
									</Select>
								</div>
								<div className="grid grid-cols-4 items-center gap-4">
									<Label
										htmlFor="create-author-name"
										className="text-right"
									>
										作者昵称
									</Label>
									<Input
										id="create-author-name"
										value={createForm.authorName}
										onChange={(e) =>
											setCreateForm({
												...createForm,
												authorName: e.target.value,
											})
										}
										className="col-span-3"
										placeholder="匿名用户昵称"
									/>
								</div>
								<div className="grid grid-cols-4 items-center gap-4">
									<Label
										htmlFor="create-author-email"
										className="text-right"
									>
										作者邮箱
									</Label>
									<Input
										id="create-author-email"
										type="email"
										value={createForm.authorEmail}
										onChange={(e) =>
											setCreateForm({
												...createForm,
												authorEmail: e.target.value,
											})
										}
										className="col-span-3"
										placeholder="匿名用户邮箱"
									/>
								</div>
							</div>
							<DialogFooter>
								<Button
									variant="outline"
									onClick={() => setCreateDialogOpen(false)}
								>
									取消
								</Button>
								<Button onClick={handleCreateSubmit}>
									创建
								</Button>
							</DialogFooter>
						</DialogContent>
					</Dialog>
				</div>
			</div>

			{/* 过滤器 */}
			<Card className="mb-6">
				<CardContent className="pt-6">
					<div className="flex gap-4 items-center">
						<div className="flex-1">
							<Input
								placeholder="搜索特性请求..."
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className="max-w-sm"
							/>
						</div>
						<Select
							value={selectedProduct}
							onValueChange={setSelectedProduct}
						>
							<SelectTrigger className="w-48">
								<SelectValue placeholder="选择产品" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">所有产品</SelectItem>
								{products.map((product) => (
									<SelectItem
										key={product.id}
										value={product.id}
									>
										{product.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
						<Select
							value={selectedStatus}
							onValueChange={setSelectedStatus}
						>
							<SelectTrigger className="w-48">
								<SelectValue placeholder="选择状态" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="all">所有状态</SelectItem>
								{Object.entries(statusConfig).map(
									([key, config]) => (
										<SelectItem key={key} value={key}>
											{config.label}
										</SelectItem>
									),
								)}
							</SelectContent>
						</Select>
					</div>
				</CardContent>
			</Card>

			{/* 特性请求列表 */}
			<Card>
				<CardHeader>
					<CardTitle>
						特性请求列表 ({featureRequests.length})
					</CardTitle>
				</CardHeader>
				<CardContent>
					<Table>
						<TableHeader>
							<TableRow>
								<TableHead>标题</TableHead>
								<TableHead>产品</TableHead>
								<TableHead>状态</TableHead>
								<TableHead>优先级</TableHead>
								<TableHead>作者</TableHead>
								<TableHead>投票数</TableHead>
								<TableHead>创建时间</TableHead>
								<TableHead>操作</TableHead>
							</TableRow>
						</TableHeader>
						<TableBody>
							{featureRequests.map((request) => {
								const statusConf =
									statusConfig[
										request.status as keyof typeof statusConfig
									];
								const priorityConf =
									priorityConfig[
										request.priority as keyof typeof priorityConfig
									];
								const StatusIcon = statusConf?.icon;

								return (
									<TableRow key={request.id}>
										<TableCell className="font-medium">
											{request.title}
										</TableCell>
										<TableCell>
											{request.product.name}
										</TableCell>
										<TableCell>
											<Badge
												status="info"
												className={cn(
													"border",
													statusConf?.color,
												)}
											>
												{StatusIcon && (
													<StatusIcon className="size-3 mr-1" />
												)}
												{statusConf?.label}
											</Badge>
										</TableCell>
										<TableCell>
											<Badge
												status="info"
												className={cn(
													"border",
													priorityConf?.color,
												)}
											>
												{priorityConf?.label}
											</Badge>
										</TableCell>
										<TableCell>
											{request.user?.name ||
												request.authorName ||
												"匿名用户"}
										</TableCell>
										<TableCell>
											{request._count.votes}
										</TableCell>
										<TableCell>
											{new Date(
												request.createdAt,
											).toLocaleDateString()}
										</TableCell>
										<TableCell>
											<div className="flex gap-2">
												<Button
													variant="ghost"
													size="sm"
													onClick={() =>
														handleViewDetail(
															request,
														)
													}
												>
													<Eye className="size-4" />
												</Button>
												<Button
													variant="ghost"
													size="sm"
													onClick={() =>
														handleEdit(request)
													}
												>
													<Edit className="size-4" />
												</Button>
												<Button
													variant="ghost"
													size="sm"
													onClick={() => {
														setDeletingRequest(
															request,
														);
														setDeleteDialogOpen(
															true,
														);
													}}
												>
													<Trash className="size-4 text-red-500" />
												</Button>
											</div>
										</TableCell>
									</TableRow>
								);
							})}
						</TableBody>
					</Table>
					{featureRequests.length === 0 && (
						<div className="text-center py-8 text-muted-foreground">
							暂无特性请求
						</div>
					)}
				</CardContent>
			</Card>

			{/* 编辑对话框 */}
			<Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
				<DialogContent className="max-w-2xl">
					<DialogHeader>
						<DialogTitle>编辑特性请求</DialogTitle>
						<DialogDescription>
							编辑特性请求的详细信息
						</DialogDescription>
					</DialogHeader>
					<div className="grid gap-4 py-4">
						<div className="grid grid-cols-4 items-center gap-4">
							<Label htmlFor="edit-title" className="text-right">
								标题
							</Label>
							<Input
								id="edit-title"
								value={editForm.title}
								onChange={(e) =>
									setEditForm({
										...editForm,
										title: e.target.value,
									})
								}
								className="col-span-3"
							/>
						</div>
						<div className="grid grid-cols-4 items-start gap-4">
							<Label
								htmlFor="edit-description"
								className="text-right pt-2"
							>
								描述
							</Label>
							<Textarea
								id="edit-description"
								value={editForm.description}
								onChange={(e) =>
									setEditForm({
										...editForm,
										description: e.target.value,
									})
								}
								className="col-span-3"
								rows={3}
							/>
						</div>
						<div className="grid grid-cols-4 items-center gap-4">
							<Label
								htmlFor="edit-product"
								className="text-right"
							>
								产品
							</Label>
							<Select
								value={editForm.productId}
								onValueChange={(value) =>
									setEditForm({
										...editForm,
										productId: value,
									})
								}
							>
								<SelectTrigger className="col-span-3">
									<SelectValue />
								</SelectTrigger>
								<SelectContent>
									{products.map((product) => (
										<SelectItem
											key={product.id}
											value={product.id}
										>
											{product.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
						<div className="grid grid-cols-4 items-center gap-4">
							<Label htmlFor="edit-status" className="text-right">
								状态
							</Label>
							<Select
								value={editForm.status}
								onValueChange={(value) =>
									setEditForm({ ...editForm, status: value })
								}
							>
								<SelectTrigger className="col-span-3">
									<SelectValue />
								</SelectTrigger>
								<SelectContent>
									{Object.entries(statusConfig).map(
										([key, config]) => (
											<SelectItem key={key} value={key}>
												{config.label}
											</SelectItem>
										),
									)}
								</SelectContent>
							</Select>
						</div>
						<div className="grid grid-cols-4 items-center gap-4">
							<Label
								htmlFor="edit-priority"
								className="text-right"
							>
								优先级
							</Label>
							<Select
								value={editForm.priority}
								onValueChange={(value) =>
									setEditForm({
										...editForm,
										priority: value,
									})
								}
							>
								<SelectTrigger className="col-span-3">
									<SelectValue />
								</SelectTrigger>
								<SelectContent>
									{Object.entries(priorityConfig).map(
										([key, config]) => (
											<SelectItem key={key} value={key}>
												{config.label}
											</SelectItem>
										),
									)}
								</SelectContent>
							</Select>
						</div>
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setEditDialogOpen(false)}
						>
							取消
						</Button>
						<Button onClick={handleEditSubmit}>保存</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* 详情对话框 */}
			<Dialog open={detailDialogOpen} onOpenChange={setDetailDialogOpen}>
				<DialogContent className="max-w-4xl">
					<DialogHeader>
						<DialogTitle>特性请求详情</DialogTitle>
					</DialogHeader>
					{detailRequest && (
						<div className="grid gap-6 py-4">
							<div className="grid grid-cols-2 gap-4">
								<div>
									<h3 className="font-semibold mb-2">
										基本信息
									</h3>
									<div className="space-y-2 text-sm">
										<div>
											<span className="font-medium">
												标题:
											</span>{" "}
											{detailRequest.title}
										</div>
										<div>
											<span className="font-medium">
												产品:
											</span>{" "}
											{detailRequest.product.name}
										</div>
										<div className="flex items-center gap-2">
											<span className="font-medium">
												状态:
											</span>
											<Badge
												status="info"
												className={cn(
													"border",
													statusConfig[
														detailRequest.status as keyof typeof statusConfig
													]?.color,
												)}
											>
												{
													statusConfig[
														detailRequest.status as keyof typeof statusConfig
													]?.label
												}
											</Badge>
										</div>
										<div className="flex items-center gap-2">
											<span className="font-medium">
												优先级:
											</span>
											<Badge
												status="info"
												className={cn(
													"border",
													priorityConfig[
														detailRequest.priority as keyof typeof priorityConfig
													]?.color,
												)}
											>
												{
													priorityConfig[
														detailRequest.priority as keyof typeof priorityConfig
													]?.label
												}
											</Badge>
										</div>
									</div>
								</div>
								<div>
									<h3 className="font-semibold mb-2">
										作者信息
									</h3>
									<div className="space-y-2 text-sm">
										<div>
											<span className="font-medium">
												作者:
											</span>{" "}
											{detailRequest.user?.name ||
												detailRequest.authorName ||
												"匿名用户"}
										</div>
										{detailRequest.user?.email && (
											<div>
												<span className="font-medium">
													邮箱:
												</span>{" "}
												{detailRequest.user.email}
											</div>
										)}
										{detailRequest.authorEmail && (
											<div>
												<span className="font-medium">
													邮箱:
												</span>{" "}
												{detailRequest.authorEmail}
											</div>
										)}
										<div>
											<span className="font-medium">
												创建时间:
											</span>{" "}
											{new Date(
												detailRequest.createdAt,
											).toLocaleString()}
										</div>
									</div>
								</div>
							</div>
							<div>
								<h3 className="font-semibold mb-2">描述</h3>
								<p className="text-sm text-muted-foreground whitespace-pre-wrap">
									{detailRequest.description || "无描述"}
								</p>
							</div>
							<div className="grid grid-cols-3 gap-4">
								<div className="text-center">
									<div className="text-2xl font-bold text-blue-600">
										{detailRequest._count.votes}
									</div>
									<div className="text-sm text-muted-foreground">
										投票数
									</div>
								</div>
								<div className="text-center">
									<div className="text-2xl font-bold text-green-600">
										{detailRequest._count.comments}
									</div>
									<div className="text-sm text-muted-foreground">
										评论数
									</div>
								</div>
								<div className="text-center">
									<div className="text-2xl font-bold text-purple-600">
										{detailRequest._count.subscriptions}
									</div>
									<div className="text-sm text-muted-foreground">
										订阅数
									</div>
								</div>
							</div>
						</div>
					)}
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setDetailDialogOpen(false)}
						>
							关闭
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* 删除确认对话框 */}
			<Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>确认删除</DialogTitle>
						<DialogDescription>
							确定要删除特性请求 "{deletingRequest?.title}"？
							此操作无法撤销。
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setDeleteDialogOpen(false)}
							disabled={isDeleting}
						>
							取消
						</Button>
						<Button
							variant="error"
							onClick={handleDelete}
							disabled={isDeleting}
						>
							{isDeleting && (
								<Loader2 className="size-4 mr-2 animate-spin" />
							)}
							删除
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
}
