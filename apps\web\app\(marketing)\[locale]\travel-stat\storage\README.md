# 旅行足迹数据存储模块

这个模块提供了一个抽象的数据存储层，支持统一的数据管理和未来的存储方式切换。

## 🏗️ 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    业务层 (Hooks)                           │
├─────────────────────────────────────────────────────────────┤
│                   数据管理器                                │
│              (TravelFootprintDataManager)                   │
├─────────────────────────────────────────────────────────────┤
│                   存储抽象层                                │
│                 (IDataStorage)                              │
├─────────────────────────────────────────────────────────────┤
│                   存储提供者                                │
│   LocalStorageProvider │ DatabaseProvider │ CloudProvider  │
└─────────────────────────────────────────────────────────────┘
```

## 📊 数据结构

所有数据统一存储在一个 `TravelFootprintData` 对象中：

```typescript
interface TravelFootprintData {
  version: string;           // 数据版本
  lastUpdated: string;       // 最后更新时间
  
  travel: {                  // 旅行数据
    points: TravelPoint[];   // 旅行点
    countries: CountryData[]; // 访问过的国家
  };
  
  mapConfig: {               // 地图配置
    atmosphereTheme: AtmosphereTheme;
    mapStyle: MapStyleType;
    mapProjection: MapProjectionType;
    animationTheme: AnimationTheme;
    viewState: ViewState;
  };
  
  styleConfig: {             // 样式配置
    colorTheme: ColorThemeType;
    markerStyle: MarkerStyleType;
    markerTheme: string;
    currentEmoji: string;
    currentEmojiColor: string;
    hideOutline: boolean;
  };
  
  cardConfig: {              // 卡片生成配置 🆕
    selectedPlatform: SocialPlatform;        // 选择的社交平台
    selectedTemplateId: string;              // 选择的模板ID
    customization: CardCustomization;        // 卡片自定义配置
    userProfile: {                           // 用户信息
      username?: string;
      avatar?: string;
      bio?: string;
    };
    exportSettings: {                        // 导出设置
      quality: "low" | "medium" | "high";
      includeWatermark: boolean;
      customWatermark?: string;
    };
  };
  
  preferences: {             // 用户偏好
    language?: string;
    enableAnimations?: boolean;
    autoSaveInterval?: number;
    defaultPageMode?: "editing" | "card-generation"; // 🆕
  };
}
```

### 🎨 卡片自定义配置详解

```typescript
interface CardCustomization {
  colors?: {                 // 颜色自定义
    primary?: string;        // 主色调
    secondary?: string;      // 次要颜色
    accent?: string;         // 强调色
    background?: string;     // 背景色
    text?: string;          // 文字颜色
  };
  
  typography?: {             // 字体自定义
    fontFamily?: string;     // 字体族
    headerSize?: number;     // 标题大小
    bodySize?: number;       // 正文大小
    titleWeight?: number;    // 标题字重
  };
  
  layout?: {                 // 布局自定义
    padding?: number;        // 内边距
    spacing?: number;        // 间距
    borderRadius?: number;   // 圆角
    showShadow?: boolean;    // 是否显示阴影
  };
  
  content?: {                // 内容自定义
    showDate?: boolean;      // 显示日期
    showUserInfo?: boolean;  // 显示用户信息
    showDetailedStats?: boolean; // 显示详细统计
    customTitle?: string;    // 自定义标题
    customFooter?: string;   // 自定义页脚
  };
}
```

## 🚀 快速开始

### 1. 使用统一数据管理器 Hook

```typescript
import { useDataManager } from '../hooks/useDataManager';

function MyComponent() {
  const {
    // 状态
    isLoading,
    error,
    
    // 旅行数据
    travelPoints,
    visitedCountries,
    
    // 地图配置
    atmosphereTheme,
    colorTheme,
    
    // 🆕 卡片配置
    selectedPlatform,
    selectedTemplateId,
    cardCustomization,
    userProfile,
    exportSettings,
    
    // 操作方法
    addTravelPoint,
    updateAtmosphereTheme,
    updateColorTheme,
    
    // 🆕 卡片操作方法
    updateSelectedPlatform,
    updateSelectedTemplateId,
    updateCardCustomization,
    updateUserProfile,
    updateExportSettings,
    
    // 通用操作
    clearAllData,
    exportData,
  } = useDataManager();

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <p>已访问 {travelPoints.length} 个地点</p>
      <p>当前主题: {atmosphereTheme}</p>
      <p>选择平台: {selectedPlatform}</p>
      
      <button onClick={() => updateAtmosphereTheme('night')}>
        切换夜晚主题
      </button>
      
      <button onClick={() => updateSelectedPlatform('wechat')}>
        切换到微信平台
      </button>
      
      <button onClick={() => updateCardCustomization({
        colors: { primary: '#ff6b6b' }
      })}>
        更新卡片颜色
      </button>
      
      <button onClick={() => clearAllData()}>
        清空数据
      </button>
    </div>
  );
}
```

### 2. 卡片配置操作示例

```typescript
// 更新社交平台
await updateSelectedPlatform('instagram');

// 更新模板
await updateSelectedTemplateId('modern-travel-card');

// 更新卡片自定义配置
await updateCardCustomization({
  colors: {
    primary: '#3b82f6',
    background: '#ffffff'
  },
  content: {
    customTitle: '我的2024旅行足迹',
    showDetailedStats: true
  }
});

// 更新用户资料
await updateUserProfile({
  username: '旅行达人',
  bio: '探索世界的每一个角落'
});

// 更新导出设置
await updateExportSettings({
  quality: 'high',
  includeWatermark: true,
  customWatermark: '© 我的旅行记录'
});
```

### 3. 完整的卡片配置流程

```typescript
function CardConfigurationExample() {
  const {
    selectedPlatform,
    cardCustomization,
    updateSelectedPlatform,
    updateCardCustomization,
    updateUserProfile,
  } = useDataManager();

  // 1. 选择平台
  const handlePlatformChange = (platform: SocialPlatform) => {
    updateSelectedPlatform(platform);
  };

  // 2. 自定义颜色
  const handleColorChange = (colors: Partial<CardCustomization['colors']>) => {
    updateCardCustomization({ colors });
  };

  // 3. 自定义内容
  const handleContentChange = (content: Partial<CardCustomization['content']>) => {
    updateCardCustomization({ content });
  };

  return (
    <div>
      {/* 平台选择 */}
      <select 
        value={selectedPlatform} 
        onChange={(e) => handlePlatformChange(e.target.value as SocialPlatform)}
      >
        <option value="instagram">Instagram</option>
        <option value="wechat">微信</option>
        <option value="weibo">微博</option>
      </select>

      {/* 颜色自定义 */}
      <input 
        type="color" 
        value={cardCustomization?.colors?.primary || '#3b82f6'}
        onChange={(e) => handleColorChange({ primary: e.target.value })}
      />

      {/* 标题自定义 */}
      <input 
        type="text"
        value={cardCustomization?.content?.customTitle || ''}
        onChange={(e) => handleContentChange({ customTitle: e.target.value })}
        placeholder="自定义标题"
      />
    </div>
  );
}
```

## 🔧 扩展新的存储提供者

### 1. 实现存储接口

```typescript
import { IDataStorage } from '../interfaces';

export class DatabaseProvider implements IDataStorage {
  async get<T>(key: string): Promise<T | null> {
    // 实现数据库读取逻辑
  }
  
  async set<T>(key: string, value: T): Promise<void> {
    // 实现数据库写入逻辑
  }
  
  // ... 其他方法
}
```

### 2. 使用新的存储提供者

```typescript
import { TravelFootprintDataManager } from '../storage/DataManager';
import { DatabaseProvider } from './DatabaseProvider';

const dataManager = new TravelFootprintDataManager(
  new DatabaseProvider({
    connectionString: 'your-db-connection'
  })
);
```

## 📱 迁移现有代码

### 之前的分散存储方式

```typescript
// 旧代码 - 分散在多个 hooks 中
const { travelPoints, addTravelPoint } = useTravelData();
const { atmosphereTheme, setAtmosphereTheme } = useMapControl();
const { colorTheme, changeTheme } = useColorTheme();

// 卡片状态分散管理
const [selectedPlatform, setSelectedPlatform] = useState('instagram');
const [customization, setCustomization] = useState({...});
```

### 现在的统一方式

```typescript
// 新代码 - 统一管理
const {
  // 旅行数据
  travelPoints,
  addTravelPoint,
  
  // 地图配置
  atmosphereTheme,
  updateAtmosphereTheme,
  colorTheme,
  updateColorTheme,
  
  // 🆕 卡片配置
  selectedPlatform,
  cardCustomization,
  updateSelectedPlatform,
  updateCardCustomization,
} = useDataManager();
```

## 🔄 数据迁移

模块自动处理旧数据的迁移：

1. **自动检测**: 启动时检查是否存在旧版本数据
2. **格式转换**: 将分散的 localStorage 数据合并到统一格式
3. **版本升级**: 支持数据结构版本升级
4. **清理工作**: 迁移完成后清理旧数据
5. **🆕 卡片配置**: 自动为卡片配置设置默认值

## 🛡️ 错误处理

```typescript
const { error, clearError } = useDataManager();

if (error) {
  console.error('数据操作失败:', error);
  clearError(); // 清除错误状态
}
```

## 📈 性能优化

- **自动保存**: 配置的间隔时间自动保存数据
- **批量操作**: 支持批量更新配置，减少存储次数
- **懒加载**: 数据管理器采用懒初始化模式
- **内存缓存**: 数据在内存中缓存，减少存储访问
- **🆕 增量更新**: 卡片配置支持增量更新，避免整体替换

## 🎨 卡片配置最佳实践

1. **模板管理**: 通过 `selectedTemplateId` 存储模板ID，而不是整个模板对象
2. **增量更新**: 使用 `updateCardCustomization` 进行增量更新，保持其他配置不变
3. **平台适配**: 根据 `selectedPlatform` 自动调整卡片尺寸和样式
4. **用户体验**: 实时保存用户的自定义配置，避免数据丢失

## 🔧 配置选项

```typescript
const dataManager = new TravelFootprintDataManager(
  new LocalStorageProvider({
    keyPrefix: 'my-app:',           // 存储键前缀
    enableCompression: true,        // 启用压缩
    errorStrategy: 'log',           // 错误处理策略
  })
);
```

## 🚀 未来扩展

这个架构设计支持：

- **云存储**: 集成云端数据同步
- **多设备同步**: 跨设备数据共享
- **离线支持**: 离线模式下的数据管理
- **数据备份**: 自动备份和恢复机制
- **性能监控**: 数据操作性能统计
- **🆕 卡片模板**: 支持自定义卡片模板的导入导出
- **🆕 协作功能**: 支持多用户卡片配置协作

## 📝 最佳实践

1. **统一入口**: 使用 `useDataManager` 作为数据访问的唯一入口
2. **错误处理**: 始终检查操作结果和错误状态
3. **类型安全**: 充分利用 TypeScript 类型检查
4. **避免直接访问**: 不要直接操作 localStorage，使用抽象层
5. **测试友好**: 可以轻松注入 mock 存储提供者进行测试
6. **🆕 配置验证**: 在更新卡片配置前进行数据验证
7. **🆕 性能考虑**: 避免频繁更新复杂的自定义配置，考虑防抖 