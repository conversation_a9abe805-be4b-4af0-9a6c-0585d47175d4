/**
 * Feature Voting 测试数据
 * 用于初始化测试环境的数据
 */

import { db } from "@repo/database";

/**
 * 初始化测试产品数据
 */
export async function initializeTestProducts() {
	try {
		// 检查是否已存在产品
		const existingProducts = await db.product.findMany();

		if (existingProducts.length > 0) {
			console.log("产品数据已存在，跳过初始化");
			return existingProducts;
		}

		// 创建测试产品
		const products = await Promise.all([
			db.product.create({
				data: {
					id: "travel-memo",
					name: "旅行足迹",
					description: "记录和分享你的旅行回忆，在地图上标记足迹",
				},
			}),
			db.product.create({
				data: {
					id: "ai-generator",
					name: "AI图片生成器",
					description: "使用AI技术生成精美的图片和艺术作品",
				},
			}),
			db.product.create({
				data: {
					id: "feature-voting",
					name: "特性投票系统",
					description: "收集用户反馈，为产品功能投票",
				},
			}),
		]);

		console.log("测试产品创建成功:", products);
		return products;
	} catch (error) {
		console.error("创建测试产品失败:", error);
		throw error;
	}
}

/**
 * 初始化测试特性请求数据
 */
export async function initializeTestFeatureRequests() {
	try {
		// 确保产品存在
		await initializeTestProducts();

		// 检查是否已存在特性请求
		const existingRequests = await db.featureRequest.findMany();

		if (existingRequests.length > 0) {
			console.log("特性请求数据已存在，跳过初始化");
			return existingRequests;
		}

		// 创建测试特性请求
		const featureRequests = await Promise.all([
			db.featureRequest.create({
				data: {
					title: "支持离线地图功能",
					description:
						"希望能够下载离线地图，在没有网络的情况下也能查看和编辑旅行记录。这对于去偏远地区旅行的用户非常重要。",
					status: "under_consideration",
					productId: "travel-memo",
					authorName: "旅行爱好者",
					authorEmail: "<EMAIL>",
				},
			}),
			db.featureRequest.create({
				data: {
					title: "添加旅行统计功能",
					description:
						"希望能看到自己的旅行统计，比如去过多少个国家、城市，总旅行距离等。可以生成年度旅行报告。",
					status: "planned",
					productId: "travel-memo",
					authorName: "数据控",
					authorEmail: "<EMAIL>",
				},
			}),
			db.featureRequest.create({
				data: {
					title: "支持更多AI绘画风格",
					description:
						"希望能够支持更多的绘画风格，比如水彩、油画、素描等。现在的风格选择还比较有限。",
					status: "in_progress",
					productId: "ai-generator",
					authorName: "艺术家",
					authorEmail: "<EMAIL>",
				},
			}),
			db.featureRequest.create({
				data: {
					title: "批量生成图片功能",
					description:
						"希望能够一次性生成多张图片，而不是每次只能生成一张。这样可以提高工作效率。",
					status: "completed",
					productId: "ai-generator",
					authorName: "设计师",
					authorEmail: "<EMAIL>",
				},
			}),
			db.featureRequest.create({
				data: {
					title: "添加评论回复功能",
					description:
						"希望在特性投票系统中能够回复其他用户的评论，形成讨论串。这样可以更好地交流想法。",
					status: "under_consideration",
					productId: "feature-voting",
					authorName: "产品经理",
					authorEmail: "<EMAIL>",
				},
			}),
		]);

		console.log("测试特性请求创建成功:", featureRequests);
		return featureRequests;
	} catch (error) {
		console.error("创建测试特性请求失败:", error);
		throw error;
	}
}

/**
 * 初始化测试投票数据
 */
export async function initializeTestVotes() {
	try {
		// 确保特性请求存在
		const featureRequests = await initializeTestFeatureRequests();

		// 为每个特性请求创建一些测试投票
		const votes = [];

		for (const request of featureRequests) {
			// 为每个特性请求创建3-8个随机投票
			const voteCount = Math.floor(Math.random() * 6) + 3;

			for (let i = 0; i < voteCount; i++) {
				const vote = await db.featureVote.create({
					data: {
						featureRequestId: request.id,
						anonymousId: `test_voter_${request.id}_${i}`,
						voterName: `测试用户${i + 1}`,
						voterEmail: `test${i + 1}@example.com`,
						ipAddress: `192.168.1.${i + 1}`,
					},
				});
				votes.push(vote);
			}
		}

		console.log("测试投票创建成功:", votes.length, "个投票");
		return votes;
	} catch (error) {
		console.error("创建测试投票失败:", error);
		throw error;
	}
}

/**
 * 初始化所有测试数据
 */
export async function initializeAllTestData() {
	console.log("开始初始化Feature Voting测试数据...");

	try {
		await initializeTestProducts();
		await initializeTestFeatureRequests();
		await initializeTestVotes();

		console.log("所有测试数据初始化完成！");
	} catch (error) {
		console.error("测试数据初始化失败:", error);
		throw error;
	}
}

/**
 * 清理测试数据
 */
export async function cleanupTestData() {
	try {
		console.log("开始清理测试数据...");

		// 按依赖关系顺序删除
		await db.featureVote.deleteMany();
		await db.featureComment.deleteMany();
		await db.featureSubscription.deleteMany();
		await db.featureRequest.deleteMany();
		await db.product.deleteMany();

		console.log("测试数据清理完成！");
	} catch (error) {
		console.error("清理测试数据失败:", error);
		throw error;
	}
}
