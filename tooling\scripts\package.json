{"dependencies": {"@repo/auth": "workspace:*", "@repo/database": "workspace:*", "@repo/logs": "workspace:*", "@repo/utils": "workspace:*", "csv-writer": "^1.6.0", "dotenv-cli": "^7.4.2", "google-play-scraper": "^10.0.1", "puppeteer": "^21.6.1"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/node": "^22.14.0", "nanoid": "^5.1.2", "tsx": "^4.19.3"}, "name": "travel-memo-scripts", "private": true, "scripts": {"create:user": "dotenv -c -e ../../.env -- tsx ./src/create-user.ts", "google-play-scraper": "dotenv -c -e ../../.env -- tsx ./src/google-play-scraper-lib.ts", "google-play-crawler-final": "dotenv -c -e ../../.env -- tsx ./src/google-play-crawler-final.ts", "crawler": "dotenv -c -e ../../.env -- tsx ./src/google-play-scraper-lib.ts", "type-check": "tsc --noEmit", "scrape:polarsteps": "node -r dotenv/config src/google-play-scraper-lib.ts", "scrape:polarsteps:browser": "node -r dotenv/config src/google-play-crawler-final.ts", "scrape:custom": "node -r dotenv/config src/google-play-scraper-lib.ts", "analyze:reviews": "python src/review-analyzer-simple.py", "analyze:install": "pip install pandas mat<PERSON><PERSON><PERSON>b numpy"}, "version": "1.0.0", "description": "Scripts for travel memo project"}