import type { TravelStatTranslations } from "../../hooks/useTravelStatTranslations";
import { SeoFaq } from "./SeoFaq";
import { SeoFeaturesGrid } from "./SeoFeaturesGrid";
import { SeoHeroSection } from "./SeoHeroSection";
import { SeoKeywords } from "./SeoKeywords";
import { SeoUsageGuide } from "./SeoUsageGuide";

interface SeoContentSectionProps {
	translations: TravelStatTranslations;
}

export function SeoContentSection({ translations }: SeoContentSectionProps) {
	return (
		<div className="relative container mx-auto px-4 py-16 mt-12">
			{/* 背景装饰 */}
			<div className="absolute inset-0 overflow-hidden pointer-events-none">
				<div className="absolute top-10 left-1/5 w-32 h-32 bg-blue-100/20 dark:bg-blue-800/10 rounded-full blur-2xl animate-pulse" />
				<div className="absolute bottom-10 right-1/5 w-24 h-24 bg-purple-100/20 dark:bg-purple-800/10 rounded-full blur-2xl animate-pulse delay-1000" />
			</div>

			<div className="max-w-4xl mx-auto space-y-12">
				{/* 主要介绍区域 */}
				<SeoHeroSection translations={translations} />

				{/* 功能特色网格 */}
				<SeoFeaturesGrid translations={translations} />

				{/* 使用指南区域 */}
				<SeoUsageGuide translations={translations} />

				{/* FAQ 区域 */}
				<SeoFaq translations={translations} />

				{/* 关键词和标签区域 */}
				<SeoKeywords translations={translations} />
			</div>
		</div>
	);
}
