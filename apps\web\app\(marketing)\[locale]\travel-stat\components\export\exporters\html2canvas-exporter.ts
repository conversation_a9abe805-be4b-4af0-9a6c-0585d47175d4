import type { ExportContext, ExportOptions } from "../types";

/**
 * 高质量地图导出方法
 * 使用 html2canvas-pro 完整捕获地图容器，包括：
 * - Mapbox GL 地图画布
 * - 自定义背景动画
 * - 自定义点位样式
 * - UI 控件和覆盖层
 */
export async function exportMap(
	options: ExportOptions,
	context: ExportContext,
): Promise<HTMLCanvasElement> {
	const { mapRef, setCurrentMethod } = context;

	setCurrentMethod("正在准备导出...");
	console.log("🚀 [导出调试] 开始导出地图", { options, mapRef });

	if (!mapRef?.current) {
		console.error("❌ [导出调试] 地图引用无效", mapRef);
		throw new Error("地图引用无效");
	}

	console.log("✅ [导出调试] 地图引用有效", {
		current: mapRef.current,
		type: typeof mapRef.current,
		constructor: mapRef.current?.constructor?.name,
	});

	// 动态导入 html2canvas-pro
	const html2canvas = (await import("html2canvas-pro")).default;

	// 按照工作代码的方式获取地图容器
	// 兼容不同的 ref 结构
	let map: any;
	if (mapRef.current?.getMap) {
		// react-map-gl ref 结构
		map = mapRef.current.getMap();
	} else if ((mapRef as any).reactMapGLRef?.getMap) {
		// 我们保存的双ref结构 - 使用react-map-gl ref
		map = (mapRef as any).reactMapGLRef.getMap();
	} else {
		// 直接就是mapbox实例
		map = mapRef.current;
	}

	const mapContainer = map.getContainer();

	console.log("🔍 [导出调试] 开始获取地图容器");
	console.log("地图实例:", map);
	console.log("地图容器元素:", mapContainer);

	if (!mapContainer) {
		console.error("❌ [导出调试] 无法获取地图容器元素");
		throw new Error("无法获取地图容器元素");
	}

	// Debug: 打印容器信息 (参考工作代码)
	console.group("🗺️ HTML2Canvas Debug - 容器信息");

	// 获取容器的各种尺寸
	const containerRect = mapContainer.getBoundingClientRect();
	const containerStyles = window.getComputedStyle(mapContainer);

	console.log("容器 getBoundingClientRect():", {
		width: containerRect.width,
		height: containerRect.height,
		left: containerRect.left,
		top: containerRect.top,
		right: containerRect.right,
		bottom: containerRect.bottom,
	});

	console.log("容器 offset 尺寸:", {
		offsetWidth: mapContainer.offsetWidth,
		offsetHeight: mapContainer.offsetHeight,
		offsetLeft: mapContainer.offsetLeft,
		offsetTop: mapContainer.offsetTop,
	});

	console.log("容器 client 尺寸:", {
		clientWidth: mapContainer.clientWidth,
		clientHeight: mapContainer.clientHeight,
	});

	console.log("容器 scroll 尺寸:", {
		scrollWidth: mapContainer.scrollWidth,
		scrollHeight: mapContainer.scrollHeight,
		scrollLeft: mapContainer.scrollLeft,
		scrollTop: mapContainer.scrollTop,
	});

	console.log("容器 computed styles:", {
		width: containerStyles.width,
		height: containerStyles.height,
		overflow: containerStyles.overflow,
		position: containerStyles.position,
		transform: containerStyles.transform,
	});

	// 获取地图 canvas 信息
	const mapCanvas = map.getCanvas();
	console.log("地图 Canvas 尺寸:", {
		canvasWidth: mapCanvas.width,
		canvasHeight: mapCanvas.height,
		canvasClientWidth: mapCanvas.clientWidth,
		canvasClientHeight: mapCanvas.clientHeight,
		canvasOffsetWidth: mapCanvas.offsetWidth,
		canvasOffsetHeight: mapCanvas.offsetHeight,
	});

	// 检查父容器
	const parentElement = mapContainer.parentElement;
	if (parentElement) {
		const parentRect = parentElement.getBoundingClientRect();
		const parentStyles = window.getComputedStyle(parentElement);
		console.log("父容器信息:", {
			parentRect: {
				width: parentRect.width,
				height: parentRect.height,
			},
			parentOffset: {
				offsetWidth: parentElement.offsetWidth,
				offsetHeight: parentElement.offsetHeight,
			},
			parentStyles: {
				width: parentStyles.width,
				height: parentStyles.height,
				overflow: parentStyles.overflow,
				position: parentStyles.position,
			},
		});
	}
	console.groupEnd();

	setCurrentMethod("等待地图加载完成...");

	// 等待地图渲染和动画稳定
	await waitForMapStable(map);

	// Additional delay for any non-Mapbox CSS animations to settle
	console.log("⏳ [导出调试] 额外等待500ms确保动画稳定");
	await new Promise((r) => setTimeout(r, 500));

	setCurrentMethod("正在捕获地图...");

	// 重新获取尺寸（可能在等待过程中发生变化）
	const finalContainerRect = mapContainer.getBoundingClientRect();
	const captureWidth = Math.max(
		mapContainer.offsetWidth,
		mapContainer.clientWidth,
		mapContainer.scrollWidth,
		Math.floor(finalContainerRect.width),
	);
	const captureHeight = Math.max(
		mapContainer.offsetHeight,
		mapContainer.clientHeight,
		mapContainer.scrollHeight,
		Math.floor(finalContainerRect.height),
	);

	console.group("🎯 HTML2Canvas Debug - 捕获参数");
	console.log("最终捕获尺寸:", {
		captureWidth,
		captureHeight,
		scale: options.scale,
		finalWidth: captureWidth * options.scale,
		finalHeight: captureHeight * options.scale,
	});
	console.groupEnd();

	try {
		// 使用 html2canvas-pro 进行高质量完整捕获
		console.log("📸 [导出调试] 开始 html2canvas 捕获", {
			targetContainer: mapContainer,
			containerRect: mapContainer.getBoundingClientRect(),
			options: {
				...options,
				width: mapContainer.offsetWidth,
				height: mapContainer.offsetHeight,
			},
		});

		// 使用 HTML2Canvas 捕获整个地图区域 (参考工作代码配置)
		const html2canvasOptions = {
			useCORS: true,
			allowTaint: false,
			backgroundColor: null, // Preserve map's own background or transparency
			scale: options.scale,
			width: captureWidth, // 使用计算出的最大宽度
			height: captureHeight, // 使用计算出的最大高度
			x: 0, // 确保从容器左上角开始
			y: 0, // 确保从容器左上角开始
			scrollX: 0, // 忽略页面滚动
			scrollY: 0, // 忽略页面滚动
			logging: true, // 启用 html2canvas 内部日志
			ignoreElements: shouldIgnoreElement,
			onclone: (clonedDoc: Document) => {
				console.log("🔄 HTML2Canvas onclone 回调执行");

				// 确保动画在克隆文档中继续运行
				const animations =
					clonedDoc.querySelectorAll('[class*="animate"]');
				console.log(`找到 ${animations.length} 个动画元素`);
				animations.forEach((el: Element) => {
					(el as HTMLElement).style.animationPlayState = "running";
				});

				// 隐藏地图控件
				hideMapControlsInClonedDoc(clonedDoc);

				// 确保地图容器在克隆文档中的尺寸正确
				const clonedMapContainer = clonedDoc.querySelector(
					mapContainer.tagName.toLowerCase() +
						(mapContainer.className
							? "." + mapContainer.className.split(" ").join(".")
							: ""),
				);

				if (clonedMapContainer) {
					const clonedElement = clonedMapContainer as HTMLElement;
					clonedElement.style.width = captureWidth + "px";
					clonedElement.style.height = captureHeight + "px";
					clonedElement.style.overflow = "visible";
					console.log("设置克隆容器尺寸:", {
						width: captureWidth,
						height: captureHeight,
					});
				}
			},
		};

		console.log("📸 [导出调试] html2canvas 配置", html2canvasOptions);

		const mapCanvas = await html2canvas(mapContainer, html2canvasOptions);

		console.group("✅ HTML2Canvas Debug - 生成结果");
		console.log("生成的 Canvas 尺寸:", {
			canvasWidth: mapCanvas.width,
			canvasHeight: mapCanvas.height,
			expectedWidth: captureWidth * options.scale,
			expectedHeight: captureHeight * options.scale,
		});
		console.groupEnd();

		setCurrentMethod("导出完成！");

		// 如果提供了文件名，自动下载
		if (options.filename) {
			const dataURL = mapCanvas.toDataURL(
				`image/${options.format}`,
				options.quality,
			);
			downloadImage(dataURL, options.filename);
		}

		return mapCanvas;
	} catch (error) {
		console.error("地图导出失败:", error);
		throw new Error(
			`地图导出失败: ${error instanceof Error ? error.message : String(error)}`,
		);
	}
}

/**
 * 下载图片
 */
function downloadImage(dataURL: string, filename: string): void {
	const link = document.createElement("a");
	link.download = filename;
	link.href = dataURL;
	document.body.appendChild(link);
	link.click();
	document.body.removeChild(link);
}

/**
 * 统一的地图状态等待逻辑
 */
async function waitForMapStable(mapInstance: any): Promise<void> {
	return new Promise<void>((resolve) => {
		if (
			mapInstance.loaded() &&
			!mapInstance.isMoving() &&
			!mapInstance.isZooming() &&
			!mapInstance.isRotating()
		) {
			console.log("✅ [导出调试] 地图已稳定");
			resolve();
		} else {
			console.log("⏳ [导出调试] 等待地图空闲状态...");
			mapInstance.once("idle", () => {
				console.log("✅ [导出调试] 地图进入空闲状态");
				setTimeout(resolve, 200);
			});
		}
	});
}

/**
 * 统一的控件隐藏逻辑
 */
function hideMapControlsInClonedDoc(clonedDoc: Document): number {
	const controlSelectors = [
		// Mapbox 原生控件
		".mapboxgl-control-container",
		".mapboxgl-ctrl",
		".mapboxgl-ctrl-group",
		".mapboxgl-ctrl-zoom-in",
		".mapboxgl-ctrl-zoom-out",
		".mapboxgl-ctrl-compass",
		".mapboxgl-ctrl-geolocate",
		".mapboxgl-ctrl-fullscreen",
		".mapboxgl-ctrl-scale",
		".mapboxgl-ctrl-attrib",
		"[class^='mapboxgl-ctrl']",
		// 自定义控件
		"[data-export-hidden='true']",
		"[data-map-control]",
	];

	// 调试信息
	const allMapboxControls = clonedDoc.querySelectorAll(
		'[class*="mapboxgl-ctrl"], .mapboxgl-control-container',
	);
	const allCustomControls = clonedDoc.querySelectorAll(
		'[data-export-hidden="true"], [data-map-control]',
	);
	console.log("🔍 [控件调试] 克隆文档中发现控件:");
	console.log(`  - Mapbox控件: ${allMapboxControls.length} 个`);
	console.log(`  - 自定义控件: ${allCustomControls.length} 个`);

	let totalHidden = 0;
	controlSelectors.forEach((selector) => {
		try {
			const controls = clonedDoc.querySelectorAll(selector);
			console.log(
				`🚫 [控件隐藏] 选择器 ${selector} 找到 ${controls.length} 个控件`,
			);
			controls.forEach((ctrlElement) => {
				const element = ctrlElement as HTMLElement;
				element.style.display = "none";
				element.style.visibility = "hidden";
				element.style.opacity = "0";
				element.style.pointerEvents = "none";
				totalHidden++;
			});
		} catch (error) {
			console.warn(`⚠️ [控件隐藏] 选择器 ${selector} 执行失败:`, error);
		}
	});

	console.log(`✅ [控件隐藏] 总共隐藏了 ${totalHidden} 个控件元素`);
	return totalHidden;
}

/**
 * 统一的点位标记检查逻辑
 */
function isMarkerOrPointElement(element: Element): boolean {
	return (
		element.classList.contains("marker") ||
		element.classList.contains("point") ||
		element.classList.contains("travel-point") ||
		element.classList.contains("mapboxgl-marker") ||
		element.getAttribute("data-marker") !== null ||
		element.getAttribute("data-point") !== null ||
		/marker|point/i.test(element.className)
	);
}

/**
 * 统一的控件检查逻辑
 */
function shouldIgnoreElement(element: Element): boolean {
	// 如果是点位标记，不要忽略
	if (isMarkerOrPointElement(element)) {
		return false;
	}

	// 检查是否在控件容器内
	const isInControlContainer = (() => {
		let parent = element.parentElement;
		while (parent) {
			if (
				parent.classList.contains("mapboxgl-control-container") ||
				parent.classList.contains("mapboxgl-ctrl-group") ||
				/mapboxgl-ctrl/.test(parent.className)
			) {
				return true;
			}
			parent = parent.parentElement;
		}
		return false;
	})();

	return (
		// Mapbox 原生控件
		element.classList.contains("mapboxgl-control-container") ||
		element.classList.contains("mapboxgl-ctrl") ||
		element.classList.contains("mapboxgl-ctrl-group") ||
		element.classList.contains("mapboxgl-ctrl-zoom-in") ||
		element.classList.contains("mapboxgl-ctrl-zoom-out") ||
		element.classList.contains("mapboxgl-ctrl-compass") ||
		element.classList.contains("mapboxgl-ctrl-geolocate") ||
		element.classList.contains("mapboxgl-ctrl-fullscreen") ||
		element.classList.contains("mapboxgl-ctrl-scale") ||
		element.classList.contains("mapboxgl-ctrl-attrib") ||
		/^mapboxgl-ctrl/.test(element.className) ||
		(element.tagName === "BUTTON" && isInControlContainer) ||
		element.getAttribute("aria-label")?.includes("Zoom") ||
		false ||
		element.getAttribute("title")?.includes("Zoom") ||
		false ||
		// 自定义控件
		element.getAttribute("data-export-hidden") === "true" ||
		element.getAttribute("data-map-control") !== null
	);
}

/**
 * 统一的WebGL画布处理逻辑
 */
function handleWebGLCanvases(clonedDoc: Document, mapInstance: any): void {
	const originalMapCanvases = document.querySelectorAll(".mapboxgl-canvas");
	const clonedMapCanvases = clonedDoc.querySelectorAll(".mapboxgl-canvas");

	console.log(
		"🎨 [WebGL调试] 找到原始画布:",
		originalMapCanvases.length,
		"克隆画布:",
		clonedMapCanvases.length,
	);

	// 如果克隆文档中没有地图画布，尝试手动创建
	if (originalMapCanvases.length > 0 && clonedMapCanvases.length === 0) {
		console.log("🔧 [WebGL调试] 克隆文档缺少地图画布，尝试手动创建");

		const clonedMapContainers = clonedDoc.querySelectorAll(
			'.mapboxgl-map, [class*="mapbox"]',
		);
		console.log("🔍 [WebGL调试] 找到地图容器:", clonedMapContainers.length);

		if (clonedMapContainers.length > 0) {
			originalMapCanvases.forEach((originalCanvas, index) => {
				const clonedContainer = clonedMapContainers[index];
				if (clonedContainer) {
					try {
						const canvas2d = clonedDoc.createElement("canvas");
						const origCanvas = originalCanvas as HTMLCanvasElement;
						canvas2d.width = origCanvas.width;
						canvas2d.height = origCanvas.height;
						canvas2d.className = origCanvas.className;
						canvas2d.style.cssText = origCanvas.style.cssText;

						const ctx2d = canvas2d.getContext("2d");
						if (ctx2d) {
							try {
								ctx2d.drawImage(origCanvas, 0, 0);
								console.log(
									"✅ [手动创建] 成功复制WebGL画布内容",
								);
							} catch (error) {
								console.log(
									"⚠️ [手动创建] WebGL复制失败，使用占位背景",
								);
								ctx2d.fillStyle = "#e9ecef";
								ctx2d.fillRect(
									0,
									0,
									canvas2d.width,
									canvas2d.height,
								);
								ctx2d.fillStyle = "#6c757d";
								ctx2d.font = "16px Arial";
								ctx2d.textAlign = "center";
								ctx2d.fillText(
									"Travel Map",
									canvas2d.width / 2,
									canvas2d.height / 2,
								);
							}
						}

						clonedContainer.appendChild(canvas2d);
						console.log(
							`✅ [手动创建] 第${index + 1}个地图画布已手动创建`,
						);
					} catch (error) {
						console.error(
							`❌ [手动创建] 第${index + 1}个画布创建失败:`,
							error,
						);
					}
				}
			});
		}
	}

	if (
		originalMapCanvases.length > 0 &&
		clonedMapCanvases.length > 0 &&
		mapInstance
	) {
		// 处理每个地图画布
		originalMapCanvases.forEach((originalCanvas, index) => {
			const clonedCanvas = clonedMapCanvases[index];
			if (!clonedCanvas) return;

			try {
				console.log(`🎨 [WebGL调试] 处理第${index + 1}个WebGL画布`);

				if (mapInstance.triggerRepaint) {
					mapInstance.triggerRepaint();
				}

				const canvas2d = clonedDoc.createElement("canvas");
				const origCanvas = originalCanvas as HTMLCanvasElement;
				canvas2d.width = origCanvas.width;
				canvas2d.height = origCanvas.height;
				canvas2d.style.cssText = origCanvas.style.cssText;
				canvas2d.className = origCanvas.className;

				const ctx2d = canvas2d.getContext("2d");
				if (ctx2d) {
					let copySuccess = false;

					// 方法1: 直接drawImage
					try {
						ctx2d.drawImage(origCanvas, 0, 0);
						const imageData = ctx2d.getImageData(
							0,
							0,
							Math.min(10, origCanvas.width),
							Math.min(10, origCanvas.height),
						);
						const hasPixels = Array.from(imageData.data).some(
							(pixel) => pixel > 0,
						);

						if (hasPixels) {
							console.log(
								"✅ [WebGL调试] 方法1成功: drawImage复制WebGL内容",
							);
							copySuccess = true;
						} else {
							console.log(
								"⚠️ [WebGL调试] 方法1失败: drawImage未复制到内容",
							);
						}
					} catch (drawError) {
						console.log(
							"⚠️ [WebGL调试] 方法1失败:",
							drawError instanceof Error
								? drawError.message
								: drawError,
						);
					}

					// 方法2: 使用readPixels (备用)
					if (!copySuccess) {
						try {
							console.log(
								"🔄 [WebGL调试] 尝试方法2: WebGL readPixels",
							);
							const gl =
								origCanvas.getContext("webgl") ||
								origCanvas.getContext("experimental-webgl");
							if (gl && "readPixels" in gl) {
								const webglContext =
									gl as WebGLRenderingContext;
								const pixels = new Uint8Array(
									origCanvas.width * origCanvas.height * 4,
								);
								webglContext.readPixels(
									0,
									0,
									origCanvas.width,
									origCanvas.height,
									webglContext.RGBA,
									webglContext.UNSIGNED_BYTE,
									pixels,
								);

								const imageData = ctx2d.createImageData(
									origCanvas.width,
									origCanvas.height,
								);
								imageData.data.set(pixels);
								ctx2d.putImageData(imageData, 0, 0);

								console.log(
									"✅ [WebGL调试] 方法2成功: readPixels复制WebGL内容",
								);
								copySuccess = true;
							}
						} catch (readPixelsError) {
							console.log(
								"⚠️ [WebGL调试] 方法2失败:",
								readPixelsError instanceof Error
									? readPixelsError.message
									: readPixelsError,
							);
						}
					}

					// 方法3: 填充占位背景 (最后备用)
					if (!copySuccess) {
						console.log("🎨 [WebGL调试] 使用方法3: 填充占位背景");
						ctx2d.fillStyle = "#f8f9fa";
						ctx2d.fillRect(0, 0, canvas2d.width, canvas2d.height);
						ctx2d.fillStyle = "#6c757d";
						ctx2d.font = "14px Arial";
						ctx2d.textAlign = "center";
						ctx2d.fillText(
							"地图区域",
							canvas2d.width / 2,
							canvas2d.height / 2,
						);
					}
				}

				clonedCanvas.parentNode?.replaceChild(canvas2d, clonedCanvas);
				console.log(`✅ [WebGL调试] 第${index + 1}个画布替换完成`);
			} catch (canvasError) {
				console.error(
					`❌ [WebGL调试] 第${index + 1}个画布处理失败:`,
					canvasError,
				);
			}
		});
	}
}

/**
 * 卡片导出功能
 * 使用统一的地图处理逻辑导出卡片
 */
export async function exportCard(
	cardElement: HTMLElement,
	mapRef: any,
	options: Partial<ExportOptions> & {
		width: number;
		height: number;
		filename?: string;
	},
): Promise<HTMLCanvasElement> {
	console.log("🚀 [卡片导出调试] 开始导出卡片");

	// 动态导入 html2canvas-pro
	const html2canvas = (await import("html2canvas-pro")).default;

	// 检查是否有地图实例
	let mapInstance = null;
	if (mapRef) {
		mapInstance = mapRef.current || mapRef.getMap?.();
		console.log("📍 [卡片导出调试] 地图引用结构:", {
			hasRef: !!mapRef,
			hasCurrent: !!mapRef.current,
			hasGetMap: typeof mapRef.getMap === "function",
			mapInstance: !!mapInstance,
		});
	} else {
		console.log("📍 [卡片导出调试] 没有地图引用");
	}

	// 等待地图和动画稳定
	console.log("⏳ [卡片导出调试] 等待地图稳定...");
	if (mapInstance) {
		await waitForMapStable(mapInstance);
	}

	// 额外等待确保所有动画和渲染完成
	await new Promise((resolve) => setTimeout(resolve, 500));

	console.log("📸 [卡片导出调试] 开始html2canvas捕获");

	// 使用html2canvas-pro捕获整个卡片
	const canvas = await html2canvas(cardElement, {
		useCORS: true,
		allowTaint: false,
		backgroundColor: null,
		scale: options.scale || 2,
		width: options.width,
		height: options.height,
		x: 0,
		y: 0,
		scrollX: 0,
		scrollY: 0,
		logging: true,
		foreignObjectRendering: false,
		ignoreElements: shouldIgnoreElement,
		onclone: (clonedDoc) => {
			console.log("🔄 [卡片导出调试] html2canvas onclone回调执行");

			// 调试信息
			const allCanvases = clonedDoc.querySelectorAll("canvas");
			console.log(
				"🔍 [onclone调试] 克隆文档中所有canvas元素:",
				allCanvases.length,
			);

			// 确保克隆文档中的卡片样式正确
			const clonedCard = clonedDoc.querySelector("[data-card-root]");
			if (clonedCard) {
				(clonedCard as HTMLElement).style.transform = "none";
				(clonedCard as HTMLElement).style.width = `${options.width}px`;
				(clonedCard as HTMLElement).style.height =
					`${options.height}px`;
				console.log("📐 [卡片导出调试] 设置克隆卡片尺寸:", {
					width: options.width,
					height: options.height,
				});
			}

			// 确保动画在克隆文档中继续运行
			const animations = clonedDoc.querySelectorAll('[class*="animate"]');
			console.log(
				`🎬 [卡片导出调试] 找到 ${animations.length} 个动画元素`,
			);
			animations.forEach((el: Element) => {
				(el as HTMLElement).style.animationPlayState = "running";
			});

			// 隐藏地图控件
			hideMapControlsInClonedDoc(clonedDoc);

			// 处理WebGL画布
			handleWebGLCanvases(clonedDoc, mapInstance);
		},
	});

	console.log("✅ [卡片导出调试] html2canvas完成");
	console.log("生成的Canvas尺寸:", {
		width: canvas.width,
		height: canvas.height,
	});

	// 验证canvas内容
	const ctx = canvas.getContext("2d");
	if (ctx) {
		const imageData = ctx.getImageData(
			0,
			0,
			Math.min(10, canvas.width),
			Math.min(10, canvas.height),
		);
		const hasContent = Array.from(imageData.data).some(
			(pixel) => pixel > 0,
		);
		console.log(
			"🔍 [卡片导出调试] Canvas内容检测:",
			hasContent ? "有内容" : "全黑",
		);
	}

	// 如果提供了文件名，自动下载
	if (options.filename) {
		const dataURL = canvas.toDataURL("image/png", 1.0);
		downloadImage(dataURL, options.filename);
	}

	return canvas;
}
