import { useMemo } from "react";
import type { ColorTheme, ColorThemeType } from "../types/colorTypes";
import {
	getAllTranslatedColorThemes,
	getTranslatedColorTheme,
} from "../utils/colorThemeUtils";
import { useTravelStatTranslations } from "./useTravelStatTranslations";

export function useTranslatedColorThemes() {
	const translations = useTravelStatTranslations();

	const translatedThemes = useMemo(() => {
		return getAllTranslatedColorThemes(translations.colorThemes);
	}, [translations.colorThemes]);

	const getTheme = useMemo(() => {
		return (themeId: ColorThemeType): ColorTheme => {
			return getTranslatedColorTheme(themeId, translations.colorThemes);
		};
	}, [translations.colorThemes]);

	return {
		themes: translatedThemes,
		getTheme,
	};
}
