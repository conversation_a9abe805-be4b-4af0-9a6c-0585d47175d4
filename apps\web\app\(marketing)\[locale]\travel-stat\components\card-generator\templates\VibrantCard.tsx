"use client";

import {
	Building2,
	Calendar,
	Flag,
	MapPin,
	Spark<PERSON>,
	Star,
} from "lucide-react";
import React from "react";

import type {
	CardCustomization,
	CardTemplateProps,
	CountryData,
	TravelPoint,
} from "../types/cardTypes";

interface VibrantCardProps extends CardTemplateProps {
	mapImageData?: {
		dataURL: string;
		dimensions: { width: number; height: number };
	};
	mapComponent?: React.ReactNode;
	travelPoints: TravelPoint[];
	visitedCountries: CountryData[];
	customization: CardCustomization;
	platform?: string;
	isPreview?: boolean;
}

export function VibrantCard({
	mapImageData,
	mapComponent,
	travelPoints,
	visitedCountries,
	customization,
	platform = "instagram",
	isPreview = false,
}: VibrantCardProps) {
	// 默认值
	const colors = customization.colors || {
		primary: "#ec4899",
		secondary: "#f97316",
		accent: "#8b5cf6",
		background: "#ffffff",
		text: "#111827",
	};

	const typography = customization.typography || {
		fontFamily: "'Poppins', -apple-system, BlinkMacSystemFont, sans-serif",
		headerSize: 36,
		bodySize: 20,
		titleWeight: 700,
	};

	const layout = customization.layout || {
		padding: 32,
		spacing: 20,
		borderRadius: 20,
		showShadow: true,
	};

	const content = customization.content || {
		showUserInfo: true,
		showDetailedStats: true,
		customTitle: "🌈 Amazing Journey",
		customFooter: "✨ Life is a Journey · Make it Colorful!",
	};

	// Ensure showUserInfo and showDetailedStats have proper defaults
	const safeContent = {
		showUserInfo: content.showUserInfo ?? true,
		showDetailedStats: content.showDetailedStats ?? true,
		customTitle: content.customTitle || "🌈 Amazing Journey",
		customFooter:
			content.customFooter || "✨ Life is a Journey · Make it Colorful!",
	};

	// 计算统计数据
	const stats = {
		totalPoints: travelPoints.length,
		totalCountries: visitedCountries.length,
		totalCities: new Set(travelPoints.map((p) => p.city)).size,
		earliestDate:
			travelPoints.length > 0
				? travelPoints.reduce((earliest, point) =>
						new Date(point.date) < new Date(earliest.date)
							? point
							: earliest,
					).date
				: null,
		latestDate:
			travelPoints.length > 0
				? travelPoints.reduce((latest, point) =>
						new Date(point.date) > new Date(latest.date)
							? point
							: latest,
					).date
				: null,
	};

	// 平台尺寸配置
	const platformDimensions = {
		instagram: { width: 1080, height: 1080, aspectRatio: 1 },
		wechat: { width: 1200, height: 900, aspectRatio: 4 / 3 },
		weibo: { width: 1080, height: 1350, aspectRatio: 4 / 5 },
		twitter: { width: 1200, height: 675, aspectRatio: 16 / 9 },
		facebook: { width: 1200, height: 630, aspectRatio: 1.91 },
	};

	const dimensions =
		platformDimensions[platform as keyof typeof platformDimensions] ||
		platformDimensions.instagram;

	// 根据宽高比决定布局策略
	const isWideFormat = dimensions.aspectRatio > 1.5; // Twitter, Facebook
	const isTallFormat = dimensions.aspectRatio < 0.9; // Weibo
	const isSquareFormat =
		dimensions.aspectRatio >= 0.9 && dimensions.aspectRatio <= 1.3; // Instagram, WeChat

	const scale = Math.min(dimensions.width / 1080, 1.5);

	const adjustedTypography = {
		titleSize: Math.round(isWideFormat ? 46 * scale : 56 * scale),
		statsSize: Math.round(isWideFormat ? 60 * scale : 80 * scale),
		headerSize: Math.round((typography.headerSize || 36) * scale),
		bodySize: Math.round((typography.bodySize || 20) * scale),
		smallSize: Math.round(16 * scale),
		titleWeight: typography.titleWeight || 700,
		fontFamily: typography.fontFamily || "'Poppins', sans-serif",
	};

	const getLayoutValue = (
		key: "padding" | "spacing" | "borderRadius",
		defaultValue: number,
	): number => {
		const value = layout[key];
		return typeof value === "number" ? value : defaultValue;
	};

	const getDateRange = () => {
		// 优先使用自定义日期文本
		if (content.customDate?.trim()) {
			return content.customDate;
		}
		// 回退到计算的日期范围
		if (!stats.earliestDate || !stats.latestDate) return "";
		const start = new Date(stats.earliestDate).getFullYear();
		const end = new Date(stats.latestDate).getFullYear();
		return start === end ? `${start}` : `${start} - ${end}`;
	};

	// 渲染宽屏布局 (Twitter, Facebook)
	if (isWideFormat) {
		return (
			<div
				data-card-element
				className="relative overflow-hidden"
				style={{
					width: dimensions.width,
					height: dimensions.height,
					fontFamily: adjustedTypography.fontFamily,
					background:
						"linear-gradient(135deg, #ff6b6b 0%, #ffd93d 25%, #6bcf7f 50%, #4ecdc4 75%, #45b7d1 100%)",
					borderRadius: getLayoutValue("borderRadius", 20),
					padding: getLayoutValue("padding", 32),
					position: "relative",
					display: "flex",
					flexDirection: "row",
					alignItems: "stretch",
				}}
			>
				{/* 背景装饰动画元素 */}
				<div className="absolute inset-0 overflow-hidden">
					<div
						style={{
							position: "absolute",
							top: "10%",
							left: "5%",
							width: "60px",
							height: "60px",
							background: "rgba(255, 255, 255, 0.1)",
							borderRadius: "50%",
							filter: "blur(15px)",
						}}
					/>
					<div
						style={{
							position: "absolute",
							top: "20%",
							right: "10%",
							width: "80px",
							height: "80px",
							background: "rgba(255, 255, 255, 0.08)",
							borderRadius: "20px",
							transform: "rotate(45deg)",
							filter: "blur(20px)",
						}}
					/>
					<div
						style={{
							position: "absolute",
							bottom: "30%",
							left: "15%",
							width: "40px",
							height: "40px",
							background: "rgba(255, 255, 255, 0.12)",
							clipPath: "polygon(50% 0%, 0% 100%, 100% 100%)",
							filter: "blur(10px)",
						}}
					/>
				</div>

				{/* 左侧内容区域 */}
				<div
					className="flex flex-col justify-between relative z-10"
					style={{
						width: "40%",
						paddingRight: getLayoutValue("spacing", 20) * 2,
					}}
				>
					{/* Header */}
					{safeContent.showUserInfo && (
						<div
							className="relative"
							style={{
								transform: "skew(-2deg)",
								padding: `${getLayoutValue("spacing", 20)}px`,
								background: "rgba(255, 255, 255, 0.15)",
								borderRadius: getLayoutValue(
									"borderRadius",
									20,
								),
								backdropFilter: "blur(20px)",
								border: "1px solid rgba(255, 255, 255, 0.2)",
							}}
						>
							<div style={{ transform: "skew(2deg)" }}>
								<h1
									style={{
										fontSize: adjustedTypography.titleSize,
										fontWeight: 900,
										color: "white",
										margin: 0,
										lineHeight: 1,
										textAlign: "center",
										textShadow:
											"0 4px 20px rgba(0, 0, 0, 0.3)",
										marginBottom:
											getLayoutValue("spacing", 20) / 2,
									}}
								>
									{safeContent.customTitle}
								</h1>

								{getDateRange() && (
									<div
										style={{
											display: "flex",
											alignItems: "center",
											justifyContent: "center",
											gap: "10px",
											color: "white",
											fontSize:
												adjustedTypography.bodySize,
											fontWeight: 600,
										}}
									>
										<Calendar
											size={adjustedTypography.bodySize}
										/>
										<span>{getDateRange()}</span>
										<Sparkles
											size={adjustedTypography.bodySize}
										/>
									</div>
								)}
							</div>
						</div>
					)}

					{/* Stats - 垂直布局 */}
					{safeContent.showDetailedStats && (
						<div className="space-y-4">
							{/* 足迹点 */}
							<div
								className="text-center relative"
								style={{
									background:
										"conic-gradient(from 0deg, #ff6b6b, #ffd93d, #ff6b6b)",
									borderRadius: "20px",
									padding: "20px",
									transform: "rotate(-5deg)",
									border: "3px solid white",
									boxShadow:
										"0 15px 30px rgba(255, 107, 107, 0.3)",
								}}
							>
								<div style={{ transform: "rotate(5deg)" }}>
									<div
										style={{
											fontSize:
												adjustedTypography.statsSize,
											fontWeight: 900,
											color: "white",
											lineHeight: 1,
											textShadow:
												"0 4px 20px rgba(0, 0, 0, 0.5)",
										}}
									>
										{stats.totalPoints}
									</div>
									<div
										style={{
											fontSize:
												adjustedTypography.bodySize - 4,
											color: "white",
											fontWeight: 700,
											marginTop: "5px",
											display: "flex",
											alignItems: "center",
											justifyContent: "center",
											gap: "5px",
										}}
									>
										<MapPin
											size={
												adjustedTypography.bodySize - 4
											}
										/>
										足迹
									</div>
								</div>
							</div>

							{/* 城市 */}
							<div
								className="text-center relative"
								style={{
									background:
										"conic-gradient(from 120deg, #6bcf7f, #4ecdc4, #6bcf7f)",
									borderRadius: "20px",
									padding: "20px",
									transform: "scale(1.05)",
									border: "3px solid white",
									boxShadow:
										"0 15px 30px rgba(107, 207, 127, 0.3)",
								}}
							>
								<div
									style={{
										fontSize: adjustedTypography.statsSize,
										fontWeight: 900,
										color: "white",
										lineHeight: 1,
										textShadow:
											"0 4px 20px rgba(0, 0, 0, 0.5)",
									}}
								>
									{stats.totalCities}
								</div>
								<div
									style={{
										fontSize:
											adjustedTypography.bodySize - 4,
										color: "white",
										fontWeight: 700,
										marginTop: "5px",
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										gap: "5px",
									}}
								>
									<Building2
										size={adjustedTypography.bodySize - 4}
									/>
									城市
								</div>
							</div>

							{/* 国家 */}
							<div
								className="text-center relative"
								style={{
									background:
										"conic-gradient(from 240deg, #45b7d1, #8b5cf6, #45b7d1)",
									borderRadius: "20px",
									padding: "20px",
									transform: "rotate(5deg)",
									border: "3px solid white",
									boxShadow:
										"0 15px 30px rgba(69, 183, 209, 0.3)",
								}}
							>
								<div style={{ transform: "rotate(-5deg)" }}>
									<div
										style={{
											fontSize:
												adjustedTypography.statsSize,
											fontWeight: 900,
											color: "white",
											lineHeight: 1,
											textShadow:
												"0 4px 20px rgba(0, 0, 0, 0.5)",
										}}
									>
										{stats.totalCountries}
									</div>
									<div
										style={{
											fontSize:
												adjustedTypography.bodySize - 4,
											color: "white",
											fontWeight: 700,
											marginTop: "5px",
											display: "flex",
											alignItems: "center",
											justifyContent: "center",
											gap: "5px",
										}}
									>
										<Flag
											size={
												adjustedTypography.bodySize - 4
											}
										/>
										国家
									</div>
								</div>
							</div>
						</div>
					)}
				</div>

				{/* 右侧地图区域 */}
				<div
					className="relative"
					style={{
						width: "60%",
						borderRadius: getLayoutValue("borderRadius", 20),
						background: "linear-gradient(45deg, #ff6b6b, #4ecdc4)",
						padding: "6px",
						boxShadow: "0 20px 40px rgba(0, 0, 0, 0.2)",
					}}
				>
					<div
						style={{
							width: "100%",
							height: "100%",
							borderRadius:
								getLayoutValue("borderRadius", 20) - 6,
							overflow: "hidden",
							position: "relative",
						}}
					>
						{mapComponent ? (
							<div style={{ width: "100%", height: "100%" }}>
								{mapComponent}
							</div>
						) : (
							mapImageData && (
								<img
									src={mapImageData.dataURL}
									alt="Travel Map"
									style={{
										width: "100%",
										height: "100%",
										objectFit: "cover",
									}}
								/>
							)
						)}

						{/* 星星装饰覆盖层 */}
						<div className="absolute inset-0">
							{[...Array(4)].map((_, i) => (
								<Star
									key={i}
									size={16}
									style={{
										position: "absolute",
										top: `${20 + i * 20}%`,
										left: `${10 + i * 15}%`,
										color: "rgba(255, 255, 255, 0.8)",
										filter: "drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))",
									}}
								/>
							))}
						</div>

						{/* 水印文字 - 放在地图右下角 */}
						{safeContent.customFooter && (
							<div
								className="absolute bottom-4 right-4 text-center"
								style={{
									background: "rgba(255, 255, 255, 0.25)",
									backdropFilter: "blur(10px)",
									borderRadius: "15px",
									padding: "8px 16px",
									border: "1px solid rgba(255, 255, 255, 0.3)",
									boxShadow: "0 4px 15px rgba(0, 0, 0, 0.15)",
									maxWidth: "80%",
								}}
							>
								<p
									style={{
										fontSize:
											adjustedTypography.bodySize - 2,
										color: "white",
										margin: 0,
										fontWeight: 600,
										textAlign: "center",
										textShadow:
											"0 1px 3px rgba(0, 0, 0, 0.3)",
									}}
								>
									{safeContent.customFooter}
								</p>
							</div>
						)}
					</div>
				</div>
			</div>
		);
	}

	return (
		<div
			data-card-element
			className="relative overflow-hidden flex flex-col"
			style={{
				width: dimensions.width,
				height: dimensions.height,
				fontFamily: adjustedTypography.fontFamily,
				background:
					"linear-gradient(135deg, #ff6b6b 0%, #ffd93d 25%, #6bcf7f 50%, #4ecdc4 75%, #45b7d1 100%)",
				borderRadius: getLayoutValue("borderRadius", 20),
				padding: getLayoutValue("padding", 32),
				boxShadow: "0 30px 60px rgba(0, 0, 0, 0.2)",
				position: "relative",
			}}
		>
			{/* 背景装饰动画元素 */}
			<div className="absolute inset-0 overflow-hidden">
				{/* 漂浮的几何形状 */}
				<div
					style={{
						position: "absolute",
						top: "10%",
						left: "5%",
						width: "80px",
						height: "80px",
						background: "rgba(255, 255, 255, 0.1)",
						borderRadius: "50%",
						filter: "blur(20px)",
					}}
				/>
				<div
					style={{
						position: "absolute",
						top: "30%",
						right: "10%",
						width: "120px",
						height: "120px",
						background: "rgba(255, 255, 255, 0.08)",
						borderRadius: "30px",
						transform: "rotate(45deg)",
						filter: "blur(25px)",
					}}
				/>
				<div
					style={{
						position: "absolute",
						bottom: "20%",
						left: "15%",
						width: "60px",
						height: "60px",
						background: "rgba(255, 255, 255, 0.12)",
						clipPath: "polygon(50% 0%, 0% 100%, 100% 100%)",
						filter: "blur(15px)",
					}}
				/>
			</div>

			{/* Header - 斜角设计 */}
			{safeContent.showUserInfo && (
				<div
					className="relative flex-shrink-0"
					style={{
						marginBottom: getLayoutValue("spacing", 20) * 1.5,
						transform: "skew(-2deg)",
						padding: `${getLayoutValue("spacing", 20)}px`,
						background: "rgba(255, 255, 255, 0.15)",
						borderRadius: getLayoutValue("borderRadius", 20),
						backdropFilter: "blur(20px)",
						border: "1px solid rgba(255, 255, 255, 0.2)",
					}}
				>
					<div style={{ transform: "skew(2deg)" }}>
						<h1
							style={{
								fontSize: adjustedTypography.titleSize,
								fontWeight: 900,
								color: "white",
								margin: 0,
								lineHeight: 1,
								textAlign: "center",
								textShadow: "0 4px 20px rgba(0, 0, 0, 0.3)",
								marginBottom: getLayoutValue("spacing", 20) / 2,
							}}
						>
							{safeContent.customTitle}
						</h1>

						{getDateRange() && (
							<div
								style={{
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
									gap: "10px",
									color: "white",
									fontSize: adjustedTypography.bodySize,
									fontWeight: 600,
								}}
							>
								<Calendar size={adjustedTypography.bodySize} />
								<span>{getDateRange()}</span>
								<Sparkles size={adjustedTypography.bodySize} />
							</div>
						)}
					</div>
				</div>
			)}

			{/* Stats - 圆形布局 */}
			{safeContent.showDetailedStats && (
				<div
					className="relative flex-shrink-0"
					style={{
						display: "flex",
						justifyContent: "space-around",
						alignItems: "center",
						marginBottom: getLayoutValue("spacing", 20) * 1.5,
						transform: "perspective(1000px) rotateX(5deg)",
					}}
				>
					{/* 足迹点 - 左边 */}
					<div
						className="text-center relative"
						style={{
							width: "150px",
							height: "150px",
							background:
								"conic-gradient(from 0deg, #ff6b6b, #ffd93d, #ff6b6b)",
							borderRadius: "50%",
							display: "flex",
							flexDirection: "column",
							alignItems: "center",
							justifyContent: "center",
							boxShadow: "0 20px 40px rgba(255, 107, 107, 0.4)",
							transform: "rotate(-10deg)",
							border: "4px solid white",
						}}
					>
						<div
							style={{
								fontSize: adjustedTypography.statsSize,
								fontWeight: 900,
								color: "white",
								lineHeight: 1,
								textShadow: "0 4px 20px rgba(0, 0, 0, 0.5)",
								transform: "rotate(10deg)",
							}}
						>
							{stats.totalPoints}
						</div>
						<div
							style={{
								fontSize: adjustedTypography.bodySize - 4,
								color: "white",
								fontWeight: 700,
								marginTop: "5px",
								transform: "rotate(10deg)",
								display: "flex",
								alignItems: "center",
								gap: "5px",
							}}
						>
							<MapPin size={adjustedTypography.bodySize - 4} />
							足迹
						</div>
					</div>

					{/* 城市 - 中间 */}
					<div
						className="text-center relative"
						style={{
							width: "170px",
							height: "170px",
							background:
								"conic-gradient(from 120deg, #6bcf7f, #4ecdc4, #6bcf7f)",
							borderRadius: "50%",
							display: "flex",
							flexDirection: "column",
							alignItems: "center",
							justifyContent: "center",
							boxShadow: "0 25px 50px rgba(107, 207, 127, 0.4)",
							transform: "scale(1.1)",
							border: "4px solid white",
							zIndex: 2,
						}}
					>
						<div
							style={{
								fontSize: adjustedTypography.statsSize + 10,
								fontWeight: 900,
								color: "white",
								lineHeight: 1,
								textShadow: "0 4px 20px rgba(0, 0, 0, 0.5)",
							}}
						>
							{stats.totalCities}
						</div>
						<div
							style={{
								fontSize: adjustedTypography.bodySize - 2,
								color: "white",
								fontWeight: 700,
								marginTop: "8px",
								display: "flex",
								alignItems: "center",
								gap: "5px",
							}}
						>
							<Building2 size={adjustedTypography.bodySize - 2} />
							城市
						</div>
					</div>

					{/* 国家 - 右边 */}
					<div
						className="text-center relative"
						style={{
							width: "150px",
							height: "150px",
							background:
								"conic-gradient(from 240deg, #45b7d1, #8b5cf6, #45b7d1)",
							borderRadius: "50%",
							display: "flex",
							flexDirection: "column",
							alignItems: "center",
							justifyContent: "center",
							boxShadow: "0 20px 40px rgba(69, 183, 209, 0.4)",
							transform: "rotate(10deg)",
							border: "4px solid white",
						}}
					>
						<div
							style={{
								fontSize: adjustedTypography.statsSize,
								fontWeight: 900,
								color: "white",
								lineHeight: 1,
								textShadow: "0 4px 20px rgba(0, 0, 0, 0.5)",
								transform: "rotate(-10deg)",
							}}
						>
							{stats.totalCountries}
						</div>
						<div
							style={{
								fontSize: adjustedTypography.bodySize - 4,
								color: "white",
								fontWeight: 700,
								marginTop: "5px",
								transform: "rotate(-10deg)",
								display: "flex",
								alignItems: "center",
								gap: "5px",
							}}
						>
							<Flag size={adjustedTypography.bodySize - 4} />
							国家
						</div>
					</div>
				</div>
			)}

			{/* Map Section - 占据剩余空间 */}
			<div
				className="relative flex-1"
				style={{
					borderRadius: getLayoutValue("borderRadius", 20),
					marginBottom: 0,
					minHeight: "200px",
					background: "linear-gradient(45deg, #ff6b6b, #4ecdc4)",
					padding: "6px",
					boxShadow: "0 20px 40px rgba(0, 0, 0, 0.2)",
				}}
			>
				<div
					style={{
						width: "100%",
						height: "100%",
						borderRadius: getLayoutValue("borderRadius", 20) - 6,
						overflow: "hidden",
						position: "relative",
					}}
				>
					{/* 渲染地图 - 支持真实地图组件或图片 */}
					{mapComponent ? (
						// 预览模式：使用真实地图组件
						<div
							style={{
								width: "100%",
								height: "100%",
							}}
						>
							{mapComponent}
						</div>
					) : (
						// 导出模式：使用地图截图
						mapImageData && (
							<img
								src={mapImageData.dataURL}
								alt="Travel Map"
								style={{
									width: "100%",
									height: "100%",
									objectFit: "cover",
								}}
							/>
						)
					)}

					{/* 星星装饰覆盖层 - 不阻挡地图交互 */}
					<div
						className="absolute inset-0"
						style={{
							// 确保装饰层不阻挡地图交互
							pointerEvents: "none",
							zIndex: 20, // 在地图之上但不阻挡交互
						}}
					>
						{[...Array(6)].map((_, i) => (
							<Star
								key={i}
								size={20}
								style={{
									position: "absolute",
									top: `${20 + i * 15}%`,
									left: `${10 + i * 12}%`,
									color: "rgba(255, 255, 255, 0.8)",
									filter: "drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3))",
									animation: `twinkle ${2 + i * 0.5}s infinite alternate`,
									// 确保单个星星也不阻挡交互
									pointerEvents: "none",
								}}
							/>
						))}
					</div>

					{/* 水印文字 - 放在地图右下角 */}
					{safeContent.customFooter && (
						<div
							className="absolute bottom-4 right-4 text-center"
							style={{
								background: "rgba(255, 255, 255, 0.25)",
								backdropFilter: "blur(10px)",
								borderRadius: "15px",
								padding: "8px 16px",
								border: "1px solid rgba(255, 255, 255, 0.3)",
								boxShadow: "0 4px 15px rgba(0, 0, 0, 0.15)",
								maxWidth: "80%",
							}}
						>
							<p
								style={{
									fontSize: adjustedTypography.bodySize - 2,
									color: "white",
									margin: 0,
									fontWeight: 600,
									textAlign: "center",
									textShadow: "0 1px 3px rgba(0, 0, 0, 0.3)",
								}}
							>
								{safeContent.customFooter}
							</p>
						</div>
					)}
				</div>
			</div>

			<style jsx>{`
				@keyframes twinkle {
					0% { opacity: 0.5; transform: scale(1); }
					100% { opacity: 1; transform: scale(1.2); }
				}
			`}</style>
		</div>
	);
}
