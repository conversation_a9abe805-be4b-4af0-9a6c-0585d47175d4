# **功能：特性记录与投票模块**

## 1. 项目背景与目标

为了提升用户在产品迭代过程中的参与感，并为产品团队提供一个集中、量化的用户需求收集渠道，我们计划开发一个"特性记录与投票"模块。该模块将作为公开的产品路线图 (Public Roadmap)，允许用户查看、投票并提交新的功能建议，从而加强产品与用户之间的沟通，确保开发资源能投入到用户最需要的功能上。

**核心目标:**
*   **提高用户参与度与忠诚度**: 让用户感觉到他们的声音被倾听。
*   **数据驱动决策**: 基于用户的投票和建议来规划产品功能优先级。
*   **增加开发透明度**: 公开展示产品未来的发展方向和当前进度。
*   **集中化反馈**: 将功能请求从分散的渠道（邮件、客服）统一管理。

## 2. 核心功能需求

### 2.1. 管理员 (Admin)
*   **产品管理**: 能够创建和管理多个产品线，以便为不同产品分别设置特性列表。
*   **特性管理**: 能够为指定产品创建、编辑和删除功能特性请求。
*   **状态更新**: 能够更新功能特性的状态，例如：`待考虑` -> `计划中` -> `开发中` -> `已完成`。
*   **反馈审阅**: 能够查看所有用户提交的新建议和评论。
*   **请求合并 (优化项)**: 能够将内容重复的用户建议进行合并，并整合票数。
*   **数据看板 (优化项)**: 能够在一个仪表盘上快速浏览各特性的投票排行和统计数据。

### 2.2. 用户 (User)
*   **浏览特性**: 能够查看指定产品已发布的未来特性列表。
*   **投票功能**: 能够对自己感兴趣的特性进行投票支持（每个用户限一票，用 localstorage 存储，无需用户登录），并可以取消投票。
*   **提交建议**: 能够通过表单提交自己想要的新功能建议。
*   **查看票数**: (可选开启) 能够看到每个特性当前获得的票数。
*   **评论互动 (优化项)**: 能够对特性进行评论，阐述自己的具体想法。
*   **状态跟踪 (优化项)**: 能够清晰地看到每个特性的当前状态。
*   **接收通知 (优化项)**: 当自己投票或评论过的特性状态更新时，能收到通知（用户可以留邮箱以便收到通知，但不强制）。

## 3. 技术方案设计

本方案旨在创建一个高度模块化、可复用的功能模块，以便在 Monorepo 内的任何项目中轻松集成。

### 3.1. 数据库 (`packages/database`)

我们将通过在 `prisma/schema.prisma` 中添加以下新模型来扩展数据库。

```prisma
// ===============================================
// Feature Voting Module
// ===============================================

// 产品模型
model Product {
  id              String           @id @default(cuid())
  name            String           @unique
  description     String?
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  featureRequests FeatureRequest[]

  @@map("products")
}

// 特性请求模型
model FeatureRequest {
  id          String   @id @default(cuid())
  title       String
  description String
  status      String   @default("under_consideration") // e.g., under_consideration, planned, in_progress, completed
  voteCount   Int      @default(0)
  productId   String
  product     Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  // 提交者信息 - 支持登录和非登录用户
  userId      String?  // 登录用户ID（可选）
  user        User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  authorName  String?  // 非登录用户的昵称（可选）
  authorEmail String?  // 非登录用户的邮箱（可选，用于通知）
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  votes    FeatureVote[]
  comments FeatureComment[]

  @@index([productId, status])
  @@map("feature_requests")
}

// 特性投票模型 - 支持登录和非登录用户
model FeatureVote {
  id               String         @id @default(cuid())
  featureRequestId String
  featureRequest   FeatureRequest @relation(fields: [featureRequestId], references: [id], onDelete: Cascade)
  
  // 投票者身份 - 二选一
  userId           String?        // 登录用户ID（可选）
  user             User?          @relation(fields: [userId], references: [id], onDelete: Cascade)
  anonymousId      String?        // 匿名用户标识（浏览器指纹或UUID，可选）
  
  // 投票者信息（用于匿名用户）
  voterName        String?        // 投票者昵称（可选）
  voterEmail       String?        // 投票者邮箱（可选，用于通知）
  ipAddress        String?        // IP地址（用于防刷票）
  userAgent        String?        // 用户代理（用于防刷票）
  
  createdAt        DateTime       @default(now())

  // 确保同一用户/匿名用户对同一特性只能投一票
  @@unique([featureRequestId, userId])
  @@unique([featureRequestId, anonymousId])
  @@index([featureRequestId])
  @@index([userId])
  @@index([anonymousId])
  @@index([ipAddress]) // 用于防刷票查询
  @@map("feature_votes")
}

// 特性评论模型 - 支持登录和非登录用户
model FeatureComment {
  id               String         @id @default(cuid())
  content          String
  featureRequestId String
  featureRequest   FeatureRequest @relation(fields: [featureRequestId], references: [id], onDelete: Cascade)
  
  // 评论者身份 - 二选一
  userId           String?        // 登录用户ID（可选）
  user             User?          @relation(fields: [userId], references: [id], onDelete: Cascade)
  anonymousId      String?        // 匿名用户标识（可选）
  
  // 评论者信息（用于匿名用户）
  authorName       String?        // 评论者昵称（可选）
  authorEmail      String?        // 评论者邮箱（可选，用于通知）
  ipAddress        String?        // IP地址（用于防刷票）
  
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt

  @@index([featureRequestId])
  @@index([userId])
  @@index([anonymousId])
  @@map("feature_comments")
}

// 邮箱订阅模型 - 用于匿名用户接收通知
model FeatureSubscription {
  id               String         @id @default(cuid())
  featureRequestId String
  featureRequest   FeatureRequest @relation(fields: [featureRequestId], references: [id], onDelete: Cascade)
  
  // 订阅者身份 - 二选一
  userId           String?        // 登录用户ID（可选）
  user             User?          @relation(fields: [userId], references: [id], onDelete: Cascade)
  email            String?        // 匿名用户邮箱（可选）
  
  // 订阅设置
  notifyOnStatusChange Boolean    @default(true)  // 状态变更通知
  notifyOnComments     Boolean    @default(false) // 新评论通知
  isActive             Boolean    @default(true)  // 订阅是否激活
  
  createdAt        DateTime       @default(now())
  updatedAt        DateTime       @updatedAt

  @@unique([featureRequestId, userId])
  @@unique([featureRequestId, email])
  @@index([email])
  @@map("feature_subscriptions")
}
```

**注意**: 需要在现有的 `User` 模型中添加以下关联关系：
```prisma
model User {
  // ... 现有字段 ...
  
  // Feature Voting 模块关联
  featureRequests      FeatureRequest[]
  featureVotes         FeatureVote[]
  featureComments      FeatureComment[]
  featureSubscriptions FeatureSubscription[]
}
```

### 3.2. 后端 API (`packages/api`)

将在 `packages/api/src/routes/` 目录下创建新的路由文件。

*   **后台管理 API (`/api/admin/`)**:
    *   `products.ts`: 提供对 `Product` 模型的 CRUD 操作。
    *   `feature-requests.ts`: 提供管理员对 `FeatureRequest` 模型的 CRUD 操作，包括状态变更。
*   **前端用户 API (`/api/`)**:
    *   `feature-requests.ts`:
        *   `GET /`: 获取某个产品发布的特性列表（含用户本人投票状态，支持匿名用户通过 localStorage 查询）。
        *   `POST /`: 用户提交新的特性建议（支持登录和匿名用户）。
        *   `POST /:id/vote`: 用户投票（支持登录和匿名用户，匿名用户使用浏览器指纹或设备ID）。
        *   `DELETE /:id/vote`: 用户取消投票（支持登录和匿名用户）。
        *   `GET /:id/comments`: (优化项) 获取特性的评论。
        *   `POST /:id/comments`: (优化项) 用户发表评论（支持登录和匿名用户）。
        *   `POST /:id/subscribe`: (优化项) 订阅特性更新通知（支持邮箱订阅）。

### 3.3. 可复用 UI 组件 (`packages/shared-ui`)

在 `packages/shared-ui` 中创建一个新的模块 `feature-voting`，提供即插即用的组件。

*   **目录**: `packages/shared-ui/src/feature-voting/`
*   **导出配置**: 在 `packages/shared-ui/package.json` 的 `exports` 中添加 `"./feature-voting": "./src/feature-voting/index.ts"`。
*   **核心组件**:
    *   `FeatureRequestList`: 渲染特性列表。
    *   `FeatureRequestItem`: 渲染单个特性，包含标题、描述、状态和投票按钮。
    *   `SubmitFeatureForm`: 用户提交新建议的表单。
*   **管理组件**:
    *   `ProductManager`: 用于后台管理产品和其关联的特性请求。
    *   `FeatureRequestForm`: 用于管理员创建和编辑特性的表单。

### 3.4. 匿名用户身份识别策略

为了支持匿名用户投票和评论，我们需要实现一套身份识别机制：

*   **前端身份生成**:
    *   使用 `crypto.randomUUID()` 或类似方法为每个匿名用户生成唯一的 `anonymousId`
    *   将 `anonymousId` 存储在 `localStorage` 中，确保用户在同一浏览器中的一致性
    *   可选：结合浏览器指纹技术（如 screen resolution, timezone, language 等）增强唯一性

*   **防刷票机制**:
    *   记录用户的 IP 地址和 User-Agent
    *   限制同一 IP 在短时间内的投票频率
    *   可选：使用 CAPTCHA 验证可疑行为

*   **数据一致性**:
    *   当匿名用户注册登录后，提供迁移机制将其匿名投票和评论关联到用户账户
    *   保持投票的唯一性约束（同一特性每人只能投一票）

### 3.5. 应用集成 (`packages/app`)

*   **后台管理页面**:
    *   路径: `app/(admin)/feature-requests/page.tsx`
    *   内容: 导入并使用 `@repo/shared-ui/feature-voting` 中的 `ProductManager` 组件，实现完整的后台管理功能。
*   **前端展示页面**:
    *   路径: `app/feedback/page.tsx` (或类似路径)
    *   内容: 导入并使用 `FeatureRequestList` 和 `SubmitFeatureForm` 组件，向用户提供反馈渠道。

## 4. 任务清单 (Task To-Do List)

将按优先级顺序排列以下任务。

### 优先级 P0 (最高 - 核心功能)
-   [x] **任务 1 (数据库)**: 更新 `schema.prisma` 文件，添加 `Product`, `FeatureRequest`, `FeatureVote`, `FeatureComment`, `FeatureSubscription` 模型，支持匿名用户。
-   [x] **任务 2 (数据库)**: 更新现有 `User` 模型，添加 Feature Voting 模块的关联关系。
-   [x] **任务 3 (数据库)**: 运行数据库迁移 `pnpm --filter database migrate --name "add_feature_voting_module"` 并生成客户端。
-   [x] **任务 4 (工具)**: 实现匿名用户身份识别工具，包括 `anonymousId` 生成和 localStorage 管理。
-   [x] **任务 5 (API)**: 创建后台 API 端点，用于管理产品 (`/api/admin/products`) 和特性 (`/api/admin/feature-requests`)。
-   [x] **任务 6 (API)**: 创建前端 API 端点，支持登录和匿名用户的投票功能 (`POST|DELETE /api/feature-requests/:id/vote`)。
-   [x] **任务 7 (工具)**: 在 `@repo/utils` 中创建功能投票模块的类型定义、API客户端和工具函数。
-   [x] **任务 8 (Hook)**: 在 `@repo/utils` 中创建 React Hook，包含匿名投票功能。
-   [x] **任务 9 (UI)**: 在 `@repo/utils` 中创建可复用的UI组件，包含投票按钮、表单等。
-   [x] **任务 10 (页面)**: 创建后台管理页面 (`/app/admin/feature-requests`)，实现基本的特性增删改查。
    - 完整的特性请求管理界面
    - 创建、编辑、删除、查看详情功能
    - 状态和优先级管理
    - 搜索和过滤功能
    - 支持匿名用户信息管理
-   [x] **任务 11 (页面)**: 创建前端反馈页面 (`/app/feedback` 和 `/feedback`)，展示特性列表并允许登录和匿名用户投票。
    - 用户友好的反馈界面
    - 产品选择和过滤
    - 集成FeatureVotingBoard组件
    - 支持匿名投票和建议提交
    - 响应式设计和用户指导

### 优先级 P1 (中等 - 完善体验)
-   [ ] **任务 12 (API)**: 创建 API 端点，允许登录和匿名用户提交新的功能建议 (`POST /api/feature-requests`)。
-   [ ] **任务 13 (UI)**: 在 `@repo/utils` 中创建 `SubmitFeatureForm` 组件，支持匿名用户填写昵称和邮箱。
-   [ ] **任务 14 (页面)**: 在前端反馈页面集成 `SubmitFeatureForm`。
-   [ ] **任务 15 (API)**: 完善后台 API，支持更新特性的 `status` 字段。
-   [ ] **任务 16 (UI)**: 在 `FeatureRequestItem` 组件中清晰地展示特性的状态（如使用不同颜色的标签）。
-   [ ] **任务 17 (API)**: 创建 API 端点，用于获取和提交评论，支持匿名用户。
-   [ ] **任务 18 (UI)**: 为 `FeatureRequestItem` 添加评论区功能，支持匿名评论。
-   [ ] **任务 19 (API)**: 实现邮箱订阅功能，允许匿名用户订阅特性更新通知。
-   [ ] **任务 20 (防刷票)**: 实现基础的防刷票机制，包括 IP 限制和频率控制。
-   [ ] **任务 21 (文档)**: 为新的 `@repo/utils/feature-voting` 模块编写基础的 README 文档。

### 优先级 P2 (最低 - 优化与扩展)
-   [ ] **任务 22 (UI/API)**: 为特性列表添加排序（按票数、时间）和筛选（按状态）功能。
-   [ ] **任务 23 (后台)**: 实现邮件通知功能，当用户关注的特性状态变更时提醒用户。
-   [ ] **任务 24 (后台)**: 在管理后台添加合并重复功能建议的逻辑。
-   [ ] **任务 25 (后台)**: 创建一个数据仪表盘，可视化展示投票数据和趋势。
-   [ ] **任务 26 (迁移)**: 实现匿名用户数据迁移功能，当匿名用户注册后将其投票和评论关联到账户。
-   [ ] **任务 27 (安全)**: 增强防刷票机制，包括 CAPTCHA 验证和更复杂的行为分析。
-   [ ] **任务 28 (分析)**: 添加匿名用户行为分析，帮助了解用户参与模式。

## 5. 实施建议

### 5.1. 开发顺序
建议按照优先级 P0 -> P1 -> P2 的顺序进行开发，确保核心功能先上线，然后逐步完善用户体验。

### 5.2. 测试策略
*   **单元测试**: 为所有 API 端点编写测试用例。
*   **集成测试**: 测试前端组件与 API 的交互。
*   **用户测试**: 在小范围内测试用户体验，收集反馈后再全面推广。

### 5.3. 部署考虑
*   **数据库迁移**: 确保在生产环境中安全地执行数据库迁移。
*   **缓存策略**: 考虑为特性列表和投票数据添加适当的缓存。
*   **性能监控**: 监控新功能对系统性能的影响。

### 5.4. 安全考虑
*   **权限控制**: 确保只有管理员能访问后台管理功能。
*   **防刷票**: 实施适当的防刷票机制，包括：
    *   IP 地址限制和频率控制
    *   匿名用户身份验证（基于 localStorage 和浏览器指纹）
    *   可疑行为检测（如短时间内大量投票）
*   **内容审核**: 对用户提交的建议进行适当的内容过滤。
*   **数据隐私**: 
    *   匿名用户的 IP 地址和 User-Agent 仅用于防刷票，不用于用户追踪
    *   邮箱信息仅用于通知，不用于营销
    *   提供数据删除机制，允许用户删除其匿名数据
*   **防止滥用**:
    *   对匿名评论实施内容过滤和举报机制
    *   限制匿名用户的操作频率
    *   提供管理员审核工具

---

**文档版本**: v1.0  
**创建日期**: 2024年  
**最后更新**: 2024年
