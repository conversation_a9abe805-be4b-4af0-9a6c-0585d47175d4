"use client";

import { MapPin } from "lucide-react";
import type { BaseMarkerProps } from "./types";

interface ClassicMarkerProps extends BaseMarkerProps {
	color?: string;
	hideOutline?: boolean;
}

export function ClassicMarker({
	point,
	onRemovePoint,
	isSelected = false,
	scale = 1,
	color = "rgb(59, 130, 246)", // blue-500
	hideOutline = false,
}: ClassicMarkerProps) {
	const size = 32 * scale;

	return (
		<div className="relative group">
			{hideOutline ? (
				// 无轮廓模式 - 只显示图标
				<div
					className="flex items-center justify-center cursor-pointer hover:scale-110 transition-all duration-200"
					style={{
						width: `${size}px`,
						height: `${size}px`,
						transform: isSelected ? "scale(1.2)" : "scale(1)",
					}}
				>
					<MapPin
						className="w-6 h-6 drop-shadow-lg"
						style={{ color: color }}
					/>
				</div>
			) : (
				// 标准模式 - 带轮廓
				<div
					className="rounded-full border-2 border-white shadow-lg flex items-center justify-center cursor-pointer hover:scale-110 transition-all duration-200"
					style={{
						width: `${size}px`,
						height: `${size}px`,
						backgroundColor: color,
						transform: isSelected ? "scale(1.2)" : "scale(1)",
					}}
				>
					<MapPin className="w-4 h-4 text-white" />
				</div>
			)}
		</div>
	);
}
