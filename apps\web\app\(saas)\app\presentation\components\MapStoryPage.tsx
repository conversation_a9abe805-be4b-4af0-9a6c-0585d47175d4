"use client";

import "mapbox-gl/dist/mapbox-gl.css";
import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";
import { useEffect, useRef } from "react";
import { StoryState } from "../constants";
import { useMapStory } from "../hooks";
import { createLogger } from "../hooks/map-story/utils";
import type { FrontendTravelPoint, TravelDiary } from "../types";
import { CompletionPrompt } from "./CompletionPrompt";
import { ContentArea } from "./ContentArea";
import { CountdownPrompt } from "./CountdownPrompt";
import { Lightbox } from "./Lightbox";
import { MapComponent } from "./MapComponent";
import { MapStoryStyles } from "./MapStoryStyles";
import { PlaybackControls } from "./PlaybackControls";
import { SpotlightEffect } from "./SpotlightEffect";
import { GlobalFontStyles } from "./common/GlobalFontStyles";

// 创建故事页面专用的日志记录器
const logger = createLogger("MapStoryPage");

// 辅助函数：cn - 用于条件性地连接className
function cn(...classes: (string | boolean | undefined | null)[]): string {
	return classes.filter(Boolean).join(" ");
}

interface MapStoryPageProps {
	diary: TravelDiary;
	points: FrontendTravelPoint[];
	onComplete: () => void;
}

// 在主组件之前添加一个图片预加载工具函数
const preloadPointImages = (points: FrontendTravelPoint[]) => {
	// 收集所有图片URL
	const allImages = points.reduce<string[]>((acc, point) => {
		if (
			point.images &&
			Array.isArray(point.images) &&
			point.images.length > 0
		) {
			// 只添加唯一的URL，避免重复加载同一图片
			point.images.forEach((img) => {
				if (!acc.includes(img.url)) {
					acc.push(img.url);
				}
			});
		}
		return acc;
	}, []);

	// 对收集到的所有图片进行低优先级预加载
	if (allImages.length > 0) {
		// 使用requestIdleCallback进行低优先级加载（不影响主线程性能）
		const preloadImage = (url: string) => {
			const img = new Image();
			img.src = url;
			// 我们不需要处理onload和onerror，只是希望浏览器缓存这些图片
		};

		// 现代浏览器支持requestIdleCallback
		if (typeof window !== "undefined" && "requestIdleCallback" in window) {
			// 将预加载任务分批进行
			const batchSize = 5; // 每批预加载的图片数量
			for (let i = 0; i < allImages.length; i += batchSize) {
				const batch = allImages.slice(i, i + batchSize);
				window.requestIdleCallback(() => {
					batch.forEach(preloadImage);
				});
			}
		} else {
			// 回退方案，使用setTimeout并延迟低优先级加载
			setTimeout(() => {
				allImages.forEach(preloadImage);
			}, 1000); // 延迟1秒，让页面核心内容先加载
		}
	}
};

export function MapStoryPage({ diary, points, onComplete }: MapStoryPageProps) {
	const t = useTranslations("travelMemo.mapStoryPage");

	// Analytics tracking (direct Plausible)
	const trackEvent = (eventName: string, props?: Record<string, any>) => {
		if (typeof window !== "undefined" && window.plausible) {
			window.plausible(eventName, { props });
		}
	};

	// 检查是否处于导出视频模式
	const searchParams = useSearchParams();
	const isExportMode = searchParams.get("export") === "video";
	const autoPlay = searchParams.get("autoPlay") === "true";
	const pointDuration = Number(searchParams.get("pointDuration") || "5");

	// 记录是否已经完成播放的ref（用于视频导出模式）
	const completedRef = useRef(false);
	// 自动播放触发ID，用于防止重复触发
	const autoPlayTriggerIdRef = useRef<string>(`autoplay-${Date.now()}`);

	// 图片预加载状态参考
	const imagePreloadStartedRef = useRef(false);

	// 使用自定义hook管理所有状态和逻辑
	const {
		// 状态
		storyState,
		isPlaying,
		isMapReady,
		currentPointIndex,
		currentPoint,
		currentImages,
		showPointInfo,
		showImages,
		lightboxOpen,
		selectedImageIndex,
		flyToPointIndex,
		fitBoundsToPoints,
		spotlightPosition,
		blinkingPoints,
		autoPlayCountdown,
		showRestartPrompt,
		manuallyReset,

		// Refs
		activeMarkerRef,

		// 回调函数
		setLightboxOpen,
		handleMapLoad,
		handlePointArrival,
		handleMarkerClick,
		handleImageClick,
		handlePrevImage,
		handleNextImage,
		handleSliderChange,
		togglePlay,
		goToPrevPoint,
		goToNextPoint,
		resetPresentation,
		viewMemories,
		startJourney,
		onTypingComplete,
	} = useMapStory({
		points,
		onComplete,
		// 如果是导出模式，则传入相关配置
		config: isExportMode
			? {
					isExportMode: true,
					autoPlay,
					pointDuration,
				}
			: undefined,
	});

	// 页面加载时预加载所有点位图片
	useEffect(() => {
		// 确保只执行一次预加载
		if (!imagePreloadStartedRef.current && points.length > 0) {
			imagePreloadStartedRef.current = true;
			// 启动预加载流程，但不等待其完成
			preloadPointImages(points);
			logger.info("开始预加载所有点位图片", {
				totalPoints: points.length,
			});

			// Analytics: 追踪演示模式开始
			trackEvent("presentation_started", {
				diary_id: diary.id,
				points_count: points.length,
				is_export_mode: isExportMode,
			});
		}
	}, [points, diary.id, isExportMode, trackEvent]);

	// 跳过播放，直接完成并跳转到回顾页面
	const skipToEnd = () => {
		if (storyState !== StoryState.COMPLETED) {
			// Analytics: 追踪跳过到结尾
			trackEvent("presentation_skipped", {
				diary_id: diary.id,
				current_point_index: currentPointIndex,
				total_points: points.length,
			});

			// 直接调用onComplete回调函数，跳转到回顾页面
			onComplete();
		}
	};

	// 视频导出模式下的特殊处理
	useEffect(() => {
		if (!isExportMode) return;

		// 在导出模式下，如果故事状态为完成且未记录完成，则设置全局变量通知录制完成
		if (storyState === StoryState.COMPLETED && !completedRef.current) {
			completedRef.current = true;

			// 设置全局变量，通知Playwright脚本录制完成
			// 这将被 Playwright 脚本中的 waitForFunction 所检测
			window.storyExportCompleted = true;

			logger.info(t("logVideoExportStoryComplete"), { storyState });
		}
	}, [isExportMode, storyState, t]);

	// 导出模式下，自动开始播放（当地图准备好后）
	useEffect(() => {
		// 使用组件顶层定义的 ref
		const triggerId = autoPlayTriggerIdRef.current;
		let isTriggered = false;

		// 检查条件
		if (
			isExportMode &&
			isMapReady &&
			storyState === StoryState.OVERVIEW &&
			autoPlay
		) {
			logger.info("设置自动播放启动定时器", {
				isMapReady,
				storyState,
				autoPlay,
				triggerId,
			});

			// 短暂延迟后开始播放，确保地图完全加载
			const timer = setTimeout(() => {
				if (!isTriggered) {
					isTriggered = true;
					logger.info(t("logVideoExportMapReadyAutoPlay"), {
						isMapReady,
						storyState,
						triggerId,
					});
					startJourney();
				} else {
					logger.warn("自动播放已触发，跳过重复调用", {
						triggerId,
					});
				}
			}, 2000);

			return () => {
				clearTimeout(timer);
				logger.debug("清理自动播放定时器", {
					triggerId,
					wasTriggered: isTriggered,
				});
			};
		}

		return undefined;
	}, [isExportMode, isMapReady, storyState, autoPlay, startJourney, t]);

	// 当达到最后一个点位且播放完成时，调用onComplete回调
	useEffect(() => {
		// 检查是否已经播放到最后一个点并且当前是在播放状态
		const isLastPoint =
			currentPointIndex === points.length - 1 ||
			currentPointIndex === points.length;

		// 添加额外的日志，帮助调试完成状态
		if (isLastPoint) {
			logger.info("已到达最后一个点位附近", {
				currentPointIndex,
				totalPoints: points.length,
				storyState,
				completedFlag: completedRef.current,
			});
		}

		// 如果到达最后一个点位且状态为COMPLETED，触发完成回调
		if (
			isLastPoint &&
			storyState === StoryState.COMPLETED &&
			!completedRef.current
		) {
			logger.info("播放已完成，准备显示完成提示", {
				currentPointIndex,
				totalPoints: points.length,
				isExportMode,
			});
			completedRef.current = true;

			// 添加短暂延迟，确保状态更新和动画完成
			// 非导出模式下，让用户有时间阅读最后一个点位的信息
			const delay = isExportMode ? 1000 : 3000;
			setTimeout(() => {
				// 只有在导出模式下才调用onComplete，自动跳转到回顾页面
				if (isExportMode) {
					logger.info("导出模式：触发完成回调，跳转到回顾页面");
					onComplete();
				} else {
					logger.info("普通模式：不自动跳转到回顾页面，显示完成提示");
				}
			}, delay);
		}

		// 添加额外的检查，确保自动播放时能够正确完成
		// 当到达最后一个点位后一段时间，如果仍未转到完成状态，强制设置为完成
		if (
			autoPlay &&
			isLastPoint &&
			// 使用安全的方式检查状态是否不是完成状态
			(storyState === StoryState.OVERVIEW ||
				storyState === StoryState.PLAYING) &&
			!completedRef.current
		) {
			// 检测到可能是自动播放卡在最后一个点的情况
			const forceCompleteTimer = setTimeout(() => {
				logger.warn(
					"检测到自动播放可能卡在最后一个点，强制触发完成流程",
					{
						currentPointIndex,
						totalPoints: points.length,
						storyState,
						forceCompleteDelay: "10000ms",
						autoPlay,
					},
				);

				// 再次检查状态是否仍在非完成状态
				if (!completedRef.current) {
					logger.warn("强制触发完成回调");
					completedRef.current = true;

					// 只有在导出模式下调用onComplete
					if (isExportMode) {
						onComplete();
					}
				}
			}, 10000); // 10秒后如果还未完成，强制触发

			return () => clearTimeout(forceCompleteTimer);
		}
	}, [
		currentPointIndex,
		points.length,
		storyState,
		isExportMode,
		onComplete,
		searchParams,
	]);

	return (
		<div className="relative h-screen w-full bg-slate-100 overflow-hidden travel-memo-content">
			{/* 引入全局字体样式 */}
			<GlobalFontStyles />

			{/* 地图组件 */}
			<MapComponent
				mapboxToken={process.env.NEXT_PUBLIC_MAPBOX_TOKEN || ""}
				points={points}
				currentPointIndex={
					// 在完成状态下，将所有点标记为已访问
					storyState === StoryState.COMPLETED
						? points.length
						: // 设置为大于最后一个点的索引，确保所有点都标为已访问
							currentPointIndex <= 0
							? -1
							: currentPointIndex - 1 // 普通状态下的逻辑
				}
				onMapLoad={handleMapLoad}
				onMarkerClick={handleMarkerClick}
				onPointArrival={handlePointArrival}
				flyToPointIndex={flyToPointIndex}
				fitBoundsToPoints={fitBoundsToPoints}
				activeMarkerRef={activeMarkerRef}
				blinkingPoints={
					storyState === StoryState.OVERVIEW ? blinkingPoints : []
				}
				showOverviewMode={storyState === StoryState.OVERVIEW}
				showCompletedMode={storyState === StoryState.COMPLETED}
			/>

			{/* 聚光灯效果 */}
			<SpotlightEffect
				position={spotlightPosition}
				isVisible={showPointInfo}
			/>

			{/* 加载状态 */}
			{!isMapReady && (
				<div className="absolute inset-0 flex items-center justify-center bg-white/80 z-20">
					<div className="flex flex-col items-center">
						<div className="animate-spin h-10 w-10 border-t-2 border-b-2 border-primary rounded-full mb-3" />
						<p className="text-muted-foreground travel-memo-text">
							{t("loadingMap")}
						</p>
					</div>
				</div>
			)}

			{/* 自动播放倒计时提示 */}
			<CountdownPrompt
				countdown={autoPlayCountdown}
				isVisible={
					storyState === StoryState.OVERVIEW &&
					autoPlayCountdown > 0 &&
					!manuallyReset
				}
			/>

			{/* 完成状态提示 */}
			<CompletionPrompt
				isVisible={
					storyState === StoryState.COMPLETED &&
					showRestartPrompt &&
					!isExportMode
				}
				onViewMemories={viewMemories}
			/>

			{/* 开发环境调试信息 */}
			{process.env.NODE_ENV !== "production" && (
				<div className="fixed top-16 right-2 z-50 bg-black/70 text-white text-xs p-2 rounded max-w-xs">
					状态: {storyState} | 点位: {currentPointIndex}/
					{points.length} | 显示完成提示:{" "}
					{storyState === StoryState.COMPLETED && showRestartPrompt
						? "是"
						: "否"}
				</div>
			)}

			{/* 内容区域 - 图片画廊和点位信息 */}
			{currentPoint && (showPointInfo || showImages) && (
				<ContentArea
					currentPoint={currentPoint}
					images={currentImages}
					showPointInfo={showPointInfo}
					showImages={showImages}
					onImageClick={handleImageClick}
					onTypingComplete={onTypingComplete}
				/>
			)}

			{/* 图片查看器 */}
			<Lightbox
				images={currentImages}
				initialIndex={selectedImageIndex}
				isOpen={lightboxOpen}
				onClose={() => setLightboxOpen(false)}
				onPrev={handlePrevImage}
				onNext={handleNextImage}
			/>

			{/* 控制面板 - 在导出模式下隐藏控制面板 */}
			{!isExportMode && storyState !== StoryState.COMPLETED && (
				<PlaybackControls
					isPlaying={isPlaying}
					currentPointIndex={currentPointIndex}
					totalPoints={points.length}
					storyState={storyState}
					onPlayPause={togglePlay}
					onNext={
						storyState === StoryState.OVERVIEW
							? startJourney
							: goToNextPoint
					}
					onPrev={goToPrevPoint}
					onSliderChange={handleSliderChange}
					onReset={resetPresentation}
					onSkipToEnd={skipToEnd}
				/>
			)}

			{/* 全局样式 */}
			<MapStoryStyles />
		</div>
	);
}

// 为 TypeScript 全局声明添加 storyExportCompleted 属性
declare global {
	interface Window {
		storyExportCompleted?: boolean;
		plausible?: (
			eventName: string,
			options?: { props?: Record<string, any> },
		) => void;
	}
}
