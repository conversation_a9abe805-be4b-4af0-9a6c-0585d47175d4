import { createProvider } from "./providers";
import { type AIModelConfig, type AIProvider, AIProviderType } from "./types";

/**
 * AI客户端类，提供统一的接口来使用不同的AI提供商
 */
export class AIClient {
	private provider: AIProvider;

	constructor(provider: AIProvider) {
		this.provider = provider;
	}

	/**
	 * 生成文本响应
	 * @param prompt 提示词或提示词对象（包含系统提示和用户提示）
	 * @param options 选项
	 */
	async generateText(
		prompt: string | { systemPrompt?: string; userPrompt: string },
		options?: any,
	): Promise<string> {
		// 处理提示词格式
		if (typeof prompt === "string") {
			// 如果是字符串，转换为标准格式
			return this.provider.generateText({
				userPrompt: prompt,
				systemPrompt: options?.systemPrompt,
				maxTokens: options?.maxTokens,
				temperature: options?.temperature,
			});
		}

		// 如果是对象，直接使用
		return this.provider.generateText({
			systemPrompt: prompt.systemPrompt,
			userPrompt: prompt.userPrompt,
			maxTokens: options?.maxTokens,
			temperature: options?.temperature,
		});
	}

	/**
	 * 使用提示词模板生成文本
	 * @param promptTemplate 提示词模板函数
	 * @param params 提示词参数
	 * @param options 额外选项
	 */
	async generateFromTemplate<T extends any[]>(
		promptTemplate: (...args: T) => {
			systemPrompt: string;
			userPrompt: string;
		},
		params: T,
		options?: any,
	): Promise<string> {
		// 调用提示词模板函数获取格式化的提示词
		const { systemPrompt, userPrompt } = promptTemplate(...params);

		// 调用生成文本方法
		return this.generateText(
			{
				systemPrompt,
				userPrompt,
			},
			options,
		);
	}

	/**
	 * 生成图像
	 * @param prompt 提示词
	 * @param options 选项
	 */
	async generateImage(prompt: string, options?: any): Promise<string> {
		if (
			!this.provider.supportedFeatures.image ||
			!this.provider.generateImage
		) {
			throw new Error(`提供商 ${this.provider.name} 不支持图像生成`);
		}
		return this.provider.generateImage(prompt, options);
	}

	/**
	 * 语音转文本
	 * @param audioData 音频数据
	 * @param options 选项
	 */
	async transcribeAudio(
		audioData: Blob | Buffer,
		options?: any,
	): Promise<string> {
		if (
			!this.provider.supportedFeatures.audio ||
			!this.provider.transcribeAudio
		) {
			throw new Error(`提供商 ${this.provider.name} 不支持语音转写`);
		}
		return this.provider.transcribeAudio(audioData, options);
	}

	/**
	 * 分析图片内容并回答问题
	 * @param imageUrl 图片URL
	 * @param prompt 提示词/问题
	 * @param options 选项
	 * @returns 分析结果
	 */
	async analyzeImage(
		imageUrl: string,
		prompt: string,
		options?: any,
	): Promise<string> {
		if (
			!this.provider.supportedFeatures.imageAnalysis ||
			!this.provider.analyzeImage
		) {
			throw new Error(`提供商 ${this.provider.name} 不支持图片分析`);
		}
		return this.provider.analyzeImage(imageUrl, prompt, options);
	}

	/**
	 * 获取当前使用的AI提供商
	 */
	getProvider(): AIProvider {
		return this.provider;
	}

	/**
	 * 切换AI提供商
	 * @param provider 新的AI提供商
	 */
	setProvider(provider: AIProvider): void {
		this.provider = provider;
	}
}

/**
 * 创建AI客户端实例
 * @param config AI模型配置
 */
export function createAIClient(config: AIModelConfig): AIClient {
	const provider = createProvider(config.provider, {
		textModel: config.model,
		apiKey: config.apiKey,
		apiEndpoint: config.apiEndpoint,
		...config.options,
	});

	return new AIClient(provider);
}

/**
 * 创建基于OpenAI的AI客户端
 * @param model 模型名称
 * @param apiKey API密钥
 */
export function createOpenAIClient(
	model = "gpt-4o-mini",
	apiKey?: string,
): AIClient {
	return createAIClient({
		provider: AIProviderType.OPENAI,
		model,
		apiKey,
	});
}

/**
 * 创建基于Google Gemini的AI客户端
 * @param apiKey API密钥
 * @param apiEndpoint API端点
 */
export function createGeminiClient(
	apiKey: string,
	apiEndpoint?: string,
): AIClient {
	return createAIClient({
		provider: AIProviderType.GEMINI,
		model: "gemini-pro",
		apiKey,
		apiEndpoint,
	});
}

/**
 * 创建基于火山引擎的AI客户端
 * @param apiKey API密钥
 * @param secretKey 秘钥
 * @param apiEndpoint API端点
 */
export function createVolcengineClient(
	apiKey: string,
	secretKey: string,
	apiEndpoint?: string,
): AIClient {
	return createAIClient({
		provider: AIProviderType.VOLCENGINE,
		model: "ep-20250405133902-vrgn4",
		apiKey,
		options: {
			secretKey,
		},
		apiEndpoint,
	});
}
