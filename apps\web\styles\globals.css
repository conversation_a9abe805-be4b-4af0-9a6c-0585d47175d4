/* Mapbox 自定义样式 */
@import url("./mapbox-custom.css");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	@font-face {
		font-family: "Poppins";
		src: url("/fonts/Poppins-Regular.woff2") format("woff2");
		font-weight: 400;
		font-style: normal;
		font-display: swap;
	}

	@font-face {
		font-family: "Poppins";
		src: url("/fonts/Poppins-Medium.woff2") format("woff2");
		font-weight: 500;
		font-style: normal;
		font-display: swap;
	}

	@font-face {
		font-family: "Poppins";
		src: url("/fonts/Poppins-Bold.woff2") format("woff2");
		font-weight: 700;
		font-style: normal;
		font-display: swap;
	}

	:root {
		--font-sans:
			"Poppins", system-ui, -apple-system, BlinkMacSystemFont,
			"Segoe UI", Roboto, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif,
			"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
	}

	body {
		font-family: var(--font-sans);
	}
}
