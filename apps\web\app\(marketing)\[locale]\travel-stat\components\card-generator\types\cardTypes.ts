export interface TravelStats {
	totalPoints: number;
	citiesCount: number;
	countriesCount: number;
	totalDistance?: number;
	totalDays?: number;
}

// 旅行点数据
export interface TravelPoint {
	id: string;
	name: string;
	city: string;
	country: string;
	coordinates: [number, number]; // [lng, lat]
	date: string;
	photos?: string[];
	description?: string;
	tags?: string[];
}

// 国家数据
export interface CountryData {
	code: string;
	name: string;
	visitedCities: string[];
	totalPoints: number;
	firstVisit: string;
	lastVisit: string;
}

export interface UserProfile {
	username?: string;
	avatar?: string;
	bio?: string;
}

export interface MapImageData {
	blob: Blob;
	dataURL: string;
	dimensions: {
		width: number;
		height: number;
		scale: number;
	};
	metadata: {
		timestamp: Date;
		quality: number;
		method: string;
	};
}

export interface CardData {
	travelStats: TravelStats;
	mapImageData: MapImageData;
	userProfile?: UserProfile;
	generatedAt: Date;
}

export interface CardCustomization {
	// 颜色自定义
	colors?: {
		primary?: string;
		secondary?: string;
		accent?: string;
		background?: string;
		text?: string;
	};
	// 字体自定义
	typography?: {
		fontFamily?: string;
		headerSize?: number;
		bodySize?: number;
		titleWeight?: number;
	};
	// 布局自定义
	layout?: {
		padding?: number;
		spacing?: number;
		borderRadius?: number;
		showShadow?: boolean;
	};
	// 内容自定义
	content?: {
		showDate?: boolean;
		customDate?: string; // 新增：自定义日期文本
		showUserInfo?: boolean;
		showDetailedStats?: boolean;
		customTitle?: string;
		customFooter?: string;
	};
}

// 社交平台类型
export type SocialPlatform =
	| "instagram"
	| "wechat"
	| "weibo"
	| "twitter"
	| "facebook";

// 导出质量类型
export type ExportQuality = "low" | "medium" | "high";

export interface SocialPlatformConfig {
	instagram: { width: 1080; height: 1080 };
	wechat: { width: 1200; height: 900 };
	weibo: { width: 1080; height: 1350 };
	twitter: { width: 1200; height: 675 };
	facebook: { width: 1200; height: 630 };
}

export interface CardProps {
	data: CardData;
	customization: CardCustomization;
	targetPlatform: SocialPlatform;
	className?: string;
}

// 卡片模板组件的props
export interface CardTemplateProps {
	mapImageData?: {
		dataURL: string;
		dimensions: { width: number; height: number };
	};
	mapComponent?: React.ReactNode; // 新增：支持传入真实地图组件
	travelPoints: TravelPoint[];
	visitedCountries: CountryData[];
	customization: CardCustomization;
	platform?: string;
	isPreview?: boolean; // 新增：标识是否为预览模式
}

export interface CustomizationOption {
	key: string;
	label: string;
	type: "color" | "font" | "size" | "toggle" | "select" | "slider";
	category: "colors" | "typography" | "layout" | "content";
	options?: string[] | number[];
	min?: number;
	max?: number;
	step?: number;
	default: any;
}

export interface CardTemplate {
	id: string;
	name: string;
	description: string;
	preview: string;
	category?: string;
	component: React.ComponentType<CardTemplateProps>;
	colors: {
		primary: string;
		secondary: string;
		accent: string;
		background: string;
		text: string;
	};
	typography: {
		fontFamily: string;
		headerSize: number;
		bodySize: number;
		titleWeight: number;
	};
	layout: {
		padding: number;
		spacing: number;
		borderRadius: number;
		showShadow: boolean;
	};
	features: string[];
	supportedPlatforms?: SocialPlatform[];
	customizationOptions?: CustomizationOption[];
	defaultCustomization?: CardCustomization;
}

export interface CardExportOptions {
	platform: SocialPlatform;
	quality: ExportQuality;
	filename?: string;
}

export interface CardExportResult {
	success: boolean;
	blob?: Blob;
	dataURL?: string;
	filename?: string;
	width?: number;
	height?: number;
	error?: string;
}
