import type { FrontendTravelDiary } from "@packages/database/src/types";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Camera, ChevronDown, ChevronUp, Eye, Loader2 } from "lucide-react";
import { useTranslations } from "next-intl";
import { useRef, useState } from "react";
import { toast } from "sonner";

interface DiaryHeaderProps {
	diary: FrontendTravelDiary;
	diaryId: string;
	isHeaderCompact: boolean;
	isRichTextMode: boolean;
	isModeTransitioning: boolean;
	onToggleHeaderMode: () => void;
	onToggleEditMode: () => void;
	onTitleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
	onSubtitleChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
	onKeyDown: (
		e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>,
	) => void;
}

export function DiaryHeader({
	diary,
	diaryId,
	isHeaderCompact,
	isRichTextMode,
	isModeTransitioning,
	onToggleHeaderMode,
	onToggleEditMode,
	onTitleChange,
	onSubtitleChange,
	onKeyDown,
}: DiaryHeaderProps) {
	const t = useTranslations("travelMemo.diaryEditor");
	const [isUploadingCover, setIsUploadingCover] = useState<boolean>(false);
	const fileInputRef = useRef<HTMLInputElement>(null);

	const handleCoverImageChange = async (
		e: React.ChangeEvent<HTMLInputElement>,
	) => {
		if (!diary || !e.target.files || e.target.files.length === 0) return;

		const file = e.target.files[0];
		e.target.value = "";

		setIsUploadingCover(true);
		toast.info(t("toasts.coverImageUploading"));

		try {
			const presignResponse = await fetch("/api/storage/upload", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify({
					filename: file.name,
					contentType: file.type,
				}),
			});

			if (!presignResponse.ok) {
				const errorData = await presignResponse.json();
				throw new Error(
					t("errors.getUploadUrlFailed", {
						detail: errorData.error || presignResponse.statusText,
					}),
				);
			}

			const { uploadUrl, permanentUrl } = await presignResponse.json();

			if (!uploadUrl || !permanentUrl) {
				throw new Error(t("errors.missingUrlInApiResponse"));
			}

			const uploadResponse = await fetch(uploadUrl, {
				method: "PUT",
				headers: {
					"Content-Type": file.type,
				},
				body: file,
			});

			if (!uploadResponse.ok) {
				throw new Error(
					t("errors.coverUploadFailed", {
						detail: uploadResponse.statusText,
					}),
				);
			}

			console.log(t("logs.coverUploadSuccess"), permanentUrl);
			// 这里需要通过props回调来更新diary状态
			// onCoverImageUpdate?.(permanentUrl);
			toast.success(t("toasts.coverImageUpdated"));
		} catch (error) {
			console.error(t("errors.coverUploadProcessFailed"), error);
			let errorMessage = t("errors.coverUploadFailedSimple");
			if (error instanceof Error) {
				errorMessage = `${errorMessage}: ${error.message}`;
			}
			toast.error(errorMessage);
		} finally {
			setIsUploadingCover(false);
		}
	};

	const triggerFileInput = () => {
		if (!isUploadingCover) {
			fileInputRef.current?.click();
		}
	};

	if (isRichTextMode) {
		return null; // 富文本模式下不显示头部
	}

	return (
		<div
			className={`relative w-full overflow-hidden diary-header-transition ${
				isHeaderCompact ? "h-16" : "h-52"
			}`}
			style={{
				backgroundImage: `url(${diary.coverImage || "/images/default-cover.jpg"})`,
				backgroundSize: "cover",
				backgroundPosition: "center",
			}}
		>
			<div className="absolute inset-0 bg-gradient-to-t from-black/80 to-black/20">
				{/* 紧凑模式下的顶部布局 */}
				{isHeaderCompact ? (
					<div className="flex items-center justify-between h-full px-4 py-2 animate-slide-down">
						{/* 左侧标题区域 */}
						<div className="flex-1 min-w-0 pr-4">
							<div className="relative group">
								<Input
									value={diary.title}
									onChange={onTitleChange}
									onKeyDown={onKeyDown}
									placeholder={t("header.titlePlaceholder")}
									className="bg-transparent border border-transparent hover:border-white/30 focus:border-white/40 focus:ring-2 focus:ring-white/30 text-left p-1 px-2 h-auto w-full placeholder:text-gray-300 text-white text-base font-semibold truncate diary-title-smooth"
									style={{
										textShadow:
											"1px 1px 4px rgba(0,0,0,0.9)",
									}}
								/>
								<div className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none">
									<span className="bg-white/30 backdrop-blur-sm text-white px-2 py-0.5 rounded-md text-xs shadow-sm">
										{t("header.editSaveHint")}
									</span>
								</div>
							</div>
						</div>

						{/* 右侧按钮区域 */}
						<div className="flex items-center gap-1.5 flex-shrink-0 animate-slide-left">
							<Button
								variant="outline"
								size="icon"
								onClick={onToggleHeaderMode}
								className="bg-white/90 hover:bg-blue-50 text-blue-700 border-blue-200/60 diary-button h-8 w-8 p-1"
								title={t("header.expandButton")}
							>
								<ChevronDown className="h-3.5 w-3.5" />
							</Button>
							<Button
								variant="outline"
								size="icon"
								className="bg-white/90 hover:bg-blue-50 text-blue-700 border-blue-200/60 diary-button disabled:opacity-50 disabled:cursor-not-allowed h-8 w-8 p-1"
								onClick={triggerFileInput}
								title={
									isUploadingCover
										? t("toasts.coverImageUploading")
										: t("header.changeCoverButton")
								}
								disabled={isUploadingCover}
							>
								{isUploadingCover ? (
									<Loader2 className="h-3.5 w-3.5 animate-spin" />
								) : (
									<Camera className="h-3.5 w-3.5" />
								)}
							</Button>
							<Button
								variant="outline"
								size="icon"
								className="bg-white/90 hover:bg-blue-50 text-blue-700 border-blue-200/60 diary-button h-8 w-8 p-1"
								onClick={() =>
									window.open(
										`/app/presentation/${diaryId}`,
										"_blank",
									)
								}
								title={t("header.presentationModeButton")}
							>
								<Eye className="h-3.5 w-3.5" />
							</Button>
							<Button
								variant="outline"
								size="icon"
								onClick={onToggleEditMode}
								disabled={isModeTransitioning}
								className="bg-white/90 hover:bg-blue-50 text-blue-700 border-blue-200/60 diary-button disabled:opacity-50 h-8 w-8 p-1"
								title={
									isModeTransitioning
										? "切换中..."
										: t("header.switchToRichTextModeButton")
								}
							>
								{isModeTransitioning ? (
									<Loader2 className="h-3 w-3 animate-spin" />
								) : (
									<span className="font-serif text-xs">
										T
									</span>
								)}
							</Button>
						</div>
					</div>
				) : (
					/* 展开模式下的居中布局 */
					<div className="flex flex-col justify-center items-center h-full p-4 animate-fade-in-up">
						<div className="relative w-full max-w-xl group">
							<Input
								value={diary.title}
								onChange={onTitleChange}
								onKeyDown={onKeyDown}
								placeholder={t("header.titlePlaceholder")}
								className="bg-transparent border border-transparent hover:border-white/30 focus:border-white/40 focus:ring-2 focus:ring-white/30 text-center p-0 h-auto w-full placeholder:text-gray-300 text-white diary-title text-4xl font-extrabold mb-3 diary-title-smooth"
								style={{
									textShadow: "1px 1px 4px rgba(0,0,0,0.9)",
								}}
							/>
							<div className="absolute right-3 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none">
								<span className="bg-white/30 backdrop-blur-sm text-white px-2 py-0.5 rounded-md text-xs shadow-sm">
									{t("header.editSaveHint")}
								</span>
							</div>
							<div className="absolute -left-6 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-80 transition-all duration-300 pointer-events-none">
								<span className="text-white text-lg">✏️</span>
							</div>
						</div>

						{/* 副标题区域 - 只在展开模式显示 */}
						<div className="relative w-full max-w-3xl group mt-2 transform transition-all duration-700 ease-out delay-150">
							<textarea
								value={diary.subtitle || ""}
								onChange={onSubtitleChange}
								onKeyDown={onKeyDown}
								placeholder={t("header.subtitlePlaceholder")}
								rows={2}
								className="text-lg text-gray-100 bg-transparent border border-transparent hover:border-white/30 focus:border-white/40 focus:ring-2 focus:ring-white/30 text-center p-0 w-full placeholder:text-gray-400 diary-subtitle resize-none overflow-hidden transition-all duration-700 ease-out"
								style={{
									textShadow: "1px 1px 3px rgba(0,0,0,0.8)",
									minHeight: "3rem",
								}}
							/>
							<div className="absolute right-3 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none">
								<span className="bg-white/30 backdrop-blur-sm text-white px-2 py-0.5 rounded-md text-xs shadow-sm">
									{t("header.editSaveHint")}
								</span>
							</div>
							<div className="absolute -left-6 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-80 transition-all duration-300 pointer-events-none">
								<span className="text-white text-lg">✏️</span>
							</div>
						</div>

						{/* 展开模式下的按钮 - 绝对定位在右下角 */}
						<div className="absolute bottom-2 right-2 flex gap-2 transform transition-all duration-500 ease-out delay-300">
							<Button
								variant="outline"
								size="icon"
								onClick={onToggleHeaderMode}
								className="bg-white/90 hover:bg-blue-50 text-blue-700 border-blue-200/60 diary-button"
								title={t("header.collapseButton")}
							>
								<ChevronUp className="h-4 w-4" />
							</Button>
							<Button
								variant="outline"
								size="icon"
								className="bg-white/90 hover:bg-blue-50 text-blue-700 border-blue-200/60 diary-button disabled:opacity-50 disabled:cursor-not-allowed"
								onClick={triggerFileInput}
								title={
									isUploadingCover
										? t("toasts.coverImageUploading")
										: t("header.changeCoverButton")
								}
								disabled={isUploadingCover}
							>
								{isUploadingCover ? (
									<Loader2 className="h-4 w-4 animate-spin" />
								) : (
									<Camera className="h-4 w-4" />
								)}
							</Button>
							<Button
								variant="outline"
								size="icon"
								className="bg-white/90 hover:bg-blue-50 text-blue-700 border-blue-200/60 diary-button"
								onClick={() =>
									window.open(
										`/app/presentation/${diaryId}`,
										"_blank",
									)
								}
								title={t("header.presentationModeButton")}
							>
								<Eye className="h-4 w-4" />
							</Button>
							<Button
								variant="outline"
								size="icon"
								onClick={onToggleEditMode}
								disabled={isModeTransitioning}
								className="bg-white/90 hover:bg-blue-50 text-blue-700 border-blue-200/60 diary-button disabled:opacity-50"
								title={
									isModeTransitioning
										? "切换中..."
										: t("header.switchToRichTextModeButton")
								}
							>
								{isModeTransitioning ? (
									<Loader2 className="h-3 w-3 animate-spin" />
								) : (
									<span className="font-serif text-xs">
										T
									</span>
								)}
							</Button>
						</div>
					</div>
				)}
			</div>

			<input
				type="file"
				ref={fileInputRef}
				onChange={handleCoverImageChange}
				accept="image/*"
				className="hidden"
				disabled={isUploadingCover}
			/>
		</div>
	);
}
