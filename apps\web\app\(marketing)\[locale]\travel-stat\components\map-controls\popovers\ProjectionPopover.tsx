"use client";

import {
	Circle,
	Compass,
	Globe,
	Grid3X3,
	Map as MapIcon,
	Navigation,
	Square,
	Target,
} from "lucide-react";
import { useTranslatedProjections } from "../../../hooks/useTranslatedProjections";
import type { MapProjectionType } from "../../types";
import { PopoverContainer } from "./PopoverContainer";

interface ProjectionPopoverProps {
	mapProjection: MapProjectionType;
	onProjectionChange: (projection: MapProjectionType) => void;
}

const PROJECTION_ICONS: Record<
	MapProjectionType,
	React.ComponentType<{ className?: string }>
> = {
	globe: Globe,
	mercator: Navigation,
	equalEarth: Circle,
	naturalEarth: Target,
	winkelTripel: Compass,
	albers: Grid3X3,
	lambertConformalConic: MapIcon,
	equirectangular: Square,
};

export function ProjectionPopover({
	mapProjection,
	onProjectionChange,
}: ProjectionPopoverProps) {
	const { t, translatedProjections, getTranslatedProjection } =
		useTranslatedProjections();

	// 常用投影类型
	const commonProjectionKeys: MapProjectionType[] = [
		"globe",
		"mercator",
		"equalEarth",
		"naturalEarth",
	];
	// 专业投影类型
	const professionalProjectionKeys: MapProjectionType[] = [
		"winkelTripel",
		"albers",
		"lambertConformalConic",
		"equirectangular",
	];

	const currentProjection = getTranslatedProjection(mapProjection);

	if (!currentProjection) return null;

	const Icon = PROJECTION_ICONS[mapProjection];

	return (
		<PopoverContainer>
			{/* 当前投影信息 - 置顶显示 */}
			<div className="mb-6 p-4 bg-gradient-to-br from-sky-50 via-blue-50 to-indigo-50 rounded-xl border border-sky-100">
				<div className="flex items-center gap-3 mb-3">
					<div className="p-2 bg-white rounded-lg shadow-sm">
						<Icon className="w-5 h-5 text-sky-600" />
					</div>
					<div>
						<h6 className="text-sm font-semibold text-slate-800">
							{currentProjection.name}
						</h6>
						<p className="text-xs text-slate-600">
							{currentProjection.shortDesc}
						</p>
					</div>
					<span className="ml-auto px-2 py-1 text-xs font-medium bg-sky-100 text-sky-700 rounded-full">
						{currentProjection.badge}
					</span>
				</div>
				<p className="text-xs text-slate-600 leading-relaxed">
					{currentProjection.description}
				</p>
			</div>

			{/* 快速选择区域 */}
			<div className="space-y-4">
				<div>
					<h5 className="text-xs font-semibold text-slate-700 mb-3 flex items-center gap-2">
						<div className="w-1 h-4 bg-sky-500 rounded-full" />
						{t.mapProjections.categories.common()}
					</h5>
					<div className="grid grid-cols-2 gap-2">
						{commonProjectionKeys.map((key) => {
							const config = getTranslatedProjection(key);
							if (!config) return null;
							const IconComponent = PROJECTION_ICONS[key];
							const isActive = mapProjection === key;

							return (
								<button
									key={key}
									type="button"
									onClick={(e) => {
										e.stopPropagation();
										onProjectionChange(key);
									}}
									className={`group p-3 rounded-xl border-2 transition-all duration-200 ${
										isActive
											? "bg-sky-50 border-sky-200 shadow-sm"
											: "bg-white border-slate-200 hover:border-sky-200 hover:bg-sky-50/50"
									}`}
									title={config.description}
								>
									<div className="flex items-center gap-2 mb-2">
										<IconComponent
											className={`w-4 h-4 transition-colors ${
												isActive
													? "text-sky-600"
													: "text-slate-500 group-hover:text-sky-500"
											}`}
										/>
										<span
											className={`text-xs font-medium transition-colors ${
												isActive
													? "text-sky-700"
													: "text-slate-700"
											}`}
										>
											{config.name}
										</span>
									</div>
									<p className="text-xs text-slate-500 text-left">
										{config.shortDesc}
									</p>
									<div className="mt-2 flex justify-between items-center">
										<span
											className={`text-xs px-1.5 py-0.5 rounded-md ${
												isActive
													? "bg-sky-100 text-sky-600"
													: "bg-slate-100 text-slate-600"
											}`}
										>
											{config.badge}
										</span>
										{isActive && (
											<div className="w-1.5 h-1.5 bg-sky-500 rounded-full" />
										)}
									</div>
								</button>
							);
						})}
					</div>
				</div>

				<div>
					<h5 className="text-xs font-semibold text-slate-700 mb-3 flex items-center gap-2">
						<div className="w-1 h-4 bg-amber-500 rounded-full" />
						{t.mapProjections.categories.professional()}
					</h5>
					<div className="space-y-2">
						{professionalProjectionKeys.map((key) => {
							const config = getTranslatedProjection(key);
							if (!config) return null;
							const IconComponent = PROJECTION_ICONS[key];
							const isActive = mapProjection === key;

							return (
								<button
									key={key}
									type="button"
									onClick={(e) => {
										e.stopPropagation();
										onProjectionChange(key);
									}}
									className={`group w-full p-3 rounded-lg border transition-all duration-200 text-left ${
										isActive
											? "bg-amber-50 border-amber-200 shadow-sm"
											: "bg-white border-slate-200 hover:border-amber-200 hover:bg-amber-50/50"
									}`}
									title={config.description}
								>
									<div className="flex items-center justify-between">
										<div className="flex items-center gap-3">
											<IconComponent
												className={`w-4 h-4 transition-colors ${
													isActive
														? "text-amber-600"
														: "text-slate-500 group-hover:text-amber-500"
												}`}
											/>
											<div>
												<span
													className={`text-sm font-medium transition-colors ${
														isActive
															? "text-amber-700"
															: "text-slate-700"
													}`}
												>
													{config.name}
												</span>
												<p className="text-xs text-slate-500 mt-0.5">
													{config.shortDesc}
												</p>
											</div>
										</div>
										<div className="flex items-center gap-2">
											<span
												className={`text-xs px-2 py-0.5 rounded-md ${
													isActive
														? "bg-amber-100 text-amber-600"
														: "bg-slate-100 text-slate-600"
												}`}
											>
												{config.badge}
											</span>
											{isActive && (
												<div className="w-1.5 h-1.5 bg-amber-500 rounded-full" />
											)}
										</div>
									</div>
								</button>
							);
						})}
					</div>
				</div>
			</div>
		</PopoverContainer>
	);
}
