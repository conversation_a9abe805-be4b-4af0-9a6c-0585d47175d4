import type {
	FrontendTravelDiary,
	FrontendTravelTimeline,
} from "@packages/database/src/types";
import { debounce } from "lodash";
import { useTranslations } from "next-intl";
import { useCallback, useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import {
	getDiary,
	saveDiary,
	transformDiaryContent,
} from "../../../services/diary-service";

interface UseDiaryDataProps {
	diaryId: string;
}

export function useDiaryData({ diaryId }: UseDiaryDataProps) {
	const t = useTranslations("travelMemo.diaryEditor");

	// 状态
	const [isLoading, setIsLoading] = useState<boolean>(true);
	const [diary, setDiary] = useState<FrontendTravelDiary | null>(null);
	const [isSaving, setIsSaving] = useState<boolean>(false);

	// 引用
	const isInitialLoad = useRef(true);
	const isDiaryModified = useRef(false);
	const lastSaveTimestamp = useRef<number>(0);

	// 加载日记数据
	const loadDiaryData = useCallback(async () => {
		isInitialLoad.current = true;
		isDiaryModified.current = false;
		setIsLoading(true);

		try {
			const diaryData = await getDiary(diaryId);
			if (diaryData) {
				const frontendDiary = transformDiaryContent(diaryData);
				setDiary(frontendDiary);
			}
		} catch (error) {
			console.error(t("errors.loadDiaryDataFailed"), error);
			toast.error(t("toasts.loadFailed"));
		} finally {
			setIsLoading(false);
		}
	}, [diaryId, t]);

	// 保存日记数据
	const saveDiaryData = useCallback(
		async (options?: { silent?: boolean }) => {
			if (!diary) return;

			if (isInitialLoad.current) {
				console.log("Skipping save on initial load completion.");
				return;
			}

			if (!isDiaryModified.current) {
				console.log("Diary not modified, skipping save.");
				return;
			}

			const now = Date.now();
			if (now - lastSaveTimestamp.current < 1000) {
				console.log(
					"Save attempted too soon after previous save, skipping.",
				);
				return;
			}

			lastSaveTimestamp.current = now;
			setIsSaving(true);

			try {
				await saveDiary(diaryId, diary);

				// Analytics: 追踪日记保存
				if (typeof window !== "undefined" && window.plausible) {
					window.plausible("diary_saved", {
						props: {
							diary_id: diaryId,
							timelines_count: diary.timelines?.length || 0,
							total_points:
								diary.timelines?.reduce(
									(sum, t) => sum + (t.points?.length || 0),
									0,
								) || 0,
							save_type: options?.silent ? "auto" : "manual",
						},
					});
				}

				if (!options?.silent) {
					toast.success(t("toasts.autoSaveSuccess"));
				}
				isDiaryModified.current = false;
			} catch (error) {
				console.error("自动保存日记失败:", error);
				toast.error(t("toasts.autoSaveFailed"));
			} finally {
				setIsSaving(false);
			}
		},
		[diary, diaryId, t],
	);

	// 防抖保存
	const debouncedSave = useCallback(
		debounce(() => {
			void saveDiaryData();
		}, 1500),
		[saveDiaryData],
	);

	// 标记为已修改
	const markAsModified = useCallback(() => {
		isDiaryModified.current = true;
	}, []);

	// 更新日记基本信息
	const updateDiary = useCallback(
		(updates: Partial<FrontendTravelDiary>) => {
			if (!diary) return;
			markAsModified();
			setDiary((prev) => (prev ? { ...prev, ...updates } : null));
		},
		[diary, markAsModified],
	);

	// 更新时间线
	const updateTimelines = useCallback(
		(updatedTimelines: FrontendTravelTimeline[]) => {
			if (!diary) return;
			markAsModified();
			setDiary((prev) =>
				prev ? { ...prev, timelines: updatedTimelines } : null,
			);
		},
		[diary, markAsModified],
	);

	// 初始化加载
	useEffect(() => {
		void loadDiaryData();
	}, [loadDiaryData]);

	// 自动保存监听
	useEffect(() => {
		if (isInitialLoad.current) {
			const timer = setTimeout(() => {
				isInitialLoad.current = false;
				console.log("Initial load marked as complete.");
			}, 500);
			return () => clearTimeout(timer);
		}

		if (
			diary &&
			!isInitialLoad.current &&
			isDiaryModified.current &&
			!isSaving
		) {
			console.log("Diary state changed, triggering debounced save.");
			debouncedSave();
		}

		return () => {
			debouncedSave.cancel();
		};
	}, [diary, debouncedSave, isSaving]);

	return {
		// 状态
		diary,
		isLoading,
		isSaving,
		isDiaryModified: isDiaryModified.current,

		// 方法
		updateDiary,
		updateTimelines,
		saveDiaryData,
		markAsModified,
		setDiary,

		// 原始状态引用（为了向后兼容）
		isInitialLoad,
		isDiaryModifiedRef: isDiaryModified,
	};
}
