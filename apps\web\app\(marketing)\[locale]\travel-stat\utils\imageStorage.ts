// 图片存储工具类
// 统一处理IndexedDB图片存储、加载和URL管理

export class ImageStorageManager {
	private static instance: ImageStorageManager;
	private dbName = "TravelPhotos";
	private dbVersion = 1;
	private storeName = "photos";

	// 单例模式
	public static getInstance(): ImageStorageManager {
		if (!ImageStorageManager.instance) {
			ImageStorageManager.instance = new ImageStorageManager();
		}
		return ImageStorageManager.instance;
	}

	/**
	 * 存储文件到IndexedDB
	 */
	async storeFile(file: File, pointId: string): Promise<string> {
		return new Promise((resolve, reject) => {
			const request = indexedDB.open(this.dbName, this.dbVersion);

			request.onerror = () => reject(request.error);

			request.onupgradeneeded = (event) => {
				const db = (event.target as IDBOpenDBRequest).result;
				if (!db.objectStoreNames.contains(this.storeName)) {
					db.createObjectStore(this.storeName, { keyPath: "id" });
				}
			};

			request.onsuccess = (event) => {
				const db = (event.target as IDBOpenDBRequest).result;
				const transaction = db.transaction(
					[this.storeName],
					"readwrite",
				);
				const store = transaction.objectStore(this.storeName);

				const fileId = `${pointId}_${Date.now()}`;
				const photoData = {
					id: fileId,
					file: file,
					fileName: file.name,
					fileType: file.type,
					fileSize: file.size,
					createdAt: new Date().toISOString(),
				};

				const addRequest = store.add(photoData);
				addRequest.onsuccess = () => {
					console.log("✅ 文件已存储到IndexedDB:", fileId);
					resolve(fileId);
				};
				addRequest.onerror = () => reject(addRequest.error);
			};
		});
	}

	/**
	 * 从IndexedDB读取文件并创建blob URL
	 */
	async getFileUrl(fileId: string): Promise<string | null> {
		return new Promise((resolve) => {
			const request = indexedDB.open(this.dbName, this.dbVersion);

			request.onerror = () => {
				console.error("❌ 打开IndexedDB失败");
				resolve(null);
			};

			request.onupgradeneeded = (event) => {
				const db = (event.target as IDBOpenDBRequest).result;
				if (!db.objectStoreNames.contains(this.storeName)) {
					db.createObjectStore(this.storeName, { keyPath: "id" });
					console.log("🔄 创建IndexedDB对象存储:", this.storeName);
				}
			};

			request.onsuccess = (event) => {
				const db = (event.target as IDBOpenDBRequest).result;

				// 检查对象存储是否存在
				if (!db.objectStoreNames.contains(this.storeName)) {
					console.warn("⚠️ 对象存储不存在:", this.storeName);
					resolve(null);
					return;
				}

				const transaction = db.transaction(
					[this.storeName],
					"readonly",
				);
				const store = transaction.objectStore(this.storeName);

				const getRequest = store.get(fileId);
				getRequest.onsuccess = () => {
					if (getRequest.result?.file) {
						const url = URL.createObjectURL(getRequest.result.file);
						console.log("✅ 从IndexedDB获取文件URL:", fileId);
						resolve(url);
					} else {
						console.warn("⚠️ IndexedDB中未找到文件:", fileId);
						resolve(null);
					}
				};
				getRequest.onerror = () => {
					console.error("❌ 从IndexedDB读取文件失败:", fileId);
					resolve(null);
				};
			};
		});
	}

	/**
	 * 删除IndexedDB中的文件
	 */
	async deleteFile(fileId: string): Promise<boolean> {
		return new Promise((resolve) => {
			const request = indexedDB.open(this.dbName, this.dbVersion);

			request.onerror = () => {
				console.error("❌ 打开IndexedDB失败");
				resolve(false);
			};

			request.onupgradeneeded = (event) => {
				const db = (event.target as IDBOpenDBRequest).result;
				if (!db.objectStoreNames.contains(this.storeName)) {
					db.createObjectStore(this.storeName, { keyPath: "id" });
					console.log("🔄 创建IndexedDB对象存储:", this.storeName);
				}
			};

			request.onsuccess = (event) => {
				const db = (event.target as IDBOpenDBRequest).result;

				// 检查对象存储是否存在
				if (!db.objectStoreNames.contains(this.storeName)) {
					console.warn("⚠️ 对象存储不存在:", this.storeName);
					resolve(false);
					return;
				}

				const transaction = db.transaction(
					[this.storeName],
					"readwrite",
				);
				const store = transaction.objectStore(this.storeName);

				const deleteRequest = store.delete(fileId);
				deleteRequest.onsuccess = () => {
					console.log("✅ 文件已从IndexedDB删除:", fileId);
					resolve(true);
				};
				deleteRequest.onerror = () => {
					console.error("❌ 从IndexedDB删除文件失败:", fileId);
					resolve(false);
				};
			};
		});
	}

	/**
	 * 处理图片URL - 统一入口
	 * 自动识别URL类型并返回可用的blob URL
	 * 支持智能缓存和性能优化
	 */
	async processImageUrl(
		url: string,
		options: {
			priority?: "high" | "medium" | "low";
			maxWidth?: number;
			maxHeight?: number;
			useCache?: boolean;
		} = {},
	): Promise<string | null> {
		if (!url) {
			console.warn("⚠️ processImageUrl: 空URL");
			return null;
		}

		const {
			priority = "medium",
			maxWidth = 400,
			maxHeight = 400,
			useCache = true,
		} = options;

		console.log("🔄 processImageUrl: 开始处理图片:", {
			url: url.slice(0, 50),
			priority,
			useCache,
			maxWidth,
			maxHeight,
		});

		try {
			// 🔧 安全检查：禁止直接返回HTTP URL，确保必须经过压缩处理
			if (url.startsWith("http")) {
				console.log(
					"🔄 processImageUrl: 检测到HTTP URL，强制压缩处理",
					{
						url: url.slice(0, 80),
						maxWidth,
						maxHeight,
						priority,
					},
				);
			}

			// 如果启用缓存，尝试使用图片缓存管理器
			if (useCache) {
				try {
					const { imageCache } = await import("./imageCache");
					const cachedResult = await imageCache.processAndCacheImage(
						url,
						{ maxWidth, maxHeight, format: "jpeg", quality: 0.75 },
						priority,
					);

					if (cachedResult) {
						console.log("✅ processImageUrl: 缓存处理成功");
						// 🔧 最终安全检查：确保不返回原始HTTP URL
						if (
							cachedResult.startsWith("http") &&
							cachedResult === url
						) {
							console.error(
								"🚨 processImageUrl: 缓存返回了原始HTTP URL，这是安全违规!",
							);
							return null;
						}
						return cachedResult;
					}
					console.warn(
						"⚠️ processImageUrl: 缓存处理返回null，尝试传统方式",
						{
							url: url.slice(0, 50),
							maxWidth,
							maxHeight,
							priority,
						},
					);
				} catch (cacheError) {
					console.warn(
						"⚠️ processImageUrl: 缓存处理失败，使用传统方式:",
						{
							error:
								cacheError instanceof Error
									? cacheError.message
									: String(cacheError),
							url: url.slice(0, 50),
							maxWidth,
							maxHeight,
						},
					);
				}
			}

			// 传统处理方式（不使用缓存）
			const legacyResult = await this.processImageUrlLegacy(url, {
				maxWidth,
				maxHeight,
			});
			if (legacyResult) {
				console.log("✅ processImageUrl: 传统方式处理成功");
				// 🔧 最终安全检查：确保Legacy不返回原始HTTP URL
				if (legacyResult.startsWith("http") && legacyResult === url) {
					console.error(
						"🚨 processImageUrl: Legacy返回了原始HTTP URL，这是安全违规!",
					);
					return null;
				}
				return legacyResult;
			}
			console.error("❌ processImageUrl: 传统方式也失败了");
			return null;
		} catch (error) {
			console.error("❌ processImageUrl: 图片URL处理失败:", error);
			return null;
		}
	}

	/**
	 * 传统的图片URL处理方法（不使用缓存）
	 * 🔧 修正：确保即使在传统模式下也进行压缩处理
	 */
	private async processImageUrlLegacy(
		url: string,
		options: {
			maxWidth?: number;
			maxHeight?: number;
		} = {},
	): Promise<string | null> {
		if (!url) {
			console.warn("⚠️ processImageUrlLegacy: 空URL");
			return null;
		}

		const { maxWidth = 250, maxHeight = 250 } = options;

		console.log("🔄 processImageUrlLegacy: 处理图片URL:", {
			url: url.slice(0, 50),
			type: url.startsWith("indexeddb:")
				? "indexeddb"
				: url.startsWith("blob:")
					? "blob"
					: url.startsWith("data:")
						? "base64"
						: url.startsWith("http")
							? "http"
							: "unknown",
			maxWidth,
			maxHeight,
		});

		try {
			// 如果是IndexedDB引用
			if (url.startsWith("indexeddb:")) {
				const fileId = url.replace("indexeddb:", "");
				console.log(
					"📁 processImageUrlLegacy: 处理IndexedDB文件:",
					fileId,
				);

				const result = await this.getFileUrl(fileId);
				if (result) {
					console.log(
						"✅ processImageUrlLegacy: IndexedDB文件获取成功",
					);
					return result;
				}
				console.warn(
					"⚠️ processImageUrlLegacy: IndexedDB文件获取失败，fileId:",
					fileId,
				);
				return null;
			}

			// 如果是blob URL，检查是否有效
			if (url.startsWith("blob:")) {
				console.log("🔗 processImageUrlLegacy: 验证blob URL");
				const result = await this.validateBlobUrl(url);
				if (result) {
					console.log("✅ processImageUrlLegacy: Blob URL验证成功");
					return result;
				}
				console.warn("⚠️ processImageUrlLegacy: Blob URL验证失败");
				return null;
			}

			// 如果是base64，直接返回（已经是压缩格式）
			if (url.startsWith("data:")) {
				console.log("✅ processImageUrlLegacy: 直接返回base64 URL");
				return url;
			}

			// 🔧 关键修正：如果是HTTP URL，进行压缩处理而不是直接返回
			if (url.startsWith("http")) {
				console.log("🔄 processImageUrlLegacy: 处理HTTP URL，进行压缩");
				try {
					// 获取原始图片数据
					const response = await fetch(url);
					if (!response.ok) {
						throw new Error(
							`HTTP ${response.status}: ${response.statusText}`,
						);
					}

					const blob = await response.blob();
					console.log("📥 processImageUrlLegacy: 图片下载完成:", {
						originalSize: this.formatBytes(blob.size),
						contentType: blob.type,
					});

					// 进行压缩处理
					const compressedBlob = await this.compressImageBlob(
						blob,
						maxWidth,
						maxHeight,
					);

					// 创建压缩后的blob URL
					const compressedUrl = URL.createObjectURL(compressedBlob);
					console.log("✅ processImageUrlLegacy: HTTP图片压缩完成:", {
						originalSize: this.formatBytes(blob.size),
						compressedSize: this.formatBytes(compressedBlob.size),
						compressionRatio: `${Math.round((1 - compressedBlob.size / blob.size) * 100)}%`,
						resultUrl: compressedUrl.slice(0, 50),
					});

					return compressedUrl;
				} catch (error) {
					console.error(
						"❌ processImageUrlLegacy: HTTP图片处理失败:",
						error,
					);
					// 不能再回退到原始URL，而是返回null
					return null;
				}
			}

			console.warn("⚠️ processImageUrlLegacy: 未知的图片URL格式:", url);
			return null;
		} catch (error) {
			console.error("❌ processImageUrlLegacy: 处理失败:", error);
			return null;
		}
	}

	/**
	 * 🔧 新增：压缩图片Blob的辅助方法
	 */
	private async compressImageBlob(
		blob: Blob,
		maxWidth: number,
		maxHeight: number,
	): Promise<Blob> {
		return new Promise((resolve, reject) => {
			const img = new Image();
			const canvas = document.createElement("canvas");
			const ctx = canvas.getContext("2d");

			if (!ctx) {
				reject(new Error("无法获取Canvas上下文"));
				return;
			}

			img.onload = () => {
				try {
					// 计算新尺寸
					const { width, height } = this.calculateNewSize(
						img.width,
						img.height,
						maxWidth,
						maxHeight,
					);

					canvas.width = width;
					canvas.height = height;

					// 使用高质量图像平滑
					ctx.imageSmoothingEnabled = true;
					ctx.imageSmoothingQuality = "high";

					// 绘制图片
					ctx.drawImage(img, 0, 0, width, height);

					// 根据图片大小选择压缩质量
					const quality = blob.size > 5 * 1024 * 1024 ? 0.6 : 0.75; // 大图片使用更低质量

					// 转换为JPEG格式以减小文件大小
					canvas.toBlob(
						(compressedBlob) => {
							if (compressedBlob) {
								resolve(compressedBlob);
							} else {
								reject(new Error("图片压缩失败"));
							}
						},
						"image/jpeg",
						quality,
					);
				} catch (error) {
					reject(error);
				}
			};

			img.onerror = () => reject(new Error("图片加载失败"));
			img.src = URL.createObjectURL(blob);
		});
	}

	/**
	 * 🔧 新增：计算新的图片尺寸（保持宽高比）
	 */
	private calculateNewSize(
		originalWidth: number,
		originalHeight: number,
		maxWidth: number,
		maxHeight: number,
	): { width: number; height: number } {
		let { width, height } = {
			width: originalWidth,
			height: originalHeight,
		};

		// 如果原始尺寸已经符合要求，直接返回
		if (width <= maxWidth && height <= maxHeight) {
			return { width, height };
		}

		// 计算缩放比例
		const widthRatio = maxWidth / width;
		const heightRatio = maxHeight / height;
		const ratio = Math.min(widthRatio, heightRatio);

		// 应用缩放比例
		width = Math.round(width * ratio);
		height = Math.round(height * ratio);

		return { width, height };
	}

	/**
	 * 🔧 新增：格式化文件大小显示
	 */
	private formatBytes(bytes: number): string {
		if (bytes === 0) return "0 Bytes";
		const k = 1024;
		const sizes = ["Bytes", "KB", "MB", "GB"];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
	}

	/**
	 * 验证blob URL是否仍然有效
	 */
	private async validateBlobUrl(url: string): Promise<string | null> {
		try {
			const response = await fetch(url);
			if (response.ok) {
				return url; // blob URL仍然有效
			}
			console.warn("⚠️ blob URL已失效:", url);
			return null;
		} catch (error) {
			console.warn("⚠️ blob URL验证失败:", url, error);
			return null;
		}
	}

	/**
	 * 迁移旧的blob URL到IndexedDB（如果可能）
	 * 注意：页面刷新后blob URL通常已失效，此函数主要用于运行时迁移
	 */
	async migrateBlobUrlToIndexedDB(
		blobUrl: string,
		pointId: string,
	): Promise<string | null> {
		try {
			const response = await fetch(blobUrl);
			if (!response.ok) {
				console.warn("❌ blob URL已失效，无法迁移:", blobUrl);
				return null;
			}

			const blob = await response.blob();
			const file = new File([blob], `migrated_${pointId}.jpg`, {
				type: blob.type || "image/jpeg",
			});

			const fileId = await this.storeFile(file, pointId);
			console.log("✅ blob URL已迁移到IndexedDB:", {
				originalUrl: blobUrl,
				newFileId: fileId,
			});

			return `indexeddb:${fileId}`;
		} catch (error) {
			console.error("❌ blob URL迁移失败:", error);
			return null;
		}
	}

	/**
	 * 清理所有IndexedDB数据
	 */
	async clearAll(): Promise<boolean> {
		return new Promise((resolve) => {
			const request = indexedDB.deleteDatabase(this.dbName);
			request.onsuccess = () => {
				console.log("✅ IndexedDB已清空");
				resolve(true);
			};
			request.onerror = () => {
				console.error("❌ 清空IndexedDB失败");
				resolve(false);
			};
		});
	}

	/**
	 * 初始化数据库 - 确保数据库和对象存储都存在
	 */
	async initializeDatabase(): Promise<boolean> {
		return new Promise((resolve) => {
			const request = indexedDB.open(this.dbName, this.dbVersion);

			request.onerror = () => {
				console.error("❌ 初始化IndexedDB失败");
				resolve(false);
			};

			request.onupgradeneeded = (event) => {
				const db = (event.target as IDBOpenDBRequest).result;
				if (!db.objectStoreNames.contains(this.storeName)) {
					db.createObjectStore(this.storeName, { keyPath: "id" });
					console.log("✅ 创建IndexedDB对象存储:", this.storeName);
				}
			};

			request.onsuccess = (event) => {
				const db = (event.target as IDBOpenDBRequest).result;
				const hasStore = db.objectStoreNames.contains(this.storeName);
				console.log("✅ IndexedDB初始化完成:", {
					数据库: this.dbName,
					版本: this.dbVersion,
					对象存储: this.storeName,
					存储存在: hasStore,
				});
				db.close();
				resolve(hasStore);
			};
		});
	}

	/**
	 * 获取存储统计信息
	 */
	async getStorageStats(): Promise<{
		fileCount: number;
		totalSize: number;
		files: Array<{ id: string; fileName: string; fileSize: number }>;
	}> {
		return new Promise((resolve) => {
			const request = indexedDB.open(this.dbName, this.dbVersion);

			request.onerror = () => {
				console.error("❌ 打开IndexedDB失败");
				resolve({ fileCount: 0, totalSize: 0, files: [] });
			};

			request.onupgradeneeded = (event) => {
				const db = (event.target as IDBOpenDBRequest).result;
				if (!db.objectStoreNames.contains(this.storeName)) {
					db.createObjectStore(this.storeName, { keyPath: "id" });
					console.log("🔄 创建IndexedDB对象存储:", this.storeName);
				}
			};

			request.onsuccess = (event) => {
				const db = (event.target as IDBOpenDBRequest).result;

				// 检查对象存储是否存在
				if (!db.objectStoreNames.contains(this.storeName)) {
					console.warn("⚠️ 对象存储不存在:", this.storeName);
					resolve({ fileCount: 0, totalSize: 0, files: [] });
					return;
				}

				const transaction = db.transaction(
					[this.storeName],
					"readonly",
				);
				const store = transaction.objectStore(this.storeName);

				const getAllRequest = store.getAll();
				getAllRequest.onsuccess = () => {
					const files = getAllRequest.result || [];
					const totalSize = files.reduce(
						(sum, file) => sum + (file.fileSize || 0),
						0,
					);

					resolve({
						fileCount: files.length,
						totalSize,
						files: files.map((file) => ({
							id: file.id,
							fileName: file.fileName || "unknown",
							fileSize: file.fileSize || 0,
						})),
					});
				};
				getAllRequest.onerror = () => {
					resolve({ fileCount: 0, totalSize: 0, files: [] });
				};
			};
		});
	}
}

// 导出单例实例
export const imageStorage = ImageStorageManager.getInstance();
