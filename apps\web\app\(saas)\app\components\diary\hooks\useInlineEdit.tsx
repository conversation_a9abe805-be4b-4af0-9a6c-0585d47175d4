import type { TravelPointImage } from "@packages/database/src/types";
import { useCallback, useRef, useState } from "react";
import type { TravelPoint } from "../SortablePointItem";

export interface InlineEditState {
	editingPoints: Set<string>; // 正在编辑的点位IDs
	editingFields: Map<string, Set<string>>; // 每个点位正在编辑的字段
	pendingChanges: Map<string, Partial<TravelPoint>>; // 待保存的更改
	isAddingNew: boolean; // 是否正在添加新点位
}

export interface UseInlineEditProps {
	onPointUpdate: (id: string, changes: Partial<TravelPoint>) => void;
	onPointAdd: (point: TravelPoint) => void;
	autoSaveDelay?: number; // 自动保存延迟，默认1500ms
}

export function useInlineEdit({
	onPointUpdate,
	onPointAdd,
	autoSaveDelay = 1500,
}: UseInlineEditProps) {
	const [editState, setEditState] = useState<InlineEditState>({
		editingPoints: new Set(),
		editingFields: new Map(),
		pendingChanges: new Map(),
		isAddingNew: false,
	});

	const autoSaveTimeouts = useRef<Map<string, NodeJS.Timeout>>(new Map());

	// 进入编辑模式
	const enterEditMode = useCallback((pointId: string, fieldName?: string) => {
		setEditState((prev) => {
			const newEditingPoints = new Set(prev.editingPoints);
			const newEditingFields = new Map(prev.editingFields);

			newEditingPoints.add(pointId);

			if (fieldName) {
				const currentFields =
					newEditingFields.get(pointId) || new Set();
				currentFields.add(fieldName);
				newEditingFields.set(pointId, currentFields);
			}

			return {
				...prev,
				editingPoints: newEditingPoints,
				editingFields: newEditingFields,
			};
		});
	}, []);

	// 退出编辑模式
	const exitEditMode = useCallback(
		(pointId: string, fieldName?: string, saveChanges = true) => {
			setEditState((prev) => {
				const newEditingPoints = new Set(prev.editingPoints);
				const newEditingFields = new Map(prev.editingFields);
				const newPendingChanges = new Map(prev.pendingChanges);

				if (fieldName) {
					const currentFields =
						newEditingFields.get(pointId) || new Set();
					currentFields.delete(fieldName);
					if (currentFields.size === 0) {
						newEditingFields.delete(pointId);
						newEditingPoints.delete(pointId);
					} else {
						newEditingFields.set(pointId, currentFields);
					}
				} else {
					newEditingFields.delete(pointId);
					newEditingPoints.delete(pointId);
				}

				// 如果要保存更改且有待保存的更改
				if (saveChanges && newPendingChanges.has(pointId)) {
					const changes = newPendingChanges.get(pointId)!;
					newPendingChanges.delete(pointId);

					// 清除自动保存定时器
					const timeout = autoSaveTimeouts.current.get(pointId);
					if (timeout) {
						clearTimeout(timeout);
						autoSaveTimeouts.current.delete(pointId);
					}

					// 立即保存
					onPointUpdate(pointId, changes);
				} else if (!saveChanges) {
					// 取消更改
					newPendingChanges.delete(pointId);
					const timeout = autoSaveTimeouts.current.get(pointId);
					if (timeout) {
						clearTimeout(timeout);
						autoSaveTimeouts.current.delete(pointId);
					}
				}

				return {
					...prev,
					editingPoints: newEditingPoints,
					editingFields: newEditingFields,
					pendingChanges: newPendingChanges,
				};
			});
		},
		[onPointUpdate],
	);

	// 更新字段值
	const updateField = useCallback(
		(pointId: string, fieldName: string, value: any) => {
			setEditState((prev) => {
				const newPendingChanges = new Map(prev.pendingChanges);
				const currentChanges = newPendingChanges.get(pointId) || {};

				newPendingChanges.set(pointId, {
					...currentChanges,
					[fieldName]: value,
				});

				// 清除之前的自动保存定时器
				const existingTimeout = autoSaveTimeouts.current.get(pointId);
				if (existingTimeout) {
					clearTimeout(existingTimeout);
				}

				// 设置新的自动保存定时器
				const newTimeout = setTimeout(() => {
					const changes = newPendingChanges.get(pointId);
					if (changes) {
						onPointUpdate(pointId, changes);
						setEditState((current) => {
							const updatedPendingChanges = new Map(
								current.pendingChanges,
							);
							updatedPendingChanges.delete(pointId);
							return {
								...current,
								pendingChanges: updatedPendingChanges,
							};
						});
					}
					autoSaveTimeouts.current.delete(pointId);
				}, autoSaveDelay);

				autoSaveTimeouts.current.set(pointId, newTimeout);

				return {
					...prev,
					pendingChanges: newPendingChanges,
				};
			});
		},
		[onPointUpdate, autoSaveDelay],
	);

	// 获取当前字段的值（包括待保存的更改）
	const getFieldValue = useCallback(
		(pointId: string, fieldName: string, originalValue: any) => {
			const pendingChanges = editState.pendingChanges.get(pointId);
			if (pendingChanges && fieldName in pendingChanges) {
				return (pendingChanges as any)[fieldName];
			}
			return originalValue;
		},
		[editState.pendingChanges],
	);

	// 检查是否正在编辑
	const isEditing = useCallback(
		(pointId: string, fieldName?: string) => {
			if (!editState.editingPoints.has(pointId)) return false;
			if (!fieldName) return true;

			const editingFields = editState.editingFields.get(pointId);
			return editingFields ? editingFields.has(fieldName) : false;
		},
		[editState.editingPoints, editState.editingFields],
	);

	// 开始添加新点位
	const startAddingNew = useCallback(() => {
		setEditState((prev) => ({
			...prev,
			isAddingNew: true,
		}));
	}, []);

	// 完成添加新点位
	const finishAddingNew = useCallback(
		(newPoint?: TravelPoint) => {
			if (newPoint) {
				onPointAdd(newPoint);
			}
			setEditState((prev) => ({
				...prev,
				isAddingNew: false,
			}));
		},
		[onPointAdd],
	);

	// 取消添加新点位
	const cancelAddingNew = useCallback(() => {
		setEditState((prev) => ({
			...prev,
			isAddingNew: false,
		}));
	}, []);

	// 立即保存更改
	const saveChanges = useCallback(
		(pointId: string) => {
			const changes = editState.pendingChanges.get(pointId);
			if (changes) {
				onPointUpdate(pointId, changes);
				setEditState((prev) => {
					const newPendingChanges = new Map(prev.pendingChanges);
					newPendingChanges.delete(pointId);
					return {
						...prev,
						pendingChanges: newPendingChanges,
					};
				});

				// 清除自动保存定时器
				const timeout = autoSaveTimeouts.current.get(pointId);
				if (timeout) {
					clearTimeout(timeout);
					autoSaveTimeouts.current.delete(pointId);
				}
			}
		},
		[editState.pendingChanges, onPointUpdate],
	);

	// 清理函数
	const cleanup = useCallback(() => {
		autoSaveTimeouts.current.forEach((timeout) => clearTimeout(timeout));
		autoSaveTimeouts.current.clear();
	}, []);

	// 更新位置字段值（包括坐标）
	const updateLocationField = useCallback(
		(
			pointId: string,
			location: string,
			coordinates?: { lat: number; lng: number },
		) => {
			setEditState((prev) => {
				const newPendingChanges = new Map(prev.pendingChanges);
				const currentChanges = newPendingChanges.get(pointId) || {};

				const locationChanges: any = {
					...currentChanges,
					location: location,
				};

				// 如果提供了坐标，同时更新坐标信息
				if (coordinates) {
					locationChanges.coordinates = coordinates;
				}

				newPendingChanges.set(pointId, locationChanges);

				// 清除之前的自动保存定时器
				const existingTimeout = autoSaveTimeouts.current.get(pointId);
				if (existingTimeout) {
					clearTimeout(existingTimeout);
				}

				// 设置新的自动保存定时器
				const newTimeout = setTimeout(() => {
					const changes = newPendingChanges.get(pointId);
					if (changes) {
						onPointUpdate(pointId, changes);
						setEditState((current) => {
							const updatedPendingChanges = new Map(
								current.pendingChanges,
							);
							updatedPendingChanges.delete(pointId);
							return {
								...current,
								pendingChanges: updatedPendingChanges,
							};
						});
					}
					autoSaveTimeouts.current.delete(pointId);
				}, autoSaveDelay);

				autoSaveTimeouts.current.set(pointId, newTimeout);

				return {
					...prev,
					pendingChanges: newPendingChanges,
				};
			});
		},
		[onPointUpdate, autoSaveDelay],
	);

	// 更新图片字段值
	const updateImagesField = useCallback(
		(pointId: string, images: TravelPointImage[]) => {
			setEditState((prev) => {
				const newPendingChanges = new Map(prev.pendingChanges);
				const currentChanges = newPendingChanges.get(pointId) || {};

				newPendingChanges.set(pointId, {
					...currentChanges,
					images: images,
				});

				// 清除之前的自动保存定时器
				const existingTimeout = autoSaveTimeouts.current.get(pointId);
				if (existingTimeout) {
					clearTimeout(existingTimeout);
				}

				// 设置新的自动保存定时器
				const newTimeout = setTimeout(() => {
					const changes = newPendingChanges.get(pointId);
					if (changes) {
						onPointUpdate(pointId, changes);
						setEditState((current) => {
							const updatedPendingChanges = new Map(
								current.pendingChanges,
							);
							updatedPendingChanges.delete(pointId);
							return {
								...current,
								pendingChanges: updatedPendingChanges,
							};
						});
					}
					autoSaveTimeouts.current.delete(pointId);
				}, autoSaveDelay);

				autoSaveTimeouts.current.set(pointId, newTimeout);

				return {
					...prev,
					pendingChanges: newPendingChanges,
				};
			});
		},
		[onPointUpdate, autoSaveDelay],
	);

	return {
		editState,
		enterEditMode,
		exitEditMode,
		updateField,
		updateLocationField,
		updateImagesField,
		getFieldValue,
		isEditing,
		startAddingNew,
		finishAddingNew,
		cancelAddingNew,
		saveChanges,
		cleanup,
	};
}
