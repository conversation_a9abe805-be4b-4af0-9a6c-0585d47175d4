"use client";

import { Alert, AlertDescription } from "@ui/components/alert";
import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@ui/components/tabs";
import { Textarea } from "@ui/components/textarea";
import { cn } from "@ui/lib";
import {
	AlertCircle,
	Calendar,
	CheckCircle,
	ChevronUp,
	Clock,
	Loader2,
	Plus,
	RefreshCw,
	Send,
	X,
	Zap,
} from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

/**
 * Feature Voting 集成测试页面
 * 用于测试和演示Feature Voting模块的功能
 *
 * 注意：这个页面暂时使用内联实现，避免shared-ui包的导入问题
 * 一旦shared-ui包的TypeScript错误修复，可以切换回模块化导入
 */
export default function FeatureVotingTestPage() {
	const [selectedProduct, setSelectedProduct] = useState("travel-memo");
	const [selectedStatus, setSelectedStatus] = useState("all");
	const [features, setFeatures] = useState<any[]>([]);
	const [products, setProducts] = useState<any[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	// 状态配置
	const statusConfig = {
		under_consideration: {
			label: "考虑中",
			color: "bg-yellow-100 text-yellow-800 border-yellow-200",
			icon: Clock,
		},
		planned: {
			label: "已计划",
			color: "bg-blue-100 text-blue-800 border-blue-200",
			icon: Calendar,
		},
		in_progress: {
			label: "开发中",
			color: "bg-purple-100 text-purple-800 border-purple-200",
			icon: Zap,
		},
		completed: {
			label: "已完成",
			color: "bg-green-100 text-green-800 border-green-200",
			icon: CheckCircle,
		},
		wont_do: {
			label: "不会做",
			color: "bg-gray-100 text-gray-800 border-gray-200",
			icon: X,
		},
	};

	// 加载数据
	const loadData = async () => {
		setIsLoading(true);
		setError(null);

		try {
			// 加载产品列表
			const productsResponse = await fetch("/api/products");
			if (productsResponse.ok) {
				const productsData = await productsResponse.json();
				setProducts(Array.isArray(productsData) ? productsData : []);
			} else {
				setProducts([]);
			}

			// 加载特性请求
			const params = new URLSearchParams();
			if (selectedProduct !== "all") {
				params.append("productId", selectedProduct);
			}
			if (selectedStatus !== "all") {
				params.append("status", selectedStatus);
			}

			const featuresResponse = await fetch(
				`/api/feature-requests?${params}`,
			);
			if (featuresResponse.ok) {
				const featuresData = await featuresResponse.json();
				setFeatures(Array.isArray(featuresData) ? featuresData : []);
			} else {
				setFeatures([]);
				throw new Error("加载特性请求失败");
			}
		} catch (err: any) {
			console.error("加载数据失败:", err);
			setError(err?.message || "未知错误");
			// 确保在错误情况下也设置为空数组
			setFeatures([]);
			setProducts([]);
			toast.error("加载数据失败，请稍后重试");
		} finally {
			setIsLoading(false);
		}
	};

	// 投票功能
	const handleVote = async (featureId: string, hasVoted: boolean) => {
		try {
			const endpoint = hasVoted ? "unvote" : "vote";
			const response = await fetch(
				`/api/feature-requests/${featureId}/${endpoint}`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
				},
			);

			if (response.ok) {
				toast.success(hasVoted ? "取消投票成功" : "投票成功");
				loadData(); // 重新加载数据
			} else {
				throw new Error("投票操作失败");
			}
		} catch (err: any) {
			console.error("投票失败:", err);
			toast.error("投票失败，请稍后重试");
		}
	};

	// 提交新特性
	const handleSubmit = async (formData: FormData) => {
		try {
			const response = await fetch("/api/feature-requests", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(formData),
			});

			if (!response.ok) {
				throw new Error("提交失败");
			}
			toast.success("特性请求提交成功");
			loadData(); // 重新加载数据
			return true;
		} catch (err: any) {
			console.error("提交失败:", err);
			toast.error("提交失败，请稍后重试");
			return false;
		}
	};

	// 初次加载
	useEffect(() => {
		loadData();
	}, [selectedProduct, selectedStatus]);

	// 过滤特性请求
	const filteredFeatures = (features || []).filter((feature) => {
		const productMatch =
			selectedProduct === "all" || feature.productId === selectedProduct;
		const statusMatch =
			selectedStatus === "all" || feature.status === selectedStatus;
		return productMatch && statusMatch;
	});

	return (
		<div className="container mx-auto py-8 px-4">
			{/* 页面头部 */}
			<div className="mb-8">
				<h1 className="text-3xl font-bold text-foreground mb-2">
					Feature Voting 集成测试页面
				</h1>
				<p className="text-muted-foreground">
					这是Feature
					Voting模块的集成测试页面，连接真实的API和数据库。
				</p>
			</div>

			{/* 状态指示 */}
			{error && (
				<Alert className="mb-6">
					<AlertCircle className="h-4 w-4" />
					<AlertDescription>{error}</AlertDescription>
				</Alert>
			)}

			{/* 测试说明 */}
			<div className="mb-8 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
				<h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
					集成测试功能
				</h2>
				<ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
					<li>
						• <strong>真实API调用</strong>：连接实际的后端API接口
					</li>
					<li>
						• <strong>数据库交互</strong>：操作真实的数据库数据
					</li>
					<li>
						• <strong>完整CRUD</strong>：创建、读取、更新、删除操作
					</li>
					<li>
						• <strong>实时更新</strong>：投票和提交后立即更新数据
					</li>
					<li>
						• <strong>错误处理</strong>：完整的错误处理和用户反馈
					</li>
				</ul>
			</div>

			{/* 主要功能 */}
			<div className="max-w-6xl mx-auto">
				<Tabs defaultValue="voting" className="w-full">
					<TabsList className="grid w-full grid-cols-2">
						<TabsTrigger value="voting">特性投票</TabsTrigger>
						<TabsTrigger value="submit">提交特性</TabsTrigger>
					</TabsList>

					{/* 投票页面 */}
					<TabsContent value="voting" className="space-y-6">
						{/* 筛选器 */}
						<div className="flex flex-wrap gap-4 p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
							<div className="flex-1 min-w-[200px]">
								<Label htmlFor="product-select">选择产品</Label>
								<Select
									value={selectedProduct}
									onValueChange={setSelectedProduct}
								>
									<SelectTrigger>
										<SelectValue placeholder="选择产品" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="all">
											所有产品
										</SelectItem>
										{products &&
											products.length > 0 &&
											products.map((product) => (
												<SelectItem
													key={product.id}
													value={product.id}
												>
													{product.name}
												</SelectItem>
											))}
									</SelectContent>
								</Select>
							</div>

							<div className="flex-1 min-w-[200px]">
								<Label htmlFor="status-select">筛选状态</Label>
								<Select
									value={selectedStatus}
									onValueChange={setSelectedStatus}
								>
									<SelectTrigger>
										<SelectValue placeholder="选择状态" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="all">
											所有状态
										</SelectItem>
										{Object.entries(statusConfig).map(
											([key, config]) => (
												<SelectItem
													key={key}
													value={key}
												>
													{config.label}
												</SelectItem>
											),
										)}
									</SelectContent>
								</Select>
							</div>

							<div className="flex items-end">
								<Button
									variant="outline"
									size="sm"
									onClick={loadData}
									disabled={isLoading}
								>
									{isLoading ? (
										<Loader2 className="h-4 w-4 mr-2 animate-spin" />
									) : (
										<RefreshCw className="h-4 w-4 mr-2" />
									)}
									刷新
								</Button>
							</div>
						</div>

						{/* 特性列表 */}
						<div className="space-y-4">
							{isLoading ? (
								<div className="text-center py-8">
									<Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
									<p className="text-muted-foreground">
										加载中...
									</p>
								</div>
							) : filteredFeatures.length === 0 ? (
								<div className="text-center py-8">
									<p className="text-muted-foreground">
										暂无特性请求数据
									</p>
									<p className="text-sm text-muted-foreground mt-2">
										请先到{" "}
										<a
											href="/app/feature-voting/init"
											className="text-primary hover:underline"
										>
											数据初始化页面
										</a>{" "}
										创建测试数据
									</p>
								</div>
							) : (
								filteredFeatures.map((feature) => {
									const StatusIcon =
										statusConfig[
											feature.status as keyof typeof statusConfig
										]?.icon || Clock;
									return (
										<Card
											key={feature.id}
											className="hover:shadow-md transition-shadow"
										>
											<CardContent className="p-6">
												<div className="flex gap-4">
													{/* 投票按钮 */}
													<div className="flex flex-col items-center gap-1">
														<Button
															variant={
																feature.status ===
																"completed"
																	? "secondary"
																	: "outline"
															}
															size="sm"
															className="flex flex-col h-auto py-2 px-3 min-w-[60px]"
															onClick={() =>
																handleVote(
																	feature.id,
																	feature.hasVoted,
																)
															}
															style={{
																backgroundColor:
																	statusConfig[
																		feature.status as keyof typeof statusConfig
																	]?.color ||
																	"#e5e7eb",
															}}
														>
															<ChevronUp
																className={cn(
																	"h-4 w-4 transition-transform",
																	feature.hasVoted &&
																		"scale-110",
																)}
															/>
															<span className="text-xs font-medium">
																{feature.voteCount ||
																	0}
															</span>
														</Button>
													</div>

													{/* 内容 */}
													<div className="flex-1 space-y-3">
														<div className="flex items-start justify-between gap-4">
															<h3 className="text-lg font-semibold text-foreground">
																{feature.title}
															</h3>
															<div
																className={cn(
																	"flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border",
																	statusConfig[
																		feature.status as keyof typeof statusConfig
																	]?.color ||
																		statusConfig
																			.under_consideration
																			.color,
																)}
															>
																<StatusIcon className="h-3 w-3" />
																{statusConfig[
																	feature.status as keyof typeof statusConfig
																]?.label ||
																	"未知状态"}
															</div>
														</div>

														<p className="text-muted-foreground">
															{
																feature.description
															}
														</p>

														<div className="flex items-center gap-4 text-sm text-muted-foreground">
															<span>
																由{" "}
																{feature.authorName ||
																	"匿名用户"}{" "}
																提交
															</span>
															<span>•</span>
															<span>
																{new Date(
																	feature.createdAt,
																).toLocaleDateString()}
															</span>
															<span>•</span>
															<span>
																{(
																	products ||
																	[]
																).find(
																	(p) =>
																		p.id ===
																		feature.productId,
																)?.name ||
																	feature.productId}
															</span>
														</div>
													</div>
												</div>
											</CardContent>
										</Card>
									);
								})
							)}
						</div>
					</TabsContent>

					{/* 提交页面 */}
					<TabsContent value="submit" className="space-y-6">
						<SubmitFeatureForm
							products={products}
							onSubmit={handleSubmit}
						/>
					</TabsContent>
				</Tabs>
			</div>

			{/* 开发者信息 */}
			<div className="mt-12 p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg border">
				<h3 className="text-lg font-semibold mb-2">开发者信息</h3>
				<div className="text-sm text-muted-foreground space-y-1">
					<p>
						<strong>当前实现</strong>:
						内联实现，避免shared-ui包导入问题
					</p>
					<p>
						<strong>API路由</strong>:
						packages/api/src/routes/feature-requests.ts
					</p>
					<p>
						<strong>数据库</strong>:
						packages/database/prisma/schema.prisma
					</p>
					<p>
						<strong>数据初始化</strong>:{" "}
						<a
							href="/app/feature-voting/init"
							className="text-primary hover:underline"
						>
							/app/feature-voting/init
						</a>
					</p>
				</div>
			</div>
		</div>
	);
}

/**
 * 提交特性表单组件
 */
function SubmitFeatureForm({
	products = [],
	onSubmit,
}: { products?: any[]; onSubmit: (data: any) => Promise<boolean> }) {
	const [formData, setFormData] = useState({
		productId: "",
		title: "",
		description: "",
		authorName: "",
		authorEmail: "",
	});
	const [isSubmitting, setIsSubmitting] = useState(false);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!formData.productId || !formData.title || !formData.description) {
			toast.error("请填写必填字段");
			return;
		}

		setIsSubmitting(true);

		try {
			const success = await onSubmit(formData);
			if (success) {
				setFormData({
					productId: "",
					title: "",
					description: "",
					authorName: "",
					authorEmail: "",
				});
			}
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Plus className="h-5 w-5" />
					提交新的特性请求
				</CardTitle>
			</CardHeader>
			<CardContent>
				<form onSubmit={handleSubmit} className="space-y-4">
					<div>
						<Label htmlFor="product">选择产品 *</Label>
						<Select
							value={formData.productId}
							onValueChange={(value) =>
								setFormData((prev) => ({
									...prev,
									productId: value,
								}))
							}
						>
							<SelectTrigger>
								<SelectValue placeholder="选择产品" />
							</SelectTrigger>
							<SelectContent>
								{products &&
									products.length > 0 &&
									products.map((product) => (
										<SelectItem
											key={product.id}
											value={product.id}
										>
											{product.name}
										</SelectItem>
									))}
							</SelectContent>
						</Select>
					</div>

					<div>
						<Label htmlFor="title">特性标题 *</Label>
						<Input
							id="title"
							value={formData.title}
							onChange={(e) =>
								setFormData((prev) => ({
									...prev,
									title: e.target.value,
								}))
							}
							placeholder="简洁描述你想要的功能特性"
							required
						/>
					</div>

					<div>
						<Label htmlFor="description">详细描述 *</Label>
						<Textarea
							id="description"
							value={formData.description}
							onChange={(e) =>
								setFormData((prev) => ({
									...prev,
									description: e.target.value,
								}))
							}
							placeholder="详细描述这个功能特性，包括使用场景、预期效果等"
							rows={4}
							required
						/>
					</div>

					<div>
						<Label htmlFor="author">你的名字（可选）</Label>
						<Input
							id="author"
							value={formData.authorName}
							onChange={(e) =>
								setFormData((prev) => ({
									...prev,
									authorName: e.target.value,
								}))
							}
							placeholder="留下你的名字，让其他人知道是谁提出的好想法"
						/>
					</div>

					<div>
						<Label htmlFor="email">邮箱（可选）</Label>
						<Input
							id="email"
							type="email"
							value={formData.authorEmail}
							onChange={(e) =>
								setFormData((prev) => ({
									...prev,
									authorEmail: e.target.value,
								}))
							}
							placeholder="留下邮箱，我们会在功能上线时通知你"
						/>
					</div>

					<Button
						type="submit"
						className="w-full"
						disabled={isSubmitting}
					>
						{isSubmitting ? (
							<Loader2 className="h-4 w-4 mr-2 animate-spin" />
						) : (
							<Send className="h-4 w-4 mr-2" />
						)}
						{isSubmitting ? "提交中..." : "提交特性请求"}
					</Button>
				</form>
			</CardContent>
		</Card>
	);
}
