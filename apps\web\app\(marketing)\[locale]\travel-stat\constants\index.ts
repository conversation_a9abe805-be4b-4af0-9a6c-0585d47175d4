// =============================================================================
// 统一常量配置 - Travel Stat 模块
// 提供所有配置常量的统一管理，避免魔法数字和字符串分散
// =============================================================================

import type {
	AnimationTheme,
	AtmosphereConfig,
	AtmosphereTheme,
	ColorTheme,
	ColorThemeType,
	MapProjectionType,
	MapStyleType,
	MarkerStyleType,
	SocialPlatform,
} from "../types";

// ===== 地图配置常量 =====
export const MAP_CONFIG = {
	// Mapbox 访问令牌和样式URL
	ACCESS_TOKEN: process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN || "",

	// 默认视图状态
	DEFAULT_VIEW_STATE: {
		longitude: 116.4074,
		latitude: 39.9042,
		zoom: 2,
		pitch: 0,
		bearing: 0,
	},

	// 地图样式URLs
	STYLE_URLS: {
		streets: "mapbox://styles/mapbox/streets-v12",
		outdoors: "mapbox://styles/mapbox/outdoors-v12",
		light: "mapbox://styles/mapbox/light-v11",
		dark: "mapbox://styles/mapbox/dark-v11",
		satellite: "mapbox://styles/mapbox/satellite-v9",
		"satellite-streets": "mapbox://styles/mapbox/satellite-streets-v12",
		"navigation-day": "mapbox://styles/mapbox/navigation-day-v1",
		"navigation-night": "mapbox://styles/mapbox/navigation-night-v1",
	} as Record<MapStyleType, string>,

	// 地图投影配置
	PROJECTIONS: {
		mercator: { name: "mercator" },
		globe: { name: "globe" },
		albers: { name: "albers" },
		lambertConformalConic: { name: "lambertConformalConic" },
		equalEarth: { name: "equalEarth" },
		equirectangular: { name: "equirectangular" },
		naturalEarth: { name: "naturalEarth" },
		winkelTripel: { name: "winkelTripel" },
	} as Record<MapProjectionType, { name: string }>,

	// 地图限制
	BOUNDS: {
		maxZoom: 20,
		minZoom: 0.5,
		maxPitch: 85,
	},
} as const;

// ===== 大气层配置常量 =====
export const ATMOSPHERE_CONFIGS: Record<AtmosphereTheme, AtmosphereConfig> = {
	day: {
		color: "white",
		"high-color": "#87CEEB",
		"space-color": "#E0F6FF",
		"star-intensity": 0.0,
		"horizon-blend": 0.1,
		range: [0.5, 10],
	},
	night: {
		color: "#1e293b",
		"high-color": "#0f172a",
		"space-color": "#020617",
		"star-intensity": 0.8,
		"horizon-blend": 0.3,
		range: [0.8, 12],
	},
	sunset: {
		color: "#f97316",
		"high-color": "#dc2626",
		"space-color": "#451a03",
		"star-intensity": 0.3,
		"horizon-blend": 0.4,
		range: [0.6, 8],
	},
	dawn: {
		color: "#fbbf24",
		"high-color": "#f59e0b",
		"space-color": "#fef3c7",
		"star-intensity": 0.1,
		"horizon-blend": 0.25,
		range: [0.5, 9],
	},
	aurora: {
		color: "#10b981",
		"high-color": "#059669",
		"space-color": "#064e3b",
		"star-intensity": 0.6,
		"horizon-blend": 0.5,
		range: [0.7, 15],
	},
	"deep-space": {
		color: "#1f2937",
		"high-color": "#111827",
		"space-color": "#000000",
		"star-intensity": 1.0,
		"horizon-blend": 0.8,
		range: [1.0, 20],
	},
	ocean: {
		color: "#0ea5e9",
		"high-color": "#0284c7",
		"space-color": "#bae6fd",
		"star-intensity": 0.0,
		"horizon-blend": 0.2,
		range: [0.4, 6],
	},
	minimal: {
		color: "#f8fafc",
		"high-color": "#e2e8f0",
		"space-color": "#ffffff",
		"star-intensity": 0.0,
		"horizon-blend": 0.05,
		range: [0.3, 5],
	},
};

// ===== 动画主题配置 =====
export const ANIMATION_THEMES = {
	none: "无动画",
	"shooting-stars": "流星雨",
	"floating-particles": "浮动粒子",
	aurora: "极光",
	minimal: "简约",
	galaxy: "银河",
} as Record<AnimationTheme, string>;

// ===== 颜色主题配置 =====
export const COLOR_THEMES: Record<ColorThemeType, ColorTheme> = {
	"classic-blue-green": {
		id: "classic-blue-green",
		name: "经典蓝绿",
		description: "经典的蓝绿色渐变，适合大多数场景",
		category: "classic",
		colors: {
			unvisited: {
				rgba: "rgba(229, 231, 235, 0.8)",
				hex: "#e5e7eb",
				description: "未访问",
			},
			level1: {
				rgba: "rgba(219, 234, 254, 0.8)",
				hex: "#dbeafe",
				description: "1次访问",
			},
			level2: {
				rgba: "rgba(147, 197, 253, 0.8)",
				hex: "#93c5fd",
				description: "2次访问",
			},
			level3: {
				rgba: "rgba(59, 130, 246, 0.8)",
				hex: "#3b82f6",
				description: "3次访问",
			},
			level4: {
				rgba: "rgba(29, 78, 216, 0.8)",
				hex: "#1d4ed8",
				description: "4次访问",
			},
			level5: {
				rgba: "rgba(30, 64, 175, 0.8)",
				hex: "#1e40af",
				description: "5次访问",
			},
			level6to10: {
				rgba: "rgba(30, 58, 138, 0.8)",
				hex: "#1e3a8a",
				description: "6-10次访问",
			},
			level10plus: {
				rgba: "rgba(23, 37, 84, 0.8)",
				hex: "#172554",
				description: "10+次访问",
			},
		},
		previewColors: ["#dbeafe", "#3b82f6", "#1e40af", "#172554"],
		recommendedForMapStyles: ["light", "satellite"],
	},
	"warm-sunset": {
		id: "warm-sunset",
		name: "暖色日落",
		description: "温暖的日落色系，营造浪漫氛围",
		category: "nature",
		colors: {
			unvisited: {
				rgba: "rgba(254, 249, 195, 0.8)",
				hex: "#fef9c3",
				description: "未访问",
			},
			level1: {
				rgba: "rgba(254, 240, 138, 0.8)",
				hex: "#fef08a",
				description: "1次访问",
			},
			level2: {
				rgba: "rgba(252, 211, 77, 0.8)",
				hex: "#fcd34d",
				description: "2次访问",
			},
			level3: {
				rgba: "rgba(245, 158, 11, 0.8)",
				hex: "#f59e0b",
				description: "3次访问",
			},
			level4: {
				rgba: "rgba(217, 119, 6, 0.8)",
				hex: "#d97706",
				description: "4次访问",
			},
			level5: {
				rgba: "rgba(194, 65, 12, 0.8)",
				hex: "#c2410c",
				description: "5次访问",
			},
			level6to10: {
				rgba: "rgba(154, 52, 18, 0.8)",
				hex: "#9a3412",
				description: "6-10次访问",
			},
			level10plus: {
				rgba: "rgba(124, 45, 18, 0.8)",
				hex: "#7c2d12",
				description: "10+次访问",
			},
		},
		previewColors: ["#fef08a", "#f59e0b", "#c2410c", "#7c2d12"],
		recommendedForMapStyles: ["dark", "satellite-streets"],
	},
	"cool-ocean": {
		id: "cool-ocean",
		name: "冷色海洋",
		description: "清新的海洋色系，给人宁静感受",
		category: "nature",
		colors: {
			unvisited: {
				rgba: "rgba(240, 249, 255, 0.8)",
				hex: "#f0f9ff",
				description: "未访问",
			},
			level1: {
				rgba: "rgba(224, 242, 254, 0.8)",
				hex: "#e0f2fe",
				description: "1次访问",
			},
			level2: {
				rgba: "rgba(125, 211, 252, 0.8)",
				hex: "#7dd3fc",
				description: "2次访问",
			},
			level3: {
				rgba: "rgba(14, 165, 233, 0.8)",
				hex: "#0ea5e9",
				description: "3次访问",
			},
			level4: {
				rgba: "rgba(2, 132, 199, 0.8)",
				hex: "#0284c7",
				description: "4次访问",
			},
			level5: {
				rgba: "rgba(3, 105, 161, 0.8)",
				hex: "#0369a1",
				description: "5次访问",
			},
			level6to10: {
				rgba: "rgba(12, 74, 110, 0.8)",
				hex: "#0c4a6e",
				description: "6-10次访问",
			},
			level10plus: {
				rgba: "rgba(8, 47, 73, 0.8)",
				hex: "#082f49",
				description: "10+次访问",
			},
		},
		previewColors: ["#e0f2fe", "#0ea5e9", "#0369a1", "#082f49"],
		recommendedForMapStyles: ["light", "outdoors"],
	},
	"vibrant-rainbow": {
		id: "vibrant-rainbow",
		name: "彩虹色系",
		description: "活力四射的彩虹渐变",
		category: "vibrant",
		colors: {
			unvisited: {
				rgba: "rgba(243, 244, 246, 0.8)",
				hex: "#f3f4f6",
				description: "未访问",
			},
			level1: {
				rgba: "rgba(254, 202, 202, 0.8)",
				hex: "#fecaca",
				description: "1次访问",
			},
			level2: {
				rgba: "rgba(252, 165, 165, 0.8)",
				hex: "#fca5a5",
				description: "2次访问",
			},
			level3: {
				rgba: "rgba(251, 146, 60, 0.8)",
				hex: "#fb923c",
				description: "3次访问",
			},
			level4: {
				rgba: "rgba(250, 204, 21, 0.8)",
				hex: "#facc15",
				description: "4次访问",
			},
			level5: {
				rgba: "rgba(34, 197, 94, 0.8)",
				hex: "#22c55e",
				description: "5次访问",
			},
			level6to10: {
				rgba: "rgba(59, 130, 246, 0.8)",
				hex: "#3b82f6",
				description: "6-10次访问",
			},
			level10plus: {
				rgba: "rgba(147, 51, 234, 0.8)",
				hex: "#9333ea",
				description: "10+次访问",
			},
		},
		previewColors: ["#fecaca", "#facc15", "#22c55e", "#9333ea"],
		recommendedForMapStyles: ["dark", "light"],
	},
	"earth-tones": {
		id: "earth-tones",
		name: "大地色系",
		description: "自然的大地色调",
		category: "nature",
		colors: {
			unvisited: {
				rgba: "rgba(250, 250, 249, 0.8)",
				hex: "#fafaf9",
				description: "未访问",
			},
			level1: {
				rgba: "rgba(231, 229, 228, 0.8)",
				hex: "#e7e5e4",
				description: "1次访问",
			},
			level2: {
				rgba: "rgba(168, 162, 158, 0.8)",
				hex: "#a8a29e",
				description: "2次访问",
			},
			level3: {
				rgba: "rgba(120, 113, 108, 0.8)",
				hex: "#78716c",
				description: "3次访问",
			},
			level4: {
				rgba: "rgba(87, 83, 78, 0.8)",
				hex: "#57534e",
				description: "4次访问",
			},
			level5: {
				rgba: "rgba(68, 64, 60, 0.8)",
				hex: "#44403c",
				description: "5次访问",
			},
			level6to10: {
				rgba: "rgba(41, 37, 36, 0.8)",
				hex: "#292524",
				description: "6-10次访问",
			},
			level10plus: {
				rgba: "rgba(28, 25, 23, 0.8)",
				hex: "#1c1917",
				description: "10+次访问",
			},
		},
		previewColors: ["#e7e5e4", "#78716c", "#44403c", "#1c1917"],
		recommendedForMapStyles: ["outdoors", "satellite"],
	},
	"purple-pink": {
		id: "purple-pink",
		name: "紫粉色系",
		description: "梦幻的紫粉渐变",
		category: "vibrant",
		colors: {
			unvisited: {
				rgba: "rgba(253, 244, 255, 0.8)",
				hex: "#fdf4ff",
				description: "未访问",
			},
			level1: {
				rgba: "rgba(250, 232, 255, 0.8)",
				hex: "#fae8ff",
				description: "1次访问",
			},
			level2: {
				rgba: "rgba(240, 171, 252, 0.8)",
				hex: "#f0abfc",
				description: "2次访问",
			},
			level3: {
				rgba: "rgba(217, 70, 239, 0.8)",
				hex: "#d946ef",
				description: "3次访问",
			},
			level4: {
				rgba: "rgba(168, 85, 247, 0.8)",
				hex: "#a855f7",
				description: "4次访问",
			},
			level5: {
				rgba: "rgba(147, 51, 234, 0.8)",
				hex: "#9333ea",
				description: "5次访问",
			},
			level6to10: {
				rgba: "rgba(126, 34, 206, 0.8)",
				hex: "#7e22ce",
				description: "6-10次访问",
			},
			level10plus: {
				rgba: "rgba(88, 28, 135, 0.8)",
				hex: "#581c87",
				description: "10+次访问",
			},
		},
		previewColors: ["#fae8ff", "#d946ef", "#9333ea", "#581c87"],
		recommendedForMapStyles: ["dark", "navigation-night"],
	},
	monochrome: {
		id: "monochrome",
		name: "单色系",
		description: "简洁的黑白灰色调",
		category: "minimal",
		colors: {
			unvisited: {
				rgba: "rgba(255, 255, 255, 0.8)",
				hex: "#ffffff",
				description: "未访问",
			},
			level1: {
				rgba: "rgba(249, 250, 251, 0.8)",
				hex: "#f9fafb",
				description: "1次访问",
			},
			level2: {
				rgba: "rgba(209, 213, 219, 0.8)",
				hex: "#d1d5db",
				description: "2次访问",
			},
			level3: {
				rgba: "rgba(156, 163, 175, 0.8)",
				hex: "#9ca3af",
				description: "3次访问",
			},
			level4: {
				rgba: "rgba(107, 114, 128, 0.8)",
				hex: "#6b7280",
				description: "4次访问",
			},
			level5: {
				rgba: "rgba(75, 85, 99, 0.8)",
				hex: "#4b5563",
				description: "5次访问",
			},
			level6to10: {
				rgba: "rgba(55, 65, 81, 0.8)",
				hex: "#374151",
				description: "6-10次访问",
			},
			level10plus: {
				rgba: "rgba(17, 24, 39, 0.8)",
				hex: "#111827",
				description: "10+次访问",
			},
		},
		previewColors: ["#f9fafb", "#9ca3af", "#4b5563", "#111827"],
		recommendedForMapStyles: ["light", "dark"],
	},
	"high-contrast": {
		id: "high-contrast",
		name: "高对比度",
		description: "高对比度配色，更易识别",
		category: "minimal",
		colors: {
			unvisited: {
				rgba: "rgba(255, 255, 255, 0.9)",
				hex: "#ffffff",
				description: "未访问",
			},
			level1: {
				rgba: "rgba(254, 226, 226, 0.9)",
				hex: "#fee2e2",
				description: "1次访问",
			},
			level2: {
				rgba: "rgba(252, 165, 165, 0.9)",
				hex: "#fca5a5",
				description: "2次访问",
			},
			level3: {
				rgba: "rgba(239, 68, 68, 0.9)",
				hex: "#ef4444",
				description: "3次访问",
			},
			level4: {
				rgba: "rgba(185, 28, 28, 0.9)",
				hex: "#b91c1c",
				description: "4次访问",
			},
			level5: {
				rgba: "rgba(127, 29, 29, 0.9)",
				hex: "#7f1d1d",
				description: "5次访问",
			},
			level6to10: {
				rgba: "rgba(69, 10, 10, 0.9)",
				hex: "#450a0a",
				description: "6-10次访问",
			},
			level10plus: {
				rgba: "rgba(0, 0, 0, 0.9)",
				hex: "#000000",
				description: "10+次访问",
			},
		},
		previewColors: ["#fee2e2", "#ef4444", "#7f1d1d", "#000000"],
		recommendedForMapStyles: ["light", "satellite"],
	},
	"pastel-soft": {
		id: "pastel-soft",
		name: "柔和糖果色",
		description: "温柔的糖果色系",
		category: "modern",
		colors: {
			unvisited: {
				rgba: "rgba(255, 251, 235, 0.8)",
				hex: "#fffbeb",
				description: "未访问",
			},
			level1: {
				rgba: "rgba(254, 240, 138, 0.8)",
				hex: "#fef08a",
				description: "1次访问",
			},
			level2: {
				rgba: "rgba(251, 207, 232, 0.8)",
				hex: "#fbcfe8",
				description: "2次访问",
			},
			level3: {
				rgba: "rgba(196, 181, 253, 0.8)",
				hex: "#c4b5fd",
				description: "3次访问",
			},
			level4: {
				rgba: "rgba(165, 243, 252, 0.8)",
				hex: "#a5f3fc",
				description: "4次访问",
			},
			level5: {
				rgba: "rgba(167, 243, 208, 0.8)",
				hex: "#a7f3d0",
				description: "5次访问",
			},
			level6to10: {
				rgba: "rgba(134, 239, 172, 0.8)",
				hex: "#86efac",
				description: "6-10次访问",
			},
			level10plus: {
				rgba: "rgba(74, 222, 128, 0.8)",
				hex: "#4ade80",
				description: "10+次访问",
			},
		},
		previewColors: ["#fef08a", "#fbcfe8", "#a5f3fc", "#4ade80"],
		recommendedForMapStyles: ["light", "outdoors"],
	},
	"neon-bright": {
		id: "neon-bright",
		name: "霓虹亮色",
		description: "鲜艳的霓虹色彩",
		category: "vibrant",
		colors: {
			unvisited: {
				rgba: "rgba(3, 7, 18, 0.8)",
				hex: "#030712",
				description: "未访问",
			},
			level1: {
				rgba: "rgba(34, 197, 94, 0.8)",
				hex: "#22c55e",
				description: "1次访问",
			},
			level2: {
				rgba: "rgba(14, 165, 233, 0.8)",
				hex: "#0ea5e9",
				description: "2次访问",
			},
			level3: {
				rgba: "rgba(168, 85, 247, 0.8)",
				hex: "#a855f7",
				description: "3次访问",
			},
			level4: {
				rgba: "rgba(236, 72, 153, 0.8)",
				hex: "#ec4899",
				description: "4次访问",
			},
			level5: {
				rgba: "rgba(239, 68, 68, 0.8)",
				hex: "#ef4444",
				description: "5次访问",
			},
			level6to10: {
				rgba: "rgba(249, 115, 22, 0.8)",
				hex: "#f97316",
				description: "6-10次访问",
			},
			level10plus: {
				rgba: "rgba(250, 204, 21, 0.8)",
				hex: "#facc15",
				description: "10+次访问",
			},
		},
		previewColors: ["#22c55e", "#a855f7", "#ef4444", "#facc15"],
		recommendedForMapStyles: ["dark", "navigation-night"],
	},
};

// ===== 标记样式配置 =====
export const MARKER_STYLES = {
	classic: "经典标记",
	"gradient-pulse": "渐变脉冲",
	"neon-glow": "霓虹光晕",
	"particle-effect": "粒子效果",
	constellation: "星座连线",
	seasonal: "季节主题",
	artistic: "艺术风格",
	badge: "徽章样式",
} as Record<MarkerStyleType, string>;

// ===== 社交平台配置 =====
export const SOCIAL_PLATFORM_CONFIG = {
	instagram: { width: 1080, height: 1080, name: "Instagram" },
	wechat: { width: 1200, height: 900, name: "微信朋友圈" },
	weibo: { width: 1080, height: 1350, name: "微博" },
	twitter: { width: 1200, height: 675, name: "Twitter" },
	facebook: { width: 1200, height: 630, name: "Facebook" },
} as Record<SocialPlatform, { width: number; height: number; name: string }>;

// ===== 导出质量配置 =====
export const EXPORT_QUALITY_CONFIG = {
	low: { scale: 1, quality: 0.6, description: "低质量 (快速)" },
	medium: { scale: 1.5, quality: 0.8, description: "中等质量 (平衡)" },
	high: { scale: 2, quality: 1.0, description: "高质量 (精美)" },
};

// ===== 动画配置 =====
export const ANIMATION_CONFIG = {
	duration: {
		short: 200,
		medium: 300,
		long: 500,
	},
	easing: {
		ease: "ease",
		easeInOut: "ease-in-out",
		spring: "cubic-bezier(0.175, 0.885, 0.32, 1.275)",
	},
} as const;

// ===== 尺寸配置 =====
export const SIZE_CONFIG = {
	marker: {
		small: 0.8,
		medium: 1.0,
		large: 1.2,
		xlarge: 1.5,
	},
	popup: {
		maxWidth: 300,
		minWidth: 200,
	},
	panel: {
		width: 320,
		maxHeight: 600,
	},
} as const;

// ===== 应用元信息 =====
export const APP_META = {
	name: "旅行足迹统计工具",
	description: "记录和可视化你的旅行足迹",
	version: "1.0.0",
	author: "Travel Stat Team",
} as const;
