"use client";

import { Card } from "@ui/components/card";
import { Input } from "@ui/components/input";
import { MapPin, Search } from "lucide-react";
import type { GeocodeFeature } from "../../types";

import { useTravelStatTranslations } from "../../hooks/useTravelStatTranslations";

interface SearchBoxProps {
	searchQuery: string;
	searchResults: GeocodeFeature[];
	isSearching: boolean;
	showResults: boolean;
	onSearchChange: (value: string) => void;
	onSelectResult: (result: GeocodeFeature) => void;
	onFocus?: () => void;
	isPanelExpanded?: boolean;
}

export function SearchBox({
	searchQuery,
	searchResults,
	isSearching,
	showResults,
	onSearchChange,
	onSelectResult,
	onFocus,
	isPanelExpanded,
}: SearchBoxProps) {
	const travelStatT = useTravelStatTranslations();

	return (
		<div className="relative w-full z-50">
			<div className="relative">
				<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
				<Input
					type="text"
					placeholder={travelStatT.searchBox.placeholder()}
					value={searchQuery}
					onChange={(e) => onSearchChange(e.target.value)}
					onFocus={() => onFocus?.()}
					className={`pl-9 pr-4 py-2 text-sm bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 focus:border-sky-400 focus:ring-sky-400 dark:focus:border-sky-400 transition-all duration-200 ${
						isPanelExpanded
							? "ring-2 ring-sky-400 border-sky-400"
							: ""
					}`}
				/>
				{isSearching && (
					<div className="absolute right-3 top-1/2 transform -translate-y-1/2">
						<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-sky-500" />
					</div>
				)}
			</div>

			{/* 搜索结果 */}
			{showResults && searchResults.length > 0 && (
				<Card className="absolute top-full left-0 right-0 mt-2 z-[9999] bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-600 shadow-xl max-h-64 overflow-y-auto">
					<div className="p-1">
						{searchResults.map((result) => (
							<button
								key={result.id}
								type="button"
								onClick={() => onSelectResult(result)}
								className="w-full text-left p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded transition-colors"
							>
								<div className="flex items-center gap-2">
									<MapPin className="w-3 h-3 text-sky-500 flex-shrink-0" />
									<span className="text-xs text-gray-700 dark:text-gray-300 line-clamp-2">
										{result.place_name}
									</span>
								</div>
							</button>
						))}
					</div>
				</Card>
			)}
		</div>
	);
}
