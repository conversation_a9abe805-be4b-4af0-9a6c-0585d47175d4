import { LocaleLink } from "@i18n/routing";
import { config } from "@repo/config";
import { Logo } from "@shared/components/Logo";
import { useTranslations } from "next-intl";

export function Footer() {
	const t = useTranslations();

	return (
		<footer className="border-t py-2 text-foreground/60 text-sm">
			<div className="container flex flex-wrap items-center justify-between gap-4">
				<div className="flex items-center gap-2">
					<Logo className="opacity-70 grayscale" />
					<span className="text-sm opacity-70">
						© {new Date().getFullYear()} MapMoment. All rights
						reserved.
					</span>
				</div>

				<div className="flex items-center gap-6">
					{config.contactForm.enabled && (
						<LocaleLink
							href="/contact"
							className="hover:text-foreground transition-colors"
						>
							{t("common.menu.contact")}
						</LocaleLink>
					)}

					<LocaleLink
						href="/legal/privacy-policy"
						className="hover:text-foreground transition-colors"
					>
						Privacy policy
					</LocaleLink>

					<LocaleLink
						href="/legal/terms"
						className="hover:text-foreground transition-colors"
					>
						Terms and conditions
					</LocaleLink>
				</div>
			</div>
		</footer>
	);
}
