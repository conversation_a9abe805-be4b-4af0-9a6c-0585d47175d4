import {
	CreateBucketCommand,
	DeleteObjectsCommand,
	GetObjectCommand,
	Put<PERSON><PERSON>Command,
	S3Client,
} from "@aws-sdk/client-s3";
import { getSignedUrl as getS3SignedUrl } from "@aws-sdk/s3-request-presigner";
import { logger } from "@repo/logs";
import type {
	BatchDeleteOptions,
	BatchDeleteResult,
	BatchUploadOptions,
	BatchUploadResult,
	StorageProvider,
	StorageProviderConfig,
	UploadFileOptions,
	UploadFileResult,
} from "../../types";

/**
 * AWS S3/兼容S3 API的存储提供商实现
 */
export class S3Provider implements StorageProvider {
	private client: S3Client;
	private config: StorageProviderConfig;

	constructor(config: StorageProviderConfig) {
		this.config = config;
		this.client = this.createClient();
	}

	/**
	 * 获取提供商名称
	 */
	getProviderName(): string {
		return "s3";
	}

	/**
	 * 创建S3客户端
	 */
	private createClient(): S3Client {
		const {
			endpoint,
			region = "auto",
			accessKeyId,
			secretAccessKey,
			options,
		} = this.config;

		if (!endpoint) {
			throw new Error("Missing endpoint in S3 provider config");
		}

		if (!accessKeyId) {
			throw new Error("Missing accessKeyId in S3 provider config");
		}

		if (!secretAccessKey) {
			throw new Error("Missing secretAccessKey in S3 provider config");
		}

		return new S3Client({
			region,
			endpoint,
			forcePathStyle: true,
			credentials: {
				accessKeyId,
				secretAccessKey,
			},
			...options,
		});
	}

	/**
	 * 创建存储桶
	 */
	async createBucket(
		name: string,
		options?: { public?: boolean },
	): Promise<void> {
		try {
			await this.client.send(
				new CreateBucketCommand({
					Bucket: name,
				}),
			);

			// 如果需要设置为公开访问，这里可以添加设置桶策略的代码
			if (options?.public) {
				// 实现公开访问策略设置
				logger.info(`设置桶 ${name} 为公开访问`);
			}
		} catch (e) {
			logger.error(e);
			throw new Error(`无法创建存储桶: ${name}`);
		}
	}

	/**
	 * 获取签名上传URL
	 */
	async getSignedUploadUrl(
		path: string,
		options: { bucket: string; contentType?: string; region?: string },
	): Promise<string> {
		try {
			// 构建一个包含所有必要参数的命令对象
			const command = new PutObjectCommand({
				Bucket: options.bucket,
				Key: path,
				ContentType: options.contentType || "application/octet-stream",
			});

			// 获取签名URL
			return await getS3SignedUrl(this.client, command, {
				expiresIn: 60,
			});
		} catch (e) {
			logger.error(e);
			throw new Error("无法获取签名上传URL");
		}
	}

	/**
	 * 获取签名访问URL
	 */
	async getSignedUrl(
		path: string,
		options: { bucket: string; expiresIn?: number; region?: string },
	): Promise<string> {
		try {
			return getS3SignedUrl(
				this.client,
				new GetObjectCommand({ Bucket: options.bucket, Key: path }),
				{ expiresIn: options.expiresIn || 3600 },
			);
		} catch (e) {
			logger.error(e);
			throw new Error("无法获取签名访问URL");
		}
	}

	/**
	 * 批量获取签名上传URL
	 */
	async getBatchSignedUploadUrls(
		options: BatchUploadOptions,
	): Promise<BatchUploadResult> {
		const {
			bucket,
			paths,
			contentType = "application/octet-stream",
		} = options;
		const result: BatchUploadResult = {
			urls: {},
			failedPaths: [],
			errors: {},
		};

		// 使用 Promise.allSettled 并行处理所有路径
		const promises = paths.map(async (path) => {
			try {
				const url = await getS3SignedUrl(
					this.client,
					new PutObjectCommand({
						Bucket: bucket,
						Key: path,
						ContentType: contentType,
					}),
					{
						expiresIn: 60,
					},
				);
				return { path, url, success: true };
			} catch (error) {
				logger.error(`无法为路径 ${path} 获取签名上传URL:`, error);
				return {
					path,
					success: false,
					error:
						error instanceof Error ? error.message : String(error),
				};
			}
		});

		const results = await Promise.allSettled(promises);

		// 处理结果
		results.forEach((promiseResult) => {
			if (promiseResult.status === "fulfilled") {
				const { path, url, success, error } = promiseResult.value;
				if (success) {
					result.urls[path] = url || "";
				} else {
					result.failedPaths!.push(path);
					result.errors![path] = error || "";
				}
			} else {
				// 处理 Promise.allSettled 中被拒绝的情况
				logger.error(
					"处理批量签名URL时发生Promise拒绝:",
					promiseResult.reason,
				);
			}
		});

		// 如果没有失败的路径，清除这些字段
		if (result.failedPaths?.length === 0) {
			delete result.failedPaths;
			delete result.errors;
		}

		return result;
	}

	/**
	 * 批量删除对象
	 */
	async batchDeleteObjects(
		options: BatchDeleteOptions,
	): Promise<BatchDeleteResult> {
		const { bucket, paths } = options;
		const result: BatchDeleteResult = {
			deletedPaths: [],
			failedPaths: [],
			errors: {},
		};

		if (paths.length === 0) {
			return result;
		}

		try {
			// 使用S3的批量删除API
			const deleteResponse = await this.client.send(
				new DeleteObjectsCommand({
					Bucket: bucket,
					Delete: {
						Objects: paths.map((path) => ({ Key: path })),
						Quiet: false, // 返回详细信息
					},
				}),
			);

			// 处理成功删除的对象
			if (deleteResponse.Deleted) {
				deleteResponse.Deleted.forEach((deleted) => {
					if (deleted.Key) {
						result.deletedPaths.push(deleted.Key);
					}
				});
			}

			// 处理删除失败的对象
			if (deleteResponse.Errors && deleteResponse.Errors.length > 0) {
				deleteResponse.Errors.forEach((error) => {
					if (error.Key) {
						result.failedPaths!.push(error.Key);
						result.errors![error.Key] = error.Message || "未知错误";
					}
				});
			}
		} catch (error) {
			// 如果整个批量删除操作失败
			logger.error("批量删除对象失败:", error);
			// 将所有路径标记为失败
			result.failedPaths = paths;
			const errorMessage =
				error instanceof Error ? error.message : String(error);
			paths.forEach((path) => {
				result.errors![path] = errorMessage;
			});
			result.deletedPaths = []; // 清空成功删除的列表
		}

		// 如果没有失败的路径，清除这些字段
		if (result.failedPaths?.length === 0) {
			delete result.failedPaths;
			delete result.errors;
		}

		return result;
	}

	/**
	 * 直接上传文件到S3
	 * @param options 上传选项
	 */
	async uploadFile(options: UploadFileOptions): Promise<UploadFileResult> {
		const {
			bucket,
			path,
			file,
			contentType = "application/octet-stream",
		} = options;

		try {
			// 准备上传命令
			const command = new PutObjectCommand({
				Bucket: bucket,
				Key: path,
				Body: file as any,
				ContentType: contentType,
			});

			// 执行上传
			const response = await this.client.send(command);

			return {
				success: true,
				path,
				etag: response.ETag
					? response.ETag.replace(/"/g, "")
					: undefined,
			};
		} catch (error) {
			logger.error(`上传文件到S3失败 (${bucket}/${path}):`, error);
			return {
				success: false,
				error: error instanceof Error ? error.message : String(error),
			};
		}
	}
}

/**
 * 创建S3存储提供商实例
 */
export function createS3Provider(
	config: StorageProviderConfig,
): StorageProvider {
	return new S3Provider(config);
}

/**
 * 使用环境变量创建S3存储提供商实例
 */
export function createS3ProviderFromEnv(): StorageProvider {
	const s3Endpoint = process.env.S3_ENDPOINT as string;
	if (!s3Endpoint) {
		throw new Error("缺少环境变量 S3_ENDPOINT");
	}

	const s3Region = (process.env.S3_REGION as string) || "auto";

	const s3AccessKeyId = process.env.S3_ACCESS_KEY_ID as string;
	if (!s3AccessKeyId) {
		throw new Error("缺少环境变量 S3_ACCESS_KEY_ID");
	}

	const s3SecretAccessKey = process.env.S3_SECRET_ACCESS_KEY as string;
	if (!s3SecretAccessKey) {
		throw new Error("缺少环境变量 S3_SECRET_ACCESS_KEY");
	}

	return createS3Provider({
		endpoint: s3Endpoint,
		region: s3Region,
		accessKeyId: s3AccessKeyId,
		secretAccessKey: s3SecretAccessKey,
	});
}
