import { useEffect } from "react";

export function useMapboxControlStyling() {
	useEffect(() => {
		// 添加自定义样式
		const styleSheet = document.createElement("style");
		styleSheet.textContent = `
			/* Mapbox 控件自定义样式 - 使用内联样式确保优先级 */
			.mapboxgl-ctrl-group {
				background: rgba(255, 255, 255, 0.9) !important;
				backdrop-filter: blur(8px) !important;
				border: 1px solid rgb(224, 242, 254) !important;
				border-radius: 10px !important;
				box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
				padding: 3px !important;
				margin: 6px !important;
				display: flex !important;
				flex-direction: column !important;
			}

			.mapboxgl-ctrl-group button,
			.mapboxgl-ctrl button {
				background: transparent !important;
				border: 1px solid transparent !important;
				border-radius: 6px !important;
				width: 28px !important;
				height: 28px !important;
				margin: 1px !important;
				display: flex !important;
				align-items: center !important;
				justify-content: center !important;
				transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1) !important;
				color: rgb(71, 85, 105) !important;
				position: relative !important;
				outline: none !important;
				font-size: 12px !important;
				font-weight: 500 !important;
				padding: 0 !important;
				flex-shrink: 0 !important;
			}

			.mapboxgl-ctrl-group button:hover,
			.mapboxgl-ctrl button:hover {
				background: rgb(248, 250, 252) !important;
				border-color: rgb(226, 232, 240) !important;
				color: rgb(51, 65, 85) !important;
			}

			.mapboxgl-ctrl-group button:active,
			.mapboxgl-ctrl button:active {
				background: rgb(241, 245, 249) !important;
				transform: none !important;
			}

			.mapboxgl-ctrl-group button:disabled,
			.mapboxgl-ctrl button:disabled {
				background: transparent !important;
				color: rgb(156, 163, 175) !important;
				cursor: not-allowed !important;
				opacity: 0.5 !important;
			}

			.mapboxgl-ctrl-geolocate.mapboxgl-ctrl-geolocate-active {
				background: rgb(239, 246, 255) !important;
				border-color: rgb(147, 197, 253) !important;
			}

			.mapboxgl-ctrl-geolocate.mapboxgl-ctrl-geolocate-active .mapboxgl-ctrl-icon {
				background-color: rgb(59, 130, 246) !important;
			}

			.mapboxgl-ctrl-scale {
				background: rgba(255, 255, 255, 0.9) !important;
				backdrop-filter: blur(8px) !important;
				border: 1px solid rgb(224, 242, 254) !important;
				border-radius: 6px !important;
				color: rgb(71, 85, 105) !important;
				font-size: 11px !important;
				font-weight: 500 !important;
				padding: 3px 6px !important;
				box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
				margin: 6px !important;
			}

			.mapboxgl-ctrl-zoom-in .mapboxgl-ctrl-icon,
			.mapboxgl-ctrl-zoom-out .mapboxgl-ctrl-icon,
			.mapboxgl-ctrl-compass .mapboxgl-ctrl-icon,
			.mapboxgl-ctrl-fullscreen .mapboxgl-ctrl-icon,
			.mapboxgl-ctrl-geolocate .mapboxgl-ctrl-icon {
				background-size: 18px 18px !important;
				opacity: 0.8 !important;
				background-position: center !important;
			}

			/* 特别调整缩放按钮的图标 */
			.mapboxgl-ctrl-zoom-in,
			.mapboxgl-ctrl-zoom-out {
				line-height: 1 !important;
				text-align: center !important;
			}

			.mapboxgl-ctrl-zoom-in .mapboxgl-ctrl-icon,
			.mapboxgl-ctrl-zoom-out .mapboxgl-ctrl-icon {
				width: 18px !important;
				height: 18px !important;
				background-size: 18px 18px !important;
				margin: auto !important;
			}

			/* 确保按钮垂直排列，移除默认分隔线 */
			.mapboxgl-ctrl-group > button:not(:first-child) {
				border-top: none !important;
				border-left: none !important;
				margin-top: 1px !important;
				margin-left: 1px !important;
			}

			.mapboxgl-ctrl-group > button:first-child {
				margin-top: 1px !important;
				margin-left: 1px !important;
			}

			/* 控件组间距和布局 */
			.mapboxgl-ctrl-top-right .mapboxgl-ctrl {
				margin: 0 8px 4px 0 !important;
			}

			.mapboxgl-ctrl-top-right .mapboxgl-ctrl:first-child {
				margin-top: 50px !important; /* 为顶部的重置按钮预留空间 */
			}

			.mapboxgl-ctrl-bottom-left .mapboxgl-ctrl {
				margin: 0 0 4px 4px !important;
			}

			/* 确保所有控件组都是垂直布局 */
			.mapboxgl-ctrl-top-right .mapboxgl-ctrl-group,
			.mapboxgl-ctrl-top-left .mapboxgl-ctrl-group,
			.mapboxgl-ctrl-bottom-right .mapboxgl-ctrl-group,
			.mapboxgl-ctrl-bottom-left .mapboxgl-ctrl-group {
				display: flex !important;
				flex-direction: column !important;
			}

			/* 修复可能的 float 样式冲突 */
			.mapboxgl-ctrl-group button {
				float: none !important;
				clear: none !important;
			}

			/* 焦点状态 */
			.mapboxgl-ctrl-group button:focus,
			.mapboxgl-ctrl button:focus {
				outline: 2px solid rgb(147, 197, 253) !important;
				outline-offset: 2px !important;
			}

			/* 强制覆盖Mapbox默认样式 */
			.mapboxgl-ctrl-group:not(.mapboxgl-ctrl-vertical) {
				display: flex !important;
				flex-direction: column !important;
			}

			/* 确保按钮容器没有默认的边框和分隔线 */
			.mapboxgl-ctrl-group button:first-child {
				border-top-left-radius: 6px !important;
				border-top-right-radius: 6px !important;
				border-bottom-left-radius: 6px !important;
				border-bottom-right-radius: 6px !important;
			}

			.mapboxgl-ctrl-group button:last-child {
				border-top-left-radius: 6px !important;
				border-top-right-radius: 6px !important;
				border-bottom-left-radius: 6px !important;
				border-bottom-right-radius: 6px !important;
			}

			.mapboxgl-ctrl-group button:not(:first-child):not(:last-child) {
				border-radius: 6px !important;
			}

			/* 隐藏Mapbox logo和版权信息 */
			.mapboxgl-ctrl-logo {
				display: none !important;
			}

			.mapboxgl-ctrl-attrib {
				display: none !important;
			}

			.mapboxgl-ctrl-attrib-button {
				display: none !important;
			}

			/* 响应式调整 */
			@media (max-width: 768px) {
				.mapboxgl-ctrl-group {
					border-radius: 8px !important;
					padding: 2px !important;
					margin: 4px !important;
					display: flex !important;
					flex-direction: column !important;
				}

				.mapboxgl-ctrl-top-right .mapboxgl-ctrl {
					margin: 0 6px 3px 0 !important;
				}

				.mapboxgl-ctrl-top-right .mapboxgl-ctrl:first-child {
					margin-top: 45px !important; /* 为顶部的重置按钮预留空间 */
				}
				
				.mapboxgl-ctrl-group button,
				.mapboxgl-ctrl button {
					width: 24px !important;
					height: 24px !important;
					border-radius: 5px !important;
					font-size: 11px !important;
					flex-shrink: 0 !important;
				}
				
				.mapboxgl-ctrl-scale {
					border-radius: 5px !important;
					font-size: 10px !important;
					padding: 2px 4px !important;
					margin: 4px !important;
				}

				.mapboxgl-ctrl-zoom-in .mapboxgl-ctrl-icon,
				.mapboxgl-ctrl-zoom-out .mapboxgl-ctrl-icon,
				.mapboxgl-ctrl-compass .mapboxgl-ctrl-icon,
				.mapboxgl-ctrl-fullscreen .mapboxgl-ctrl-icon,
				.mapboxgl-ctrl-geolocate .mapboxgl-ctrl-icon {
					background-size: 16px 16px !important;
					width: 16px !important;
					height: 16px !important;
				}

				.mapboxgl-ctrl-group > button:not(:first-child) {
					margin-top: 0.5px !important;
					margin-left: 2px !important;
				}

				.mapboxgl-ctrl-group > button:first-child {
					margin-top: 0.5px !important;
					margin-left: 2px !important;
				}
			}
		`;

		// 添加到文档头部
		document.head.appendChild(styleSheet);

		// 清理函数
		return () => {
			if (document.head.contains(styleSheet)) {
				document.head.removeChild(styleSheet);
			}
		};
	}, []);
}
