import { PrismaClient } from "@prisma/client";
import type { TravelPointImage } from "../src/types/travel-diary";

const prisma = new PrismaClient();

interface OldTravelPoint {
	id: string;
	location: string;
	description: string;
	date: string;
	images: string[]; // 旧格式：字符串数组
	iconType: string;
	latitude: number;
	longitude: number;
	order: number;
	country?: string;
	city?: string;
}

interface NewTravelPoint {
	id: string;
	location: string;
	description: string;
	date: string;
	images: TravelPointImage[]; // 新格式：图片对象数组
	iconType: string;
	latitude: number;
	longitude: number;
	order: number;
	country?: string;
	city?: string;
}

interface Timeline {
	id: string;
	title: string;
	date: string;
	points: OldTravelPoint[] | NewTravelPoint[];
}

interface DiaryContent {
	timelines: Timeline[];
}

/**
 * 将旧格式的图片字符串转换为新格式的图片对象
 */
function convertImageStringToObject(imageUrl: string): TravelPointImage {
	return {
		url: imageUrl,
		description: "",
		alt: "",
		caption: "",
		uploadedAt: new Date().toISOString(),
	};
}

/**
 * 检查图片是否已经是新格式（对象格式）
 */
function isNewImageFormat(image: any): image is TravelPointImage {
	return (
		typeof image === "object" &&
		image !== null &&
		typeof image.url === "string" &&
		"description" in image
	);
}

/**
 * 转换单个旅行点位的图片格式
 */
function convertTravelPointImages(point: any): NewTravelPoint {
	if (!point.images || !Array.isArray(point.images)) {
		return {
			...point,
			images: [],
		};
	}

	const convertedImages: TravelPointImage[] = point.images
		.map((image: any) => {
			// 如果已经是新格式，直接返回
			if (isNewImageFormat(image)) {
				return image;
			}

			// 如果是字符串格式，转换为对象格式
			if (typeof image === "string") {
				return convertImageStringToObject(image);
			}

			// 如果是其他格式，尝试提取 URL
			if (typeof image === "object" && image !== null && image.url) {
				return {
					url: image.url,
					description: image.description || "",
					alt: image.alt || "",
					caption: image.caption || "",
					uploadedAt: image.uploadedAt || new Date().toISOString(),
				};
			}

			// 无法识别的格式，跳过
			console.warn("无法识别的图片格式:", image);
			return null;
		})
		.filter(
			(img: TravelPointImage | null): img is TravelPointImage =>
				img !== null,
		);

	return {
		...point,
		images: convertedImages,
	};
}

/**
 * 转换整个日记内容的图片格式
 */
function convertDiaryContent(content: any): DiaryContent {
	if (!content || !Array.isArray(content.timelines)) {
		console.warn("无效的日记内容格式:", content);
		return { timelines: [] };
	}

	const convertedTimelines = content.timelines.map((timeline: Timeline) => {
		if (!Array.isArray(timeline.points)) {
			return timeline;
		}

		const convertedPoints = timeline.points.map((point: any) =>
			convertTravelPointImages(point),
		);

		return {
			...timeline,
			points: convertedPoints,
		};
	});

	return {
		timelines: convertedTimelines,
	};
}

/**
 * 主迁移函数
 */
async function migrateImageFormats() {
	console.log("开始迁移图片格式...");

	try {
		// 获取所有旅行日记
		const diaries = await prisma.travelDiary.findMany({
			where: {
				status: 0, // 只处理未删除的日记
			},
		});

		console.log(`找到 ${diaries.length} 个日记需要检查`);

		let updatedCount = 0;
		let skippedCount = 0;

		for (const diary of diaries) {
			try {
				console.log(`处理日记: ${diary.title} (ID: ${diary.id})`);

				// 检查内容是否需要转换
				const content = diary.content as any;
				if (!content || !Array.isArray(content.timelines)) {
					console.log(`跳过日记 ${diary.id}: 无效的内容格式`);
					skippedCount++;
					continue;
				}

				// 检查是否有需要转换的图片
				let needsConversion = false;
				for (const timeline of content.timelines) {
					if (Array.isArray(timeline.points)) {
						for (const point of timeline.points) {
							if (Array.isArray(point.images)) {
								for (const image of point.images) {
									if (typeof image === "string") {
										needsConversion = true;
										break;
									}
								}
								if (needsConversion) break;
							}
						}
						if (needsConversion) break;
					}
				}

				if (!needsConversion) {
					console.log(`跳过日记 ${diary.id}: 已经是新格式`);
					skippedCount++;
					continue;
				}

				// 转换内容
				const convertedContent = convertDiaryContent(content);

				// 更新数据库
				await prisma.travelDiary.update({
					where: { id: diary.id },
					data: {
						content: convertedContent as any,
						updatedAt: new Date(),
					},
				});

				console.log(`✅ 成功更新日记 ${diary.id}`);
				updatedCount++;
			} catch (error) {
				console.error(`❌ 处理日记 ${diary.id} 时出错:`, error);
			}
		}

		console.log("\n迁移完成!");
		console.log(`总计: ${diaries.length} 个日记`);
		console.log(`更新: ${updatedCount} 个日记`);
		console.log(`跳过: ${skippedCount} 个日记`);
	} catch (error) {
		console.error("迁移过程中出错:", error);
		throw error;
	}
}

/**
 * 验证迁移结果
 */
async function validateMigration() {
	console.log("\n开始验证迁移结果...");

	const diaries = await prisma.travelDiary.findMany({
		where: {
			status: 0,
		},
	});

	let validCount = 0;
	let invalidCount = 0;

	for (const diary of diaries) {
		const content = diary.content as any;
		if (!content || !Array.isArray(content.timelines)) {
			continue;
		}

		let isValid = true;
		for (const timeline of content.timelines) {
			if (Array.isArray(timeline.points)) {
				for (const point of timeline.points) {
					if (Array.isArray(point.images)) {
						for (const image of point.images) {
							if (typeof image === "string") {
								console.log(
									`❌ 发现未转换的字符串图片: ${diary.id}`,
								);
								isValid = false;
								break;
							}
							if (!isNewImageFormat(image)) {
								console.log(
									`❌ 发现无效的图片格式: ${diary.id}`,
									image,
								);
								isValid = false;
								break;
							}
						}
						if (!isValid) break;
					}
				}
				if (!isValid) break;
			}
		}

		if (isValid) {
			validCount++;
		} else {
			invalidCount++;
		}
	}

	console.log("验证结果:");
	console.log(`✅ 有效: ${validCount} 个日记`);
	console.log(`❌ 无效: ${invalidCount} 个日记`);

	return invalidCount === 0;
}

/**
 * 主函数
 */
async function main() {
	try {
		console.log("=== 旅行日记图片格式迁移工具 ===\n");

		// 执行迁移
		await migrateImageFormats();

		// 验证结果
		const isValid = await validateMigration();

		if (isValid) {
			console.log("\n🎉 迁移成功完成，所有数据都已转换为新格式！");
		} else {
			console.log("\n⚠️ 迁移完成，但发现一些数据可能需要手动检查");
		}
	} catch (error) {
		console.error("迁移失败:", error);
		process.exit(1);
	} finally {
		await prisma.$disconnect();
	}
}

// 如果直接运行此脚本
if (require.main === module) {
	main();
}

export { migrateImageFormats, validateMigration };
