import {
	createGeminiClient,
	createOpenAIClient,
	createVolcengineClient,
} from "@packages/ai";

/**
 * 使用火山引擎进行图片分析的示例
 * @param imageUrl 要分析的图片URL
 * @param question 关于图片的问题
 */
export async function analyzeImageWithVolcengine(
	imageUrl: string,
	question: string,
) {
	try {
		// 使用环境变量中的API密钥创建火山引擎客户端
		const client = createVolcengineClient(
			process.env.VOLCENGINE_API_KEY || "",
			process.env.VOLCENGINE_SECRET_KEY || "",
		);

		// 调用图片分析方法
		const result = await client.analyzeImage(imageUrl, question, {
			// 可以指定特定模型
			model: "ep-20250317191902-5fh22",
			// 可以自定义系统提示词
			systemPrompt:
				"你是一位精通图像分析的AI助手，请基于图像内容提供详细而准确的分析。",
			// 其他参数
			temperature: 0.5,
			maxTokens: 1500,
		});

		return result;
	} catch (error) {
		console.error("图片分析出错:", error);
		throw error;
	}
}

/**
 * 使用OpenAI进行图片分析的示例
 * @param imageUrl 要分析的图片URL
 * @param question 关于图片的问题
 */
export async function analyzeImageWithOpenAI(
	imageUrl: string,
	question: string,
) {
	try {
		const client = createOpenAIClient(
			"gpt-4o", // 使用支持图像分析的模型
			process.env.OPENAI_API_KEY,
		);

		const result = await client.analyzeImage(imageUrl, question, {
			visionModel: "gpt-4-vision-preview", // 指定视觉模型
			systemPrompt: "作为图片分析专家，请分析图片并回答问题。",
		});

		return result;
	} catch (error) {
		console.error("图片分析出错:", error);
		throw error;
	}
}

/**
 * 使用Google Gemini进行图片分析的示例
 * @param imageUrl 要分析的图片URL
 * @param question 关于图片的问题
 */
export async function analyzeImageWithGemini(
	imageUrl: string,
	question: string,
) {
	try {
		const client = createGeminiClient(process.env.GEMINI_API_KEY || "");

		const result = await client.analyzeImage(imageUrl, question, {
			visionModel: "gemini-pro-vision", // 指定视觉模型
			systemPrompt: "你是一位专业的图像分析专家，请对图像进行详细分析。",
		});

		return result;
	} catch (error) {
		console.error("图片分析出错:", error);
		throw error;
	}
}

/**
 * 使用多个AI提供商进行图片分析并比较结果的示例
 * @param imageUrl 要分析的图片URL
 * @param question 关于图片的问题
 */
export async function compareImageAnalysis(imageUrl: string, question: string) {
	try {
		// 并行调用三个不同的AI提供商进行分析
		const [volcResult, openaiResult, geminiResult] = await Promise.all([
			analyzeImageWithVolcengine(imageUrl, question).catch(
				(e) => `火山引擎分析错误: ${e.message}`,
			),
			analyzeImageWithOpenAI(imageUrl, question).catch(
				(e) => `OpenAI分析错误: ${e.message}`,
			),
			analyzeImageWithGemini(imageUrl, question).catch(
				(e) => `Gemini分析错误: ${e.message}`,
			),
		]);

		// 返回比较结果
		return {
			volcengine: volcResult,
			openai: openaiResult,
			gemini: geminiResult,
		};
	} catch (error) {
		console.error("图片分析比较出错:", error);
		throw error;
	}
}
