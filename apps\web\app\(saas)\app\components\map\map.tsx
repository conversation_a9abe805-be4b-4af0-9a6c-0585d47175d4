"use client";

import { cn } from "@ui/lib";
import React, { useCallback, useEffect, useRef, useState } from "react"; // 引入 React
import mapProviderFactory, {
	type MapProviderType,
} from "./providers/map-provider-factory";
import {
	ReactGoogleMap,
	ReactGoogleMapProvider,
	// 导入 GoogleMapProvider 以便设置 mapRef
	GoogleMapProvider as ReactGoogleMapProviderClass,
} from "./providers/react-google-map-provider";
import type { MapProps, MapService } from "./types"; // 引入 MarkerOptions

/**
 * 通用地图组件
 *
 * 提供了一个统一的地图界面，可以使用不同的地图服务提供商实现
 */
export function TravelMapComponent({
	center = { lat: 39.9042, lng: 116.4074 }, // 默认北京中心坐标
	zoom = 12,
	minZoom,
	maxZoom,
	// markers = [], // 不再直接处理 markers
	onMapClick,
	onMapIdle,
	onBoundsChanged,
	onMarkerClick, // 传递给 ReactGoogleMap，但实际触发在 MarkerClusterer
	onMapInit, // 非常重要，用于传递 mapService 实例
	height = "400px",
	width = "100%",
	className,
	providerOptions,
}: MapProps) {
	// 地图容器引用 (用于非 React 地图)
	const mapContainerRef = useRef<HTMLDivElement>(null);
	// Google 地图实例引用 (用于 ReactGoogleMap)
	const googleMapRef = useRef<google.maps.Map | null>(null);

	// 地图服务实例状态
	const [mapService, setMapService] = useState<MapService | null>(null);

	// 当前使用的地图提供商类型
	const [providerType, setProviderType] = useState<MapProviderType>(
		mapProviderFactory.getCurrentProviderType(),
	);

	// 地图容器样式
	const mapStyle = {
		height: typeof height === "number" ? `${height}px` : height,
		width: typeof width === "number" ? `${width}px` : width,
	};

	// 调试日志
	useEffect(() => {
		if (process.env.NODE_ENV === "development") {
			console.log("[TravelMapComponent] 渲染，容器样式:", mapStyle);
		}
	}, [mapStyle]);

	// 处理Google地图加载 (由 ReactGoogleMap 的 onLoad 触发)
	const handleGoogleMapLoad = useCallback(
		(map: google.maps.Map) => {
			console.log("[TravelMapComponent] Google 地图已加载");
			googleMapRef.current = map;

			// 更新 GoogleMapProvider 单例中的 mapRef
			const provider = ReactGoogleMapProviderClass.getInstance();
			provider.setMapRef(googleMapRef);

			// 创建地图服务实例
			const service = provider.createMapService();
			setMapService(service);
			console.log("[TravelMapComponent] MapService 已创建");

			// 调用外部初始化回调，传递 service 实例
			if (onMapInit) {
				console.log("[TravelMapComponent] 调用 onMapInit 回调");
				onMapInit(service);
			}
		},
		[onMapInit],
	); // 依赖 onMapInit

	// 初始化非React地图服务 (逻辑保持不变)
	useEffect(() => {
		if (providerType === "google") return;
		if (!mapContainerRef.current) return;

		const container = mapContainerRef.current;
		const provider = mapProviderFactory.getCurrentProvider();
		const service = provider.createMapService();

		const initMap = async () => {
			try {
				await service.init(container, {
					center,
					zoom,
					minZoom,
					maxZoom,
				});
				setMapService(service);
				if (onMapInit) {
					onMapInit(service);
				}
			} catch (error) {
				console.error("初始化非React地图失败:", error);
			}
		};
		initMap();

		return () => {
			if (service) {
				service.destroy();
				setMapService(null);
			}
		};
	}, [providerType, center, zoom, minZoom, maxZoom, onMapInit]); // 添加依赖

	// 移除内部标记管理逻辑，因为标记由外部 (TravelMap.tsx) 通过 MarkerClusterer 管理

	// 移除内部事件处理逻辑，因为事件现在由 ReactGoogleMap 或外部 (TravelMap.tsx) 处理

	// 渲染地图
	const renderMap = () => {
		switch (providerType) {
			case "google":
				return (
					<ReactGoogleMapProvider>
						<ReactGoogleMap
							center={center} // 初始中心点
							zoom={zoom} // 初始缩放
							// markers 不再传递
							onMapClick={onMapClick} // 传递地图点击事件
							onMapIdle={onMapIdle} // 传递地图空闲事件
							onBoundsChanged={onBoundsChanged} // 传递边界变化事件
							// onMarkerClick 不再直接传递，由 MarkerClusterer 处理
							mapContainerStyle={{
								// 确保覆盖整个容器
								position: "absolute",
								top: 0,
								left: 0,
								right: 0,
								bottom: 0,
							}}
							onLoad={handleGoogleMapLoad} // 关键：获取地图实例
							options={{
								minZoom,
								maxZoom,
								...(providerOptions as google.maps.MapOptions), // 传递特定选项
							}}
						/>
					</ReactGoogleMapProvider>
				);
			default:
				// 非 React 地图提供商的渲染 (保持不变)
				return (
					<div
						ref={mapContainerRef}
						style={{
							position: "absolute",
							top: 0,
							left: 0,
							right: 0,
							bottom: 0,
						}}
						className="map-container"
					/>
				);
		}
	};

	return (
		<div
			className={cn("relative overflow-hidden rounded-md", className)}
			style={mapStyle} // 应用外部容器样式
			data-map-provider={providerType}
			// ref={mapContainerRef} // 这个 ref 只用于非 React 地图
		>
			{renderMap()}

			{/* 开发环境下的调试信息 */}
			{process.env.NODE_ENV === "development" && (
				<div className="absolute bottom-0 left-0 bg-black/50 text-white text-xs p-1 z-10">
					Provider: {providerType} | H: {mapStyle.height} | W:{" "}
					{mapStyle.width} | Service:{" "}
					{mapService ? "Ready" : "Not Ready"}
				</div>
			)}
		</div>
	);
}

// 为了方便使用，导出地图工厂和相关类型
export { mapProviderFactory };
export type { MapProviderType };

// 导出React Google Maps组件，方便直接使用
export {
	ReactGoogleMap,
	ReactGoogleMapProvider,
} from "./providers/react-google-map-provider";

// 默认导出重命名后的地图组件
export default TravelMapComponent;
