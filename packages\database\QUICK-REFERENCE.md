# 🚀 数据库操作快速参考

## 📋 常用命令速查

### 主数据库（旅行日记）
```bash
# 生成类型
pnpm --filter database generate

# 创建迁移
pnpm --filter database migrate --name "your_migration_name"

# 推送到开发环境
pnpm --filter database push

# 数据库管理界面
pnpm --filter database studio
```

### AI图片数据库
```bash
# 生成类型
pnpm --filter database tsx scripts/migrate-ai-images.ts generate

# 创建迁移
pnpm --filter database tsx scripts/migrate-ai-images.ts migrate your_migration_name

# 推送到开发环境
pnpm --filter database tsx scripts/migrate-ai-images.ts push

# 数据库管理界面
pnpm --filter database tsx scripts/migrate-ai-images.ts studio
```

### 批量操作
```bash
# 生成所有数据库类型
pnpm --filter database generate:all

# 查看所有数据库
pnpm --filter database studio              # 主数据库
pnpm --filter database studio:ai-images    # AI图片数据库
```

## 🔄 典型工作流程

### 1. 添加新字段
```bash
# 1. 编辑 schema 文件
# 2. 创建迁移
pnpm --filter database migrate --name "add_new_field"
# 3. 生成类型
pnpm --filter database generate
```

### 2. 修改现有字段
```bash
# ⚠️ 危险操作，建议分步进行
# 1. 添加新字段
# 2. 迁移数据
# 3. 删除旧字段
```

### 3. 生产部署
```bash
# 主数据库
pnpm --filter database migrate deploy

# AI图片数据库
pnpm --filter database tsx scripts/migrate-ai-images.ts deploy
```

## 📁 文件位置

```
packages/database/
├── prisma/
│   ├── schema.prisma              # 主数据库
│   └── schema-ai-images.prisma    # AI图片数据库
├── scripts/
│   └── migrate-ai-images.ts       # AI数据库迁移工具
└── src/
    ├── clients/
    │   ├── travel-memo.ts         # 主数据库客户端
    │   └── ai-images.ts           # AI数据库客户端
    └── generated/                 # 生成的类型文件
```

## 🔧 环境变量

```bash
# .env.local
DATABASE_URL="postgresql://localhost:5432/travel_memo"
AI_IMAGES_DATABASE_URL="postgresql://localhost:5432/ai_images"
```

## 💡 最佳实践

1. **开发环境**：使用 `push` 快速原型
2. **生产环境**：使用 `migrate` 确保版本控制
3. **字段重命名**：分步骤进行，避免数据丢失
4. **索引优化**：为查询字段添加索引
5. **备份优先**：生产环境操作前先备份

## ⚠️ 注意事项

- 跨数据库操作无事务支持
- 用户数据需要手动同步
- 生产环境迁移前务必备份
- 字段类型变更可能导致数据丢失 