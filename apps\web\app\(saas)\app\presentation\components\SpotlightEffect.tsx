"use client";

interface SpotlightEffectProps {
	position: { x: number; y: number } | null;
	isVisible: boolean;
}

export function SpotlightEffect({ position, isVisible }: SpotlightEffectProps) {
	// 如果没有位置或者不可见，则不显示聚光灯效果
	if (!position || !isVisible) return null;

	return (
		<div
			className="absolute inset-0 z-10 pointer-events-none transition-opacity duration-500"
			style={{
				background: `radial-gradient(circle 150px at ${position.x}px ${position.y}px, transparent 30%, rgba(0, 0, 0, 0.6) 70%)`,
				opacity: isVisible ? 1 : 0,
			}}
		/>
	);
}
