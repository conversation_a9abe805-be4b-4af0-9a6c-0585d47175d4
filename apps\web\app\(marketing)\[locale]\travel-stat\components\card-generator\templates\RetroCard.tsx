"use client";

import { Building2, Calendar, Camera, Flag, MapPin } from "lucide-react";
import React from "react";
import type {
	CardCustomization,
	CardTemplateProps,
	CountryData,
	TravelPoint,
} from "../types/cardTypes";

interface RetroCardProps extends CardTemplateProps {
	mapImageData?: {
		dataURL: string;
		dimensions: { width: number; height: number };
	};
	mapComponent?: React.ReactNode;
	travelPoints: TravelPoint[];
	visitedCountries: CountryData[];
	customization: CardCustomization;
	platform?: string;
	isPreview?: boolean;
}

export function RetroCard({
	mapImageData,
	mapComponent,
	travelPoints,
	visitedCountries,
	customization,
	platform = "instagram",
	isPreview = false,
}: RetroCardProps) {
	// 默认值
	const colors = customization.colors || {
		primary: "#d35400",
		secondary: "#e67e22",
		accent: "#f39c12",
		background: "#f4e4c1",
		text: "#8b4513",
	};

	const typography = customization.typography || {
		fontFamily: "'Courier New', monospace",
		headerSize: 36,
		bodySize: 20,
		titleWeight: 700,
	};

	const layout = customization.layout || {
		padding: 32,
		spacing: 20,
		borderRadius: 8,
		showShadow: true,
	};

	const content = customization.content || {
		showUserInfo: true,
		showDetailedStats: true,
		customTitle: "VINTAGE TRAVELS",
		customFooter: "Shot on 35mm Film · Memories Developed",
	};

	// 计算统计数据
	const stats = {
		totalPoints: travelPoints.length,
		totalCountries: visitedCountries.length,
		totalCities: new Set(travelPoints.map((p) => p.city)).size,
		earliestDate:
			travelPoints.length > 0
				? travelPoints.reduce((earliest, point) =>
						new Date(point.date) < new Date(earliest.date)
							? point
							: earliest,
					).date
				: null,
		latestDate:
			travelPoints.length > 0
				? travelPoints.reduce((latest, point) =>
						new Date(point.date) > new Date(latest.date)
							? point
							: latest,
					).date
				: null,
	};

	// 平台尺寸配置
	const platformDimensions = {
		instagram: { width: 1080, height: 1080, aspectRatio: 1 },
		wechat: { width: 1200, height: 900, aspectRatio: 4 / 3 },
		weibo: { width: 1080, height: 1350, aspectRatio: 4 / 5 },
		twitter: { width: 1200, height: 675, aspectRatio: 16 / 9 },
		facebook: { width: 1200, height: 630, aspectRatio: 1.91 },
	};

	const dimensions =
		platformDimensions[platform as keyof typeof platformDimensions] ||
		platformDimensions.instagram;

	// 根据宽高比决定布局策略
	const isWideFormat = dimensions.aspectRatio > 1.5; // Twitter, Facebook
	const isTallFormat = dimensions.aspectRatio < 0.9; // Weibo
	const isSquareFormat =
		dimensions.aspectRatio >= 0.9 && dimensions.aspectRatio <= 1.3; // Instagram, WeChat

	const scale = Math.min(dimensions.width / 1080, 1.5);

	const adjustedTypography = {
		titleSize: Math.round(isWideFormat ? 42 * scale : 52 * scale),
		statsSize: Math.round(isWideFormat ? 56 * scale : 72 * scale),
		headerSize: Math.round((typography.headerSize || 36) * scale),
		bodySize: Math.round((typography.bodySize || 18) * scale),
		smallSize: Math.round(14 * scale),
		titleWeight: typography.titleWeight || 700,
		fontFamily: "'Bebas Neue', sans-serif",
	};

	const getLayoutValue = (
		key: "padding" | "spacing" | "borderRadius",
		defaultValue: number,
	): number => {
		const value = layout[key];
		return typeof value === "number" ? value : defaultValue;
	};

	const getDateRange = () => {
		// 优先使用自定义日期文本
		if (content.customDate?.trim()) {
			return content.customDate;
		}
		// 回退到计算的日期范围
		if (!stats.earliestDate || !stats.latestDate) return "";
		const start = new Date(stats.earliestDate).getFullYear();
		const end = new Date(stats.latestDate).getFullYear();
		return start === end ? `${start}` : `${start} - ${end}`;
	};

	return (
		<div
			data-card-element
			className="relative overflow-hidden"
			style={{
				width: dimensions.width,
				height: dimensions.height,
				fontFamily: adjustedTypography.fontFamily,
				background:
					"linear-gradient(135deg, #f4e4c1 0%, #e8d5b7 25%, #d4c5a9 50%, #c9b99b 75%, #bea68c 100%)",
				padding: getLayoutValue("padding", 32),
				position: "relative",
				border: "12px solid #8b4513",
				boxSizing: "border-box",
				display: "flex",
				flexDirection: isWideFormat ? "row" : "column",
			}}
		>
			{/* 复古胶片边框纹理 */}
			<div className="absolute inset-0">
				{/* 胶片孔效果 */}
				<div
					style={{
						position: "absolute",
						top: 0,
						left: 0,
						right: 0,
						height: "20px",
						background:
							"repeating-linear-gradient(90deg, #8b4513 0px, #8b4513 15px, transparent 15px, transparent 25px)",
					}}
				/>
				<div
					style={{
						position: "absolute",
						bottom: 0,
						left: 0,
						right: 0,
						height: "20px",
						background:
							"repeating-linear-gradient(90deg, #8b4513 0px, #8b4513 15px, transparent 15px, transparent 25px)",
					}}
				/>

				{/* 老旧纸张纹理 */}
				<div
					style={{
						position: "absolute",
						inset: "20px",
						background:
							"radial-gradient(circle at 30% 20%, rgba(139, 69, 19, 0.1) 0%, transparent 50%), radial-gradient(circle at 80% 80%, rgba(139, 69, 19, 0.1) 0%, transparent 50%)",
						pointerEvents: "none",
					}}
				/>
			</div>

			{/* 宽屏格式左侧内容 */}
			<div
				style={{
					display: "flex",
					flexDirection: "column",
					flex: isWideFormat ? "0 0 40%" : "0 0 auto",
					marginRight: isWideFormat
						? getLayoutValue("spacing", 20)
						: 0,
					marginBottom: isWideFormat
						? 0
						: getLayoutValue("spacing", 20),
				}}
			>
				{/* Header - 打字机风格 */}
				{content.showUserInfo && (
					<div
						className="relative"
						style={{
							marginBottom: getLayoutValue("spacing", 20),
							textAlign: "center",
							padding: `${isWideFormat ? getLayoutValue("spacing", 20) / 1.5 : getLayoutValue("spacing", 20)}px`,
							background: "rgba(139, 69, 19, 0.1)",
							border: "2px dashed #8b4513",
							transform: "rotate(-0.5deg)",
						}}
					>
						<div style={{ transform: "rotate(0.5deg)" }}>
							{/* 胶片相机图标 */}
							<div
								style={{
									display: "flex",
									justifyContent: "center",
									marginBottom: isWideFormat
										? getLayoutValue("spacing", 20) / 2
										: getLayoutValue("spacing", 20),
								}}
							>
								<Camera
									size={
										adjustedTypography.bodySize +
										(isWideFormat ? 0 : 8)
									}
									style={{
										color: "#8b4513",
										filter: "drop-shadow(2px 2px 4px rgba(139, 69, 19, 0.3))",
									}}
								/>
							</div>

							<h1
								style={{
									fontSize: isWideFormat
										? adjustedTypography.titleSize * 0.8
										: adjustedTypography.titleSize,
									fontWeight: 700,
									color: "#8b4513",
									margin: 0,
									lineHeight: 1.1,
									textAlign: "center",
									letterSpacing: "3px",
									textTransform: "uppercase",
									marginBottom: isWideFormat
										? getLayoutValue("spacing", 20) / 2
										: getLayoutValue("spacing", 20),
									textShadow:
										"2px 2px 0px rgba(244, 228, 193, 0.8)",
								}}
							>
								{content.customTitle || "VINTAGE TRAVELS"}
							</h1>

							{getDateRange() && (
								<div
									style={{
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										gap: isWideFormat ? "10px" : "15px",
										color: "#8b4513",
										fontSize: isWideFormat
											? adjustedTypography.bodySize - 4
											: adjustedTypography.bodySize - 2,
										fontWeight: 600,
										letterSpacing: "2px",
										textTransform: "uppercase",
									}}
								>
									<div
										style={{
											width: isWideFormat
												? "30px"
												: "40px",
											height: "2px",
											background: "#8b4513",
										}}
									/>
									<Calendar
										size={
											isWideFormat
												? adjustedTypography.bodySize -
													4
												: adjustedTypography.bodySize -
													2
										}
									/>
									<span>{getDateRange()}</span>
									<div
										style={{
											width: isWideFormat
												? "30px"
												: "40px",
											height: "2px",
											background: "#8b4513",
										}}
									/>
								</div>
							)}
						</div>
					</div>
				)}

				{/* Stats - 拍立得风格布局 */}
				{content.showDetailedStats && (
					<div
						style={{
							display: "flex",
							flexDirection: isWideFormat ? "column" : "row",
							justifyContent: "space-between",
							gap:
								getLayoutValue("spacing", 20) /
								(isWideFormat ? 2 : 1),
							marginBottom: isWideFormat
								? getLayoutValue("spacing", 20)
								: getLayoutValue("spacing", 20) * 2,
							transform: isWideFormat
								? "perspective(800px) rotateX(-2deg)"
								: "perspective(800px) rotateX(-5deg)",
						}}
					>
						{/* 足迹点 - 拍立得卡片 */}
						<div
							style={{
								flex: 1,
								background: "#ffffff",
								padding: isWideFormat
									? "15px 10px 20px 10px"
									: "20px 15px 30px 15px",
								boxShadow:
									"0 8px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.8)",
								transform: "rotate(-2deg)",
								border: "1px solid #ddd",
							}}
						>
							<div
								style={{
									transform: "rotate(2deg)",
									textAlign: "center",
								}}
							>
								<div
									style={{
										fontSize: isWideFormat
											? adjustedTypography.statsSize * 0.8
											: adjustedTypography.statsSize,
										fontWeight: 900,
										color: "#d35400",
										lineHeight: 1,
										marginBottom: isWideFormat
											? "5px"
											: "8px",
										fontFamily:
											"'Impact', 'Arial Black', sans-serif",
									}}
								>
									{stats.totalPoints}
								</div>
								<div
									style={{
										fontSize: isWideFormat
											? adjustedTypography.bodySize - 8
											: adjustedTypography.bodySize - 6,
										color: "#8b4513",
										fontWeight: 600,
										letterSpacing: "1px",
										textTransform: "uppercase",
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										gap: "5px",
									}}
								>
									<MapPin
										size={
											isWideFormat
												? adjustedTypography.bodySize -
													8
												: adjustedTypography.bodySize -
													6
										}
									/>
									Points
								</div>
							</div>
						</div>

						{/* 城市 - 拍立得卡片 */}
						<div
							style={{
								flex: 1,
								background: "#ffffff",
								padding: isWideFormat
									? "15px 10px 20px 10px"
									: "20px 15px 30px 15px",
								boxShadow:
									"0 8px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.8)",
								transform: isWideFormat
									? "rotate(1deg) scale(1.02)"
									: "rotate(1deg) scale(1.05)",
								border: "1px solid #ddd",
								zIndex: 2,
								marginTop: 24,
							}}
						>
							<div
								style={{
									transform: "rotate(-1deg)",
									textAlign: "center",
								}}
							>
								<div
									style={{
										fontSize: isWideFormat
											? (adjustedTypography.statsSize +
													4) *
												0.8
											: adjustedTypography.statsSize + 8,
										fontWeight: 900,
										color: "#e67e22",
										lineHeight: 1,
										marginBottom: isWideFormat
											? "5px"
											: "8px",
										fontFamily:
											"'Impact', 'Arial Black', sans-serif",
									}}
								>
									{stats.totalCities}
								</div>
								<div
									style={{
										fontSize: isWideFormat
											? adjustedTypography.bodySize - 8
											: adjustedTypography.bodySize - 6,
										color: "#8b4513",
										fontWeight: 600,
										letterSpacing: "1px",
										textTransform: "uppercase",
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										gap: "5px",
									}}
								>
									<Building2
										size={
											isWideFormat
												? adjustedTypography.bodySize -
													8
												: adjustedTypography.bodySize -
													6
										}
									/>
									Cities
								</div>
							</div>
						</div>

						{/* 国家 - 拍立得卡片 */}
						<div
							style={{
								flex: 1,
								background: "#ffffff",
								padding: isWideFormat
									? "15px 10px 20px 10px"
									: "20px 15px 30px 15px",
								boxShadow:
									"0 8px 16px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.8)",
								transform: "rotate(-1deg)",
								border: "1px solid #ddd",
								marginTop: 24,
							}}
						>
							<div
								style={{
									transform: "rotate(1deg)",
									textAlign: "center",
								}}
							>
								<div
									style={{
										fontSize: isWideFormat
											? adjustedTypography.statsSize * 0.8
											: adjustedTypography.statsSize,
										fontWeight: 900,
										color: "#f39c12",
										lineHeight: 1,
										marginBottom: isWideFormat
											? "5px"
											: "8px",
										fontFamily:
											"'Impact', 'Arial Black', sans-serif",
									}}
								>
									{stats.totalCountries}
								</div>
								<div
									style={{
										fontSize: isWideFormat
											? adjustedTypography.bodySize - 8
											: adjustedTypography.bodySize - 6,
										color: "#8b4513",
										fontWeight: 600,
										letterSpacing: "1px",
										textTransform: "uppercase",
										display: "flex",
										alignItems: "center",
										justifyContent: "center",
										gap: "5px",
									}}
								>
									<Flag
										size={
											isWideFormat
												? adjustedTypography.bodySize -
													8
												: adjustedTypography.bodySize -
													6
										}
									/>
									Nations
								</div>
							</div>
						</div>
					</div>
				)}
			</div>

			{/* 宽屏格式右侧内容或普通布局下的地图部分 */}
			<div
				style={{
					flex: isWideFormat ? "0 0 60%" : 1,
					display: "flex",
					flexDirection: "column",
					height: isWideFormat ? "auto" : undefined,
				}}
			>
				{/* Map Section - 老照片风格 */}
				<div
					className="relative"
					style={{
						marginBottom: 0,
						background: "#ffffff",
						padding: "20px 20px 20px 20px",
						boxShadow:
							"0 12px 24px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.8)",
						transform: "rotate(0.5deg)",
						border: "1px solid #ddd",
						display: "flex",
						flexDirection: "column",
						flex: 1,
						height: "100%",
					}}
				>
					<div
						style={{
							transform: "rotate(-0.5deg)",
							display: "flex",
							flexDirection: "column",
							flex: 1,
							height: "100%",
						}}
					>
						<div
							style={{
								width: "100%",
								flex: 1,
								overflow: "hidden",
								position: "relative",
								background: "#f0f0f0",
							}}
						>
							{mapComponent ? (
								<div style={{ width: "100%", height: "100%" }}>
									{mapComponent}
								</div>
							) : (
								<img
									src={mapImageData?.dataURL}
									alt="Travel Map"
									style={{
										width: "100%",
										height: "100%",
										objectFit: "cover",
										filter: "sepia(30%) contrast(1.1) brightness(1.1) saturate(0.8)",
									}}
								/>
							)}

							{/* 老照片效果覆盖层 - 不阻挡地图交互 */}
							<div
								className="absolute inset-0"
								style={{
									background:
										"radial-gradient(ellipse at center, transparent 30%, rgba(139, 69, 19, 0.1) 100%)",
									mixBlendMode: "multiply",
									// 确保装饰层不阻挡地图交互
									pointerEvents: "none",
									zIndex: 20, // 在地图之上但不阻挡交互
								}}
							/>
						</div>

						{/* 照片底部手写字体 */}
						<div
							style={{
								marginTop: "15px",
								textAlign: "center",
								color: "#8b4513",
								fontSize: adjustedTypography.bodySize - 2,
								fontFamily: "'Dancing Script', cursive",
								fontWeight: 600,
								fontStyle: "italic",
							}}
						>
							{content.customFooter && (
								<p
									style={{
										fontSize:
											adjustedTypography.bodySize - 4,

										fontWeight: 600,
										fontStyle: "italic",
									}}
								>
									{content.customFooter}
								</p>
							)}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
