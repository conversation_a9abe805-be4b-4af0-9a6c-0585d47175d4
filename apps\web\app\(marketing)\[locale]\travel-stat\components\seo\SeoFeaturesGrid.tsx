import { Search, Sparkles } from "lucide-react";
import type { TravelStatTranslations } from "../../hooks/useTravelStatTranslations";

interface SeoFeaturesGridProps {
	translations: TravelStatTranslations;
}

interface FeatureCardProps {
	icon: React.ReactNode;
	title: string;
	description: string;
	gradient: string;
	hoverGradient: string;
}

function FeatureCard({
	icon,
	title,
	description,
	gradient,
	hoverGradient,
}: FeatureCardProps) {
	return (
		<div className="group relative">
			<div
				className={`absolute inset-0 ${hoverGradient} rounded-2xl blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10`}
			/>
			<div className="relative bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 dark:border-gray-700/50 hover:shadow-xl transition-all duration-300">
				<div
					className={`w-12 h-12 ${gradient} rounded-lg flex items-center justify-center mb-4`}
				>
					{icon}
				</div>
				<h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
					{title}
				</h3>
				<p className="text-gray-600 dark:text-gray-300 leading-relaxed">
					{description}
				</p>
			</div>
		</div>
	);
}

export function SeoFeaturesGrid({ translations }: SeoFeaturesGridProps) {
	const features = [
		{
			icon: <Search className="w-6 h-6 text-white" />,
			title:
				translations.seo?.features?.smartSearch?.title() ||
				"Smart Memory Search",
			description:
				translations.seo?.features?.smartSearch?.description() ||
				"Instantly find and mark any destination worldwide. Map Moment's intelligent search helps you quickly locate cities, landmarks, and special places where your precious memories were made.",
			gradient: "bg-gradient-to-br from-blue-500 to-sky-500",
			hoverGradient:
				"bg-gradient-to-r from-blue-200/30 via-sky-200/30 to-cyan-200/30 dark:from-blue-800/20 dark:via-sky-800/20 dark:to-cyan-800/20",
		},
		{
			icon: (
				<svg
					className="w-6 h-6 text-white"
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
					aria-label="统计图表"
				>
					<title>统计图表</title>
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						strokeWidth={2}
						d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
					/>
				</svg>
			),
			title:
				translations.seo?.features?.detailedStats?.title() ||
				"Memory Analytics",
			description:
				translations.seo?.features?.detailedStats?.description() ||
				"Track your travel journey with beautiful statistics. See how many countries you've explored, cities you've visited, and moments you've captured on your personal Map Moment collection.",
			gradient: "bg-gradient-to-br from-green-500 to-emerald-500",
			hoverGradient:
				"bg-gradient-to-r from-green-200/30 via-emerald-200/30 to-teal-200/30 dark:from-green-800/20 dark:via-emerald-800/20 dark:to-teal-800/20",
		},
		{
			icon: <Sparkles className="w-6 h-6 text-white" />,
			title:
				translations.seo?.features?.socialCards?.title() ||
				"Shareable Memory Cards",
			description:
				translations.seo?.features?.socialCards?.description() ||
				"Transform your travel moments into stunning visual cards. Perfect for Instagram, Twitter, and Facebook - let Map Moment help you share your most cherished travel memories with the world.",
			gradient: "bg-gradient-to-br from-purple-500 to-pink-500",
			hoverGradient:
				"bg-gradient-to-r from-purple-200/30 via-pink-200/30 to-rose-200/30 dark:from-purple-800/20 dark:via-pink-800/20 dark:to-rose-800/20",
		},
		{
			icon: (
				<svg
					className="w-6 h-6 text-white"
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
					aria-label="喜爱地点"
				>
					<title>喜爱地点</title>
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						strokeWidth={2}
						d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
					/>
				</svg>
			),
			title:
				translations.seo?.features?.mapStyles?.title() ||
				"Beautiful Map Themes",
			description:
				translations.seo?.features?.mapStyles?.description() ||
				"Choose from gorgeous map styles that match your memories. From satellite views to artistic themes, Map Moment offers the perfect backdrop for your travel moments.",
			gradient: "bg-gradient-to-br from-orange-500 to-red-500",
			hoverGradient:
				"bg-gradient-to-r from-orange-200/30 via-red-200/30 to-pink-200/30 dark:from-orange-800/20 dark:via-red-800/20 dark:to-pink-800/20",
		},
		{
			icon: (
				<svg
					className="w-6 h-6 text-white"
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
					aria-label="数据导入导出"
				>
					<title>数据导入导出</title>
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						strokeWidth={2}
						d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"
					/>
				</svg>
			),
			title:
				translations.seo?.features?.dataManagement?.title() ||
				"Memory Backup & Sync",
			description:
				translations.seo?.features?.dataManagement?.description() ||
				"Keep your precious travel moments safe forever. Map Moment's backup features ensure your memories are never lost, with easy import and export options.",
			gradient: "bg-gradient-to-br from-cyan-500 to-blue-500",
			hoverGradient:
				"bg-gradient-to-r from-cyan-200/30 via-blue-200/30 to-indigo-200/30 dark:from-cyan-800/20 dark:via-blue-800/20 dark:to-indigo-800/20",
		},
		{
			icon: (
				<svg
					className="w-6 h-6 text-white"
					fill="none"
					stroke="currentColor"
					viewBox="0 0 24 24"
					aria-label="响应式设计"
				>
					<title>响应式设计</title>
					<path
						strokeLinecap="round"
						strokeLinejoin="round"
						strokeWidth={2}
						d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
					/>
				</svg>
			),
			title:
				translations.seo?.features?.responsiveDesign?.title() ||
				"Memories Everywhere",
			description:
				translations.seo?.features?.responsiveDesign?.description() ||
				"Access your Map Moment collection on any device. Whether on desktop, tablet, or mobile, your travel memories are always at your fingertips, beautifully displayed.",
			gradient: "bg-gradient-to-br from-indigo-500 to-purple-500",
			hoverGradient:
				"bg-gradient-to-r from-indigo-200/30 via-purple-200/30 to-violet-200/30 dark:from-indigo-800/20 dark:via-purple-800/20 dark:to-violet-800/20",
		},
	];

	return (
		<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
			{features.map((feature, index) => (
				<FeatureCard
					key={index}
					icon={feature.icon}
					title={feature.title}
					description={feature.description}
					gradient={feature.gradient}
					hoverGradient={feature.hoverGradient}
				/>
			))}
		</div>
	);
}
