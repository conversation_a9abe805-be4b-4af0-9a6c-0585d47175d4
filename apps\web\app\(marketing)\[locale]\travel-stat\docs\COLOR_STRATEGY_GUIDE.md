# 🎨 颜色策略系统使用指南

## 📋 概述

新的颜色策略系统采用策略模式设计，将分散的颜色计算逻辑统一管理，提供了良好的扩展性和维护性。

## 🏗️ 系统架构

```
utils/
├── colorStrategy.ts    # 策略系统核心
├── colorUtils.ts      # 兼容性工具函数
└── ...

策略接口 (ColorStrategy)
├── VisitCountStrategy     # 基于访问次数
├── TimeDistanceStrategy   # 基于时间距离  
├── CompositeStrategy      # 复合策略
└── ...                    # 更多策略...
```

## 🚀 快速开始

### 基础使用

```typescript
import { getStrategyManager, updateStrategyTheme } from "../utils/colorUtils";

// 更新颜色主题
updateStrategyTheme("warm-sunset");

// 获取策略管理器
const manager = getStrategyManager();

// 计算颜色
const color = manager.getHexColor(3); // 根据访问次数3获取颜色
const rgbaColor = manager.getRgbaColor(3); // 获取RGBA格式
```

### 高级使用

```typescript
import { 
  createColorStrategyManager, 
  VisitCountStrategy, 
  PRESET_STRATEGIES 
} from "../utils/colorStrategy";

// 创建自定义策略管理器
const customManager = createColorStrategyManager("visitCountStrict", "neon-bright");

// 使用自定义阈值的策略
const customStrategy = new VisitCountStrategy({
  thresholds: {
    level1: 1,
    level2: 3,
    level3: 6,
    level4: 10,
    level5: 15,
    level6to10: 20,
    level10plus: 30,
  }
});

// 设置自定义策略
customManager.setStrategy(customStrategy);
```

## 📊 可用策略

### 1. 访问次数策略 (VisitCountStrategy)

**默认阈值：**
- Level 1: 1次访问
- Level 2: 2次访问  
- Level 3: 3次访问
- Level 4: 4次访问
- Level 5: 5次访问
- Level 6-10: 6次访问
- Level 10+: 11次访问

**预设变体：**
- `visitCount` - 默认策略
- `visitCountLoose` - 宽松阈值（需要更多访问才升级）
- `visitCountStrict` - 严格阈值（更容易升级）

### 2. 时间距离策略 (TimeDistanceStrategy)

根据最后访问时间的距离计算颜色：
- 30天内：深色（频繁）
- 6个月内：中等色
- 1年内：浅色
- 1年以上：灰色（很久未访问）

### 3. 复合策略 (CompositeStrategy)

结合访问次数和时间距离的复合判断。

## 🎛️ 配置策略

### 动态调整阈值

```typescript
const strategy = new VisitCountStrategy();

// 获取当前配置
const config = strategy.getConfig();
console.log(config);

// 修改配置
strategy.setConfig({
  thresholds: {
    ...config.thresholds,
    level2: 5, // 将level2阈值改为5次访问
  }
});
```

### 配置预设策略

```typescript
// 使用宽松策略
const looseManager = createColorStrategyManager("visitCountLoose");

// 使用严格策略  
const strictManager = createColorStrategyManager("visitCountStrict");
```

## 🔧 扩展新策略

### 创建自定义策略

```typescript
class PopularityStrategy implements ColorStrategy {
  readonly name = "popularity";
  readonly description = "根据地点热门程度确定颜色";
  readonly version = "1.0.0";
  
  private config = {
    popularityThresholds: {
      low: 100,
      medium: 1000, 
      high: 5000,
      veryHigh: 10000,
    }
  };
  
  calculateLevel(popularityScore: number): ColorLevel {
    const { popularityThresholds } = this.config;
    
    if (popularityScore >= popularityThresholds.veryHigh) {
      return ColorLevel.LEVEL10PLUS;
    }
    if (popularityScore >= popularityThresholds.high) {
      return ColorLevel.LEVEL5;
    }
    if (popularityScore >= popularityThresholds.medium) {
      return ColorLevel.LEVEL3;
    }
    if (popularityScore >= popularityThresholds.low) {
      return ColorLevel.LEVEL1;
    }
    
    return ColorLevel.UNVISITED;
  }
  
  getConfig() { return { ...this.config }; }
  setConfig(config: any) { this.config = { ...this.config, ...config }; }
}

// 使用自定义策略
const customStrategy = new PopularityStrategy();
const manager = new ColorStrategyManager(customStrategy, "vibrant-rainbow");
```

## 💡 最佳实践

### 1. 策略选择指南

**访问次数策略** - 适用于：
- 个人旅行足迹展示
- 基于历史访问数据的可视化

**时间距离策略** - 适用于：
- 突出最近访问的地点
- 时间相关的数据分析

**复合策略** - 适用于：
- 需要综合多个维度的复杂场景
- 高级数据分析需求

### 2. 性能优化

```typescript
// 批量计算颜色（推荐用于大量数据）
const dataList = [1, 2, 3, 4, 5];
const colors = manager.batchCalculateColors(dataList);

// 避免在循环中重复获取管理器实例
const manager = getStrategyManager();
points.forEach(point => {
  const color = manager.getHexColor(point.visitCount); // ✅ 好
  // const color = getStrategyManager().getHexColor(point.visitCount); // ❌ 不好
});
```

### 3. 调试和监控

```typescript
// 获取当前策略信息
const strategyInfo = manager.getStrategyInfo();
console.log("当前策略:", strategyInfo);

// 获取当前主题信息  
const themeInfo = manager.getThemeInfo();
console.log("当前主题:", themeInfo);

// 获取所有可用策略
const availableStrategies = getAvailableStrategies();
console.log("可用策略:", availableStrategies);
```

## ⚠️ 迁移指南

### 从旧API迁移到新API

**旧代码：**
```typescript
import { getVisitColorHex, setColorTheme } from "../utils/colorUtils";

setColorTheme("warm-sunset");
const color = getVisitColorHex(3);
```

**新代码（推荐）：**
```typescript
import { updateStrategyTheme, getStrategyManager } from "../utils/colorUtils";

updateStrategyTheme("warm-sunset");
const color = getStrategyManager().getHexColor(3);
```

**注意：** 旧API仍然可用，但标记为 `@deprecated`，建议逐步迁移到新API。

## 🔮 未来扩展

系统设计支持未来添加更多策略：

- **季节策略** - 根据访问的季节显示不同颜色
- **天气策略** - 基于当时的天气条件
- **活动类型策略** - 根据旅行活动类型（商务、休闲、探险等）
- **社交热度策略** - 基于社交媒体分享数据
- **个人偏好策略** - 基于用户的个人喜好

## 📞 技术支持

如有问题或建议，请参考：
- 源码：`utils/colorStrategy.ts`
- 示例：`MapContainer.tsx` 中的使用方式
- 类型定义：`types/colorTypes.ts` 