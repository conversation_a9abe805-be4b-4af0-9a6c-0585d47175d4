# Database
DATABASE_URL="YOUR_DATABASE_CONNECTION_STRING"
# ... if you use Supabase
DIRECT_URL="YOUR_DATABASE_CONNECTION_STRING"

# Site url
NEXT_PUBLIC_SITE_URL="http://localhost:3000"

# Authentication
BETTER_AUTH_SECRET="A_RANDOM_SECRET_STRING"
# ... for Github
GITHUB_CLIENT_ID="YOUR_GITHUB_CLIENT_ID"
GITHUB_CLIENT_SECRET="YOUR_GITHUB_CLIENT_SECRET"
# ... for Google
GOOGLE_CLIENT_ID="YOUR_GOOGLE_CLIENT_ID"
GOOGLE_CLIENT_SECRET="YOUR_GOOGLE_CLIENT_SECRET"

# Mails
# ... with nodemailer
MAIL_HOST=""
MAIL_PORT=""
MAIL_USER=""
MAIL_PASS=""
# ... with Plunk
PLUNK_API_KEY=""
# ... with Resend
RESEND_API_KEY=""
# ... with Postmark
POSTMARK_SERVER_TOKEN=""

GOOGLE_MAPS_API_KEY=""
AMAP_API_KEY=""
BAIDU_MAPS_API_KEY=""
MAPBOX_API_KEY=""
# BING_MAPS_API_KEY=""

# Payments
# ... with Lemonsqueezy
LEMONSQUEEZY_API_KEY=""
LEMONSQUEEZY_WEBHOOK_SECRET=""
LEMONSQUEEZY_STORE_ID=""
# ... with Stripe
STRIPE_SECRET_KEY=""
STRIPE_WEBHOOK_SECRET=""
# ... with Chargebee
CHARGEBEE_SITE=""
CHARGEBEE_API_KEY=""
# ... with Creem
CREEM_API_KEY=""
CREEM_WEBHOOK_SECRET=""
# ... with Polar
POLAR_ACCESS_TOKEN=""
POLAR_WEBHOOK_SECRET=""

# Products
NEXT_PUBLIC_PRODUCT_ID_PRO_MONTHLY="prod_77mnWUvIXKJuLKisxMRTIn"
NEXT_PUBLIC_PRODUCT_ID_PRO_YEARLY="prod_TSRTiZYbv8sOPzHEG7Vvv"

# Analytics
# ... for Pirsch
NEXT_PUBLIC_PIRSCH_CODE=""
# ... for Plausible
NEXT_PUBLIC_PLAUSIBLE_URL=""
# ... for Mixpanel
NEXT_PUBLIC_MIXPANEL_TOKEN=""
# ... for Google Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=""

# Storage
S3_ACCESS_KEY_ID=""
S3_SECRET_ACCESS_KEY=""
S3_ENDPOINT=""
S3_REGION=""
NEXT_PUBLIC_AVATARS_BUCKET_NAME="avatars"

# S3示例配置
S3_ENDPOINT=https://s3.amazonaws.com
S3_REGION=us-east-1
S3_ACCESS_KEY_ID=your_access_key
S3_SECRET_ACCESS_KEY=your_secret_key

# 阿里云OSS示例配置
ALIYUN_OSS_REGION=oss-cn-beijing
ALIYUN_OSS_ENDPOINT=https://oss-cn-beijing.aliyuncs.com
ALIYUN_OSS_ACCESS_KEY_ID=your_access_key
ALIYUN_OSS_SECRET_ACCESS_KEY=your_secret_key

# 腾讯云COS示例配置
TENCENT_COS_REGION=ap-beijing
TENCENT_COS_SECRET_ID=your_secret_id
TENCENT_COS_SECRET_KEY=your_secret_key

CLOUDFLARE_R2_SECRET_ACCESS_KEY=""
CLOUDFLARE_R2_ACCOUNT_ID=""
CLOUDFLARE_R2_ACCESS_KEY_ID


# 可选：显式指定存储提供商类型
# STORAGE_PROVIDER_TYPE=tencent-cos

# AI
# ... with OpenAI
OPENAI_API_KEY=""
