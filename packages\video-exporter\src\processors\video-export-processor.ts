import { spawn } from "node:child_process";
import fs from "node:fs";
import path from "node:path";
import { db } from "@repo/database/src/client";
import { logger } from "@repo/logs";
// 使用 try-catch 来导入 playwright 包，以便在出错时提供更好的错误信息
let chromium: any;
try {
	({ chromium } = require("playwright"));
	logger.info("Playwright 导入成功");
} catch (e) {
	logger.error(`导入 Playwright 失败: ${e}`);
	// 尝试使用 playwright-core 作为备选
	try {
		({ chromium } = require("playwright-core/lib/server"));
		logger.info("从 playwright-core 导入 chromium 成功");
	} catch (e2) {
		logger.error(`从 playwright-core 导入失败: ${e2}`);
	}
}
import type { JobData, ProcessResult } from "../types";

// 设置视频分辨率
const RESOLUTIONS = {
	"720p": { width: 1280, height: 720 },
	"1080p": { width: 1920, height: 1080 },
};

/**
 * 处理视频导出任务
 */
export async function videoExportProcessor(
	jobData: JobData,
): Promise<ProcessResult> {
	const { taskId, diaryId, userId, options } = jobData;
	const startTime = Date.now();

	// 辅助函数：生成带时间戳的文件名
	const getTimestampedFilename = (prefix: string) => {
		const now = new Date();
		// 格式化为 YYYYMMDD_HHMMSS 格式
		const timestamp = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, "0")}${String(now.getDate()).padStart(2, "0")}_${String(now.getHours()).padStart(2, "0")}${String(now.getMinutes()).padStart(2, "0")}${String(now.getSeconds()).padStart(2, "0")}`;
		return `${prefix}_${timestamp}.png`;
	};

	logger.info("开始处理视频导出任务", { taskId, diaryId });

	// 检查chromium是否可用
	if (!chromium) {
		logger.error("Chromium 对象不可用，无法启动浏览器", { taskId });
		throw new Error("视频导出服务初始化失败: 浏览器引擎不可用");
	}

	try {
		// 创建输出目录
		const outputDir = path.join(process.cwd(), "temp", "videos", taskId);
		fs.mkdirSync(outputDir, { recursive: true });
		logger.debug("创建输出目录成功", { taskId, outputDir });

		const videoPath = path.join(outputDir, `${diaryId}.mp4`);

		// 更新进度
		await updateProgress(taskId, 20, "获取日记数据");

		// 获取日记详情
		const diary = await getDiaryData(diaryId, userId);
		if (!diary) {
			throw new Error("日记不存在或无权访问");
		}
		logger.debug("获取日记数据成功", { taskId, diaryId });

		// 更新进度
		await updateProgress(taskId, 30, "准备录制环境");

		// 获取分辨率设置
		const resolution =
			RESOLUTIONS[options.resolution as keyof typeof RESOLUTIONS] ||
			RESOLUTIONS["720p"];
		logger.debug("视频分辨率设置", { taskId, resolution });

		// 添加调试日志
		logger.debug("Playwright 状态检查", {
			taskId,
			chromiumAvailable: !!chromium,
			nodeEnv: process.env.NODE_ENV,
			playwrightVersion: chromium?.version?.(),
			executablePath:
				process.env.PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH || "未设置",
		});

		// 启动 Playwright
		logger.info("准备启动浏览器...", { taskId });

		// 浏览器启动选项
		const launchOptions = {
			headless: true,
			args: [
				"--no-sandbox",
				"--disable-setuid-sandbox",
				"--disable-dev-shm-usage",
			],
		};

		if (process.env.PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH) {
			logger.debug("使用自定义Chrome路径", {
				taskId,
				path: process.env.PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH,
			});
			// 添加自定义Chrome路径
			Object.assign(launchOptions, {
				executablePath: process.env.PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH,
			});
		}

		// 添加更多启动错误处理
		try {
			logger.debug("浏览器启动选项", { taskId, launchOptions });
			const browser = await chromium.launch(launchOptions);
			logger.info("浏览器启动成功", { taskId });

			// 创建上下文
			const context = await browser.newContext({
				viewport: resolution,
				recordVideo: {
					dir: outputDir,
					size: resolution,
				},
			});
			logger.debug("浏览器上下文创建成功", { taskId });

			// 创建页面
			const page = await context.newPage();
			logger.debug("页面创建成功", { taskId });

			try {
				// 更新进度
				await updateProgress(taskId, 40, "访问目标页面");

				// 构建要访问的URL，包含导出模式参数
				const appBaseUrl =
					process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

				// 添加导出令牌 - 应该与 auth.ts 中定义的 EXPORT_AUTH_TOKEN 保持一致
				// 理想情况下应该从环境变量或配置中获取
				const exportToken =
					"j8Kp4Xz2Q9vYfTs7B3e5W6gA1hR0dNmVlCuMoIpLxZyE";

				const url = `${appBaseUrl}/app/presentation/${diaryId}?export=video&autoPlay=true&pointDuration=${options.pointDuration}&exportToken=${exportToken}`;

				logger.info(`访问页面: ${url}`, { taskId });
				await page.goto(url, { timeout: 60000 }); // 增加超时至60秒
				logger.debug("页面导航成功", { taskId });

				// 等待页面加载完成
				await page.waitForLoadState("networkidle");
				logger.debug("页面网络请求已完成", { taskId });

				// 更新进度
				await updateProgress(taskId, 50, "等待地图加载");

				// 等待地图加载完成
				try {
					await page.waitForSelector(".travel-memo-content", {
						state: "visible",
						timeout: 30000, // 30秒超时
					});
					logger.debug("地图内容元素已加载", { taskId });
				} catch (selectorError) {
					logger.error(`等待地图元素超时: ${selectorError}`, {
						taskId,
					});
					// 保存当前页面截图，用于调试
					const errorFilename =
						getTimestampedFilename("error-map-timeout");
					await page.screenshot({
						path: path.join(outputDir, errorFilename),
						fullPage: true,
					});
					logger.debug(`已保存地图加载超时截图: ${errorFilename}`, {
						taskId,
					});
					throw new Error("地图加载超时，请检查应用是否正常运行");
				}

				// 更新进度
				await updateProgress(taskId, 60, "开始录制");

				// 识别当前页面类型并处理
				// 如果是 CoverPage，点击继续按钮进入 MapStoryPage
				const isCoverPage = await page.evaluate(() => {
					return (
						document.querySelector(
							'.travel-memo-content button[aria-label="开始旅行"]',
						) !== null
					);
				});
				logger.debug("页面类型检测", { taskId, isCoverPage });

				if (isCoverPage) {
					logger.info("当前在封面页，点击进入地图故事页", { taskId });
					await page.click('button[aria-label="开始旅行"]');
					// 等待过渡动画完成
					await page.waitForTimeout(1000);
					logger.debug("已点击开始按钮", { taskId });
				}

				// 更新进度
				await updateProgress(taskId, 70, "正在录制");

				// 等待地图故事播放完成
				logger.info("等待地图故事播放完成", { taskId });

				// 每30秒记录一次进度
				let checkCount = 0;
				const progressInterval = setInterval(() => {
					checkCount++;
					logger.debug(`等待故事播放中... ${checkCount * 30}秒`, {
						taskId,
					});
				}, 30000);

				try {
					// 等待故事播放完成标志
					await page.waitForFunction(
						"window.storyExportCompleted === true",
						{
							timeout: 600000, // 最长等待10分钟
							polling: 1000, // 每秒检查一次
						},
					);
					clearInterval(progressInterval);
					logger.info("故事播放已完成", { taskId });
				} catch (waitError) {
					clearInterval(progressInterval);
					logger.error(`等待故事完成超时: ${waitError}`, { taskId });

					// 尝试获取当前页面状态
					const pageState = await page
						.evaluate(() => {
							return {
								exportCompleted: window.storyExportCompleted,
								url: window.location.href,
								title: document.title,
							};
						})
						.catch((e: Error) => ({ error: String(e) }));

					logger.debug("页面状态信息", { taskId, pageState });

					// 保存当前页面截图，用于调试
					const errorFilename = getTimestampedFilename(
						"error-playback-timeout",
					);
					await page.screenshot({
						path: path.join(outputDir, errorFilename),
						fullPage: true,
					});
					logger.debug(`已保存播放超时截图: ${errorFilename}`, {
						taskId,
					});

					throw new Error("故事播放完成等待超时，请检查播放逻辑");
				}

				logger.info("地图故事播放完成，准备关闭浏览器", { taskId });

				// 更新进度
				await updateProgress(taskId, 80, "完成录制，处理视频");

				// 故事播放完成，关闭浏览器以保存视频
				await context.close();
				logger.debug("浏览器上下文已关闭", { taskId });

				await browser.close();
				logger.debug("浏览器已完全关闭", { taskId });

				// 处理生成的视频文件
				// Playwright 录制的视频文件名可能是随机的，需要找到并重命名
				const files = fs
					.readdirSync(outputDir)
					.filter((f) => f.endsWith(".webm"));

				logger.debug("检查录制文件", {
					taskId,
					filesFound: files.length,
					files,
					dirContents: fs.readdirSync(outputDir),
				});

				if (files.length === 0) {
					throw new Error("未找到录制的视频文件，视频录制可能失败");
				}

				// 找到生成的视频文件
				const recordedVideo = path.join(outputDir, files[0]);
				logger.debug("找到录制的视频文件", { taskId, recordedVideo });

				// 更新进度
				await updateProgress(taskId, 90, "转换视频格式");

				// 使用ffmpeg转换为MP4
				const mp4Path = await convertToMp4(
					recordedVideo,
					videoPath,
					options.fps,
				);
				logger.debug("视频格式转换完成", { taskId, mp4Path });

				// 更新进度
				await updateProgress(taskId, 95, "清理临时文件");

				// 清理临时文件
				if (fs.existsSync(recordedVideo)) {
					fs.unlinkSync(recordedVideo);
					logger.debug("已删除原始WebM文件", { taskId });
				}

				// 计算处理时间
				const processingTime = Date.now() - startTime;
				logger.info(`视频导出完成: ${mp4Path}`, {
					taskId,
					processingTime,
				});

				// 在实际生产环境中，应该将视频上传到云存储，并返回URL
				// 这里简单地返回本地文件路径作为URL
				const videoUrl = `file://${mp4Path}`;

				return {
					videoUrl,
					processingTime,
				};
			} catch (error) {
				logger.error(
					`视频录制过程中出错: ${error instanceof Error ? error.message : String(error)}`,
					{
						taskId,
						errorStack:
							error instanceof Error ? error.stack : undefined,
					},
				);

				// 尝试保存截图
				try {
					const errorFilename =
						getTimestampedFilename("error-recording");
					await page.screenshot({
						path: path.join(outputDir, errorFilename),
						fullPage: true,
					});
					logger.debug(`已保存错误时的页面截图: ${errorFilename}`, {
						taskId,
					});
				} catch (e) {
					logger.error(`保存错误截图失败: ${e}`, { taskId });
				}

				// 确保浏览器关闭
				try {
					await browser.close();
					logger.debug("出错后浏览器已关闭", { taskId });
				} catch (closeError) {
					logger.error(`关闭浏览器失败: ${closeError}`, { taskId });
				}

				throw error;
			}
		} catch (browserError) {
			logger.error(`浏览器启动或操作失败: ${browserError}`, {
				taskId,
				errorStack:
					browserError instanceof Error
						? browserError.stack
						: undefined,
			});
			throw new Error(`浏览器操作失败: ${browserError}`);
		}
	} catch (error) {
		logger.error(
			`视频导出失败: ${error instanceof Error ? error.message : String(error)}`,
			{
				taskId,
				errorStack: error instanceof Error ? error.stack : undefined,
			},
		);
		throw error;
	}
}

/**
 * 将WebM转换为MP4
 */
async function convertToMp4(
	inputPath: string,
	outputPath: string,
	fps: number,
): Promise<string> {
	return new Promise((resolve, reject) => {
		try {
			logger.debug("开始转换视频格式", { inputPath, outputPath, fps });

			// 使用ffmpeg转换为mp4
			const ffmpeg = spawn("ffmpeg", [
				"-i",
				inputPath,
				"-c:v",
				"libx264",
				"-preset",
				"fast",
				"-r",
				fps.toString(), // 设置帧率
				"-pix_fmt",
				"yuv420p", // 确保兼容性
				"-movflags",
				"+faststart", // 优化网络播放
				"-y", // 覆盖输出文件
				outputPath,
			]);

			ffmpeg.stderr.on("data", (data) => {
				// ffmpeg 输出日志到 stderr
				logger.debug(`ffmpeg: ${data}`);
			});

			ffmpeg.on("close", (code) => {
				if (code === 0) {
					logger.debug("视频转换完成", { outputPath });
					resolve(outputPath);
				} else {
					reject(new Error(`ffmpeg 进程退出，状态码 ${code}`));
				}
			});

			ffmpeg.on("error", (err) => {
				reject(new Error(`ffmpeg 错误: ${err.message}`));
			});
		} catch (error) {
			reject(
				new Error(
					`启动 ffmpeg 失败: ${error instanceof Error ? error.message : String(error)}`,
				),
			);
		}
	});
}

/**
 * 获取日记数据
 */
async function getDiaryData(diaryId: string, userId: string) {
	try {
		// 从数据库直接获取日记数据
		const diary = await db.travelDiary.findFirst({
			where: {
				id: diaryId,
				OR: [{ userId }, { isPublic: true }],
			},
		});

		return diary;
	} catch (error) {
		logger.error(
			`获取日记数据失败: ${error instanceof Error ? error.message : String(error)}`,
		);
		throw new Error("获取日记数据失败");
	}
}

/**
 * 更新任务进度
 */
async function updateProgress(
	taskId: string,
	progress: number,
	stage?: string,
) {
	try {
		await db.videoExportTask.update({
			where: { id: taskId },
			data: {
				progress,
				...(stage ? { errorMessage: stage } : {}), // 临时使用 errorMessage 字段存储阶段信息
			},
		});
		logger.debug(`更新任务进度: ${progress}%`, { taskId, stage });
	} catch (error) {
		logger.error(
			`更新任务进度失败: ${error instanceof Error ? error.message : String(error)}`,
		);
		// 不抛出错误，让处理继续
	}
}

/**
 * 为window对象扩展类型声明，添加storyExportCompleted属性
 */
declare global {
	interface Window {
		storyExportCompleted?: boolean;
	}
}
