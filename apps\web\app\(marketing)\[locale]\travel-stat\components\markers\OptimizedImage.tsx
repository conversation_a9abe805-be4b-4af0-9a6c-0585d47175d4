"use client";
import { memo, useCallback, useState } from "react";

interface OptimizedImageProps {
	src: string;
	alt: string;
	className?: string;
	style?: React.CSSProperties;
	onLoad?: (e: React.SyntheticEvent<HTMLImageElement>) => void;
	onError?: (e: React.SyntheticEvent<HTMLImageElement>) => void;
	loading?: "lazy" | "eager";
	placeholder?: React.ReactNode;
}

/**
 * 🔧 优化图片组件 - 专门为marker设计的高性能图片渲染
 *
 * 优化特性：
 * 1. GPU加速 (will-change, transform3d)
 * 2. 避免重排重绘 (transform instead of position)
 * 3. 内存友好 (及时释放blob URL)
 * 4. 占位符支持
 * 5. Memo优化，避免不必要的重渲染
 */
const OptimizedImage = memo(function OptimizedImage({
	src,
	alt,
	className = "",
	style = {},
	onLoad,
	onError,
	loading = "lazy",
	placeholder,
}: OptimizedImageProps) {
	const [imageLoaded, setImageLoaded] = useState(false);
	const [imageError, setImageError] = useState(false);

	const handleLoad = useCallback(
		(e: React.SyntheticEvent<HTMLImageElement>) => {
			setImageLoaded(true);
			setImageError(false);
			onLoad?.(e);
		},
		[onLoad],
	);

	const handleError = useCallback(
		(e: React.SyntheticEvent<HTMLImageElement>) => {
			setImageError(true);
			setImageLoaded(false);
			onError?.(e);
		},
		[onError],
	);

	// 🔧 优化样式：使用GPU加速和避免重排
	const optimizedStyle: React.CSSProperties = {
		...style,
		// GPU加速
		willChange: "transform",
		// 使用transform而非position变化（避免重排）
		transform: "translate3d(0, 0, 0)",
		// 防止选择和拖拽
		userSelect: "none",
		// 图片渲染优化
		imageRendering: "auto",
		// 避免字体渲染影响
		WebkitFontSmoothing: "antialiased",
		// 优化合成层
		isolation: "isolate",
	};

	return (
		<div
			className="relative w-full h-full"
			style={{
				// 容器也使用GPU加速
				willChange: "transform",
				transform: "translate3d(0, 0, 0)",
			}}
		>
			{/* 占位符 - 在图片加载前显示 */}
			{!imageLoaded && !imageError && placeholder && (
				<div className="absolute inset-0 flex items-center justify-center">
					{placeholder}
				</div>
			)}

			{/* 主图片 */}
			<img
				src={src}
				alt={alt}
				className={`${className} ${imageLoaded ? "opacity-100" : "opacity-0"}`}
				style={{
					...optimizedStyle,
					// 渐入动画
					transition: "opacity 0.2s ease-in-out",
				}}
				onLoad={handleLoad}
				onError={handleError}
				loading={loading}
				// 🔧 关键优化属性
				decoding="async" // 异步解码，避免阻塞主线程
				fetchPriority="low" // 降低获取优先级，避免阻塞关键资源
			/>
		</div>
	);
});

export { OptimizedImage };
