"use client";

import { LoginForm as BaseLoginForm } from "../components/LoginForm";

// 从 shadcn/ui 导入的组件
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

// 图标
import {
	AlertTriangleIcon,
	ArrowRightIcon,
	EyeIcon,
	EyeOffIcon,
	KeyIcon,
	MailboxIcon,
} from "lucide-react";

interface LoginFormAdapterProps {
	// 翻译函数
	t: (key: string) => string;

	// 自定义配置
	redirectAfterSignIn?: string;
	enablePasswordLogin?: boolean;
	enableMagicLink?: boolean;

	// 可选的自定义组件
	LoginModeSwitch?: any;
	SocialSigninButton?: any;
	OrganizationInvitationAlert?: any;

	// 错误处理
	getAuthErrorMessage?: (code?: string) => string;
}

export function LoginFormAdapter(props: LoginFormAdapterProps) {
	return (
		<BaseLoginForm
			{...props}
			Alert={Alert}
			AlertDescription={AlertDescription}
			AlertTitle={AlertTitle}
			Button={Button}
			Form={Form}
			FormControl={FormControl}
			FormField={FormField}
			FormItem={FormItem}
			FormLabel={FormLabel}
			Input={Input}
			icons={{
				AlertTriangle: AlertTriangleIcon,
				ArrowRight: ArrowRightIcon,
				Eye: EyeIcon,
				EyeOff: EyeOffIcon,
				Key: KeyIcon,
				Mailbox: MailboxIcon,
			}}
		/>
	);
}
