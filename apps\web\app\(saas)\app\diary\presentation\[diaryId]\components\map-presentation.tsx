"use client";

import {
	createPulseEffect,
	startPulseAnimation,
	stopPulseAnimation,
	useMapAnimation,
} from "@modules/map-presentation";
import {
	type MapOptions,
	initializeGoogleMap,
} from "@modules/map-presentation/initialize-map";
import type { MapPoint } from "@modules/map-presentation/types";
import { useEffect, useRef, useState } from "react";
import { checkIfGoogleMapsLoaded } from "../../../../../../../lib/google-maps-loader";

// 点位类型定义
interface Point {
	id: string;
	coordinates: { lat: number; lng: number };
	timestamp: string;
	location: string; // 不再是可选属性
	iconType?: string;
	date: string; // 不再是可选属性
	description: string; // 不再是可选属性
}

interface MapPresentationProps {
	diary: any;
	onFinish: () => void;
}

export function MapPresentation({ diary, onFinish }: MapPresentationProps) {
	// 状态
	const [currentPointIndex, setCurrentPointIndex] = useState(0);
	const [displayedPoints, setDisplayedPoints] = useState<Point[]>([]);
	const [isPlaying, setIsPlaying] = useState(false);
	const [playbackSpeed, setPlaybackSpeed] = useState(3); // 默认速度，单位秒
	const isPlayingRef = useRef(isPlaying);

	// 从日记数据中提取所有点位
	const allPoints: Point[] = diary.timelines
		.flatMap((timeline: any) => timeline.points)
		.sort(
			(a: any, b: any) =>
				new Date(a.date).getTime() - new Date(b.date).getTime(),
		);
	// 引用
	const mapRef = useRef<google.maps.Map | null>(null);
	const mapContainerRef = useRef<HTMLDivElement>(null);
	const autoPlayTimerRef = useRef<NodeJS.Timeout | null>(null);
	const pulseEffectRef = useRef<any>(null);
	const pulseAnimationRef = useRef<number | null>(null);

	// 更新isPlayingRef
	useEffect(() => {
		isPlayingRef.current = isPlaying;
	}, [isPlaying]);

	// 初始化地图动画 hook
	const {
		addMarker,
		clearMarkers,
		clearPolylines,
		drawRouteBetweenPoints,
		animateRoute,
		calculateDistance,
		getBestZoomForDistance,
		focusOnRouteBetweenPoints,
		adjustMapViewToPoints,
		executeThreeStageAnimation,
	} = useMapAnimation(mapRef, {
		routeAnimationStyle: "dashed",
	});

	// 将 Point 类型转换为 MapPoint 类型
	const convertToMapPoints = (points: Point[]): MapPoint[] => {
		return points.map((point) => ({
			coordinates: point.coordinates,
			// 确保提供默认值，避免 undefined
			location: point.location || "",
			description: point.description || "",
			date: point.date || point.timestamp || "",
			iconType: point.iconType,
		}));
	};

	// 绘制多个点之间的路线
	const drawRouteBetweenMultiplePoints = (points: MapPoint[]) => {
		// 至少需要两个点才能绘制路线
		if (points.length < 2) {
			return;
		}

		// 连接每两个相邻的点
		for (let i = 0; i < points.length - 1; i++) {
			drawRouteBetweenPoints(points[i], points[i + 1]);
		}
	};
	// 清除计时器的辅助函数
	const clearTimers = () => {
		if (autoPlayTimerRef.current) {
			clearTimeout(autoPlayTimerRef.current);
			autoPlayTimerRef.current = null;
		}

		// 清除脉冲动画
		if (pulseAnimationRef.current) {
			stopPulseAnimation(pulseAnimationRef.current);
			pulseAnimationRef.current = null;
		}
		// 移除脉冲效果
		if (pulseEffectRef.current?.setMap) {
			pulseEffectRef.current.setMap(null);
			pulseEffectRef.current = null;
		}
	};

	// 初始化地图
	useEffect(() => {
		if (!mapContainerRef.current || allPoints.length === 0) {
			return;
		}

		// 初始化Google地图
		const initMap = async () => {
			// 确定初始中心和缩放级别
			const initialCenter = allPoints[0].coordinates;

			// 地图选项配置
			const mapOptions: MapOptions = {
				mapTypeControl: true,
				streetViewControl: false,
				fullscreenControl: true,
				zoomControl: true,
				mapTypeControlOptions: {
					position: 1, // google.maps.ControlPosition.TOP_RIGHT
				},
				styles: [
					{
						featureType: "poi",
						elementType: "labels",
						stylers: [{ visibility: "off" }],
					},
				],
				// 传入所有点位，以便地图自动调整视图
				points: convertToMapPoints(allPoints),
			};

			// 使用 initializeGoogleMap 函数初始化地图
			try {
				if (mapContainerRef.current) {
					// initializeGoogleMap会自动处理Google Maps API的加载
					const map = await initializeGoogleMap(
						mapContainerRef as React.RefObject<HTMLDivElement>,
						initialCenter,
						12, // 初始缩放级别
						mapOptions,
					);

					mapRef.current = map;

					// 添加初始标记
					setDisplayedPoints([allPoints[0]]);
					setCurrentPointIndex(0);

					// 调整地图视图以包含所有点
					if (map && checkIfGoogleMapsLoaded()) {
						adjustMapViewToPoints(convertToMapPoints(allPoints));
					}
				}
			} catch (error) {
				console.error("初始化地图失败:", error);
			}
		};

		initMap();

		// 清理函数
		return () => {
			clearTimers();
			clearMarkers();
			clearPolylines();
		};
	}, [allPoints, adjustMapViewToPoints, clearMarkers, clearPolylines]);

	// 地图点位更新
	useEffect(() => {
		if (
			!mapRef.current ||
			allPoints.length === 0 ||
			!checkIfGoogleMapsLoaded()
		) {
			return;
		}

		// 清除现有标记和路线
		clearMarkers();
		clearPolylines();
		// 添加已显示的点位标记
		displayedPoints.forEach((point, index) => {
			const isCurrentPoint = index === displayedPoints.length - 1;
			const mapPoint: MapPoint = {
				coordinates: point.coordinates,
				location: point.location || "",
				description: point.description || "",
				date: point.date || point.timestamp || "",
				iconType: point.iconType,
			};

			addMarker(mapPoint);
		});

		// 绘制路线
		if (displayedPoints.length > 1) {
			const pointCoordinates = convertToMapPoints(displayedPoints);
			drawRouteBetweenMultiplePoints(pointCoordinates);
		}
		// 为当前点添加脉冲效果
		if (displayedPoints.length > 0 && checkIfGoogleMapsLoaded()) {
			const currentPoint = displayedPoints[displayedPoints.length - 1];
			if (mapRef.current && currentPoint) {
				const mapPoint: MapPoint = {
					coordinates: currentPoint.coordinates,
					location: currentPoint.location || "",
					description: currentPoint.description || "",
					date: currentPoint.date || currentPoint.timestamp || "",
					iconType: currentPoint.iconType,
				};

				pulseEffectRef.current = createPulseEffect(
					mapRef.current,
					mapPoint,
				);

				// 启动脉冲动画
				if (pulseEffectRef.current) {
					pulseAnimationRef.current = startPulseAnimation(
						pulseEffectRef.current,
					);
				}
			}
		}

		// 调整地图视图，显示所有当前点位
		adjustMapViewToPoints(convertToMapPoints(displayedPoints));
	}, [
		displayedPoints,
		addMarker,
		clearMarkers,
		clearPolylines,
		drawRouteBetweenPoints,
		adjustMapViewToPoints,
		allPoints,
	]);

	// 播放下一个点位的函数
	const playNextPoint = () => {
		const nextIndex = currentPointIndex + 1;

		if (nextIndex < allPoints.length) {
			setCurrentPointIndex(nextIndex);
			setDisplayedPoints((prev) => [...prev, allPoints[nextIndex]]);

			// 延迟播放下一个点位
			if (isPlayingRef.current && nextIndex < allPoints.length - 1) {
				autoPlayTimerRef.current = setTimeout(() => {
					if (isPlayingRef.current) {
						playNextPoint();
					}
				}, playbackSpeed * 1000);
			} else if (nextIndex === allPoints.length - 1) {
				// 到达最后一个点，停止播放
				setIsPlaying(false);
			}
		} else {
			// 如果已经是最后一个点了，停止播放
			setIsPlaying(false);
		}
	};

	// 处理播放/暂停
	const togglePlayPause = () => {
		const newPlayingState = !isPlaying;
		setIsPlaying(newPlayingState);

		if (newPlayingState) {
			// 如果是播放，且还有下一个点
			if (currentPointIndex < allPoints.length - 1) {
				clearTimers();
				playNextPoint();
			} else {
				// 如果已经到最后一个点，重置到第一个点
				clearTimers();
				setCurrentPointIndex(0);
				setDisplayedPoints([allPoints[0]]);
				// 延迟播放下一个点位
				autoPlayTimerRef.current = setTimeout(() => {
					if (isPlayingRef.current) {
						playNextPoint();
					}
				}, playbackSpeed * 1000);
			}
		} else {
			// 如果是暂停
			clearTimers();
		}
	};

	// 设置播放速度
	const handleSpeedChange = () => {
		// 循环切换速度：3秒 -> 2秒 -> 1秒 -> 3秒
		const nextSpeed = playbackSpeed === 1 ? 3 : playbackSpeed - 1;
		setPlaybackSpeed(nextSpeed);
	};

	// 上一个点位
	const handlePrev = () => {
		if (currentPointIndex > 0) {
			clearTimers();
			setIsPlaying(false);
			const newIndex = currentPointIndex - 1;
			setCurrentPointIndex(newIndex);
			setDisplayedPoints(allPoints.slice(0, newIndex + 1));
		}
	};

	// 下一个点位
	const handleNext = () => {
		if (currentPointIndex < allPoints.length - 1) {
			clearTimers();
			setIsPlaying(false);
			playNextPoint();
		}
	};

	return (
		<div className="h-full w-full relative">
			{/* 地图容器 */}
			<div ref={mapContainerRef} className="h-full w-full z-0" />

			{/* 自动播放控件 */}
			<div className="absolute left-4 bottom-4 z-10">
				<div className="bg-white/90 dark:bg-gray-800/90 rounded-lg shadow-lg p-2 backdrop-blur">
					<div className="flex items-center space-x-2">
						{/* 进度显示 */}
						<div className="text-xs font-medium text-gray-700 dark:text-gray-300 min-w-[60px]">
							{currentPointIndex + 1}/{allPoints.length}
						</div>

						{/* 播放控制按钮 */}
						<div className="flex items-center space-x-1">
							{/* 上一个按钮 */}
							<button
								type="button"
								onClick={handlePrev}
								disabled={currentPointIndex === 0}
								className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 
                         disabled:opacity-50 disabled:cursor-not-allowed"
								title="上一个"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="16"
									height="16"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									strokeWidth="2"
									strokeLinecap="round"
									strokeLinejoin="round"
								>
									<title>上一个</title>
									<path d="m12 19-7-7 7-7" />
									<path d="M19 12H5" />
								</svg>
							</button>

							{/* 播放/暂停按钮 */}
							<button
								type="button"
								onClick={togglePlayPause}
								className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700"
								title={isPlaying ? "暂停" : "播放"}
							>
								{isPlaying ? (
									<svg
										xmlns="http://www.w3.org/2000/svg"
										width="16"
										height="16"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										strokeWidth="2"
										strokeLinecap="round"
										strokeLinejoin="round"
									>
										<title>暂停</title>
										<rect
											x="6"
											y="4"
											width="4"
											height="16"
										/>
										<rect
											x="14"
											y="4"
											width="4"
											height="16"
										/>
									</svg>
								) : (
									<svg
										xmlns="http://www.w3.org/2000/svg"
										width="16"
										height="16"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										strokeWidth="2"
										strokeLinecap="round"
										strokeLinejoin="round"
									>
										<title>播放</title>
										<polygon points="5 3 19 12 5 21 5 3" />
									</svg>
								)}
							</button>

							{/* 下一个按钮 */}
							<button
								type="button"
								onClick={handleNext}
								disabled={
									currentPointIndex === allPoints.length - 1
								}
								className="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-700 
                         disabled:opacity-50 disabled:cursor-not-allowed"
								title="下一个"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="16"
									height="16"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									strokeWidth="2"
									strokeLinecap="round"
									strokeLinejoin="round"
								>
									<title>下一个</title>
									<path d="M5 12h14" />
									<path d="m12 5 7 7-7 7" />
								</svg>
							</button>

							{/* 速度控制 */}
							<button
								type="button"
								onClick={handleSpeedChange}
								className="ml-1 text-xs font-medium bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded"
								title="调整播放速度"
							>
								{playbackSpeed}x
							</button>
						</div>
					</div>
				</div>
			</div>

			{/* 旅行总结按钮 */}
			<div className="absolute right-6 bottom-6 z-50">
				<button
					onClick={onFinish}
					type="button"
					className="flex items-center justify-center bg-primary hover:bg-primary/90 text-white rounded-full px-4 py-2 text-sm shadow-lg transition-all"
				>
					旅行总结
				</button>
			</div>
		</div>
	);
}
