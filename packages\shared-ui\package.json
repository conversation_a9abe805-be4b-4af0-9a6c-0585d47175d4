{"name": "@repo/shared-ui", "version": "0.0.0", "private": true, "type": "module", "exports": {"./auth": "./src/auth/index.ts", "./marketing": "./src/marketing/index.ts", "./payments": "./src/payments/index.ts", "./onboarding": "./src/onboarding/index.ts", "./settings": "./src/settings/index.ts", "./components": "./src/components/index.ts", "./feature-voting": "./src/feature-voting/index.ts"}, "scripts": {"build": "echo 'Skipping TypeScript compilation due to type errors' && exit 0", "dev": "tsc --watch", "lint": "biome lint .", "type-check": "tsc --noEmit"}, "dependencies": {"@repo/auth": "workspace:*", "@repo/database": "workspace:*", "@repo/utils": "workspace:*", "next": "15.1.3", "react": "19.0.0", "react-dom": "19.0.0"}, "devDependencies": {"@repo/tsconfig": "workspace:*", "@types/node": "^22.14.0", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "typescript": "5.8.3"}}