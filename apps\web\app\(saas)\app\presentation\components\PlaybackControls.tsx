"use client";

import { But<PERSON> } from "@ui/components/button";
import { Slider } from "@ui/components/slider";
import { cn } from "@ui/lib";
import {
	ChevronDown,
	ChevronUp,
	CircleArrowUp,
	FastForward,
	Loader2,
	Pause,
	Play,
} from "lucide-react";
import { useTranslations } from "next-intl";
import { StoryState } from "../constants";

interface PlaybackControlsProps {
	isPlaying: boolean;
	currentPointIndex: number;
	totalPoints: number;
	storyState: StoryState;
	isAnimating?: boolean;
	onPlayPause: () => void;
	onNext: () => void;
	onPrev: () => void;
	onSliderChange: (value: number[]) => void;
	onReset: () => void;
	onSkipToEnd?: () => void;
}

export function PlaybackControls({
	isPlaying,
	currentPointIndex,
	totalPoints,
	storyState,
	isAnimating = false,
	onPlayPause,
	onNext,
	onPrev,
	onSliderChange,
	onReset,
	onSkipToEnd,
}: PlaybackControlsProps) {
	const t = useTranslations("travelMemo.playbackControls");

	const isOverviewMode = storyState === StoryState.OVERVIEW;
	const isCompletedMode = storyState === StoryState.COMPLETED;

	// 计算进度百分比
	const progress = isCompletedMode
		? 100
		: totalPoints > 0
			? ((currentPointIndex + (isOverviewMode ? 0 : 1)) / totalPoints) *
				100
			: 0;

	// 判断按钮状态
	const playButtonDisabled = isAnimating;
	const prevButtonDisabled =
		isAnimating ||
		currentPointIndex <= (isOverviewMode ? 0 : 1) ||
		isCompletedMode;
	const nextButtonDisabled =
		isAnimating ||
		currentPointIndex >= totalPoints - (isOverviewMode ? 0 : 0) ||
		isCompletedMode;
	const sliderDisabled = isAnimating || isCompletedMode;

	// 根据当前状态获取播放按钮内容
	const getPlayButtonContent = () => {
		if (isAnimating) {
			return <Loader2 className="h-4 w-4 animate-spin" />;
		}

		if (isCompletedMode) {
			return (
				<svg
					xmlns="http://www.w3.org/2000/svg"
					viewBox="0 0 24 24"
					fill="none"
					stroke="currentColor"
					strokeWidth="2"
					strokeLinecap="round"
					strokeLinejoin="round"
					className="h-4 w-4"
					aria-label={t("svgLabelViewRecap")}
				>
					<title>{t("svgLabelViewRecap")}</title>
					<path d="M9 18l6-6-6-6" />
				</svg>
			);
		}

		return isPlaying ? (
			<Pause className="h-4 w-4" />
		) : (
			<Play className="h-4 w-4" />
		);
	};

	// 获取播放按钮提示文本
	const getPlayButtonTooltip = () => {
		if (isCompletedMode) return t("tooltipViewRecap");
		if (isAnimating) return "加载中";
		return isPlaying ? t("tooltipPause") : t("tooltipPlay");
	};

	// 获取进度条状态类名
	const getProgressBarClasses = () => {
		if (isCompletedMode) return "bg-green-500";
		if (isAnimating) return "bg-blue-300 pulse-opacity";
		return "bg-blue-400";
	};

	return (
		<div className="absolute left-4 top-1/2 transform -translate-y-1/2 h-auto z-20">
			<div
				className={cn(
					"bg-slate-800/80 backdrop-blur-sm rounded-lg shadow-lg p-3 flex flex-col items-center space-y-3 border border-slate-700/50 text-slate-100 w-14",
					isCompletedMode && "border-green-500/50",
				)}
			>
				{/* 回到开始按钮 */}
				<Button
					variant="ghost"
					size="sm"
					onClick={onReset}
					title={t("tooltipBackToStart")}
					className="h-7 w-7 p-0 text-blue-400 hover:text-blue-300 hover:bg-slate-700/50 inline-flex items-center justify-center"
					disabled={isAnimating}
				>
					<span className="flex items-center justify-center">
						<CircleArrowUp className="h-4 w-4" />
					</span>
				</Button>

				{/* 上一个点位按钮 */}
				<Button
					variant="ghost"
					size="sm"
					onClick={onPrev}
					disabled={prevButtonDisabled}
					title={t("tooltipPreviousPoint")}
					className={cn(
						"h-7 w-7 p-0 inline-flex items-center justify-center",
						prevButtonDisabled && "opacity-50",
					)}
				>
					<span className="flex items-center justify-center">
						<ChevronUp className="h-4 w-4" />
					</span>
				</Button>

				{/* 播放/暂停按钮 */}
				<Button
					variant={
						isOverviewMode
							? "primary"
							: isCompletedMode
								? "outline"
								: "ghost"
					}
					size="sm"
					onClick={onPlayPause}
					disabled={playButtonDisabled}
					title={getPlayButtonTooltip()}
					className={cn(
						"h-7 w-7 p-0 transition-colors duration-300 inline-flex items-center justify-center",
						playButtonDisabled && "opacity-70",
					)}
				>
					<span className="flex items-center justify-center">
						{getPlayButtonContent()}
					</span>
				</Button>

				{/* 下一个点位按钮 */}
				<Button
					variant="ghost"
					size="sm"
					onClick={onNext}
					disabled={nextButtonDisabled}
					title={
						isOverviewMode
							? t("tooltipStartJourney")
							: t("tooltipNextPoint")
					}
					className={cn(
						"h-7 w-7 p-0 inline-flex items-center justify-center",
						nextButtonDisabled && "opacity-50",
					)}
				>
					<span className="flex items-center justify-center">
						<ChevronDown className="h-4 w-4" />
					</span>
				</Button>

				{/* 跳转到回顾页面按钮 */}
				{onSkipToEnd && !isCompletedMode && (
					<Button
						variant="ghost"
						size="sm"
						onClick={onSkipToEnd}
						disabled={isAnimating}
						title={t("tooltipSkipToRecap")}
						className={cn(
							"h-7 w-7 p-0 text-blue-400 hover:text-blue-300 hover:bg-slate-700/50 inline-flex items-center justify-center",
							isAnimating && "opacity-50",
						)}
					>
						<span className="flex items-center justify-center">
							<FastForward className="h-4 w-4" />
						</span>
					</Button>
				)}

				{/* 进度条 */}
				<div className="h-40 flex items-center justify-center w-full relative">
					{/* 进度条背景 */}
					<div className="absolute h-36 w-[2px] bg-slate-600 rounded-full" />

					{/* 进度条前景 */}
					<div
						className={cn(
							"absolute top-0 w-[4px] rounded-full transition-all duration-300 ease-in-out shadow-glow",
							getProgressBarClasses(),
						)}
						style={{
							height: `${progress}%`,
							maxHeight: "144px",
						}}
					/>

					{/* 交互式进度滑块 */}
					<Slider
						className="h-36 w-6 opacity-0 absolute cursor-pointer"
						value={[
							isCompletedMode ? totalPoints : currentPointIndex,
						]}
						min={0}
						max={totalPoints}
						step={1}
						onValueChange={
							sliderDisabled ? undefined : onSliderChange
						}
						orientation="vertical"
						aria-label={t("sliderLabelProgress")}
						dir="ltr"
						disabled={sliderDisabled}
					/>
				</div>

				{/* 状态文本 */}
				<div className="text-xs text-slate-300 text-center font-medium">
					{isCompletedMode ? (
						<span className="text-green-400">
							{t("statusCompleted")}
						</span>
					) : isOverviewMode ? (
						t("statusOverview")
					) : isAnimating ? (
						<span className="text-blue-300 animate-pulse">
							动画中
						</span>
					) : (
						`${currentPointIndex} / ${totalPoints}`
					)}
				</div>
			</div>
		</div>
	);
}
