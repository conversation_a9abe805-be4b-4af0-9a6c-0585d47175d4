"use client";

import type { FrontendTravelDiary } from "@repo/database/src/types/travel-diary";
import { cn } from "@ui/lib/utils";
import { motion } from "framer-motion";
import Image from "next/image";
import { useEffect, useState } from "react";

interface OpeningSectionProps {
	diary: FrontendTravelDiary;
	onStartExploring: () => void;
}

export function OpeningSection({
	diary,
	onStartExploring,
}: OpeningSectionProps) {
	const [isImageLoaded, setIsImageLoaded] = useState(false);
	const [scale, setScale] = useState(1);

	// 创建缓慢的Ken Burns效果
	useEffect(() => {
		if (!isImageLoaded) return;

		const interval = setInterval(() => {
			setScale((prevScale) => (prevScale === 1 ? 1.05 : 1));
		}, 10000); // 每10秒切换缩放

		return () => clearInterval(interval);
	}, [isImageLoaded]);

	// 监听滚动事件
	useEffect(() => {
		const handleScroll = (e: WheelEvent) => {
			// 当用户向下滚动时，触发开始探索
			if (e.deltaY > 0) {
				onStartExploring();
			}
		};

		// 监听触摸滑动事件
		let touchStartY = 0;
		const handleTouchStart = (e: TouchEvent) => {
			touchStartY = e.touches[0].clientY;
		};

		const handleTouchMove = (e: TouchEvent) => {
			const touchEndY = e.touches[0].clientY;
			// 如果向上滑动距离超过50px，触发开始探索
			if (touchStartY - touchEndY > 50) {
				onStartExploring();
			}
		};

		// 添加事件监听器
		window.addEventListener("wheel", handleScroll);
		window.addEventListener("touchstart", handleTouchStart);
		window.addEventListener("touchmove", handleTouchMove);

		// 清理事件监听器
		return () => {
			window.removeEventListener("wheel", handleScroll);
			window.removeEventListener("touchstart", handleTouchStart);
			window.removeEventListener("touchmove", handleTouchMove);
		};
	}, [onStartExploring]);

	const totalFootprints = diary.timelines.reduce(
		(acc: number, timeline) => acc + timeline.points.length,
		0,
	);

	return (
		<motion.div
			initial={{ opacity: 0 }}
			animate={{ opacity: 1 }}
			exit={{ opacity: 0 }}
			transition={{ duration: 0.8 }}
			className="absolute inset-0 w-full h-full flex flex-col items-center justify-center overflow-hidden"
		>
			{/* 背景图片 */}
			<div className="absolute inset-0 w-full h-full overflow-hidden">
				{diary.coverImage && (
					<div
						className={cn(
							"relative w-full h-full transform transition-transform duration-[20s]",
							"ease-linear",
						)}
						style={{ transform: `scale(${scale})` }}
					>
						<Image
							src={diary.coverImage}
							alt={diary.title}
							fill
							priority
							quality={90}
							className="object-cover"
							onLoad={() => setIsImageLoaded(true)}
						/>
						<div className="absolute inset-0 bg-black/60" />
					</div>
				)}
			</div>

			{/* 内容叠加层 */}
			<div className="relative z-10 max-w-4xl px-4 text-center">
				<motion.h1
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.8, delay: 0.3 }}
					className="text-4xl md:text-6xl font-bold mb-6 text-white tracking-tight"
				>
					{diary.title}
				</motion.h1>

				<motion.h2
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.8, delay: 0.6 }}
					className="text-xl md:text-2xl text-white/80 mb-12 max-w-2xl mx-auto"
				>
					{diary.subtitle}
				</motion.h2>

				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.8, delay: 0.9 }}
					className="text-white/70 text-sm md:text-base mb-16"
				>
					<p>旅行天数: {diary.timelines.length} 天</p>
					<p>足迹数量: {totalFootprints}</p>
				</motion.div>

				<motion.button
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.8, delay: 1.2 }}
					onClick={onStartExploring}
					className="px-8 py-3 bg-primary text-white rounded-full text-lg font-medium hover:bg-primary/90 transition-colors"
				>
					开始旅程
				</motion.button>
			</div>

			{/* 向下滚动提示 - 添加动画效果 */}
			<motion.div
				initial={{ opacity: 0, y: 0 }}
				animate={{
					opacity: 1,
					y: [0, 10, 0],
				}}
				transition={{
					duration: 1.5,
					delay: 1.8,
					repeat: Number.POSITIVE_INFINITY,
					repeatType: "loop",
				}}
				className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center cursor-pointer"
				onClick={onStartExploring}
			>
				<p className="text-white/70 mb-2">向下滚动探索</p>
				<svg
					xmlns="http://www.w3.org/2000/svg"
					width="24"
					height="24"
					viewBox="0 0 24 24"
					fill="none"
					stroke="currentColor"
					strokeWidth="2"
					strokeLinecap="round"
					strokeLinejoin="round"
					className="text-white/70"
				>
					<path d="M12 5v14" />
					<path d="m19 12-7 7-7-7" />
				</svg>
			</motion.div>
		</motion.div>
	);
}
