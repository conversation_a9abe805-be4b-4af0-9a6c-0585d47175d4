import type { Metadata } from "next";
import { setRequestLocale } from "next-intl/server";
import Script from "next/script";
import { TravelFootprintTool } from "../travel-stat/TravelFootprintTool";

// SEO metadata with localization support for EN/US market
const createLocalizedMetadata = (locale?: string): Metadata => {
	const isEnglish = locale === "en";

	// 优化后的标题 - 融合品牌与功能关键词
	const baseTitle = isEnglish
		? "Map Moment - Interactive Travel Footprint Mapping Tool | Record Your Journey"
		: "Map Moment - 旅行足迹记录工具 | 在互动地图上记录你的旅行足迹";

	// 优化后的描述 - 包含更多目标关键词
	const baseDescription = isEnglish
		? "Professional travel footprint mapping tool by Map Moment. Mark visited places on interactive world maps, track countries and cities statistics, export travel data. Free online travel tracking with multiple map projections and beautiful visualizations."
		: "Map Moment 专业的旅行足迹记录工具。在互动世界地图上标记你去过的地方，支持多种地图投影，统计访问的城市和国家数量，导出和导入你的旅行数据。免费在线旅行足迹工具。";

	// 扩展关键词 - 融合GSC指南建议
	const keywords = isEnglish
		? "travel footprint,footprint mapping,travel tracking tool,interactive travel map,journey tracking,world map markers,travel statistics,visited countries tracker,travel data export,online travel tool,map moment,travel visualization,footprint recorder"
		: "旅行足迹,足迹记录,地图标记,旅行统计,世界地图,旅行工具,地图投影,互动地图,旅行日记,旅行地图,世界足迹,旅行记录,地图工具,在线旅行足迹记录,免费旅行地图工具,旅行轨迹可视化,个人旅行统计分析,足迹标记工具,世界地图标记,Map Moment";

	// 优化OG描述 - 更突出功能特点
	const ogDescription = isEnglish
		? "Map Moment - Professional travel footprint mapping tool. Track your journeys on beautiful interactive maps, visualize travel statistics, and create stunning travel footprint records. Free online tool with advanced mapping features."
		: "Map Moment - 专业旅行足迹记录工具。在精美互动地图上追踪你的旅程，可视化旅行统计数据，创建精美的旅行足迹记录。免费在线工具，具备先进的地图功能。";

	return {
		title: baseTitle,
		description: baseDescription,
		keywords,
		openGraph: {
			title: baseTitle,
			description: ogDescription,
			type: "website",
			url: "https://mapmoment.app",
			siteName: "Map Moment",
			images: [
				{
					url: "/images/map-moment-travel-footprint-preview.jpg",
					width: 1200,
					height: 630,
					alt: isEnglish
						? "Map Moment - Travel Footprint Mapping Interface"
						: "Map Moment - 旅行足迹记录工具界面预览",
				},
			],
			locale: isEnglish ? "en_US" : "zh_CN",
		},
		twitter: {
			card: "summary_large_image",
			title: baseTitle,
			description: ogDescription,
			images: ["/images/map-moment-travel-footprint-preview.jpg"],
			creator: "@mapmoment",
		},
		robots: {
			index: true,
			follow: true,
			googleBot: {
				index: true,
				follow: true,
				"max-video-preview": -1,
				"max-image-preview": "large",
				"max-snippet": -1,
			},
		},
		verification: {
			google: "your-google-verification-code",
			other: {
				baidu: "your-baidu-verification-code",
			},
		},
		alternates: {
			canonical: `https://mapmoment.app${isEnglish ? "/en" : ""}`,
			languages: {
				"zh-CN": "https://mapmoment.app/zh",
				"en-US": "https://mapmoment.app/en",
			},
		},
		// 增强的meta标签 - 更好的SEO定位
		category: isEnglish ? "Travel Tracking Tools" : "旅行足迹工具",
		classification: isEnglish
			? "Travel Footprint Mapping & Statistics"
			: "旅行足迹记录与统计",
		other: {
			"apple-mobile-web-app-capable": "yes",
			"apple-mobile-web-app-status-bar-style": "default",
			"format-detection": "telephone=no",
			"msapplication-TileColor": "#2563eb",
			"theme-color": "#2563eb",
			// 添加地理标记
			"geo.region": isEnglish ? "US" : "CN",
			"geo.placename": isEnglish ? "Global" : "全球",
		},
	};
};

// Export dynamic metadata generation
export async function generateMetadata({
	params,
}: {
	params: Promise<{ locale: string }>;
}): Promise<Metadata> {
	const { locale } = await params;
	return createLocalizedMetadata(locale);
}

export default async function Home({
	params,
}: {
	params: Promise<{ locale: string }>;
}) {
	const { locale } = await params;
	setRequestLocale(locale);

	const isEnglish = locale === "en";

	// Structured data (JSON-LD) with localization
	const structuredData = {
		"@context": "https://schema.org",
		"@graph": [
			{
				"@type": "WebApplication",
				name: isEnglish
					? "Map Moment Travel Footprint Tool"
					: "Map Moment 旅行足迹记录工具",
				alternateName: isEnglish
					? "Travel Footprint Mapper"
					: "旅行足迹标记工具",
				description: isEnglish
					? "Map Moment is a professional travel footprint mapping tool that helps you track and visualize your journeys on interactive world maps. Mark visited places, analyze travel statistics, export data, and create beautiful travel footprint records with multiple map projections and themes."
					: "Map Moment 是一款专业的旅行足迹记录工具，帮助你在互动世界地图上追踪和可视化你的旅程。标记访问过的地方，分析旅行统计数据，导出数据，并创建精美的旅行足迹记录，支持多种地图投影和主题。",
				url: "https://mapmoment.app",
				applicationCategory: "TravelApplication",
				applicationSubCategory: isEnglish
					? "Travel Tracking"
					: "旅行追踪",
				operatingSystem: "Web Browser",
				browserRequirements: "Requires JavaScript. Requires HTML5.",
				softwareVersion: "2.0",
				offers: {
					"@type": "Offer",
					price: "0",
					priceCurrency: isEnglish ? "USD" : "CNY",
					availability: "https://schema.org/InStock",
					priceValidUntil: "2025-12-31",
				},
				featureList: isEnglish
					? [
							"Interactive World Map with Multiple Projections",
							"Travel Footprint Tracking & Visualization",
							"Countries & Cities Statistics",
							"Travel Data Export & Import",
							"Beautiful Map Themes & Styles",
							"Travel Footprint Analytics",
							"Social Media Card Generation",
							"Free Online Tool",
						]
					: [
							"多投影互动世界地图",
							"旅行足迹追踪与可视化",
							"国家与城市统计",
							"旅行数据导出与导入",
							"精美地图主题与样式",
							"旅行足迹分析",
							"社交媒体卡片生成",
							"免费在线工具",
						],
				audience: {
					"@type": "Audience",
					audienceType: isEnglish
						? "Travel Enthusiasts and Digital Nomads"
						: "旅行爱好者和数字游民",
				},
				creator: {
					"@type": "Organization",
					name: "Map Moment",
				},
				keywords: isEnglish
					? "travel footprint, footprint mapping, travel tracking, world map, travel statistics, journey visualization"
					: "旅行足迹, 足迹记录, 旅行追踪, 世界地图, 旅行统计, 旅程可视化",
				inLanguage: isEnglish ? "en-US" : "zh-CN",
				availableLanguage: ["en-US", "zh-CN"],
				screenshot:
					"https://mapmoment.app/images/map-moment-travel-footprint-preview.jpg",
			},
			{
				"@type": "Organization",
				name: "Map Moment",
				url: "https://mapmoment.app",
				logo: "https://mapmoment.app/logo.png",
				sameAs: ["https://twitter.com/mapmoment"],
				description: isEnglish
					? "Innovative travel technology company providing professional tools for travel footprint tracking and journey visualization on interactive world maps."
					: "创新的旅行科技公司，提供专业的旅行足迹追踪工具和互动世界地图上的旅程可视化服务。",
				foundingDate: "2024",
				areaServed: "Worldwide",
				serviceType: isEnglish ? "Travel Technology" : "旅行科技",
			},
			{
				"@type": "WebPage",
				"@id": "https://mapmoment.app",
				url: "https://mapmoment.app",
				name: isEnglish
					? "Map Moment - Travel Footprint Mapping Tool"
					: "Map Moment - 旅行足迹记录工具",
				description: isEnglish
					? "Track and visualize your travel footprints on beautiful interactive world maps with Map Moment's professional mapping tool"
					: "使用 Map Moment 专业地图工具在精美的互动世界地图上追踪和可视化你的旅行足迹",
				inLanguage: isEnglish ? "en-US" : "zh-CN",
				isPartOf: {
					"@type": "WebSite",
					"@id": "https://mapmoment.app",
					url: "https://mapmoment.app",
					name: "Map Moment",
					potentialAction: {
						"@type": "SearchAction",
						target: "https://mapmoment.app/search?q={search_term_string}",
						"query-input": "required name=search_term_string",
					},
				},
				datePublished: "2024-01-01",
				dateModified: new Date().toISOString().split("T")[0],
				breadcrumb: {
					"@type": "BreadcrumbList",
					itemListElement: [
						{
							"@type": "ListItem",
							position: 1,
							name: isEnglish ? "Home" : "首页",
							item: "https://mapmoment.app",
						},
						{
							"@type": "ListItem",
							position: 2,
							name: isEnglish
								? "Travel Footprint Tool"
								: "旅行足迹工具",
							item: "https://mapmoment.app",
						},
					],
				},
			},
			// 添加FAQ结构化数据
			{
				"@type": "FAQPage",
				mainEntity: isEnglish
					? [
							{
								"@type": "Question",
								name: "What is Map Moment Travel Footprint Tool?",
								acceptedAnswer: {
									"@type": "Answer",
									text: "Map Moment is a free online travel footprint mapping tool that helps you track and visualize your journeys on interactive world maps. You can mark visited places, view travel statistics, and export your travel data.",
								},
							},
							{
								"@type": "Question",
								name: "Is Map Moment free to use?",
								acceptedAnswer: {
									"@type": "Answer",
									text: "Yes, Map Moment is completely free to use. You can track unlimited travel footprints, access all map projections, and export your data without any cost.",
								},
							},
							{
								"@type": "Question",
								name: "Can I export my travel footprint data?",
								acceptedAnswer: {
									"@type": "Answer",
									text: "Yes, you can export your travel footprint data in JSON format, which includes all your marked locations, visit statistics, and travel timeline information.",
								},
							},
						]
					: [
							{
								"@type": "Question",
								name: "什么是 Map Moment 旅行足迹记录工具？",
								acceptedAnswer: {
									"@type": "Answer",
									text: "Map Moment 是一款免费的在线旅行足迹记录工具，帮助你在互动世界地图上追踪和可视化你的旅程。你可以标记访问过的地方，查看旅行统计数据，并导出你的旅行数据。",
								},
							},
							{
								"@type": "Question",
								name: "Map Moment 免费使用吗？",
								acceptedAnswer: {
									"@type": "Answer",
									text: "是的，Map Moment 完全免费使用。你可以追踪无限的旅行足迹，访问所有地图投影，并免费导出你的数据。",
								},
							},
							{
								"@type": "Question",
								name: "可以导出我的旅行足迹数据吗？",
								acceptedAnswer: {
									"@type": "Answer",
									text: "可以，你可以将旅行足迹数据导出为 JSON 格式，包括所有标记的位置、访问统计和旅行时间线信息。",
								},
							},
						],
			},
		],
	};

	return (
		<>
			{/* Structured data */}
			<Script
				id="structured-data"
				type="application/ld+json"
				strategy="beforeInteractive"
			>
				{JSON.stringify(structuredData)}
			</Script>

			{/* Additional SEO scripts for international markets */}
			{isEnglish && (
				<>
					{/* Hreflang tags for international SEO */}
					<link
						rel="alternate"
						hrefLang="en"
						href="https://mapmoment.app/en"
					/>
					<link
						rel="alternate"
						hrefLang="zh"
						href="https://mapmoment.app/zh"
					/>
					<link
						rel="alternate"
						hrefLang="x-default"
						href="https://mapmoment.app/en"
					/>
				</>
			)}

			{/* Main content */}
			<TravelFootprintTool />
		</>
	);
}
