"use client";

import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from "@ui/components/alert-dialog";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Download, Settings, Trash2, Upload } from "lucide-react";

import { useTravelStatTranslations } from "../../hooks/useTravelStatTranslations";

interface OperationPanelProps {
	onExport: () => void;
	onImport: () => void;
	onClear: () => void;
	onCardExport: () => void;
	hasData: boolean;
}

export function OperationPanel({
	onExport,
	onImport,
	onClear,
	onCardExport,
	hasData,
}: OperationPanelProps) {
	const travelStatT = useTravelStatTranslations();

	return (
		<Card className="bg-white/90 backdrop-blur-sm border-sky-200">
			<CardHeader className="px-3 py-2 pb-1">
				<CardTitle className="text-sm font-medium flex items-center gap-2">
					<Settings className="w-4 h-4 text-sky-500" />
					{travelStatT.operationPanel.title()}
				</CardTitle>
			</CardHeader>
			<CardContent className="px-3 pb-3 pt-3 space-y-3">
				{/* 导入数据 */}
				<Button
					onClick={onImport}
					variant="outline"
					size="sm"
					className="w-full justify-start"
				>
					<Upload className="w-4 h-4 mr-2" />
					{travelStatT.operationPanel.importData()}
				</Button>

				{/* 导出数据 */}
				<Button
					onClick={onExport}
					variant="outline"
					size="sm"
					className="w-full justify-start"
					disabled={!hasData}
				>
					<Download className="w-4 h-4 mr-2" />
					{travelStatT.operationPanel.exportData()}
				</Button>

				{/* 生成卡片功能已移至页面模式切换 */}

				{/* 清空数据 - 带确认对话框 */}
				<AlertDialog>
					<AlertDialogTrigger asChild>
						<Button
							variant="error"
							size="sm"
							className="w-full justify-start"
							disabled={!hasData}
						>
							<Trash2 className="w-4 h-4 mr-2" />
							{travelStatT.operationPanel.clearData()}
						</Button>
					</AlertDialogTrigger>
					<AlertDialogContent>
						<AlertDialogHeader>
							<AlertDialogTitle className="flex items-center gap-2 text-red-600">
								<Trash2 className="w-5 h-5" />
								{travelStatT.operationPanel.confirmClear.title()}
							</AlertDialogTitle>
							<AlertDialogDescription asChild>
								<div className="text-gray-600">
									<p>
										{travelStatT.operationPanel.confirmClear.description()}
									</p>
									<p className="mt-3">
										{travelStatT.operationPanel.confirmClear.warningText()}
									</p>
									<ul className="mt-2 ml-4 list-disc space-y-1">
										<li>
											{travelStatT.operationPanel.confirmClear.items.travelPoints()}
										</li>
										<li>
											{travelStatT.operationPanel.confirmClear.items.countryStats()}
										</li>
										<li>
											{travelStatT.operationPanel.confirmClear.items.mapData()}
										</li>
									</ul>
									<p className="mt-3">
										{travelStatT.operationPanel.confirmClear.confirmText()}
									</p>
								</div>
							</AlertDialogDescription>
						</AlertDialogHeader>
						<AlertDialogFooter>
							<AlertDialogCancel>
								{travelStatT.operationPanel.confirmClear.cancelButton()}
							</AlertDialogCancel>
							<AlertDialogAction
								onClick={onClear}
								className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
							>
								{travelStatT.operationPanel.confirmClear.confirmButton()}
							</AlertDialogAction>
						</AlertDialogFooter>
					</AlertDialogContent>
				</AlertDialog>
			</CardContent>
		</Card>
	);
}
