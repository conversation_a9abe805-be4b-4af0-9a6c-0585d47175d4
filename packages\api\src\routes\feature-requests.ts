import { zValidator } from "@hono/zod-validator";
import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";

// 投票相关的验证schema
const voteSchema = z.object({
	anonymousId: z.string().optional(),
	voterName: z.string().optional(),
	voterEmail: z.string().email().optional(),
});

// 提交特性请求的验证schema
const submitFeatureRequestSchema = z.object({
	title: z.string().min(1, "标题不能为空").max(200, "标题不能超过200个字符"),
	description: z.string().min(1, "描述不能为空"),
	productId: z.string().min(1, "必须选择产品"),
	// 匿名用户信息
	authorName: z.string().optional(),
	authorEmail: z.string().email().optional(),
	anonymousId: z.string().optional(),
});

// 查询参数验证
const querySchema = z.object({
	productId: z.string().optional(),
	status: z.string().optional(),
	showVoteCounts: z
		.string()
		.optional()
		.transform((val) => val === "true"),
	sortBy: z.enum(["createdAt", "voteCount", "title"]).default("createdAt"),
	sortOrder: z.enum(["asc", "desc"]).default("desc"),
	page: z
		.string()
		.optional()
		.transform((val) => (val ? Number.parseInt(val, 10) : 1)),
	limit: z
		.string()
		.optional()
		.transform((val) => (val ? Number.parseInt(val, 10) : 20)),
});

// 获取客户端IP地址的辅助函数
function getClientIP(c: any): string {
	return (
		c.req.header("x-forwarded-for") ||
		c.req.header("x-real-ip") ||
		c.req.header("cf-connecting-ip") ||
		"unknown"
	);
}

// 获取用户代理的辅助函数
function getUserAgent(c: any): string {
	return c.req.header("user-agent") || "unknown";
}

export const featureRequestRouter = new Hono()
	// 获取特性请求列表（公开接口）
	.get("/feature-requests", zValidator("query", querySchema), async (c) => {
		try {
			const {
				productId,
				status,
				showVoteCounts,
				sortBy,
				sortOrder,
				page,
				limit,
			} = c.req.valid("query");
			const offset = (page - 1) * limit;

			// 构建查询条件
			const where: any = {};

			if (productId) {
				where.productId = productId;
			}

			if (status) {
				where.status = status;
			}

			// 构建排序条件
			const orderBy: any = {};
			orderBy[sortBy] = sortOrder;

			// 获取用户信息（如果已登录）
			const userId = null; // TODO: 根据实际认证系统获取
			const anonymousId = c.req.header("x-anonymous-id") || null;

			// 获取特性请求列表
			const featureRequests = await db.featureRequest.findMany({
				where,
				skip: offset,
				take: limit,
				orderBy,
				include: {
					product: {
						select: {
							id: true,
							name: true,
						},
					},
					user: {
						select: {
							id: true,
							name: true,
						},
					},
					votes:
						userId || anonymousId
							? {
									where: userId
										? { userId }
										: { anonymousId },
									select: {
										id: true,
									},
								}
							: false,
					_count: showVoteCounts
						? {
								select: {
									votes: true,
									comments: true,
								},
							}
						: false,
				},
			});

			// 格式化返回数据
			const formattedData = featureRequests.map((fr) => ({
				id: fr.id,
				title: fr.title,
				description: fr.description,
				status: fr.status,
				voteCount: showVoteCounts ? fr.voteCount : undefined,
				hasVoted: fr.votes && fr.votes.length > 0,
				product: fr.product,
				author: fr.user
					? {
							name: fr.user.name,
						}
					: {
							name: fr.authorName || "匿名用户",
						},
				createdAt: fr.createdAt,
				updatedAt: fr.updatedAt,
				commentCount: showVoteCounts ? fr._count?.comments : undefined,
			}));

			return c.json({
				success: true,
				data: formattedData,
			});
		} catch (error) {
			console.error("获取特性请求列表失败:", error);
			return c.json(
				{
					success: false,
					error: "获取特性请求列表失败",
				},
				500,
			);
		}
	})

	// 投票支持特性
	.post(
		"/feature-requests/:id/vote",
		zValidator("json", voteSchema),
		async (c) => {
			try {
				const id = c.req.param("id");
				const data = c.req.valid("json");
				const userId = null; // TODO: 获取当前用户ID
				const ip = getClientIP(c);
				const userAgent = getUserAgent(c);

				// 检查特性请求是否存在
				const featureRequest = await db.featureRequest.findUnique({
					where: { id },
				});

				if (!featureRequest) {
					return c.json(
						{
							success: false,
							error: "特性请求不存在",
						},
						404,
					);
				}

				// 准备投票数据
				const voteData: any = {
					featureRequestId: id,
					ipAddress: ip,
					userAgent: userAgent,
				};

				if (userId) {
					voteData.userId = userId;
				} else {
					// 匿名用户
					const anonymousId = data.anonymousId;
					if (!anonymousId) {
						return c.json(
							{
								success: false,
								error: "匿名用户必须提供anonymousId",
							},
							400,
						);
					}
					voteData.anonymousId = anonymousId;
					voteData.voterName = data.voterName;
					voteData.voterEmail = data.voterEmail;
				}

				// 检查是否已经投票
				const existingVote = await db.featureVote.findFirst({
					where: userId
						? { featureRequestId: id, userId }
						: {
								featureRequestId: id,
								anonymousId: voteData.anonymousId,
							},
				});

				if (existingVote) {
					return c.json(
						{
							success: false,
							error: "您已经为此特性投过票了",
						},
						400,
					);
				}

				// 创建投票记录并更新投票计数
				await db.$transaction(async (tx) => {
					await tx.featureVote.create({
						data: voteData,
					});

					await tx.featureRequest.update({
						where: { id },
						data: {
							voteCount: {
								increment: 1,
							},
						},
					});
				});

				return c.json({
					success: true,
					message: "投票成功",
				});
			} catch (error) {
				console.error("投票失败:", error);
				return c.json(
					{
						success: false,
						error: "投票失败",
					},
					500,
				);
			}
		},
	)

	// 取消投票
	.delete("/feature-requests/:id/vote", async (c) => {
		try {
			const id = c.req.param("id");
			const userId = null; // TODO: 获取当前用户ID
			const anonymousId = c.req.header("x-anonymous-id") || null;

			if (!userId && !anonymousId) {
				return c.json(
					{
						success: false,
						error: "无法识别用户身份",
					},
					400,
				);
			}

			// 查找现有投票
			const existingVote = await db.featureVote.findFirst({
				where: userId
					? { featureRequestId: id, userId }
					: { featureRequestId: id, anonymousId },
			});

			if (!existingVote) {
				return c.json(
					{
						success: false,
						error: "您还没有为此特性投票",
					},
					400,
				);
			}

			// 删除投票记录并更新投票计数
			await db.$transaction(async (tx) => {
				await tx.featureVote.delete({
					where: { id: existingVote.id },
				});

				await tx.featureRequest.update({
					where: { id },
					data: {
						voteCount: {
							decrement: 1,
						},
					},
				});
			});

			return c.json({
				success: true,
				message: "取消投票成功",
			});
		} catch (error) {
			console.error("取消投票失败:", error);
			return c.json(
				{
					success: false,
					error: "取消投票失败",
				},
				500,
			);
		}
	})

	// 获取产品列表（用于前端选择）
	.get("/products", async (c) => {
		try {
			const products = await db.product.findMany({
				select: {
					id: true,
					name: true,
					description: true,
					_count: {
						select: {
							featureRequests: true,
						},
					},
				},
				orderBy: { name: "asc" },
			});

			return c.json({
				success: true,
				data: products,
			});
		} catch (error) {
			console.error("获取产品列表失败:", error);
			return c.json(
				{
					success: false,
					error: "获取产品列表失败",
				},
				500,
			);
		}
	});
