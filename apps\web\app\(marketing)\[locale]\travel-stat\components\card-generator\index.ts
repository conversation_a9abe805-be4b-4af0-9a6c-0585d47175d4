// 主要组件导出
export { CardGenerator } from "./CardGenerator";
export { CardPreviewArea } from "./CardPreviewArea";
export { TemplateSelector } from "./TemplateSelector";

// 模板导出
export { cardTemplates } from "./templates";
export { MinimalCard } from "./templates/MinimalCard";

// 类型导出
export type {
	CardData,
	CardCustomization,
	CardProps,
	CardTemplate,
	CardExportOptions,
	CardExportResult,
	TravelStats,
	UserProfile,
	MapImageData,
	SocialPlatform,
	SocialPlatformConfig,
	ExportQuality,
	CustomizationOption,
} from "./types/cardTypes";

// 工具函数导出
export {
	exportCard,
	copyToClipboard,
	shareToSocialMedia,
	getSupportedQualities,
	getPlatformInfo,
	checkBrowserSupport,
} from "./utils/cardExportUtils";
