"use client";

import { useCallback, useEffect, useRef, useState } from "react";
import MapGL from "react-map-gl";
import "mapbox-gl/dist/mapbox-gl.css";

// Mapbox Access Token - 需要设置环境变量
// 获取你自己的 token: https://account.mapbox.com/access-tokens/
// 在 .env.local 中设置: NEXT_PUBLIC_MAPBOX_TOKEN=你的token
const MAPBOX_TOKEN = process.env.NEXT_PUBLIC_MAPBOX_TOKEN;

// 地图样式URLs - 参考 TravelMapMapbox
const MAP_STYLES = {
	day: "mapbox://styles/mapbox/streets-v11", // 日间模式 - 标准地图样式
	night: "mapbox://styles/mapbox/dark-v11", // 夜间模式 - 深色样式
};

// 默认地图中心坐标和缩放级别
const DEFAULT_CENTER = { lat: 20, lng: 0 };
const DEFAULT_ZOOM = 1.2;

// 缩放级别限制
const MIN_ZOOM = 0.5;
const MAX_ZOOM = 6; // 限制最大缩放，保持地球视角

// 点位数据类型定义
interface TravelPoint {
	id: number;
	lng: number;
	lat: number;
	intensity: number;
	delay: number;
}

// 随机生成的旅行点位数据
const generateRandomPoints = (count: number): TravelPoint[] => {
	const points: TravelPoint[] = [];
	for (let i = 0; i < count; i++) {
		points.push({
			id: i,
			lng: (Math.random() - 0.5) * 360,
			lat: (Math.random() - 0.5) * 160, // 限制纬度范围，避免极地
			intensity: Math.random(),
			delay: Math.random() * 3000,
		});
	}
	return points;
};

interface MapBackgroundProps {
	className?: string;
}

export function MapBackground({ className = "" }: MapBackgroundProps) {
	const mapRef = useRef<any>(null);
	const animationRef = useRef<number | undefined>(undefined);
	const [points] = useState<TravelPoint[]>(generateRandomPoints(120));
	const [mapLoaded, setMapLoaded] = useState(false);
	const [isNightMode, setIsNightMode] = useState(false);

	// 处理地图加载完成
	const handleLoad = useCallback(() => {
		setMapLoaded(true);

		if (!mapRef.current) return;

		const map = mapRef.current.getMap();

		// 设置 Globe projection
		map.setProjection("globe");

		// 设置统一的缩放限制
		map.setMinZoom(MIN_ZOOM);
		map.setMaxZoom(MAX_ZOOM);

		// 设置地球大气层效果
		map.setFog({
			color: isNightMode ? "rgb(5, 5, 15)" : "rgb(10, 30, 50)", // 深蓝色太空背景
			"high-color": isNightMode ? "rgb(15, 20, 40)" : "rgb(30, 50, 80)", // 高层大气颜色
			"horizon-blend": 0.05, // 地平线混合
			"space-color": isNightMode ? "rgb(2, 2, 8)" : "rgb(5, 10, 20)", // 太空颜色
			"star-intensity": 0.9, // 星星强度
		});

		// 添加旅行点位数据源
		map.addSource("travel-points", {
			type: "geojson",
			data: {
				type: "FeatureCollection",
				features: points.map((point) => ({
					type: "Feature",
					geometry: {
						type: "Point",
						coordinates: [point.lng, point.lat],
					},
					properties: {
						id: point.id,
						intensity: point.intensity,
						delay: point.delay,
					},
				})),
			},
		});

		// 添加外围光晕层 - 大光圈
		map.addLayer({
			id: "travel-points-outer-glow",
			type: "circle",
			source: "travel-points",
			paint: {
				"circle-radius": [
					"interpolate",
					["linear"],
					["zoom"],
					1,
					12,
					5,
					24,
				],
				"circle-color": isNightMode ? "#4fc3f7" : "#29b6f6",
				"circle-opacity": 0.4,
				"circle-blur": 1.5,
			},
		});

		// 添加中层光晕
		map.addLayer({
			id: "travel-points-inner-glow",
			type: "circle",
			source: "travel-points",
			paint: {
				"circle-radius": [
					"interpolate",
					["linear"],
					["zoom"],
					1,
					6,
					5,
					12,
				],
				"circle-color": isNightMode ? "#81d4fa" : "#4fc3f7",
				"circle-opacity": 0.8,
				"circle-blur": 0.8,
			},
		});

		// 添加主要点位层
		map.addLayer({
			id: "travel-points-core",
			type: "circle",
			source: "travel-points",
			paint: {
				"circle-radius": [
					"interpolate",
					["linear"],
					["zoom"],
					1,
					2,
					5,
					4,
				],
				"circle-color": "#ffffff",
				"circle-opacity": 1,
				"circle-stroke-width": 1,
				"circle-stroke-color": isNightMode ? "#4fc3f7" : "#29b6f6",
				"circle-stroke-opacity": 1,
			},
		});

		// 开始地球旋转动画
		const startRotation = () => {
			const rotateCamera = () => {
				if (!map) return;

				const currentBearing = map.getBearing();
				map.rotateTo(currentBearing + 0.08, {
					duration: 100,
					easing: (t: number) => t,
				});

				animationRef.current = requestAnimationFrame(rotateCamera);
			};

			rotateCamera();
		};

		// 延迟开始旋转，让地图先加载完成
		setTimeout(startRotation, 1500);

		// 点位闪烁动画
		const animatePoints = () => {
			points.forEach((point, index) => {
				setTimeout(() => {
					const animatePoint = () => {
						if (!map) return;

						const time = Date.now() * 0.001;
						const baseOpacity =
							0.4 + 0.6 * Math.sin(time * 2 + index * 0.5);
						const pulseOpacity =
							0.6 + 0.4 * Math.sin(time * 4 + index);

						try {
							map.setPaintProperty(
								"travel-points-outer-glow",
								"circle-opacity",
								[
									"case",
									["==", ["get", "id"], point.id],
									baseOpacity * 0.5,
									0.4,
								],
							);

							map.setPaintProperty(
								"travel-points-inner-glow",
								"circle-opacity",
								[
									"case",
									["==", ["get", "id"], point.id],
									pulseOpacity,
									0.8,
								],
							);
						} catch (error) {
							// 忽略可能的错误，防止动画中断
						}

						requestAnimationFrame(animatePoint);
					};

					animatePoint();
				}, point.delay);
			});
		};

		// 延迟开始点位动画
		setTimeout(animatePoints, 2500);
	}, [points, isNightMode]);

	useEffect(() => {
		return () => {
			if (animationRef.current) {
				cancelAnimationFrame(animationRef.current);
			}
		};
	}, []);

	return (
		<div className={`relative ${className}`}>
			{/* 星空背景层 */}
			<div className="absolute inset-0 bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900">
				<div className="stars" />
				<div className="twinkling" />
			</div>

			{/* Mapbox 地图容器 - 添加透明度和毛玻璃效果 */}
			<div className="absolute inset-0 opacity-60 backdrop-blur-sm">
				<MapGL
					ref={mapRef}
					mapboxAccessToken={MAPBOX_TOKEN}
					initialViewState={{
						latitude: DEFAULT_CENTER.lat,
						longitude: DEFAULT_CENTER.lng,
						zoom: DEFAULT_ZOOM,
						pitch: 0,
						bearing: 0,
					}}
					style={{ width: "100%", height: "100%" }}
					mapStyle={isNightMode ? MAP_STYLES.night : MAP_STYLES.day}
					onLoad={handleLoad}
					minZoom={MIN_ZOOM}
					maxZoom={MAX_ZOOM}
					interactive={false} // 禁用交互，保持背景效果
				>
					{/* 移除所有控件和按钮 */}
				</MapGL>
			</div>

			{/* 前景渐变遮罩 - 增强毛玻璃效果 */}
			<div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-background/80 backdrop-blur-md pointer-events-none" />

			{/* CSS 星空动画 */}
			<style jsx>{`
				.stars {
					background: transparent url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="0.3" fill="white" opacity="0.8"/><circle cx="80" cy="40" r="0.2" fill="white" opacity="0.6"/><circle cx="50" cy="70" r="0.25" fill="white" opacity="0.7"/><circle cx="90" cy="80" r="0.15" fill="white" opacity="0.5"/><circle cx="10" cy="90" r="0.2" fill="white" opacity="0.6"/><circle cx="70" cy="10" r="0.3" fill="white" opacity="0.8"/><circle cx="30" cy="50" r="0.15" fill="white" opacity="0.4"/><circle cx="60" cy="30" r="0.2" fill="white" opacity="0.7"/></svg>') repeat;
					background-size: 300px 300px;
					animation: move-stars 80s linear infinite;
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
				}
				
				.twinkling {
					background: transparent url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="25" cy="25" r="0.2" fill="white" opacity="0.9"/><circle cx="75" cy="75" r="0.15" fill="white" opacity="0.8"/><circle cx="15" cy="60" r="0.25" fill="white" opacity="0.7"/><circle cx="85" cy="35" r="0.15" fill="white" opacity="0.9"/></svg>') repeat;
					background-size: 400px 400px;
					animation: move-twinkle 120s linear infinite;
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					bottom: 0;
				}
				
				@keyframes move-stars {
					from { transform: translateX(0px); }
					to { transform: translateX(-300px); }
				}
				
				@keyframes move-twinkle {
					from { transform: translateX(0px); }
					to { transform: translateX(-400px); }
				}
			`}</style>

			{/* 隐藏 Mapbox 水印和归属信息的全局样式 */}
			<style jsx global>{`
				/* 隐藏 Mapbox logo */
				.mapboxgl-ctrl-logo {
					display: none !important;
				}
				
				/* 隐藏归属信息 */
				.mapboxgl-ctrl-attrib {
					display: none !important;
				}
				
				/* 隐藏归属信息按钮 */
				.mapboxgl-ctrl-attrib-button {
					display: none !important;
				}
				
				/* 隐藏归属信息内容 */
				.mapboxgl-ctrl-attrib-inner {
					display: none !important;
				}
				
				/* 确保所有相关的归属元素都被隐藏 */
				.mapboxgl-ctrl-bottom-left,
				.mapboxgl-ctrl-bottom-right {
					display: none !important;
				}
			`}</style>
		</div>
	);
}
