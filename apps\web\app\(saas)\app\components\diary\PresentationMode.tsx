"use client";

import { But<PERSON> } from "@ui/components/button";
import { Slider } from "@ui/components/slider";
import { cn } from "@ui/lib";
import { ChevronLeft, ChevronRight, Pause, Play, SkipBack } from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { toast } from "sonner";
import TravelMap from "../TravelMap";
import type { MapService } from "../map/types";
import type { TravelPoint } from "./travel-point-form/types";

// 演示模式配置接口
export interface PresentationConfig {
	// 点位动画设置
	pointAnimation: {
		type: "fade" | "bounce" | "drop" | "none"; // 动画类型
		duration: number; // 动画持续时间 (毫秒)
		delay: number; // 点位之间的延迟 (毫秒)
	};

	// 路线动画设置
	routeAnimation: {
		type: "progressive" | "instant" | "none"; // 动画类型
		duration: number; // 动画持续时间 (毫秒)
		color: string; // 路线颜色
		width: number; // 路线宽度
	};

	// 地图移动设置
	mapMovement: {
		type: "fly" | "instant" | "none"; // 移动类型
		duration: number; // 移动持续时间 (毫秒)
	};

	// 自动播放设置
	autoPlay: {
		enabled: boolean; // 是否启用自动播放
		delay: number; // 停留时间 (毫秒)
	};
}

// 组件属性接口
interface PresentationModeProps {
	points: TravelPoint[];
	onClose?: () => void;
	isStandalone?: boolean; // 是否作为独立页面使用
	className?: string;
	config?: Partial<PresentationConfig>;
}

// 默认配置
const defaultConfig: PresentationConfig = {
	pointAnimation: {
		type: "fade",
		duration: 800,
		delay: 300,
	},
	routeAnimation: {
		type: "progressive",
		duration: 1500,
		color: "#3b82f6",
		width: 4,
	},
	mapMovement: {
		type: "fly",
		duration: 1000,
	},
	autoPlay: {
		enabled: false,
		delay: 3000,
	},
};

const PresentationMode = ({
	points,
	onClose,
	isStandalone = false,
	className,
	config: customConfig,
}: PresentationModeProps) => {
	// 合并默认配置和自定义配置
	const config = useMemo(() => {
		return {
			...defaultConfig,
			...customConfig,
			pointAnimation: {
				...defaultConfig.pointAnimation,
				...customConfig?.pointAnimation,
			},
			routeAnimation: {
				...defaultConfig.routeAnimation,
				...customConfig?.routeAnimation,
			},
			mapMovement: {
				...defaultConfig.mapMovement,
				...customConfig?.mapMovement,
			},
			autoPlay: {
				...defaultConfig.autoPlay,
				...customConfig?.autoPlay,
			},
		};
	}, [customConfig]);

	// 状态管理
	const [currentPointIndex, setCurrentPointIndex] = useState<number>(-1);
	const [displayedPoints, setDisplayedPoints] = useState<TravelPoint[]>([]);
	const [isPlaying, setIsPlaying] = useState<boolean>(
		config.autoPlay.enabled,
	);
	const [mapReady, setMapReady] = useState<boolean>(false);
	const [polylines, setPolylines] = useState<google.maps.Polyline[]>([]);

	// 引用
	const mapServiceRef = useRef<MapService | null>(null);
	const mapRef = useRef<google.maps.Map | null>(null);
	const autoPlayTimerRef = useRef<NodeJS.Timeout | null>(null);
	const routeAnimationTimerRef = useRef<NodeJS.Timeout | null>(null);

	// 清除定时器的辅助函数
	const clearTimers = useCallback(() => {
		if (autoPlayTimerRef.current) {
			clearTimeout(autoPlayTimerRef.current);
			autoPlayTimerRef.current = null;
		}
		if (routeAnimationTimerRef.current) {
			clearTimeout(routeAnimationTimerRef.current);
			routeAnimationTimerRef.current = null;
		}
	}, []);

	// 设置地图服务
	const handleMapReady = useCallback(
		(mapService: any) => {
			mapServiceRef.current = mapService;
			if (mapService.getNativeMapInstance) {
				mapRef.current = mapService.getNativeMapInstance();
			}
			setMapReady(true);

			// 如果有点位，自动启动演示
			if (points.length > 0 && config.autoPlay.enabled) {
				startPresentation();
			}
		},
		[points, config.autoPlay.enabled],
	);

	// 开始演示
	const startPresentation = useCallback(() => {
		setCurrentPointIndex(-1);
		setDisplayedPoints([]);
		clearPolylines();
		setIsPlaying(true);

		// 适配显示所有点位的边界
		if (mapServiceRef.current && points.length > 0) {
			const latLngPoints = points.map((p) => ({
				lat: p.coordinates.lat,
				lng: p.coordinates.lng,
			}));
			mapServiceRef.current.fitBounds(latLngPoints);
		}

		// 延迟一下再开始动画，让地图有时间调整
		setTimeout(() => {
			moveToNextPoint();
		}, 500);
	}, [points]);

	// 重置演示
	const resetPresentation = useCallback(() => {
		setCurrentPointIndex(-1);
		setDisplayedPoints([]);
		clearPolylines();
		setIsPlaying(false);
		clearTimers();
	}, [clearTimers]);

	// 清除路线
	const clearPolylines = useCallback(() => {
		polylines.forEach((line) => {
			if (line) line.setMap(null);
		});
		setPolylines([]);
	}, [polylines]);

	// 绘制路线
	const drawRoute = useCallback(
		(fromPoint: TravelPoint, toPoint: TravelPoint) => {
			if (!mapRef.current) return;

			const googleMap = mapRef.current;

			if (config.routeAnimation.type === "none") return;

			// 创建路线
			const polyline = new google.maps.Polyline({
				path: [
					{
						lat: fromPoint.coordinates.lat,
						lng: fromPoint.coordinates.lng,
					},
					{
						lat: toPoint.coordinates.lat,
						lng: toPoint.coordinates.lng,
					},
				],
				geodesic: true,
				strokeColor: config.routeAnimation.color,
				strokeOpacity: 1.0,
				strokeWeight: config.routeAnimation.width,
				map: googleMap,
			});

			// 如果是渐进式动画，使用动画效果
			if (config.routeAnimation.type === "progressive") {
				animatePolyline(polyline, fromPoint, toPoint);
			}

			// 保存路线引用以便清理
			setPolylines((prev) => [...prev, polyline]);
		},
		[config.routeAnimation, mapRef.current],
	);

	// 路线动画
	const animatePolyline = useCallback(
		(
			polyline: google.maps.Polyline,
			fromPoint: TravelPoint,
			toPoint: TravelPoint,
		) => {
			if (!mapRef.current) return;

			const startLat = fromPoint.coordinates.lat;
			const startLng = fromPoint.coordinates.lng;
			const endLat = toPoint.coordinates.lat;
			const endLng = toPoint.coordinates.lng;

			const latDiff = endLat - startLat;
			const lngDiff = endLng - startLng;

			const startTime = Date.now();
			const duration = config.routeAnimation.duration;

			const animate = () => {
				const elapsed = Date.now() - startTime;
				const progress = Math.min(elapsed / duration, 1);

				const currentLat = startLat + latDiff * progress;
				const currentLng = startLng + lngDiff * progress;

				const path = [
					{ lat: startLat, lng: startLng },
					{ lat: currentLat, lng: currentLng },
				];

				polyline.setPath(path);

				if (progress < 1) {
					requestAnimationFrame(animate);
				}
			};

			requestAnimationFrame(animate);
		},
		[config.routeAnimation.duration, mapRef.current],
	);

	// 移动到下一个点位
	const moveToNextPoint = useCallback(() => {
		if (!isPlaying) return;

		const nextIndex = currentPointIndex + 1;

		// 检查是否已到达最后一个点位
		if (nextIndex >= points.length) {
			setIsPlaying(false);
			toast.info("演示结束");
			return;
		}

		const nextPoint = points[nextIndex];

		// 移动地图到新点位位置
		if (mapServiceRef.current) {
			const center = {
				lat: nextPoint.coordinates.lat,
				lng: nextPoint.coordinates.lng,
			};

			// 应用不同的移动效果
			if (config.mapMovement.type === "fly" && mapRef.current) {
				// 这里使用Google Maps的动画效果
				mapRef.current.panTo(center);
			} else {
				// 即时移动
				mapServiceRef.current.setCenter(center);
			}
		}

		// 绘制到前一个点的路线
		if (nextIndex > 0) {
			drawRoute(points[nextIndex - 1], nextPoint);
		}

		// 添加新点到显示列表
		setDisplayedPoints((prev) => [...prev, nextPoint]);
		setCurrentPointIndex(nextIndex);

		// 如果自动播放启用，设置计时器移动到下一个点
		if (config.autoPlay.enabled && isPlaying) {
			autoPlayTimerRef.current = setTimeout(() => {
				moveToNextPoint();
			}, config.autoPlay.delay);
		}
	}, [
		isPlaying,
		currentPointIndex,
		points,
		config.mapMovement,
		config.autoPlay,
		mapServiceRef.current,
		mapRef.current,
		drawRoute,
	]);

	// 移动到前一个点位
	const moveToPrevPoint = useCallback(() => {
		if (currentPointIndex <= 0) return;

		clearTimers();

		const prevIndex = currentPointIndex - 1;
		const prevPoint = points[prevIndex];

		// 移动地图到上一个点位位置
		if (mapServiceRef.current) {
			const center = {
				lat: prevPoint.coordinates.lat,
				lng: prevPoint.coordinates.lng,
			};

			// 应用不同的移动效果
			if (config.mapMovement.type === "fly" && mapRef.current) {
				mapRef.current.panTo(center);
			} else {
				mapServiceRef.current.setCenter(center);
			}
		}

		// 移除最后一条路线
		if (polylines.length > 0) {
			const lastPolyline = polylines[polylines.length - 1];
			if (lastPolyline) lastPolyline.setMap(null);
			setPolylines((prev) => prev.slice(0, -1));
		}

		// 从显示列表中移除最后一个点
		setDisplayedPoints((prev) => prev.slice(0, -1));
		setCurrentPointIndex(prevIndex);

		// 如果自动播放启用，设置计时器移动到下一个点
		if (config.autoPlay.enabled && isPlaying) {
			autoPlayTimerRef.current = setTimeout(() => {
				moveToNextPoint();
			}, config.autoPlay.delay);
		}
	}, [
		currentPointIndex,
		points,
		config.mapMovement,
		config.autoPlay,
		isPlaying,
		clearTimers,
		mapServiceRef.current,
		mapRef.current,
		polylines,
	]);

	// 处理播放/暂停切换
	const togglePlayPause = useCallback(() => {
		if (isPlaying) {
			// 暂停
			clearTimers();
			setIsPlaying(false);
		} else {
			// 播放
			setIsPlaying(true);
			if (currentPointIndex < points.length - 1) {
				moveToNextPoint();
			} else {
				// 重新开始
				resetPresentation();
				setTimeout(() => {
					startPresentation();
				}, 500);
			}
		}
	}, [
		isPlaying,
		currentPointIndex,
		points.length,
		clearTimers,
		moveToNextPoint,
		resetPresentation,
		startPresentation,
	]);

	// 处理进度条变化
	const handleProgressChange = useCallback(
		(value: number[]) => {
			clearTimers();
			setIsPlaying(false);

			const newIndex = Math.floor(value[0]);

			// 如果当前索引与新索引相同，不做任何事
			if (newIndex === currentPointIndex) return;

			// 重置路线和点位
			clearPolylines();

			// 设置新的点位和路线
			const newDisplayedPoints = points.slice(0, newIndex + 1);
			setDisplayedPoints(newDisplayedPoints);
			setCurrentPointIndex(newIndex);

			// 移动地图到选定点位
			if (mapServiceRef.current && newIndex >= 0) {
				const center = {
					lat: points[newIndex].coordinates.lat,
					lng: points[newIndex].coordinates.lng,
				};

				if (config.mapMovement.type === "fly" && mapRef.current) {
					mapRef.current.panTo(center);
				} else {
					mapServiceRef.current.setCenter(center);
				}
			}

			// 绘制路线
			newDisplayedPoints.forEach((point, idx) => {
				if (idx > 0) {
					drawRoute(newDisplayedPoints[idx - 1], point);
				}
			});
		},
		[
			clearTimers,
			currentPointIndex,
			points,
			clearPolylines,
			mapServiceRef.current,
			config.mapMovement,
			mapRef.current,
			drawRoute,
		],
	);

	// 清理定时器和事件监听器
	useEffect(() => {
		return () => {
			clearTimers();
			clearPolylines();
		};
	}, [clearTimers, clearPolylines]);

	return (
		<div
			className={cn(
				"w-full h-full flex flex-col bg-white bg-opacity-90 rounded-lg shadow-lg overflow-hidden",
				className,
			)}
		>
			<div className="relative flex-1 overflow-hidden">
				{/* 地图容器 */}
				<div className="absolute inset-0">
					<TravelMap
						className="h-full w-full absolute inset-0"
						footprints={displayedPoints.map((point) => ({
							id: point.id,
							latitude: point.coordinates.lat,
							longitude: point.coordinates.lng,
							location: point.location,
							diaryId: "presentation", // 为演示模式提供一个默认的日记ID
							iconType: point.iconType,
						}))}
						height="100%"
					/>
				</div>

				{/* 浮动标题栏 - 仅在独立模式显示 */}
				{isStandalone && (
					<div className="absolute top-4 left-4 right-4 bg-white/80 backdrop-blur-sm p-3 rounded-lg shadow-md z-10">
						<h2 className="text-xl font-semibold text-center">
							旅行路线演示
						</h2>
					</div>
				)}

				{/* 右上角关闭按钮 - 仅在非独立模式显示 */}
				{!isStandalone && onClose && (
					<Button
						variant="outline"
						size="sm"
						className="absolute top-4 right-4 z-10 bg-white/80 hover:bg-white"
						onClick={onClose}
					>
						退出演示
					</Button>
				)}

				{/* 当前点位信息卡片 */}
				{currentPointIndex >= 0 &&
					currentPointIndex < points.length && (
						<div className="absolute bottom-20 left-4 right-4 md:w-auto md:max-w-md md:right-auto md:left-4 bg-white/90 backdrop-blur-sm p-3 rounded-lg shadow-md z-10">
							<div className="flex items-start">
								<div className="text-2xl mr-3">
									{getIconForType(
										points[currentPointIndex].iconType,
									)}
								</div>
								<div className="flex-1">
									<h3 className="font-medium text-base">
										{points[currentPointIndex].location}
									</h3>
									<p className="text-sm text-muted-foreground line-clamp-2">
										{points[currentPointIndex].description}
									</p>
								</div>
							</div>
						</div>
					)}
			</div>

			{/* 底部控制条 */}
			<div className="bg-muted/60 backdrop-blur-sm px-4 py-2 flex flex-col md:flex-row gap-3 md:items-center">
				{/* 播放控制按钮 */}
				<div className="flex items-center gap-2">
					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 bg-white/80 hover:bg-white"
						onClick={() => resetPresentation()}
						title="重新开始"
					>
						<SkipBack size={16} />
					</Button>

					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 bg-white/80 hover:bg-white"
						onClick={() => moveToPrevPoint()}
						disabled={currentPointIndex <= 0}
						title="上一个"
					>
						<ChevronLeft size={16} />
					</Button>

					<Button
						variant={isPlaying ? "secondary" : "primary"}
						size="icon"
						className="h-8 w-8"
						onClick={togglePlayPause}
						title={isPlaying ? "暂停" : "播放"}
					>
						{isPlaying ? <Pause size={16} /> : <Play size={16} />}
					</Button>

					<Button
						variant="outline"
						size="icon"
						className="h-8 w-8 bg-white/80 hover:bg-white"
						onClick={() => moveToNextPoint()}
						disabled={currentPointIndex >= points.length - 1}
						title="下一个"
					>
						<ChevronRight size={16} />
					</Button>
				</div>

				{/* 进度条 */}
				<div className="flex-1 flex items-center gap-2">
					<div className="text-xs font-medium w-6 text-right">
						{currentPointIndex + 1}
					</div>

					<Slider
						value={[currentPointIndex + 1]}
						min={0}
						max={points.length > 0 ? points.length - 1 : 0}
						step={1}
						onValueChange={handleProgressChange}
						disabled={points.length <= 1}
						className="flex-1"
					/>

					<div className="text-xs font-medium w-6">
						{points.length}
					</div>
				</div>
			</div>
		</div>
	);
};

// 辅助函数 - 根据图标类型获取表情符号
function getIconForType(iconType: string): string {
	switch (iconType) {
		case "hotel":
			return "🏨";
		case "food":
			return "🍽️";
		case "landmark":
			return "🗿";
		case "beach":
			return "🏖️";
		case "mountain":
			return "⛰️";
		case "museum":
			return "🏛️";
		case "park":
			return "🌳";
		default:
			return "📍";
	}
}

export default PresentationMode;
