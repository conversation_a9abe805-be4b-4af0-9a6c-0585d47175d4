"use client";
import { <PERSON><PERSON> } from "@ui/components/button";
import { Eye, EyeOff, MapPin } from "lucide-react";
import { useTravelStatTranslations } from "../../../hooks/useTravelStatTranslations";

interface DisplayPopoverProps {
	showTooltips: boolean;
	showMarkers: boolean;
	onShowTooltipsChange: (show: boolean) => void;
	onShowMarkersChange: (show: boolean) => void;
}

export function DisplayPopover({
	showTooltips,
	showMarkers,
	onShowTooltipsChange,
	onShowMarkersChange,
}: DisplayPopoverProps) {
	const translations = useTravelStatTranslations();

	// 预设选项
	const presets = [
		{
			id: "all",
			name: translations.mapControls.display.presets.all.name(),
			description:
				translations.mapControls.display.presets.all.description(),
			icon: <Eye className="w-4 h-4" />,
			tooltips: true,
			markers: true,
		},
		{
			id: "markers-only",
			name: translations.mapControls.display.presets.markersOnly.name(),
			description:
				translations.mapControls.display.presets.markersOnly.description(),
			icon: <MapPin className="w-4 h-4" />,
			tooltips: false,
			markers: true,
		},
		{
			id: "clean",
			name: translations.mapControls.display.presets.clean.name(),
			description:
				translations.mapControls.display.presets.clean.description(),
			icon: <EyeOff className="w-4 h-4" />,
			tooltips: false,
			markers: false,
		},
	];

	const currentPreset = presets.find(
		(preset) =>
			preset.tooltips === showTooltips && preset.markers === showMarkers,
	);

	const handlePresetClick = (preset: (typeof presets)[0]) => {
		onShowTooltipsChange(preset.tooltips);
		onShowMarkersChange(preset.markers);
	};

	return (
		<div className="w-[320px] p-4 space-y-4">
			{/* 预设选项 - 占满整个宽度 */}
			<div className="space-y-2">
				{presets.map((preset) => (
					<Button
						key={preset.id}
						variant={
							currentPreset?.id === preset.id
								? "primary"
								: "outline"
						}
						size="sm"
						className="w-full justify-start h-auto p-3"
						onClick={() => handlePresetClick(preset)}
					>
						<div className="flex items-center gap-3 w-full">
							<div
								className={`${currentPreset?.id === preset.id ? "text-white" : "text-blue-600"}`}
							>
								{preset.icon}
							</div>
							<div className="flex-1 text-left">
								<div className="font-medium text-sm">
									{preset.name}
								</div>
								<div
									className={`text-xs ${currentPreset?.id === preset.id ? "text-blue-100" : "text-gray-500"}`}
								>
									{preset.description}
								</div>
							</div>
							{currentPreset?.id === preset.id && (
								<span className="bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded">
									{translations.mapControls.display.current()}
								</span>
							)}
						</div>
					</Button>
				))}
			</div>
		</div>
	);
}
