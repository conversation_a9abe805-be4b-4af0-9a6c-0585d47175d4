#!/usr/bin/env tsx

import { execSync } from "node:child_process";
import { existsSync } from "node:fs";

const SCHEMA_PATH = "prisma/schema-ai-images.prisma";
const ENV_FILE = "../../.env.local";

function runCommand(command: string, description: string) {
	console.log(`\n🔄 ${description}...`);
	try {
		execSync(command, {
			stdio: "inherit",
			cwd: process.cwd(),
		});
		console.log(`✅ ${description} 完成`);
	} catch (error) {
		console.error(`❌ ${description} 失败:`, error);
		process.exit(1);
	}
}

function main() {
	const args = process.argv.slice(2);
	const action = args[0];
	const migrationName = args[1];

	if (!existsSync(SCHEMA_PATH)) {
		console.error(`❌ Schema 文件不存在: ${SCHEMA_PATH}`);
		process.exit(1);
	}

	switch (action) {
		case "generate":
			runCommand(
				`dotenv -c -e ${ENV_FILE} -- prisma generate --schema=${SCHEMA_PATH}`,
				"AI图片数据库类型生成",
			);
			break;

		case "push":
			runCommand(
				`dotenv -c -e ${ENV_FILE} -- prisma db push --skip-generate --schema=${SCHEMA_PATH}`,
				"AI图片数据库推送",
			);
			break;

		case "migrate":
			if (!migrationName) {
				console.error(
					"❌ 请提供迁移名称: pnpm tsx scripts/migrate-ai-images.ts migrate <migration-name>",
				);
				process.exit(1);
			}
			runCommand(
				`dotenv -c -e ${ENV_FILE} -- prisma migrate dev --name ${migrationName} --schema=${SCHEMA_PATH}`,
				`AI图片数据库迁移: ${migrationName}`,
			);
			break;

		case "studio":
			runCommand(
				`dotenv -c -e ${ENV_FILE} -- prisma studio --schema=${SCHEMA_PATH}`,
				"AI图片数据库管理界面启动",
			);
			break;

		case "reset":
			console.log("⚠️  这将重置AI图片数据库的所有数据！");
			runCommand(
				`dotenv -c -e ${ENV_FILE} -- prisma migrate reset --schema=${SCHEMA_PATH}`,
				"AI图片数据库重置",
			);
			break;

		case "deploy":
			runCommand(
				`dotenv -c -e ${ENV_FILE} -- prisma migrate deploy --schema=${SCHEMA_PATH}`,
				"AI图片数据库生产部署",
			);
			break;

		default:
			console.log(`
🗄️  AI图片数据库迁移工具

用法:
  pnpm tsx scripts/migrate-ai-images.ts <action> [options]

操作:
  generate              生成Prisma客户端类型
  push                  推送schema到数据库（开发环境）
  migrate <name>        创建并应用迁移
  studio                启动数据库管理界面
  reset                 重置数据库（危险操作）
  deploy                部署迁移到生产环境

示例:
  pnpm tsx scripts/migrate-ai-images.ts generate
  pnpm tsx scripts/migrate-ai-images.ts migrate add_image_tags
  pnpm tsx scripts/migrate-ai-images.ts studio
      `);
			break;
	}
}

main();
