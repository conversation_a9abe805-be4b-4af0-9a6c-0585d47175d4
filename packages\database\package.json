{"dependencies": {"@prisma/client": "^6.3.1", "@repo/config": "workspace:*", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@tiptap/core": "^2.12.0", "@types/node": "22.14.0", "dotenv-cli": "^8.0.0", "prisma": "^6.6.0", "tsx": "^4.19.4", "zod-prisma-types": "^3.2.4"}, "main": "./index.ts", "name": "@repo/database", "scripts": {"generate": "prisma generate", "generate:ai-images": "prisma generate --schema=prisma/schema-ai-images.prisma", "generate:all": "pnpm run generate && pnpm run generate:ai-images", "push": "dotenv -c -e ../../.env -- prisma db push --skip-generate", "push:ai-images": "dotenv -c -e ../../.env -- prisma db push --skip-generate --schema=prisma/schema-ai-images.prisma", "migrate": "dotenv -c -e ../../.env -- prisma migrate dev", "migrate:ai-images": "dotenv -c -e ../../.env -- prisma migrate dev --schema=prisma/schema-ai-images.prisma", "migrate:resolve": "dotenv -c -e ../../.env -- prisma migrate resolve --applied 20250510222111_remove_travel_point_icon_type_enum", "migrate:images": "dotenv -c -e ../../.env -- npx tsx scripts/migrate-images-to-objects.ts", "studio": "dotenv -c -e ../../.env -- prisma studio", "studio:ai-images": "dotenv -c -e ../../.env -- prisma studio --schema=prisma/schema-ai-images.prisma", "type-check": "tsc --noEmit"}, "types": "./**/.tsx", "version": "0.0.0"}