import type { AiChat } from "@repo/database";
import { apiClient } from "@shared/lib/api-client";
import { useMutation, useQuery } from "@tanstack/react-query";

// API返回的类型（日期是字符串）
type AiChatFromAPI = Omit<AiChat, "createdAt" | "updatedAt"> & {
	createdAt: string;
	updatedAt: string;
};

export const aiChatListQueryKey = (organizationId?: string) =>
	organizationId
		? (["ai-chat-list", organizationId] as const)
		: (["ai-chat-list"] as const);

export const useAiChatListQuery = (organizationId?: string) =>
	useQuery<AiChatFromAPI[]>({
		queryKey: aiChatListQueryKey(organizationId),
		queryFn: async (): Promise<AiChatFromAPI[]> => {
			const response = await apiClient.ai.chats.$get({
				query: {
					organizationId,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to fetch AI chat list");
			}

			return response.json();
		},
	});

export const aiChatQueryKey = (id: string) => ["ai-chat", id];
export const useAiChatQuery = (id: string) =>
	useQuery<AiChatFromAPI | null>({
		queryKey: aiChatQueryKey(id),
		queryFn: async (): Promise<AiChatFromAPI | null> => {
			if (id === "new") {
				return null;
			}

			const response = await apiClient.ai.chats[":id"].$get({
				param: {
					id,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to fetch AI chat");
			}

			return response.json();
		},
	});

export const useCreateAiChatMutation = () => {
	return useMutation({
		mutationFn: async ({
			title,
			organizationId,
		}: {
			title?: string;
			organizationId?: string;
		}): Promise<AiChatFromAPI> => {
			const response = await apiClient.ai.chats.$post({
				json: {
					title,
					organizationId,
				},
			});

			if (!response.ok) {
				throw new Error("Failed to create AI chat");
			}

			return response.json();
		},
	});
};

export const updateAiChatMutation = () => {
	return useMutation({
		mutationFn: async ({
			id,
			title,
		}: { id: string; title?: string }): Promise<AiChatFromAPI> => {
			const response = await apiClient.ai.chats[":id"].$put({
				param: { id },
				json: { title },
			});

			if (!response.ok) {
				throw new Error("Failed to update AI chat");
			}

			return response.json();
		},
	});
};

export const deleteAiChatMutation = () => {
	return useMutation({
		mutationFn: async (id: string) => {
			const response = await apiClient.ai.chats[":id"].$delete({
				param: { id },
			});

			if (!response.ok) {
				throw new Error("Failed to delete AI chat");
			}
		},
	});
};
