"use client";

import { type ReactNode, useEffect, useRef, useState } from "react";

interface ScrollableAreaProps {
	children: ReactNode;
	className?: string;
	maxHeight?: string;
	showScrollIndicator?: boolean;
}

/**
 * 可滚动区域组件
 * 提供更好的滚动体验和可选的滚动指示器
 */
export function ScrollableArea({
	children,
	className = "",
	maxHeight = "70vh",
	showScrollIndicator = true,
}: ScrollableAreaProps) {
	const scrollRef = useRef<HTMLDivElement>(null);
	const [showTopShadow, setShowTopShadow] = useState(false);
	const [showBottomShadow, setShowBottomShadow] = useState(false);

	useEffect(() => {
		const checkScrollShadows = () => {
			const element = scrollRef.current;
			if (!element) return;

			const { scrollTop, scrollHeight, clientHeight } = element;
			const isScrollable = scrollHeight > clientHeight;

			if (!isScrollable) {
				setShowTopShadow(false);
				setShowBottomShadow(false);
				return;
			}

			setShowTopShadow(scrollTop > 0);
			setShowBottomShadow(scrollTop < scrollHeight - clientHeight - 1);
		};

		const element = scrollRef.current;
		if (element) {
			checkScrollShadows();
			element.addEventListener("scroll", checkScrollShadows);

			// 检查初始状态
			const resizeObserver = new ResizeObserver(checkScrollShadows);
			resizeObserver.observe(element);

			return () => {
				element.removeEventListener("scroll", checkScrollShadows);
				resizeObserver.disconnect();
			};
		}
	}, []);

	return (
		<div className={`relative w-full ${className}`} style={{ maxHeight }}>
			{/* 顶部阴影指示器 */}
			{showScrollIndicator && showTopShadow && (
				<div className="absolute top-0 left-0 right-0 h-3 bg-gradient-to-b from-white to-transparent pointer-events-none z-10" />
			)}

			{/* 滚动内容区域 */}
			<div
				ref={scrollRef}
				className={`h-full overflow-y-auto 
							scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100
							hover:scrollbar-thumb-gray-400 transition-colors`}
			>
				{children}
			</div>

			{/* 底部阴影指示器 */}
			{showScrollIndicator && showBottomShadow && (
				<div className="absolute bottom-0 left-0 right-0 h-3 bg-gradient-to-t from-white to-transparent pointer-events-none z-10" />
			)}
		</div>
	);
}
