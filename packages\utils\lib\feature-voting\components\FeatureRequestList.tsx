"use client";

import React from "react";
import type {
	FeatureRequest,
	FeatureRequestQuery,
	PaginationInfo,
} from "../types";
import { FeatureRequestCard } from "./FeatureRequestCard";

interface FeatureRequestListProps {
	featureRequests: FeatureRequest[];
	onVote: (featureRequestId: string) => Promise<void>;
	onUnvote: (featureRequestId: string) => Promise<void>;
	votingStates?: Record<string, boolean>;
	showVoteCount?: boolean;
	showProduct?: boolean;
	showAuthor?: boolean;
	showDescription?: boolean;
	onFeatureRequestClick?: (featureRequest: FeatureRequest) => void;
	loading?: boolean;
	error?: string | null;
	emptyMessage?: string;
	pagination?: PaginationInfo | null;
	onPageChange?: (page: number) => void;
	onQueryChange?: (query: FeatureRequestQuery) => void;
	className?: string;
}

export function FeatureRequestList({
	featureRequests,
	onVote,
	onUnvote,
	votingStates = {},
	showVoteCount = true,
	showProduct = true,
	showAuthor = true,
	showDescription = true,
	onFeatureRequestClick,
	loading = false,
	error = null,
	emptyMessage = "暂无功能请求",
	pagination = null,
	onPageChange,
	onQueryChange,
	className = "",
}: FeatureRequestListProps) {
	// 加载状态
	if (loading) {
		return (
			<div className={`space-y-4 ${className}`}>
				{Array.from({ length: 3 }).map((_, index) => (
					<div
						key={index}
						className="bg-white rounded-lg border border-gray-200 p-6 animate-pulse"
					>
						<div className="flex items-start justify-between mb-3">
							<div className="flex-1">
								<div className="h-6 bg-gray-200 rounded w-3/4 mb-2" />
								<div className="flex items-center gap-2">
									<div className="h-4 bg-gray-200 rounded w-16" />
									<div className="h-4 bg-gray-200 rounded w-20" />
								</div>
							</div>
							<div className="h-8 bg-gray-200 rounded w-20" />
						</div>
						<div className="space-y-2 mb-4">
							<div className="h-4 bg-gray-200 rounded w-full" />
							<div className="h-4 bg-gray-200 rounded w-5/6" />
						</div>
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<div className="w-6 h-6 bg-gray-200 rounded-full" />
								<div className="h-4 bg-gray-200 rounded w-24" />
							</div>
						</div>
					</div>
				))}
			</div>
		);
	}

	// 错误状态
	if (error) {
		return (
			<div className={`text-center py-12 ${className}`}>
				<div className="text-red-600 mb-4">
					<svg
						className="w-12 h-12 mx-auto mb-4"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
						xmlns="http://www.w3.org/2000/svg"
						aria-label="错误图标"
					>
						<title>错误</title>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
						/>
					</svg>
				</div>
				<h3 className="text-lg font-medium text-gray-900 mb-2">
					加载失败
				</h3>
				<p className="text-gray-600">{error}</p>
			</div>
		);
	}

	// 空状态
	if (!featureRequests.length) {
		return (
			<div className={`text-center py-12 ${className}`}>
				<div className="text-gray-400 mb-4">
					<svg
						className="w-12 h-12 mx-auto mb-4"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
						xmlns="http://www.w3.org/2000/svg"
						aria-label="空状态图标"
					>
						<title>空状态</title>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
						/>
					</svg>
				</div>
				<h3 className="text-lg font-medium text-gray-900 mb-2">
					{emptyMessage}
				</h3>
				<p className="text-gray-600">
					成为第一个提交功能请求的用户吧！
				</p>
			</div>
		);
	}

	return (
		<div className={className}>
			{/* 功能请求列表 */}
			<div className="space-y-4 mb-6">
				{featureRequests.map((featureRequest) => (
					<FeatureRequestCard
						key={featureRequest.id}
						featureRequest={featureRequest}
						onVote={onVote}
						onUnvote={onUnvote}
						isVoting={votingStates[featureRequest.id] || false}
						showVoteCount={showVoteCount}
						showProduct={showProduct}
						showAuthor={showAuthor}
						showDescription={showDescription}
						onClick={onFeatureRequestClick}
					/>
				))}
			</div>

			{/* 分页 */}
			{pagination && pagination.totalPages > 1 && (
				<Pagination
					pagination={pagination}
					onPageChange={onPageChange}
				/>
			)}
		</div>
	);
}

// 分页组件
interface PaginationProps {
	pagination: PaginationInfo;
	onPageChange?: (page: number) => void;
}

function Pagination({ pagination, onPageChange }: PaginationProps) {
	const { page, totalPages, total } = pagination;

	const generatePageNumbers = () => {
		const pages: (number | string)[] = [];
		const showEllipsis = totalPages > 7;

		if (!showEllipsis) {
			// 如果总页数不超过7页，显示所有页码
			for (let i = 1; i <= totalPages; i++) {
				pages.push(i);
			}
		} else {
			// 复杂的省略号逻辑
			if (page <= 4) {
				for (let i = 1; i <= 5; i++) {
					pages.push(i);
				}
				pages.push("...");
				pages.push(totalPages);
			} else if (page >= totalPages - 3) {
				pages.push(1);
				pages.push("...");
				for (let i = totalPages - 4; i <= totalPages; i++) {
					pages.push(i);
				}
			} else {
				pages.push(1);
				pages.push("...");
				for (let i = page - 1; i <= page + 1; i++) {
					pages.push(i);
				}
				pages.push("...");
				pages.push(totalPages);
			}
		}

		return pages;
	};

	const pageNumbers = generatePageNumbers();

	const handlePageClick = (targetPage: number) => {
		if (targetPage !== page && onPageChange) {
			onPageChange(targetPage);
		}
	};

	return (
		<div className="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
			<div className="flex flex-1 justify-between sm:hidden">
				{/* 移动端简化版分页 */}
				<button
					type="button"
					onClick={() => handlePageClick(page - 1)}
					disabled={page <= 1}
					className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
				>
					上一页
				</button>
				<button
					type="button"
					onClick={() => handlePageClick(page + 1)}
					disabled={page >= totalPages}
					className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
				>
					下一页
				</button>
			</div>

			<div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
				<div>
					<p className="text-sm text-gray-700">
						共 <span className="font-medium">{total}</span>{" "}
						条结果，第 <span className="font-medium">{page}</span>{" "}
						页，共 <span className="font-medium">{totalPages}</span>{" "}
						页
					</p>
				</div>

				<div>
					<nav
						className="isolate inline-flex -space-x-px rounded-md shadow-sm"
						aria-label="分页"
					>
						{/* 上一页按钮 */}
						<button
							type="button"
							onClick={() => handlePageClick(page - 1)}
							disabled={page <= 1}
							className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
						>
							<span className="sr-only">上一页</span>
							<svg
								className="h-5 w-5"
								viewBox="0 0 20 20"
								fill="currentColor"
								aria-hidden="true"
							>
								<path
									fillRule="evenodd"
									d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z"
									clipRule="evenodd"
								/>
							</svg>
						</button>

						{/* 页码 */}
						{pageNumbers.map((pageNum, index) => {
							if (pageNum === "...") {
								return (
									<span
										key={`ellipsis-${index}`}
										className="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300 focus:outline-offset-0"
									>
										...
									</span>
								);
							}

							const isCurrentPage = pageNum === page;
							return (
								<button
									key={pageNum}
									type="button"
									onClick={() =>
										handlePageClick(pageNum as number)
									}
									className={`relative inline-flex items-center px-4 py-2 text-sm font-semibold ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 ${
										isCurrentPage
											? "z-10 bg-blue-600 text-white focus:bg-blue-700"
											: "text-gray-900"
									}`}
								>
									{pageNum}
								</button>
							);
						})}

						{/* 下一页按钮 */}
						<button
							type="button"
							onClick={() => handlePageClick(page + 1)}
							disabled={page >= totalPages}
							className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 disabled:opacity-50 disabled:cursor-not-allowed"
						>
							<span className="sr-only">下一页</span>
							<svg
								className="h-5 w-5"
								viewBox="0 0 20 20"
								fill="currentColor"
								aria-hidden="true"
							>
								<path
									fillRule="evenodd"
									d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
									clipRule="evenodd"
								/>
							</svg>
						</button>
					</nav>
				</div>
			</div>
		</div>
	);
}
