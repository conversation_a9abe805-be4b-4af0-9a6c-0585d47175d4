"use client";

import { cn } from "@ui/lib";
import Image from "next/image";
import { useEffect, useState } from "react";

interface BlogGalleryProps {
	images: Array<{
		src: string;
		alt: string;
		title?: string;
		description?: string;
	}>;
	layout?: "grid" | "masonry" | "hero";
	columns?: 2 | 3 | 4;
	className?: string;
}

export function BlogGallery({
	images,
	layout = "grid",
	columns = 3,
	className,
}: BlogGalleryProps) {
	const [selectedImage, setSelectedImage] = useState<string | null>(null);
	const [currentImageIndex, setCurrentImageIndex] = useState(0);

	// 自动轮播逻辑（仅在hero模式且有多张图片时）
	useEffect(() => {
		if (layout === "hero" && images.length > 1) {
			const timer = setInterval(() => {
				setCurrentImageIndex((prev) => (prev + 1) % images.length);
			}, 5000); // 5秒切换一次

			return () => clearInterval(timer);
		}
	}, [layout, images.length]);

	const getGridCols = () => {
		switch (columns) {
			case 2:
				return "md:grid-cols-2";
			case 3:
				return "md:grid-cols-3";
			case 4:
				return "md:grid-cols-2 lg:grid-cols-4";
			default:
				return "md:grid-cols-3";
		}
	};

	const goToNextImage = () => {
		setCurrentImageIndex((prev) => (prev + 1) % images.length);
	};

	const goToPrevImage = () => {
		setCurrentImageIndex(
			(prev) => (prev - 1 + images.length) % images.length,
		);
	};

	if (layout === "hero" && images.length > 0) {
		const currentImage = images[currentImageIndex];

		return (
			<div className={cn("relative w-full mb-8 group", className)}>
				<div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-50/30 to-purple-50/30 dark:from-blue-900/20 dark:to-purple-900/20">
					{/* 主图片 */}
					<Image
						src={currentImage.src}
						alt={currentImage.alt}
						width={1200}
						height={800}
						className="w-full h-auto transition-opacity duration-500"
						sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
						priority
					/>
					<div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent" />

					{/* 左右切换按钮 - 仅在有多张图片时显示 */}
					{images.length > 1 && (
						<>
							<button
								type="button"
								onClick={goToPrevImage}
								className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 backdrop-blur-sm border border-white/30 rounded-full p-3 text-white transition-all duration-200 opacity-0 group-hover:opacity-100"
								aria-label="上一张图片"
							>
								<svg
									className="w-6 h-6"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<title>上一张</title>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M15 19l-7-7 7-7"
									/>
								</svg>
							</button>
							<button
								type="button"
								onClick={goToNextImage}
								className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 backdrop-blur-sm border border-white/30 rounded-full p-3 text-white transition-all duration-200 opacity-0 group-hover:opacity-100"
								aria-label="下一张图片"
							>
								<svg
									className="w-6 h-6"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<title>下一张</title>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M9 5l7 7-7 7"
									/>
								</svg>
							</button>
						</>
					)}

					{/* 图片指示器 - 仅在有多张图片时显示 */}
					{images.length > 1 && (
						<div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2">
							{images.map((_, index) => (
								<button
									key={index}
									type="button"
									onClick={() => setCurrentImageIndex(index)}
									className={cn(
										"w-2 h-2 rounded-full transition-all duration-200",
										index === currentImageIndex
											? "bg-white scale-125"
											: "bg-white/50 hover:bg-white/70",
									)}
									aria-label={`切换到第${index + 1}张图片`}
								/>
							))}
						</div>
					)}

					{/* 图片标题和描述 */}
					{currentImage.title && (
						<div className="absolute bottom-8 left-6 right-6">
							<h3 className="text-2xl font-bold text-white mb-2 drop-shadow-lg">
								{currentImage.title}
							</h3>
							{currentImage.description && (
								<p className="text-white/90 drop-shadow-md">
									{currentImage.description}
								</p>
							)}
						</div>
					)}
				</div>
			</div>
		);
	}

	// Grid 或 Masonry 布局
	return (
		<>
			<div
				className={cn(
					"grid gap-4 mb-8",
					layout === "masonry"
						? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
						: `grid-cols-1 ${getGridCols()}`,
					className,
				)}
			>
				{images.map((image, index) => (
					<button
						type="button"
						key={index}
						className={cn(
							"group relative overflow-hidden rounded-xl bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 cursor-pointer transition-all duration-300 hover:scale-[1.02] hover:shadow-lg aspect-video w-full",
							layout === "masonry" && index === 0
								? "md:col-span-2 md:row-span-2"
								: "",
						)}
						onClick={() => setSelectedImage(image.src)}
						aria-label={`查看图片: ${image.alt}`}
					>
						<Image
							src={image.src}
							alt={image.alt}
							fill
							className="object-cover transition-transform duration-300 group-hover:scale-105"
							sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
						/>
						<div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

						{/* 图片信息 */}
						{(image.title || image.description) && (
							<div className="absolute bottom-0 left-0 right-0 p-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
								{image.title && (
									<h3 className="font-semibold mb-1 drop-shadow-lg">
										{image.title}
									</h3>
								)}
								{image.description && (
									<p className="text-sm text-white/90 drop-shadow-md">
										{image.description}
									</p>
								)}
							</div>
						)}
					</button>
				))}
			</div>

			{/* 全屏预览模态框 */}
			{selectedImage && (
				<dialog
					open
					className="fixed inset-0 z-50 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 border-none"
					onClick={() => setSelectedImage(null)}
					onKeyDown={(e) => {
						if (e.key === "Escape") {
							setSelectedImage(null);
						}
					}}
				>
					<div className="relative max-w-4xl max-h-[90vh] w-full h-full">
						<Image
							src={selectedImage}
							alt="预览图片"
							fill
							className="object-contain"
							sizes="(max-width: 768px) 100vw, 80vw"
						/>
						<button
							type="button"
							onClick={(e) => {
								e.stopPropagation();
								setSelectedImage(null);
							}}
							className="absolute top-4 right-4 bg-white/20 hover:bg-white/30 backdrop-blur-sm border border-white/30 rounded-full p-2 text-white transition-all duration-200"
							aria-label="关闭预览"
						>
							<svg
								className="w-6 h-6"
								fill="none"
								viewBox="0 0 24 24"
								stroke="currentColor"
							>
								<title>关闭</title>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M6 18L18 6M6 6l12 12"
								/>
							</svg>
						</button>
					</div>
				</dialog>
			)}
		</>
	);
}
