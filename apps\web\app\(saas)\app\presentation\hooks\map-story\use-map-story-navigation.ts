"use client";

import mapboxgl from "mapbox-gl";
import { useCallback } from "react";
import { ANIMATION_DURATION, StoryState } from "../../constants";
import type { FrontendTravelPoint } from "../../types";
import type { MapStoryAnimationContext, MapStoryNavigation } from "./types";
import { createLogger } from "./utils";

// 创建导航模块专用的日志记录器
const logger = createLogger("MapStoryNavigation");

export function useMapStoryNavigation(
	context: MapStoryAnimationContext,
): MapStoryNavigation {
	const { props, state, setters, refs, animation, timers } = context;
	const { points, onComplete } = props;
	const {
		storyState,
		currentPointIndex,
		isPlaying,
		isMapReady,
		mapInstance,
	} = state;
	const {
		setCurrentPointIndex,
		setIsPlaying,
		setFlyToPointIndex,
		setFitBoundsToPoints,
		setShowPointInfo,
		setShowImages,
		setSpotlightPosition,
		setStoryState,
		setShowStartPrompt,
		setShowRestartPrompt,
		setManuallyReset,
		setAutoPlayCountdown,
		setIsTypingCompleted,
	} = setters;
	const {
		isAnimatingRef,
		currentPointDisplayTimeoutRef,
		autoPlayTimerRef,
		typingPromiseResolverRef,
	} = refs;
	const { finishPresentation, advanceToPoint, updateSpotlightPosition } =
		animation;
	const { clearPlayTimer } = timers;

	// 提取导出模式配置
	const isExportMode = props.config?.isExportMode || false;
	const exportPointDuration = props.config?.pointDuration || 5;

	// 处理到达点位的回调
	const handlePointArrival = useCallback(
		async (arrivedPoint: FrontendTravelPoint) => {
			// 如果已经处于完成状态，则不响应
			if (
				storyState === StoryState.COMPLETED ||
				storyState === StoryState.ANIMATING_TO_COMPLETED_VIEW
			) {
				return;
			}

			// 清除任何现有的点位显示计时器
			clearPlayTimer();

			// 查找到达点位的索引（基于0的数组索引）
			const pointIndex = points.findIndex(
				(p) => p.id === arrivedPoint.id,
			);
			if (pointIndex === -1) {
				logger.error("无法找到到达的点位", {
					arrivedPointId: arrivedPoint.id,
				});
				return;
			}

			// 更新UI索引（显示为1基的索引）
			const newPointIndex = pointIndex + 1;
			logger.info("到达点位", {
				pointId: arrivedPoint.id,
				newPointIndex,
				pointTitle: arrivedPoint.title,
			});

			setCurrentPointIndex(newPointIndex);
			setStoryState(StoryState.POINT_ARRIVED_DISPLAYING);
			setShowPointInfo(true);
			setShowImages(!!arrivedPoint.images?.length); // 只有在有图片时才显示图片
			setIsTypingCompleted(false); // 重置打字完成状态

			// 更新聚光灯位置
			updateSpotlightPosition();

			// 创建一个Promise，等待打字机效果完成
			const waitForTypingCompletion = (): Promise<void> => {
				return new Promise((resolve) => {
					// 存储resolver到ref中，以便在打字完成时调用
					typingPromiseResolverRef.current = resolve;
				});
			};

			// 定义最小停留时间
			const minDisplayDuration = ANIMATION_DURATION.pause; // 最少停留4秒

			// 记录打字开始时间
			const typingStartTime = Date.now();

			try {
				// 等待打字机效果完成
				await waitForTypingCompletion();

				// 确保打字完成状态已更新
				setIsTypingCompleted(true);

				// 更新故事状态，表示内容已完全显示
				setStoryState(StoryState.POINT_CONTENT_DISPLAYED);

				// 重置动画锁定状态，允许其他动画操作执行
				isAnimatingRef.current = false;

				// 计算打字机效果已经耗费的时间
				const typingDuration = Date.now() - typingStartTime;
				logger.info("打字机效果完成", {
					typingDuration,
					minDisplayDuration,
				});

				// 如果还在自动播放中，才处理自动前进逻辑
				// mark todo for debug
				if (state.isPlaying) {
					// 计算还需等待的剩余时间，确保总停留时间不少于最小值
					const remainingTime = Math.max(
						0,
						minDisplayDuration - typingDuration,
					);

					logger.info("计算剩余等待时间", {
						typingDuration,
						minDisplayDuration,
						remainingTime,
					});

					// 设置新的计时器，等待剩余时间后再前进
					refs.currentPointDisplayTimeoutRef.current = setTimeout(
						async () => {
							if (isAnimatingRef.current) {
								logger.info("正在进行其他动画，跳过自动前进");
								return;
							}

							// 最后一个点位需要特殊处理
							if (pointIndex === points.length - 1) {
								logger.info(
									"已到达最后一个点位，准备结束演示",
									{
										pointIndex,
										totalPoints: points.length,
									},
								);
								await finishPresentation();
							} else {
								logger.info(
									"点位展示完成，准备前进到下一个点位",
								);
								// 如果仍在播放中，自动前进到下一个点位
								if (state.isPlaying) {
									const nextArrayIndex = pointIndex + 1;
									logger.info("自动前进到下一个点位", {
										currentIndex: pointIndex,
										nextIndex: nextArrayIndex,
									});
									await advanceToPoint(nextArrayIndex);
								}
							}
						},
						Math.max(remainingTime, 1500),
					);
				}
			} catch (error) {
				logger.error("等待打字机效果过程中发生错误", error);
			}
		},
		[
			storyState,
			clearPlayTimer,
			points,
			setCurrentPointIndex,
			setStoryState,
			setShowPointInfo,
			setShowImages,
			updateSpotlightPosition,
			state.isPlaying,
			isAnimatingRef,
			finishPresentation,
			advanceToPoint,
			typingPromiseResolverRef,
			setIsTypingCompleted,
		],
	);

	// 切换到下一个点位
	const goToNextPoint = useCallback(async () => {
		logger.debug("goToNextPoint: 开始切换到下一个点位", {
			storyState: state.storyState,
			isAnimating: isAnimatingRef.current,
			currentPointIndex: state.currentPointIndex,
		});
		// 如果有动画在进行或已完成则跳过
		if (
			isAnimatingRef.current ||
			storyState === StoryState.COMPLETED ||
			storyState === StoryState.ANIMATING_TO_COMPLETED_VIEW
		) {
			logger.info("goToNextPoint: 有动画在进行或已完成，跳过", {
				storyState: state.storyState,
				isAnimating: isAnimatingRef.current,
			});
			return;
		}

		// 清除任何正在进行的自动前进计时器
		clearPlayTimer();

		// 计算下一个点位的数组索引（currentPointIndex是1基的UI索引）
		const nextArrayIndex = currentPointIndex; // 当前UI索引对应的数组索引是currentPointIndex-1，所以下一个点的数组索引就是currentPointIndex

		// 如果已经超出点位数量则结束演示
		if (nextArrayIndex >= points.length) {
			logger.info("goToNextPoint: 已经是最后一个点位，开始结束动画", {
				currentPointIndex,
				totalPoints: points.length,
			});
			await finishPresentation();
		} else {
			// 如果是手动前进，通常需要暂停自动播放
			setIsPlaying(false);
			setManuallyReset(true);
			await advanceToPoint(nextArrayIndex);
		}
	}, [
		currentPointIndex,
		points.length,
		storyState,
		isAnimatingRef,
		clearPlayTimer,
		finishPresentation,
		setIsPlaying,
		advanceToPoint,
	]);

	// 切换到上一个点位
	const goToPrevPoint = useCallback(async () => {
		// 如果有动画在进行或已完成则跳过
		if (
			isAnimatingRef.current ||
			storyState === StoryState.COMPLETED ||
			storyState === StoryState.ANIMATING_TO_COMPLETED_VIEW
		) {
			return;
		}

		// 清除任何正在进行的自动前进计时器
		clearPlayTimer();

		// 计算上一个点位的数组索引
		const prevArrayIndex = currentPointIndex - 2; // currentPointIndex是1基的，-1后是当前点位的数组索引，再-1是上一个点位的数组索引

		// 验证索引是否有效
		if (prevArrayIndex < 0) {
			// 如果已经是第一个点位或概览状态，则返回概览状态
			logger.info("goToPrevPoint: 已经是第一个点位或概览状态", {
				currentPointIndex,
			});
			if (currentPointIndex === 1) {
				// 如果当前是第一个点位，则返回概览状态
				// 这里不能直接调用resetPresentation，否则会产生循环引用
				setCurrentPointIndex(0);
				setIsPlaying(false);
				clearPlayTimer();
				setFitBoundsToPoints(true);
				setShowPointInfo(false);
				setShowImages(false);
				setSpotlightPosition(null);
				setStoryState(StoryState.OVERVIEW);
			}
			return;
		}

		// 手动导航通常会暂停自动播放
		setIsPlaying(false);
		await advanceToPoint(prevArrayIndex);
	}, [
		currentPointIndex,
		storyState,
		isAnimatingRef,
		clearPlayTimer,
		setIsPlaying,
		advanceToPoint,
		setCurrentPointIndex,
		setFitBoundsToPoints,
		setShowPointInfo,
		setShowImages,
		setSpotlightPosition,
		setStoryState,
	]);

	// 重置演示
	const resetPresentation = useCallback(() => {
		// 重置所有状态
		setCurrentPointIndex(0);
		setIsPlaying(false);
		clearPlayTimer();
		setFitBoundsToPoints(true);
		setShowPointInfo(false);
		setShowImages(false);
		setSpotlightPosition(null);
		setStoryState(StoryState.OVERVIEW);
		setShowStartPrompt(false);
		setShowRestartPrompt(false);
		// 确保flyToPointIndex也被重置，避免下一次动画被跳过
		setFlyToPointIndex(null);

		// 重置内部控制状态
		isAnimatingRef.current = false;

		// 标记已经手动重置过，不再自动播放
		setManuallyReset(true);

		// 清除任何可能存在的自动播放定时器
		if (autoPlayTimerRef.current) {
			clearInterval(autoPlayTimerRef.current);
			autoPlayTimerRef.current = null;
		}

		// 将倒计时设为0而不是重置为3，避免显示倒计时提示
		setAutoPlayCountdown(0);

		// 调整地图视角以显示所有点位
		if (mapInstance && points.length > 0) {
			try {
				// 计算所有点位的边界
				const bounds = new mapboxgl.LngLatBounds();
				const validPoints = points.filter(
					(point) =>
						point.coordinates &&
						typeof point.coordinates.lng === "number" &&
						typeof point.coordinates.lat === "number",
				);

				if (validPoints.length > 0) {
					validPoints.forEach((point) => {
						bounds.extend([
							point.coordinates.lng,
							point.coordinates.lat,
						]);
					});

					// 调整地图视角
					mapInstance.fitBounds(bounds, {
						padding: 100,
						pitch: 0,
						bearing: 0,
						duration: ANIMATION_DURATION.overviewFit,
					});
				}
			} catch (error) {
				logger.error("resetPresentation: 调整地图视角失败", error);
			}
		}
	}, [
		clearPlayTimer,
		setCurrentPointIndex,
		setIsPlaying,
		setFitBoundsToPoints,
		setShowPointInfo,
		setShowImages,
		setSpotlightPosition,
		setStoryState,
		setShowStartPrompt,
		setShowRestartPrompt,
		isAnimatingRef,
		setManuallyReset,
		autoPlayTimerRef,
		setAutoPlayCountdown,
		mapInstance,
		points,
		setFlyToPointIndex,
	]);

	// 开始旅程
	const startJourney = useCallback(async () => {
		// 检查当前状态和条件
		if (storyState !== StoryState.OVERVIEW || points.length === 0) {
			logger.info(
				"startJourney: 无法开始旅程，状态或点位数量不满足条件",
				{
					storyState,
					pointsCount: points.length,
					isAnimating: isAnimatingRef.current,
				},
			);
			return;
		}

		// 重置动画状态，确保不会被之前的动画锁定阻止
		isAnimatingRef.current = false;

		// 标记动画开始
		logger.info("startJourney: 开始旅程", {
			timestamp: Date.now(),
			storyState,
			pointsCount: points.length,
		});

		// 重置手动重置标记（如果用户手动开始，则下次仍会自动播放）
		setManuallyReset(false);

		// 清除自动开始计时器
		if (autoPlayTimerRef.current) {
			clearInterval(autoPlayTimerRef.current);
			autoPlayTimerRef.current = null;
		}

		// 清除任何可能的播放计时器
		clearPlayTimer();

		// 隐藏开始提示
		setShowStartPrompt(false);

		// 准备地图状态和UI状态
		setCurrentPointIndex(0);
		setStoryState(StoryState.INITIALIZING);
		setIsPlaying(true);

		// 使用setTimeout确保状态更新后再触发动画
		setTimeout(async () => {
			// 如果有地图实例，执行相机动画
			if (mapInstance && points.length > 0) {
				const firstPoint = points[0];

				// 验证第一个点位的坐标
				if (
					!firstPoint?.coordinates ||
					typeof firstPoint.coordinates.lng !== "number" ||
					typeof firstPoint.coordinates.lat !== "number"
				) {
					logger.error("startJourney: 第一个点位坐标无效", {
						coordinates: firstPoint?.coordinates,
					});
					// 出错时直接切换状态，跳过动画
					isAnimatingRef.current = false;
					setStoryState(StoryState.PLAYING);
					setCurrentPointIndex(1);
					setIsPlaying(true);
					return;
				}

				try {
					// 第一段动画：抬高视角
					await new Promise<void>((resolve, reject) => {
						try {
							mapInstance.flyTo({
								center: mapInstance.getCenter(),
								zoom: 2,
								pitch: 0,
								bearing: 0,
								duration: 2000,
								essential: true,
							});

							mapInstance.once("moveend", () => resolve());

							// 安全超时
							setTimeout(() => resolve(), 2000);
						} catch (error) {
							reject(error);
						}
					});

					// 第二段动画：降落到第一个点位（通过advanceToPoint）
					// 设置播放状态并开始前进到第一个点位
					setStoryState(StoryState.PLAYING);

					// 确保我们正在这里运行的代码不会干扰动画流程
					logger.info(
						"startJourney: 第一段动画完成，准备前往第一个点位",
						{
							isAnimating: isAnimatingRef.current,
						},
					);

					// 调用advanceToPoint前确保锁定已经重置
					isAnimatingRef.current = false;

					// 开始导航到第一个点位
					await advanceToPoint(0);
				} catch (error) {
					logger.error("startJourney: 相机动画失败", error);
					// 出错时直接切换状态
					isAnimatingRef.current = false;
					setStoryState(StoryState.PLAYING);
					setCurrentPointIndex(1);
					setIsPlaying(true);
				}
			} else {
				// 没有地图实例时直接切换状态
				logger.warn("startJourney: 没有有效的地图实例，跳过动画", {
					hasMapInstance: !!mapInstance,
					pointsCount: points.length,
				});
				isAnimatingRef.current = false;
				setStoryState(StoryState.PLAYING);
				setCurrentPointIndex(1);
				setIsPlaying(true);
			}
		}, 50); // 50ms的延迟确保React状态已更新
	}, [
		storyState,
		points.length,
		mapInstance,
		isAnimatingRef,
		setManuallyReset,
		autoPlayTimerRef,
		clearPlayTimer,
		setShowStartPrompt,
		setCurrentPointIndex,
		setStoryState,
		setIsPlaying,
		advanceToPoint,
	]);

	// 查看旅行回忆
	const viewMemories = useCallback(() => {
		onComplete();
	}, [onComplete]);

	return {
		handlePointArrival,
		goToNextPoint,
		goToPrevPoint,
		startJourney,
		resetPresentation,
		viewMemories,
	};
}
