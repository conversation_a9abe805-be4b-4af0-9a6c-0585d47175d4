"use client";

let isGoogleMapsLoaded = false;
let loadingPromise: Promise<void> | null = null;

/**
 * 检查Google Maps API是否已加载
 */
export function checkIfGoogleMapsLoaded(): boolean {
	return typeof window !== "undefined" && Boolean(window.google?.maps);
}

/**
 * 加载Google Maps API
 * 使用单例模式确保API只加载一次
 */
export function loadGoogleMapsApi(): Promise<void> {
	// 如果已加载，返回已解决的Promise
	if (checkIfGoogleMapsLoaded()) {
		isGoogleMapsLoaded = true;
		return Promise.resolve();
	}

	// 如果正在加载中，返回现有的Promise
	if (loadingPromise) {
		return loadingPromise;
	}

	// 开始新的加载过程
	loadingPromise = new Promise<void>((resolve) => {
		// 检查是否已存在脚本标签
		const existingScript = document.querySelector(
			'script[src*="maps.googleapis.com/maps/api/js"]',
		);

		if (!existingScript) {
			// 加载Google Maps API
			const googleMapsScript = document.createElement("script");
			googleMapsScript.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=geometry&v=weekly`;
			googleMapsScript.async = true;
			googleMapsScript.defer = true;
			document.head.appendChild(googleMapsScript);

			googleMapsScript.onload = () => {
				console.log("Google Maps API 加载完成");
				isGoogleMapsLoaded = true;
				resolve();
			};
		} else {
			// 如果脚本存在但API尚未初始化，等待初始化
			const checkGoogleMaps = () => {
				if (checkIfGoogleMapsLoaded()) {
					console.log("检测到现有的Google Maps API");
					isGoogleMapsLoaded = true;
					resolve();
				} else {
					setTimeout(checkGoogleMaps, 100);
				}
			};
			checkGoogleMaps();
		}
	});

	return loadingPromise;
}
