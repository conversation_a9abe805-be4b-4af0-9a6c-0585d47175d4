"use client";

import { Fireworks } from "fireworks-js";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";

// 确定性伪随机数生成器
function seededRandom(seed: number) {
	const x = Math.sin(seed) * 10000;
	return x - Math.floor(x);
}

// 地球位置计算工具
export function calculateGlobePosition(
	containerWidth: number,
	containerHeight: number,
	mapCenter?: [number, number], // 地图中心经纬度
	mapZoom?: number, // 地图缩放级别
): { center: { x: number; y: number }; radius: number } {
	// 默认地球居中显示
	const centerX = 50; // 百分比
	const centerY = 50; // 百分比
	let radius = 35; // 百分比

	// 如果提供了地图参数，可以根据地图状态调整
	if (mapCenter && mapZoom) {
		// 根据地图缩放级别调整地球大小
		// 缩放级别越高，地球显示越大
		radius = Math.min(45, 25 + (mapZoom - 1) * 2);

		// 可以根据需要调整地球中心位置
		// 这里保持居中，但可以根据实际需求调整
	}

	return {
		center: { x: centerX, y: centerY },
		radius: radius,
	};
}

// 动态地球位置Hook
export function useGlobePosition(
	mapRef?: React.RefObject<any>, // Mapbox地图引用
	enabled = false,
) {
	const [globePosition, setGlobePosition] = useState({
		center: { x: 50, y: 50 },
		radius: 35,
	});

	const updateGlobePosition = useCallback(() => {
		if (!enabled || !mapRef?.current) return;

		try {
			const map = mapRef.current;
			const container = map.getContainer();
			if (!container) return;

			const { clientWidth, clientHeight } = container;
			const center = map.getCenter();
			const zoom = map.getZoom();

			const position = calculateGlobePosition(
				clientWidth,
				clientHeight,
				[center.lng, center.lat],
				zoom,
			);

			setGlobePosition(position);
		} catch (error) {
			console.warn("Failed to update globe position:", error);
		}
	}, [enabled, mapRef]);

	useEffect(() => {
		if (!enabled || !mapRef?.current) return;

		const map = mapRef.current;

		// 监听地图变化事件
		map.on("move", updateGlobePosition);
		map.on("zoom", updateGlobePosition);
		map.on("resize", updateGlobePosition);

		// 初始化位置
		updateGlobePosition();

		return () => {
			map.off("move", updateGlobePosition);
			map.off("zoom", updateGlobePosition);
			map.off("resize", updateGlobePosition);
		};
	}, [enabled, mapRef, updateGlobePosition]);

	return globePosition;
}

// 动画主题类型定义
export type AnimationTheme =
	| "shooting-stars"
	| "floating-particles"
	| "aurora"
	| "minimal"
	| "galaxy"
	| "none";

// 动画配置接口
export interface AnimationConfig {
	theme: AnimationTheme;
	intensity: "low" | "medium" | "high";
	colors: {
		primary: string;
		secondary: string;
		accent: string;
	};
	speed: number; // 动画速度倍数
}

// 预设主题配置
const ANIMATION_THEMES: Record<
	AnimationTheme,
	Omit<AnimationConfig, "theme">
> = {
	"shooting-stars": {
		intensity: "medium",
		colors: {
			primary: "#5f91ff", // 清新蓝色
			secondary: "#ffffff", // 纯白色
			accent: "#87CEEB", // 天空蓝
		},
		speed: 1,
	},
	"floating-particles": {
		intensity: "low",
		colors: {
			primary: "#E6E6FA", // 薰衣草
			secondary: "#DDA0DD", // 梅红
			accent: "#D8BFD8", // 蓟色
		},
		speed: 0.8,
	},
	aurora: {
		intensity: "medium",
		colors: {
			primary: "#E8F4FD", // 淡蓝色
			secondary: "#F0F8E8", // 淡绿色
			accent: "#FFF8E8", // 淡黄色
		},
		speed: 0.8,
	},
	minimal: {
		intensity: "low",
		colors: {
			primary: "#F0F8FF", // 爱丽丝蓝
			secondary: "#F5F5DC", // 米色
			accent: "#FFFAF0", // 花白
		},
		speed: 0.6,
	},
	galaxy: {
		intensity: "high",
		colors: {
			primary: "#4B0082", // 靛蓝
			secondary: "#8A2BE2", // 蓝紫
			accent: "#9370DB", // 中紫
		},
		speed: 1.5,
	},
	none: {
		intensity: "low",
		colors: {
			primary: "transparent",
			secondary: "transparent",
			accent: "transparent",
		},
		speed: 0,
	},
};

// 主题名称映射
export const THEME_NAMES: Record<AnimationTheme, string> = {
	"shooting-stars": "流星雨",
	"floating-particles": "浮动粒子",
	aurora: "烟花",
	minimal: "简约",
	galaxy: "银河",
	none: "无动画",
};

interface BackgroundAnimationsProps {
	theme?: AnimationTheme;
	config?: Partial<AnimationConfig>;
	className?: string;
	foreground?: boolean; // 新增：是否为前景模式
	maskGlobe?: boolean; // 新增：是否遮罩地球区域
	globeCenter?: { x: number; y: number }; // 地球中心位置
	globeRadius?: number; // 地球半径
	style?: React.CSSProperties; // 新增：自定义样式
}

export function BackgroundAnimations({
	theme = "shooting-stars",
	config,
	className = "",
	foreground = false, // 默认为背景模式
	maskGlobe = false, // 默认不遮罩地球
	globeCenter = { x: 50, y: 50 }, // 默认地球中心位置（百分比）
	globeRadius = 40, // 默认地球半径（百分比）
	style, // 新增：自定义样式
}: BackgroundAnimationsProps) {
	const [isMounted, setIsMounted] = useState(false);

	// 确保只在客户端渲染
	useEffect(() => {
		setIsMounted(true);
	}, []);

	const animationConfig = useMemo(() => {
		const baseConfig = ANIMATION_THEMES[theme];
		const finalConfig = {
			...baseConfig,
			theme,
			...config,
			colors: {
				...baseConfig.colors,
				...config?.colors,
			},
		} as AnimationConfig;

		// 如果是前景模式，增强颜色和对比度
		if (foreground) {
			finalConfig.colors = {
				primary: baseConfig.colors.primary,
				secondary: baseConfig.colors.secondary,
				accent: baseConfig.colors.accent,
			};
			// 可以根据需要调整前景模式的强度
			if (finalConfig.intensity === "low")
				finalConfig.intensity = "medium";
			else if (finalConfig.intensity === "medium")
				finalConfig.intensity = "high";
		}

		return finalConfig;
	}, [theme, config, foreground]);

	if (theme === "none" || !isMounted) {
		return null;
	}

	// 创建地球遮罩的CSS样式
	const globeMaskStyle = maskGlobe
		? {
				WebkitMask: `radial-gradient(circle at ${globeCenter.x}% ${globeCenter.y}%, transparent ${globeRadius}%, black ${globeRadius + 5}%)`,
				mask: `radial-gradient(circle at ${globeCenter.x}% ${globeCenter.y}%, transparent ${globeRadius}%, black ${globeRadius + 5}%)`,
			}
		: {};

	return (
		<div
			className={`absolute inset-0 pointer-events-none overflow-hidden ${className}`}
			style={{
				...globeMaskStyle,
				...style, // 让外部传入的style覆盖默认设置
			}}
		>
			{/* 渲染对应主题的动画 */}
			{theme === "shooting-stars" && (
				<ShootingStarsAnimation config={animationConfig} />
			)}
			{theme === "floating-particles" && (
				<FloatingParticlesAnimation config={animationConfig} />
			)}
			{theme === "aurora" && <AuroraAnimation config={animationConfig} />}
			{theme === "minimal" && (
				<MinimalAnimation config={animationConfig} />
			)}
			{theme === "galaxy" && <GalaxyAnimation config={animationConfig} />}

			{/* 动画样式 */}
			<AnimationStyles config={animationConfig} />
		</div>
	);
}

// 流星雨动画组件
function ShootingStarsAnimation({ config }: { config: AnimationConfig }) {
	const meteorCount =
		config.intensity === "low" ? 3 : config.intensity === "medium" ? 6 : 10;
	const starCount =
		config.intensity === "low"
			? 50
			: config.intensity === "medium"
				? 80
				: 120;

	return (
		<>
			{/* 十字星星 - 参考实现 */}
			<div className="absolute inset-0">
				{Array.from({ length: starCount }).map((_, i) => {
					// 使用确定性位置计算
					const topPos = (seededRandom(i * 13.7 + 42) * 100).toFixed(
						1,
					);
					const leftPos = (seededRandom(i * 17.3 + 89) * 100).toFixed(
						1,
					);
					const duration = (
						2 +
						seededRandom(i * 11.1 + 156) * 4
					).toFixed(1);
					const delay = (seededRandom(i * 19.7 + 234) * 8).toFixed(1);
					const scale = (
						0.3 +
						seededRandom(i * 23.9 + 178) * 0.7
					).toFixed(2);
					const colorChoice = seededRandom(i * 29.1 + 345);
					const starColor =
						colorChoice > 0.7
							? config.colors.primary
							: colorChoice > 0.4
								? config.colors.secondary
								: "#ffffff";

					return (
						<div
							key={`star-${i}`}
							className="absolute star-container"
							style={{
								top: `${topPos}%`,
								left: `${leftPos}%`,
								transform: `scale(${scale})`,
							}}
						>
							{/* 横条 */}
							<div
								className="absolute star-horizontal"
								style={{
									width: "30px",
									height: "2px",
									borderRadius: "100%",
									background: `linear-gradient(-45deg, transparent, ${starColor}, transparent)`,
									animation: `star-shining ${duration}s ease-in-out infinite`,
									animationDelay: `${delay}s`,
									transformOrigin: "center",
								}}
							>
								{/* 竖条 */}
								<div
									className="absolute star-vertical"
									style={{
										top: "-14px",
										left: "14px",
										width: "2px",
										height: "30px",
										borderRadius: "100%",
										background: `linear-gradient(-45deg, transparent, ${starColor}, transparent)`,
										animation: `star-shining ${duration}s ease-in-out infinite`,
										animationDelay: `${delay}s`,
										transformOrigin: "center",
									}}
								/>
							</div>
						</div>
					);
				})}
			</div>

			{/* 流星效果 - 优化版 */}
			<div className="absolute inset-0">
				{Array.from({ length: meteorCount }).map((_, i) => {
					const topStart = (
						seededRandom(i * 47.3 + 567) * 40 -
						20
					).toFixed(1); // -20% to 20%
					const leftStart = (
						50 +
						seededRandom(i * 53.7 + 678) * 50
					).toFixed(1); // 50% to 100%
					const duration = (
						8 +
						seededRandom(i * 59.1 + 789) * 12
					).toFixed(1); // 8s to 20s
					const delay = (
						i * 3 +
						seededRandom(i * 61.3 + 890) * 5
					).toFixed(1);
					const scale = (
						0.5 +
						seededRandom(i * 67.7 + 901) * 0.5
					).toFixed(2);
					const meteorColor =
						seededRandom(i * 71.1 + 123) > 0.5
							? "#ffffff"
							: config.colors.primary;

					return (
						<div
							key={`meteor-${i}`}
							className="absolute meteor-container"
							style={{
								top: `${topStart}%`,
								left: `${leftStart}%`,
								transform: `scale(${scale})`,
								animation: `meteor-fly-${i % 3} ${duration}s linear infinite`,
								animationDelay: `${delay}s`,
							}}
						>
							{/* 流星主体 - 自然流体形状 */}
							<div
								className="absolute meteor-stream"
								style={{
									width: "200px",
									height: "8px",
									borderRadius: "4px 0 0 4px",
									background: `linear-gradient(90deg, 
										${meteorColor} 0%, 
										${meteorColor}f8 5%, 
										${meteorColor}e0 12%, 
										${meteorColor}c0 25%, 
										${meteorColor}90 45%, 
										${meteorColor}60 65%, 
										${meteorColor}30 85%, 
										transparent 100%)`,
									boxShadow: `
										0 0 16px ${meteorColor}90,
										0 0 32px ${meteorColor}60,
										0 0 48px ${meteorColor}30
									`,
									transform: "translateY(-50%)",
									clipPath:
										"polygon(0 50%, 100% 0, 100% 100%)",
								}}
							/>

							{/* 流星核心 - 最亮的前端 */}
							<div
								className="absolute meteor-head"
								style={{
									width: "50px",
									height: "4px",
									borderRadius: "2px",
									background: `linear-gradient(90deg, 
										#ffffff 0%, 
										#ffffff 15%, 
										${meteorColor} 40%, 
										${meteorColor}d0 70%, 
										${meteorColor}80 100%)`,
									boxShadow: `
										0 0 12px #ffffff90,
										0 0 20px ${meteorColor}b0,
										0 0 28px ${meteorColor}70
									`,
									transform: "translateY(-50%)",
									zIndex: 2,
								}}
							/>

							{/* 流星外层光晕 - 柔和扩散 */}
							<div
								className="absolute meteor-aura"
								style={{
									width: "250px",
									height: "16px",
									borderRadius: "8px",
									background: `radial-gradient(ellipse 100% 50% at 0% 50%, 
										${meteorColor}50 0%, 
										${meteorColor}30 20%, 
										${meteorColor}20 40%, 
										${meteorColor}10 60%, 
										${meteorColor}05 80%, 
										transparent 100%)`,
									transform: "translateY(-50%)",
									filter: "blur(3px)",
									zIndex: -1,
								}}
							/>

							{/* 流星闪烁效果 */}
							<div
								className="absolute meteor-sparkle"
								style={{
									width: "30px",
									height: "2px",
									borderRadius: "1px",
									background: `linear-gradient(90deg, 
										#ffffff 0%, 
										${meteorColor} 60%, 
										transparent 100%)`,
									transform: "translateY(-50%)",
									opacity: 0.8,
									animation:
										"meteor-sparkle 0.5s ease-in-out infinite alternate",
									zIndex: 3,
								}}
							/>
						</div>
					);
				})}
			</div>

			{/* 浮动云朵效果 */}
			<FloatingClouds config={config} />
		</>
	);
}

// 浮动粒子动画组件
function FloatingParticlesAnimation({ config }: { config: AnimationConfig }) {
	const particleCount =
		config.intensity === "low"
			? 25
			: config.intensity === "medium"
				? 45
				: 70; // 大幅增加粒子数量

	return (
		<div className="absolute inset-0">
			{Array.from({ length: particleCount }).map((_, i) => {
				// 使用确定性计算
				const width = (4 + seededRandom(i * 15.3 + 67) * 8).toFixed(1); // 增大粒子尺寸
				const height = width; // 保持圆形
				const topPos = (seededRandom(i * 21.7 + 123) * 100).toFixed(1);
				const leftPos = (seededRandom(i * 18.9 + 234) * 100).toFixed(1);
				const duration = (3 + seededRandom(i * 14.2 + 345) * 3).toFixed(
					1,
				); // 更快的移动
				const delay = (seededRandom(i * 16.8 + 456) * 4).toFixed(1);
				const opacity = (
					0.5 +
					seededRandom(i * 12.4 + 567) * 0.5
				).toFixed(2); // 更明显的透明度
				const blurSize = Number(
					(12 + seededRandom(i * 13.6 + 678) * 16).toFixed(1),
				); // 更大的光晕

				return (
					<div
						key={`particle-${i}`}
						className="absolute rounded-full"
						style={{
							width: `${width}px`,
							height: `${height}px`,
							top: `${topPos}%`,
							left: `${leftPos}%`,
							background: `radial-gradient(circle, ${config.colors.primary}aa, ${config.colors.secondary}66, transparent)`, // 增强颜色
							animation: `float-particle-${i % 3} ${duration}s ease-in-out infinite`,
							animationDelay: `${delay}s`,
							opacity: Number(opacity),
							boxShadow: `
								0 0 ${blurSize}px ${config.colors.primary},
								0 0 ${blurSize * 2}px ${config.colors.primary}40,
								0 0 ${blurSize * 3}px ${config.colors.secondary}20
							`, // 多层光晕效果
						}}
					/>
				);
			})}
		</div>
	);
}

// 烟花动画组件
function AuroraAnimation({ config }: { config: AnimationConfig }) {
	const containerRef = useRef<HTMLDivElement>(null);
	const fireworksRef = useRef<Fireworks | null>(null);

	useEffect(() => {
		if (!containerRef.current) return;

		// 清理之前的实例
		if (fireworksRef.current) {
			fireworksRef.current.stop();
			fireworksRef.current = null;
		}

		// 清新色彩的色相范围 - 涵盖蓝色、粉色、黄色
		const hueRange = {
			min: 80, // 从红色开始，包含粉色到黄色的完整色谱
			max: 240, // 完整色环，让烟花随机展现清新色彩
		};

		// 根据强度设置参数 - 柔和版本
		const intensitySettings = {
			low: { particles: 15, intensity: 8, explosion: 2 },
			medium: { particles: 25, intensity: 12, explosion: 3 },
			high: { particles: 35, intensity: 18, explosion: 4 },
		};

		const settings = intensitySettings[config.intensity];

		// 创建烟花实例 - 柔和自然配置
		const fireworks = new Fireworks(containerRef.current, {
			opacity: 0.25, // 适中的透明度，让清新色彩更明显
			acceleration: 1.01, // 更轻柔的加速度
			friction: 0.98, // 更高的摩擦力
			gravity: 2, // 更轻的重力
			particles: settings.particles,
			traceLength: 2, // 更长的轨迹
			traceSpeed: 2, // 更慢的轨迹速度
			explosion: settings.explosion,
			intensity: settings.intensity,
			flickering: 30, // 更柔和的闪烁
			lineStyle: "round",
			hue: hueRange,
			delay: {
				min: 40, // 更长的延迟
				max: 80,
			},
			rocketsPoint: {
				min: 50,
				max: 50,
			},
			lineWidth: {
				explosion: {
					min: 0.5, // 更细的线条
					max: 2,
				},
				trace: {
					min: 0.5,
					max: 1.5,
				},
			},
			brightness: {
				min: 50, // 清新明亮的色彩
				max: 85,
			},
			decay: {
				min: 0.005, // 更慢的衰减
				max: 0.015,
			},
			mouse: {
				click: false,
				move: false,
				max: 1,
			},
			boundaries: {
				x: 0,
				y: 0,
				width: containerRef.current.offsetWidth,
				height: containerRef.current.offsetHeight,
			},
		});

		fireworksRef.current = fireworks;
		fireworks.start();

		// 清理函数
		return () => {
			if (fireworksRef.current) {
				fireworksRef.current.stop(true);
				fireworksRef.current = null;
			}
		};
	}, [config]);

	// 处理窗口大小变化
	useEffect(() => {
		const handleResize = () => {
			if (fireworksRef.current && containerRef.current) {
				fireworksRef.current.updateSize({
					width: containerRef.current.offsetWidth,
					height: containerRef.current.offsetHeight,
				});
			}
		};

		window.addEventListener("resize", handleResize);
		return () => window.removeEventListener("resize", handleResize);
	}, []);

	return (
		<div
			ref={containerRef}
			className="absolute inset-0 pointer-events-none"
			style={{
				background: "transparent",
				overflow: "hidden",
			}}
		/>
	);
}

// 简约动画组件
function MinimalAnimation({ config }: { config: AnimationConfig }) {
	return (
		<div className="absolute inset-0">
			{/* 简单的浮动点 - 增强版 */}
			{Array.from({ length: 15 }).map((_, i) => {
				// 增加粒子数量
				// 使用确定性计算
				const topPos = (15 + seededRandom(i * 25.3 + 111) * 70).toFixed(
					1,
				); // 更大的分布范围
				const leftPos = (
					15 +
					seededRandom(i * 27.8 + 222) * 70
				).toFixed(1);
				const duration = (4 + i * 0.5).toString(); // 更快的动画
				const delay = (i * 1.5).toString();
				const opacity = (
					0.4 +
					seededRandom(i * 31.4 + 333) * 0.6
				).toFixed(2); // 更明显的透明度
				const size = seededRandom(i * 33.8 + 444) > 0.7 ? 2 : 1; // 部分粒子更大

				return (
					<div
						key={`minimal-${i}`}
						className={`absolute w-${size} h-${size} rounded-full`}
						style={{
							top: `${topPos}%`,
							left: `${leftPos}%`,
							background: `radial-gradient(circle, ${config.colors.primary}dd, ${config.colors.primary}66)`,
							animation: `minimal-float ${duration}s ease-in-out infinite`,
							animationDelay: `${delay}s`,
							opacity: Number(opacity),
							boxShadow: `0 0 ${size * 8}px ${config.colors.primary}80`,
						}}
					/>
				);
			})}
		</div>
	);
}

// 银河动画组件
function GalaxyAnimation({ config }: { config: AnimationConfig }) {
	return (
		<div className="absolute inset-0">
			{/* 银河旋转背景 - 加强版 */}
			<div
				className="absolute inset-0"
				style={{
					background: `radial-gradient(ellipse at center, 
						${config.colors.primary}40 0%, 
						${config.colors.secondary}25 30%, 
						${config.colors.accent}15 60%, 
						transparent 100%)`,
					animation: "galaxy-rotation 45s linear infinite", // 更快的旋转
					transformOrigin: "center center",
				}}
			/>

			{/* 银河螺旋臂 */}
			{Array.from({ length: 3 }).map((_, i) => (
				<div
					key={`spiral-${i}`}
					className="absolute inset-0"
					style={{
						background: `conic-gradient(from ${i * 120}deg at center, 
							transparent 0%, 
							${config.colors.primary}20 20%, 
							transparent 40%,
							${config.colors.secondary}20 60%,
							transparent 80%,
							${config.colors.accent}20 100%)`,
						animation: `galaxy-rotation ${60 + i * 10}s linear infinite`,
						animationDelay: `${i * 5}s`,
						transformOrigin: "center center",
					}}
				/>
			))}

			{/* 星座点 - 大幅增强 */}
			{Array.from({ length: 80 }).map((_, i) => {
				// 增加到80颗星
				// 使用确定性计算
				const width = (2 + seededRandom(i * 29.3 + 444) * 6).toFixed(1); // 更大的星星
				const height = width; // 保持圆形
				const topPos = (seededRandom(i * 33.7 + 555) * 100).toFixed(1);
				const leftPos = (seededRandom(i * 37.1 + 666) * 100).toFixed(1);
				const duration = (
					0.8 +
					seededRandom(i * 41.5 + 777) * 1.5
				).toFixed(1); // 更快的闪烁
				const delay = (seededRandom(i * 43.9 + 888) * 3).toFixed(1);
				const opacity = (
					0.5 +
					seededRandom(i * 47.3 + 999) * 0.5
				).toFixed(2); // 更亮的星星
				const blurSize = Number(
					(8 + seededRandom(i * 51.7 + 1111) * 16).toFixed(1),
				); // 更大的光晕

				return (
					<div
						key={`galaxy-star-${i}`}
						className="absolute rounded-full"
						style={{
							width: `${width}px`,
							height: `${height}px`,
							top: `${topPos}%`,
							left: `${leftPos}%`,
							background: `radial-gradient(circle, #ffffff, ${config.colors.accent}aa, transparent)`,
							animation: `galaxy-twinkle ${duration}s ease-in-out infinite`,
							animationDelay: `${delay}s`,
							opacity: Number(opacity),
							boxShadow: `
								0 0 ${blurSize}px ${config.colors.accent},
								0 0 ${blurSize * 2}px ${config.colors.primary}60,
								0 0 ${blurSize * 3}px ${config.colors.secondary}40
							`, // 多层光晕效果
						}}
					/>
				);
			})}

			{/* 超新星效果 */}
			{Array.from({ length: 5 }).map((_, i) => {
				const topPos = (seededRandom(i * 67.3 + 1234) * 100).toFixed(1);
				const leftPos = (seededRandom(i * 73.7 + 5678) * 100).toFixed(
					1,
				);
				const duration = (8 + i * 2).toString();
				const delay = (i * 4).toString();
				const size = 12 + i * 4;

				return (
					<div
						key={`supernova-${i}`}
						className="absolute rounded-full"
						style={{
							width: `${size}px`,
							height: `${size}px`,
							top: `${topPos}%`,
							left: `${leftPos}%`,
							background: `radial-gradient(circle, #ffffff, ${config.colors.primary}cc, transparent)`,
							animation: `supernova-pulse ${duration}s ease-in-out infinite`,
							animationDelay: `${delay}s`,
							boxShadow: `
								0 0 ${size * 2}px #ffffff,
								0 0 ${size * 4}px ${config.colors.primary},
								0 0 ${size * 8}px ${config.colors.secondary}60
							`,
						}}
					/>
				);
			})}
		</div>
	);
}

// 浮动云朵组件
function FloatingClouds({ config }: { config: AnimationConfig }) {
	const cloudCount =
		config.intensity === "low" ? 3 : config.intensity === "medium" ? 4 : 6;

	return (
		<>
			{Array.from({ length: cloudCount }).map((_, i) => (
				<div
					key={`cloud-${i}`}
					className={"absolute rounded-full blur-xl"}
					style={{
						width: `${16 + i * 8}px`,
						height: `${16 + i * 8}px`,
						top: `${20 + ((i * 20) % 60)}%`,
						left: `${10 + ((i * 25) % 80)}%`,
						background: `${config.colors.secondary}15`,
						animation: `float-cloud-${i % 3} ${4 + i * 2}s ease-in-out infinite`,
						animationDelay: `${i * 2}s`,
					}}
				/>
			))}
		</>
	);
}

// 动画样式组件
function AnimationStyles({ config }: { config: AnimationConfig }) {
	const speedMultiplier = config.speed;

	return (
		<style jsx>{`
			/* 十字星星闪烁动画 */
			@keyframes star-shining {
				0% { transform: scale(0); }
				50% { transform: scale(1); }
				100% { transform: scale(0); }
			}

			/* 流星飞行动画 - 减少重力效果 */
			@keyframes meteor-fly-0 {
				0% { 
					opacity: 0; 
					transform: rotate(325deg) translateX(0) translateY(0); 
				}
				10% { opacity: 1; }
				90% { opacity: 1; }
				100% { 
					opacity: 0; 
					transform: rotate(325deg) translateX(-180vw) translateY(80vh); 
				}
			}

			@keyframes meteor-fly-1 {
				0% { 
					opacity: 0; 
					transform: rotate(330deg) translateX(0) translateY(0); 
				}
				8% { opacity: 1; }
				88% { opacity: 1; }
				100% { 
					opacity: 0; 
					transform: rotate(330deg) translateX(-170vw) translateY(75vh); 
				}
			}

			@keyframes meteor-fly-2 {
				0% { 
					opacity: 0; 
					transform: rotate(320deg) translateX(0) translateY(0); 
				}
				12% { opacity: 1; }
				85% { opacity: 1; }
				100% { 
					opacity: 0; 
					transform: rotate(320deg) translateX(-190vw) translateY(85vh); 
				}
			}

			/* 流星闪烁动画 */
			@keyframes meteor-sparkle {
				0% { 
					opacity: 0.6; 
					transform: translateY(-50%) scaleX(0.8); 
				}
				100% { 
					opacity: 1; 
					transform: translateY(-50%) scaleX(1.2); 
				}
			}

			/* 浮动粒子动画 */
			@keyframes float-particle-0 {
				0%, 100% { transform: translateY(0px) translateX(0px) scale(1); }
				25% { transform: translateY(-30px) translateX(20px) scale(1.2); }
				50% { transform: translateY(-60px) translateX(-15px) scale(0.8); }
				75% { transform: translateY(-25px) translateX(-30px) scale(1.1); }
			}

			@keyframes float-particle-1 {
				0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg) scale(1); }
				33% { transform: translateY(-40px) translateX(25px) rotate(120deg) scale(1.3); }
				66% { transform: translateY(-15px) translateX(-20px) rotate(240deg) scale(0.9); }
			}

			@keyframes float-particle-2 {
				0%, 100% { transform: translateY(0px) translateX(0px) scale(1); }
				50% { transform: translateY(-45px) translateX(15px) scale(1.4); }
			}



			/* 简约动画 */
			@keyframes minimal-float {
				0%, 100% { 
					transform: translateY(0px) scale(1); 
					opacity: 0.4; 
					filter: brightness(1);
				}
				25% { 
					transform: translateY(-30px) scale(1.2); 
					opacity: 0.8; 
					filter: brightness(1.5);
				}
				50% { 
					transform: translateY(-50px) scale(1.4); 
					opacity: 1; 
					filter: brightness(2);
				}
				75% { 
					transform: translateY(-20px) scale(1.1); 
					opacity: 0.7; 
					filter: brightness(1.2);
				}
			}

			/* 银河动画 */
			@keyframes galaxy-rotation {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}

			@keyframes galaxy-twinkle {
				0%, 100% { opacity: 0.3; transform: scale(0.8); }
				50% { opacity: 1; transform: scale(1.8); }
			}

			/* 超新星脉冲动画 */
			@keyframes supernova-pulse {
				0%, 100% { 
					opacity: 0.2; 
					transform: scale(0.5); 
					filter: brightness(1); 
				}
				25% { 
					opacity: 0.8; 
					transform: scale(1.5); 
					filter: brightness(2); 
				}
				50% { 
					opacity: 1; 
					transform: scale(2.5); 
					filter: brightness(3); 
				}
				75% { 
					opacity: 0.6; 
					transform: scale(1.8); 
					filter: brightness(1.5); 
				}
			}

			/* 云朵浮动动画 */
			@keyframes float-cloud-0 {
				0%, 100% { transform: translateY(0px) translateX(0px) scale(1); }
				33% { transform: translateY(-20px) translateX(10px) scale(1.05); }
				66% { transform: translateY(-10px) translateX(-10px) scale(0.95); }
			}

			@keyframes float-cloud-1 {
				0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg); }
				50% { transform: translateY(-25px) translateX(15px) rotate(5deg); }
			}

			@keyframes float-cloud-2 {
				0%, 100% { transform: translateY(0px) translateX(0px); }
				25% { transform: translateY(-15px) translateX(-8px); }
				50% { transform: translateY(-30px) translateX(8px); }
				75% { transform: translateY(-10px) translateX(-5px); }
			}

			/* 动画速度调整 */
			* {
				animation-duration: calc(var(--animation-duration, 1s) / ${speedMultiplier}) !important;
			}
		`}</style>
	);
}
