/**
 * 百度地图地理编码提供商
 */

import { BaseGeocodingProvider } from "../base-provider";
import type {
	GeocodingOptions,
	GeocodingProvider,
	UnifiedGeocodingResult,
} from "../types";

/**
 * 百度地图地理编码提供商
 */
export class BaiduProvider extends BaseGeocodingProvider {
	private readonly baseUrl = "https://api.map.baidu.com/geocoding/v3";

	getProviderType(): GeocodingProvider {
		return "baidu";
	}

	protected async performGeocode(
		address: string,
		options: GeocodingOptions,
	): Promise<UnifiedGeocodingResult | null> {
		const params = new URLSearchParams({
			address: address,
			ak: this.config.apiKey,
			output: "json",
		});

		// 添加可选参数
		if (options.city) {
			params.set("city", options.city);
		}

		const response = await this.makeRequest(`${this.baseUrl}?${params}`);

		if (!response.success || !response.rawData) {
			return null;
		}

		const data = response.rawData;

		if (data.status === 0 && data.result && data.result.location) {
			const result = data.result;
			const location = result.location;

			return {
				longitude: location.lng,
				latitude: location.lat,
				formattedAddress: this.buildFormattedAddress(result),
				addressComponents: {
					country: "中国",
					province: result.addressComponent?.province,
					city: result.addressComponent?.city,
					district: result.addressComponent?.district,
					street: result.addressComponent?.street,
					streetNumber: result.addressComponent?.street_number,
				},
				confidence: this.mapBaiduConfidence(result.confidence),
				placeTypes: [result.level],
				placeId: result.uid,
				provider: "baidu",
				responseTime: response.responseTime,
			};
		}

		return null;
	}

	protected async performReverseGeocode(
		longitude: number,
		latitude: number,
		options: GeocodingOptions,
	): Promise<UnifiedGeocodingResult | null> {
		const params = new URLSearchParams({
			location: `${latitude},${longitude}`,
			ak: this.config.apiKey,
			output: "json",
			coordtype: "wgs84ll", // 输入坐标类型
		});

		const response = await this.makeRequest(`${this.baseUrl}?${params}`);

		if (!response.success || !response.rawData) {
			return null;
		}

		const data = response.rawData;

		if (data.status === 0 && data.result) {
			const result = data.result;
			const addressComponent = result.addressComponent;

			return {
				longitude,
				latitude,
				formattedAddress: result.formatted_address,
				addressComponents: {
					country: addressComponent.country,
					province: addressComponent.province,
					city: addressComponent.city,
					district: addressComponent.district,
					street: addressComponent.street,
					streetNumber: addressComponent.street_number,
				},
				confidence: "high",
				placeTypes: ["reverse_geocoded"],
				placeId: result.cityCode,
				provider: "baidu",
				responseTime: response.responseTime,
			};
		}

		return null;
	}

	/**
	 * 构建格式化地址
	 */
	private buildFormattedAddress(result: any): string {
		if (result.formatted_address) {
			return result.formatted_address;
		}

		const addressComponent = result.addressComponent;
		if (!addressComponent) {
			return "";
		}

		const parts = [
			addressComponent.country,
			addressComponent.province,
			addressComponent.city,
			addressComponent.district,
			addressComponent.street,
			addressComponent.street_number,
		].filter(Boolean);

		return parts.join("");
	}

	/**
	 * 映射百度地图的置信度到标准置信度
	 */
	private mapBaiduConfidence(confidence?: number): "high" | "medium" | "low" {
		if (!confidence) return "medium";

		if (confidence >= 80) return "high";
		if (confidence >= 50) return "medium";
		return "low";
	}
}
