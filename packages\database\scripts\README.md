# 数据库迁移脚本

## 图片格式迁移

### 背景
旅行日记系统中的图片存储格式从字符串数组升级为图片对象数组，以支持存储图片的描述、标题、alt文本等元数据信息。

**旧格式：**
```json
{
  "images": [
    "https://example.com/image1.jpg",
    "https://example.com/image2.jpg"
  ]
}
```

**新格式：**
```json
{
  "images": [
    {
      "url": "https://example.com/image1.jpg",
      "description": "图片描述",
      "alt": "图片alt文本",
      "caption": "图片标题",
      "uploadedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

### 使用方法

#### 方法一：直接运行 TypeScript 脚本
```bash
cd packages/database/scripts
npx tsx migrate-images-to-objects.ts
```

#### 方法二：使用 JavaScript 运行器
```bash
cd packages/database/scripts
node run-migration.js
```

#### 方法三：从项目根目录运行
```bash
# 确保在项目根目录
cd packages/database
npm run migrate:images
```

### 脚本功能

1. **自动检测**：脚本会自动检测哪些日记需要迁移，跳过已经是新格式的数据
2. **安全转换**：只处理状态为正常（status=0）的日记，不影响已删除的数据
3. **兼容处理**：支持多种图片格式的转换，包括：
   - 字符串格式 → 图片对象格式
   - 部分对象格式 → 完整图片对象格式
   - 已经是新格式的数据保持不变
4. **验证功能**：迁移完成后自动验证结果，确保所有数据都已正确转换
5. **详细日志**：提供详细的处理日志，包括成功、跳过和错误的统计

### 输出示例

```
=== 旅行日记图片格式迁移工具 ===

开始迁移图片格式...
找到 5 个日记需要检查
处理日记: 我的东京之旅 (ID: clx1234567890)
✅ 成功更新日记 clx1234567890
处理日记: 巴黎浪漫行 (ID: clx0987654321)
跳过日记 clx0987654321: 已经是新格式
...

迁移完成!
总计: 5 个日记
更新: 3 个日记
跳过: 2 个日记

开始验证迁移结果...
验证结果:
✅ 有效: 5 个日记
❌ 无效: 0 个日记

🎉 迁移成功完成，所有数据都已转换为新格式！
```

### 注意事项

1. **备份数据**：运行迁移前建议备份数据库
2. **环境变量**：确保 `DATABASE_URL` 环境变量已正确配置
3. **权限检查**：确保数据库连接有读写权限
4. **幂等性**：脚本可以安全地多次运行，不会重复处理已转换的数据

### 故障排除

如果迁移过程中出现错误：

1. 检查数据库连接是否正常
2. 确认环境变量配置正确
3. 查看错误日志中的具体错误信息
4. 如果部分数据迁移失败，可以重新运行脚本（会跳过已成功的数据）

### 回滚

如果需要回滚到旧格式，可以手动编写反向迁移脚本，或者从备份中恢复数据。 