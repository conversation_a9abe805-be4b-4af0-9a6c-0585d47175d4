import type { AnimationTheme, AtmosphereTheme } from "../../types";

// 大气层主题描述
export const ATMOSPHERE_DESCRIPTIONS = {
	day: "明亮清新的白天天空",
	night: "深邃神秘的星空夜晚",
	sunset: "温暖绚烂的日落黄昏",
	dawn: "柔和清新的晨曦黎明",
	aurora: "绚丽多彩的极光夜空",
	"deep-space": "浩瀚无垠的深邃宇宙",
	ocean: "清澈湛蓝的海洋天空",
	minimal: "纯净优雅的极简风格",
} as const satisfies Record<AtmosphereTheme, string>;

// 动画主题描述
export const ANIMATION_DESCRIPTIONS = {
	"shooting-stars": "流星划过夜空，浪漫而梦幻",
	"floating-particles": "轻柔的粒子在空中飘舞",
	aurora: "绚烂的极光在天际流动",
	minimal: "简约优雅的微妙动画",
	galaxy: "神秘的银河星空旋转",
	none: "无背景动画，纯净清爽",
} as const satisfies Record<AnimationTheme, string>;
