#!/bin/bash

# CI 检查脚本 - 模拟 Render 构建环境
set -e

echo "🚀 开始 CI 检查..."

# 1. 检查 Node.js 版本
echo "📋 检查 Node.js 版本..."
node --version
npm --version
pnpm --version

# 2. 清理缓存
echo "🧹 清理所有缓存..."
cd apps/web
rm -rf .next
rm -rf node_modules/.cache
cd ../..

# 3. 重新安装依赖（模拟 CI 环境）
echo "📦 重新安装依赖..."
pnpm install --frozen-lockfile

# 4. 生成 Prisma 客户端
echo "🔄 生成 Prisma 客户端..."
pnpm --filter database generate

# 5. 类型检查（仅检查 web 应用）
echo "🔍 运行 Web 应用类型检查..."
cd apps/web
pnpm run type-check
cd ../..

# 6. Linting（仅检查 web 应用）
echo "✨ 运行 Web 应用代码检查..."
cd apps/web
pnpm run lint
cd ../..

# 7. 构建（仅构建 web 应用）
echo "🏗️  开始 Web 应用构建..."
cd apps/web
pnpm run build:strict
cd ../..

echo "✅ Web 应用检查通过！" 