import crypto from "node:crypto";
import path from "node:path";
import { zValidator } from "@hono/zod-validator";
import { getSignedUploadUrl } from "@repo/storage";
import { Hono } from "hono";
import { z } from "zod";
import { authMiddleware } from "../middleware/auth";

type Variables = {
	user: {
		id: string;
		email: string;
		emailVerified: boolean;
		name: string;
		createdAt: Date;
		updatedAt: Date;
		banned: boolean;
	};
};

export const storageRouter = new Hono<{ Variables: Variables }>();

// Apply auth middleware to all storage routes
storageRouter.use(authMiddleware);

// Define the schema for the request body
const uploadRequestSchema = z.object({
	filename: z.string().min(1, "Filename is required"),
	contentType: z.string().min(1, "Content type is required"),
});

// POST /api/storage/upload
storageRouter.post(
	"/upload",
	zValidator("json", uploadRequestSchema),
	async (c) => {
		const { filename, contentType } = c.req.valid("json");
		const user = c.get("user");
		const userId = user?.id;

		if (!userId) {
			return c.json({ error: "Unauthorized" }, 401);
		}

		// Get bucket name and public URL prefix from environment variables
		const bucketName = process.env.CLOUDFLARE_R2_BUCKET_NAME;
		const publicUrlPrefix = process.env.STORAGE_PUBLIC_URL_PREFIX; // New env var

		if (!bucketName) {
			console.error(
				"Missing environment variable: CLOUDFLARE_R2_BUCKET_NAME",
			);
			return c.json(
				{ error: "Storage configuration error (bucket)" },
				500,
			);
		}
		if (!publicUrlPrefix) {
			console.error(
				"Missing environment variable: STORAGE_PUBLIC_URL_PREFIX",
			);
			return c.json(
				{ error: "Storage configuration error (URL prefix)" },
				500,
			);
		}

		try {
			const uniqueId = crypto.randomUUID();
			const extension = path.extname(filename);
			const storagePath = `${userId}/uploads/${uniqueId}${extension}`;

			// Get the presigned URL for uploading
			const uploadUrl = await getSignedUploadUrl(
				storagePath,
				{ bucket: bucketName },
				contentType,
			);

			if (!uploadUrl) {
				console.error(
					"Failed to get signed URL, but no error was thrown.",
				);
				return c.json({ error: "Failed to get upload URL" }, 500);
			}

			// Construct the permanent public URL
			// Ensure no double slashes if prefix ends with / and path starts with / (though our path doesn't start with /)
			const permanentUrl = `${publicUrlPrefix.replace(/\/$/, "")}/${storagePath}`;

			// Return both the upload URL and the permanent URL
			return c.json({ uploadUrl, permanentUrl });
		} catch (err) {
			console.error("Error processing upload request:", err);
			let errorMessage = "Internal server error";
			if (err instanceof Error) {
				errorMessage = err.message;
			}
			return c.json({ error: `获取上传URL失败: ${errorMessage}` }, 500);
		}
	},
);
