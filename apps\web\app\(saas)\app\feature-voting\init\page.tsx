"use client";

import { But<PERSON> } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { AlertCircle, CheckCircle, Database, Trash2 } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

/**
 * Feature Voting 数据初始化页面
 * 用于初始化和管理测试数据
 */
export default function FeatureVotingInitPage() {
	const [isInitializing, setIsInitializing] = useState(false);
	const [isCleaning, setIsCleaning] = useState(false);
	const [initStatus, setInitStatus] = useState<"idle" | "success" | "error">(
		"idle",
	);

	const handleInitializeData = async () => {
		setIsInitializing(true);
		setInitStatus("idle");

		try {
			const response = await fetch("/api/feature-voting/init", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
			});

			if (!response.ok) {
				throw new Error("初始化失败");
			}

			const result = await response.json();
			console.log("初始化结果:", result);

			setInitStatus("success");
			toast.success("测试数据初始化成功！");
		} catch (error) {
			console.error("初始化失败:", error);
			setInitStatus("error");
			toast.error("测试数据初始化失败，请检查控制台错误信息");
		} finally {
			setIsInitializing(false);
		}
	};

	const handleCleanupData = async () => {
		if (!confirm("确定要清理所有测试数据吗？此操作不可恢复！")) {
			return;
		}

		setIsCleaning(true);

		try {
			const response = await fetch("/api/feature-voting/cleanup", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
			});

			if (!response.ok) {
				throw new Error("清理失败");
			}

			const result = await response.json();
			console.log("清理结果:", result);

			setInitStatus("idle");
			toast.success("测试数据清理成功！");
		} catch (error) {
			console.error("清理失败:", error);
			toast.error("测试数据清理失败，请检查控制台错误信息");
		} finally {
			setIsCleaning(false);
		}
	};

	return (
		<div className="container mx-auto py-8 px-4 max-w-4xl">
			{/* 页面头部 */}
			<div className="mb-8">
				<h1 className="text-3xl font-bold text-foreground mb-2">
					Feature Voting 数据初始化
				</h1>
				<p className="text-muted-foreground">
					管理Feature
					Voting模块的测试数据，包括产品、特性请求和投票数据。
				</p>
			</div>

			{/* 状态指示器 */}
			{initStatus !== "idle" && (
				<div
					className={`mb-6 p-4 rounded-lg border ${
						initStatus === "success"
							? "bg-green-50 border-green-200 text-green-800 dark:bg-green-950/20 dark:border-green-800 dark:text-green-200"
							: "bg-red-50 border-red-200 text-red-800 dark:bg-red-950/20 dark:border-red-800 dark:text-red-200"
					}`}
				>
					<div className="flex items-center gap-2">
						{initStatus === "success" ? (
							<CheckCircle className="h-5 w-5" />
						) : (
							<AlertCircle className="h-5 w-5" />
						)}
						<span className="font-medium">
							{initStatus === "success"
								? "数据初始化成功"
								: "数据初始化失败"}
						</span>
					</div>
				</div>
			)}

			<div className="grid gap-6 md:grid-cols-2">
				{/* 初始化数据卡片 */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Database className="h-5 w-5" />
							初始化测试数据
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<p className="text-sm text-muted-foreground">
							创建测试所需的基础数据，包括：
						</p>
						<ul className="text-sm text-muted-foreground space-y-1 ml-4">
							<li>
								•
								3个测试产品（旅行足迹、AI图片生成器、特性投票系统）
							</li>
							<li>• 5个示例特性请求</li>
							<li>• 随机生成的投票数据</li>
						</ul>
						<Button
							onClick={handleInitializeData}
							disabled={isInitializing || isCleaning}
							loading={isInitializing}
							className="w-full"
						>
							{isInitializing ? "初始化中..." : "初始化测试数据"}
						</Button>
					</CardContent>
				</Card>

				{/* 清理数据卡片 */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Trash2 className="h-5 w-5" />
							清理测试数据
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<p className="text-sm text-muted-foreground">
							清理所有Feature Voting相关的测试数据：
						</p>
						<ul className="text-sm text-muted-foreground space-y-1 ml-4">
							<li>• 删除所有投票记录</li>
							<li>• 删除所有评论记录</li>
							<li>• 删除所有特性请求</li>
							<li>• 删除所有产品数据</li>
						</ul>
						<Button
							onClick={handleCleanupData}
							disabled={isInitializing || isCleaning}
							loading={isCleaning}
							variant="error"
							className="w-full"
						>
							{isCleaning ? "清理中..." : "清理所有数据"}
						</Button>
					</CardContent>
				</Card>
			</div>

			{/* 使用说明 */}
			<div className="mt-8 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
				<h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
					使用说明
				</h3>
				<div className="text-sm text-blue-800 dark:text-blue-200 space-y-2">
					<p>
						<strong>1. 首次使用</strong>
						：点击"初始化测试数据"按钮创建基础数据
					</p>
					<p>
						<strong>2. 测试功能</strong>：访问{" "}
						<code>/app/feature-voting</code> 页面测试功能
					</p>
					<p>
						<strong>3. 重置数据</strong>
						：如需重新开始，先清理数据再重新初始化
					</p>
					<p>
						<strong>4. 生产环境</strong>：请勿在生产环境中使用此功能
					</p>
				</div>
			</div>

			{/* 快速链接 */}
			<div className="mt-6 flex gap-4">
				<Button asChild variant="outline">
					<a href="/app/feature-voting">前往测试页面</a>
				</Button>
				<Button asChild variant="ghost">
					<a href="/app">返回应用首页</a>
				</Button>
			</div>
		</div>
	);
}
