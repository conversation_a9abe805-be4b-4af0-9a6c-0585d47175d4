"use client";

import { But<PERSON> } from "@ui/components/button";
import { Card } from "@ui/components/card";
import { Minimize, Palette, Sparkles, Star, Stars, X, Zap } from "lucide-react";
import { useEffect, useState } from "react";
import type { AnimationTheme } from "./BackgroundAnimations";
import { THEME_NAMES } from "./BackgroundAnimations";

interface AnimationThemeSwitcherProps {
	currentTheme: AnimationTheme;
	onThemeChange: (theme: AnimationTheme) => void;
	className?: string;
}

// 主题图标映射
const THEME_ICONS = {
	"shooting-stars": Star,
	"floating-particles": Sparkles,
	aurora: Zap,
	minimal: Minimize,
	galaxy: Stars,
	none: X,
} as const;

// 主题描述
const THEME_DESCRIPTIONS = {
	"shooting-stars": "流星划过夜空，浪漫而梦幻",
	"floating-particles": "轻柔的粒子在空中飘舞",
	aurora: "绚烂的极光在天际流动",
	minimal: "简约优雅的微妙动画",
	galaxy: "神秘的银河星空旋转",
	none: "无背景动画，纯净清爽",
} as const;

export function AnimationThemeSwitcher({
	currentTheme,
	onThemeChange,
	className = "",
}: AnimationThemeSwitcherProps) {
	const [isOpen, setIsOpen] = useState(false);
	const [isMounted, setIsMounted] = useState(false);

	// 确保只在客户端渲染
	useEffect(() => {
		setIsMounted(true);
	}, []);

	const themes = Object.keys(THEME_NAMES) as AnimationTheme[];

	// 如果还没有挂载，显示简化版本
	if (!isMounted) {
		return (
			<Button
				variant="outline"
				size="sm"
				className="bg-white/90 backdrop-blur-sm border-sky-200 shadow-lg transition-all duration-200"
				disabled
			>
				<Palette className="w-4 h-4 mr-1" />
				<span className="text-xs">动画主题</span>
			</Button>
		);
	}

	return (
		<div className={`relative ${className}`}>
			{/* 触发按钮 */}
			<Button
				onClick={() => setIsOpen(!isOpen)}
				variant="outline"
				size="sm"
				className="bg-white/90 backdrop-blur-sm border-sky-200 hover:bg-sky-50 shadow-lg transition-all duration-200"
				title="切换背景动画主题"
			>
				<Palette className="w-4 h-4 mr-1" />
				<span className="text-xs">动画主题</span>
			</Button>

			{/* 主题选择面板 */}
			{isOpen && (
				<>
					{/* 背景遮罩 */}
					<div
						className="fixed inset-0 z-40"
						onClick={() => setIsOpen(false)}
						onKeyDown={(e) => {
							if (e.key === "Escape") {
								setIsOpen(false);
							}
						}}
						role="button"
						tabIndex={0}
						aria-label="关闭主题选择面板"
					/>

					{/* 主题面板 */}
					<Card className="absolute top-full left-0 mt-2 z-50 w-72 bg-white/95 backdrop-blur-sm border-sky-200 shadow-xl">
						<div className="p-4">
							<div className="flex items-center justify-between mb-3">
								<h3 className="text-sm font-semibold text-gray-800">
									选择背景动画主题
								</h3>
								<Button
									onClick={() => setIsOpen(false)}
									variant="ghost"
									size="sm"
									className="h-6 w-6 p-0"
								>
									<X className="w-4 h-4" />
								</Button>
							</div>

							<div className="grid grid-cols-2 gap-2">
								{themes.map((theme) => {
									const Icon = THEME_ICONS[theme];
									const isActive = theme === currentTheme;

									return (
										<button
											key={theme}
											type="button"
											onClick={() => {
												onThemeChange(theme);
												setIsOpen(false);
											}}
											className={`
												relative p-3 rounded-lg border-2 transition-all duration-200 text-left
												${
													isActive
														? "border-sky-400 bg-sky-50 shadow-md"
														: "border-gray-200 hover:border-sky-300 hover:bg-sky-25"
												}
											`}
										>
											{/* 激活指示器 */}
											{isActive && (
												<div className="absolute top-1 right-1 w-2 h-2 bg-sky-500 rounded-full" />
											)}

											<div className="flex items-center gap-2 mb-1">
												<Icon
													className={`w-4 h-4 ${isActive ? "text-sky-600" : "text-gray-500"}`}
												/>
												<span
													className={`text-sm font-medium ${isActive ? "text-sky-800" : "text-gray-700"}`}
												>
													{THEME_NAMES[theme]}
												</span>
											</div>

											<p
												className={`text-xs ${isActive ? "text-sky-600" : "text-gray-500"}`}
											>
												{THEME_DESCRIPTIONS[theme]}
											</p>
										</button>
									);
								})}
							</div>

							{/* 当前主题预览 */}
							<div className="mt-4 p-3 bg-gradient-to-r from-sky-50 to-blue-50 rounded-lg border border-sky-200">
								<div className="flex items-center gap-2 mb-1">
									<Star className="w-4 h-4 text-sky-500" />
									<span className="text-sm font-medium text-sky-800">
										当前主题
									</span>
								</div>
								<p className="text-xs text-sky-600">
									{THEME_NAMES[currentTheme]} -{" "}
									{THEME_DESCRIPTIONS[currentTheme]}
								</p>
							</div>
						</div>
					</Card>
				</>
			)}
		</div>
	);
}
