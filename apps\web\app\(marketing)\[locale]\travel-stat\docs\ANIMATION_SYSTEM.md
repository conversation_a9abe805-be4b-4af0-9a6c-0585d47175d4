# 动画系统使用说明

## 概述

本项目集成了两套酷炫的背景动画系统：

1. **Three.js 3D动画** - 基于WebGL的3D粒子效果
2. **TSParticles 2D动画** - 基于Canvas的2D粒子系统

## 功能特性

### 🎨 动画主题
- **无动画** - 专注地图本身
- **原始动画** - 简单的CSS动画效果  
- **Three.js星空** - 3D粒子星空背景
- **银河系** - 旋转星环与星云
- **虫洞** - 扭曲的时空效果
- **流星雨** - 梦幻流星雨效果
- **浮动城市** - 连接的城市网络
- **极光波浪** - 绚丽的极光效果

### 🔧 技术特点
- **不阻挡交互** - 动画背景不会影响地图控制
- **性能优化** - 支持密度调节和设备适配
- **客户端渲染** - 确保SSR兼容性
- **多层架构** - 动画层、控制层分离

## 使用方法

### 1. 基本使用
在旅行足迹地图页面，右下角会显示"选择动画"按钮，点击即可打开动画选择面板。

### 2. 切换动画
- 点击任意动画选项即可切换
- 当前选择的动画会显示"当前"徽章
- 面板会自动关闭，新动画立即生效

### 3. 性能建议
- 3D动画建议在高配置设备上使用
- 低端设备可选择2D粒子效果
- 移动设备建议使用较低密度

## 技术架构

### 组件结构
```
AnimationController
├── 动画背景层 (pointer-events-none)
│   ├── ThreeJsBackground
│   └── TSParticlesBackground
└── 控制界面层 (pointer-events-auto)
    ├── 切换按钮
    └── 选择面板
```

### 层级管理
- z-0: 动画背景层
- z-10: 前景动画效果
- z-30: 地图控制器
- z-50: 动画控制按钮

### 交互隔离
所有动画组件都设置了`pointer-events: none`，确保：
- 不影响地图拖拽缩放
- 不阻挡Mapbox控件
- 不影响点位点击
- 控制按钮正常交互

## 故障排除

### 1. Three.js动画不显示
- 检查浏览器WebGL支持
- 查看控制台错误信息
- 确认three.js依赖正确安装

### 2. TSParticles动画不显示  
- 检查tsparticles依赖版本
- 确认Canvas API支持
- 查看网络连接状态

### 3. 控制按钮无法点击
- 检查z-index层级设置
- 确认pointer-events配置
- 验证元素没有被遮挡

## 性能优化

### 1. 自动优化
- 根据设备性能调整粒子数量
- 移动设备自动降低帧率
- 后台标签页暂停动画

### 2. 手动优化
- 选择较简单的动画主题
- 关闭不必要的交互效果
- 使用"无动画"选项

## 扩展开发

### 添加新主题
1. 在对应组件中定义新主题类型
2. 实现主题配置逻辑
3. 在AnimationController中添加选项
4. 更新主题预设导出

### 修改样式
- 调整opacity控制透明度
- 修改className传递自定义样式
- 使用density参数控制密度

## 兼容性

- **Chrome 80+** - 完全支持
- **Firefox 75+** - 完全支持  
- **Safari 14+** - 部分支持（WebGL限制）
- **Edge 80+** - 完全支持
- **移动浏览器** - 基础支持

## 更新日志

### v1.0.0 (2024-01-XX)
- 集成Three.js 3D动画系统
- 添加TSParticles 2D粒子效果  
- 实现动画控制器界面
- 修复交互阻挡问题
- 优化性能和兼容性 