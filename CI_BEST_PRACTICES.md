 # CI/CD 最佳实践指南

## 📋 概述

基于你的 monorepo 项目结构，这里是推荐的 CI/CD 最佳实践配置。

## 🏗️ 当前项目结构分析

你的项目已经具备：
- ✅ Turbo 构建系统
- ✅ pnpm workspaces
- ✅ Biome 代码检查
- ✅ 基础 PR 验证
- ✅ Dependabot 自动更新

## 🚀 推荐的 CI 工作流

### 1. Pull Request 验证工作流

你的现有 `validate-prs.yml` 已经被优化，包含：

- **变更检测**: 智能检测代码和依赖变更
- **并行执行**: 多个检查并行运行提高效率
- **缓存优化**: pnpm 缓存减少安装时间
- **矩阵构建**: 并行构建多个应用
- **智能跳过**: 无代码变更时跳过不必要的检查

### 2. 主要 CI 检查项目

#### 代码质量检查
```bash
# Lint 检查
pnpm lint

# 格式检查  
pnpm prettier

# 类型检查
pnpm type-check
```

#### 构建验证
```bash
# 构建所有应用
pnpm build:all

# 或单独构建
pnpm build:mapmoment
pnpm build:ai-images
```

#### 测试执行
```bash
# E2E 测试
pnpm --filter web e2e:ci

# 单元测试（如果有）
pnpm test
```

#### 安全扫描
```bash
# npm 安全审计
npm audit --audit-level=moderate

# Trivy 漏洞扫描
trivy fs .
```

## 🔧 环境变量配置

### GitHub Secrets 设置

在 GitHub 仓库的 Settings > Secrets and variables > Actions 中添加：

```
# 数据库
DATABASE_URL=postgresql://...
STAGING_DATABASE_URL=postgresql://...
PRODUCTION_DATABASE_URL=postgresql://...

# 应用 URL
STAGING_APP_URL=https://staging.your-app.com
PRODUCTION_APP_URL=https://your-app.com

# API 密钥
NEXT_PUBLIC_API_KEY=...
OPENAI_API_KEY=...

# 部署相关
VERCEL_TOKEN=...
DEPLOYMENT_KEY=...
```

## 📦 分支策略

### Git Flow 策略
```
main (生产)
├── develop (开发)
├── feature/* (功能分支)
├── hotfix/* (紧急修复)
└── release/* (发布分支)
```

### 触发条件
- **PR 到 main/develop**: 完整 CI 检查
- **Push 到 main**: 部署到生产
- **Push 到 develop**: 部署到预发布
- **标签推送**: 创建正式发布

## 🎯 性能优化

### 1. 缓存策略
```yaml
# pnpm 缓存
- name: Setup pnpm cache
  uses: actions/cache@v4
  with:
    path: ${{ env.STORE_PATH }}
    key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
    restore-keys: ${{ runner.os }}-pnpm-store-
```

### 2. 并行执行
```yaml
# 矩阵策略并行构建
strategy:
  matrix:
    app: [mapmoment, ai-images]
```

### 3. 智能跳过
```yaml
# 基于变更检测跳过
if: needs.changes.outputs.has-code-changes == 'true'
```

## 🔍 监控和通知

### 1. 失败通知
- Slack 集成
- Email 通知
- GitHub 状态检查

### 2. 性能监控
- 构建时间追踪
- 测试覆盖率报告
- 安全扫描结果

## 📋 部署策略

### 1. 预发布环境
```bash
# 自动部署到 staging
git push origin develop
```

### 2. 生产部署
```bash
# 创建发布标签
git tag v1.0.0
git push origin v1.0.0
```

### 3. 回滚策略
```bash
# 快速回滚到上一版本
git tag v1.0.1-rollback
```

## 🛠️ 本地开发集成

### 预提交钩子
```bash
# 安装 husky
pnpm add -D husky

# 设置预提交钩子
echo "pnpm lint && pnpm type-check" > .husky/pre-commit
```

### 开发脚本
```bash
# 开发环境快速启动
pnpm dev:all

# 生产构建验证
pnpm build:all

# 格式化代码
pnpm prettier:fix

# 修复 lint 问题
pnpm lint:fix
```

## 📊 质量门禁

### 必须通过的检查
- [x] 代码格式检查
- [x] TypeScript 类型检查
- [x] 所有应用构建成功
- [x] E2E 测试通过
- [x] 安全扫描无高危漏洞

### 可选检查
- [ ] 单元测试覆盖率 > 80%
- [ ] 性能测试通过
- [ ] 可访问性测试

## 🚨 故障排除

### 常见问题

1. **构建失败**
   ```bash
   # 清理缓存重新构建
   pnpm clean
   pnpm install
   pnpm build:all
   ```

2. **依赖冲突**
   ```bash
   # 重新安装依赖
   rm -rf node_modules pnpm-lock.yaml
   pnpm install
   ```

3. **类型检查失败**
   ```bash
   # 重新生成数据库类型
   pnpm db:generate
   pnpm type-check
   ```

## 📈 持续改进

### 定期审查
- 每月审查 CI 性能
- 优化构建时间
- 更新依赖版本
- 改进测试覆盖率

### 度量指标
- 构建成功率
- 平均构建时间
- 测试通过率
- 部署频率
- 故障恢复时间

## 🎉 下一步行动

1. **立即执行**:
   - 提交现有代码触发 CI
   - 检查所有检查项通过
   - 配置必要的环境变量

2. **短期优化**:
   - 添加单元测试
   - 设置预提交钩子
   - 配置通知集成

3. **长期规划**:    
   - 实现自动化部署
   - 添加性能监控
   - 完善发布流程

通过这些配置，你的项目将拥有一个健壮、高效的 CI/CD 流程！