import type { MetadataRoute } from "next";

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
	const baseUrl = process.env.NEXT_PUBLIC_APP_URL || "https://mapmoment.app";

	// 静态页面
	const staticPages: MetadataRoute.Sitemap = [
		{
			url: baseUrl,
			lastModified: new Date(),
			changeFrequency: "monthly",
			priority: 1,
		},
		{
			url: `${baseUrl}/travel-stat`,
			lastModified: new Date(),
			changeFrequency: "weekly",
			priority: 0.9,
		},
		{
			url: `${baseUrl}/en/blog`,
			lastModified: new Date(),
			changeFrequency: "weekly",
			priority: 0.8,
		},
		{
			url: `${baseUrl}/zh/blog`,
			lastModified: new Date(),
			changeFrequency: "weekly",
			priority: 0.8,
		},
		{
			url: `${baseUrl}/legal/privacy-policy`,
			lastModified: new Date(),
			changeFrequency: "yearly",
			priority: 0.3,
		},
		{
			url: `${baseUrl}/legal/terms`,
			lastModified: new Date(),
			changeFrequency: "yearly",
			priority: 0.3,
		},
	];

	// 动态blog文章
	let blogPages: MetadataRoute.Sitemap = [];

	try {
		const { allPosts } = await import("content-collections");
		const publishedPosts = allPosts.filter((post) => post.published);

		blogPages = publishedPosts.map((post) => ({
			url: `${baseUrl}/${post.locale}/blog/${post.path}`,
			lastModified: post.date ? new Date(post.date) : new Date(),
			changeFrequency: "monthly" as const,
			priority: 0.7,
		}));

		console.log(
			`✅ Successfully loaded ${publishedPosts.length} blog posts for sitemap`,
		);
	} catch (error) {
		console.error("❌ Error loading posts for sitemap:", error);
		// 添加静态测试数据作为后备
		blogPages = [
			{
				url: `${baseUrl}/en/blog/map-moment-travel-stats-guide`,
				lastModified: new Date("2025-01-20"),
				changeFrequency: "monthly" as const,
				priority: 0.7,
			},
			{
				url: `${baseUrl}/de/blog/first-post`,
				lastModified: new Date("2023-02-28"),
				changeFrequency: "monthly" as const,
				priority: 0.7,
			},
		];
	}

	// 合并所有页面
	return [...staticPages, ...blogPages];
}
