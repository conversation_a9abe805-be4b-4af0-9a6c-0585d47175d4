{"admin": {"menu": {"organizations": "Organizations", "users": "Users"}, "organizations": {"backToList": "Back to organizations", "confirmDelete": {"confirm": "Delete", "message": "Are you sure you want to delete this organization? This action cannot be undone.", "title": "Delete organization"}, "create": "Create", "delete": "Delete", "deleteOrganization": {"deleted": "Organization has been deleted successfully!", "deleting": "Deleting organization...", "notDeleted": "Organization could not be deleted. Please try again."}, "edit": "Edit", "form": {"createTitle": "Create an organization", "name": "Organization name", "notifications": {"error": "Could not save the organization. Please try again later.", "success": "Organization has been saved."}, "save": "Save", "updateTitle": "Edit organization"}, "loading": "Loading organizations...", "membersCount": "{count} {count, plural, one {member} other {members}}", "search": "Search for an organization...", "title": "Manage organizations"}, "title": "Administration", "users": {"confirmDelete": {"confirm": "Delete", "message": "Are you sure you want to delete this user? This action cannot be undone.", "title": "Delete user"}, "delete": "Delete", "deleteUser": {"deleted": "User has been deleted successfully!", "deleting": "Deleting user...", "notDeleted": "User could not be deleted. Please try again."}, "emailVerified": {"verified": "Email verified", "waiting": "Email waiting for verification"}, "impersonate": "Impersonate", "impersonation": {"impersonating": "Impersonating as {name}..."}, "loading": "Loading users...", "resendVerificationMail": {"error": "Could not resend verification mail. Please try again.", "submitting": "Resending verification mail...", "success": "Verification mail has been sent.", "title": "Resend verification mail"}, "search": "Search for name or email...", "title": "Manage users", "assignAdminRole": "Assign admin role", "removeAdminRole": "Remove admin role"}, "description": "Manage your application."}, "app": {"menu": {"accountSettings": "Account settings", "admin": "Admin", "aiChatbot": "AI Chatbot", "organizationSettings": "Organization settings", "start": "Start", "dashboard": "Dashboard", "travel-diary": "Travel Diary"}, "userMenu": {"accountSettings": "Account settings", "colorMode": "Color mode", "documentation": "Documentation", "home": "Home", "logout": "Logout"}}, "auth": {"errors": {"invalidEmailOrPassword": "The credentials you entered are invalid. Please check them and try again.", "unknown": "Something went wrong. Please try again.", "userNotFound": "This user does not exists", "failedToCreateUser": "Could not create user. Please try again.", "failedToCreateSession": "Could not create a session. Please try again.", "failedToUpdateUser": "Could not update user. Please try again.", "failedToGetSession": "Could not get the session.", "invalidPassword": "The entered password is incorrect.", "invalidEmail": "The entered email is invalid.", "invalidToken": "The token you entered is invalid or has expired.", "credentialAccountNotFound": "Account not found.", "emailCanNotBeUpdated": "Email could not be updated. Please try again.", "emailNotVerified": "Please verify your email first before logging in.", "failedToGetUserInfo": "Could not load user information.", "idTokenNotSupported": "ID token is not supported.", "passwordTooLong": "Password is too long.", "passwordTooShort": "Password is too short.", "providerNotFound": "This provider is not suppported.", "socialAccountAlreadyLinked": "This account is already linked to a user.", "userEmailNotFound": "<PERSON><PERSON> not found.", "userAlreadyExists": "This user already exists.", "invalidInvitation": "The invitation is invalid or expired.", "sessionExpired": "The session has expired.", "failedToUnlinkLastAccount": "Failed to unlink account", "accountNotFound": "Account not found"}, "forgotPassword": {"backToSignin": "Back to signin", "email": "Email", "hints": {"linkNotSent": {"message": "We are sorry, but we were unable to send you a link to reset your password. Please try again later.", "title": "<PERSON> not sent"}, "linkSent": {"message": "We have sent you a link to continue. Please check your inbox.", "title": "<PERSON> sent"}}, "message": "Please enter your email address and we will send you a link to reset your password.", "submit": "Send link", "title": "Forgot your password?"}, "login": {"continueWith": "Or continue with", "createAnAccount": "Create an account", "dontHaveAnAccount": "Don't have an account yet?", "forgotPassword": "Forgot password?", "hints": {"invalidCredentials": "The email or password you entered are invalid. Please try again.", "linkSent": {"message": "We have sent you a link to continue. Please check your inbox.", "title": "<PERSON> sent"}}, "loginWithPasskey": "Login with passkey", "modes": {"magicLink": "Magic link", "password": "Password"}, "submit": "Sign in", "subtitle": "Please enter your credentials to sign in.", "title": "Welcome back", "sendMagicLink": "Send magic link"}, "resetPassword": {"backToSignin": "Back to signin", "hints": {"error": "We are sorry, but we were unable to reset your password. Please try again.", "success": "Your password has been reset successfully."}, "message": "Please enter a new password.", "newPassword": "New password", "submit": "Reset password", "title": "Reset your password"}, "signup": {"alreadyHaveAccount": "Already have an account?", "email": "Email", "hints": {"signupFailed": "We are sorry, but we were unable to create your account. Please try again later.", "verifyEmail": "We have sent you a link to verify your email. Please check your inbox."}, "message": "We are happy that you want to join us. Please fill in the form below to create your account.", "name": "Name", "password": "Password", "signIn": "Sign in", "submit": "Create account", "title": "Create an account"}}, "blog": {"description": "Leave your footprints in Map Moment", "title": "My awesome blog"}, "changelog": {"description": "Stay up to date with the latest changes in our product.", "title": "Changelog", "dateErrors": {"invalidDate": "Invalid date", "unknownTime": "Unknown time", "invalid": "Invalid", "unknown": "Unknown", "error": "Error"}, "items": {"2025-06-15": {"date": "June 15, 2025", "changes": ["🎉 Map Moment officially launched! Capture your precious travel moments on interactive maps", "✨ Smart search functionality - Quickly find any location worldwide and add it to your travel memories", "📊 Travel statistics dashboard - Real-time view of your travel footprint with beautiful stats including countries and cities visited", "🗺️ Diverse map themes - From satellite views to artistic styles, choose the perfect backdrop for your memories", "📱 Fully responsive design - Perfect display of your travel maps on desktop, tablet, and mobile"]}, "2025-06-12": {"date": "June 12, 2025", "changes": ["🎨 Social media card generator - Transform your travel footprint into beautiful shareable cards", "🖼️ Multi-platform optimization - Support for optimal sizes for Instagram, Twitter, Facebook and other social platforms", "🎯 Custom card styles - Personalize colors, fonts, and layouts to make each card uniquely yours", "💫 Beautiful template library - Multiple professionally designed templates to make your travel memories stand out"]}, "2025-06-10": {"date": "June 10, 2025", "changes": ["💾 Data backup & sync - Safely export your precious travel data, never lose your beautiful memories", "📥 Smart data import - Easily manage and restore your travel footprint data", "🔄 One-click reset function - Start new travel records anytime, flexible map collection management", "🚀 Performance optimization - Significantly improved map loading speed and interaction smoothness"]}, "2025-06-08": {"date": "June 8, 2025", "changes": ["🌍 Global location search engine - Covering millions of locations worldwide, from popular cities to hidden gems", "📍 Smart location marking - One-click addition of visited places with automatic beautiful map markers", "🎭 Immersive map experience - Smooth map navigation and zooming, making browsing a delight", "🔍 Real-time search suggestions - Instant search results as you type, quickly find target locations"]}, "2025-06-05": {"date": "June 5, 2025", "changes": ["🎪 Dynamic background effects - Beautiful particle animations and aurora effects make your travel maps more vibrant", "🌙 Dark mode support - Elegant night theme, protect your eyes while enjoying beautiful maps", "🎨 UI/UX design upgrade - Modern minimalist interface design, every use is a visual pleasure", "⚡ Smooth page transitions - Seamless mode switching animations, providing silky user experience"]}, "2025-06-02": {"date": "June 2, 2025", "changes": ["🏗️ Map Moment project launched!", "💡 Product vision established: Help everyone preserve and share precious travel moments on beautiful maps", "🚀 Started building core functionality framework", "🎯 Focus on perfect combination of user experience and visual design"]}}}, "common": {"confirmation": {"cancel": "Cancel", "confirm": "Confirm"}, "menu": {"blog": "Blog", "changelog": "Changelog", "contact": "Contact", "dashboard": "Dashboard", "docs": "Docs", "faq": "FAQ", "login": "<PERSON><PERSON>", "pricing": "Pricing"}, "tableOfContents": {"title": "On this page"}}, "contact": {"description": "We are here to help you. Please use the form below to get in touch with us.", "form": {"email": "Email", "message": "Message", "name": "Name", "notifications": {"error": "We are sorry, but we were unable to send your message. Please try again later.", "success": "Your message has been sent successfully. We will get back to you as soon as possible."}, "submit": "Send message"}, "title": "Contact us"}, "documentation": {"title": "Documentation"}, "faq": {"description": "Do you have any questions? We have got you covered.", "title": "Frequently asked questions"}, "mail": {"common": {"openLinkInBrowser": "If you want to open the link in a different browser than your default one, copy and paste this link:", "otp": "One-time password", "useLink": "or use the following link:"}, "emailVerification": {"body": "Hey,\nplease click the link below to verify this new email address.", "confirmEmail": "Verify email", "subject": "Verify your email"}, "forgotPassword": {"body": "Hey,\nyou requested a password reset.\n\nClick the button below to reset your password.", "resetPassword": "Reset password", "subject": "Reset your password"}, "magicLink": {"body": "Hey,\nyou requested a login email from supastarter.\n\nClick the link below to login.", "login": "<PERSON><PERSON>", "subject": "Login to supastarter"}, "newUser": {"body": "Hey,\nthanks for signing up for supastarter.\n\nTo start using our app, please confirm your email address by clicking the link below.", "confirmEmail": "Confirm email", "subject": "Confirm your email"}, "newsletterSignup": {"body": "Thank you for signing up for the supastarter newsletter. We will keep you updated with the latest news and updates.", "subject": "Welcome to our newsletter"}, "organizationInvitation": {"body": "You have been invited to join the organization {organizationName}. Click the button below or copy and paste the link into your browser of choice to accept the invitation and join the organization.", "headline": "Join the organization {organizationName}", "join": "Join the organization", "subject": "You have been invited to join an organization"}}, "newsletter": {"email": "Email", "hints": {"error": {"message": "Could not subscribe to newsletter. Please try again later."}, "success": {"message": "Thank you for subscribing to our newsletter. We will keep you posted.", "title": "Subscribed"}}, "submit": "Subscribe", "subtitle": "Be among the first to get access to supastarter.nextjs.", "title": "Get early access"}, "onboarding": {"account": {"avatar": "Avatar", "avatarDescription": "Click the circle or drop an image to it to upload your avatar.", "name": "Name"}, "continue": "Continue", "message": "Just a few quick steps to get you started.", "notifications": {"accountSetupFailed": "We are sorry, but we were unable to set up your account. Please try again later."}, "step": "Step {step} / {total}", "title": "Set up your account"}, "organizations": {"createForm": {"name": "Organization name", "notifications": {"error": "We are sorry, but we were unable to create your organization. Please try again later.", "success": "Your organization has been created. You can now invite members."}, "submit": "Create organization", "subtitle": "Enter a name for your organization to get started. You can change the name later in the organization settings.", "title": "Create an organization"}, "invitationAlert": {"description": "You need to sign in or create an account to join the organization.", "title": "You have been invited to join an organization."}, "invitationModal": {"accept": "Accept", "decline": "Decline", "description": "You have been invited to join the organization {organizationName}. Do you want to accept the invitation and join the organization?", "title": "Join the organization"}, "organizationSelect": {"createNewOrganization": "Create new organization", "organizations": "Organizations", "personalAccount": "Personal account"}, "organizationsGrid": {"createNewOrganization": "Create new organization", "title": "Your organizations"}, "roles": {"admin": "Admin", "member": "Member", "owner": "Owner"}, "settings": {"changeName": {"title": "Organization name"}, "deleteOrganization": {"confirmation": "Are you sure you want to delete your organization?", "description": "Permanently delete your organization. Once you delete your organization, there is no going back. To confirm, please enter your password below:", "submit": "Delete organization", "title": "Delete organization"}, "logo": {"description": "Upload a logo for your organization.", "title": "Organization logo"}, "members": {"activeMembers": "Active members", "description": "See all active members and the pending invites of your organization.", "invitations": {"empty": "You have not invited any members yet.", "expiresAt": "Expires at {date}", "invitationStatus": {"accepted": "Accepted", "canceled": "Canceled", "pending": "Pending", "rejected": "Rejected"}, "revoke": "Revoke invitation", "resend": "Resend invitation"}, "inviteMember": {"description": "To invite a new member, send them an invitation.", "email": "Email", "notifications": {"error": {"description": "We were unable to invite the member. Please try again later.", "title": "Could not invite member"}, "success": {"description": "The member has been invited.", "title": "Member invited"}}, "role": "Role", "submit": "Invite", "title": "Invite member"}, "leaveOrganization": "Leave organization", "notifications": {"removeMember": {"error": {"description": "Could not remove the member from your organization. Please try again."}, "loading": {"description": "Removing member from organization..."}, "success": {"description": "The member has been successfully removed from your organization."}}, "revokeInvitation": {"error": {"description": "The invitation could not be revoked. Please try again later."}, "loading": {"description": "Revoking invitation..."}, "success": {"description": "The invitation has been revoked."}}, "updateMembership": {"error": {"description": "Could not update organization membership. Please try again."}, "loading": {"description": "Updating membership..."}, "success": {"description": "Membership was updated successfully"}}, "resendInvitation": {"loading": {"description": "Resending invitation..."}, "success": {"description": "Invitation sent"}, "error": {"description": "Could not sent invitation. Please try again."}}}, "pendingInvitations": "Pending invitations", "removeMember": "Remove member", "title": "Members"}, "notifications": {"organizationDeleted": "Your organization has been deleted.", "organizationNameNotUpdated": "We were unable to update your organization name. Please try again later.", "organizationNameUpdated": "Your organization name has been updated.", "organizationNotDeleted": "We were unable to delete your organization. Please try again later."}, "subtitle": "Manage the settings of the organization.", "title": "Organization", "dangerZone": {"title": "Danger zone"}}, "start": {"subtitle": "Welcome to the start page of this organization!"}}, "pricing": {"choosePlan": "Choose plan", "contactSales": "Contact sales", "description": "Choose the plan that works best for you.", "getStarted": "Get started", "month": "{count, plural, one {month} other {{count} months}}", "monthly": "Monthly", "products": {"basic": {"description": "Perfect to small teams.", "features": {"anotherFeature": "Another amazing feature", "limitedSupport": "Limited support"}, "title": "Basic"}, "enterprise": {"description": "Custom plan tailored to your requirements", "features": {"enterpriseSupport": "Enterprise support", "unlimitedProjects": "Unlimited projects"}, "title": "Enterprise"}, "free": {"description": "Start for free", "features": {"anotherFeature": "Another amazing feature", "limitedSupport": "Limited support"}, "title": "Free"}, "lifetime": {"description": "Buy once. Use forever.", "features": {"extendSupport": "Extended support", "noRecurringCosts": "No recurring costs"}, "title": "Lifetime"}, "pro": {"description": "Best for teams", "features": {"anotherFeature": "Another amazing feature", "fiveMembers": "Up to 5 members", "fullSupport": "Full support"}, "title": "Pro"}}, "purchase": "Purchase", "recommended": "Recommended", "subscribe": "Subscribe", "title": "Pricing", "trialPeriod": "{days} {days, plural, one {day} other {days}} free trial", "year": "{count, plural, one {year} other {{count} years}}", "yearly": "Yearly", "perSeat": "seat"}, "settings": {"account": {"avatar": {"description": "To change your avatar click the picture in this block and select a file from your computer to upload.", "notifications": {"error": "Could not update avatar", "success": "Avatar was updated successfully"}, "title": "Your avatar"}, "changeEmail": {"description": "To change your email, enter the new email and hit save. You will have to confirm the new email before it will become active.", "notifications": {"error": "Could not update email", "success": "Email was updated successfully"}, "title": "Your email"}, "changeName": {"notifications": {"error": "Could not update name", "success": "Name was updated successfully"}, "title": "Your name"}, "deleteAccount": {"confirmation": "Are you sure you want to delete your account?", "description": "Permanently delete your account. Once you delete your account, there is no going back. To confirm, please enter your password below:", "notifications": {"error": "Could not delete account", "success": "Account was deleted successfully"}, "submit": "Delete account", "title": "Delete account"}, "language": {"description": "To change the language of the app for your account, select a language from the list and click save.", "notifications": {"error": "Could not update language", "success": "Language was updated successfully"}, "title": "Your language"}, "security": {"activeSessions": {"description": "These are all the active sessions of your account. Click the X to end a specifc session.", "title": "Active sessions", "notifications": {"revokeSession": {"success": "Session revoked"}}}, "changePassword": {"currentPassword": "Current password", "newPassword": "New password", "notifications": {"error": "Could not update password", "success": "Password was updated successfully"}, "title": "Your password"}, "connectedAccounts": {"connect": "Connect", "disconnect": "Disconnect", "title": "Connected accounts"}, "passkeys": {"description": "Use passkeys as a secure alternative to passwords.", "notifications": {"addPasskey": {"error": {"title": "Could not add passkey"}, "success": {"title": "Passkey added"}}, "deletePasskey": {"error": {"title": "Could not delete passkey"}, "loading": {"title": "Deleting passkey..."}, "success": {"title": "Passkey deleted"}}}, "title": "Passkeys"}, "setPassword": {"description": "You have not set a password yet. To set one, you need to go through the password reset flow. Click the button below to send an email to reset your password and follow the instructions in the email.", "notifications": {"error": "Could not set password", "success": "Password was set successfully"}, "submit": "Set password", "title": "Your password"}, "title": "Security"}, "subtitle": "Manage the settings of your personal account.", "title": "Account settings"}, "billing": {"createCustomerPortal": {"label": "Manage billing", "notifications": {"error": {"title": "Could not create a customer portal session. Please try again."}}}, "activePlan": {"status": {"active": "Active", "canceled": "Canceled", "expired": "Expired", "incomplete": "Incomplete", "past_due": "Past due", "paused": "Paused", "trialing": "Trialing", "unpaid": "Unpaid"}, "title": "Your plan"}, "changePlan": {"description": "Choose a plan to subscribe to.", "title": "Change your plan"}, "title": "Billing"}, "menu": {"account": {"billing": "Billing", "dangerZone": "Danger zone", "general": "General", "security": "Security", "title": "Account"}, "organization": {"billing": "Billing", "general": "General", "members": "Members", "title": "Organization", "dangerZone": "Danger zone"}}, "save": "Save"}, "start": {"subtitle": "See the latest stats of your awesome business.", "welcome": "Welcome {name}!"}, "zod": {"errors": {"invalid_arguments": "Invalid function arguments", "invalid_date": "Invalid date", "invalid_enum_value": "Invalid enum value. Expected {- options}, received '{received}'", "invalid_intersection_types": "Intersection results could not be merged", "invalid_literal": "Invalid literal value, expected {expected}", "invalid_return_type": "Invalid function return type", "invalid_string": {"cuid": "Invalid {validation}", "datetime": "Invalid {validation}", "email": "Invalid {validation}", "endsWith": "Invalid input: must end with \"{endsWith}\"", "regex": "Invalid", "startsWith": "Invalid input: must start with \"{startsWith}\"", "url": "Invalid {validation}", "uuid": "Invalid {validation}"}, "invalid_type": "Expected {expected}, received {received}", "invalid_type_received_undefined": "Required", "invalid_union": "Invalid input", "invalid_union_discriminator": "Invalid discriminator value. Expected {- options}", "not_finite": "Number must be finite", "not_multiple_of": "Number must be a multiple of {multipleOf}", "too_big": {"array": {"exact": "Array must contain exactly {maximum} element(s)", "inclusive": "Array must contain at most {maximum} element(s)", "not_inclusive": "Array must contain less than {maximum} element(s)"}, "date": {"exact": "Date must be exactly {- maximum, datetime}", "inclusive": "Date must be smaller than or equal to {- maximum, datetime}", "not_inclusive": "Date must be smaller than {- maximum, datetime}"}, "number": {"exact": "Number must be exactly {maximum}", "inclusive": "Number must be less than or equal to {maximum}", "not_inclusive": "Number must be less than {maximum}"}, "set": {"exact": "Invalid input", "inclusive": "Invalid input", "not_inclusive": "Invalid input"}, "string": {"exact": "String must contain exactly {maximum} character(s)", "inclusive": "String must contain at most {maximum} character(s)", "not_inclusive": "String must contain under {maximum} character(s)"}}, "too_small": {"array": {"exact": "Array must contain exactly {minimum} element(s)", "inclusive": "Array must contain at least {minimum} element(s)", "not_inclusive": "Array must contain more than {minimum} element(s)"}, "date": {"exact": "Date must be exactly {- minimum, datetime}", "inclusive": "Date must be greater than or equal to {- minimum, datetime}", "not_inclusive": "Date must be greater than {- minimum, datetime}"}, "number": {"exact": "Number must be exactly {minimum}", "inclusive": "Number must be greater than or equal to {minimum}", "not_inclusive": "Number must be greater than {minimum}"}, "set": {"exact": "Invalid input", "inclusive": "Invalid input", "not_inclusive": "Invalid input"}, "string": {"exact": "String must contain exactly {minimum} character(s)", "inclusive": "String must contain at least {minimum} character(s)", "not_inclusive": "String must contain over {minimum} character(s)"}}, "unrecognized_keys": "Unrecognized key(s) in object: {- keys}"}, "types": {"array": "array", "bigint": "bigint", "boolean": "boolean", "date": "date", "float": "float", "function": "function", "integer": "integer", "map": "map", "nan": "nan", "never": "never", "null": "null", "number": "number", "object": "object", "promise": "promise", "set": "set", "string": "string", "symbol": "symbol", "undefined": "undefined", "unknown": "unknown", "void": "void"}, "validations": {"cuid": "cuid", "cuid2": "cuid2", "datetime": "datetime", "email": "email", "emoji": "emoji", "ip": "up", "regex": "regex", "ulid": "ulid", "url": "url", "uuid": "uuid"}}, "choosePlan": {"title": "Choose your plan", "description": "To continue, please select a plan."}, "analytics": {"enableInfo": "Please set your analytics provider in the environment variables to enable analytics."}, "travelMemo": {"dashboard": {"headerTitle": "Travel Dashboard", "newDiaryButton": "New Travel Diary", "stats": {"countriesVisited": "Countries Visited", "citiesExplored": "Cities Explored", "footprintsLeft": "Footprints Left", "photosTaken": "Photos Taken"}, "errors": {"loadDiariesFailed": "Failed to load travel diaries. Please try again later.", "loadStatsFailed": "Failed to load statistics. Please try again later.", "loadFootprintsFailed": "Failed to load map footprints. Please try again later."}, "toast": {"deleteSuccessTitle": "Deletion Successful", "deleteSuccessDescription": "The diary has been permanently deleted.", "deleteFailedTitle": "Deletion Failed", "deleteFailedDescriptionGeneric": "An error occurred while deleting the diary. Please try again."}, "diaries": {"title": "My Travel Diaries", "viewAllButton": "View all diaries", "emptyState": {"title": "No travel diaries found.", "cta": "Start by creating your first travel diary!"}, "card": {"defaultExcerpt": "Start recording your travel stories...", "defaultLocation": "Location not specified"}}, "map": {"title": "Global Travel Map", "description": "Explore your footprints across the globe."}}, "travelPointForm": {"modalTitle": {"add": "Add New Travel Point", "edit": "Edit Travel Point"}, "buttons": {"cancel": "Cancel", "add": "Add to Journey", "save": "Save Changes", "refreshPage": "Map search not showing? Click here to refresh"}, "toasts": {"invalidLocation": "Please enter and select a valid location first"}, "iconOptions": {"pin": "📍 Pin", "landmark": "🗿 Landmark", "food": "🍽️ Food", "park": "🌳 Park", "hotel": "🏨 Hotel", "shopping": "🛍️ Shopping", "transport": "🚗 Transport", "other": "❓ Other"}, "logs": {"formDataUpdated": "TravelPointForm data updated:", "lastDateSaved": "Last selected date saved:", "dialogOpenedResetForm": "Dialog opened, form data reset.", "formSubmitPrevented": "Form auto-submit prevented.", "iconSelected": "Icon type selected: {iconId}"}, "labels": {"iconType": "Icon Type", "location": "Location", "date": "Date", "description": "Description", "photos": "Photos"}, "placeholders": {"searchLocation": "Search for a place...", "selectDate": "Select a date", "describeExperience": "Describe your experience...", "loadingMapSearch": "Loading place search..."}, "errors": {"mapConfigError": "Map service configuration error, please contact administrator", "coordinatesError": "Unable to get coordinates for selected location, please select again", "mapLoadError": "Map configuration error, please contact administrator"}, "messages": {"locationSelected": "Location selected and coordinates confirmed", "locationTimeExtracted": "Location and time information extracted from image", "locationExtracted": "Location information extracted from image", "timeExtracted": "Time information extracted from image"}, "imageUploader": {"uploadPreviewAlt": "Upload preview {id}", "imageLoadFailAlt": "Image loading failed", "deleteImageTitle": "Delete image", "fileUploadFailed": "File upload failed: {error}", "apiError": "API response missing uploadUrl or permanentUrl"}}, "diaryCard": {"dateNotSet": "Date not set", "footprintsCount": "{count, plural, =0 {No footprints} one {# footprint} other {# footprints}}", "photosCount": "{count, plural, =0 {No photos} one {# photo} other {# photos}}", "deleteDiaryLabel": "Delete diary", "deleteConfirm": {"title": "Confirm diary deletion", "descriptionPart1": "This action will permanently delete the diary \"{title}\" and all its footprints, photos, and videos. After deletion, it ", "irreversible": "cannot be recovered", "descriptionPart2": ". Please confirm your action.", "cancel": "Cancel", "confirm": "Confirm Delete"}}, "diaryEditor": {"toasts": {"loadFailed": "Unable to load travel diary data", "autoSaveSuccess": "Diary auto-saved successfully", "autoSaveFailed": "Auto-save failed", "changesSaved": "Changes saved", "coverImageUploading": "Uploading cover image...", "coverImageUpdated": "Cover image updated", "pointAdded": "Travel point added", "pointUpdated": "Travel point updated", "savingChanges": "Saving changes...", "unformattedContentWarning": "There is unformatted content in the rich text editor. After switching, this content will remain in the draft.", "noContentToImportError": "No content in diary, please create timelines and points first", "formatAndSaveButtonLabel": "Format & Preview"}, "richText": {"loadingDraft": "Loading rich text draft for diary {diaryId}...", "loadedDraft": "Retrieved rich text draft content:", "fixingNestedContent": "Detected nested content format, correcting...", "draftSetToEditor": "Rich text draft content set to editor state:", "noDraftFound": "No rich text draft content found", "loadDraftFailedError": "Failed to load rich text draft:", "editorPlaceholder": "Start writing your travel story..."}, "errors": {"loadDiaryDataFailed": "Failed to load diary data:", "getUploadUrlFailed": "Failed to get upload URL: {detail}", "missingUrlInApiResponse": "API response missing uploadUrl or permanentUrl", "coverUploadFailed": "Cover image upload failed: {detail}", "coverUploadProcessFailed": "Cover image upload processing failed:", "coverUploadFailedSimple": "Cover image upload failed"}, "logs": {"coverUploadSuccess": "Cover image uploaded successfully, permanent URL:", "importToRichTextStart": "Starting to import content from diary to rich text editor", "noContentToImportError": "No timelines or points in diary, cannot import"}, "header": {"titlePlaceholder": "Give your trip a name!", "subtitlePlaceholder": "Add a brief introduction (optional)", "changeCoverButton": "Change Cover", "uploadCoverButton": "Upload Cover", "editSaveHint": "Edit | Enter to save", "expandButton": "Expand header", "collapseButton": "Collapse header", "presentationModeButton": "Open presentation mode", "switchToClassicModeButton": "Switch to classic edit mode", "switchToRichTextModeButton": "Switch to rich text edit mode"}, "resizeHandle": {"tooltip": "Drag to resize panel", "hoverText": "Drag to adjust width of left/right areas"}, "importDialog": {"title": "Confirm Import", "message": "Import action will overwrite current rich text content. Continue?", "cancelButton": "Cancel", "confirmButton": "Confirm Import"}, "markdownPreview": {"title": "Markdown Content Preview", "emptyState": "No Markdown content to preview.", "closeButton": "Close Preview", "formatAndSaveButtonLabel": "Format & Preview"}}, "mapboxPresentation": {"pageTitle": "Interactive Travel Map", "loadingMap": "Loading map...", "errorNotFound": "Diary not found.", "errorFailedToLoad": "Failed to load map data. Please try again later."}, "presentationPage": {"errorInvalidDiaryId": "Invalid Diary ID", "errorDiaryNotFound": "Specified travel diary not found", "errorLoadDiaryFailed": "Failed to load diary, please try again later.", "loadingMessage": "Loading travel story...", "defaultLoadError": "Loading failed"}, "mapStoryPage": {"loadingMap": "Loading map...", "logVideoExportStoryComplete": "[Video Export] Story playback complete, completion flag set", "logVideoExportMapReadyAutoPlay": "[Video Export] Map ready, automatically starting story playback"}, "recapPage": {"photoCaptions": {"diary": "Travel Diary", "memories": "Beautiful Memories", "moments": "Wonderful Moments", "scenery": "Journey Scenery", "time": "Precious Times"}, "header": {"summary": "{days, plural, one {# day} other {# days}}, {locations, plural, one {# footprint} other {# footprints}}, {images, plural, one {# photo} other {# photos}}", "shareStoryButtonLabel": "Share my journey", "toastLinkCopied": "Link copied to clipboard!", "toastCopyLinkFailed": "Failed to copy link: ", "logShareFailed": "Sharing failed:", "logShareError": "Error sharing:"}, "photoWall": {"title": "{count, plural, one {# Photo Memory} other {# Photo Memories}}", "emptyState": "No photo records yet."}, "timeline": {"itemTitleFormat": "{date} at {location}", "emptyState": "No detailed itinerary records yet."}, "footer": {"exploreMoreButton": "Explore More Diaries", "orSeparator": "or", "backToHomeButton": "Back to Homepage"}, "viewStoryButton": {"defaultLabel": "View Map Story"}}, "exportVideo": {"buttonText": "Export Video", "dialogTitleExporting": "Exporting Video...", "dialogTitleSubmitted": "Export Request Submitted", "dialogTitleError": "Export Failed", "dialogDescPreparing": "Preparing export, please wait...", "dialogDescSubmittedMsg1": "Your video export request has been submitted. This may take a few minutes.", "dialogDescTaskId": "Task ID: {taskId}", "dialogDescSubmittedMsg2": "You will be notified to download the video once it's ready. You can close this dialog and continue using the system.", "dialogDescErrorPrefix": "An error occurred while exporting the video:", "dialogDescUnknownError": "Unknown error", "footerButtonCancel": "Cancel", "footerButtonClose": "Close", "errorRequestFailed": "Export request failed: {status}", "errorGetStatusFailed": "Failed to get task status", "errorExportFailedMessage": "Export failed"}, "coverPage": {"statsLabelLocations": "Locations", "statsLabelPhotos": "Photos", "startButtonAriaLabel": "Start Journey", "startJourneyPrompt": "Click or swipe down to start the journey"}, "playbackControls": {"tooltipBackToStart": "Back to Start", "tooltipPreviousPoint": "Previous Point", "tooltipViewRecap": "View Recap", "tooltipPause": "Pause", "tooltipPlay": "Play", "svgLabelViewRecap": "View Recap", "tooltipStartJourney": "Start Journey", "tooltipNextPoint": "Next Point", "tooltipSkipToRecap": "Skip to Recap", "sliderLabelProgress": "Playback Progress", "statusCompleted": "Completed", "statusOverview": "Overview"}, "lightbox": {"closeButtonLabel": "Close lightbox", "previousButtonLabel": "Previous image", "nextButtonLabel": "Next image", "imageAltText": "Photo {currentIndex} of {totalImages}", "imageCounterText": "{currentIndex} / {totalImages}"}, "imageGallery": {"scrollLeftButtonLabel": "<PERSON><PERSON> left", "scrollRightButtonLabel": "<PERSON><PERSON> right", "viewImageButtonLabel": "View travel photo {indexPlusOne}", "imageAltText": "Travel spot {indexPlusOne}"}, "completionPrompt": {"title": "Journey Recap Complete", "description": "You've fully experienced this travel story. Scroll down to explore detailed memories.", "viewMemoriesButton": "View Memories"}, "countdownPrompt": {"autoPlayMessage": "Auto-playing in {countdown}s", "manualControlHint": "Click button for manual control"}}, "newDiaryPage": {"loadingIndicator": {"creating": "Creating a new travel diary..."}, "errorDisplay": {"title": "Creation Failed", "unknownError": "An unknown error occurred while creating the diary."}, "toast": {"createFailedPrefix": "Creation failed: "}}, "travelStat": {"meta": {"title": "Travel Footprint Statistics", "description": "Visualize your global travel journey and create stunning travel cards"}, "navigation": {"editMode": "Edit Mode", "cardGeneration": "Card Generation", "switchToEdit": "Return to Edit Mode", "switchToCard": "Generate Beautiful Card", "addFootprintsFirst": "Please add some travel footprint points first", "addPointsToGenerate": "Add footprint points to generate cards", "generateCard": "Generate beautiful card"}, "addPointPanel": {"selectedLocation": "Selected Location", "addToMap": "Add to Map", "adding": "Adding...", "addDetailedInfo": "Add Detailed Information", "date": "Date", "photo": "Photo", "description": "Description", "clickToUpload": "Click to Upload", "uploading": "Uploading...", "jpgPngMax20mb": "JPG, PNG, max 20MB", "shareYourFeelings": "Share your feelings...", "selectImageFile": "Please select an image file", "imageTooLarge": "Image size cannot exceed 20MB", "imageProcessFailed": "Image processing failed, please try again", "addSuccess": "✅ Location added successfully!", "addFailed": "Add failed: {error}", "preview": "Preview"}, "seo": {"mainTitle": "Capture Your Travel Moments on Interactive Maps", "mainDescription": "Map Moment helps you preserve precious travel moments on beautiful interactive maps. Mark your visited places, create stunning travel cards, and turn every journey into lasting digital memories worth cherishing.", "features": {"smartSearch": {"title": "Smart Destination Search", "description": "Instantly find and mark any destination worldwide. Map Moment's intelligent search helps you quickly locate cities, landmarks, and special places where your precious moments happened."}, "detailedStats": {"title": "Journey Analytics", "description": "Track your travel journey with beautiful statistics. See how many countries you've explored, cities you've visited, and moments you've captured on your personal Map Moment collection."}, "socialCards": {"title": "Shareable Moment Cards", "description": "Transform your travel moments into stunning visual cards. Perfect for Instagram, Twitter, and Facebook - let Map Moment help you share your most cherished travel experiences with the world."}, "mapStyles": {"title": "Beautiful Map Themes", "description": "Choose from gorgeous map styles that match your travel style. From satellite views to artistic themes, Map Moment offers the perfect backdrop for your precious travel moments."}, "dataManagement": {"title": "Moment Backup & Sync", "description": "Keep your precious travel moments safe forever. Map Moment's backup features ensure your captured moments are never lost, with easy import and export options."}, "responsiveDesign": {"title": "Moments Everywhere", "description": "Access your Map Moment collection on any device. Whether on desktop, tablet, or mobile, your travel moments are always at your fingertips, beautifully displayed."}}, "howToUse": {"title": "How to Create Your Map Moment Collection", "steps": {"searchAndAdd": {"title": "Mark Your Special Spots", "description": "Search for any place where you've experienced special moments. Add cities, landmarks, or hidden gems to your personal Map Moment collection."}, "viewStatistics": {"title": "Track Your Journey", "description": "Watch your Map Moment collection grow! See how many countries you've explored, cities you've visited, and precious moments you've captured."}, "generateCards": {"title": "Create Moment Cards", "description": "Transform your Map Moment collection into beautiful shareable cards. Perfect for social media or keeping as digital keepsakes of your adventures."}, "customizeMapStyle": {"title": "Style Your Memories", "description": "Choose map themes that reflect your travel style. From satellite views to artistic designs, make your Map Moment collection uniquely yours."}, "exportBackupData": {"title": "Preserve Your Moments", "description": "Keep your precious Map Moment collection safe with backup options. Your travel moments deserve to be preserved forever."}, "shareToSocial": {"title": "Share Your Story", "description": "Let the world see your incredible journey! Share your Map Moment cards on Instagram, Twitter, Facebook and inspire others to create their own moment maps."}}}, "faq": {"title": "Frequently Asked Questions About Map Moment", "items": {"isItFree": {"question": "Is Map Moment free to use?", "answer": "Yes! Map Moment is completely free to use. Create unlimited moment maps, generate beautiful cards, and preserve your travel moments without any cost."}, "dataStorage": {"question": "How are my Map Moment data stored?", "answer": "Your precious moments are stored securely in your browser. Map Moment respects your privacy - we don't collect or store your personal travel data on our servers."}, "deviceSupport": {"question": "Can I access Map Moment on different devices?", "answer": "Absolutely! Map Moment works beautifully on desktop, tablet, and mobile devices. Your moment collection is accessible wherever you are, whenever inspiration strikes."}, "socialPlatforms": {"question": "Where can I share my Map Moment cards?", "answer": "Share your moment cards anywhere! Map Moment generates cards optimized for Instagram, Twitter, Facebook, and other social platforms. Perfect for inspiring friends and family with your adventures."}}}, "keywords": {"tags": "Map Moment,Travel Moments,Moment Maps,Journey Moments,Interactive Travel Map,Travel Moment Keeper,Precious Moments,Travel Story Maps,Moment Collection,Travel Cards", "description": "Map Moment - the ultimate tool for capturing travel moments on beautiful interactive maps. Preserve precious moments from your journeys, create stunning moment cards, and build a visual story of your adventures. Perfect for travel enthusiasts who want to keep their most cherished travel moments alive forever."}}, "search": {"title": "Search Places", "placeholder": "Search countries, cities, or places...", "noResults": "No results found", "suggestions": "Suggestions", "recent": "Recent Searches", "popular": "Popular Destinations"}, "stats": {"title": "Travel Statistics", "countries": {"title": "Countries", "count": "{count, plural, one {# country} other {# countries}}"}, "cities": {"title": "Cities", "count": "{count, plural, one {# city} other {# cities}}"}, "points": {"title": "Footprints", "count": "{count, plural, one {# point} other {# points}}"}, "continents": {"title": "Continents", "count": "{count, plural, one {# continent} other {# continents}}"}, "distance": {"title": "Distance", "km": "{distance} km", "miles": "{distance} miles"}}, "countryList": {"title": "Visited Countries", "emptyState": "No countries visited yet", "colorGuideTitle": "Color System Guide", "colorGuideHelpTooltip": "View color system explanation", "colorGuide": {"currentTheme": "Current Color Theme", "mapColorDescription": "Map colors are coded according to visit frequency", "visitCount": {"one": "1 time", "two": "2 times", "three": "3 times", "four": "4 times", "five": "5 times", "sixToTen": "6-10 times", "tenPlus": "10+ times"}, "tip": "💡 Tip: Colors change based on the currently selected theme, visually representing your travel frequency"}}, "operationPanel": {"title": "Data Operations", "importData": "Import Data", "exportData": "Export Data", "clearData": "Clear Data", "confirmClear": {"title": "Confirm Clear Data", "description": "This is an irreversible operation.", "warningText": "About to clear the following data:", "items": {"travelPoints": "All travel footprint points", "countryStats": "Visited country statistics", "mapData": "Map markers and routes"}, "confirmText": "Are you sure you want to continue?", "cancelButton": "Cancel", "confirmButton": "Confirm Clear"}}, "clearDataDialog": {"title": "Confirm Clear Data", "message": "Are you sure you want to clear all travel footprint data? This action cannot be undone.", "clearImagesLabel": "Also clear image files", "clearImagesDescription": "Check this to also delete all uploaded images from local storage. Unchecked will keep images for future data imports.", "confirm": "Clear Data", "cancel": "Cancel"}, "searchBox": {"placeholder": "Search cities, countries or landmarks..."}, "templateSelector": {"noTemplatesForPlatform": "No templates available for {platform}", "noTemplatesAvailable": "No templates available", "selectTemplate": "Select template: {templateName}", "selected": "Selected"}, "templates": {"names": {"minimal": "Minimal", "vibrant": "Vibrant", "elegant": "Elegant", "retro": "Retro", "handDrawn": "Hand-drawn"}, "descriptions": {"minimal": "Clean and modern, content-focused", "vibrant": "Rainbow gradients, full of energy", "elegant": "High-end style, golden decorations", "retro": "Nostalgic style, warm tones", "handDrawn": "Hand-drawn style, casual and friendly"}, "features": {"minimal": ["Modern Typography", "Large Statistics", "Gradient Design", "3D Visual Effects"], "vibrant": ["Rainbow Background", "Circular Layout", "Diagonal Design", "Star Animation"], "elegant": ["Golden Border", "Symmetric Layout", "Premium Fonts", "Crown Decoration"], "retro": ["Film Border", "Polaroid Cards", "Vintage Filter", "Retro Icons"], "handDrawn": ["Hand-drawn Map", "Cute Icons", "Warm Colors", "Playful Decorations"]}, "categories": {"minimal": "Minimal", "vibrant": "Vibrant", "elegant": "Elegant", "retro": "Retro", "handDrawn": "Hand-drawn"}, "categoryDescriptions": {"minimal": "Clean and elegant", "vibrant": "Colorful and energetic", "elegant": "Luxurious and sophisticated", "retro": "Nostalgic and warm", "handDrawn": "Cute and playful"}, "customization": {"labels": {"primaryColor": "Primary Color", "accentColor": "Accent Color", "decorativeColor": "Decorative Color", "headerSize": "Head<PERSON>", "borderRadius": "Border Radius", "showDetailedStats": "Show Detailed Stats", "customTitle": "Custom Title", "cardTitle": "Card Title"}, "categories": {"colors": "Colors", "typography": "Typography", "layout": "Layout", "content": "Content"}, "titleOptions": ["My Travel Journey", "Around the World", "Travel Memories", "Global Footprints", "<PERSON>", "✈️ My Travel Journey", "🌍 Around the World", "📍 Global Footprints", "🗺️ Journey Marks", "🎒 Travel Memories"], "footerOptions": ["Wanderlust · Capturing every moment", "Recording every beautiful journey 🌍", "Curated by Excellence", "Journey of Distinction"]}}, "colorThemes": {"names": {"classic-blue-green": "Classic Blue-Green", "warm-sunset": "Warm Sunset", "cool-ocean": "Cool Ocean", "vibrant-rainbow": "Vibrant <PERSON>", "earth-tones": "<PERSON> Tones", "purple-pink": "Purple Pink Dream", "monochrome": "Classic Monochrome", "high-contrast": "High Contrast", "pastel-soft": "Soft Pastel", "neon-bright": "<PERSON><PERSON>"}, "descriptions": {"classic-blue-green": "Classic gradient from light blue to deep green, suitable for most map styles", "warm-sunset": "Warm tones from light orange to deep red, avoiding conflicts with map green", "cool-ocean": "Cool tones from light cyan to deep blue, avoiding conflicts with map blue", "vibrant-rainbow": "Each level uses different vibrant colors, high recognition", "earth-tones": "Brown-tan tones, perfectly blends with natural map styles", "purple-pink": "Elegant tones from light purple to deep pink, modern feel", "monochrome": "Minimalist black, white and gray tones, suitable for clean style", "high-contrast": "Strong contrasting color combinations, easy to distinguish", "pastel-soft": "Gentle macaron colors, visually comfortable", "neon-bright": "Fluorescent tones, full of technology"}, "levelDescriptions": {"unvisited": "Unvisited", "firstVisit": "First Visit", "secondVisit": "Second Visit", "multipleVisits": "Multiple Visits", "frequent": "Frequent", "veryFrequent": "Very Frequent", "extremelyFrequent": "Extremely Frequent", "superFrequent": "Super Frequent"}}, "colorThemeSelector": {"current": "Current", "recommendedThemes": "Recommended Themes", "recommendedSubtitle": "Optimized for current map style", "allThemes": "All Themes", "recommended": "Recommended", "categories": {"classic": "Classic", "modern": "Modern", "nature": "Nature", "vibrant": "Vibrant", "minimal": "Minimal"}}, "mapControls": {"title": "Map Controls", "export": "Export Map", "exportHighQuality": "Export Map (High Quality)", "exporting": "Exporting...", "styles": {"title": "Map Style", "markers": "Marker Style", "atmosphere": "Atmosphere", "animation": "Animation", "colors": "Color Theme", "projection": "Projection"}, "display": {"title": "Display Control", "current": "Current", "custom": "Custom", "detailedSettings": "Advanced Settings", "showMarkers": "Show Markers", "showTooltips": "Show Tooltips", "tooltipDisabledHint": "Tooltips are disabled when markers are hidden", "presets": {"all": {"name": "Show All", "description": "Show markers and details"}, "markersOnly": {"name": "Markers Only", "description": "Hide detail tooltips"}, "clean": {"name": "Clean Map", "description": "Hide all markers"}}}}, "common": {"settings": "Settings", "close": "Close", "closePopover": "Close popover"}, "atmosphereThemes": {"names": {"day": "Day", "night": "Night", "sunset": "Sunset", "dawn": "Dawn", "aurora": "Aurora", "deep-space": "Deep Space", "ocean": "Ocean", "minimal": "Minimal"}, "descriptions": {"day": "Bright and fresh daytime sky", "night": "Deep and mysterious starry night", "sunset": "Warm and brilliant sunset twilight", "dawn": "Soft and fresh morning dawn", "aurora": "Brilliant and colorful aurora night sky", "deep-space": "Vast and boundless deep universe", "ocean": "Clear and azure ocean sky", "minimal": "Pure and elegant minimalist style"}}, "markerStyles": {"names": {"classic": "Classic", "gradient-pulse": "Gradient Pulse", "particle-effect": "Particle Effect", "hand-drawn": "Hand Drawn", "emoji": "<PERSON><PERSON><PERSON>", "polaroid": "Polaroid Photo"}, "descriptions": {"classic": "Simple and clean circular markers", "gradient-pulse": "Gradient glow effect with pulsing animation", "particle-effect": "Dynamic particle orbit effects", "hand-drawn": "Artistic hand-drawn style markers", "emoji": "Customizable emoji markers", "polaroid": "Vintage polaroid photo style with images and descriptions"}, "popover": {"title": "Marker Style", "currentStyle": "Current Style: {styleName}", "outlineControl": "Outer Outline", "hideOutline": "Hide outline, show icon only", "showOutline": "Show outline and icon", "emojiSettings": "<PERSON><PERSON><PERSON>", "backgroundColor": "Background Color", "transparent": "Transparent", "themeSelector": "Theme Selector", "gradientPulseThemes": "Gradient Pulse Themes", "particleEffectThemes": "Particle Effect Themes", "handDrawnThemes": "Hand Drawn Themes", "selectEmoji": "Select Emoji", "travelRelated": "Travel Related", "expressions": "Expressions", "activities": "Activities", "themes": {"gradient": {"ocean": {"name": "Ocean", "description": "<PERSON><PERSON>"}, "sunset": {"name": "Sunset", "description": "Romantic"}, "forest": {"name": "Forest", "description": "Fresh"}, "aurora": {"name": "Aurora", "description": "Brilliant"}, "fire": {"name": "Fire", "description": "Passionate"}}, "particle": {"fire": {"name": "Fire", "description": "Passionate"}, "electric": {"name": "Electric", "description": "Dynamic"}, "magic": {"name": "Magic", "description": "Mysterious"}, "nature": {"name": "Nature", "description": "Fresh"}}, "handDrawn": {"pencil": {"name": "Pencil", "description": "Soft and delicate"}, "pen": {"name": "Pen", "description": "Clear and sharp"}, "charcoal": {"name": "Charc<PERSON>l", "description": "Bold and heavy"}, "marker": {"name": "<PERSON><PERSON>", "description": "Rich and vibrant"}}}, "particleCount": "{count} particles"}}, "cardGenerator": {"platforms": {"title": "Platform", "instagram": "Instagram", "wechat": "WeChat", "weibo": "Weibo", "twitter": "Twitter", "facebook": "Facebook"}, "templates": {"title": "Templates"}, "customization": {"title": "Content Settings", "cardTitle": "Card Title", "cardTitlePlaceholder": "✈️ My Travel Journey", "footer": "Custom Footer", "footerPlaceholder": "Wanderlust · Capturing every moment", "showDate": "Show Date", "customDate": "Custom Date", "customDatePlaceholder": "e.g. 2023-2024 or Summer Trip"}, "preview": {"calculating": "Calculating preview dimensions...", "mapNotLoaded": "Map not loaded, please try again later", "mapExportSuccess": "Map exported successfully", "mapExportFailed": "Map export failed, please retry", "cardNotReady": "Card not ready, please try again later", "cardExportSuccess": "Successfully exported card: {filename}", "cardExportFailed": "Card export failed, please retry", "exportMapOnly": "Export map only", "exporting": "Exporting...", "exportCard": "Export card"}, "stats": {"destinations": "Destinations", "places": "Places", "cities": "Cities", "countries": "Countries"}, "defaults": {"title": "Journey of Distinction", "footer": "Curated by Excellence"}, "export": {"messages": {"completed": "Card export completed: {quality}"}}}, "mapStyles": {"names": {"streets": "Streets", "outdoors": "Outdoors", "light": "Light", "dark": "Dark", "satellite": "Satellite", "satellite-streets": "Satellite Streets", "navigation-day": "Navigation Day", "navigation-night": "Navigation Night"}, "descriptions": {"streets": "Classic street view with detailed roads and labels.", "outdoors": "Designed for hiking and outdoor activities, showing terrain and trails.", "light": "A clean and bright light theme, suitable for data visualization.", "dark": "An elegant dark theme to reduce eye strain.", "satellite": "Authentic satellite imagery showing the Earth as it is.", "satellite-streets": "Satellite imagery overlaid with street information, the best of both worlds.", "navigation-day": "Day mode optimized for navigation.", "navigation-night": "Dark mode designed for night-time navigation."}, "categories": {"all": "All", "basic": "Basic", "satellite": "Satellite", "navigation": "Navigation", "artistic": "Artistic"}, "popover": {"title": "Map Style", "recommended": "Recommended", "currentStyle": "Current Style: {styleName}", "goodWithEffects": "✓ Works well with current effects"}}, "mapProjections": {"names": {"globe": "Globe", "mercator": "Mercator", "equalEarth": "Equal Earth", "naturalEarth": "Natural Earth", "winkelTripel": "<PERSON><PERSON>", "albers": "<PERSON><PERSON>", "lambertConformalConic": "Lambert Conformal Conic", "equirectangular": "Equirectangular"}, "descriptions": {"globe": "Renders the map on a sphere, providing the most realistic view of the Earth.", "mercator": "Renders the map as a 2D square, the standard for web maps.", "equalEarth": "An equal-area pseudocylindrical projection, more visually pleasing.", "naturalEarth": "A pseudocylindrical projection for creating small-scale maps.", "winkelTripel": "A modified azimuthal projection, standard for the National Geographic Society.", "albers": "An equal-area conic projection, often used for maps of large countries like the USA.", "lambertConformalConic": "A conformal conic projection widely used in aeronautical charts.", "equirectangular": "A simple projection that maps meridians and parallels to a Cartesian grid."}, "shortDescs": {"globe": "Realistic 3D Globe", "mercator": "Classic Navigation", "equalEarth": "Area-Accurate", "naturalEarth": "Visually Optimized", "winkelTripel": "NGS Standard", "albers": "Statistical Use", "lambertConformalConic": "Angle-Accurate", "equirectangular": "Simple Rectangle"}, "badges": {"globe": "3D", "mercator": "Navigation", "equalEarth": "Equal-Area", "naturalEarth": "Aesthetic", "winkelTripel": "Compromise", "albers": "Data", "lambertConformalConic": "Conformal", "equirectangular": "Simple"}, "categories": {"common": "Common Projections", "professional": "Professional Projections"}}, "atmosphere": {"names": {"day": "Day", "night": "Night", "sunset": "Sunset", "dawn": "Dawn", "aurora": "Aurora", "deep-space": "Deep Space", "ocean": "Ocean", "minimal": "Minimal"}, "descriptions": {"day": "Bright and fresh daytime sky", "night": "Deep and mysterious starry night", "sunset": "Warm and brilliant sunset twilight", "dawn": "Soft and fresh morning dawn", "aurora": "Brilliant and colorful aurora night sky", "deep-space": "Vast and boundless deep universe", "ocean": "Clear and azure ocean sky", "minimal": "Pure and elegant minimalist style"}, "popover": {"title": "Atmosphere", "currentAtmosphere": "Current Atmosphere: {atmosphereName}", "starIntensity": "Star Intensity", "horizonBlend": "Horizon Blend", "starfield": "Starfield"}}, "animation": {"names": {"shooting-stars": "Shooting Stars", "floating-particles": "Floating Particles", "aurora": "Aurora", "minimal": "Minimal", "galaxy": "Galaxy", "none": "None"}, "descriptions": {"shooting-stars": "Meteors streak across the night sky, romantic and dreamy", "floating-particles": "Soft particles dancing in the air", "aurora": "Brilliant aurora flowing in the sky", "minimal": "Simple and elegant subtle animation", "galaxy": "Mysterious galaxy stars rotating", "none": "No background animation, pure and refreshing"}, "popover": {"title": "Animation", "currentTheme": "Current Theme", "currentAnimation": "Current Animation: {animationName}"}}, "resizablePanel": {"dragHint": "← <PERSON>ag to Adjust →", "draggingHint": "Drag to Adjust <PERSON>"}}}