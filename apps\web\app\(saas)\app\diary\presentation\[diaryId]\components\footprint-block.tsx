"use client";

import { cn } from "@ui/lib/utils";
import { format } from "date-fns";
import { motion } from "framer-motion";
import Image from "next/image";
import { useState } from "react";
import type { TravelPoint } from "../../../../components/diary/travel-point-form/types";
import {
	ReactGoogleMap,
	ReactGoogleMapProvider,
} from "../../../../components/map/providers/react-google-map-provider";

interface FootprintBlockProps {
	footprint: TravelPoint;
	isActive: boolean;
	timelineTitle: string;
	showTimelineTitle: boolean;
}

export function FootprintBlock({
	footprint,
	isActive,
	timelineTitle,
	showTimelineTitle,
}: FootprintBlockProps) {
	const [selectedImageIndex, setSelectedImageIndex] = useState(0);
	const [isTextExpanded, setIsTextExpanded] = useState(false);
	const [isModalOpen, setIsModalOpen] = useState(false);

	// 根据图标类型选择不同的图标
	const iconMap = {
		landmark: "🏛️",
		food: "🍽️",
		park: "🌳",
		hotel: "🏨",
		transportation: "🚆",
		shopping: "🛍️",
		entertainment: "🎭",
		default: "📍",
	};

	const icon =
		iconMap[footprint.iconType as keyof typeof iconMap] || iconMap.default;

	// 文本是否需要"展开/收起"功能
	const needsExpansion = footprint.description.length > 150;
	const displayText =
		isTextExpanded || !needsExpansion
			? footprint.description
			: `${footprint.description.slice(0, 150)}...`;

	return (
		<div className="relative w-full h-full flex flex-col justify-center items-center px-4 md:px-8">
			{/* 时间线标题 */}
			{showTimelineTitle && (
				<motion.div
					initial={{ opacity: 0, y: -20 }}
					animate={
						isActive ? { opacity: 1, y: 0 } : { opacity: 0, y: -20 }
					}
					transition={{ duration: 0.6 }}
					className="absolute top-8 left-1/2 transform -translate-x-1/2 bg-black/70 px-6 py-2 rounded-full"
				>
					<h2 className="text-xl font-semibold text-white">
						{timelineTitle}
					</h2>
				</motion.div>
			)}

			{/* 主内容区 */}
			<div className="max-w-4xl w-full">
				{/* 足迹内容 */}
				<motion.div
					initial={{ opacity: 0, y: 30 }}
					animate={
						isActive ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }
					}
					transition={{ duration: 0.8 }}
					className="bg-black/40 backdrop-blur-sm rounded-xl overflow-hidden shadow-2xl"
				>
					{/* 位置和时间信息 */}
					<div className="p-4 md:p-6 border-b border-white/10 flex flex-col md:flex-row justify-between items-start md:items-center gap-2">
						<div className="flex items-center">
							<span className="text-2xl mr-2">{icon}</span>
							<h3 className="text-xl md:text-2xl font-bold text-white">
								{footprint.location}
							</h3>
						</div>
						<div className="text-white/70 text-sm">
							{format(
								new Date(footprint.date),
								"yyyy年MM月dd日 HH:mm",
							)}
						</div>
					</div>

					{/* 照片墙 */}
					<div className="relative">
						{footprint.images && footprint.images.length > 0 ? (
							<div className="w-full aspect-video relative overflow-hidden">
								<Image
									src={
										footprint.images[selectedImageIndex].url
									}
									alt={`${footprint.location} - 图片 ${selectedImageIndex + 1}`}
									fill
									className="object-cover rounded-lg"
									priority={selectedImageIndex === 0}
								/>

								{/* 多张照片的导航指示器 */}
								{footprint.images.length > 1 && (
									<div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
										{footprint.images.map((_, index) => (
											<button
												key={index}
												onClick={(e) => {
													e.stopPropagation();
													setSelectedImageIndex(
														index,
													);
												}}
												className={cn(
													"w-2 h-2 rounded-full transition-all duration-300",
													selectedImageIndex === index
														? "bg-white scale-125"
														: "bg-white/50",
												)}
											/>
										))}
									</div>
								)}

								{/* 图片切换箭头 - 仅在多张照片时显示 */}
								{footprint.images.length > 1 && (
									<>
										<button
											onClick={(e) => {
												e.stopPropagation();
												setSelectedImageIndex(
													(prev) =>
														(prev -
															1 +
															footprint.images
																.length) %
														footprint.images.length,
												);
											}}
											className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 rounded-full p-2 text-white"
										>
											<svg
												xmlns="http://www.w3.org/2000/svg"
												width="24"
												height="24"
												viewBox="0 0 24 24"
												fill="none"
												stroke="currentColor"
												strokeWidth="2"
												strokeLinecap="round"
												strokeLinejoin="round"
											>
												<path d="m15 18-6-6 6-6" />
											</svg>
										</button>
										<button
											onClick={(e) => {
												e.stopPropagation();
												setSelectedImageIndex(
													(prev) =>
														(prev + 1) %
														footprint.images.length,
												);
											}}
											className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 rounded-full p-2 text-white"
										>
											<svg
												xmlns="http://www.w3.org/2000/svg"
												width="24"
												height="24"
												viewBox="0 0 24 24"
												fill="none"
												stroke="currentColor"
												strokeWidth="2"
												strokeLinecap="round"
												strokeLinejoin="round"
											>
												<path d="m9 18 6-6-6-6" />
											</svg>
										</button>
									</>
								)}
							</div>
						) : (
							<motion.div
								initial={{ opacity: 0, scale: 0.95 }}
								animate={{ opacity: 1, scale: 1 }}
								transition={{ duration: 0.8, ease: "easeOut" }}
								className="w-full aspect-video bg-neutral-800 overflow-hidden relative rounded-md"
							>
								<ReactGoogleMapProvider>
									<div className="w-full h-full">
										<ReactGoogleMap
											center={footprint.coordinates}
											zoom={14}
											markers={[
												{
													position:
														footprint.coordinates,
													title: footprint.location,
													isActive: true,
												},
											]}
											mapContainerStyle={{
												borderRadius: "0.375rem",
											}}
											options={{
												styles: [
													{
														featureType: "all",
														elementType: "all",
														stylers: [
															{
																saturation:
																	-100,
															},
															{ brightness: 15 },
														],
													},
													{
														featureType: "water",
														elementType: "all",
														stylers: [
															{
																color: "#151d28",
															},
														],
													},
													{
														featureType: "poi",
														elementType: "all",
														stylers: [
															{
																visibility:
																	"on",
															},
														],
													},
												],
												mapTypeControl: false,
												streetViewControl: false,
												fullscreenControl: false,
											}}
										/>
									</div>
								</ReactGoogleMapProvider>

								<motion.div
									initial={{ opacity: 0, y: 20 }}
									animate={{ opacity: 1, y: 0 }}
									transition={{ delay: 0.5, duration: 0.5 }}
									className="absolute top-4 left-4 bg-black/50 backdrop-blur-sm px-4 py-2 rounded-full flex items-center"
								>
									<span className="text-xl mr-2">{icon}</span>
									<span className="text-sm font-medium text-white">
										{footprint.location}
									</span>
								</motion.div>
							</motion.div>
						)}
					</div>

					{/* 文字内容 */}
					<div className="p-4 md:p-6">
						<p className="text-white/90 text-base leading-relaxed mb-2">
							{displayText}
						</p>

						{/* 展开/收起按钮 */}
						{needsExpansion && (
							<button
								onClick={() =>
									setIsTextExpanded(!isTextExpanded)
								}
								className="text-primary text-sm font-medium hover:underline"
							>
								{isTextExpanded ? "收起" : "展开全文"}
							</button>
						)}
					</div>
				</motion.div>
			</div>

			{/* 照片全屏模态框 */}
			{isModalOpen && (
				<div
					className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4"
					onClick={() => setIsModalOpen(false)}
				>
					<button
						className="absolute top-4 right-4 text-white bg-black/50 rounded-full p-2"
						onClick={() => setIsModalOpen(false)}
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							width="24"
							height="24"
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							strokeWidth="2"
							strokeLinecap="round"
							strokeLinejoin="round"
						>
							<path d="M18 6 6 18" />
							<path d="m6 6 12 12" />
						</svg>
					</button>

					<div className="relative w-full max-w-5xl max-h-[80vh]">
						<Image
							src={footprint.images[selectedImageIndex].url}
							alt={`${footprint.location} - 图片 ${selectedImageIndex + 1}`}
							width={1200}
							height={800}
							className="object-contain w-full h-auto max-h-[80vh]"
						/>

						{/* 全屏模式的图片导航 */}
						{footprint.images.length > 1 && (
							<div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-3">
								{footprint.images.map((_, index) => (
									<button
										key={index}
										onClick={(e) => {
											e.stopPropagation();
											setSelectedImageIndex(index);
										}}
										className={cn(
											"w-3 h-3 rounded-full transition-all duration-300",
											selectedImageIndex === index
												? "bg-white scale-125"
												: "bg-white/50",
										)}
									/>
								))}
							</div>
						)}

						{/* 全屏模式的左右箭头 */}
						{footprint.images.length > 1 && (
							<>
								<button
									onClick={(e) => {
										e.stopPropagation();
										setSelectedImageIndex(
											(prev) =>
												(prev -
													1 +
													footprint.images.length) %
												footprint.images.length,
										);
									}}
									className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 rounded-full p-3 text-white"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										width="24"
										height="24"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										strokeWidth="2"
										strokeLinecap="round"
										strokeLinejoin="round"
									>
										<path d="m15 18-6-6 6-6" />
									</svg>
								</button>
								<button
									onClick={(e) => {
										e.stopPropagation();
										setSelectedImageIndex(
											(prev) =>
												(prev + 1) %
												footprint.images.length,
										);
									}}
									className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 rounded-full p-3 text-white"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										width="24"
										height="24"
										viewBox="0 0 24 24"
										fill="none"
										stroke="currentColor"
										strokeWidth="2"
										strokeLinecap="round"
										strokeLinejoin="round"
									>
										<path d="m9 18 6-6-6-6" />
									</svg>
								</button>
							</>
						)}
					</div>
				</div>
			)}
		</div>
	);
}
