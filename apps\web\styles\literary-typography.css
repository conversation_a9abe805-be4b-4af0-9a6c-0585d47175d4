/* 文艺清新主题字体样式 */
:root {
	--font-sans: "Quicksand", "LXGW WenKai", -apple-system, BlinkMacSystemFont,
		"Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
	--font-serif: "LXG<PERSON> WenKai", "Noto Serif SC", Georgia, Cambria,
		"Times New Roman", Times, serif;
}

body,
html {
	font-family: var(--font-sans);
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	text-rendering: optimizeLegibility;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-family: var(--font-serif);
	letter-spacing: -0.01em;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
}

/* 应用到输入框 */
input,
textarea,
select {
	font-family: var(--font-sans);
}

/* 旅行日记特定样式 */
.diary-title {
	font-family: var(--font-serif);
	font-weight: 700;
	letter-spacing: 0.03em;
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.diary-subtitle {
	font-family: var(--font-serif);
	font-weight: 400;
	letter-spacing: 0.02em;
}

.travel-point-title {
	font-family: var(--font-serif);
	font-weight: 700;
	letter-spacing: 0.01em;
	text-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
}

/* 时间标签 */
.time-label {
	font-family: var(--font-sans);
	font-weight: 500;
	letter-spacing: 0.02em;
	font-size: 0.85rem;
	color: rgba(107, 114, 128, 0.8);
}

/* 页面内容样式 */
.content-text {
	font-family: var(--font-sans);
	line-height: 1.8;
	letter-spacing: 0.01em;
}

/* 文艺清新风格引用 */
.literary-quote {
	font-family: var(--font-serif);
	font-style: italic;
	line-height: 1.6;
	color: rgba(107, 114, 128, 0.95);
	border-left: 2px solid rgba(147, 197, 253, 0.5);
	padding-left: 1rem;
	margin-left: 0.5rem;
	position: relative;
}

.literary-quote::before {
	content: "\201C"; /* 使用Unicode转义序列表示左双引号 */
	font-size: 2.5rem;
	color: rgba(147, 197, 253, 0.5);
	position: absolute;
	left: -1.2rem;
	top: -1rem;
	font-family: var(--font-serif);
}

/* 文艺清新风格引用尾部 */
.literary-quote::after {
	content: "\201D"; /* 使用Unicode转义序列表示右双引号 */
	font-size: 2.5rem;
	color: rgba(147, 197, 253, 0.5);
	line-height: 0;
	position: relative;
	top: 1rem;
	margin-left: 0.2rem;
	font-family: var(--font-serif);
}
