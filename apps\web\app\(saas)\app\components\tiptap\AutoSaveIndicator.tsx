"use client";

import { But<PERSON> } from "@ui/components/button";
import { cn } from "@ui/lib";
import { Check, Clock, Save, Wifi, WifiOff } from "lucide-react";
import React, { useState, useEffect } from "react";

interface AutoSaveIndicatorProps {
	isModified: boolean;
	isSaving: boolean;
	lastSaved?: Date;
	onManualSave?: () => void;
	className?: string;
}

export const AutoSaveIndicator: React.FC<AutoSaveIndicatorProps> = ({
	isModified,
	isSaving,
	lastSaved,
	onManualSave,
	className,
}) => {
	const [isOnline, setIsOnline] = useState(true);
	const [timeAgo, setTimeAgo] = useState<string>("");

	// 监听网络状态
	useEffect(() => {
		const handleOnline = () => setIsOnline(true);
		const handleOffline = () => setIsOnline(false);

		window.addEventListener("online", handleOnline);
		window.addEventListener("offline", handleOffline);

		// 初始状态
		setIsOnline(navigator.onLine);

		return () => {
			window.removeEventListener("online", handleOnline);
			window.removeEventListener("offline", handleOffline);
		};
	}, []);

	// 更新"多久前保存"的时间显示
	useEffect(() => {
		if (!lastSaved) return;

		const updateTimeAgo = () => {
			const now = new Date();
			const diff = now.getTime() - lastSaved.getTime();
			const seconds = Math.floor(diff / 1000);
			const minutes = Math.floor(seconds / 60);
			const hours = Math.floor(minutes / 60);

			if (seconds < 60) {
				setTimeAgo("刚刚保存");
			} else if (minutes < 60) {
				setTimeAgo(`${minutes}分钟前保存`);
			} else if (hours < 24) {
				setTimeAgo(`${hours}小时前保存`);
			} else {
				setTimeAgo(lastSaved.toLocaleDateString("zh-CN"));
			}
		};

		updateTimeAgo();
		const interval = setInterval(updateTimeAgo, 30000); // 每30秒更新一次

		return () => clearInterval(interval);
	}, [lastSaved]);

	// 获取状态信息
	const getStatus = () => {
		if (!isOnline) {
			return {
				icon: <WifiOff className="h-4 w-4" />,
				text: "离线模式",
				color: "text-orange-600",
				bgColor: "bg-orange-50",
				borderColor: "border-orange-200",
			};
		}

		if (isSaving) {
			return {
				icon: <Clock className="h-4 w-4 animate-spin" />,
				text: "保存中...",
				color: "text-blue-600",
				bgColor: "bg-blue-50",
				borderColor: "border-blue-200",
			};
		}

		if (isModified) {
			return {
				icon: <Save className="h-4 w-4" />,
				text: "有未保存更改",
				color: "text-yellow-600",
				bgColor: "bg-yellow-50",
				borderColor: "border-yellow-200",
			};
		}

		return {
			icon: <Check className="h-4 w-4" />,
			text: "已保存",
			color: "text-green-600",
			bgColor: "bg-green-50",
			borderColor: "border-green-200",
		};
	};

	const status = getStatus();

	return (
		<div className={cn("flex items-center gap-2", className)}>
			{/* 网络状态指示器 */}
			<div className="flex items-center gap-1">
				{isOnline ? (
					<Wifi className="h-3 w-3 text-green-500" />
				) : (
					<WifiOff className="h-3 w-3 text-orange-500" />
				)}
			</div>

			{/* 保存状态指示器 */}
			<div
				className={cn(
					"flex items-center gap-2 px-3 py-1 rounded-full border text-xs font-medium transition-all duration-200",
					status.color,
					status.bgColor,
					status.borderColor,
				)}
			>
				{status.icon}
				<span>{status.text}</span>
			</div>

			{/* 最后保存时间 */}
			{timeAgo && (
				<span className="text-xs text-gray-500">{timeAgo}</span>
			)}

			{/* 手动保存按钮 */}
			{onManualSave && (isModified || !isOnline) && (
				<Button
					onClick={onManualSave}
					size="sm"
					variant="outline"
					className="h-7 px-2 text-xs"
					disabled={isSaving}
				>
					<Save className="h-3 w-3 mr-1" />
					保存
				</Button>
			)}
		</div>
	);
};
