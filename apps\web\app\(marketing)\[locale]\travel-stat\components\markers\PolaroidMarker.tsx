"use client";
import { useCallback, useEffect, useMemo, useState } from "react";
import { imageStorage } from "../../utils/imageStorage";
import { OptimizedImage } from "./OptimizedImage";
import type { BaseMarkerProps, PolaroidConfig, PolaroidTheme } from "./types";
import { POLAROID_THEMES } from "./types";

interface PolaroidMarkerProps extends BaseMarkerProps {
	theme?: PolaroidTheme;
	customConfig?: Partial<PolaroidConfig>;
	hideOutline?: boolean;
	size?: "small" | "medium" | "large";
	showDescription?: boolean;
	maxDescriptionLength?: number;
	onPhotoClick?: (photoUrl: string, description: string) => void;
	onMarkerClick?: (
		point: any,
		mousePosition: { x: number; y: number },
	) => void;
	mapZoom?: number; // 地图缩放级别
	isVisible?: boolean; // 是否在视野内（用于性能优化）
	priority?: "high" | "medium" | "low"; // 加载优先级
}

// 占位图片路径
const PLACEHOLDER_IMAGE = "/images/polaroid-placeholder.svg";

// 性能优化配置
const PERFORMANCE_CONFIG = {
	// 图片懒加载距离阈值
	LAZY_LOAD_THRESHOLD: 1000, // 1000px
	// 图片缓存数量限制
	MAX_IMAGE_CACHE: 50,
	// 🔧 优化：大幅降低图片尺寸，基于地图缩放级别动态调整
	// 超低质量图片尺寸（远距离查看）
	ULTRA_LOW_QUALITY_SIZE: 250,
	// 低质量图片尺寸（中距离查看）
	LOW_QUALITY_SIZE: 500, // 从100降到80
	// 中等质量图片尺寸（近距离查看）
	MEDIUM_QUALITY_SIZE: 1000, // 新增中等质量
	// 高质量图片尺寸（最近距离查看）
	HIGH_QUALITY_SIZE: 1500, // 从400大幅降到180
	// 地图缩放级别阈值
	ZOOM_THRESHOLDS: {
		ULTRA_LOW: 3, // 6级及以下使用超低质量
		LOW: 6, // 6-10级使用低质量
		MEDIUM: 10, // 10-13级使用中等质量
		HIGH: 16, // 13级以上使用高质量
	},
};

// 🔧 新增：根据地图缩放级别计算最优图片尺寸
function getOptimalImageSize(mapZoom: number, hideOutline: boolean): number {
	const thresholds = PERFORMANCE_CONFIG.ZOOM_THRESHOLDS;

	// 如果是迷你模式，进一步降低尺寸
	const sizeMultiplier = hideOutline ? 0.6 : 1;

	if (mapZoom <= thresholds.ULTRA_LOW) {
		return Math.round(
			PERFORMANCE_CONFIG.ULTRA_LOW_QUALITY_SIZE * sizeMultiplier,
		);
	}
	if (mapZoom <= thresholds.LOW) {
		return Math.round(PERFORMANCE_CONFIG.LOW_QUALITY_SIZE * sizeMultiplier);
	}
	if (mapZoom <= thresholds.MEDIUM) {
		return Math.round(
			PERFORMANCE_CONFIG.MEDIUM_QUALITY_SIZE * sizeMultiplier,
		);
	}
	return Math.round(PERFORMANCE_CONFIG.HIGH_QUALITY_SIZE * sizeMultiplier);
}

export function PolaroidMarker({
	point,
	onRemovePoint,
	isSelected = false,
	scale = 1,
	theme = "classic",
	customConfig,
	hideOutline = false,
	size = "medium",
	showDescription = true,
	maxDescriptionLength = 20,
	onPhotoClick,
	onMarkerClick,
	mapZoom = 10, // 默认缩放级别
	isVisible = true, // 默认可见
	priority = "medium", // 默认优先级
}: PolaroidMarkerProps) {
	const [isHovered, setIsHovered] = useState(false);
	const [imageError, setImageError] = useState(false);
	const [actualImageUrl, setActualImageUrl] = useState<string | null>(null);
	const [imageLoading, setImageLoading] = useState(false);
	const [imageLoaded, setImageLoaded] = useState(false);
	const [shouldLoadImage, setShouldLoadImage] = useState(false);

	// 合并主题配置和自定义配置
	const config = useMemo(() => {
		const baseConfig = POLAROID_THEMES[theme];
		return {
			...baseConfig,
			...customConfig,
		};
	}, [theme, customConfig]);

	// 获取第一张图片
	const firstImage = useMemo(() => {
		console.log("🖼️ PolaroidMarker 图片数据调试:", {
			pointId: point.id.slice(0, 8),
			pointName: point.name,
			hasImages: !!point.images?.length,
			imagesCount: point.images?.length || 0,
			hasPhotos: !!point.photos?.length,
			photosCount: point.photos?.length || 0,
			firstImageUrl: point.images?.[0]?.url
				? `${point.images[0].url.slice(0, 50)}...`
				: null,
			imageUrlType: point.images?.[0]?.url
				? point.images[0].url.startsWith("data:")
					? "base64"
					: point.images[0].url.startsWith("blob:")
						? "blob"
						: point.images[0].url.startsWith("indexeddb:")
							? "indexeddb"
							: point.images[0].url.startsWith("http")
								? "http"
								: "other"
				: null,
		});

		if (point.images && point.images.length > 0) {
			return point.images[0];
		}
		if (point.photos && point.photos.length > 0) {
			return { url: point.photos[0], alt: point.city };
		}
		if ((point as any).image?.url) {
			return (point as any).image;
		}
		return null;
	}, [point.images, point.photos, point.city, point.id, point.name]);

	// 智能加载判断：根据优先级和可见性决定是否加载图片
	useEffect(() => {
		if (!firstImage?.url) {
			setShouldLoadImage(false);
			return;
		}

		// 立即加载高优先级的图片
		if (priority === "high") {
			setShouldLoadImage(true);
			return;
		}

		// 可见时加载中等优先级的图片
		if (priority === "medium" && isVisible) {
			setShouldLoadImage(true);
			return;
		}

		// 低优先级的图片只在hover时加载
		if (priority === "low" && (isVisible || isHovered)) {
			setShouldLoadImage(true);
			return;
		}

		// 默认不加载
		setShouldLoadImage(false);
	}, [firstImage?.url, priority, isVisible, isHovered, mapZoom]);

	// 处理图片URL加载 - 使用新的imageStorage，支持懒加载
	useEffect(() => {
		if (!firstImage?.url || !shouldLoadImage) {
			setActualImageUrl(null);
			setImageLoading(false);
			setImageLoaded(false);
			return;
		}

		// 🔧 性能优化：如果已经有处理过的URL，避免重复处理
		if (actualImageUrl && imageLoaded) {
			return;
		}

		console.log("🔄 PolaroidMarker: 正在处理图片URL:", {
			pointId: point.id.slice(0, 8),
			originalUrl: firstImage.url.slice(0, 50),
			urlType: firstImage.url.startsWith("indexeddb:")
				? "indexeddb"
				: firstImage.url.startsWith("blob:")
					? "blob"
					: firstImage.url.startsWith("data:")
						? "base64"
						: "other",
			priority,
			isVisible,
		});

		setImageLoading(true);
		setImageError(false);

		// 设置加载超时 (10秒)
		const loadingTimeout = setTimeout(() => {
			console.warn("⏰ PolaroidMarker: 图片加载超时:", {
				pointId: point.id.slice(0, 8),
				url: firstImage.url.slice(0, 50),
			});
			setImageLoading(false);
			setImageError(true);
			setActualImageUrl(null);
		}, 10000);

		// 🔧 优化：使用基于地图缩放级别的动态图片尺寸
		const optimalSize = getOptimalImageSize(mapZoom, hideOutline);

		console.log("📏 PolaroidMarker: 动态尺寸计算:", {
			pointId: point.id.slice(0, 8),
			mapZoom,
			hideOutline,
			optimalSize,
			priority,
		});

		// 使用优化的图片处理逻辑，支持智能缓存和动态尺寸
		imageStorage
			.processImageUrl(firstImage.url, {
				priority: priority,
				maxWidth: optimalSize,
				maxHeight: optimalSize,
				useCache: true, // 启用智能缓存
			})
			.then((url) => {
				clearTimeout(loadingTimeout);

				if (url) {
					console.log("✅ PolaroidMarker: 图片URL处理成功:", {
						pointId: point.id.slice(0, 8),
						originalUrl: firstImage.url.slice(0, 50),
						processedUrl: url.slice(0, 50),
						urlType: url.startsWith("blob:")
							? "blob"
							: url.startsWith("data:")
								? "data"
								: url.startsWith("http")
									? "🚨HTTP(未压缩!)"
									: "other",
						priority,
						optimalSize,
						cached: true,
					});

					// 🔧 关键检查：如果处理后仍然是HTTP URL，这是个问题！
					if (url.startsWith("http")) {
						console.error(
							"🚨 PolaroidMarker: 严重错误 - 处理后仍然是HTTP URL!",
							{
								pointId: point.id.slice(0, 8),
								originalUrl: firstImage.url.slice(0, 80),
								processedUrl: url.slice(0, 80),
								priority,
								optimalSize,
							},
						);
					}

					setActualImageUrl(url);
					setImageError(false);
					setImageLoaded(true);
				} else {
					console.warn("⚠️ PolaroidMarker: 图片URL处理失败:", {
						pointId: point.id.slice(0, 8),
						originalUrl: firstImage.url.slice(0, 80),
					});
					setImageError(true);
					setActualImageUrl(null);
					setImageLoaded(false);
				}
			})
			.catch((error) => {
				clearTimeout(loadingTimeout);
				console.error("❌ PolaroidMarker: 图片URL处理异常:", error);
				setImageError(true);
				setActualImageUrl(null);
				setImageLoaded(false);
			})
			.finally(() => {
				setImageLoading(false);
			});

		// 清理函数：如果URL是blob URL，需要释放
		return () => {
			clearTimeout(loadingTimeout);
			if (actualImageUrl?.startsWith("blob:")) {
				URL.revokeObjectURL(actualImageUrl);
			}
		};
	}, [
		firstImage?.url,
		point.id,
		// 🔧 移除actualImageUrl依赖，防止无限循环
		// actualImageUrl,
		shouldLoadImage,
		priority,
		isVisible,
		mapZoom, // 地图缩放级别依赖
		hideOutline, // 轮廓模式依赖
	]);

	// 恢复原有的dimensions逻辑，修复缩放失效问题
	const dimensions = useMemo(() => {
		const baseSizes = {
			small: { width: 60, height: 72 }, // 基于真实比例 4.2:3.5 缩小
			medium: { width: 84, height: 105 }, // 基于真实比例 4.2:3.5
			large: { width: 100, height: 132 }, // 基于真实比例 4.2:3.5 放大
		};

		// 地图缩放：zoom越小，拍立得越小（正比关系）
		const zoomScale = Math.max(0.5, Math.min(4, mapZoom / 4));
		const baseSize = baseSizes[size];
		const finalScale = scale * zoomScale;

		// 真实拍立得比例计算
		const totalWidth = baseSize.width * finalScale;
		const totalHeight = baseSize.height * finalScale;
		// 边框：顶部、左右各6px，底部16px（用于文字）
		const borderTop = 6 * finalScale;
		const borderSide = 6 * finalScale;
		const borderBottom = 16 * finalScale; // 底部更宽，用于文字
		// 照片区域是正方形
		const photoSize = totalWidth - borderSide * 2;

		console.log("🔍 PolaroidMarker Dimensions Debug:", {
			mapZoom,
			zoomScale,
			finalScale,
			totalWidth,
			totalHeight,
			pointId: point.id.slice(0, 8),
		});

		return {
			totalWidth,
			totalHeight,
			borderTop,
			borderSide,
			borderBottom,
			photoSize,
			textAreaHeight: borderBottom,
		};
	}, [size, scale, mapZoom, point.id]);

	// 随机旋转角度（基于point.id确保一致性）
	const rotation = useMemo(() => {
		const hash = point.id.split("").reduce((acc, char) => {
			const updated = (acc << 5) - acc + char.charCodeAt(0);
			return updated & updated;
		}, 0);
		return (hash % (config.rotation * 2)) - config.rotation;
	}, [point.id, config.rotation]);

	// 图片加载错误处理
	const handleImageError = useCallback(
		(e: React.SyntheticEvent<HTMLImageElement>) => {
			console.error("❌ 拍立得图片加载失败:", {
				pointId: point.id.slice(0, 8),
				imageUrl: actualImageUrl
					? `${actualImageUrl.slice(0, 100)}...`
					: null,
				imageUrlType: actualImageUrl
					? actualImageUrl.startsWith("data:")
						? "base64"
						: actualImageUrl.startsWith("blob:")
							? "blob"
							: actualImageUrl.startsWith("http")
								? "http"
								: "other"
					: null,
				errorEvent: e.type,
			});
			setImageError(true);
			setImageLoaded(false);
		},
		[point.id, actualImageUrl],
	);

	// 图片加载成功处理
	const handleImageLoad = useCallback(
		(e: React.SyntheticEvent<HTMLImageElement>) => {
			console.log("✅ 拍立得图片加载成功:", {
				pointId: point.id.slice(0, 8),
				imageSize: {
					naturalWidth: (e.target as HTMLImageElement).naturalWidth,
					naturalHeight: (e.target as HTMLImageElement).naturalHeight,
				},
				imageUrlType: actualImageUrl
					? actualImageUrl.startsWith("data:")
						? "base64"
						: actualImageUrl.startsWith("blob:")
							? "blob"
							: actualImageUrl.startsWith("http")
								? "http"
								: "other"
					: null,
			});
			setImageError(false);
			setImageLoaded(true);
		},
		[point.id, actualImageUrl],
	);

	// 处理拍立得点击 - 触发自定义tooltip
	const handlePolaroidClick = (e: React.MouseEvent) => {
		e.stopPropagation();

		// 获取点击位置
		const mousePosition = {
			x: e.clientX,
			y: e.clientY,
		};

		// 触发marker点击回调
		if (onMarkerClick) {
			onMarkerClick(point, mousePosition);
		}

		// 保留原有的图片点击逻辑作为备用
		if (onPhotoClick) {
			onPhotoClick(actualImageUrl || "", point.description || point.city);
		}

		console.log("🖼️ 拍立得点击:", point);
	};

	// 截取描述文本
	const truncatedDescription = useMemo(() => {
		if (!showDescription || !point.description) return "";
		return point.description.length > maxDescriptionLength
			? `${point.description.substring(0, maxDescriptionLength)}...`
			: point.description;
	}, [point.description, showDescription, maxDescriptionLength]);

	// 格式化日期
	const formattedDate = useMemo(() => {
		if (!point.date) return "";
		try {
			return new Date(point.date).toLocaleDateString("zh-CN", {
				month: "short",
				day: "numeric",
				year: "2-digit",
			});
		} catch {
			return "";
		}
	}, [point.date]);

	// 渲染图片内容
	const renderImageContent = () => {
		// 如果不应该加载图片，显示占位符
		if (!shouldLoadImage) {
			return (
				<div className="w-full h-full bg-gray-100 flex items-center justify-center">
					<div className="text-gray-400 text-xs">📷</div>
				</div>
			);
		}

		// 加载错误或超时，显示错误占位符
		if (imageError) {
			return (
				<div className="w-full h-full bg-red-50 flex flex-col items-center justify-center">
					<div className="text-red-400 text-xs">❌</div>
					<div className="text-red-400 text-[8px] mt-1 text-center">
						加载失败
					</div>
				</div>
			);
		}

		// 正在加载
		if (imageLoading && !actualImageUrl) {
			return (
				<div className="w-full h-full bg-blue-50 flex flex-col items-center justify-center">
					<div className="animate-spin w-4 h-4 border-2 border-blue-300 border-t-blue-600 rounded-full" />
					<div className="text-blue-400 text-[8px] mt-1">
						加载中...
					</div>
				</div>
			);
		}

		// 加载成功
		if (actualImageUrl && imageLoaded) {
			return (
				<OptimizedImage
					src={actualImageUrl}
					alt={firstImage?.alt || point.city}
					className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
					style={{ objectPosition: "center" }}
					onError={handleImageError}
					onLoad={handleImageLoad}
					loading="lazy"
					placeholder={
						<div className="w-full h-full bg-blue-50 flex flex-col items-center justify-center">
							<div className="animate-spin w-4 h-4 border-2 border-blue-300 border-t-blue-600 rounded-full" />
							<div className="text-blue-400 text-[8px] mt-1">
								加载中...
							</div>
						</div>
					}
				/>
			);
		}

		// 处理完成但没有URL的情况（fallback）
		return (
			<div className="w-full h-full bg-gray-100 flex flex-col items-center justify-center">
				<div className="text-gray-400 text-xs">🖼️</div>
				<div className="text-gray-400 text-[8px] mt-1 text-center">
					无图片
				</div>
			</div>
		);
	};

	return (
		<div
			className="relative group"
			style={{
				position: "absolute",
				left: "50%",
				top: "50%",
				transform: "translate(-50%, -50%)",
				zIndex: isSelected ? 20 : 10,
			}}
		>
			{/* 内层容器处理旋转和缩放，确保从中心点变换 */}
			<div
				style={{
					transform: `rotate(${rotation}deg) scale(${isSelected ? 1.15 : 1})`,
					transformOrigin: "center center",
					transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
				}}
			>
				{hideOutline ? (
					// 无轮廓模式 - 迷你拍立得
					<button
						type="button"
						className="transition-all duration-300 cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50"
						style={{
							width: `${dimensions.totalWidth * 0.7}px`,
							height: `${dimensions.totalHeight * 0.7}px`,
							backgroundColor: "#ffffff", // 纯白色背景，不透明
							borderRadius: "3px",
							border: "1px solid #e2e8f0", // 淡灰色边框
							boxShadow: isHovered
								? "0 8px 20px rgba(0,0,0,0.15), 0 2px 8px rgba(0,0,0,0.1)"
								: "0 4px 12px rgba(0,0,0,0.1), 0 1px 4px rgba(0,0,0,0.05)",
							filter: isHovered
								? "brightness(0.98)"
								: "brightness(1)",
						}}
						onMouseEnter={() => setIsHovered(true)}
						onMouseLeave={() => setIsHovered(false)}
						onClick={handlePolaroidClick}
					>
						{/* 迷你照片区域 */}
						<div
							className="absolute bg-gray-100 overflow-hidden border border-gray-200"
							style={{
								top: `${dimensions.borderTop * 0.7}px`,
								left: `${dimensions.borderSide * 0.7}px`,
								width: `${dimensions.photoSize * 0.7}px`,
								height: `${dimensions.photoSize * 0.7}px`,
								borderRadius: "2px",
							}}
						>
							{renderImageContent()}
						</div>
					</button>
				) : (
					// 标准模式 - 完整拍立得相片
					<button
						type="button"
						className="transition-all duration-300 cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50"
						style={{
							width: `${dimensions.totalWidth}px`,
							height: `${dimensions.totalHeight}px`,
							backgroundColor: "#ffffff", // 纯白色背景，不透明
							borderRadius: "4px",
							border: "1px solid #e2e8f0", // 淡灰色边框
							boxShadow: isHovered
								? "0 12px 30px rgba(0,0,0,0.2), 0 4px 12px rgba(0,0,0,0.15)"
								: "0 6px 18px rgba(0,0,0,0.15), 0 2px 6px rgba(0,0,0,0.1)",
							padding: 0,
							position: "relative",
							filter: isHovered
								? "brightness(0.98)"
								: "brightness(1)",
						}}
						onMouseEnter={() => setIsHovered(true)}
						onMouseLeave={() => setIsHovered(false)}
						onClick={handlePolaroidClick}
					>
						{/* 照片区域 - 正方形，居中偏上 */}
						<div
							className="absolute bg-gray-100 overflow-hidden border border-gray-200"
							style={{
								top: `${dimensions.borderTop}px`,
								left: `${dimensions.borderSide}px`,
								width: `${dimensions.photoSize}px`,
								height: `${dimensions.photoSize}px`,
								borderRadius: "2px",
							}}
						>
							{renderImageContent()}
						</div>

						{/* 底部文字区域 - 真实拍立得样式 */}
						<div
							className="absolute bottom-0 left-0 right-0 flex flex-col items-center justify-center px-1"
							style={{
								height: `${dimensions.textAreaHeight}px`,
								paddingTop: "2px",
							}}
						>
							{/* 只显示城市名 */}
							<div
								className="text-gray-800 font-medium text-center leading-tight truncate w-full"
								style={{
									fontSize: `${Math.max(8, dimensions.totalWidth * 0.1)}px`,
									fontFamily: "Arial, sans-serif", // 类似拍立得手写字体
								}}
							>
								{point.city}
							</div>
						</div>
					</button>
				)}

				{/* 性能指示器（开发环境显示）
				{process.env.NODE_ENV === "development" && (
					<div className="absolute -top-2 -right-2 text-xs bg-black text-white px-1 rounded opacity-50">
						{priority === "high"
							? "🔴"
							: priority === "medium"
								? "🟡"
								: "🟢"}
						{shouldLoadImage ? "📷" : "⏸️"}
					</div>
				)} */}

				{/* 增强CSS样式 */}
				<style jsx>{`
					button:hover {
						transform: translateY(-1px);
					}
					
					@keyframes subtle-float {
						0%, 100% { transform: translateY(0px); }
						50% { transform: translateY(-1px); }
					}
					
					.group:hover button {
						animation: subtle-float 3s ease-in-out infinite;
					}
				`}</style>
			</div>
		</div>
	);
}
