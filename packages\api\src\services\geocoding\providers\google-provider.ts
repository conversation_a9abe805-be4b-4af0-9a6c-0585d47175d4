/**
 * Google Maps地理编码提供商
 */

import { BaseGeocodingProvider } from "../base-provider";
import type {
	GeocodingOptions,
	GeocodingProvider,
	UnifiedGeocodingResult,
} from "../types";

/**
 * Google Maps地理编码提供商
 */
export class GoogleProvider extends BaseGeocodingProvider {
	private readonly baseUrl =
		"https://maps.googleapis.com/maps/api/geocode/json";

	getProviderType(): GeocodingProvider {
		return "google";
	}

	protected async performGeocode(
		address: string,
		options: GeocodingOptions,
	): Promise<UnifiedGeocodingResult | null> {
		const params = new URLSearchParams({
			address: address,
			key: this.config.apiKey,
		});

		// 添加可选参数
		if (options.language) {
			params.set("language", options.language);
		}
		if (options.region) {
			params.set("region", options.region);
		}
		if (options.country) {
			params.set("components", `country:${options.country}`);
		}

		const response = await this.makeRequest(`${this.baseUrl}?${params}`);

		if (!response.success || !response.rawData) {
			return null;
		}

		const data = response.rawData;

		if (data.status === "OK" && data.results && data.results.length > 0) {
			const result = data.results[0];
			const location = result.geometry.location;
			const addressComponents = this.parseAddressComponents(
				result.address_components || [],
			);

			return {
				longitude: location.lng,
				latitude: location.lat,
				formattedAddress: result.formatted_address,
				addressComponents,
				confidence: this.mapGoogleConfidence(
					result.geometry.location_type,
				),
				placeTypes: result.types || [],
				viewport: result.geometry.viewport
					? {
							northeast: result.geometry.viewport.northeast,
							southwest: result.geometry.viewport.southwest,
						}
					: undefined,
				placeId: result.place_id,
				provider: "google",
				responseTime: response.responseTime,
			};
		}

		return null;
	}

	protected async performReverseGeocode(
		longitude: number,
		latitude: number,
		options: GeocodingOptions,
	): Promise<UnifiedGeocodingResult | null> {
		const params = new URLSearchParams({
			latlng: `${latitude},${longitude}`,
			key: this.config.apiKey,
		});

		// 添加可选参数
		if (options.language) {
			params.set("language", options.language);
		}

		const response = await this.makeRequest(`${this.baseUrl}?${params}`);

		if (!response.success || !response.rawData) {
			return null;
		}

		const data = response.rawData;

		if (data.status === "OK" && data.results && data.results.length > 0) {
			const result = data.results[0];
			const addressComponents = this.parseAddressComponents(
				result.address_components || [],
			);

			return {
				longitude,
				latitude,
				formattedAddress: result.formatted_address,
				addressComponents,
				confidence: this.mapGoogleConfidence(
					result.geometry.location_type,
				),
				placeTypes: result.types || [],
				viewport: result.geometry.viewport
					? {
							northeast: result.geometry.viewport.northeast,
							southwest: result.geometry.viewport.southwest,
						}
					: undefined,
				placeId: result.place_id,
				provider: "google",
				responseTime: response.responseTime,
			};
		}

		return null;
	}

	/**
	 * 解析Google地址组件
	 */
	private parseAddressComponents(components: any[]): {
		country?: string;
		countryCode?: string;
		province?: string;
		city?: string;
		district?: string;
		street?: string;
		streetNumber?: string;
		postalCode?: string;
	} {
		const result: any = {};

		for (const component of components) {
			const types = component.types;

			if (types.includes("country")) {
				result.country = component.long_name;
				result.countryCode = component.short_name;
			} else if (types.includes("administrative_area_level_1")) {
				result.province = component.long_name;
			} else if (
				types.includes("locality") ||
				types.includes("administrative_area_level_2")
			) {
				result.city = component.long_name;
			} else if (
				types.includes("sublocality") ||
				types.includes("administrative_area_level_3")
			) {
				result.district = component.long_name;
			} else if (types.includes("route")) {
				result.street = component.long_name;
			} else if (types.includes("street_number")) {
				result.streetNumber = component.long_name;
			} else if (types.includes("postal_code")) {
				result.postalCode = component.long_name;
			}
		}

		return result;
	}

	/**
	 * 映射Google的位置类型到置信度
	 */
	private mapGoogleConfidence(
		locationType: string,
	): "high" | "medium" | "low" {
		switch (locationType) {
			case "ROOFTOP":
				return "high";
			case "RANGE_INTERPOLATED":
				return "medium";
			case "GEOMETRIC_CENTER":
				return "medium";
			case "APPROXIMATE":
				return "low";
			default:
				return "medium";
		}
	}
}
