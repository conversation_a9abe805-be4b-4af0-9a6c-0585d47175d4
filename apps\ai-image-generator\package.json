{"name": "@repo/ai-image-generator", "version": "0.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev --port 3001", "start": "next start --port 3001", "type-check": "tsc --noEmit", "lint": "next lint"}, "dependencies": {"@repo/api": "workspace:*", "@repo/auth": "workspace:*", "@repo/database": "workspace:*", "@repo/ai": "workspace:*", "@repo/storage": "workspace:*", "@repo/payments": "workspace:*", "@repo/utils": "workspace:*", "@repo/config": "workspace:*", "@repo/shared-ui": "workspace:*", "@repo/i18n": "workspace:*", "@hookform/resolvers": "^3.3.4", "@tanstack/react-query": "^5.28.4", "lucide-react": "^0.263.1", "next": "15.1.7", "next-intl": "^3.9.4", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.51.0", "tailwindcss": "4.1.3", "typescript": "5.8.3", "ufo": "^1.5.3", "zod": "^3.22.4"}, "devDependencies": {"@repo/tailwind-config": "workspace:*", "@repo/tsconfig": "workspace:*", "@types/node": "22.14.0", "@types/react": "19.0.10", "@types/react-dom": "19.0.4"}}