datasource db {
  provider = "postgresql"
  url      = env("AI_IMAGES_DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/ai-images"
}

generator zod {
  provider         = "zod-prisma-types"
  output           = "../src/zod/ai-images"
  createInputTypes = false
  addIncludeType   = false
  addSelectType    = false
}

// 基础用户模型（简化版，只包含必要字段）
model User {
  id                 String       @id
  name               String
  email              String
  emailVerified      Boolean
  image              String?
  createdAt          DateTime
  updatedAt          DateTime
  username           String?
  role               String?
  banned             Boolean?
  banReason          String?
  banExpires         DateTime?
  onboardingComplete Boolean      @default(false)
  paymentsCustomerId String?
  locale             String?
  
  // AI图片生成器相关关联
  aiImageGenerations AiImageGeneration[]
  aiImageFavorites   AiImageFavorite[]
  aiImageComments    AiImageComment[]
  aiImageTemplates   AiImageTemplate[]
  sessions           Session[]

  @@unique([email])
  @@unique([username])
  @@map("user")
}

// 会话管理（如果需要独立认证）
model Session {
  id        String   @id
  expiresAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  token     String
  createdAt DateTime
  updatedAt DateTime

  @@unique([token])
  @@map("session")
}

// AI图片生成器核心模型
model AiImageGeneration {
  id          String    @id @default(cuid())
  userId      String
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  prompt      String    // 用户输入的提示词
  negativePrompt String? // 负面提示词
  model       String    @default("dall-e-3") // 使用的AI模型
  style       String?   // 图片风格
  size        String    @default("1024x1024") // 图片尺寸
  quality     String    @default("standard") // 图片质量
  imageUrl    String?   // 生成的图片URL
  thumbnailUrl String?  // 缩略图URL
  status      String    @default("pending") // 生成状态: pending, processing, completed, failed
  errorMessage String?  // 错误信息
  metadata    Json?     // 额外的元数据
  isPublic    Boolean   @default(false) // 是否公开
  likes       Int       @default(0) // 点赞数
  downloads   Int       @default(0) // 下载次数
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // 关联的收藏和评论
  favorites   AiImageFavorite[]
  comments    AiImageComment[]
  tags        AiImageTag[]

  @@index([userId])
  @@index([status])
  @@index([isPublic])
  @@index([createdAt])
  @@map("ai_image_generations")
}

// AI图片收藏
model AiImageFavorite {
  id        String            @id @default(cuid())
  userId    String
  user      User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  imageId   String
  image     AiImageGeneration @relation(fields: [imageId], references: [id], onDelete: Cascade)
  createdAt DateTime          @default(now())

  @@unique([userId, imageId])
  @@map("ai_image_favorites")
}

// AI图片评论
model AiImageComment {
  id        String            @id @default(cuid())
  userId    String
  user      User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  imageId   String
  image     AiImageGeneration @relation(fields: [imageId], references: [id], onDelete: Cascade)
  content   String
  createdAt DateTime          @default(now())
  updatedAt DateTime          @updatedAt

  @@index([imageId])
  @@map("ai_image_comments")
}

// AI图片标签
model AiImageTag {
  id        String            @id @default(cuid())
  name      String            @unique
  color     String?           // 标签颜色
  createdAt DateTime          @default(now())
  
  images    AiImageGeneration[]

  @@map("ai_image_tags")
}

// AI图片生成配置模板
model AiImageTemplate {
  id          String   @id @default(cuid())
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  name        String   // 模板名称
  description String?  // 模板描述
  prompt      String   // 提示词模板
  negativePrompt String? // 负面提示词
  model       String   // AI模型
  style       String?  // 风格
  size        String   // 尺寸
  quality     String   // 质量
  isPublic    Boolean  @default(false) // 是否公开分享
  usageCount  Int      @default(0) // 使用次数
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([userId])
  @@index([isPublic])
  @@map("ai_image_templates")
} 