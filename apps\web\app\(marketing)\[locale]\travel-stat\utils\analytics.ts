/**
 * 旅行统计工具专用分析事件追踪工具
 */

import type {
	AnimationTheme,
	AtmosphereTheme,
	MapProjectionType,
	MapStyleType,
} from "../components/types";
import type { ColorThemeType } from "../types/colorTypes";
import type { MarkerStyleType } from "../types/markerTypes";

/**
 * 旅行统计工具事件定义
 */
export interface TravelStatEvents {
	// 页面访问事件
	travel_stat_page_visit: {
		entry_method?: "direct" | "navigation" | "share_link";
		has_existing_data?: boolean;
	};

	// 数据操作事件
	travel_stat_point_added: {
		point_name: string;
		country: string;
		city: string;
		total_points: number;
		search_term?: string;
	};

	travel_stat_point_removed: {
		point_name: string;
		country: string;
		city: string;
		remaining_points: number;
	};

	travel_stat_data_clear_started: {
		points_count: number;
		countries_count: number;
	};

	travel_stat_data_clear_completed: {
		cleared_points: number;
		cleared_countries: number;
	};

	travel_stat_data_loaded: {
		points_count: number;
		countries_count: number;
	};

	// 搜索事件
	travel_stat_search_query: {
		query: string;
		query_length: number;
	};

	travel_stat_search_result: {
		query: string;
		results_count: number;
		search_duration: number;
	};

	travel_stat_search_failed: {
		query: string;
		error_message: string;
	};

	travel_stat_search_result_selected: {
		query: string;
		selected_name: string;
		selected_country: string;
		result_index: number;
	};

	// 地图控制事件
	travel_stat_map_loaded: {
		initial_style: MapStyleType;
		initial_projection: MapProjectionType;
		initial_atmosphere: AtmosphereTheme;
	};

	travel_stat_map_style_change: {
		from_style: MapStyleType;
		to_style: MapStyleType;
	};

	travel_stat_projection_change: {
		from_projection: MapProjectionType;
		to_projection: MapProjectionType;
	};

	travel_stat_location_click: {
		location_name: string;
		location_level: "country" | "region" | "city";
		zoom_level: number;
	};

	travel_stat_projection_change_failed: {
		target_projection: MapProjectionType;
		error_message: string;
	};

	travel_stat_atmosphere_change: {
		from_theme: AtmosphereTheme;
		to_theme: AtmosphereTheme;
	};

	travel_stat_animation_change: {
		from_theme: AnimationTheme;
		to_theme: AnimationTheme;
	};

	travel_stat_color_theme_change: {
		from_theme: ColorThemeType;
		to_theme: ColorThemeType;
	};

	travel_stat_marker_style_change: {
		from_style: MarkerStyleType;
		to_style: MarkerStyleType;
	};

	// 地图视图事件
	travel_stat_map_view_reset: {
		points_count: number;
		projection: MapProjectionType;
	};

	travel_stat_point_focus: {
		point_order: number;
		zoom_level: number;
	};

	travel_stat_country_focus: {
		country_name: string;
		zoom_level: number;
	};

	// 模式切换事件
	travel_stat_mode_switch: {
		from_mode: "edit" | "card-generation";
		to_mode: "edit" | "card-generation";
	};

	// 卡片生成事件
	travel_stat_card_template_change: {
		from_template: string;
		to_template: string;
	};

	travel_stat_card_customization: {
		template: string;
		customization_type: "title" | "subtitle" | "colors" | "layout";
	};

	travel_stat_card_generated: {
		template: string;
		points_count: number;
		countries_count: number;
	};

	// 导出事件
	travel_stat_export_started: {
		points_count: number;
		format: string;
		quality: number;
		scale: number;
	};

	travel_stat_export_completed: {
		points_count: number;
		format: string;
		duration: number;
		quality: number;
		scale: number;
	};

	travel_stat_export_failed: {
		points_count: number;
		format: string;
		duration: number;
		error_message: string;
	};

	// 社交分享事件
	travel_stat_share_attempted: {
		platform:
			| "facebook"
			| "twitter"
			| "instagram"
			| "weibo"
			| "wechat"
			| "link";
		has_points: boolean;
		points_count: number;
	};

	travel_stat_share_completed: {
		platform:
			| "facebook"
			| "twitter"
			| "instagram"
			| "weibo"
			| "wechat"
			| "link";
		points_count: number;
	};

	// 用户交互事件
	travel_stat_tutorial_step: {
		step_number: number;
		step_name: string;
		action: "viewed" | "skipped" | "completed";
	};

	travel_stat_feature_discovery: {
		feature_name: string;
		discovery_method: "click" | "hover" | "tutorial" | "accident";
	};

	// 性能事件
	travel_stat_performance_metric: {
		metric_name: "map_load_time" | "search_response_time" | "export_time";
		duration: number;
		points_count?: number;
	};
}

/**
 * 预定义的分析追踪函数
 */
export const travelStatAnalytics = {
	// 页面访问
	trackPageVisit: (params: TravelStatEvents["travel_stat_page_visit"]) => ({
		event: "travel_stat_page_visit" as const,
		params,
	}),

	// 点位操作
	trackPointAdded: (params: TravelStatEvents["travel_stat_point_added"]) => ({
		event: "travel_stat_point_added" as const,
		params,
	}),

	trackPointRemoved: (
		params: TravelStatEvents["travel_stat_point_removed"],
	) => ({
		event: "travel_stat_point_removed" as const,
		params,
	}),

	// 数据管理
	trackDataClearStarted: (
		params: TravelStatEvents["travel_stat_data_clear_started"],
	) => ({
		event: "travel_stat_data_clear_started" as const,
		params,
	}),

	trackDataLoaded: (params: TravelStatEvents["travel_stat_data_loaded"]) => ({
		event: "travel_stat_data_loaded" as const,
		params,
	}),

	// 搜索操作
	trackSearchQuery: (
		params: TravelStatEvents["travel_stat_search_query"],
	) => ({
		event: "travel_stat_search_query" as const,
		params,
	}),

	trackSearchResult: (
		params: TravelStatEvents["travel_stat_search_result"],
	) => ({
		event: "travel_stat_search_result" as const,
		params,
	}),

	// 模式切换
	trackModeSwitch: (params: TravelStatEvents["travel_stat_mode_switch"]) => ({
		event: "travel_stat_mode_switch" as const,
		params,
	}),

	// 地图控制
	trackMapStyleChange: (
		params: TravelStatEvents["travel_stat_map_style_change"],
	) => ({
		event: "travel_stat_map_style_change" as const,
		params,
	}),

	trackProjectionChange: (
		params: TravelStatEvents["travel_stat_projection_change"],
	) => ({
		event: "travel_stat_projection_change" as const,
		params,
	}),

	// 导出操作
	trackExportStarted: (
		params: TravelStatEvents["travel_stat_export_started"],
	) => ({
		event: "travel_stat_export_started" as const,
		params,
	}),

	trackExportCompleted: (
		params: TravelStatEvents["travel_stat_export_completed"],
	) => ({
		event: "travel_stat_export_completed" as const,
		params,
	}),

	// 分享操作
	trackShareAttempted: (
		params: TravelStatEvents["travel_stat_share_attempted"],
	) => ({
		event: "travel_stat_share_attempted" as const,
		params,
	}),

	// 性能监控
	trackPerformanceMetric: (
		params: TravelStatEvents["travel_stat_performance_metric"],
	) => ({
		event: "travel_stat_performance_metric" as const,
		params,
	}),
};

/**
 * 事件名称常量
 */
export const TRAVEL_STAT_EVENTS = {
	PAGE_VISIT: "travel_stat_page_visit",
	POINT_ADDED: "travel_stat_point_added",
	POINT_REMOVED: "travel_stat_point_removed",
	DATA_CLEAR_STARTED: "travel_stat_data_clear_started",
	DATA_CLEAR_COMPLETED: "travel_stat_data_clear_completed",
	DATA_LOADED: "travel_stat_data_loaded",
	SEARCH_QUERY: "travel_stat_search_query",
	SEARCH_RESULT: "travel_stat_search_result",
	SEARCH_FAILED: "travel_stat_search_failed",
	SEARCH_RESULT_SELECTED: "travel_stat_search_result_selected",
	MAP_LOADED: "travel_stat_map_loaded",
	MAP_STYLE_CHANGE: "travel_stat_map_style_change",
	PROJECTION_CHANGE: "travel_stat_projection_change",
	PROJECTION_CHANGE_FAILED: "travel_stat_projection_change_failed",
	ATMOSPHERE_CHANGE: "travel_stat_atmosphere_change",
	ANIMATION_CHANGE: "travel_stat_animation_change",
	COLOR_THEME_CHANGE: "travel_stat_color_theme_change",
	MARKER_STYLE_CHANGE: "travel_stat_marker_style_change",
	MAP_VIEW_RESET: "travel_stat_map_view_reset",
	POINT_FOCUS: "travel_stat_point_focus",
	COUNTRY_FOCUS: "travel_stat_country_focus",
	MODE_SWITCH: "travel_stat_mode_switch",
	CARD_TEMPLATE_CHANGE: "travel_stat_card_template_change",
	CARD_CUSTOMIZATION: "travel_stat_card_customization",
	CARD_GENERATED: "travel_stat_card_generated",
	EXPORT_STARTED: "travel_stat_export_started",
	EXPORT_COMPLETED: "travel_stat_export_completed",
	EXPORT_FAILED: "travel_stat_export_failed",
	SHARE_ATTEMPTED: "travel_stat_share_attempted",
	SHARE_COMPLETED: "travel_stat_share_completed",
	TUTORIAL_STEP: "travel_stat_tutorial_step",
	FEATURE_DISCOVERY: "travel_stat_feature_discovery",
	PERFORMANCE_METRIC: "travel_stat_performance_metric",
} as const;
