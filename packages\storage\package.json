{"name": "@repo/storage", "version": "0.1.0", "main": "./index.ts", "types": "./index.ts", "scripts": {"type-check": "tsc --noEmit"}, "dependencies": {"@aws-sdk/client-s3": "^3.782.0", "@aws-sdk/s3-request-presigner": "^3.782.0", "@repo/logs": "workspace:*", "@repo/config": "workspace:*", "cos-nodejs-sdk-v5": "^2.12.5"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/node": "^22.14.0"}}