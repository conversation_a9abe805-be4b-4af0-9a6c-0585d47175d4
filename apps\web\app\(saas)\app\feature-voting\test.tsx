"use client";

import { But<PERSON> } from "@ui/components/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { useState } from "react";

/**
 * Feature Voting 基础功能测试组件
 */
export default function FeatureVotingTest() {
	const [activeTest, setActiveTest] = useState<"list" | "form" | "hook">(
		"list",
	);

	return (
		<div className="container mx-auto py-8 px-4">
			<div className="mb-8">
				<h1 className="text-3xl font-bold mb-4">
					Feature Voting 模块测试
				</h1>
				<p className="text-muted-foreground mb-4">
					测试Feature Voting模块的各个组件是否正常工作
				</p>

				{/* 测试选择器 */}
				<div className="flex gap-2 mb-6">
					<Button
						variant={activeTest === "list" ? "primary" : "outline"}
						onClick={() => setActiveTest("list")}
					>
						测试列表组件
					</But<PERSON>>
					<Button
						variant={activeTest === "form" ? "primary" : "outline"}
						onClick={() => setActiveTest("form")}
					>
						测试表单组件
					</Button>
					<Button
						variant={activeTest === "hook" ? "primary" : "outline"}
						onClick={() => setActiveTest("hook")}
					>
						测试Hook
					</Button>
				</div>
			</div>

			{/* 测试内容 */}
			<div className="space-y-6">
				{activeTest === "list" && <TestFeatureRequestList />}
				{activeTest === "form" && <TestSubmitFeatureForm />}
				{activeTest === "hook" && <TestUseFeatureVoting />}
			</div>
		</div>
	);
}

/**
 * 测试FeatureRequestList组件
 */
function TestFeatureRequestList() {
	return (
		<Card>
			<CardHeader>
				<CardTitle>FeatureRequestList 组件测试</CardTitle>
			</CardHeader>
			<CardContent>
				<div>TODO: 等待 feature-voting 模块修复后启用</div>
				{/* <FeatureRequestList
					productId="travel-memo"
					showVoteCounts={true}
					allowVoting={true}
					allowComments={true}
					ui={uiComponents}
				/> */}
			</CardContent>
		</Card>
	);
}

/**
 * 测试SubmitFeatureForm组件
 */
function TestSubmitFeatureForm() {
	const mockProducts = [
		{ id: "travel-memo", name: "旅行足迹", description: "测试产品" },
		{ id: "ai-generator", name: "AI图片生成器", description: "测试产品" },
	];

	const handleSubmit = async (data: any) => {
		console.log("提交数据:", data);
		alert("提交成功！数据已打印到控制台");
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle>SubmitFeatureForm 组件测试</CardTitle>
			</CardHeader>
			<CardContent>
				<div>TODO: 等待 feature-voting 模块修复后启用</div>
				{/* <SubmitFeatureForm
					products={mockProducts}
					onSubmit={handleSubmit}
					ui={uiComponents}
				/> */}
			</CardContent>
		</Card>
	);
}

/**
 * 测试useFeatureVoting Hook
 */
function TestUseFeatureVoting() {
	// TODO: 等待 feature-voting 模块修复后启用
	// const { featureRequests, products, isLoading, error, refresh } =
	// 	useFeatureVoting({
	// 		productId: "travel-memo",
	// 		showVoteCounts: true,
	// 	});

	return (
		<Card>
			<CardHeader>
				<CardTitle>useFeatureVoting Hook 测试</CardTitle>
			</CardHeader>
			<CardContent className="space-y-4">
				<div>TODO: 等待 feature-voting 模块修复后启用</div>
				{/* <div className="grid grid-cols-2 gap-4">
					<div>
						<h4 className="font-medium mb-2">状态信息</h4>
						<ul className="text-sm space-y-1">
							<li>加载状态: {isLoading ? "加载中" : "已完成"}</li>
							<li>错误信息: {error || "无"}</li>
							<li>产品数量: {products.length}</li>
							<li>特性请求数量: {featureRequests.length}</li>
						</ul>
					</div>
					<div>
						<h4 className="font-medium mb-2">操作</h4>
						<Button onClick={refresh} disabled={isLoading}>
							刷新数据
						</Button>
					</div>
				</div> */}
			</CardContent>
		</Card>
	);
}
