import { useRef, useState } from "react";
import type { StyleCategoryKey } from "../constants/types";

export function useMapControlsState() {
	const [isExpanded, setIsExpanded] = useState(false);
	const [activePopover, setActivePopover] = useState<string | null>(null);
	const [selectedStyleCategory, setSelectedStyleCategory] = useState<
		"all" | StyleCategoryKey
	>("all");
	const [popoverPosition, setPopoverPosition] = useState({ top: 0, left: 0 });

	const controlsRef = useRef<HTMLDivElement>(null);
	const buttonRefs = useRef<{ [key: string]: HTMLButtonElement | null }>({});

	return {
		isExpanded,
		setIsExpanded,
		activePopover,
		setActivePopover,
		selectedStyleCategory,
		setSelectedStyleCategory,
		popoverPosition,
		setPopoverPosition,
		controlsRef,
		buttonRefs,
	};
}
