import { Download, Sparkles, Zap } from "lucide-react";
import type { ExportOptions } from "./types";

/**
 * 高质量地图导出配置
 */
export const HIGH_QUALITY_EXPORT: ExportOptions = {
	format: "png",
	quality: 1.0,
	scale: 2,
};

/**
 * 默认导出配置（同高质量配置）
 */
export const DEFAULT_EXPORT = HIGH_QUALITY_EXPORT;

/**
 * 导出预设配置数组
 */
export const exportPresets = [
	{
		id: "high_quality",
		name: "高质量",
		description: "最佳质量，适合打印和高清显示",
		icon: Sparkles,
		color: "bg-purple-600",
		badge: "推荐",
		options: HIGH_QUALITY_EXPORT,
	},
	{
		id: "standard",
		name: "标准质量",
		description: "平衡质量和文件大小，适合社交分享",
		icon: Download,
		color: "bg-blue-600",
		options: {
			format: "png" as const,
			quality: 0.8,
			scale: 1,
		},
	},
	{
		id: "fast",
		name: "快速导出",
		description: "快速生成，适合预览和快速分享",
		icon: Zap,
		color: "bg-green-600",
		options: {
			format: "jpeg" as const,
			quality: 0.7,
			scale: 1,
		},
	},
];
