import { Node, mergeAttributes } from "@tiptap/core";
import { ReactNodeViewRenderer } from "@tiptap/react";
import { v4 as uuidv4 } from "uuid";
import { TravelTimelineNodeView } from "../TravelTimelineNodeView";

// 自定义节点属性接口
export interface TravelTimelineAttributes {
	timelineId: string | null;
	title: string;
	timelineDate: string;
}

export const CustomTravelTimelineNode = Node.create({
	name: "travelTimeline",

	group: "block",

	content: "travelPoint*", // 允许包含多个旅行点位节点

	draggable: true,

	isolating: true,

	addAttributes() {
		return {
			timelineId: {
				default: null,
			},
			title: {
				default: "",
			},
			timelineDate: {
				default: new Date().toISOString(),
			},
		};
	},

	parseHTML() {
		return [
			{
				tag: "section[data-type='travel-timeline']",
			},
		];
	},

	renderHTML({ HTMLAttributes }) {
		return [
			"section",
			mergeAttributes(HTMLAttributes, { "data-type": "travel-timeline" }),
			0,
		];
	},

	addNodeView() {
		return ReactNodeViewRenderer(TravelTimelineNodeView, {
			// 避免在 React 渲染周期内调用 flushSync
			as: "div",
			className: "travel-timeline-wrapper",
			// 添加这些选项来避免 flushSync 问题
			stopEvent: () => false,
			ignoreMutation: () => false,
			// 使用 contentDOMElementTag 而不是默认的处理方式
			contentDOMElementTag: "div",
		});
	},

	addCommands() {
		return {
			insertTravelTimeline:
				() =>
				({ chain }: { chain: any }) => {
					const timelineId = uuidv4();
					// 创建时间线，并在其中自动插入一个点位
					return chain()
						.insertContent({
							type: this.name,
							attrs: {
								timelineId,
								timelineDate: new Date().toISOString(),
								title: `第${new Date().toLocaleDateString("zh-CN")}天`,
							},
							content: [
								{
									type: "travelPoint",
									attrs: {
										pointId: uuidv4(),
										location: "新地点",
										pointDate: new Date().toISOString(),
									},
									content: [
										{
											type: "paragraph",
										},
									],
								},
							],
						})
						.run();
				},
			deleteTravelTimeline:
				() =>
				({ commands }: { commands: any }) => {
					return commands.deleteNode(this.name);
				},
		} as any;
	},
});
