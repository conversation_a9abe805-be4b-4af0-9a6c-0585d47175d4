"use client";

import { GoogleMap, useJsApiLoader } from "@react-google-maps/api";
import React, { useCallback, useEffect, useRef, useState } from "react";
import type {
	LatLng,
	MapBounds,
	MapEventHandler,
	MapEventType,
	MapService,
	MapServiceProvider,
	MapViewOptions,
	MarkerOptions,
} from "../types";

// 默认加载的库，确保包含 'marker'
const defaultLibraries: (
	| "places"
	| "drawing"
	| "geometry"
	| "localContext"
	| "visualization"
	| "marker" // 确保 marker 库被加载
)[] = ["places", "marker"];

// 用于储存API Key的环境变量名
const GOOGLE_MAPS_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || "";

// 开发环境使用测试密钥，确保地图始终能加载
const DEV_API_KEY = "AIzaSyB41DRUbKWJHPxaFjMAwdrzWzbVKartNGg"; // 这是Google官方测试密钥，仅用于开发环境

// 使用环境变量或开发密钥
const apiKey =
	GOOGLE_MAPS_API_KEY ||
	(process.env.NODE_ENV === "development" ? DEV_API_KEY : "");

// 检查API密钥是否未设置，打印警告信息
if (!apiKey && typeof window !== "undefined") {
	console.warn(
		"Google Maps API 密钥未设置，地图可能无法正常工作。请在.env.local文件中添加 NEXT_PUBLIC_GOOGLE_MAPS_API_KEY",
	);
}

// 额外Debug信息
if (process.env.NODE_ENV === "development" && typeof window !== "undefined") {
	console.log("[地图组件] 使用的API密钥:", apiKey ? "已配置" : "未配置");
	console.log("[地图组件] 环境:", process.env.NODE_ENV);
}

/**
 * 使用@react-google-maps/api的Google地图服务实现
 */
class ReactGoogleMapService implements MapService {
	private mapRef: React.MutableRefObject<google.maps.Map | null>;
	private markers: Map<string, google.maps.Marker> = new Map();
	private eventListeners: Map<
		string,
		{
			event: MapEventType;
			handler: MapEventHandler;
			listener: google.maps.MapsEventListener;
		}
	> = new Map();
	private markerIdCounter = 0; // 使用计数器生成唯一ID

	constructor(mapRef: React.MutableRefObject<google.maps.Map | null>) {
		this.mapRef = mapRef;
	}

	async init(element: HTMLElement, options: MapViewOptions): Promise<void> {
		// 在React组件中初始化地图，这里不需要做什么，因为地图已经由组件创建
		return Promise.resolve();
	}

	destroy(): void {
		// 清理所有标记
		this.markers.forEach((marker) => {
			marker.setMap(null);
		});
		this.markers.clear();

		// 清理所有事件监听器
		this.eventListeners.forEach(({ listener }) => {
			google.maps.event.removeListener(listener);
		});
		this.eventListeners.clear();
	}
	addMarker(options: MarkerOptions): string {
		if (!this.mapRef.current) {
			console.error("地图实例尚未初始化，无法添加标记");
			return ""; // 或者抛出错误
		}

		const id = `marker_${this.markerIdCounter++}`;
		const marker = new google.maps.Marker({
			position: options.position,
			map: this.mapRef.current,
			title: options.title,
			icon: options.icon,
			draggable: options.draggable,
			// 根据 isActive 或默认添加 DROP 动画
			animation: options.isActive
				? google.maps.Animation.BOUNCE // 活动标记使用跳动动画
				: google.maps.Animation.DROP, // 默认使用掉落动画
			zIndex: options.isActive ? 1000 : undefined, // 活动标记置顶
		});

		// 添加点击事件
		if (options.onClick) {
			marker.addListener("click", options.onClick);
		}

		// 保存标记引用
		this.markers.set(id, marker);
		console.log(`[ReactGoogleMapService] 添加标记: ${id}`, options);
		return id;
	}

	removeMarker(markerId: string): void {
		const marker = this.markers.get(markerId);
		if (marker) {
			marker.setMap(null); // 从地图上移除
			google.maps.event.clearInstanceListeners(marker); // 清除标记上的所有监听器
			this.markers.delete(markerId);
			console.log(`[ReactGoogleMapService] 移除标记: ${markerId}`);
		} else {
			console.warn(
				`[ReactGoogleMapService] 尝试移除不存在的标记: ${markerId}`,
			);
		}
	}

	// 清除所有标记（供 MarkerClusterer 使用）
	clearMarkers(): void {
		console.log(
			`[ReactGoogleMapService] 清除所有 ${this.markers.size} 个标记`,
		);
		this.markers.forEach((marker, id) => {
			marker.setMap(null);
			google.maps.event.clearInstanceListeners(marker);
		});
		this.markers.clear();
		this.markerIdCounter = 0; // 重置计数器
	}

	updateMarkerPosition(markerId: string, position: LatLng): void {
		const marker = this.markers.get(markerId);
		if (marker) {
			marker.setPosition(position);
		}
	}

	setCenter(center: LatLng): void {
		if (!this.mapRef.current) throw new Error("地图未初始化");
		this.mapRef.current.setCenter(center);
	}

	setZoom(zoom: number): void {
		if (!this.mapRef.current) throw new Error("地图未初始化");
		this.mapRef.current.setZoom(zoom);
	}

	getBounds(): MapBounds {
		if (!this.mapRef.current) throw new Error("地图未初始化");

		const bounds = this.mapRef.current.getBounds();
		if (!bounds) {
			// 如果边界不可用，返回默认值
			return { north: 0, south: 0, east: 0, west: 0 };
		}

		return {
			north: bounds.getNorthEast().lat(),
			south: bounds.getSouthWest().lat(),
			east: bounds.getNorthEast().lng(),
			west: bounds.getSouthWest().lng(),
		};
	}

	getCenter(): LatLng {
		if (!this.mapRef.current) throw new Error("地图未初始化");
		const center = this.mapRef.current.getCenter();
		if (!center) {
			throw new Error("无法获取地图中心点");
		}
		return { lat: center.lat(), lng: center.lng() };
	}

	getZoom(): number {
		if (!this.mapRef.current) throw new Error("地图未初始化");
		return this.mapRef.current.getZoom() || 0;
	}

	fitBounds(points: LatLng[], padding?: number | google.maps.Padding): void {
		if (!this.mapRef.current) {
			console.error("地图实例尚未初始化，无法适配边界");
			return;
		}
		if (points.length === 0) {
			console.warn(
				"[ReactGoogleMapService] fitBounds 收到空点数组，不执行操作",
			);
			return;
		}

		const bounds = new google.maps.LatLngBounds();
		points.forEach((point) => {
			bounds.extend(point);
		});

		console.log(
			"[ReactGoogleMapService] 适配边界:",
			bounds.toJSON(),
			"填充:",
			padding,
		);
		this.mapRef.current.fitBounds(bounds, padding);

		// 限制最大缩放级别，防止只有一个点时缩放过大
		if (
			points.length === 1 &&
			this.mapRef.current?.getZoom &&
			(this.mapRef.current.getZoom() ?? 0) > 15
		) {
			this.mapRef.current.setZoom(15);
		}

		// 检查是否需要缩小
		if (
			this.mapRef.current?.getZoom &&
			(this.mapRef.current.getZoom() ?? 0) > 18
		) {
			this.mapRef.current.setZoom(18);
		}
	}

	addEventListener(type: MapEventType, handler: MapEventHandler): void {
		if (!this.mapRef.current) throw new Error("地图未初始化");

		// 创建唯一的监听器ID
		const listenerId = `${type}_${Date.now()}`;

		// 添加事件监听器
		const listener = this.mapRef.current.addListener(type, (e: any) => {
			handler(e);
		});

		// 保存监听器引用
		this.eventListeners.set(listenerId, { event: type, handler, listener });
	}

	removeEventListener(type: MapEventType, handler: MapEventHandler): void {
		// 查找并移除匹配的事件监听器
		this.eventListeners.forEach((value, key) => {
			if (value.event === type && value.handler === handler) {
				google.maps.event.removeListener(value.listener);
				this.eventListeners.delete(key);
			}
		});
	}

	getNativeMapInstance(): google.maps.Map | null {
		return this.mapRef.current;
	}
}

/**
 * React Google Maps提供商组件
 */
export function ReactGoogleMapProvider({
	children,
	libraries = defaultLibraries,
}: {
	children: React.ReactNode;
	apiKey?: string;
	libraries?: (
		| "places"
		| "drawing"
		| "geometry"
		| "localContext"
		| "visualization"
		| "marker"
	)[];
}) {
	const [loadingState, setLoadingState] = useState<
		"loading" | "loaded" | "error"
	>("loading");
	// 使用useJsApiLoader挂载Google Maps API
	const { isLoaded, loadError } = useJsApiLoader({
		googleMapsApiKey: apiKey,
		libraries: libraries as any, // 类型断言解决类型不匹配问题
	});

	// 监听API加载状态
	useEffect(() => {
		if (isLoaded) {
			console.log("[GoogleMaps] API加载成功");
			setLoadingState("loaded");
		} else if (loadError) {
			console.error("[GoogleMaps] API加载失败:", loadError);
			setLoadingState("error");
		} else {
			console.log("[GoogleMaps] API正在加载中...");
			setLoadingState("loading");
		}
	}, [isLoaded, loadError]);

	if (loadError) {
		return (
			<div className="flex items-center justify-center h-full bg-destructive/10 text-destructive p-4">
				Google Maps API加载失败: {loadError.message}
			</div>
		);
	}

	if (!isLoaded) {
		return (
			<div className="flex items-center justify-center h-full bg-muted p-4">
				正在加载Google Maps API...
			</div>
		);
	}

	return <>{children}</>;
}

/**
 * 响应式Google Map组件 - 使用@react-google-maps/api
 */
export function ReactGoogleMap({
	center,
	zoom,
	markers = [],
	onMapClick,
	onMapIdle,
	onMarkerClick,
	onBoundsChanged,
	mapContainerStyle,
	options = {},
	onLoad,
}: {
	center: LatLng;
	zoom: number;
	markers?: MarkerOptions[];
	onMapClick?: (location: LatLng) => void;
	onMapIdle?: () => void;
	onMarkerClick?: (markerId: string) => void;
	onBoundsChanged?: (bounds: MapBounds) => void;
	mapContainerStyle?: React.CSSProperties;
	options?: google.maps.MapOptions;
	onLoad?: (map: google.maps.Map) => void;
}) {
	const mapRef = useRef<google.maps.Map | null>(null);
	const internalMarkersRef = useRef<Map<string, google.maps.Marker>>(
		new Map(),
	);
	const loadTriggeredRef = useRef<boolean>(false);

	// 处理地图加载完成，确保回调只触发一次
	const handleMapLoad = useCallback(
		(map: google.maps.Map) => {
			console.log("[ReactGoogleMap] 地图实例已加载");
			mapRef.current = map;

			// 确保回调只调用一次
			if (!loadTriggeredRef.current && onLoad) {
				console.log("[ReactGoogleMap] 触发onLoad回调");
				loadTriggeredRef.current = true;
				onLoad(map);
				// 手动触发一个idle事件，确保地图完全初始化
				setTimeout(() => {
					if (onMapIdle) {
						console.log("[ReactGoogleMap] 手动触发idle回调");
						onMapIdle();
					}
				}, 300);
			}

			// 添加事件监听器
			if (onMapIdle) {
				map.addListener("idle", () => {
					console.log("[ReactGoogleMap] idle事件触发");
					onMapIdle();
				});
			}

			if (onBoundsChanged) {
				map.addListener("bounds_changed", () => {
					if (mapRef.current && onBoundsChanged) {
						const bounds = mapRef.current.getBounds();
						if (bounds) {
							onBoundsChanged({
								north: bounds.getNorthEast().lat(),
								south: bounds.getSouthWest().lat(),
								east: bounds.getNorthEast().lng(),
								west: bounds.getSouthWest().lng(),
							});
						}
					}
				});
			}
		},
		[onLoad, onMapIdle, onBoundsChanged],
	);

	// 处理地图点击
	const handleMapClick = useCallback(
		(e: google.maps.MapMouseEvent) => {
			if (onMapClick && e.latLng) {
				onMapClick({
					lat: e.latLng.lat(),
					lng: e.latLng.lng(),
				});
			}
		},
		[onMapClick],
	);

	// 清理函数
	const onUnmount = useCallback(() => {
		console.log("[ReactGoogleMap] 组件卸载，清理标记和监听器");
		internalMarkersRef.current.forEach((marker) => {
			marker.setMap(null);
			google.maps.event.clearInstanceListeners(marker);
		});
		internalMarkersRef.current.clear();

		// 重置加载状态，允许重新触发回调
		loadTriggeredRef.current = false;

		if (mapRef.current) {
			google.maps.event.clearInstanceListeners(mapRef.current);
		}
		mapRef.current = null;
	}, []);

	// 确保容器样式
	const containerStyle: React.CSSProperties = {
		width: "100%",
		height: "100%",
		position: "relative",
		...mapContainerStyle,
	};

	// 确保Google Maps API初始化，提供清晰的错误信息
	useEffect(() => {
		if (!window.google || !window.google.maps) {
			console.error("[ReactGoogleMap] Google Maps API未初始化");
		} else {
			console.log("[ReactGoogleMap] Google Maps API已初始化");
		}

		return () => {
			console.log("[ReactGoogleMap] 组件卸载清理");
			loadTriggeredRef.current = false;
		};
	}, []);

	// 使用带有明确配置的GoogleMap组件
	return (
		<GoogleMap
			mapContainerStyle={containerStyle}
			center={center}
			zoom={zoom}
			options={{
				mapTypeControl: false,
				streetViewControl: false,
				fullscreenControl: false,
				...options,
			}}
			onLoad={handleMapLoad}
			onClick={handleMapClick}
			onUnmount={onUnmount}
		>
			{/* 标记在外部由MarkerClusterer管理 */}
		</GoogleMap>
	);
}

/**
 * Google地图服务提供商 - 使用React Google Maps API封装
 */
export class GoogleMapProvider implements MapServiceProvider {
	private static instance: GoogleMapProvider;
	private isApiLoaded = false;
	private mapRef: React.MutableRefObject<google.maps.Map | null> = {
		current: null,
	};

	private constructor() {}

	// 获取单例实例
	static getInstance(): GoogleMapProvider {
		if (!GoogleMapProvider.instance) {
			GoogleMapProvider.instance = new GoogleMapProvider();
		}
		return GoogleMapProvider.instance;
	}

	// 设置地图引用
	setMapRef(ref: React.MutableRefObject<google.maps.Map | null>): void {
		if (ref.current) {
			console.log("[GoogleMapProvider] 成功设置地图引用");
		} else {
			console.log("[GoogleMapProvider] 设置地图引用 (当前为null)");
		}
		this.mapRef = ref;
	}

	// 加载Google Maps API
	async load(): Promise<void> {
		// 地图API的加载由React组件处理
		this.isApiLoaded = true;
		return Promise.resolve();
	}

	// 创建地图服务实例
	createMapService(): MapService {
		if (!this.mapRef.current) {
			console.warn("[GoogleMapProvider] 创建服务时地图引用为null");
		} else {
			console.log("[GoogleMapProvider] 使用有效的地图引用创建服务");
		}
		return new ReactGoogleMapService(this.mapRef);
	}

	// 检查地图API是否已加载
	isLoaded(): boolean {
		return (
			this.isApiLoaded &&
			typeof window !== "undefined" &&
			!!window.google?.maps
		);
	}

	// 获取提供商名称
	getProviderName(): string {
		return "Google Maps";
	}
}

// 默认导出Google地图提供商实例
export default GoogleMapProvider.getInstance();
