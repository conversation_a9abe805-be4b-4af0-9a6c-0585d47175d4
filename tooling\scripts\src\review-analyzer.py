#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Google Play Reviews Data Analyzer
分析Google Play评论数据，挖掘用户痛点和需求
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import re
import jieba
import jieba.analyse
from collections import Counter
from wordcloud import WordCloud
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# NLP库
import nltk
from nltk.sentiment import SentimentIntensityAnalyzer
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize
from textblob import TextBlob

# 主题建模
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.decomposition import LatentDirichletAllocation
from sklearn.cluster import KMeans

# 确保下载必要的NLTK数据
try:
    nltk.data.find('tokenizers/punkt')
    nltk.data.find('corpora/stopwords')
    nltk.data.find('vader_lexicon')
except LookupError:
    nltk.download('punkt')
    nltk.download('stopwords')
    nltk.download('vader_lexicon')

class ReviewAnalyzer:
    def __init__(self, csv_file_path):
        """初始化分析器"""
        self.data = pd.read_csv(csv_file_path)
        self.sia = SentimentIntensityAnalyzer()
        
        # 设置中文字体（用于matplotlib）
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        print(f"📊 加载数据: {len(self.data)} 条评论")
        print(f"📅 评论时间范围: {self.data['日期'].min()} 到 {self.data['日期'].max()}")
        
    def basic_stats(self):
        """基础统计分析"""
        print("\n" + "="*50)
        print("📈 基础数据统计")
        print("="*50)
        
        # 评分分布
        rating_dist = self.data['评分'].value_counts().sort_index()
        print(f"评分分布:")
        for rating, count in rating_dist.items():
            percentage = count / len(self.data) * 100
            print(f"  {rating}星: {count} 条 ({percentage:.1f}%)")
        
        # 平均评分
        avg_rating = self.data['评分'].mean()
        print(f"\n平均评分: {avg_rating:.2f}")
        
        # 开发者回复率
        reply_rate = (self.data['开发者回复'].notna().sum() / len(self.data)) * 100
        print(f"开发者回复率: {reply_rate:.1f}%")
        
        # 评论长度统计
        self.data['评论长度'] = self.data['评论内容'].str.len()
        print(f"评论长度统计:")
        print(f"  平均长度: {self.data['评论长度'].mean():.0f} 字符")
        print(f"  最长评论: {self.data['评论长度'].max()} 字符")
        print(f"  最短评论: {self.data['评论长度'].min()} 字符")
        
    def sentiment_analysis(self):
        """情感分析"""
        print("\n" + "="*50)
        print("😊 情感分析")
        print("="*50)
        
        # 使用VADER进行情感分析
        sentiments = []
        for text in self.data['评论内容']:
            if pd.notna(text):
                score = self.sia.polarity_scores(str(text))
                sentiments.append(score)
            else:
                sentiments.append({'compound': 0, 'pos': 0, 'neu': 0, 'neg': 0})
        
        sentiment_df = pd.DataFrame(sentiments)
        self.data = pd.concat([self.data, sentiment_df], axis=1)
        
        # 分类情感
        def classify_sentiment(compound):
            if compound >= 0.05:
                return 'positive'
            elif compound <= -0.05:
                return 'negative'
            else:
                return 'neutral'
        
        self.data['情感分类'] = self.data['compound'].apply(classify_sentiment)
        
        # 情感分布
        sentiment_dist = self.data['情感分类'].value_counts()
        print("情感分布:")
        for sentiment, count in sentiment_dist.items():
            percentage = count / len(self.data) * 100
            print(f"  {sentiment}: {count} 条 ({percentage:.1f}%)")
        
        return sentiment_df
    
    def extract_pain_points(self):
        """提取用户痛点"""
        print("\n" + "="*50)
        print("😞 用户痛点分析")
        print("="*50)
        
        # 筛选负面评论（1-2星 或 情感得分低）
        negative_reviews = self.data[
            (self.data['评分'] <= 2) | (self.data['compound'] <= -0.1)
        ]
        
        print(f"负面评论数量: {len(negative_reviews)} 条")
        
        # 痛点关键词（英文）
        pain_keywords = [
            'bug', 'crash', 'slow', 'error', 'problem', 'issue', 'broken',
            'frustrating', 'annoying', 'terrible', 'awful', 'worst',
            'fix', 'improve', 'better', 'should', 'need', 'want',
            'disappointed', 'hate', 'useless', 'waste', 'bad'
        ]
        
        # 分析负面评论中的关键词
        pain_points = Counter()
        for text in negative_reviews['评论内容']:
            if pd.notna(text):
                text_lower = str(text).lower()
                for keyword in pain_keywords:
                    if keyword in text_lower:
                        # 提取包含关键词的句子
                        sentences = text_lower.split('.')
                        for sentence in sentences:
                            if keyword in sentence:
                                pain_points[sentence.strip()] += 1
        
        print("主要痛点:")
        for pain, count in pain_points.most_common(10):
            print(f"  {count}次: {pain[:100]}...")
        
        return negative_reviews, pain_points
    
    def extract_feature_requests(self):
        """提取功能需求"""
        print("\n" + "="*50)
        print("💡 功能需求分析")
        print("="*50)
        
        # 寻找包含建议的评论
        suggestion_keywords = [
            'suggest', 'recommend', 'should add', 'would like', 'wish',
            'feature', 'option', 'ability', 'function', 'improvement',
            'better if', 'great if', 'nice to have', 'missing'
        ]
        
        suggestions = []
        for text in self.data['评论内容']:
            if pd.notna(text):
                text_lower = str(text).lower()
                for keyword in suggestion_keywords:
                    if keyword in text_lower:
                        suggestions.append(text)
                        break
        
        print(f"包含建议的评论: {len(suggestions)} 条")
        
        # 提取高频词汇
        suggestion_text = ' '.join(suggestions)
        words = word_tokenize(suggestion_text.lower())
        
        # 移除停用词
        stop_words = set(stopwords.words('english'))
        filtered_words = [word for word in words if word.isalpha() and word not in stop_words]
        
        word_freq = Counter(filtered_words)
        print("用户建议高频词:")
        for word, count in word_freq.most_common(15):
            print(f"  {word}: {count}次")
        
        return suggestions, word_freq
    
    def topic_modeling(self, n_topics=5):
        """主题建模分析"""
        print("\n" + "="*50)
        print("🎯 主题建模分析")
        print("="*50)
        
        # 预处理文本
        def preprocess_text(text):
            if pd.isna(text):
                return ""
            text = str(text).lower()
            text = re.sub(r'[^a-zA-Z\s]', '', text)
            return text
        
        # 处理评论文本
        processed_texts = [preprocess_text(text) for text in self.data['评论内容']]
        processed_texts = [text for text in processed_texts if len(text) > 10]
        
        # 使用TF-IDF向量化
        vectorizer = TfidfVectorizer(
            max_features=100,
            stop_words='english',
            ngram_range=(1, 2),
            min_df=2
        )
        
        tfidf_matrix = vectorizer.fit_transform(processed_texts)
        
        # LDA主题建模
        lda = LatentDirichletAllocation(
            n_components=n_topics,
            random_state=42,
            max_iter=20
        )
        
        lda.fit(tfidf_matrix)
        
        # 显示主题
        feature_names = vectorizer.get_feature_names_out()
        
        print(f"发现 {n_topics} 个主要话题:")
        topics = []
        for topic_idx, topic in enumerate(lda.components_):
            top_words_idx = topic.argsort()[-10:][::-1]
            top_words = [str(feature_names[i]) for i in top_words_idx]  # 确保转换为字符串
            topics.append(top_words)
            print(f"\n话题 {topic_idx + 1}: {', '.join(top_words[:5])}")
        
        return topics, lda, vectorizer
    
    def generate_insights(self):
        """生成洞察报告"""
        print("\n" + "="*50)
        print("🎯 产品洞察报告")
        print("="*50)
        
        # 获取低评分评论的主要抱怨
        low_rating = self.data[self.data['评分'] <= 2]
        high_rating = self.data[self.data['评分'] >= 4]
        
        print("💡 产品优化建议:")
        print("1. 解决技术问题:")
        
        # 分析技术相关问题
        tech_issues = ['crash', 'bug', 'slow', 'freeze', 'error', 'loading']
        for issue in tech_issues:
            count = sum(1 for text in low_rating['评论内容'] 
                       if pd.notna(text) and issue in str(text).lower())
            if count > 0:
                print(f"   - {issue}: {count} 次提及")
        
        print("\n2. 功能改进方向:")
        
        # 分析功能相关建议
        feature_requests = ['add', 'improve', 'better', 'option', 'feature']
        for request in feature_requests:
            count = sum(1 for text in self.data['评论内容'] 
                       if pd.notna(text) and request in str(text).lower())
            if count > 0:
                print(f"   - {request}: {count} 次提及")
        
        print("\n3. 用户体验优化:")
        ux_keywords = ['easy', 'difficult', 'confusing', 'intuitive', 'user-friendly']
        for keyword in ux_keywords:
            count = sum(1 for text in self.data['评论内容'] 
                       if pd.notna(text) and keyword in str(text).lower())
            if count > 0:
                print(f"   - {keyword}: {count} 次提及")
        
    def create_visualizations(self):
        """创建可视化图表"""
        print("\n" + "="*50)
        print("📊 生成可视化图表")
        print("="*50)
        
        # 1. 评分分布图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 评分分布
        self.data['评分'].value_counts().sort_index().plot(kind='bar', ax=axes[0,0])
        axes[0,0].set_title('评分分布')
        axes[0,0].set_xlabel('评分')
        axes[0,0].set_ylabel('评论数量')
        
        # 情感分布
        self.data['情感分类'].value_counts().plot(kind='pie', ax=axes[0,1], autopct='%1.1f%%')
        axes[0,1].set_title('情感分布')
        
        # 评论长度分布
        axes[1,0].hist(self.data['评论长度'], bins=30, alpha=0.7)
        axes[1,0].set_title('评论长度分布')
        axes[1,0].set_xlabel('字符数')
        axes[1,0].set_ylabel('频次')
        
        # 评分与情感的关系
        sentiment_rating = pd.crosstab(self.data['评分'], self.data['情感分类'])
        sentiment_rating.plot(kind='bar', stacked=True, ax=axes[1,1])
        axes[1,1].set_title('评分与情感关系')
        axes[1,1].set_xlabel('评分')
        axes[1,1].legend(title='情感')
        
        plt.tight_layout()
        plt.savefig('review_analysis.png', dpi=300, bbox_inches='tight')
        print("✅ 可视化图表已保存为 review_analysis.png")
        
        # 2. 词云图
        try:
            negative_text = ' '.join(self.data[self.data['compound'] < -0.1]['评论内容'].dropna())
            if negative_text:
                wordcloud = WordCloud(
                    width=800, height=400,
                    background_color='white',
                    max_words=100
                ).generate(negative_text)
                
                plt.figure(figsize=(12, 6))
                plt.imshow(wordcloud, interpolation='bilinear')
                plt.axis('off')
                plt.title('负面评论词云图', fontsize=16)
                plt.savefig('negative_wordcloud.png', dpi=300, bbox_inches='tight')
                print("✅ 负面评论词云图已保存为 negative_wordcloud.png")
        except Exception as e:
            print(f"⚠️ 词云图生成失败: {e}")
    
    def export_insights(self, filename='review_insights.txt'):
        """导出分析结果"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("Google Play 评论分析报告\n")
            f.write("="*50 + "\n\n")
            
            # 基础统计
            f.write("1. 基础统计\n")
            f.write(f"总评论数: {len(self.data)}\n")
            f.write(f"平均评分: {self.data['评分'].mean():.2f}\n")
            f.write(f"开发者回复率: {(self.data['开发者回复'].notna().sum() / len(self.data) * 100):.1f}%\n\n")
            
            # 评分分布
            f.write("2. 评分分布\n")
            for rating, count in self.data['评分'].value_counts().sort_index().items():
                percentage = count / len(self.data) * 100
                f.write(f"{rating}星: {count} 条 ({percentage:.1f}%)\n")
            f.write("\n")
            
            # 情感分析
            f.write("3. 情感分析\n")
            for sentiment, count in self.data['情感分类'].value_counts().items():
                percentage = count / len(self.data) * 100
                f.write(f"{sentiment}: {count} 条 ({percentage:.1f}%)\n")
            f.write("\n")
            
            # 主要痛点
            f.write("4. 主要用户痛点\n")
            negative_reviews = self.data[self.data['评分'] <= 2]
            common_issues = []
            for text in negative_reviews['评论内容']:
                if pd.notna(text):
                    if any(word in str(text).lower() for word in ['bug', 'crash', 'slow']):
                        common_issues.append(str(text)[:100])
            
            for i, issue in enumerate(common_issues[:10], 1):
                f.write(f"{i}. {issue}...\n")
        
        print(f"✅ 分析报告已导出为 {filename}")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("使用方法: python review-analyzer.py <csv文件路径>")
        print("例如: python review-analyzer.py output/google-play-reviews-lib-xxx.csv")
        return
    
    csv_file = sys.argv[1]
    
    try:
        # 创建分析器
        analyzer = ReviewAnalyzer(csv_file)
        
        # 执行分析
        analyzer.basic_stats()
        analyzer.sentiment_analysis()
        analyzer.extract_pain_points()
        analyzer.extract_feature_requests()
        analyzer.topic_modeling()
        analyzer.generate_insights()
        analyzer.create_visualizations()
        analyzer.export_insights()
        
        print("\n🎉 分析完成！查看生成的文件:")
        print("  - review_analysis.png (可视化图表)")
        print("  - negative_wordcloud.png (词云图)")
        print("  - review_insights.txt (分析报告)")
        
    except FileNotFoundError:
        print(f"❌ 文件未找到: {csv_file}")
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")

if __name__ == "__main__":
    main() 