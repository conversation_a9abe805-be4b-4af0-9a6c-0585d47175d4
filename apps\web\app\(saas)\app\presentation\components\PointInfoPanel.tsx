"use client";

import { cn } from "@ui/lib";
import { useCallback, useEffect, useRef, useState } from "react";
import { ScrollbarStyles } from "../../components/diary/travel-point-form/ScrollbarStyles";
import { ANIMATION_DURATION } from "../constants";
import { createLogger } from "../hooks/map-story/utils";
import type { FrontendTravelPoint } from "../types";
import { formatDate } from "../utils/date-utils";

// 创建日志记录器
const loggerPanel = createLogger("PointInfoPanel");
const loggerTypewriter = createLogger("Typewriter");

interface PointInfoPanelProps {
	point: FrontendTravelPoint | null;
	isVisible: boolean;
	animationDuration?: number; // ms for fade, 现在是可选的
	onTypingComplete?: () => void; // 新增回调函数
}

// 打字机效果 Hook
function useTypewriter(
	text: string,
	baseSpeed = 1000,
	delay = 100,
	startTyping = false,
	onCompleteProp?: () => void, // 重命名以区分内部使用的 ref
) {
	const [displayText, setDisplayText] = useState("");
	const [isTyping, setIsTyping] = useState(false);
	const [animatingChars, setAnimatingChars] = useState<Set<number>>(
		new Set(),
	);
	const timerRef = useRef<NodeJS.Timeout | null>(null);
	// 使用 charIndexRef 替代组件内的 index 变量，确保其在重渲染间持久化
	const charIndexRef = useRef(0);
	const hasStartedRef = useRef(false);
	const prevTextRef = useRef("");
	const instanceIdRef = useRef(
		`tw_${Math.random().toString(36).substr(2, 9)}`,
	);

	// 使用 ref 存储 onCompleteProp，避免其成为 useEffect 的依赖项导致不必要的重触发
	const onCompleteRef = useRef(onCompleteProp);
	useEffect(() => {
		onCompleteRef.current = onCompleteProp;
	}, [onCompleteProp]);

	loggerTypewriter.debug(`[${instanceIdRef.current}] 初始化/重渲染`, {
		textLength: text?.length || 0,
		startTyping,
		hasStarted: hasStartedRef.current,
		prevText: prevTextRef.current,
		currentText: text,
	});

	const cleanup = useCallback(() => {
		if (timerRef.current) {
			loggerTypewriter.debug(`[${instanceIdRef.current}] 清理定时器`, {
				timerId: timerRef.current,
			});
			clearTimeout(timerRef.current);
			timerRef.current = null;
		}
		// setIsTyping(false); // 不在 cleanup 中直接设置 isTyping，由主 effect 控制
	}, [instanceIdRef]); // 移除 isTyping

	const reset = useCallback(() => {
		loggerTypewriter.debug(`[${instanceIdRef.current}] 重置打字机效果`, {
			prevText: prevTextRef.current,
			newText: text,
		});
		cleanup();
		charIndexRef.current = 0;
		setDisplayText("");
		setAnimatingChars(new Set());
		hasStartedRef.current = false;
		setIsTyping(false); // 确保在重置时停止打字状态
	}, [cleanup, text, instanceIdRef]);

	const getTypingDelay = useCallback(
		(char: string, prevChar: string | null) => {
			let currentDelay = baseSpeed;
			if (prevChar) {
				if (/[.!?。！？]/.test(prevChar)) {
					return currentDelay * 5;
				}
				if (/[,;:，；：、]/.test(prevChar)) {
					return currentDelay * 3;
				}
				if (/[——…]/.test(prevChar)) {
					return currentDelay * 4;
				}
				if (/["'""''（）【】《》]/.test(prevChar)) {
					return currentDelay * 2;
				}
			}
			const randomFactor = 0.7 + Math.random() * 0.6;
			if (prevChar === " " || prevChar === "\n") {
				currentDelay *= 1.5;
			}
			return currentDelay * randomFactor;
		},
		[baseSpeed],
	);

	// 移除字符动画状态
	const removeCharAnimation = useCallback((charIndex: number) => {
		setAnimatingChars((prev) => {
			const newSet = new Set(prev);
			newSet.delete(charIndex);
			return newSet;
		});
	}, []);

	useEffect(() => {
		if (text !== prevTextRef.current) {
			loggerTypewriter.info(
				`[${instanceIdRef.current}] 文本已变化，执行重置`,
				{
					prevText: prevTextRef.current,
					newText: text,
				},
			);
			prevTextRef.current = text;
			reset();
		}
	}, [text, reset, instanceIdRef]);

	useEffect(() => {
		loggerTypewriter.debug(
			`[${instanceIdRef.current}] 主打字效果 useEffect 检查`,
			{
				startTyping,
				hasStarted: hasStartedRef.current,
				textExists: !!text,
			},
		);

		if (startTyping && !hasStartedRef.current && text) {
			loggerTypewriter.info(
				`[${instanceIdRef.current}] 条件满足，开始打字流程`,
				{
					textLength: text.length,
					delay,
				},
			);
			hasStartedRef.current = true;
			setIsTyping(true);
			charIndexRef.current = 0; // 确保从头开始
			setDisplayText(""); // 清空之前的文本
			setAnimatingChars(new Set()); // 清空动画状态

			timerRef.current = setTimeout(() => {
				loggerTypewriter.debug(
					`[${instanceIdRef.current}] 初始延迟后，开始逐字打字`,
					{ textLength: text.length },
				);

				const typeNextChar = () => {
					if (charIndexRef.current < text.length) {
						const currentChar = text.charAt(charIndexRef.current);
						const prevChar =
							charIndexRef.current > 0
								? text.charAt(charIndexRef.current - 1)
								: null;

						const currentIndex = charIndexRef.current;
						setDisplayText((prev) => prev + currentChar);

						// 添加新字符到动画集合
						setAnimatingChars(
							(prev) =>
								new Set([...Array.from(prev), currentIndex]),
						);

						// 立即移除动画状态，让CSS过渡生效
						setTimeout(() => {
							removeCharAnimation(currentIndex);
						}, 50); // 缩短到50ms，让动画更快开始

						charIndexRef.current++;

						if (charIndexRef.current % 10 === 0) {
							loggerTypewriter.debug(
								`[${instanceIdRef.current}] 打字进度`,
								{
									index: charIndexRef.current,
									totalLength: text.length,
								},
							);
						}

						// 检查是否仍应继续打字 (例如，startTyping 可能变为 false)
						if (!hasStartedRef.current) {
							// 或者检查一个更明确的取消标志
							loggerTypewriter.info(
								`[${instanceIdRef.current}] 打字被中途取消`,
							);
							cleanup();
							return;
						}

						const nextDelay = getTypingDelay(currentChar, prevChar);
						timerRef.current = setTimeout(typeNextChar, nextDelay);
					} else {
						loggerTypewriter.info(
							`[${instanceIdRef.current}] 打字机效果完成`,
							{
								textLength: text.length,
							},
						);
						setIsTyping(false);
						if (onCompleteRef.current) {
							loggerTypewriter.debug(
								`[${instanceIdRef.current}] 调用完成回调`,
							);
							onCompleteRef.current();
						}
					}
				};
				typeNextChar(); // 启动第一个字符的打字
			}, delay);

			// 这个 useEffect 的清理函数
			return () => {
				loggerTypewriter.debug(
					`[${instanceIdRef.current}] 主打字 useEffect 清理函数执行 (startTyping: ${startTyping}, hasStarted: ${hasStartedRef.current})`,
					{ text, isTypingState: isTyping }, // 修正日志对象
				);
				cleanup();
			};
		}
		// 这部分逻辑不再需要 else if，因为如果 startTyping 变为 false，
		// hasStartedRef.current 应该被 reset() 中的逻辑置为 false，
		// 除非reset的调用时机有问题，或者有其他副作用在改变hasStartedRef。
		// 最好的做法是，当 startTyping 变为 false 时，在 PointInfoPanel 中显式调用 reset。
		// 或者，在这里的清理函数中处理（如果 effect 因 startTyping:false 而重新运行）
		if (!startTyping && hasStartedRef.current) {
			loggerTypewriter.info(
				`[${instanceIdRef.current}] startTyping 变为 false，清理并重置（通过主useEffect）`,
			);
			reset();
		}
		// 依赖项是关键：确保只在这些值实际变化时才重新运行此 effect
		// 移除了 isTyping, cleanup, reset, getTypingDelay，因为它们通过 useCallback/useRef 保证了稳定性或被正确处理
	}, [
		startTyping,
		text,
		delay,
		instanceIdRef,
		getTypingDelay,
		reset,
		cleanup,
		removeCharAnimation,
	]);

	return { displayText, isTyping, animatingChars, reset };
}

// 将字符串或Date对象转换为Date对象
function toDate(dateValue: string | Date): Date {
	if (dateValue instanceof Date) {
		return dateValue;
	}
	// 尝试将字符串转为日期
	return new Date(dateValue);
}

// 安全地格式化日期，处理无效日期情况
function safeFormatDate(date: Date | string | undefined): string {
	if (!date) return "未知日期";

	try {
		const dateObj = date instanceof Date ? date : new Date(date);

		// 检查日期是否有效
		if (Number.isNaN(dateObj.getTime())) {
			console.warn("无效的日期值:", date);
			return "无效日期";
		}

		return formatDate(dateObj);
	} catch (error) {
		console.error("日期格式化错误:", error);
		return "日期错误";
	}
}

export function PointInfoPanel({
	point,
	isVisible,
	animationDuration = ANIMATION_DURATION.contentFade,
	onTypingComplete,
}: PointInfoPanelProps) {
	const [isMounted, setIsMounted] = useState(false);
	const [isAnimating, setIsAnimating] = useState(false);

	// 添加组件实例ID以追踪生命周期
	const componentIdRef = useRef(
		`panel_${Math.random().toString(36).substr(2, 9)}`,
	);

	// 记录点位变化
	const prevPointIdRef = useRef<string | null>(null);
	const currentPointId = point?.id || null;

	// 当前点位的描述文本
	const descriptionText = point?.description || "";

	// 添加滚动容器的ref
	const scrollContainerRef = useRef<HTMLDivElement>(null);

	// 打字完成回调包装器
	const handleTypingComplete = useCallback(() => {
		loggerPanel.info(`[${componentIdRef.current}] 打字完成回调触发`, {
			pointId: currentPointId,
			isMounted,
			isAnimating,
			isVisible,
		});

		if (onTypingComplete) {
			onTypingComplete();
		}
	}, [onTypingComplete, currentPointId, isMounted, isAnimating, isVisible]);

	// 使用打字机效果
	const { displayText, isTyping, animatingChars } = useTypewriter(
		descriptionText,
		60, // 基础打字速度，毫秒/字符 (比原来慢一些)
		500, // 延迟，等待面板动画完成后再开始
		isMounted && isVisible, // 仅当面板显示并动画完成后开始
		handleTypingComplete, // 传递打字完成回调
	);

	// 自动滚动到底部的效果
	useEffect(() => {
		if (scrollContainerRef.current && isTyping) {
			const container = scrollContainerRef.current;
			// 使用平滑滚动到底部
			container.scrollTo({
				top: container.scrollHeight,
				behavior: "smooth",
			});
		}
	}, [displayText, isTyping]);

	// 记录点位变化
	useEffect(() => {
		if (currentPointId !== prevPointIdRef.current) {
			loggerPanel.info(`[${componentIdRef.current}] 点位变更`, {
				prevPointId: prevPointIdRef.current,
				currentPointId,
				isMounted,
				isAnimating,
				isVisible,
			});
			prevPointIdRef.current = currentPointId;
		}
	}, [currentPointId, isMounted, isAnimating, isVisible]);

	// Effect 1: 处理组件的挂载和卸载逻辑
	useEffect(() => {
		loggerPanel.debug(
			`[${componentIdRef.current}] 可见性变化 Effect 触发`,
			{
				isVisible,
				isMounted,
				isAnimating,
			},
		);

		let unmountTimer: NodeJS.Timeout | null = null;
		if (isVisible) {
			if (!isMounted) {
				loggerPanel.info(`[${componentIdRef.current}] 设置为已挂载`, {
					pointId: currentPointId,
				});
				setIsMounted(true);
			}
		} else {
			// 仅当 isMounted 为 true 时，才启动卸载动画和逻辑
			if (isMounted) {
				loggerPanel.info(`[${componentIdRef.current}] 准备卸载`, {
					pointId: currentPointId,
					animationDuration,
				});
				// 退场动画由下面的 isAnimating effect 处理
				unmountTimer = setTimeout(() => {
					loggerPanel.info(
						`[${componentIdRef.current}] 设置为未挂载`,
					);
					setIsMounted(false); // 动画应该已经结束
				}, animationDuration); // animationDuration 是动画的总时长
			}
		}
		return () => {
			if (unmountTimer) {
				loggerPanel.debug(`[${componentIdRef.current}] 清理卸载定时器`);
				clearTimeout(unmountTimer);
			}
		};
	}, [isVisible, isMounted, animationDuration, currentPointId]);

	// Effect 2: 处理动画状态的切换，依赖于 isVisible 和 isMounted
	useEffect(() => {
		loggerPanel.debug(`[${componentIdRef.current}] 动画状态 Effect 触发`, {
			isVisible,
			isMounted,
			isAnimating,
		});

		let animationStartTimer: NodeJS.Timeout | null = null;
		if (isVisible && isMounted) {
			// 只有当组件可见并且已挂载时
			// 启动入场动画
			animationStartTimer = setTimeout(() => {
				loggerPanel.info(`[${componentIdRef.current}] 设置为正在动画`, {
					pointId: currentPointId,
				});
				setIsAnimating(true);
			}, 20); // 小延迟，确保DOM已挂载完毕
		} else {
			// 如果不可见或未挂载，则不应该处于 animating 状态
			if (isAnimating) {
				loggerPanel.info(
					`[${componentIdRef.current}] 设置为不在动画中`,
					{
						pointId: currentPointId,
						isVisible,
						isMounted,
					},
				);
				setIsAnimating(false);
			}
		}
		return () => {
			if (animationStartTimer) {
				loggerPanel.debug(
					`[${componentIdRef.current}] 清理动画开始定时器`,
				);
				clearTimeout(animationStartTimer);
			}
		};
	}, [isVisible, isMounted, isAnimating, currentPointId]);

	// 如果没有挂载或没有点位数据，则不渲染
	if (!isMounted || !point) {
		loggerPanel.debug(`[${componentIdRef.current}] 不渲染组件`, {
			isMounted,
			hasPoint: !!point,
			pointId: currentPointId,
		});
		return null;
	}

	loggerPanel.debug(`[${componentIdRef.current}] 渲染组件`, {
		pointId: currentPointId,
		isAnimating,
		isTyping,
		displayTextLength: displayText.length,
	});

	return (
		<div
			className={cn(
				"w-full max-w-lg mx-auto p-4 bg-slate-800/70 backdrop-blur-md rounded-lg shadow-lg border border-slate-700/50",
				"transition-all duration-500 ease-in-out",
				isAnimating
					? "opacity-100 transform translate-y-0"
					: "opacity-0 transform translate-y-16",
			)}
			style={{
				transitionDuration: `${animationDuration}ms`,
				transitionTimingFunction: "cubic-bezier(0.34, 1.56, 0.64, 1)",
			}}
		>
			<ScrollbarStyles />
			<div className="bg-slate-900/60 backdrop-blur-sm rounded-lg p-3">
				<div className="flex justify-between items-center mb-3">
					<h3 className="text-lg font-semibold text-slate-100">
						{point.address || point.title}
					</h3>
					<p className="text-sm text-slate-400">
						{safeFormatDate(point.timestamp)}
					</p>
				</div>
				<div
					ref={scrollContainerRef}
					className="text-sm text-slate-300 max-h-32 overflow-y-auto pr-2 custom-scrollbar travel-theme"
				>
					{/* 打字机效果文本 - 逐字符渲染以支持动画 */}
					{displayText.split("").map((char, index) => (
						<span
							key={index}
							className={cn(
								"inline-block transition-all duration-300 ease-out",
								animatingChars.has(index)
									? "opacity-0 transform scale-90 translate-y-1"
									: "opacity-100 transform scale-100 translate-y-0",
							)}
						>
							{char === " " ? "\u00A0" : char}{" "}
							{/* 保持空格的宽度 */}
						</span>
					))}
					{/* 打字光标，仅在打字时显示 */}
					{isTyping && (
						<span className="text-blue-400 animate-pulse ml-1">
							|
						</span>
					)}
				</div>
			</div>
		</div>
	);
}

// TypeWriter Character Animation Styles (添加到全局CSS或这里)
// 可以考虑将这些样式移到ScrollbarStyles组件中，或创建单独的样式组件
