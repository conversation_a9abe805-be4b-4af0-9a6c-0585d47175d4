/**
 * 地图导出选项
 */
export interface ExportOptions {
	/** 导出格式 */
	format: "png" | "jpeg";
	/** 图片质量 (0-1) */
	quality: number;
	/** 导出缩放比例 */
	scale: number;
	/** 文件名（可选，不提供则不自动下载） */
	filename?: string;
}

/**
 * 导出上下文
 */
export interface ExportContext {
	/** 地图引用 */
	mapRef: any;
	/** 旅行点位数据 */
	travelPoints: any[];
	/** 设置当前导出状态 */
	setCurrentMethod: (method: string) => void;
}

/**
 * 导出结果
 */
export interface ExportResult {
	/** 导出的画布 */
	canvas: HTMLCanvasElement;
	/** 导出耗时（毫秒） */
	duration: number;
	/** 导出状态 */
	success: boolean;
	/** 错误信息（如果失败） */
	error?: string;
}
