/**
 * 国家名称映射配置
 * 用于标准化 Mapbox Geocoding API 返回的国家名称与本地 GeoJSON 文件中的国家名称
 */

// 主要的国家名称映射表 - 将各种变体统一到 GeoJSON 中的标准名称
export const COUNTRY_NAME_MAPPING: Record<string, string> = {
	// === 美国 ===
	"United States": "America",
	"United States of America": "America",
	USA: "America",
	US: "America",
	us: "America", // 小写国家代码
	America: "America", // 保持原样
	"United States (US)": "America",
	"US - United States": "America",
	"U.S.": "America",
	"U.S.A.": "America",
	"U.S.A": "America",
	"United States (USA)": "America",
	"The United States": "America",
	"The United States of America": "America",
	"Estados Unidos": "America", // 西班牙语
	"États-Unis": "America", // 法语
	美国: "America", // 中文
	美利坚合众国: "America", // 中文全称

	// === 英国 ===
	"United Kingdom": "United Kingdom",
	UK: "United Kingdom",
	"Great Britain": "United Kingdom",
	Britain: "United Kingdom",
	England: "United Kingdom",
	Scotland: "United Kingdom",
	Wales: "United Kingdom",
	"Northern Ireland": "United Kingdom",

	// === 中国 ===
	China: "China",
	中国: "China",
	"People's Republic of China": "China",
	PRC: "China",
	中华人民共和国: "China",

	// === 俄罗斯 ===
	Russia: "Russia",
	"Russian Federation": "Russia",
	Россия: "Russia",
	СССР: "Russia",

	// === 德国 ===
	Germany: "Germany",
	Deutschland: "Germany",
	"Federal Republic of Germany": "Germany",

	// === 法国 ===
	France: "France",
	"French Republic": "France",
	"République française": "France",

	// === 意大利 ===
	Italy: "Italy",
	Italia: "Italy",
	"Italian Republic": "Italy",

	// === 西班牙 ===
	Spain: "Spain",
	España: "Spain",
	"Kingdom of Spain": "Spain",

	// === 日本 ===
	Japan: "Japan",
	日本: "Japan",
	Nippon: "Japan",
	Nihon: "Japan",

	// === 韩国 ===
	"South Korea": "South Korea",
	Korea: "South Korea",
	"Republic of Korea": "South Korea",
	한국: "South Korea",
	대한민국: "South Korea",

	// === 朝鲜 ===
	"North Korea": "North Korea",
	"Democratic People's Republic of Korea": "North Korea",
	DPRK: "North Korea",
	조선: "North Korea",

	// === 印度 ===
	India: "India",
	भारत: "India",
	"Republic of India": "India",
	Hindustan: "India",

	// === 加拿大 ===
	Canada: "Canada",

	// === 澳大利亚 ===
	Australia: "Australia",
	"Commonwealth of Australia": "Australia",

	// === 巴西 ===
	Brazil: "Brazil",
	Brasil: "Brazil",
	"Federative Republic of Brazil": "Brazil",

	// === 阿根廷 ===
	Argentina: "Argentina",
	"Argentine Republic": "Argentina",

	// === 墨西哥 ===
	Mexico: "Mexico",
	México: "Mexico",
	"United Mexican States": "Mexico",

	// === 荷兰 ===
	Netherlands: "Netherlands",
	Holland: "Netherlands",
	"Kingdom of the Netherlands": "Netherlands",
	Nederland: "Netherlands",

	// === 比利时 ===
	Belgium: "Belgium",
	België: "Belgium",
	Belgique: "Belgium",
	"Kingdom of Belgium": "Belgium",

	// === 瑞士 ===
	Switzerland: "Switzerland",
	Schweiz: "Switzerland",
	Suisse: "Switzerland",
	Svizzera: "Switzerland",
	"Swiss Confederation": "Switzerland",

	// === 奥地利 ===
	Austria: "Austria",
	Österreich: "Austria",
	"Republic of Austria": "Austria",

	// === 瑞典 ===
	Sweden: "Sweden",
	Sverige: "Sweden",
	"Kingdom of Sweden": "Sweden",

	// === 挪威 ===
	Norway: "Norway",
	Norge: "Norway",
	"Kingdom of Norway": "Norway",

	// === 丹麦 ===
	Denmark: "Denmark",
	Danmark: "Denmark",
	"Kingdom of Denmark": "Denmark",

	// === 芬兰 ===
	Finland: "Finland",
	Suomi: "Finland",
	"Republic of Finland": "Finland",

	// === 波兰 ===
	Poland: "Poland",
	Polska: "Poland",
	"Republic of Poland": "Poland",

	// === 捷克 ===
	"Czech Republic": "Czech Republic",
	Czechia: "Czech Republic",
	"Česká republika": "Czech Republic",

	// === 匈牙利 ===
	Hungary: "Hungary",
	Magyarország: "Hungary",

	// === 土耳其 ===
	Turkey: "Turkey",
	Türkiye: "Turkey",
	"Republic of Turkey": "Turkey",

	// === 希腊 ===
	Greece: "Greece",
	Ελλάδα: "Greece",
	"Hellenic Republic": "Greece",

	// === 葡萄牙 ===
	Portugal: "Portugal",
	"Portuguese Republic": "Portugal",

	// === 爱尔兰 ===
	Ireland: "Ireland",
	Éire: "Ireland",
	"Republic of Ireland": "Ireland",

	// === 以色列 ===
	Israel: "Israel",
	ישראל: "Israel",
	"State of Israel": "Israel",

	// === 埃及 ===
	Egypt: "Egypt",
	مصر: "Egypt",
	"Arab Republic of Egypt": "Egypt",

	// === 南非 ===
	"South Africa": "South Africa",
	"Republic of South Africa": "South Africa",

	// === 新西兰 ===
	"New Zealand": "New Zealand",
	Aotearoa: "New Zealand",

	// === 泰国 ===
	Thailand: "Thailand",
	ไทย: "Thailand",
	"Kingdom of Thailand": "Thailand",
	Siam: "Thailand",

	// === 越南 ===
	Vietnam: "Vietnam",
	"Việt Nam": "Vietnam",
	"Socialist Republic of Vietnam": "Vietnam",

	// === 马来西亚 ===
	Malaysia: "Malaysia",

	// === 新加坡 ===
	Singapore: "Singapore",
	"Republic of Singapore": "Singapore",

	// === 印度尼西亚 ===
	Indonesia: "Indonesia",
	"Republic of Indonesia": "Indonesia",

	// === 菲律宾 ===
	Philippines: "Philippines",
	"Republic of the Philippines": "Philippines",

	// === 缅甸 ===
	Myanmar: "Myanmar",
	Burma: "Myanmar",
	"Republic of the Union of Myanmar": "Myanmar",

	// === 伊朗 ===
	Iran: "Iran",
	"Islamic Republic of Iran": "Iran",
	Persia: "Iran",

	// === 伊拉克 ===
	Iraq: "Iraq",
	"Republic of Iraq": "Iraq",

	// === 沙特阿拉伯 ===
	"Saudi Arabia": "Saudi Arabia",
	"Kingdom of Saudi Arabia": "Saudi Arabia",

	// === 阿联酋 ===
	"United Arab Emirates": "United Arab Emirates",
	UAE: "United Arab Emirates",

	// === 卡塔尔 ===
	Qatar: "Qatar",
	"State of Qatar": "Qatar",

	// === 科威特 ===
	Kuwait: "Kuwait",
	"State of Kuwait": "Kuwait",

	// === 巴林 ===
	Bahrain: "Bahrain",
	"Kingdom of Bahrain": "Bahrain",

	// === 阿曼 ===
	Oman: "Oman",
	"Sultanate of Oman": "Oman",

	// === 约旦 ===
	Jordan: "Jordan",
	"Hashemite Kingdom of Jordan": "Jordan",

	// === 黎巴嫩 ===
	Lebanon: "Lebanon",
	"Lebanese Republic": "Lebanon",

	// === 叙利亚 ===
	Syria: "Syria",
	"Syrian Arab Republic": "Syria",

	// === 巴基斯坦 ===
	Pakistan: "Pakistan",
	"Islamic Republic of Pakistan": "Pakistan",

	// === 孟加拉国 ===
	Bangladesh: "Bangladesh",
	"People's Republic of Bangladesh": "Bangladesh",

	// === 斯里兰卡 ===
	"Sri Lanka": "Sri Lanka",
	"Democratic Socialist Republic of Sri Lanka": "Sri Lanka",
	Ceylon: "Sri Lanka",

	// === 尼泊尔 ===
	Nepal: "Nepal",
	"Federal Democratic Republic of Nepal": "Nepal",

	// === 不丹 ===
	Bhutan: "Bhutan",
	"Kingdom of Bhutan": "Bhutan",

	// === 马尔代夫 ===
	Maldives: "Maldives",
	"Republic of Maldives": "Maldives",

	// === 阿富汗 ===
	Afghanistan: "Afghanistan",
	"Islamic Republic of Afghanistan": "Afghanistan",

	// === 哈萨克斯坦 ===
	Kazakhstan: "Kazakhstan",
	"Republic of Kazakhstan": "Kazakhstan",

	// === 乌兹别克斯坦 ===
	Uzbekistan: "Uzbekistan",
	"Republic of Uzbekistan": "Uzbekistan",

	// === 土库曼斯坦 ===
	Turkmenistan: "Turkmenistan",

	// === 吉尔吉斯斯坦 ===
	Kyrgyzstan: "Kyrgyzstan",
	"Kyrgyz Republic": "Kyrgyzstan",

	// === 塔吉克斯坦 ===
	Tajikistan: "Tajikistan",
	"Republic of Tajikistan": "Tajikistan",

	// === 蒙古 ===
	Mongolia: "Mongolia",

	// === 朝鲜/韩国相关 (已在上面定义，此处移除重复) ===

	// === 非洲国家 ===
	Congo: "Republic of the Congo",
	"Democratic Republic of Congo": "Democratic Republic of the Congo",
	"DR Congo": "Democratic Republic of the Congo",
	DRC: "Democratic Republic of the Congo",

	// === 其他常见映射 ===
	"The Bahamas": "The Bahamas",
	Bahamas: "The Bahamas",

	"The Gambia": "The Gambia",
	Gambia: "The Gambia",

	"Cape Verde": "Cape Verde",
	"Cabo Verde": "Cape Verde",

	"Ivory Coast": "Ivory Coast",
	"Côte d'Ivoire": "Ivory Coast",

	Swaziland: "Swaziland",
	Eswatini: "Swaziland",

	Macedonia: "North Macedonia",
	"North Macedonia": "North Macedonia",
	FYROM: "North Macedonia",

	"Bosnia and Herzegovina": "Bosnia and Herzegovina",
	Bosnia: "Bosnia and Herzegovina",

	"United Republic of Tanzania": "Tanzania",
	Tanzania: "Tanzania",

	// === 岛国和地区 ===
	"Faroe Islands": "Faroe Islands",
	Greenland: "Greenland",
	"Puerto Rico": "Puerto Rico",
	"Hong Kong": "Hong Kong",
	Macau: "Macau",
	Taiwan: "Taiwan",

	// === 中东小国 ===
	"State of Palestine": "Palestine",
	Palestine: "Palestine",
	"West Bank": "Palestine",
	"Gaza Strip": "Palestine",

	// === 加勒比海国家 ===
	"Trinidad and Tobago": "Trinidad and Tobago",
	"Trinidad & Tobago": "Trinidad and Tobago",

	"Saint Vincent and the Grenadines": "Saint Vincent and the Grenadines",
	"St. Vincent and the Grenadines": "Saint Vincent and the Grenadines",

	"Antigua and Barbuda": "Antigua and Barbuda",

	"Saint Kitts and Nevis": "Saint Kitts and Nevis",
	"St. Kitts and Nevis": "Saint Kitts and Nevis",

	"Saint Lucia": "Saint Lucia",
	"St. Lucia": "Saint Lucia",

	"Dominican Republic": "Dominican Republic",

	// === 太平洋岛国 ===
	"Papua New Guinea": "Papua New Guinea",
	"Solomon Islands": "Solomon Islands",
	Fiji: "Fiji",
	Vanuatu: "Vanuatu",
	Samoa: "Samoa",
	Tonga: "Tonga",
	Kiribati: "Kiribati",
	Tuvalu: "Tuvalu",
	Nauru: "Nauru",
	Palau: "Palau",
	"Marshall Islands": "Marshall Islands",
	Micronesia: "Federated States of Micronesia",
	"Federated States of Micronesia": "Federated States of Micronesia",

	// === 欧洲小国 ===
	Monaco: "Monaco",
	Andorra: "Andorra",
	"San Marino": "San Marino",
	Vatican: "Vatican",
	"Vatican City": "Vatican",
	Liechtenstein: "Liechtenstein",
	Luxembourg: "Luxembourg",
	Malta: "Malta",
	Cyprus: "Cyprus",
	Iceland: "Iceland",

	// === 波罗的海国家 ===
	Estonia: "Estonia",
	Latvia: "Latvia",
	Lithuania: "Lithuania",

	// === 巴尔干国家 ===
	Slovenia: "Slovenia",
	Croatia: "Croatia",
	Serbia: "Serbia",
	Montenegro: "Montenegro",
	Albania: "Albania",
	Kosovo: "Kosovo",
	Bulgaria: "Bulgaria",
	Romania: "Romania",
	Moldova: "Moldova",
	"Republic of Moldova": "Moldova",

	// === 高加索国家 ===
	Georgia: "Georgia",
	Armenia: "Armenia",
	Azerbaijan: "Azerbaijan",

	// === 白俄罗斯和乌克兰 ===
	Belarus: "Belarus",
	Ukraine: "Ukraine",
};

/**
 * 从 Mapbox 地址字符串中提取国家名称
 * @param addressString 完整地址字符串，如 "Los Angeles, California, United States"
 * @returns 提取的国家名称
 */
export function extractCountryFromAddress(addressString: string): string {
	if (!addressString) return "";

	// 按逗号分割地址，取最后一部分作为国家
	const parts = addressString.split(",").map((part) => part.trim());
	if (parts.length > 0) {
		return parts[parts.length - 1];
	}

	return addressString;
}

/**
 * 处理 Mapbox 搜索结果的国家信息
 * @param mapboxResult Mapbox 返回的位置对象
 * @returns 标准化的国家名称
 */
export function normalizeMapboxCountryResult(mapboxResult: any): string {
	if (!mapboxResult) return "";

	// 优先级顺序：country > countryCode > 从地址中提取
	let countryName = "";

	// 1. 优先使用直接的 country 字段
	if (mapboxResult.country) {
		countryName = mapboxResult.country;
	}
	// 2. 使用 countryCode 字段
	else if (mapboxResult.countryCode) {
		countryName = mapboxResult.countryCode;
	}
	// 3. 从完整地址中提取
	else if (mapboxResult.original) {
		countryName = extractCountryFromAddress(mapboxResult.original);
	}
	// 4. 从 place_name 中提取（Mapbox Geocoding API 格式）
	else if (mapboxResult.place_name) {
		countryName = extractCountryFromAddress(mapboxResult.place_name);
	}
	// 5. 从 context 中查找 country 类型（Mapbox Geocoding API 格式）
	else if (mapboxResult.context && Array.isArray(mapboxResult.context)) {
		for (const item of mapboxResult.context) {
			if (item?.id?.startsWith?.("country")) {
				countryName = item.text || item.text_en || "";
				break;
			}
		}
	}

	// 标准化提取的国家名称
	return normalizeCountryName(countryName);
}

/**
 * 标准化国家名称
 * @param countryName 原始国家名称
 * @returns 标准化后的国家名称
 */
export function normalizeCountryName(countryName: string): string {
	if (!countryName) return countryName;

	// 去除首尾空格并统一大小写比较
	const trimmedName = countryName.trim();

	// 直接查找映射
	if (COUNTRY_NAME_MAPPING[trimmedName]) {
		return COUNTRY_NAME_MAPPING[trimmedName];
	}

	// 尝试不区分大小写的查找
	const lowerCaseName = trimmedName.toLowerCase();
	for (const [key, value] of Object.entries(COUNTRY_NAME_MAPPING)) {
		if (key.toLowerCase() === lowerCaseName) {
			return value;
		}
	}

	// 如果没有找到映射，返回原始名称
	return trimmedName;
}

/**
 * 获取国家的所有可能变体名称（用于匹配 GeoJSON properties）
 * @param standardName 标准化的国家名称
 * @returns 所有可能的变体名称数组
 */
export function getCountryNameVariants(standardName: string): string[] {
	const variants = new Set<string>();

	// 添加标准名称本身
	variants.add(standardName);

	// 添加所有映射到这个标准名称的变体
	for (const [variant, standard] of Object.entries(COUNTRY_NAME_MAPPING)) {
		if (standard === standardName) {
			variants.add(variant);
		}
	}

	// 添加大小写变体
	for (const variant of Array.from(variants)) {
		variants.add(variant.toLowerCase());
		variants.add(variant.toUpperCase());
		variants.add(
			variant.charAt(0).toUpperCase() + variant.slice(1).toLowerCase(),
		);
	}

	return Array.from(variants);
}

/**
 * GeoJSON 属性中可能包含国家名称的字段列表
 */
export const GEOJSON_COUNTRY_NAME_FIELDS = [
	"name",
	"NAME",
	"name_en",
	"NAME_EN",
	"name_long",
	"NAME_LONG",
	"admin",
	"ADMIN",
	"country",
	"COUNTRY",
	"sovereignty",
	"SOVEREIGNTY",
	"name_sort",
	"NAME_SORT",
] as const;

/**
 * 检查 GeoJSON feature 是否匹配指定的国家名称
 * @param feature GeoJSON feature 对象
 * @param standardCountryName 标准化的国家名称
 * @returns 是否匹配
 */
export function matchesGeoJSONFeature(
	feature: any,
	standardCountryName: string,
): boolean {
	if (!feature?.properties) return false;

	const variants = getCountryNameVariants(standardCountryName);
	const properties = feature.properties;

	// 检查所有可能的属性字段
	for (const field of GEOJSON_COUNTRY_NAME_FIELDS) {
		const fieldValue = properties[field];
		if (fieldValue && typeof fieldValue === "string") {
			const trimmedValue = fieldValue.trim();

			// 检查是否匹配任何变体
			for (const variant of variants) {
				if (
					trimmedValue === variant ||
					trimmedValue.toLowerCase() === variant.toLowerCase()
				) {
					return true;
				}
			}
		}
	}

	return false;
}

/**
 * 调试信息：获取 GeoJSON feature 的所有国家名称属性
 * @param feature GeoJSON feature 对象
 * @returns 包含所有国家名称属性的对象
 */
export function getGeoJSONCountryNames(feature: any): Record<string, any> {
	if (!feature?.properties) return {};

	const result: Record<string, any> = {};
	const properties = feature.properties;

	for (const field of GEOJSON_COUNTRY_NAME_FIELDS) {
		if (properties[field] !== undefined) {
			result[field] = properties[field];
		}
	}

	return result;
}
