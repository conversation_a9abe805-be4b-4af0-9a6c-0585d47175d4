{"editor.defaultFormatter": "biomejs.biome", "editor.formatOnSave": true, "editor.formatOnPaste": true, "emmet.showExpandedAbbreviation": "never", "tailwindCSS.experimental.classRegex": [["cn\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"]], "editor.codeActionsOnSave": {"quickfix.biome": "explicit", "source.organizeImports.biome": "explicit", "source.fixAll": "explicit", "source.organizeImports": "explicit"}, "typescript.preferences.importModuleSpecifier": "non-relative", "typescript.tsdk": "node_modules/typescript/lib", "i18n-ally.localesPaths": ["packages/i18n/translations"], "i18n-ally.keystyle": "nested", "i18n-ally.enabledFrameworks": ["next-intl"], "i18n-ally.keysInUse": ["mail.organizationInvitation.headline"], "i18n-ally.tabStyle": "tab", "typescript.preferences.strictNullChecks": "on", "typescript.preferences.strictFunctionTypes": "on", "typescript.preferences.noImplicitAny": "on", "typescript.preferences.noImplicitReturns": "on", "typescript.preferences.noImplicitThis": "on", "typescript.preferences.noUncheckedIndexedAccess": "on", "typescript.tsc.autoDetect": "on", "typescript.validate.enable": true, "typescript.suggest.autoImports": true, "typescript.suggest.includePackageJsonAutoImports": "on", "files.exclude": {"**/.next": true, "**/node_modules": true, "**/.git": true}, "search.exclude": {"**/.next": true, "**/node_modules": true, "**/.git": true}}