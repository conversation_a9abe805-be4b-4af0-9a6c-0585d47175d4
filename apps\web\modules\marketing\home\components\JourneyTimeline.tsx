"use client";

import { Button } from "@ui/components/button";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";

interface JourneyStep {
	icon: string;
	title: string;
	description: string;
	color: string;
}

export function JourneyTimeline() {
	const [visibleSteps, setVisibleSteps] = useState<number[]>([]);
	const timelineRef = useRef<HTMLDivElement>(null);

	const journeySteps: JourneyStep[] = [
		{
			icon: "✍️",
			title: "记录心情",
			description:
				"用温暖的文字记录旅途中的感动瞬间，每一个文字都是心境的写照",
			color: "from-pink-400 to-rose-500",
		},
		{
			icon: "📍",
			title: "标记足迹",
			description:
				"在智能地图上标记每一个停留的地方，让足迹成为最美的回忆线条",
			color: "from-blue-400 to-indigo-500",
		},
		{
			icon: "📸",
			title: "珍藏时光",
			description: "上传照片和视频，让美好的画面永远定格在这一刻",
			color: "from-purple-400 to-violet-500",
		},
		{
			icon: "🗺️",
			title: "分享故事",
			description: "把你的旅行故事分享给朋友，让快乐加倍，让回忆永远流传",
			color: "from-emerald-400 to-teal-500",
		},
	];

	useEffect(() => {
		const observer = new IntersectionObserver(
			(entries) => {
				entries.forEach((entry) => {
					if (entry.isIntersecting) {
						const stepIndex = Number(
							entry.target.getAttribute("data-step"),
						);
						setVisibleSteps((prev) =>
							Array.from(new Set([...prev, stepIndex])),
						);
					}
				});
			},
			{ threshold: 0.5 },
		);

		const steps = timelineRef.current?.querySelectorAll("[data-step]");
		steps?.forEach((step) => observer.observe(step));

		return () => observer.disconnect();
	}, []);

	return (
		<section className="py-20 bg-gradient-to-b from-white to-blue-50/50 dark:from-slate-900 dark:to-slate-800">
			<div className="container mx-auto px-4">
				{/* 标题 */}
				<div className="text-center mb-16">
					<h2 className="text-4xl md:text-5xl font-bold text-gray-800 dark:text-white mb-6">
						开启你的
						<span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
							旅行记录之旅
						</span>
					</h2>
					<p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto leading-relaxed">
						简单几步，就能创建属于你的旅行日记。让每一次旅行都成为珍贵的回忆宝藏
					</p>
				</div>

				{/* 时间线 */}
				<div ref={timelineRef} className="relative max-w-4xl mx-auto">
					{/* 连接线 */}
					<div className="hidden md:block absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-gradient-to-b from-blue-200 via-purple-200 to-emerald-200 dark:from-blue-800 dark:via-purple-800 dark:to-emerald-800 -z-10" />

					{journeySteps.map((step, index) => (
						<div
							key={index}
							data-step={index}
							className={`relative flex items-center mb-16 transition-all duration-1000 ${
								visibleSteps.includes(index)
									? "opacity-100 transform translate-y-0"
									: "opacity-0 transform translate-y-8"
							} ${index % 2 === 0 ? "md:flex-row" : "md:flex-row-reverse"}`}
						>
							{/* 内容卡片 */}
							<div
								className={`w-full md:w-7/12 ${index % 2 === 0 ? "md:pr-8" : "md:pl-8"}`}
							>
								<div className="bg-white dark:bg-slate-800 rounded-2xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-100 dark:border-slate-700 relative z-10">
									{/* 标题和图标一行 */}
									<div className="flex items-center gap-4 mb-4">
										<div
											className={`w-12 h-12 rounded-xl bg-gradient-to-br ${step.color} flex items-center justify-center text-xl shadow-lg`}
										>
											{step.icon}
										</div>
										<h3 className="text-xl font-bold text-gray-800 dark:text-white">
											{step.title}
										</h3>
									</div>
									<p className="text-gray-600 dark:text-gray-300 leading-relaxed">
										{step.description}
									</p>
								</div>
							</div>

							{/* 中心圆点 */}
							<div className="hidden md:flex absolute left-1/2 transform -translate-x-1/2 w-6 h-6 bg-white dark:bg-slate-800 border-4 border-blue-400 rounded-full z-20">
								<div
									className={`w-full h-full rounded-full bg-gradient-to-br ${step.color} animate-pulse`}
								/>
							</div>

							{/* 步骤编号 */}
							<div
								className={`hidden md:block w-5/12 ${index % 2 === 0 ? "text-right pr-16" : "text-left pl-16"}`}
							>
								<div className="text-6xl font-bold text-gray-200 dark:text-slate-700">
									{String(index + 1).padStart(2, "0")}
								</div>
							</div>
						</div>
					))}
				</div>

				{/* 行动按钮 */}
				<div className="text-center mt-16">
					<Button
						size="lg"
						className="px-8 py-4 text-lg bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
					>
						<Link
							href="/auth/login"
							className="flex items-center gap-2"
						>
							<span>立即开始记录</span>
							<svg
								className="w-5 h-5"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 24 24"
							>
								<title>开始图标</title>
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z"
								/>
							</svg>
						</Link>
					</Button>
				</div>
			</div>
		</section>
	);
}
