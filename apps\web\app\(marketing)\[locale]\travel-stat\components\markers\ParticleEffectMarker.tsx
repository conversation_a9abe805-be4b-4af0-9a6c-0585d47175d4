"use client";

import { MapPin } from "lucide-react";
import { useEffect, useMemo } from "react";
import { PARTICLE_EFFECT_THEMES } from "../../types/markerTypes";

import type { BaseMarkerProps } from "./types";

interface ParticleEffectMarkerProps extends BaseMarkerProps {
	theme?: string;
	particleCount?: number;
	particleColor?: string;
	hideOutline?: boolean;
}

export function ParticleEffectMarker({
	point,
	onRemovePoint,
	isSelected = false,
	animationSpeed = "normal",
	theme = "fire",
	particleCount,
	particleColor,
	hideOutline = false,
}: ParticleEffectMarkerProps) {
	// 使用统一的主题配置
	const themeConfig =
		PARTICLE_EFFECT_THEMES[theme as keyof typeof PARTICLE_EFFECT_THEMES] ||
		PARTICLE_EFFECT_THEMES.fire;

	const finalParticleCount = particleCount || themeConfig.particleCount;
	const finalParticleColor = particleColor || themeConfig.particleColor;

	// 根据动画速度调整粒子运动频率
	const speedMultiplier = useMemo(() => {
		switch (animationSpeed) {
			case "slow":
				return 0.7;
			case "fast":
				return 1.5;
			default:
				return 1;
		}
	}, [animationSpeed]);

	const particles = useMemo(() => {
		return Array.from({ length: finalParticleCount }, (_, index) => ({
			id: index,
			angle: (360 / finalParticleCount) * index,
			delay: (index / finalParticleCount) * 2,
			radius: 18, // 固定半径，贴近图标
			size: 2 + Math.random() * 1, // 更小的粒子
		}));
	}, [finalParticleCount]);

	// 创建全局 CSS 动画样式
	useEffect(() => {
		const styleId = "particle-effect-animations";
		let styleElement = document.getElementById(styleId) as HTMLStyleElement;

		if (!styleElement) {
			styleElement = document.createElement("style");
			styleElement.id = styleId;
			document.head.appendChild(styleElement);

			// 创建简化的粒子轨道动画
			styleElement.textContent = `
        @keyframes particle-orbit {
          0% { 
            transform: translate(-50%, -50%) rotate(var(--start-angle)) translateX(var(--orbit-radius)) rotate(calc(-1 * var(--start-angle)));
          }
          100% { 
            transform: translate(-50%, -50%) rotate(calc(var(--start-angle) + 360deg)) translateX(var(--orbit-radius)) rotate(calc(-1 * (var(--start-angle) + 360deg)));
          }
        }
      `;
		}

		return () => {
			// 清理样式（可选）
		};
	}, []);

	return (
		<div className="relative group">
			{hideOutline ? (
				// 无轮廓模式 - 只显示图标
				<div
					className="flex items-center justify-center cursor-pointer hover:scale-110 transition-all duration-300"
					style={{
						width: "32px",
						height: "32px",
						transform: isSelected ? "scale(1.3)" : "scale(1)",
					}}
					role="button"
					tabIndex={0}
					onKeyDown={(e) => {
						if (e.key === "Enter" || e.key === " ") {
							e.stopPropagation();
						}
					}}
				>
					<MapPin
						className="w-6 h-6 drop-shadow-lg"
						style={{ color: finalParticleColor }}
					/>
				</div>
			) : (
				// 标准模式 - 带粒子效果
				<>
					{/* 粒子层 */}
					{particles.map((particle) => (
						<div
							key={particle.id}
							className="absolute rounded-full pointer-events-none"
							style={{
								width: `${particle.size}px`,
								height: `${particle.size}px`,
								background: finalParticleColor,
								top: "50%",
								left: "50%",
								transform: "translate(-50%, -50%)",
								// 使用 CSS 变量传递参数
								["--start-angle" as any]: `${particle.angle}deg`,
								["--orbit-radius" as any]: `${particle.radius}px`,
								["--particle-color" as any]: finalParticleColor,
								animation: `particle-orbit ${4 / speedMultiplier}s linear infinite`,
								animationDelay: `${particle.delay}s`,
							}}
						/>
					))}

					{/* 中心标记点 */}
					<div
						className="relative z-10 w-8 h-8 rounded-full border-2 border-white cursor-pointer hover:scale-110 transition-all duration-300 flex items-center justify-center"
						style={{
							background: finalParticleColor,
							transform: isSelected ? "scale(1.3)" : "scale(1)",
						}}
						role="button"
						tabIndex={0}
						onKeyDown={(e) => {
							if (e.key === "Enter" || e.key === " ") {
								e.stopPropagation();
							}
						}}
					>
						<MapPin className="w-4 h-4 text-white" />
					</div>
				</>
			)}
		</div>
	);
}
