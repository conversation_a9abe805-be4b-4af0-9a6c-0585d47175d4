import type { AIProvider } from "../types";

export class VolcengineProvider implements AIProvider {
	private apiKey: string;
	private apiEndpoint: string;

	name = "火山引擎";
	supportedFeatures = {
		text: true,
		image: true,
		imageAnalysis: true,
		audio: true,
	};

	constructor(
		apiKey: string,
		secretKey = "", // 保留参数以兼容现有代码，但不再使用
		apiEndpoint = "https://ark.cn-beijing.volces.com",
	) {
		this.apiKey = apiKey;
		this.apiEndpoint = apiEndpoint;
	}

	async generateText(options: {
		systemPrompt?: string;
		userPrompt: string;
		maxTokens?: number;
		temperature?: number;
		model?: string;
		history?: any[];
		additionalParams?: any;
	}): Promise<string> {
		const model = options?.model || "ep-20250222164022-fkllf"; // 使用默认模型或配置中的模型
		const url = `${this.apiEndpoint}/api/v3/chat/completions`;

		// 准备消息数组（按照火山引擎官方API格式）
		const messages = [
			{
				role: "system",
				content: options?.systemPrompt || "你是人工智能助手。",
			},
			{
				role: "user",
				content: options.userPrompt,
			},
		];

		// 如果有历史消息，添加到消息数组中
		if (options?.history && Array.isArray(options.history)) {
			messages.splice(1, 0, ...options.history);
		}

		const response = await fetch(url, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${this.apiKey}`,
			},
			body: JSON.stringify({
				model,
				messages,
				temperature: options?.temperature || 0.7,
				max_tokens: options?.maxTokens || 1000,
				top_p: 0.95,
				stream: false,
				...options?.additionalParams,
			}),
		});

		if (!response.ok) {
			const error = await response.text();
			throw new Error(`火山引擎 API 错误: ${error}`);
		}

		const data = await response.json();

		// 基于火山引擎API的响应格式进行解析
		return data.choices[0]?.message?.content || "";
	}

	// 图像生成功能可能需要使用不同的端点和格式
	async generateImage(prompt: string, options?: any): Promise<string> {
		// 火山引擎图像生成API可能与聊天API格式不同
		// 这里保留之前的实现，但需要注意可能需要根据实际API进行调整
		const url = `${this.apiEndpoint}/v1/imagegeneration`;

		const response = await fetch(url, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${this.apiKey}`,
			},
			body: JSON.stringify({
				prompt,
				resolution: options?.resolution || "1024x1024",
				style: options?.style || "realistic",
				...options,
			}),
		});

		if (!response.ok) {
			const error = await response.text();
			throw new Error(`火山引擎图像生成 API 错误: ${error}`);
		}

		const data = await response.json();
		return data.image_url;
	}

	async transcribeAudio(
		audioData: Blob | Buffer,
		options?: any,
	): Promise<string> {
		// 音频转录API可能也需要调整
		const url = `${this.apiEndpoint}/v1/audio/transcription`;

		const formData = new FormData();
		let audioBlob: Blob;
		if (Buffer.isBuffer(audioData)) {
			audioBlob = new Blob([audioData], { type: "audio/mpeg" });
		} else {
			audioBlob = audioData as Blob;
		}
		formData.append("file", audioBlob);
		formData.append("language", options?.language || "zh");

		if (options) {
			Object.entries(options).forEach(([key, value]) => {
				if (key !== "language") {
					formData.append(key, value as string);
				}
			});
		}

		const response = await fetch(url, {
			method: "POST",
			headers: {
				Authorization: `Bearer ${this.apiKey}`,
			},
			body: formData,
		});

		if (!response.ok) {
			const error = await response.text();
			throw new Error(`火山引擎语音转写 API 错误: ${error}`);
		}

		const data = await response.json();
		return data.text;
	}

	/**
	 * 分析图片内容并回答问题
	 * @param imageUrl 图片URL
	 * @param prompt 提示词/问题
	 * @param options 可选配置项
	 * @returns 分析结果
	 */
	async analyzeImage(
		imageUrl: string,
		prompt: string,
		options?: any,
	): Promise<string> {
		const model = options?.model || "ep-20250317191902-5fh22"; // 使用默认模型或配置中的模型
		const url = `${this.apiEndpoint}/api/v3/chat/completions`;

		// 准备消息数组，包含图片和文本
		const messages = [
			{
				role: "user",
				content: [
					{
						type: "image_url",
						image_url: {
							url: imageUrl,
						},
					},
					{
						type: "text",
						text: prompt,
					},
				],
			},
		];

		// 如果提供了系统提示词，添加到消息数组前面
		if (options?.systemPrompt) {
			messages.unshift({
				role: "system",
				content: options.systemPrompt,
			});
		}

		const response = await fetch(url, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${this.apiKey}`,
			},
			body: JSON.stringify({
				model,
				messages,
				temperature: options?.temperature || 0.7,
				max_tokens: options?.maxTokens || 1000,
				top_p: options?.topP || 0.95,
				stream: false,
				...options?.additionalParams,
			}),
		});

		if (!response.ok) {
			const error = await response.text();
			throw new Error(`火山引擎图片分析 API 错误: ${error}`);
		}

		const data = await response.json();

		// 解析并返回响应
		return data.choices[0]?.message?.content || "";
	}
}
